/**
 * Vybe Qubes Generated Content Management
 * Handles MAS-generated profitable website projects
 */

export interface VybeQube {
  id: string;
  name: string;
  description: string;
  category: string;
  businessModel: string;
  revenueProjections: {
    monthly: string;
    yearly: string;
  };
  technologies: string[];
  deploymentUrl?: string;
  githubUrl?: string;
  status: 'generating' | 'completed' | 'deployed' | 'failed';
  generatedAt: string;
  deployedAt?: string;
  agentsUsed: string[];
  qualityScore: number;
  estimatedRevenue: number;
  isPublic: boolean;
  isFeatured: boolean;
}

// Sample Vybe Qubes for demonstration
export const sampleVybeQubes: VybeQube[] = [
  {
    id: 'qube-001',
    name: 'AI Task Manager Pro',
    description: 'Intelligent task management platform with AI-powered prioritization and team collaboration',
    category: 'productivity',
    businessModel: 'SaaS Subscription',
    revenueProjections: {
      monthly: '$2,500',
      yearly: '$30,000'
    },
    technologies: ['SvelteKit', 'TypeScript', 'Appwrite', 'OpenAI API'],
    deploymentUrl: 'ai-taskmanager.vybequbes.com',
    githubUrl: 'https://github.com/vybecoding/ai-taskmanager',
    status: 'deployed',
    generatedAt: '2024-12-01T10:00:00Z',
    deployedAt: '2024-12-01T12:30:00Z',
    agentsUsed: ['VYBA', 'QUBERT', 'CODEX', 'PIXY', 'VYBRO', 'DUCKY', 'HAPPY'],
    qualityScore: 0.94,
    estimatedRevenue: 30000,
    isPublic: true,
    isFeatured: true
  },
  {
    id: 'qube-002',
    name: 'Developer Portfolio Builder',
    description: 'Automated portfolio generation for developers with GitHub integration and AI-powered content',
    category: 'developer-tools',
    businessModel: 'Freemium + Premium Templates',
    revenueProjections: {
      monthly: '$1,800',
      yearly: '$21,600'
    },
    technologies: ['Next.js', 'React', 'GitHub API', 'Vercel'],
    deploymentUrl: 'dev-portfolio.vybequbes.com',
    status: 'deployed',
    generatedAt: '2024-11-28T14:20:00Z',
    deployedAt: '2024-11-28T16:45:00Z',
    agentsUsed: ['VYBA', 'CODEX', 'PIXY', 'VYBRO', 'DUCKY'],
    qualityScore: 0.91,
    estimatedRevenue: 21600,
    isPublic: true,
    isFeatured: true
  },
  {
    id: 'qube-003',
    name: 'Learning Path Generator',
    description: 'AI-curated learning paths for developers with progress tracking and community features',
    category: 'education',
    businessModel: 'Course Marketplace + Subscriptions',
    revenueProjections: {
      monthly: '$4,200',
      yearly: '$50,400'
    },
    technologies: ['SvelteKit', 'Python', 'FastAPI', 'PostgreSQL'],
    status: 'generating',
    generatedAt: '2024-12-05T09:00:00Z',
    agentsUsed: ['VYBA', 'QUBERT', 'CODEX', 'PIXY', 'HAPPY'],
    qualityScore: 0.0,
    estimatedRevenue: 50400,
    isPublic: false,
    isFeatured: false
  }
];

export const vybeQubeCategories = [
  { id: 'all', name: 'All Categories', count: 0 },
  { id: 'productivity', name: 'Productivity', count: 0 },
  { id: 'developer-tools', name: 'Developer Tools', count: 0 },
  { id: 'education', name: 'Education', count: 0 },
  { id: 'e-commerce', name: 'E-commerce', count: 0 },
  { id: 'saas', name: 'SaaS', count: 0 },
  { id: 'marketplace', name: 'Marketplace', count: 0 }
];

export const businessModels = [
  'SaaS Subscription',
  'Freemium + Premium',
  'Course Marketplace',
  'Affiliate Marketing',
  'E-commerce',
  'Advertising Revenue',
  'Consulting Services'
];
