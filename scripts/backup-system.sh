#!/bin/bash
# VybeCoding.ai Backup System
# Comprehensive backup solution for database, files, and configurations

set -e

# Configuration
BACKUP_DIR="${BACKUP_DIR:-/backups}"
DATE=$(date +%Y%m%d_%H%M%S)
RETENTION_DAYS="${RETENTION_DAYS:-30}"
LOG_FILE="${BACKUP_DIR}/backup.log"
ENCRYPTION_KEY="${BACKUP_ENCRYPTION_KEY:-}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1" | tee -a "$LOG_FILE"
}

# Check prerequisites
check_prerequisites() {
    log "Checking backup prerequisites..."
    
    # Check if backup directory exists
    if [ ! -d "$BACKUP_DIR" ]; then
        log "Creating backup directory: $BACKUP_DIR"
        mkdir -p "$BACKUP_DIR"
    fi
    
    # Check available disk space (require at least 5GB)
    available_space=$(df "$BACKUP_DIR" | awk 'NR==2 {print $4}')
    required_space=5242880  # 5GB in KB
    
    if [ "$available_space" -lt "$required_space" ]; then
        error "Insufficient disk space. Available: ${available_space}KB, Required: ${required_space}KB"
        exit 1
    fi
    
    # Check if required tools are available
    for tool in docker gzip tar; do
        if ! command -v "$tool" &> /dev/null; then
            error "Required tool '$tool' is not installed"
            exit 1
        fi
    done
    
    log "Prerequisites check completed successfully"
}

# Database backup function
backup_database() {
    log "Starting database backup..."
    
    local db_backup_file="${BACKUP_DIR}/db_backup_${DATE}.sql"
    
    # Check if Appwrite container is running
    if ! docker ps | grep -q appwrite; then
        warning "Appwrite container not found, attempting to backup from environment"
        
        # Fallback: Use environment variables for direct database connection
        if [ -n "$DATABASE_URL" ]; then
            log "Using direct database connection for backup"
            pg_dump "$DATABASE_URL" > "$db_backup_file"
        else
            error "No database connection available for backup"
            return 1
        fi
    else
        # Primary method: Use Appwrite container
        log "Backing up database via Appwrite container..."
        docker exec appwrite-db pg_dump -U postgres vybecoding > "$db_backup_file" 2>/dev/null || {
            # Alternative: Try with different container name
            docker exec appwrite_appwrite-db_1 pg_dump -U postgres vybecoding > "$db_backup_file" 2>/dev/null || {
                error "Failed to backup database via container"
                return 1
            }
        }
    fi
    
    # Compress backup
    if [ -f "$db_backup_file" ]; then
        log "Compressing database backup..."
        gzip "$db_backup_file"
        log "Database backup completed: db_backup_${DATE}.sql.gz"
        
        # Encrypt if encryption key is provided
        if [ -n "$ENCRYPTION_KEY" ]; then
            log "Encrypting database backup..."
            openssl enc -aes-256-cbc -salt -in "${db_backup_file}.gz" -out "${db_backup_file}.gz.enc" -k "$ENCRYPTION_KEY"
            rm "${db_backup_file}.gz"
            log "Database backup encrypted: db_backup_${DATE}.sql.gz.enc"
        fi
    else
        error "Database backup file not created"
        return 1
    fi
}

# File system backup function
backup_files() {
    log "Starting file system backup..."
    
    # User uploads backup
    if [ -d "/app/uploads" ] || [ -d "./uploads" ] || [ -d "./static/uploads" ]; then
        log "Backing up user uploads..."
        for upload_dir in "/app/uploads" "./uploads" "./static/uploads"; do
            if [ -d "$upload_dir" ]; then
                tar -czf "${BACKUP_DIR}/uploads_backup_${DATE}.tar.gz" "$upload_dir" 2>/dev/null && break
            fi
        done
        log "Uploads backup completed"
    else
        warning "No uploads directory found, skipping uploads backup"
    fi
    
    # Configuration files backup
    log "Backing up configuration files..."
    config_files=()
    
    # Add configuration files if they exist
    [ -f ".env" ] && config_files+=(".env")
    [ -f ".env.production" ] && config_files+=(".env.production")
    [ -f ".env.staging" ] && config_files+=(".env.staging")
    [ -f "package.json" ] && config_files+=("package.json")
    [ -f "package-lock.json" ] && config_files+=("package-lock.json")
    [ -f "svelte.config.js" ] && config_files+=("svelte.config.js")
    [ -f "vite.config.ts" ] && config_files+=("vite.config.ts")
    [ -f "tailwind.config.js" ] && config_files+=("tailwind.config.js")
    [ -f "docker-compose.yml" ] && config_files+=("docker-compose.yml")
    [ -f "docker-compose.production.yml" ] && config_files+=("docker-compose.production.yml")
    [ -f "Dockerfile" ] && config_files+=("Dockerfile")
    
    if [ ${#config_files[@]} -gt 0 ]; then
        tar -czf "${BACKUP_DIR}/config_backup_${DATE}.tar.gz" "${config_files[@]}"
        log "Configuration backup completed"
    else
        warning "No configuration files found"
    fi
    
    # Logs backup (last 7 days)
    log "Backing up recent logs..."
    log_dirs=("/app/logs" "./logs" "/var/log/vybecoding")
    
    for log_dir in "${log_dirs[@]}"; do
        if [ -d "$log_dir" ]; then
            find "$log_dir" -name "*.log" -mtime -7 2>/dev/null | tar -czf "${BACKUP_DIR}/logs_backup_${DATE}.tar.gz" -T - 2>/dev/null || true
            log "Logs backup completed"
            break
        fi
    done
}

# Upload to cloud storage function
upload_to_cloud() {
    log "Checking for cloud storage configuration..."
    
    # AWS S3 upload
    if command -v aws &> /dev/null && [ -n "$AWS_S3_BUCKET" ]; then
        log "Uploading backups to AWS S3..."
        aws s3 sync "${BACKUP_DIR}" "s3://${AWS_S3_BUCKET}/backups/$(date +%Y/%m/%d)/" \
            --exclude "*" \
            --include "*_${DATE}*" \
            --storage-class STANDARD_IA \
            --quiet || warning "S3 upload failed"
        log "AWS S3 upload completed"
    fi
    
    # Google Cloud Storage upload
    if command -v gsutil &> /dev/null && [ -n "$GCS_BUCKET" ]; then
        log "Uploading backups to Google Cloud Storage..."
        gsutil -m cp "${BACKUP_DIR}"/*_${DATE}* "gs://${GCS_BUCKET}/backups/$(date +%Y/%m/%d)/" || warning "GCS upload failed"
        log "Google Cloud Storage upload completed"
    fi
    
    # Azure Blob Storage upload
    if command -v az &> /dev/null && [ -n "$AZURE_STORAGE_ACCOUNT" ] && [ -n "$AZURE_CONTAINER" ]; then
        log "Uploading backups to Azure Blob Storage..."
        az storage blob upload-batch \
            --destination "$AZURE_CONTAINER" \
            --source "$BACKUP_DIR" \
            --pattern "*_${DATE}*" \
            --account-name "$AZURE_STORAGE_ACCOUNT" || warning "Azure upload failed"
        log "Azure Blob Storage upload completed"
    fi
    
    if [ -z "$AWS_S3_BUCKET" ] && [ -z "$GCS_BUCKET" ] && [ -z "$AZURE_STORAGE_ACCOUNT" ]; then
        warning "No cloud storage configured, backups stored locally only"
    fi
}

# Cleanup old backups function
cleanup_old_backups() {
    log "Cleaning up old backups (older than ${RETENTION_DAYS} days)..."
    
    local deleted_count=0
    
    # Clean local backups
    while IFS= read -r -d '' file; do
        rm "$file"
        ((deleted_count++))
    done < <(find "${BACKUP_DIR}" -name "*.gz" -o -name "*.sql" -o -name "*.enc" -mtime +${RETENTION_DAYS} -print0 2>/dev/null)
    
    if [ $deleted_count -gt 0 ]; then
        log "Cleaned up $deleted_count old backup files"
    else
        log "No old backup files to clean up"
    fi
}

# Verify backup integrity function
verify_backup() {
    log "Verifying backup integrity..."
    
    local verification_failed=false
    
    # Test database backup
    local db_backup="${BACKUP_DIR}/db_backup_${DATE}.sql.gz"
    local db_backup_enc="${BACKUP_DIR}/db_backup_${DATE}.sql.gz.enc"
    
    if [ -f "$db_backup" ]; then
        if gzip -t "$db_backup" 2>/dev/null; then
            log "✅ Database backup integrity verified"
        else
            error "❌ Database backup corrupted"
            verification_failed=true
        fi
    elif [ -f "$db_backup_enc" ]; then
        log "✅ Encrypted database backup found"
    else
        error "❌ Database backup file not found"
        verification_failed=true
    fi
    
    # Test file backups
    for backup_type in uploads config logs; do
        local backup_file="${BACKUP_DIR}/${backup_type}_backup_${DATE}.tar.gz"
        if [ -f "$backup_file" ]; then
            if tar -tzf "$backup_file" > /dev/null 2>&1; then
                log "✅ ${backup_type^} backup integrity verified"
            else
                error "❌ ${backup_type^} backup corrupted"
                verification_failed=true
            fi
        fi
    done
    
    if [ "$verification_failed" = true ]; then
        error "Backup verification failed"
        exit 1
    fi
    
    log "All backup integrity checks passed"
}

# Send notification function
send_notification() {
    local status="$1"
    local message="$2"
    
    # Slack notification
    if [ -n "$SLACK_WEBHOOK_URL" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"🔄 VybeCoding.ai Backup $status: $message\"}" \
            "$SLACK_WEBHOOK_URL" &>/dev/null || true
    fi
    
    # Discord notification
    if [ -n "$DISCORD_WEBHOOK_URL" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"content\":\"🔄 VybeCoding.ai Backup $status: $message\"}" \
            "$DISCORD_WEBHOOK_URL" &>/dev/null || true
    fi
    
    # Email notification (if sendmail is available)
    if command -v sendmail &> /dev/null && [ -n "$NOTIFICATION_EMAIL" ]; then
        echo -e "Subject: VybeCoding.ai Backup $status\n\n$message" | sendmail "$NOTIFICATION_EMAIL" || true
    fi
}

# Main backup process
main() {
    local start_time=$(date)
    log "Starting backup process at $start_time"
    
    # Initialize
    check_prerequisites
    
    # Perform backups
    if backup_database && backup_files; then
        verify_backup
        upload_to_cloud
        cleanup_old_backups
        
        local end_time=$(date)
        local success_message="Backup completed successfully at $end_time"
        log "$success_message"
        send_notification "Success" "$success_message"
    else
        local error_message="Backup process failed at $(date)"
        error "$error_message"
        send_notification "Failed" "$error_message"
        exit 1
    fi
}

# Handle script arguments
case "${1:-}" in
    --help|-h)
        echo "VybeCoding.ai Backup System"
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h          Show this help message"
        echo "  --test              Run backup verification only"
        echo "  --cleanup           Run cleanup only"
        echo ""
        echo "Environment Variables:"
        echo "  BACKUP_DIR          Backup directory (default: /backups)"
        echo "  RETENTION_DAYS      Backup retention in days (default: 30)"
        echo "  BACKUP_ENCRYPTION_KEY  Encryption key for backups"
        echo "  AWS_S3_BUCKET       AWS S3 bucket for cloud backup"
        echo "  GCS_BUCKET          Google Cloud Storage bucket"
        echo "  AZURE_STORAGE_ACCOUNT  Azure storage account"
        echo "  SLACK_WEBHOOK_URL   Slack webhook for notifications"
        echo "  DISCORD_WEBHOOK_URL Discord webhook for notifications"
        echo "  NOTIFICATION_EMAIL  Email for notifications"
        exit 0
        ;;
    --test)
        log "Running backup verification test..."
        verify_backup
        exit 0
        ;;
    --cleanup)
        log "Running cleanup only..."
        cleanup_old_backups
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
