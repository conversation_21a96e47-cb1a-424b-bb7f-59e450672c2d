#!/usr/bin/env node

/**
 * Setup Deployment Collections for Enhanced MAS Content Deployment
 * Creates necessary Appwrite collections for automated content deployment
 */

import https from 'https';
import dotenv from 'dotenv';
dotenv.config();

const config = {
  endpoint: 'fra.cloud.appwrite.io',
  projectId: process.env.VITE_APPWRITE_PROJECT_ID || '683b200e00153d705da3',
  databaseId: process.env.VITE_APPWRITE_DATABASE_ID || '683b231d003c1c558e20',
  apiKey: process.env.APPWRITE_API_KEY,
};

console.log('🚀 Setting up Enhanced MAS Deployment Collections...\n');
console.log('📊 Configuration:');
console.log(`   Project: ${config.projectId}`);
console.log(`   Database: ${config.databaseId}`);
console.log(`   Endpoint: ${config.endpoint}`);
console.log('');

if (!config.apiKey) {
  console.error('❌ APPWRITE_API_KEY environment variable is required');
  process.exit(1);
}

// Make HTTP request to Appwrite API
function makeRequest(method, path, data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: config.endpoint,
      port: 443,
      path: `/v1${path}`,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'X-Appwrite-Project': config.projectId,
        'X-Appwrite-Key': config.apiKey,
      },
    };

    const req = https.request(options, res => {
      let responseData = '';

      res.on('data', chunk => {
        responseData += chunk;
      });

      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(parsedData);
          } else {
            reject(new Error(parsedData.message || `HTTP ${res.statusCode}`));
          }
        } catch (error) {
          reject(new Error(`Failed to parse response: ${responseData}`));
        }
      });
    });

    req.on('error', error => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

// Create collection with error handling
async function createCollection(collectionId, name, permissions) {
  try {
    console.log(`📋 Creating ${name} collection...`);

    const collection = await makeRequest(
      'POST',
      `/databases/${config.databaseId}/collections`,
      {
        collectionId,
        name,
        permissions,
      }
    );

    console.log(`✅ ${name} collection created successfully`);
    return collection;
  } catch (error) {
    if (
      error.message.includes('already exists') ||
      error.message.includes('409')
    ) {
      console.log(`✅ ${name} collection already exists`);
      return { $id: collectionId };
    } else {
      console.log(`⚠️  ${name} collection error: ${error.message}`);
      throw error;
    }
  }
}

// Create attribute with error handling
async function createAttribute(collectionId, type, key, options = {}) {
  try {
    const attribute = await makeRequest(
      'POST',
      `/databases/${config.databaseId}/collections/${collectionId}/attributes/${type}`,
      {
        key,
        ...options,
      }
    );

    console.log(`  ✅ Added ${key} (${type}) attribute`);
    return attribute;
  } catch (error) {
    if (
      error.message.includes('already exists') ||
      error.message.includes('409')
    ) {
      console.log(`  ✅ ${key} attribute already exists`);
    } else {
      console.log(`  ⚠️  ${key} attribute error: ${error.message}`);
    }
  }
}

// Setup deployment collections
async function setupDeploymentCollections() {
  try {
    // 1. Deployment Records Collection
    await createCollection(
      'deployment_records',
      'Deployment Records',
      ['read("any")', 'write("users")']
    );
    
    await createAttribute('deployment_records', 'string', 'taskId', {
      size: 50,
      required: true,
    });
    await createAttribute('deployment_records', 'string', 'contentType', {
      size: 50,
      required: true,
    });
    await createAttribute('deployment_records', 'string', 'status', {
      size: 20,
      required: true,
    });
    await createAttribute('deployment_records', 'string', 'deploymentUrl', {
      size: 500,
      required: false,
    });
    await createAttribute('deployment_records', 'string', 'contentId', {
      size: 50,
      required: false,
    });
    await createAttribute('deployment_records', 'string', 'deploymentPath', {
      size: 500,
      required: false,
    });
    await createAttribute('deployment_records', 'string', 'generationMetadata', {
      size: 10000,
      required: false,
    });
    await createAttribute('deployment_records', 'datetime', 'deployedAt', {
      required: false,
    });
    await createAttribute('deployment_records', 'datetime', 'createdAt', {
      required: true,
    });

    // 2. Enhanced MAS Courses Collection (if not exists)
    await createCollection(
      'vybe_courses',
      'Enhanced MAS Courses',
      ['read("any")', 'write("users")']
    );
    
    await createAttribute('vybe_courses', 'string', 'title', {
      size: 200,
      required: true,
    });
    await createAttribute('vybe_courses', 'string', 'slug', {
      size: 200,
      required: true,
    });
    await createAttribute('vybe_courses', 'string', 'description', {
      size: 1000,
      required: false,
    });
    await createAttribute('vybe_courses', 'string', 'lessons', {
      size: 50000,
      required: false,
    });
    await createAttribute('vybe_courses', 'string', 'difficulty', {
      size: 20,
      required: false,
    });
    await createAttribute('vybe_courses', 'string', 'targetAudience', {
      size: 100,
      required: false,
    });
    await createAttribute('vybe_courses', 'string', 'status', {
      size: 20,
      required: true,
      default: 'published',
    });
    await createAttribute('vybe_courses', 'boolean', 'featured', {
      required: false,
      default: false,
    });

    // 3. Enhanced MAS Articles Collection (if not exists)
    await createCollection(
      'vybe_articles',
      'Enhanced MAS Articles',
      ['read("any")', 'write("users")']
    );
    
    await createAttribute('vybe_articles', 'string', 'title', {
      size: 200,
      required: true,
    });
    await createAttribute('vybe_articles', 'string', 'slug', {
      size: 200,
      required: true,
    });
    await createAttribute('vybe_articles', 'string', 'content', {
      size: 50000,
      required: true,
    });
    await createAttribute('vybe_articles', 'string', 'excerpt', {
      size: 500,
      required: false,
    });
    await createAttribute('vybe_articles', 'string', 'category', {
      size: 50,
      required: false,
    });
    await createAttribute('vybe_articles', 'string', 'status', {
      size: 20,
      required: true,
      default: 'published',
    });
    await createAttribute('vybe_articles', 'boolean', 'featured', {
      required: false,
      default: false,
    });

    // 4. Enhanced MAS Vybe Qubes Collection (if not exists)
    await createCollection(
      'vybe_qubes',
      'Enhanced MAS Vybe Qubes',
      ['read("any")', 'write("users")']
    );
    
    await createAttribute('vybe_qubes', 'string', 'title', {
      size: 200,
      required: true,
    });
    await createAttribute('vybe_qubes', 'string', 'slug', {
      size: 200,
      required: true,
    });
    await createAttribute('vybe_qubes', 'string', 'description', {
      size: 1000,
      required: false,
    });
    await createAttribute('vybe_qubes', 'string', 'features', {
      size: 10000,
      required: false,
    });
    await createAttribute('vybe_qubes', 'string', 'techStack', {
      size: 1000,
      required: false,
    });
    await createAttribute('vybe_qubes', 'string', 'complexity', {
      size: 20,
      required: false,
    });
    await createAttribute('vybe_qubes', 'string', 'status', {
      size: 20,
      required: true,
      default: 'published',
    });
    await createAttribute('vybe_qubes', 'boolean', 'deploymentReady', {
      required: false,
      default: false,
    });

    console.log('\n🎉 ENHANCED MAS DEPLOYMENT COLLECTIONS SETUP COMPLETE!');
    console.log('\n✅ Collections Created/Verified:');
    console.log('   - deployment_records (9 attributes)');
    console.log('   - vybe_courses (8 attributes)');
    console.log('   - vybe_articles (7 attributes)');
    console.log('   - vybe_qubes (8 attributes)');
    console.log('\n🚀 Enhanced MAS deployment pipeline is ready!');
    console.log('\n📋 Next Steps:');
    console.log('   1. Start the deployment orchestrator');
    console.log('   2. Generate content using Enhanced MAS');
    console.log('   3. Content will be automatically deployed to live platform');

  } catch (error) {
    console.error('\n❌ Setup failed:', error.message);
    process.exit(1);
  }
}

// Run the setup
setupDeploymentCollections();
