#!/usr/bin/env python3
"""
Test Vybe Qube System End-to-End
Validates the complete Vybe Qube generation and deployment pipeline
"""

import asyncio
import json
import time
import httpx
import logging
from typing import Dict, Any

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class VybeQubeSystemTester:
    """Comprehensive tester for the Vybe Qube system"""
    
    def __init__(self):
        self.generator_url = "http://localhost:8001"
        self.deployer_url = "http://localhost:8002"
        self.test_results = {}
        
    async def run_all_tests(self):
        """Run complete test suite"""
        logger.info("🚀 Starting Vybe Qube System Tests")
        logger.info("=" * 60)
        
        tests = [
            ("Service Health Checks", self.test_service_health),
            ("MAS Coordinator Status", self.test_mas_status),
            ("Vybe Qube Generation", self.test_qube_generation),
            ("Deployment Pipeline", self.test_deployment_pipeline),
            ("Revenue Tracking", self.test_revenue_tracking),
            ("System Integration", self.test_system_integration)
        ]
        
        for test_name, test_func in tests:
            logger.info(f"\n🧪 Running: {test_name}")
            try:
                result = await test_func()
                self.test_results[test_name] = {"status": "PASS", "result": result}
                logger.info(f"✅ {test_name}: PASSED")
            except Exception as e:
                self.test_results[test_name] = {"status": "FAIL", "error": str(e)}
                logger.error(f"❌ {test_name}: FAILED - {e}")
        
        await self.generate_test_report()
    
    async def test_service_health(self) -> Dict[str, Any]:
        """Test that all services are running and healthy"""
        results = {}
        
        async with httpx.AsyncClient() as client:
            # Test Generator Service
            try:
                response = await client.get(f"{self.generator_url}/mas/status", timeout=10.0)
                results["generator"] = {
                    "status": response.status_code,
                    "healthy": response.status_code == 200
                }
            except Exception as e:
                results["generator"] = {"status": "error", "error": str(e)}
            
            # Test Deployer Service (if available)
            try:
                response = await client.get(f"{self.deployer_url}/deployments", timeout=10.0)
                results["deployer"] = {
                    "status": response.status_code,
                    "healthy": response.status_code == 200
                }
            except Exception as e:
                results["deployer"] = {"status": "error", "error": str(e)}
        
        return results
    
    async def test_mas_status(self) -> Dict[str, Any]:
        """Test MAS coordinator status and agent availability"""
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{self.generator_url}/mas/status", timeout=10.0)
            
            if response.status_code != 200:
                raise Exception(f"MAS status check failed: {response.status_code}")
            
            mas_data = response.json()
            
            # Validate MAS response structure
            required_fields = ["status", "agents", "active_generations", "total_generated"]
            for field in required_fields:
                if field not in mas_data:
                    raise Exception(f"Missing field in MAS status: {field}")
            
            return {
                "mas_status": mas_data["status"],
                "agent_count": len(mas_data["agents"]),
                "agents_active": all(agent["status"] == "active" for agent in mas_data["agents"]),
                "metrics": {
                    "active_generations": mas_data["active_generations"],
                    "total_generated": mas_data["total_generated"]
                }
            }
    
    async def test_qube_generation(self) -> Dict[str, Any]:
        """Test Vybe Qube generation process"""
        test_request = {
            "template_type": "ecommerce",
            "business_concept": "AI-powered test store for validation",
            "target_audience": "Tech enthusiasts and developers",
            "revenue_model": "Product sales with AI recommendations",
            "customization": {
                "branding": {
                    "primary_color": "#3B82F6",
                    "secondary_color": "#10B981",
                    "company_name": "Test Vybe Store"
                }
            }
        }
        
        async with httpx.AsyncClient() as client:
            # Start generation
            response = await client.post(
                f"{self.generator_url}/generate",
                json=test_request,
                timeout=30.0
            )
            
            if response.status_code != 200:
                raise Exception(f"Generation request failed: {response.status_code}")
            
            generation_data = response.json()
            qube_id = generation_data["qube_id"]
            
            # Monitor generation progress
            max_wait = 300  # 5 minutes
            start_time = time.time()
            
            while time.time() - start_time < max_wait:
                status_response = await client.get(
                    f"{self.generator_url}/status/{qube_id}",
                    timeout=10.0
                )
                
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    
                    if status_data.get("status") == "completed":
                        return {
                            "qube_id": qube_id,
                            "generation_time": time.time() - start_time,
                            "status": "completed",
                            "final_progress": status_data.get("progress", 0)
                        }
                    elif status_data.get("status") == "failed":
                        raise Exception(f"Generation failed: {status_data.get('error', 'Unknown error')}")
                
                await asyncio.sleep(5)  # Check every 5 seconds
            
            raise Exception("Generation timeout - took longer than 5 minutes")
    
    async def test_deployment_pipeline(self) -> Dict[str, Any]:
        """Test deployment pipeline (if deployer service is available)"""
        try:
            # Test deployment service availability
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{self.deployer_url}/deployments", timeout=10.0)
                
                if response.status_code != 200:
                    return {"status": "skipped", "reason": "Deployer service not available"}
                
                # Create test deployment
                test_files = {
                    "package.json": json.dumps({
                        "name": "test-vybe-qube",
                        "version": "1.0.0",
                        "scripts": {"build": "echo 'build complete'"}
                    }),
                    "index.html": "<html><body><h1>Test Vybe Qube</h1></body></html>"
                }
                
                deployment_request = {
                    "qube_id": f"test_{int(time.time())}",
                    "website_files": test_files,
                    "template_type": "test",
                    "business_concept": "Test deployment"
                }
                
                deploy_response = await client.post(
                    f"{self.deployer_url}/deploy",
                    json=deployment_request,
                    timeout=30.0
                )
                
                if deploy_response.status_code == 200:
                    deploy_data = deploy_response.json()
                    return {
                        "deployment_id": deploy_data["deployment_id"],
                        "subdomain": deploy_data["subdomain"],
                        "status": "initiated"
                    }
                else:
                    raise Exception(f"Deployment failed: {deploy_response.status_code}")
                    
        except Exception as e:
            return {"status": "error", "error": str(e)}
    
    async def test_revenue_tracking(self) -> Dict[str, Any]:
        """Test revenue tracking functionality"""
        async with httpx.AsyncClient() as client:
            # Test total revenue endpoint
            response = await client.get(f"{self.generator_url}/revenue/total", timeout=10.0)
            
            if response.status_code != 200:
                raise Exception(f"Revenue tracking failed: {response.status_code}")
            
            revenue_data = response.json()
            
            # Validate revenue data structure
            required_fields = ["total", "monthly", "weekly", "daily", "qube_count"]
            for field in required_fields:
                if field not in revenue_data:
                    raise Exception(f"Missing field in revenue data: {field}")
            
            return {
                "total_revenue": revenue_data["total"],
                "qube_count": revenue_data["qube_count"],
                "average_per_qube": revenue_data.get("average_per_qube", 0),
                "tracking_functional": True
            }
    
    async def test_system_integration(self) -> Dict[str, Any]:
        """Test overall system integration and data flow"""
        async with httpx.AsyncClient() as client:
            # Test Qube listing
            response = await client.get(f"{self.generator_url}/qubes", timeout=10.0)
            
            if response.status_code != 200:
                raise Exception(f"Qube listing failed: {response.status_code}")
            
            qubes_data = response.json()
            
            return {
                "total_qubes": len(qubes_data),
                "qubes_listed": isinstance(qubes_data, list),
                "integration_healthy": True
            }
    
    async def generate_test_report(self):
        """Generate comprehensive test report"""
        logger.info("\n" + "=" * 60)
        logger.info("🎯 VYBE QUBE SYSTEM TEST REPORT")
        logger.info("=" * 60)
        
        passed = sum(1 for result in self.test_results.values() if result["status"] == "PASS")
        total = len(self.test_results)
        
        logger.info(f"📊 Overall Results: {passed}/{total} tests passed")
        logger.info(f"🎯 Success Rate: {(passed/total)*100:.1f}%")
        
        for test_name, result in self.test_results.items():
            status_emoji = "✅" if result["status"] == "PASS" else "❌"
            logger.info(f"{status_emoji} {test_name}: {result['status']}")
            
            if result["status"] == "FAIL":
                logger.info(f"   Error: {result['error']}")
        
        # Save detailed report
        report_data = {
            "timestamp": time.time(),
            "summary": {
                "total_tests": total,
                "passed": passed,
                "failed": total - passed,
                "success_rate": (passed/total)*100
            },
            "test_results": self.test_results
        }
        
        with open("logs/vybe_qube_test_report.json", "w") as f:
            json.dump(report_data, f, indent=2)
        
        logger.info(f"\n📄 Detailed report saved to: logs/vybe_qube_test_report.json")
        
        if passed == total:
            logger.info("🎉 ALL TESTS PASSED! Vybe Qube system is fully operational.")
        else:
            logger.info("⚠️ Some tests failed. Check the report for details.")

async def main():
    """Run the complete test suite"""
    tester = VybeQubeSystemTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
