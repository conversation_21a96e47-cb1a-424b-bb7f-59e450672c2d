#!/usr/bin/env node

/**
 * Setup Appwrite collections for Vybe Method Content Generation
 * Creates collections for autonomous content generation tracking
 */

import https from 'https';
import dotenv from 'dotenv';
dotenv.config();

const config = {
  endpoint: 'fra.cloud.appwrite.io',
  projectId: process.env.VITE_APPWRITE_PROJECT_ID,
  databaseId: process.env.VITE_APPWRITE_DATABASE_ID,
  apiKey: process.env.APPWRITE_API_KEY,
};

console.log('🚀 Setting up Vybe Content Generation collections...\n');
console.log('📊 Configuration:');
console.log(`   Project: ${config.projectId}`);
console.log(`   Database: ${config.databaseId}`);
console.log(`   Endpoint: ${config.endpoint}`);
console.log('');

// Collection definitions
const collections = [
  {
    id: 'vybe_content_generations',
    name: 'Vybe Content Generations',
    permissions: ['read("any")', 'write("users")'],
    attributes: [
      { key: 'content_type', type: 'string', size: 50, required: true },
      { key: 'topic', type: 'string', size: 500, required: true },
      { key: 'target_audience', type: 'string', size: 200, required: true },
      { key: 'complexity_level', type: 'string', size: 20, required: true },
      { key: 'requirements', type: 'string', size: 10000, required: false },
      { key: 'user_id', type: 'string', size: 50, required: true },
      { key: 'status', type: 'string', size: 20, required: true },
      { key: 'progress', type: 'integer', required: false, default: 0 },
      { key: 'current_phase', type: 'string', size: 100, required: false },
      { key: 'estimated_completion', type: 'datetime', required: false },
      { key: 'agents_involved', type: 'string', size: 1000, required: false },
      { key: 'result', type: 'string', size: 100000, required: false },
      { key: 'error', type: 'string', size: 1000, required: false },
      { key: 'created_at', type: 'datetime', required: true },
      { key: 'updated_at', type: 'datetime', required: false },
      { key: 'completed_at', type: 'datetime', required: false },
      { key: 'failed_at', type: 'datetime', required: false }
    ]
  },
  {
    id: 'vybe_generated_courses',
    name: 'Vybe Generated Courses',
    permissions: ['read("any")', 'write("users")'],
    attributes: [
      { key: 'generation_id', type: 'string', size: 50, required: true },
      { key: 'title', type: 'string', size: 200, required: true },
      { key: 'description', type: 'string', size: 1000, required: false },
      { key: 'topic', type: 'string', size: 500, required: true },
      { key: 'target_audience', type: 'string', size: 200, required: true },
      { key: 'complexity_level', type: 'string', size: 20, required: true },
      { key: 'estimated_duration', type: 'integer', required: false },
      { key: 'lesson_count', type: 'integer', required: false },
      { key: 'lessons', type: 'string', size: 50000, required: false },
      { key: 'assessments', type: 'string', size: 10000, required: false },
      { key: 'resources', type: 'string', size: 10000, required: false },
      { key: 'agent_contributions', type: 'string', size: 50000, required: false },
      { key: 'quality_scores', type: 'string', size: 5000, required: false },
      { key: 'is_published', type: 'boolean', required: false, default: false },
      { key: 'created_at', type: 'datetime', required: true }
    ]
  },
  {
    id: 'vybe_generated_articles',
    name: 'Vybe Generated Articles',
    permissions: ['read("any")', 'write("users")'],
    attributes: [
      { key: 'generation_id', type: 'string', size: 50, required: true },
      { key: 'title', type: 'string', size: 200, required: true },
      { key: 'headline', type: 'string', size: 300, required: false },
      { key: 'content', type: 'string', size: 50000, required: true },
      { key: 'topic', type: 'string', size: 500, required: true },
      { key: 'target_audience', type: 'string', size: 200, required: true },
      { key: 'category', type: 'string', size: 50, required: false },
      { key: 'sources', type: 'string', size: 10000, required: false },
      { key: 'research_data', type: 'string', size: 20000, required: false },
      { key: 'fact_check_score', type: 'float', required: false },
      { key: 'agent_contributions', type: 'string', size: 30000, required: false },
      { key: 'is_published', type: 'boolean', required: false, default: false },
      { key: 'created_at', type: 'datetime', required: true }
    ]
  },
  {
    id: 'vybe_generated_documentation',
    name: 'Vybe Generated Documentation',
    permissions: ['read("any")', 'write("users")'],
    attributes: [
      { key: 'generation_id', type: 'string', size: 50, required: true },
      { key: 'title', type: 'string', size: 200, required: true },
      { key: 'topic', type: 'string', size: 500, required: true },
      { key: 'target_audience', type: 'string', size: 200, required: true },
      { key: 'complexity_level', type: 'string', size: 20, required: true },
      { key: 'sections', type: 'string', size: 50000, required: false },
      { key: 'code_examples', type: 'string', size: 30000, required: false },
      { key: 'api_reference', type: 'string', size: 20000, required: false },
      { key: 'technical_specs', type: 'string', size: 30000, required: false },
      { key: 'ux_design', type: 'string', size: 20000, required: false },
      { key: 'agent_contributions', type: 'string', size: 40000, required: false },
      { key: 'version', type: 'string', size: 20, required: false, default: '1.0' },
      { key: 'is_published', type: 'boolean', required: false, default: false },
      { key: 'created_at', type: 'datetime', required: true }
    ]
  },
  {
    id: 'vybe_generated_qubes',
    name: 'Vybe Generated Qubes',
    permissions: ['read("any")', 'write("users")'],
    attributes: [
      { key: 'generation_id', type: 'string', size: 50, required: true },
      { key: 'title', type: 'string', size: 200, required: true },
      { key: 'topic', type: 'string', size: 500, required: true },
      { key: 'target_audience', type: 'string', size: 200, required: true },
      { key: 'business_model', type: 'string', size: 20000, required: false },
      { key: 'product_requirements', type: 'string', size: 30000, required: false },
      { key: 'technical_architecture', type: 'string', size: 40000, required: false },
      { key: 'design_specifications', type: 'string', size: 30000, required: false },
      { key: 'implementation', type: 'string', size: 100000, required: false },
      { key: 'quality_report', type: 'string', size: 20000, required: false },
      { key: 'deployment_plan', type: 'string', size: 15000, required: false },
      { key: 'deployment_url', type: 'string', size: 200, required: false },
      { key: 'revenue_projections', type: 'string', size: 5000, required: false },
      { key: 'tech_stack', type: 'string', size: 5000, required: false },
      { key: 'agent_contributions', type: 'string', size: 50000, required: false },
      { key: 'is_deployed', type: 'boolean', required: false, default: false },
      { key: 'is_profitable', type: 'boolean', required: false, default: false },
      { key: 'monthly_revenue', type: 'float', required: false, default: 0 },
      { key: 'created_at', type: 'datetime', required: true }
    ]
  },
  {
    id: 'vybe_agent_performance',
    name: 'Vybe Agent Performance',
    permissions: ['read("any")', 'write("users")'],
    attributes: [
      { key: 'agent_id', type: 'string', size: 20, required: true },
      { key: 'generation_id', type: 'string', size: 50, required: true },
      { key: 'content_type', type: 'string', size: 50, required: true },
      { key: 'phase', type: 'string', size: 100, required: true },
      { key: 'start_time', type: 'datetime', required: true },
      { key: 'end_time', type: 'datetime', required: false },
      { key: 'duration_seconds', type: 'integer', required: false },
      { key: 'tokens_used', type: 'integer', required: false },
      { key: 'quality_score', type: 'float', required: false },
      { key: 'success', type: 'boolean', required: false, default: true },
      { key: 'error_message', type: 'string', size: 1000, required: false },
      { key: 'contribution_size', type: 'integer', required: false },
      { key: 'created_at', type: 'datetime', required: true }
    ]
  }
];

// HTTP request helper
function makeRequest(method, path, data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: config.endpoint,
      port: 443,
      path: `/v1${path}`,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'X-Appwrite-Project': config.projectId,
        'X-Appwrite-Key': config.apiKey,
      },
    };

    const req = https.request(options, res => {
      let responseData = '';

      res.on('data', chunk => {
        responseData += chunk;
      });

      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(parsed);
          } else {
            reject(new Error(parsed.message || `HTTP ${res.statusCode}`));
          }
        } catch (error) {
          reject(new Error(`Parse error: ${error.message}`));
        }
      });
    });

    req.on('error', error => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

// Create collection with error handling
async function createCollection(collectionData) {
  try {
    console.log(`📋 Creating ${collectionData.name} collection...`);

    const collection = await makeRequest(
      'POST',
      `/databases/${config.databaseId}/collections`,
      {
        collectionId: collectionData.id,
        name: collectionData.name,
        permissions: collectionData.permissions,
      }
    );

    console.log(`✅ ${collectionData.name} collection created successfully`);

    // Create attributes
    for (const attr of collectionData.attributes) {
      try {
        await createAttribute(collectionData.id, attr);
      } catch (error) {
        console.log(`⚠️  Attribute ${attr.key} error: ${error.message}`);
      }
    }

    return collection;
  } catch (error) {
    if (
      error.message.includes('already exists') ||
      error.message.includes('409')
    ) {
      console.log(`✅ ${collectionData.name} collection already exists`);
      return { $id: collectionData.id };
    } else {
      console.log(`⚠️  ${collectionData.name} collection error: ${error.message}`);
      throw error;
    }
  }
}

// Create attribute
async function createAttribute(collectionId, attr) {
  const attributeData = {
    key: attr.key,
    size: attr.size,
    required: attr.required || false,
  };

  if (attr.default !== undefined) {
    attributeData.default = attr.default;
  }

  let endpoint;
  switch (attr.type) {
    case 'string':
      endpoint = `/databases/${config.databaseId}/collections/${collectionId}/attributes/string`;
      break;
    case 'integer':
      endpoint = `/databases/${config.databaseId}/collections/${collectionId}/attributes/integer`;
      break;
    case 'float':
      endpoint = `/databases/${config.databaseId}/collections/${collectionId}/attributes/float`;
      break;
    case 'boolean':
      endpoint = `/databases/${config.databaseId}/collections/${collectionId}/attributes/boolean`;
      break;
    case 'datetime':
      endpoint = `/databases/${config.databaseId}/collections/${collectionId}/attributes/datetime`;
      delete attributeData.size; // datetime doesn't need size
      break;
    default:
      throw new Error(`Unknown attribute type: ${attr.type}`);
  }

  await makeRequest('POST', endpoint, attributeData);
  console.log(`  ✓ Attribute ${attr.key} (${attr.type}) created`);
}

// Main setup function
async function setupContentGenerationCollections() {
  try {
    console.log('🔧 Starting content generation collections setup...\n');

    for (const collection of collections) {
      await createCollection(collection);
      console.log(''); // Add spacing between collections
    }

    console.log('🎉 Content generation collections setup completed!\n');
    console.log('📋 Created collections:');
    collections.forEach(col => {
      console.log(`   ✓ ${col.name} (${col.id})`);
    });

    console.log('\n🚀 You can now use the Vybe Method Content Generator!');
    console.log('   Visit: http://localhost:5173/content/generator');

  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    process.exit(1);
  }
}

// Run setup
setupContentGenerationCollections();
