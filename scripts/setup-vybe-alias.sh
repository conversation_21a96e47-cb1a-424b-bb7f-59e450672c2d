#!/bin/bash
# 🚀 SETUP VYBE ALIAS
# Creates a shell alias so you can type 'vybe' to run npm run dev:host

set -e

echo "🚀 Setting up 'vybe' command alias..."
echo "===================================="

# Get the current project directory
PROJECT_DIR=$(pwd)
echo "📁 Project Directory: $PROJECT_DIR"

# Create the alias command
ALIAS_COMMAND="alias vybe='cd \"$PROJECT_DIR\" && npm run dev:host'"

echo "📝 Creating vybe alias..."

# Add to .bashrc
if [ -f "$HOME/.bashrc" ]; then
    # Remove any existing vybe alias
    sed -i '/alias vybe=/d' "$HOME/.bashrc" 2>/dev/null || true
    
    # Add new alias
    echo "" >> "$HOME/.bashrc"
    echo "# VybeCoding.ai development alias" >> "$HOME/.bashrc"
    echo "$ALIAS_COMMAND" >> "$HOME/.bashrc"
    echo "✅ Added vybe alias to ~/.bashrc"
fi

# Add to .zshrc if it exists
if [ -f "$HOME/.zshrc" ]; then
    # Remove any existing vybe alias
    sed -i '/alias vybe=/d' "$HOME/.zshrc" 2>/dev/null || true
    
    # Add new alias
    echo "" >> "$HOME/.zshrc"
    echo "# VybeCoding.ai development alias" >> "$HOME/.zshrc"
    echo "$ALIAS_COMMAND" >> "$HOME/.zshrc"
    echo "✅ Added vybe alias to ~/.zshrc"
fi

# Add to current session
eval "$ALIAS_COMMAND"
echo "✅ vybe alias active in current session"

echo ""
echo "🎉 Setup Complete!"
echo ""
echo "📋 Usage:"
echo "   Type: vybe"
echo "   From anywhere in your terminal!"
echo ""
echo "🌐 This will:"
echo "   1. Navigate to: $PROJECT_DIR"
echo "   2. Run: npm run dev:host"
echo "   3. Start comprehensive protected development server"
echo "   4. Enable network access for multi-device development"
echo ""
echo "🔄 To activate in new terminals, run:"
echo "   source ~/.bashrc"
echo ""
echo "🧪 Testing the alias..."

# Test the alias
if type vybe >/dev/null 2>&1; then
    echo "✅ vybe command is ready!"
    echo ""
    echo "🚀 Try it now: vybe"
else
    echo "⚠️ Alias not immediately available"
    echo "💡 Restart terminal or run: source ~/.bashrc"
fi
