#!/usr/bin/env node

/**
 * Deployment Configuration Script
 *
 * Handles environment-specific configuration for VybeCoding.ai deployments
 * Used by CI/CD pipelines to set up proper environment variables
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, '..');

/**
 * Environment configurations
 */
const environments = {
  development: {
    name: 'Development',
    domain: 'localhost:5173',
    appwriteProject: 'development',
    enableDebug: true,
    enableAnalytics: false,
    logLevel: 'debug',
  },
  staging: {
    name: 'Staging',
    domain: 'vybecoding-staging.vercel.app',
    appwriteProject: 'staging',
    enableDebug: true,
    enableAnalytics: false,
    logLevel: 'info',
  },
  production: {
    name: 'Production',
    domain: 'vybecoding.ai',
    appwriteProject: 'production',
    enableDebug: false,
    enableAnalytics: true,
    logLevel: 'error',
  },
};

/**
 * Generate environment-specific configuration
 */
function generateConfig(environment) {
  const config = environments[environment];

  if (!config) {
    throw new Error(`Unknown environment: ${environment}`);
  }

  const timestamp = new Date().toISOString();
  const commitHash = process.env.GITHUB_SHA || 'unknown';
  const branchName = process.env.GITHUB_REF_NAME || 'unknown';

  return {
    // Basic app configuration
    VITE_ENVIRONMENT: environment,
    VITE_APP_NAME:
      environment === 'production'
        ? 'VybeCoding.ai'
        : `VybeCoding.ai (${config.name})`,
    VITE_APP_VERSION: process.env.npm_package_version || '1.0.0',

    // Deployment information
    VITE_DEPLOY_TIMESTAMP: timestamp,
    VITE_COMMIT_HASH: commitHash,
    VITE_BRANCH_NAME: branchName,

    // API configuration
    VITE_API_BASE_URL: `https://${config.domain}/api`,
    VITE_API_TIMEOUT: environment === 'production' ? '15000' : '30000',

    // Feature flags
    VITE_ENABLE_DEBUG_MODE: config.enableDebug.toString(),
    VITE_ENABLE_ANALYTICS: config.enableAnalytics.toString(),
    VITE_ENABLE_ERROR_REPORTING: 'true',
    VITE_ENABLE_PERFORMANCE_MONITORING: 'true',

    // Security settings
    VITE_ENABLE_CSP: 'true',
    VITE_ENABLE_HTTPS_ONLY: (environment !== 'development').toString(),
    VITE_ENABLE_SECURE_COOKIES: (environment !== 'development').toString(),

    // Logging
    VITE_LOG_LEVEL: config.logLevel,
    VITE_ENABLE_CONSOLE_LOGS: (environment !== 'production').toString(),

    // Development tools
    VITE_ENABLE_DEVTOOLS: (environment !== 'production').toString(),
    VITE_ENABLE_SOURCE_MAPS: (environment !== 'production').toString(),

    // Build configuration
    VITE_BUILD_TARGET: environment,
    VITE_OPTIMIZE_BUNDLE: 'true',
    VITE_MINIFY_CODE: (environment === 'production').toString(),

    // Cache configuration
    VITE_CACHE_STRATEGY:
      environment === 'production' ? 'cache-first' : 'network-first',
    VITE_CACHE_TTL: environment === 'production' ? '3600' : '300',
  };
}

/**
 * Write configuration to .env file
 */
function writeEnvFile(environment, config) {
  const envFile = path.join(projectRoot, `.env.${environment}`);

  let content = `# VybeCoding.ai ${environments[environment].name} Environment Configuration\n`;
  content += `# Generated on ${new Date().toISOString()}\n`;
  content += `# Commit: ${config.VITE_COMMIT_HASH}\n\n`;

  Object.entries(config).forEach(([key, value]) => {
    content += `${key}=${value}\n`;
  });

  fs.writeFileSync(envFile, content);
  console.log(`✅ Generated ${envFile}`);
}

/**
 * Validate required environment variables
 */
function validateEnvironment(environment) {
  const required = [
    'VITE_APPWRITE_ENDPOINT',
    'VITE_APPWRITE_PROJECT_ID',
    'VITE_APPWRITE_DATABASE_ID',
  ];

  const missing = required.filter(key => !process.env[key]);

  if (missing.length > 0) {
    console.warn(
      `⚠️ Missing required environment variables for ${environment}:`
    );
    missing.forEach(key => console.warn(`  - ${key}`));
    console.warn('These should be set in GitHub Secrets for CI/CD deployments');
  }

  return missing.length === 0;
}

/**
 * Main execution
 */
function main() {
  const environment =
    process.argv[2] || process.env.VITE_ENVIRONMENT || 'development';

  console.log(`🔧 Configuring deployment for ${environment} environment...`);

  try {
    // Generate configuration
    const config = generateConfig(environment);

    // Write environment file
    writeEnvFile(environment, config);

    // Validate environment
    const isValid = validateEnvironment(environment);

    console.log(`✅ Deployment configuration complete for ${environment}`);

    if (!isValid) {
      console.log(
        '⚠️ Some environment variables are missing - check GitHub Secrets'
      );
      process.exit(1);
    }
  } catch (error) {
    console.error(`❌ Configuration failed: ${error.message}`);
    process.exit(1);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { generateConfig, validateEnvironment };
