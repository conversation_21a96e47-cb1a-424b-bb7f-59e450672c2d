#!/bin/bash

# Autonomous Milestone System for BMad Method Workflow
# Monitors agent completion and automatically creates milestones

set -e

# Configuration
AGENT_DIR="/home/<USER>/Projects/vybecoding/docs"
MILESTONE_SCRIPT="/home/<USER>/Projects/vybecoding/scripts/auto-milestone.sh"
STATE_FILE="/home/<USER>/Projects/vybecoding/.autonomous-milestones/state.json"
LOG_FILE="/home/<USER>/Projects/vybecoding/logs/autonomous-milestones.log"

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

# Logging function
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
    echo -e "${BLUE}[AUTO-MILESTONE]${NC} $1"
}

# Initialize state tracking
init_state() {
    mkdir -p "$(dirname "$STATE_FILE")"
    mkdir -p "$(dirname "$LOG_FILE")"
    
    if [ ! -f "$STATE_FILE" ]; then
        cat > "$STATE_FILE" << 'EOF'
{
  "last_check": "",
  "completed_agents": [],
  "milestones_created": [],
  "current_phase": "analysis"
}
EOF
    fi
}

# Check agent completion status
check_agent_completion() {
    local agent_file="$1"
    local agent_name="$2"
    
    # Check if agent file shows completion
    if grep -q "✅.*[Cc]omplete" "$agent_file" 2>/dev/null; then
        return 0  # Agent is complete
    fi
    
    # Check for specific completion markers
    if grep -qE "(Status.*Complete|Phase.*Complete|Ready for handoff)" "$agent_file" 2>/dev/null; then
        return 0  # Agent is complete
    fi
    
    return 1  # Agent not complete
}

# Get current state
get_state() {
    if [ -f "$STATE_FILE" ]; then
        cat "$STATE_FILE"
    else
        echo "{}"
    fi
}

# Update state
update_state() {
    local key="$1"
    local value="$2"
    
    local temp_file=$(mktemp)
    jq ".$key = $value" "$STATE_FILE" > "$temp_file" && mv "$temp_file" "$STATE_FILE"
}

# Add completed agent to state
add_completed_agent() {
    local agent="$1"
    local temp_file=$(mktemp)
    
    jq ".completed_agents += [\"$agent\"] | .completed_agents |= unique" "$STATE_FILE" > "$temp_file" && mv "$temp_file" "$STATE_FILE"
}

# Create milestone for agent completion
create_agent_milestone() {
    local agent_num="$1"
    local agent_name="$2"
    local description="$3"
    
    log_message "Creating milestone for Agent $agent_num ($agent_name) completion"
    
    # Create milestone using existing script
    "$MILESTONE_SCRIPT" create feature "BMad Method Agent-$agent_num-$agent_name Complete: $description" force
    
    # Record milestone creation
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    local temp_file=$(mktemp)
    jq ".milestones_created += [{\"agent\": \"$agent_num-$agent_name\", \"timestamp\": \"$timestamp\", \"description\": \"$description\"}]" "$STATE_FILE" > "$temp_file" && mv "$temp_file" "$STATE_FILE"
    
    log_message "Milestone created successfully for Agent $agent_num ($agent_name)"
}

# Check all agents and create milestones as needed
monitor_agents() {
    local current_completed=$(jq -r '.completed_agents[]' "$STATE_FILE" 2>/dev/null || echo "")
    
    # Define agent completion checks
    declare -A agents=(
        ["1"]="mary:Strategic Analysis and Business Model Validation"
        ["2"]="john:PRD Development and Technical Requirements" 
        ["3"]="alex:Technical Architecture and System Design"
        ["4"]="maya:Frontend Development and UI Implementation"
        ["5"]="sarah:Backend Development and API Implementation"
        ["6"]="bob:DevOps Infrastructure and Deployment"
        ["7"]="larry:Quality Assurance and Testing"
    )
    
    for agent_num in "${!agents[@]}"; do
        local agent_info="${agents[$agent_num]}"
        local agent_name="${agent_info%%:*}"
        local agent_description="${agent_info##*:}"
        local agent_file="$AGENT_DIR/agent-$agent_num-$agent_name.md"
        local agent_key="$agent_num-$agent_name"
        
        # Skip if already processed
        if echo "$current_completed" | grep -q "^$agent_key$"; then
            continue
        fi
        
        # Check if agent is complete
        if check_agent_completion "$agent_file" "$agent_name"; then
            log_message "Detected completion: Agent $agent_num ($agent_name)"
            
            # Create milestone
            create_agent_milestone "$agent_num" "$agent_name" "$agent_description"
            
            # Mark as completed
            add_completed_agent "$agent_key"
            
            # Update current phase
            case "$agent_num" in
                "1"|"2") update_state "current_phase" "\"planning\"" ;;
                "3") update_state "current_phase" "\"architecture\"" ;;
                "4"|"5"|"6") update_state "current_phase" "\"development\"" ;;
                "7") update_state "current_phase" "\"testing\"" ;;
            esac
            
            log_message "Agent $agent_num ($agent_name) marked as complete"
        fi
    done
    
    # Update last check timestamp
    update_state "last_check" "\"$(date -u +"%Y-%m-%dT%H:%M:%SZ")\""
}

# Main monitoring function
run_monitor() {
    log_message "Starting autonomous milestone monitoring"
    
    init_state
    monitor_agents
    
    log_message "Monitoring cycle complete"
}

# Continuous monitoring mode
continuous_monitor() {
    log_message "Starting continuous autonomous milestone monitoring"
    
    while true; do
        run_monitor
        sleep 300  # Check every 5 minutes
    done
}

# Show status
show_status() {
    if [ ! -f "$STATE_FILE" ]; then
        echo "No state file found. Run 'init' first."
        exit 1
    fi
    
    echo -e "${GREEN}=== Autonomous Milestone Status ===${NC}"
    echo
    
    local state=$(cat "$STATE_FILE")
    local last_check=$(echo "$state" | jq -r '.last_check')
    local current_phase=$(echo "$state" | jq -r '.current_phase')
    local completed_agents=$(echo "$state" | jq -r '.completed_agents[]' 2>/dev/null || echo "None")
    
    echo "Last Check: $last_check"
    echo "Current Phase: $current_phase"
    echo "Completed Agents:"
    
    if [ "$completed_agents" = "None" ]; then
        echo "  - None yet"
    else
        echo "$completed_agents" | while read -r agent; do
            echo "  - Agent $agent ✅"
        done
    fi
    
    echo
    echo "Created Milestones:"
    echo "$state" | jq -r '.milestones_created[] | "  - \(.agent): \(.description) (\(.timestamp))"' 2>/dev/null || echo "  - None yet"
}

# Install as git hook
install_git_hook() {
    local hook_file=".git/hooks/post-commit"
    
    cat > "$hook_file" << 'EOF'
#!/bin/bash
# Auto-run milestone monitoring after commits
/home/<USER>/Projects/vybecoding/scripts/autonomous-milestones.sh monitor
EOF
    
    chmod +x "$hook_file"
    log_message "Git post-commit hook installed"
}

# Help function
show_help() {
    echo "Autonomous Milestone System for BMad Method"
    echo
    echo "Usage: $0 [command]"
    echo
    echo "Commands:"
    echo "  monitor      - Run single monitoring cycle"
    echo "  continuous   - Start continuous monitoring (every 5 minutes)"
    echo "  status       - Show current status and completed agents"
    echo "  init         - Initialize state tracking"
    echo "  install-hook - Install git post-commit hook"
    echo "  help         - Show this help"
    echo
    echo "The system automatically detects when BMad Method agents complete"
    echo "their phases and creates milestones accordingly."
}

# Main script logic
case "${1:-monitor}" in
    "monitor")
        run_monitor
        ;;
    "continuous")
        continuous_monitor
        ;;
    "status")
        show_status
        ;;
    "init")
        init_state
        log_message "Autonomous milestone system initialized"
        ;;
    "install-hook")
        install_git_hook
        ;;
    "help"|"-h"|"--help")
        show_help
        ;;
    *)
        echo "Unknown command: $1"
        show_help
        exit 1
        ;;
esac
