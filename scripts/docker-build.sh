#!/bin/bash

# VybeCoding.ai Docker Production Build Script
# Optimized production container builds with security scanning

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
IMAGE_NAME="vybecoding"
REGISTRY="ghcr.io/hiram-ducky"
VERSION=${VERSION:-$(git rev-parse --short HEAD 2>/dev/null || echo "latest")}
BUILD_DATE=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
GIT_COMMIT=$(git rev-parse HEAD 2>/dev/null || echo "unknown")

# Function to build production image
build_production() {
    local tag=${1:-$VERSION}
    local full_tag="$IMAGE_NAME:$tag"
    
    print_status "Building production image: $full_tag"
    print_status "Build date: $BUILD_DATE"
    print_status "Git commit: $GIT_COMMIT"
    
    # Build with build args for metadata
    docker build \
        --file Dockerfile \
        --tag $full_tag \
        --build-arg BUILD_DATE="$BUILD_DATE" \
        --build-arg GIT_COMMIT="$GIT_COMMIT" \
        --build-arg VERSION="$tag" \
        --no-cache \
        .
    
    print_success "Production image built: $full_tag"
    
    # Show image size
    local size=$(docker images $full_tag --format "{{.Size}}")
    print_status "Image size: $size"
}

# Function to build development image
build_development() {
    local tag="dev"
    local full_tag="$IMAGE_NAME:$tag"
    
    print_status "Building development image: $full_tag"
    
    docker build \
        --file Dockerfile.dev \
        --tag $full_tag \
        --build-arg BUILD_DATE="$BUILD_DATE" \
        --build-arg GIT_COMMIT="$GIT_COMMIT" \
        .
    
    print_success "Development image built: $full_tag"
}

# Function to scan image for vulnerabilities
scan_image() {
    local tag=${1:-$VERSION}
    local full_tag="$IMAGE_NAME:$tag"
    
    print_status "Scanning image for vulnerabilities: $full_tag"
    
    # Check if trivy is available
    if command -v trivy &> /dev/null; then
        trivy image --exit-code 1 --severity HIGH,CRITICAL $full_tag
        print_success "Security scan completed - no high/critical vulnerabilities found"
    else
        print_warning "Trivy not found - skipping security scan"
        print_status "Install trivy for security scanning: https://aquasecurity.github.io/trivy/"
    fi
}

# Function to test image
test_image() {
    local tag=${1:-$VERSION}
    local full_tag="$IMAGE_NAME:$tag"
    local container_name="vybecoding-test-$$"
    
    print_status "Testing image: $full_tag"
    
    # Start container in background
    docker run -d \
        --name $container_name \
        --publish 3001:3000 \
        $full_tag
    
    # Wait for container to start
    sleep 10
    
    # Test health endpoint
    if curl -f http://localhost:3001/api/health &> /dev/null; then
        print_success "Image test passed - health endpoint responding"
    else
        print_error "Image test failed - health endpoint not responding"
        docker logs $container_name
        docker stop $container_name
        docker rm $container_name
        exit 1
    fi
    
    # Cleanup
    docker stop $container_name
    docker rm $container_name
    
    print_success "Image testing completed"
}

# Function to tag image for registry
tag_for_registry() {
    local tag=${1:-$VERSION}
    local source_tag="$IMAGE_NAME:$tag"
    local registry_tag="$REGISTRY/$IMAGE_NAME:$tag"
    local latest_tag="$REGISTRY/$IMAGE_NAME:latest"
    
    print_status "Tagging image for registry..."
    
    # Tag with version
    docker tag $source_tag $registry_tag
    print_status "Tagged: $registry_tag"
    
    # Tag as latest if this is a release
    if [[ "$tag" != "dev" && "$tag" != *"-"* ]]; then
        docker tag $source_tag $latest_tag
        print_status "Tagged: $latest_tag"
    fi
    
    print_success "Registry tagging completed"
}

# Function to push to registry
push_to_registry() {
    local tag=${1:-$VERSION}
    local registry_tag="$REGISTRY/$IMAGE_NAME:$tag"
    
    print_status "Pushing to registry: $registry_tag"
    
    # Check if logged in to registry
    if ! docker info | grep -q "Registry:"; then
        print_warning "Not logged in to registry. Run: docker login $REGISTRY"
    fi
    
    docker push $registry_tag
    
    # Push latest tag if applicable
    if [[ "$tag" != "dev" && "$tag" != *"-"* ]]; then
        docker push "$REGISTRY/$IMAGE_NAME:latest"
    fi
    
    print_success "Push to registry completed"
}

# Function to build multi-platform images
build_multiplatform() {
    local tag=${1:-$VERSION}
    local registry_tag="$REGISTRY/$IMAGE_NAME:$tag"
    
    print_status "Building multi-platform image: $registry_tag"
    
    # Check if buildx is available
    if ! docker buildx version &> /dev/null; then
        print_error "Docker buildx is required for multi-platform builds"
        exit 1
    fi
    
    # Create builder if it doesn't exist
    docker buildx create --name vybecoding-builder --use 2>/dev/null || true
    
    # Build and push multi-platform image
    docker buildx build \
        --platform linux/amd64,linux/arm64 \
        --file Dockerfile \
        --tag $registry_tag \
        --build-arg BUILD_DATE="$BUILD_DATE" \
        --build-arg GIT_COMMIT="$GIT_COMMIT" \
        --build-arg VERSION="$tag" \
        --push \
        .
    
    print_success "Multi-platform build completed: $registry_tag"
}

# Function to show image information
show_info() {
    local tag=${1:-$VERSION}
    local full_tag="$IMAGE_NAME:$tag"
    
    echo "VybeCoding.ai Docker Image Information"
    echo "====================================="
    echo ""
    echo "🏷️  Image: $full_tag"
    echo "📅 Build Date: $BUILD_DATE"
    echo "🔗 Git Commit: $GIT_COMMIT"
    echo "📦 Version: $tag"
    echo ""
    
    if docker images $full_tag --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}" | grep -q $IMAGE_NAME; then
        echo "📊 Image Details:"
        docker images $full_tag --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
        echo ""
        
        echo "🔍 Image Layers:"
        docker history $full_tag --format "table {{.CreatedBy}}\t{{.Size}}" | head -10
    else
        echo "❌ Image not found locally"
    fi
}

# Function to clean up build artifacts
cleanup() {
    print_status "Cleaning up build artifacts..."
    
    # Remove dangling images
    docker image prune -f
    
    # Remove build cache
    docker builder prune -f
    
    print_success "Cleanup completed"
}

# Function to show help
show_help() {
    echo "VybeCoding.ai Docker Production Build Script"
    echo "==========================================="
    echo ""
    echo "Usage: $0 [COMMAND] [TAG]"
    echo ""
    echo "Build Commands:"
    echo "  prod [tag]         - Build production image"
    echo "  dev                - Build development image"
    echo "  all [tag]          - Build both production and development images"
    echo "  multiplatform [tag] - Build multi-platform image (requires buildx)"
    echo ""
    echo "Quality Commands:"
    echo "  scan [tag]         - Scan image for vulnerabilities"
    echo "  test [tag]         - Test image functionality"
    echo ""
    echo "Registry Commands:"
    echo "  tag [tag]          - Tag image for registry"
    echo "  push [tag]         - Push image to registry"
    echo "  release [tag]      - Build, scan, test, tag, and push"
    echo ""
    echo "Utility Commands:"
    echo "  info [tag]         - Show image information"
    echo "  cleanup            - Clean up build artifacts"
    echo "  help               - Show this help message"
    echo ""
    echo "Environment Variables:"
    echo "  VERSION            - Image version tag (default: git commit hash)"
    echo "  REGISTRY           - Container registry (default: ghcr.io/hiram-ducky)"
    echo ""
    echo "Examples:"
    echo "  $0 prod v1.0.0     - Build production image with tag v1.0.0"
    echo "  $0 release         - Full release build with current git hash"
    echo "  $0 scan            - Scan latest built image"
}

# Main script logic
main() {
    case ${1:-help} in
        "prod"|"production")
            build_production ${2:-$VERSION}
            ;;
        "dev"|"development")
            build_development
            ;;
        "all")
            build_production ${2:-$VERSION}
            build_development
            ;;
        "multiplatform"|"multi")
            build_multiplatform ${2:-$VERSION}
            ;;
        "scan")
            scan_image ${2:-$VERSION}
            ;;
        "test")
            test_image ${2:-$VERSION}
            ;;
        "tag")
            tag_for_registry ${2:-$VERSION}
            ;;
        "push")
            push_to_registry ${2:-$VERSION}
            ;;
        "release")
            local tag=${2:-$VERSION}
            build_production $tag
            scan_image $tag
            test_image $tag
            tag_for_registry $tag
            push_to_registry $tag
            print_success "Release build completed: $tag"
            ;;
        "info")
            show_info ${2:-$VERSION}
            ;;
        "cleanup")
            cleanup
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# Run main function with all arguments
main "$@"
