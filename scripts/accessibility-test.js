#!/usr/bin/env node

/**
 * Comprehensive Accessibility Testing Script
 * Tests all pages for WCAG compliance and accessibility issues
 */

import { chromium } from 'playwright';

const BASE_URL = 'http://localhost:5173';

// All pages to test
const PAGES_TO_TEST = [
  // Main Pages
  { name: 'Home', path: '/' },
  { name: 'About', path: '/about' },
  { name: 'Contact', path: '/contact' },
  { name: 'Blog', path: '/blog' },
  { name: 'Careers', path: '/careers' },
  { name: 'Enterprise', path: '/enterprise' },
  { name: 'Support', path: '/support' },
  { name: 'Status', path: '/status' },

  // Learning & Development
  { name: 'Courses', path: '/courses' },
  { name: 'Methods', path: '/methods' },
  { name: 'Documentation', path: '/docs' },
  { name: 'Workspace', path: '/workspace' },
  { name: 'Dashboard', path: '/dashboard' },

  // AI & Automation
  { name: 'MAS Control', path: '/mas' },
  { name: 'Autonomous', path: '/autonomous' },
  { name: 'Vybe Qubes', path: '/vybeqube' },
  { name: 'Vybe Demo', path: '/vybe-demo' },
  { name: 'Vybe Demo Simple', path: '/vybe-demo-simple' },

  // Community & Social
  { name: 'Community', path: '/community' },

  // Business & Pricing
  { name: 'Pricing', path: '/pricing' },

  // Authentication
  { name: 'Sign In', path: '/auth/signin' },
  { name: 'Sign Up', path: '/auth/signup' },

  // Legal & Compliance
  { name: 'Privacy Policy', path: '/privacy' },
  { name: 'Terms of Service', path: '/terms' },
  { name: 'Cookie Policy', path: '/cookies' },
  { name: 'GDPR', path: '/gdpr' },

  // Testing & Development
  { name: 'Accessibility Audit', path: '/accessibility-audit' },
  { name: 'Accessibility Advanced', path: '/accessibility-advanced' },
  { name: 'Help Navigation', path: '/help/navigation' },
];

async function testPageAccessibility(page, pageName, path) {
  console.log(`\n🔍 Testing: ${pageName} (${path})`);

  try {
    // Navigate to page
    await page.goto(`${BASE_URL}${path}`, {
      waitUntil: 'networkidle',
      timeout: 30000,
    });

    // Wait for page to be fully loaded
    await page.waitForTimeout(2000);

    // Basic accessibility checks without axe for now
    const basicChecks = await page.evaluate(() => {
      const issues = [];

      // Check for images without alt text
      const imagesWithoutAlt = document.querySelectorAll('img:not([alt])');
      if (imagesWithoutAlt.length > 0) {
        issues.push({
          type: 'missing-alt-text',
          count: imagesWithoutAlt.length,
          description: 'Images without alt text found',
        });
      }

      // Check for buttons without accessible names
      const buttonsWithoutNames = Array.from(
        document.querySelectorAll('button')
      ).filter(btn => {
        return (
          !btn.textContent?.trim() &&
          !btn.getAttribute('aria-label') &&
          !btn.getAttribute('aria-labelledby')
        );
      });
      if (buttonsWithoutNames.length > 0) {
        issues.push({
          type: 'button-no-name',
          count: buttonsWithoutNames.length,
          description: 'Buttons without accessible names found',
        });
      }

      // Check for form inputs without labels
      const inputsWithoutLabels = Array.from(
        document.querySelectorAll(
          'input[type="text"], input[type="email"], input[type="password"], textarea'
        )
      ).filter(input => {
        const id = input.id;
        const hasLabel = id && document.querySelector(`label[for="${id}"]`);
        const hasAriaLabel = input.getAttribute('aria-label');
        const hasAriaLabelledby = input.getAttribute('aria-labelledby');
        return !hasLabel && !hasAriaLabel && !hasAriaLabelledby;
      });
      if (inputsWithoutLabels.length > 0) {
        issues.push({
          type: 'input-no-label',
          count: inputsWithoutLabels.length,
          description: 'Form inputs without labels found',
        });
      }

      // Check for headings hierarchy
      const headings = Array.from(
        document.querySelectorAll('h1, h2, h3, h4, h5, h6')
      );
      const headingLevels = headings.map(h => parseInt(h.tagName.charAt(1)));
      let hierarchyIssues = 0;
      for (let i = 1; i < headingLevels.length; i++) {
        if (headingLevels[i] > headingLevels[i - 1] + 1) {
          hierarchyIssues++;
        }
      }
      if (hierarchyIssues > 0) {
        issues.push({
          type: 'heading-hierarchy',
          count: hierarchyIssues,
          description: 'Heading hierarchy issues found',
        });
      }

      return issues;
    });

    const violations = basicChecks;

    if (violations.length === 0) {
      console.log(`✅ ${pageName}: No accessibility violations found`);
    } else {
      console.log(
        `❌ ${pageName}: ${violations.length} accessibility violations found`
      );

      violations.forEach((violation, index) => {
        console.log(
          `\n  ${index + 1}. ${violation.id}: ${violation.description}`
        );
        console.log(`     Impact: ${violation.impact}`);
        console.log(`     Help: ${violation.helpUrl}`);
        console.log(`     Elements affected: ${violation.nodes.length}`);

        // Show first few affected elements
        violation.nodes.slice(0, 3).forEach((node, nodeIndex) => {
          console.log(`       ${nodeIndex + 1}. ${node.target.join(' ')}`);
          if (node.failureSummary) {
            console.log(`          ${node.failureSummary}`);
          }
        });
      });
    }

    // Test keyboard navigation
    await testKeyboardNavigation(page, pageName);

    // Test color contrast
    await testColorContrast(page, pageName);

    return {
      page: pageName,
      path,
      violations: violations.length,
      details: violations,
    };
  } catch (error) {
    console.log(`❌ ${pageName}: Error testing page - ${error.message}`);
    return {
      page: pageName,
      path,
      violations: -1,
      error: error.message,
    };
  }
}

async function testKeyboardNavigation(page, pageName) {
  try {
    // Test Tab navigation
    await page.keyboard.press('Tab');
    const focusedElement = await page.evaluate(() => {
      const focused = document.activeElement;
      return focused
        ? focused.tagName +
            (focused.className ? '.' + focused.className.split(' ')[0] : '')
        : 'none';
    });

    if (focusedElement !== 'none') {
      console.log(
        `⌨️  ${pageName}: Keyboard navigation working (focused: ${focusedElement})`
      );
    } else {
      console.log(`⚠️  ${pageName}: No focusable elements found`);
    }
  } catch (error) {
    console.log(
      `⚠️  ${pageName}: Keyboard navigation test failed - ${error.message}`
    );
  }
}

async function testColorContrast(page, pageName) {
  try {
    // Get all text elements and check their contrast
    const contrastIssues = await page.evaluate(() => {
      const textElements = document.querySelectorAll(
        'p, h1, h2, h3, h4, h5, h6, span, a, button, label'
      );
      const issues = [];

      textElements.forEach((element, index) => {
        if (index > 50) return; // Limit to first 50 elements for performance

        const styles = window.getComputedStyle(element);
        const color = styles.color;
        const backgroundColor = styles.backgroundColor;

        // Simple contrast check (would need more sophisticated algorithm for real testing)
        if (
          color &&
          backgroundColor &&
          color !== 'rgba(0, 0, 0, 0)' &&
          backgroundColor !== 'rgba(0, 0, 0, 0)'
        ) {
          // This is a simplified check - in real implementation, you'd calculate actual contrast ratio
          const text = element.textContent?.trim();
          if (text && text.length > 0) {
            issues.push({
              text: text.substring(0, 50),
              color,
              backgroundColor,
              tagName: element.tagName,
            });
          }
        }
      });

      return issues.slice(0, 5); // Return first 5 for brevity
    });

    if (contrastIssues.length > 0) {
      console.log(
        `🎨 ${pageName}: Found ${contrastIssues.length} elements to check for contrast`
      );
    }
  } catch (error) {
    console.log(
      `⚠️  ${pageName}: Color contrast test failed - ${error.message}`
    );
  }
}

async function generateReport(results) {
  console.log('\n' + '='.repeat(80));
  console.log('📊 ACCESSIBILITY TEST SUMMARY');
  console.log('='.repeat(80));

  const totalPages = results.length;
  const pagesWithViolations = results.filter(r => r.violations > 0).length;
  const pagesWithErrors = results.filter(r => r.violations === -1).length;
  const totalViolations = results.reduce(
    (sum, r) => sum + (r.violations > 0 ? r.violations : 0),
    0
  );

  console.log(`\n📈 Overall Statistics:`);
  console.log(`   Total pages tested: ${totalPages}`);
  console.log(`   Pages with violations: ${pagesWithViolations}`);
  console.log(`   Pages with errors: ${pagesWithErrors}`);
  console.log(`   Total violations: ${totalViolations}`);
  console.log(
    `   Success rate: ${(((totalPages - pagesWithViolations - pagesWithErrors) / totalPages) * 100).toFixed(1)}%`
  );

  console.log(`\n🔴 Pages with most violations:`);
  results
    .filter(r => r.violations > 0)
    .sort((a, b) => b.violations - a.violations)
    .slice(0, 5)
    .forEach(result => {
      console.log(`   ${result.page}: ${result.violations} violations`);
    });

  console.log(`\n✅ Pages with no violations:`);
  results
    .filter(r => r.violations === 0)
    .forEach(result => {
      console.log(`   ${result.page}`);
    });

  if (pagesWithErrors > 0) {
    console.log(`\n❌ Pages with errors:`);
    results
      .filter(r => r.violations === -1)
      .forEach(result => {
        console.log(`   ${result.page}: ${result.error}`);
      });
  }
}

async function main() {
  console.log('🚀 Starting Comprehensive Accessibility Testing');
  console.log(`Testing ${PAGES_TO_TEST.length} pages...`);

  const browser = await chromium.launch({
    headless: true,
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
    ],
  });
  const context = await browser.newContext();
  const page = await context.newPage();

  const results = [];

  for (const pageInfo of PAGES_TO_TEST) {
    const result = await testPageAccessibility(
      page,
      pageInfo.name,
      pageInfo.path
    );
    results.push(result);

    // Small delay between tests
    await page.waitForTimeout(1000);
  }

  await browser.close();

  // Generate comprehensive report
  await generateReport(results);

  console.log('\n🎉 Accessibility testing completed!');
  console.log(
    '💡 Check the output above for detailed results and recommendations.'
  );
}

// Run the tests
main().catch(console.error);
