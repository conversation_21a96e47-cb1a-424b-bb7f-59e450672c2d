#!/bin/bash

# Development helper scripts for VybeCoding.ai

case "$1" in
  "start")
    echo "🚀 Starting VybeCoding.ai development server..."
    npm run dev
    ;;
  "test")
    echo "🧪 Running tests..."
    npm run test
    ;;
  "build")
    echo "🏗️  Building for production..."
    npm run build
    ;;
  "preview")
    echo "👀 Preview production build..."
    npm run preview
    ;;
  "lint")
    echo "🔍 Linting code..."
    npm run lint
    ;;
  *)
    echo "VybeCoding.ai Development Helper"
    echo "Usage: ./scripts/dev-helpers.sh [command]"
    echo ""
    echo "Commands:"
    echo "  start   - Start development server"
    echo "  test    - Run tests"
    echo "  build   - Build for production"
    echo "  preview - Preview production build"
    echo "  lint    - Lint code"
    ;;
esac
