#!/usr/bin/env node
// VybeCoding.ai Test User Creation Script
// Creates test users for performance testing

const https = require('https');
const http = require('http');

// Configuration
const BASE_URL = process.env.BASE_URL || 'http://localhost:3000';
const ADMIN_EMAIL = process.env.ADMIN_EMAIL || '<EMAIL>';
const ADMIN_PASSWORD = process.env.ADMIN_PASSWORD || 'AdminPass123!';

// Test user profiles
const TEST_USERS = [
  // Students
  ...Array.from({ length: 50 }, (_, i) => ({
    email: `student${i + 1}@vybecoding.ai`,
    password: 'StudentPass123!',
    name: `Test Student ${i + 1}`,
    role: 'student',
    profile: {
      learningStyle: ['visual', 'hands-on', 'reading', 'mixed'][i % 4],
      preferredPace: ['slow', 'medium', 'fast'][i % 3],
      experience: ['beginner', 'intermediate', 'advanced'][i % 3],
    },
  })),

  // Instructors
  ...Array.from({ length: 10 }, (_, i) => ({
    email: `instructor${i + 1}@vybecoding.ai`,
    password: 'InstructorPass123!',
    name: `Test Instructor ${i + 1}`,
    role: 'instructor',
    profile: {
      experience: ['intermediate', 'senior', 'expert'][i % 3],
      specialization: ['web-dev', 'ai-ml', 'mobile', 'data-science', 'devops'][
        i % 5
      ],
      yearsExperience: Math.floor(Math.random() * 15) + 2,
    },
  })),

  // Stress test users
  ...Array.from({ length: 100 }, (_, i) => ({
    email: `stress_user_${i}@vybecoding.ai`,
    password: 'StressTest123!',
    name: `Stress Test User ${i}`,
    role: 'student',
    profile: {
      testUser: true,
      batchId: Math.floor(i / 10),
    },
  })),
];

// HTTP request helper
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const client = isHttps ? https : http;

    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'VybeCoding-TestSetup/1.0',
        ...options.headers,
      },
    };

    const req = client.request(requestOptions, res => {
      let data = '';

      res.on('data', chunk => {
        data += chunk;
      });

      res.on('end', () => {
        try {
          const response = {
            status: res.statusCode,
            headers: res.headers,
            body: data,
            json: () => JSON.parse(data),
          };
          resolve(response);
        } catch (error) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            body: data,
            json: () => null,
          });
        }
      });
    });

    req.on('error', reject);

    if (options.body) {
      req.write(
        typeof options.body === 'string'
          ? options.body
          : JSON.stringify(options.body)
      );
    }

    req.end();
  });
}

// Authenticate as admin
async function authenticateAdmin() {
  console.log('🔐 Authenticating as admin...');

  try {
    const response = await makeRequest(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      body: {
        email: ADMIN_EMAIL,
        password: ADMIN_PASSWORD,
      },
    });

    if (response.status === 200 || response.status === 201) {
      const data = response.json();
      const token = data?.token || data?.access_token || data?.jwt;

      if (token) {
        console.log('✅ Admin authentication successful');
        return token;
      }
    }

    console.log(
      '⚠️  Admin authentication failed, proceeding without admin token'
    );
    return null;
  } catch (error) {
    console.log('⚠️  Admin authentication error:', error.message);
    return null;
  }
}

// Create a single user
async function createUser(user, adminToken) {
  try {
    const headers = {};
    if (adminToken) {
      headers.Authorization = `Bearer ${adminToken}`;
    }

    // Try to create user via API
    const response = await makeRequest(`${BASE_URL}/api/auth/register`, {
      method: 'POST',
      headers,
      body: {
        email: user.email,
        password: user.password,
        name: user.name,
        role: user.role,
        profile: user.profile,
        testUser: true, // Mark as test user
      },
    });

    if (response.status === 200 || response.status === 201) {
      return { success: true, user: user.email };
    } else if (response.status === 409) {
      // User already exists
      return { success: true, user: user.email, existed: true };
    } else {
      return {
        success: false,
        user: user.email,
        error: `HTTP ${response.status}: ${response.body}`,
      };
    }
  } catch (error) {
    return {
      success: false,
      user: user.email,
      error: error.message,
    };
  }
}

// Create users in batches
async function createUsersInBatches(users, adminToken, batchSize = 10) {
  console.log(
    `👥 Creating ${users.length} test users in batches of ${batchSize}...`
  );

  const results = {
    created: 0,
    existed: 0,
    failed: 0,
    errors: [],
  };

  for (let i = 0; i < users.length; i += batchSize) {
    const batch = users.slice(i, i + batchSize);
    console.log(
      `📦 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(users.length / batchSize)}...`
    );

    // Process batch concurrently
    const batchPromises = batch.map(user => createUser(user, adminToken));
    const batchResults = await Promise.all(batchPromises);

    // Process results
    batchResults.forEach(result => {
      if (result.success) {
        if (result.existed) {
          results.existed++;
        } else {
          results.created++;
        }
      } else {
        results.failed++;
        results.errors.push(`${result.user}: ${result.error}`);
      }
    });

    // Brief pause between batches to avoid overwhelming the server
    if (i + batchSize < users.length) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  return results;
}

// Create test courses for users to interact with
async function createTestCourses(adminToken) {
  console.log('📚 Creating test courses...');

  const testCourses = [
    {
      title: 'Introduction to AI - Load Test',
      description: 'Basic AI concepts for performance testing',
      difficulty: 'beginner',
      category: 'artificial-intelligence',
      estimatedDuration: 120,
      isPublished: true,
      testCourse: true,
    },
    {
      title: 'Web Development Fundamentals - Load Test',
      description: 'HTML, CSS, JavaScript basics for testing',
      difficulty: 'beginner',
      category: 'web-development',
      estimatedDuration: 180,
      isPublished: true,
      testCourse: true,
    },
    {
      title: 'Advanced JavaScript - Load Test',
      description: 'Advanced JS concepts for performance testing',
      difficulty: 'intermediate',
      category: 'web-development',
      estimatedDuration: 240,
      isPublished: true,
      testCourse: true,
    },
    {
      title: 'Machine Learning Basics - Load Test',
      description: 'ML fundamentals for load testing',
      difficulty: 'intermediate',
      category: 'machine-learning',
      estimatedDuration: 300,
      isPublished: true,
      testCourse: true,
    },
    {
      title: 'Full Stack Development - Load Test',
      description: 'Complete full stack course for testing',
      difficulty: 'advanced',
      category: 'web-development',
      estimatedDuration: 480,
      isPublished: true,
      testCourse: true,
    },
  ];

  const results = {
    created: 0,
    existed: 0,
    failed: 0,
  };

  for (const course of testCourses) {
    try {
      const headers = {};
      if (adminToken) {
        headers.Authorization = `Bearer ${adminToken}`;
      }

      const response = await makeRequest(`${BASE_URL}/api/courses`, {
        method: 'POST',
        headers,
        body: course,
      });

      if (response.status === 200 || response.status === 201) {
        results.created++;
      } else if (response.status === 409) {
        results.existed++;
      } else {
        results.failed++;
        console.log(
          `⚠️  Failed to create course "${course.title}": HTTP ${response.status}`
        );
      }
    } catch (error) {
      results.failed++;
      console.log(
        `⚠️  Error creating course "${course.title}":`,
        error.message
      );
    }
  }

  return results;
}

// Verify test setup
async function verifyTestSetup() {
  console.log('🔍 Verifying test setup...');

  try {
    // Test basic connectivity
    const healthResponse = await makeRequest(`${BASE_URL}/api/health`);
    if (healthResponse.status !== 200) {
      console.log('⚠️  Health check failed');
      return false;
    }

    // Test user authentication
    const testUser = TEST_USERS.find(u => u.role === 'student');
    const authResponse = await makeRequest(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      body: {
        email: testUser.email,
        password: testUser.password,
      },
    });

    if (authResponse.status === 200 || authResponse.status === 201) {
      console.log('✅ Test user authentication verified');
      return true;
    } else {
      console.log('⚠️  Test user authentication failed');
      return false;
    }
  } catch (error) {
    console.log('⚠️  Verification error:', error.message);
    return false;
  }
}

// Main execution
async function main() {
  console.log('🚀 VybeCoding.ai Test Data Setup');
  console.log('================================');
  console.log(`Target URL: ${BASE_URL}`);
  console.log(`Users to create: ${TEST_USERS.length}`);

  try {
    // Authenticate as admin
    const adminToken = await authenticateAdmin();

    // Create test users
    const userResults = await createUsersInBatches(TEST_USERS, adminToken);

    console.log('\n👥 User Creation Results:');
    console.log(`✅ Created: ${userResults.created}`);
    console.log(`ℹ️  Already existed: ${userResults.existed}`);
    console.log(`❌ Failed: ${userResults.failed}`);

    if (userResults.errors.length > 0) {
      console.log('\n❌ Errors:');
      userResults.errors.slice(0, 5).forEach(error => {
        console.log(`   ${error}`);
      });
      if (userResults.errors.length > 5) {
        console.log(`   ... and ${userResults.errors.length - 5} more errors`);
      }
    }

    // Create test courses
    const courseResults = await createTestCourses(adminToken);

    console.log('\n📚 Course Creation Results:');
    console.log(`✅ Created: ${courseResults.created}`);
    console.log(`ℹ️  Already existed: ${courseResults.existed}`);
    console.log(`❌ Failed: ${courseResults.failed}`);

    // Verify setup
    const setupValid = await verifyTestSetup();

    console.log('\n🎯 Setup Summary:');
    console.log(
      `Status: ${setupValid ? '✅ Ready for testing' : '⚠️  Issues detected'}`
    );
    console.log(
      `Total users available: ${userResults.created + userResults.existed}`
    );
    console.log(
      `Total courses available: ${courseResults.created + courseResults.existed}`
    );

    if (setupValid) {
      console.log('\n🚀 Test environment is ready for performance testing!');
      process.exit(0);
    } else {
      console.log(
        '\n⚠️  Test environment has issues. Performance tests may fail.'
      );
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = {
  createUsersInBatches,
  createTestCourses,
  verifyTestSetup,
  TEST_USERS,
};
