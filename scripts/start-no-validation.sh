#!/bin/bash
"""
🚀 VYBE DEVELOPMENT SERVER (NO VALIDATION)
Starts comprehensive development server without anti-simulation validation
"""

set -e

echo "🚀 VybeCoding.ai Development Server (Testing Mode)"
echo "================================================="
echo "🌐 Network Access: ENABLED (--host)"
echo "⚠️ Anti-Simulation Protection: SKIPPED (testing mode)"
echo "🚀 All Services: MAS Observatory, Portainer, Dev Server"
echo ""

# Function to cleanup all background processes
cleanup() {
    echo ""
    echo "🛑 Shutting down all services..."
    
    # Kill the anti-simulation watcher
    if [ ! -z "$WATCHER_PID" ]; then
        kill $WATCHER_PID 2>/dev/null || true
        echo "✅ Anti-simulation watcher stopped"
    fi
    
    # Kill MAS Observatory services
    python3 mas-observatory-control.py stop-observatory 2>/dev/null || true
    echo "✅ MAS Observatory stopped"
    
    # Kill Portainer
    docker stop portainer 2>/dev/null || true
    echo "✅ <PERSON><PERSON><PERSON> stopped"
    
    # Kill dev server and related processes
    pkill -f "vite dev" 2>/dev/null || true
    pkill -f "concurrently" 2>/dev/null || true
    
    # Clean up ports directly without npm
    ./scripts/kill-observatory-ports.sh 2>/dev/null || true
    lsof -ti:5173 2>/dev/null | xargs kill -9 2>/dev/null || true
    
    echo "✅ All services stopped"
    exit 0
}

# Set up cleanup on script exit
trap cleanup EXIT INT TERM

echo "🧹 Step 1: Cleaning up ports and processes..."
# Kill any existing processes on port 5173
lsof -ti:5173 2>/dev/null | xargs kill -9 2>/dev/null || true
# Clean up observatory ports if script exists
if [ -f "./scripts/kill-observatory-ports.sh" ]; then
    ./scripts/kill-observatory-ports.sh 2>/dev/null || true
fi
echo "✅ Ports cleaned up"
echo ""

echo "🔍 Step 2: Starting real-time anti-simulation monitoring..."
if command -v python3 >/dev/null 2>&1 && [ -f "scripts/anti-simulation-watcher.py" ]; then
    python3 scripts/anti-simulation-watcher.py &
    WATCHER_PID=$!
    echo "✅ Real-time violation watcher started (PID: $WATCHER_PID)"
else
    echo "⚠️ Real-time monitoring not available, skipping..."
    WATCHER_PID=""
fi
echo ""

echo "🚀 Step 3: Starting MAS Observatory services..."
if [ -f "mas-observatory-control.py" ]; then
    if python3 mas-observatory-control.py start-observatory 2>/dev/null; then
        echo "✅ MAS Observatory services started"
    else
        echo "⚠️ MAS Observatory failed to start, continuing anyway..."
    fi
else
    echo "⚠️ MAS Observatory control script not found, skipping..."
fi
echo ""

echo "🐳 Step 4: Starting Portainer (Docker management)..."
if command -v docker >/dev/null 2>&1; then
    # Start Portainer directly without npm to avoid script conflicts
    if docker run -d --name portainer --restart=always -p 9000:9000 -v /var/run/docker.sock:/var/run/docker.sock -v portainer_data:/data portainer/portainer-ce 2>/dev/null; then
        echo "✅ Portainer started on port 9000"
    elif docker start portainer 2>/dev/null; then
        echo "✅ Portainer restarted on port 9000"
    else
        echo "⚠️ Portainer failed to start, continuing anyway..."
    fi
else
    echo "⚠️ Docker not found, skipping Portainer..."
fi
echo ""

echo "🌐 Step 5: Starting development server with network access..."
echo "📍 Server URLs:"
echo "   🏠 Local:    http://localhost:5173"
echo "   🌐 Network:  http://$(hostname -I | awk '{print $1}'):5173"
echo "   🐳 Portainer: http://localhost:9000"
echo ""
echo "🛡️ Protection Status:"
echo "   ⚠️ Anti-simulation validation: SKIPPED (testing mode)"
echo "   ✅ Real-time monitoring: ACTIVE"  
echo "   ✅ Pre-commit hooks: ACTIVE"
echo "   ✅ All services: RUNNING"
echo ""
echo "⚠️ Any code violations will be detected by real-time monitoring!"
echo "🛑 Press Ctrl+C to stop all services"
echo ""

# Start the development server with host access and all features
echo "🚀 Starting Vite development server..."
npx vite dev --host --port 5173 --strictPort
