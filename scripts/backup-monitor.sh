#!/bin/bash
# VybeCoding.ai Backup Monitoring System
# Monitors backup health, integrity, and schedules

set -e

# Configuration
BACKUP_DIR="${BACKUP_DIR:-/backups}"
LOG_FILE="${BACKUP_DIR}/backup-monitor.log"
ALERT_THRESHOLD_HOURS="${ALERT_THRESHOLD_HOURS:-25}"  # Alert if no backup in 25 hours
STORAGE_ALERT_THRESHOLD="${STORAGE_ALERT_THRESHOLD:-80}"  # Alert if storage > 80%

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1" | tee -a "$LOG_FILE"
}

info() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] INFO:${NC} $1" | tee -a "$LOG_FILE"
}

# Check backup freshness
check_backup_freshness() {
    log "Checking backup freshness..."
    
    local latest_backup=$(find "$BACKUP_DIR" -name "db_backup_*.sql.gz*" | sort | tail -1)
    
    if [ -z "$latest_backup" ]; then
        error "No database backups found"
        return 1
    fi
    
    local backup_time=$(stat -c %Y "$latest_backup")
    local current_time=$(date +%s)
    local hours_since_backup=$(( (current_time - backup_time) / 3600 ))
    
    if [ $hours_since_backup -gt $ALERT_THRESHOLD_HOURS ]; then
        error "Last backup is $hours_since_backup hours old (threshold: $ALERT_THRESHOLD_HOURS hours)"
        return 1
    else
        log "✅ Latest backup is $hours_since_backup hours old (within threshold)"
        return 0
    fi
}

# Check backup integrity
check_backup_integrity() {
    log "Checking backup integrity..."
    
    local integrity_issues=0
    
    # Check database backups
    while IFS= read -r -d '' backup_file; do
        local filename=$(basename "$backup_file")
        
        if [[ "$backup_file" == *.enc ]]; then
            info "Skipping encrypted backup integrity check: $filename (requires decryption)"
        elif [[ "$backup_file" == *.gz ]]; then
            if gzip -t "$backup_file" 2>/dev/null; then
                info "✅ $filename integrity OK"
            else
                error "❌ $filename integrity FAILED"
                ((integrity_issues++))
            fi
        fi
    done < <(find "$BACKUP_DIR" -name "db_backup_*.sql.gz*" -print0)
    
    # Check file backups
    while IFS= read -r -d '' backup_file; do
        local filename=$(basename "$backup_file")
        
        if tar -tzf "$backup_file" > /dev/null 2>&1; then
            info "✅ $filename integrity OK"
        else
            error "❌ $filename integrity FAILED"
            ((integrity_issues++))
        fi
    done < <(find "$BACKUP_DIR" -name "*_backup_*.tar.gz" -print0)
    
    if [ $integrity_issues -eq 0 ]; then
        log "✅ All backup integrity checks passed"
        return 0
    else
        error "❌ $integrity_issues backup integrity issues found"
        return 1
    fi
}

# Check storage usage
check_storage_usage() {
    log "Checking backup storage usage..."
    
    local usage_percent=$(df "$BACKUP_DIR" | awk 'NR==2 {print int($5)}')
    local available_space=$(df -h "$BACKUP_DIR" | awk 'NR==2 {print $4}')
    
    if [ $usage_percent -gt $STORAGE_ALERT_THRESHOLD ]; then
        warning "Backup storage usage is ${usage_percent}% (threshold: ${STORAGE_ALERT_THRESHOLD}%)"
        warning "Available space: $available_space"
        return 1
    else
        log "✅ Storage usage is ${usage_percent}% (available: $available_space)"
        return 0
    fi
}

# Check backup schedule
check_backup_schedule() {
    log "Checking backup schedule configuration..."
    
    # Check if backup cron job exists
    if crontab -l 2>/dev/null | grep -q "backup-system.sh"; then
        log "✅ Backup cron job is configured"
        
        # Show current schedule
        local cron_schedule=$(crontab -l 2>/dev/null | grep "backup-system.sh" | head -1)
        info "Schedule: $cron_schedule"
        return 0
    else
        warning "No backup cron job found"
        return 1
    fi
}

# Check cloud storage connectivity
check_cloud_storage() {
    log "Checking cloud storage connectivity..."
    
    local cloud_issues=0
    
    # Check AWS S3
    if [ -n "$AWS_S3_BUCKET" ] && command -v aws &> /dev/null; then
        if aws s3 ls "s3://$AWS_S3_BUCKET/" &>/dev/null; then
            log "✅ AWS S3 connectivity OK"
        else
            error "❌ AWS S3 connectivity FAILED"
            ((cloud_issues++))
        fi
    fi
    
    # Check Google Cloud Storage
    if [ -n "$GCS_BUCKET" ] && command -v gsutil &> /dev/null; then
        if gsutil ls "gs://$GCS_BUCKET/" &>/dev/null; then
            log "✅ Google Cloud Storage connectivity OK"
        else
            error "❌ Google Cloud Storage connectivity FAILED"
            ((cloud_issues++))
        fi
    fi
    
    # Check Azure Blob Storage
    if [ -n "$AZURE_STORAGE_ACCOUNT" ] && [ -n "$AZURE_CONTAINER" ] && command -v az &> /dev/null; then
        if az storage blob list --container-name "$AZURE_CONTAINER" --account-name "$AZURE_STORAGE_ACCOUNT" &>/dev/null; then
            log "✅ Azure Blob Storage connectivity OK"
        else
            error "❌ Azure Blob Storage connectivity FAILED"
            ((cloud_issues++))
        fi
    fi
    
    if [ $cloud_issues -eq 0 ]; then
        log "✅ All configured cloud storage services are accessible"
        return 0
    else
        error "❌ $cloud_issues cloud storage connectivity issues found"
        return 1
    fi
}

# Generate backup report
generate_backup_report() {
    log "Generating backup report..."
    
    local report_file="${BACKUP_DIR}/backup-report-$(date +%Y%m%d).txt"
    
    {
        echo "VybeCoding.ai Backup Report"
        echo "Generated: $(date)"
        echo "========================================"
        echo ""
        
        echo "BACKUP INVENTORY:"
        echo "----------------"
        find "$BACKUP_DIR" -name "*_backup_*.gz*" -o -name "*_backup_*.tar.gz" | sort | while read -r backup; do
            local filename=$(basename "$backup")
            local size=$(du -h "$backup" | cut -f1)
            local date=$(stat -c %y "$backup" | cut -d' ' -f1)
            echo "  $filename ($size, $date)"
        done
        echo ""
        
        echo "STORAGE USAGE:"
        echo "-------------"
        df -h "$BACKUP_DIR"
        echo ""
        
        echo "LATEST BACKUPS:"
        echo "--------------"
        echo "Database: $(find "$BACKUP_DIR" -name "db_backup_*.sql.gz*" | sort | tail -1 | xargs basename 2>/dev/null || echo 'None')"
        echo "Uploads: $(find "$BACKUP_DIR" -name "uploads_backup_*.tar.gz" | sort | tail -1 | xargs basename 2>/dev/null || echo 'None')"
        echo "Config: $(find "$BACKUP_DIR" -name "config_backup_*.tar.gz" | sort | tail -1 | xargs basename 2>/dev/null || echo 'None')"
        echo "Logs: $(find "$BACKUP_DIR" -name "logs_backup_*.tar.gz" | sort | tail -1 | xargs basename 2>/dev/null || echo 'None')"
        echo ""
        
        echo "BACKUP COUNT BY TYPE:"
        echo "--------------------"
        echo "Database backups: $(find "$BACKUP_DIR" -name "db_backup_*.sql.gz*" | wc -l)"
        echo "Upload backups: $(find "$BACKUP_DIR" -name "uploads_backup_*.tar.gz" | wc -l)"
        echo "Config backups: $(find "$BACKUP_DIR" -name "config_backup_*.tar.gz" | wc -l)"
        echo "Log backups: $(find "$BACKUP_DIR" -name "logs_backup_*.tar.gz" | wc -l)"
        echo ""
        
        echo "RETENTION ANALYSIS:"
        echo "------------------"
        local retention_days="${RETENTION_DAYS:-30}"
        local old_backups=$(find "$BACKUP_DIR" -name "*.gz*" -mtime +$retention_days | wc -l)
        echo "Backups older than $retention_days days: $old_backups"
        
    } > "$report_file"
    
    log "Backup report generated: $report_file"
    echo "$report_file"
}

# Send monitoring alert
send_monitoring_alert() {
    local severity="$1"
    local message="$2"
    
    local emoji="🔍"
    case "$severity" in
        "critical") emoji="🚨" ;;
        "warning") emoji="⚠️" ;;
        "info") emoji="ℹ️" ;;
    esac
    
    # Slack notification
    if [ -n "$SLACK_WEBHOOK_URL" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"$emoji VybeCoding.ai Backup Monitor [$severity]: $message\"}" \
            "$SLACK_WEBHOOK_URL" &>/dev/null || true
    fi
    
    # Discord notification
    if [ -n "$DISCORD_WEBHOOK_URL" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"content\":\"$emoji VybeCoding.ai Backup Monitor [$severity]: $message\"}" \
            "$DISCORD_WEBHOOK_URL" &>/dev/null || true
    fi
    
    # Email notification
    if command -v sendmail &> /dev/null && [ -n "$NOTIFICATION_EMAIL" ]; then
        echo -e "Subject: VybeCoding.ai Backup Monitor Alert [$severity]\n\n$message" | sendmail "$NOTIFICATION_EMAIL" || true
    fi
}

# Main monitoring function
main() {
    log "Starting backup monitoring check..."
    
    local issues=0
    local warnings=0
    
    # Create backup directory if it doesn't exist
    mkdir -p "$BACKUP_DIR"
    
    # Run all checks
    if ! check_backup_freshness; then
        ((issues++))
        send_monitoring_alert "critical" "Backup freshness check failed - backups are too old"
    fi
    
    if ! check_backup_integrity; then
        ((issues++))
        send_monitoring_alert "critical" "Backup integrity check failed - corrupted backups detected"
    fi
    
    if ! check_storage_usage; then
        ((warnings++))
        send_monitoring_alert "warning" "Backup storage usage is high"
    fi
    
    if ! check_backup_schedule; then
        ((warnings++))
        send_monitoring_alert "warning" "Backup schedule configuration issue"
    fi
    
    if ! check_cloud_storage; then
        ((warnings++))
        send_monitoring_alert "warning" "Cloud storage connectivity issues"
    fi
    
    # Generate report
    local report_file=$(generate_backup_report)
    
    # Summary
    if [ $issues -eq 0 ] && [ $warnings -eq 0 ]; then
        log "✅ All backup monitoring checks passed"
        send_monitoring_alert "info" "All backup systems are healthy"
    else
        if [ $issues -gt 0 ]; then
            error "❌ $issues critical issues found"
        fi
        if [ $warnings -gt 0 ]; then
            warning "⚠️ $warnings warnings found"
        fi
    fi
    
    log "Backup monitoring check completed"
    log "Report available at: $report_file"
}

# Handle script arguments
case "${1:-}" in
    --help|-h)
        echo "VybeCoding.ai Backup Monitoring System"
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h          Show this help message"
        echo "  --report            Generate backup report only"
        echo "  --freshness         Check backup freshness only"
        echo "  --integrity         Check backup integrity only"
        echo "  --storage           Check storage usage only"
        echo "  --schedule          Check backup schedule only"
        echo "  --cloud             Check cloud storage connectivity only"
        echo ""
        echo "Environment Variables:"
        echo "  BACKUP_DIR                  Backup directory (default: /backups)"
        echo "  ALERT_THRESHOLD_HOURS       Hours before backup freshness alert (default: 25)"
        echo "  STORAGE_ALERT_THRESHOLD     Storage usage percentage alert threshold (default: 80)"
        echo "  SLACK_WEBHOOK_URL           Slack webhook for notifications"
        echo "  DISCORD_WEBHOOK_URL         Discord webhook for notifications"
        echo "  NOTIFICATION_EMAIL          Email for notifications"
        exit 0
        ;;
    --report)
        generate_backup_report
        exit 0
        ;;
    --freshness)
        check_backup_freshness
        exit $?
        ;;
    --integrity)
        check_backup_integrity
        exit $?
        ;;
    --storage)
        check_storage_usage
        exit $?
        ;;
    --schedule)
        check_backup_schedule
        exit $?
        ;;
    --cloud)
        check_cloud_storage
        exit $?
        ;;
    *)
        main "$@"
        ;;
esac
