#!/bin/bash

# Start MAS Services for VybeCoding.ai
# Starts all advanced protocol services automatically

echo "🚀 Starting MAS Advanced Protocol Services..."

# Function to check if port is in use
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo "Port $port is already in use"
        return 0
    else
        return 1
    fi
}

# Function to start service if not running
start_service() {
    local service_name=$1
    local port=$2
    local script_path=$3
    
    if check_port $port; then
        echo "✅ $service_name already running on port $port"
    else
        echo "🔧 Starting $service_name on port $port..."
        nohup python3 "$script_path" > "logs/${service_name,,}.log" 2>&1 &
        echo $! > "pids/${service_name,,}.pid"
        sleep 2
        
        if check_port $port; then
            echo "✅ $service_name started successfully"
        else
            echo "❌ Failed to start $service_name"
        fi
    fi
}

# Create directories for logs and PIDs
mkdir -p logs pids

# Start Advanced Protocol Services
echo "🔧 Starting Advanced Protocol Services..."

# Start unified advanced protocols service
if check_port 3002; then
    echo "✅ Advanced Protocol Services already running"
else
    echo "🔧 Starting Advanced Protocol Services..."
    nohup python3 scripts/start-advanced-protocols.py > logs/advanced-protocols.log 2>&1 &
    echo $! > pids/advanced-protocols.pid
    
    # Wait for services to start
    echo "⏳ Waiting for services to initialize..."
    sleep 5
    
    # Check each service
    services=("MCP:3002" "A2A:3003" "Agentic_Retrieval:3004" "Guardrails:3005")
    all_started=true
    
    for service in "${services[@]}"; do
        IFS=':' read -r name port <<< "$service"
        if check_port $port; then
            echo "✅ $name service running on port $port"
        else
            echo "❌ $name service failed to start on port $port"
            all_started=false
        fi
    done
    
    if $all_started; then
        echo "🎉 All Advanced Protocol Services started successfully!"
    else
        echo "⚠️  Some services failed to start. Check logs in logs/ directory."
    fi
fi

# Start MAS Observatory Control (if not already running)
if ! pgrep -f "mas-observatory-control.py" > /dev/null; then
    echo "🔭 Starting MAS Observatory Control..."
    nohup python3 scripts/mas-observatory-control.py > logs/mas-observatory.log 2>&1 &
    echo $! > pids/mas-observatory.pid
    echo "✅ MAS Observatory Control started"
else
    echo "✅ MAS Observatory Control already running"
fi

echo ""
echo "🎯 MAS Services Status Summary:"
echo "================================"
echo "✅ MCP Server:          http://localhost:3002"
echo "✅ A2A Protocol:        http://localhost:3003"  
echo "✅ Agentic Retrieval:   http://localhost:3004"
echo "✅ Guardrails Service:  http://localhost:3005"
echo "✅ MAS Observatory:     Integrated into dev server"
echo ""
echo "📊 Monitoring Services:"
echo "✅ Grafana:             http://localhost:3001"
echo "✅ Prometheus:          http://localhost:9091"
echo "✅ Netdata:             http://localhost:19999"
echo "✅ Kibana:              http://localhost:5601"
echo "✅ Jaeger:              http://localhost:16686"
echo "✅ Portainer:           http://localhost:9000"
echo ""
echo "🤖 LLM Services:"
echo "✅ Ollama API:          http://localhost:11434"
echo ""
echo "🎉 All MAS services are ready for 24/7 autonomous operation!"
echo "📁 Logs available in: logs/"
echo "🔧 PIDs stored in: pids/"
