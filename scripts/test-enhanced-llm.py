#!/usr/bin/env python3
"""
Test Enhanced LLM Integration with MCP, Tools, and Web Search
Verifies the complete autonomous capabilities
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime

class EnhancedLLMTest:
    def __init__(self):
        self.base_url = "http://localhost:5173"
        self.test_results = []
        
    async def test_web_search_integration(self, session):
        """Test LLM with web search capabilities"""
        print("🌐 Testing LLM with Web Search Integration...")
        
        test_cases = [
            {
                "name": "Current AI News",
                "payload": {
                    "input": "What are the latest developments in AI technology in 2025?",
                    "type": "text",
                    "outputTypes": ["news"],
                    "options": {
                        "autonomousMode": True,
                        "enableWebSearch": True,
                        "detectedType": {
                            "type": "news",
                            "confidence": 95,
                            "keywords": ["AI", "technology", "2025", "developments"]
                        }
                    }
                }
            },
            {
                "name": "Technical Tutorial with Current Info",
                "payload": {
                    "input": "Create a course on the latest SvelteKit features and best practices",
                    "type": "text", 
                    "outputTypes": ["course"],
                    "options": {
                        "autonomousMode": True,
                        "enableWebSearch": True,
                        "detectedType": {
                            "type": "course",
                            "confidence": 90,
                            "keywords": ["SvelteKit", "features", "best practices"]
                        }
                    }
                }
            }
        ]
        
        for test_case in test_cases:
            try:
                print(f"  Testing {test_case['name']}...")
                start_time = time.time()
                
                async with session.post(f"{self.base_url}/api/vybe/process-content", 
                                      json=test_case['payload'],
                                      timeout=90) as response:
                    if response.status == 200:
                        data = await response.json()
                        duration = time.time() - start_time
                        task_id = data.get('taskId', 'unknown')
                        print(f"    ✅ {test_case['name']}: Task {task_id} created ({duration:.1f}s)")
                        
                        # Wait and check task status
                        await asyncio.sleep(3)
                        await self.check_enhanced_task_status(session, task_id, test_case['name'])
                        
                    else:
                        error_text = await response.text()
                        print(f"    ❌ {test_case['name']}: HTTP {response.status} - {error_text[:100]}")
                        
            except Exception as e:
                print(f"    ❌ {test_case['name']}: {str(e)}")
    
    async def test_mcp_capabilities(self, session):
        """Test MCP (Model Context Protocol) integration"""
        print("\n🔧 Testing MCP Tool Integration...")
        
        # Check if MCP server is available
        try:
            async with session.get("http://localhost:3002/health", timeout=5) as response:
                if response.status == 200:
                    print("    ✅ MCP Server: Available")
                    self.test_results.append({
                        "test": "mcp_server_health",
                        "status": "success"
                    })
                else:
                    print("    ❌ MCP Server: Not responding")
                    self.test_results.append({
                        "test": "mcp_server_health", 
                        "status": "failed",
                        "error": f"HTTP {response.status}"
                    })
        except Exception as e:
            print(f"    ⚠️  MCP Server: Not available ({str(e)})")
            print("    ℹ️  Continuing without MCP capabilities")
            self.test_results.append({
                "test": "mcp_server_health",
                "status": "unavailable",
                "note": "MCP server not running"
            })
    
    async def test_direct_web_search(self, session):
        """Test direct web search functionality"""
        print("\n🔍 Testing Direct Web Search...")
        
        try:
            # Test DuckDuckGo API directly
            search_query = "SvelteKit 2025 features"
            async with session.get(f"https://api.duckduckgo.com/?q={search_query}&format=json&no_html=1&skip_disambig=1", 
                                 timeout=10) as response:
                if response.status == 200:
                    data = await response.json()
                    has_results = bool(data.get('Abstract') or data.get('RelatedTopics'))
                    print(f"    ✅ DuckDuckGo API: {'Results found' if has_results else 'No results'}")
                    self.test_results.append({
                        "test": "duckduckgo_search",
                        "status": "success",
                        "has_results": has_results
                    })
                else:
                    print(f"    ❌ DuckDuckGo API: HTTP {response.status}")
                    self.test_results.append({
                        "test": "duckduckgo_search",
                        "status": "failed",
                        "error": f"HTTP {response.status}"
                    })
        except Exception as e:
            print(f"    ❌ DuckDuckGo API: {str(e)}")
            self.test_results.append({
                "test": "duckduckgo_search",
                "status": "error",
                "error": str(e)
            })
    
    async def check_enhanced_task_status(self, session, task_id, test_name):
        """Check task status and look for enhanced capabilities"""
        try:
            async with session.get(f"{self.base_url}/api/vybe/task-status/{task_id}") as response:
                if response.status == 200:
                    data = await response.json()
                    status = data.get('status', 'unknown')
                    progress = data.get('progress', 0)
                    
                    # Check for enhanced features in result
                    result = data.get('result', {})
                    has_web_search = 'webSearchResults' in result
                    has_tools = 'toolCalls' in result
                    
                    print(f"      📊 Task Status: {status} ({progress}%)")
                    if has_web_search:
                        print(f"      🌐 Web search results included")
                    if has_tools:
                        print(f"      🔧 Tool calls detected")
                    
                    self.test_results.append({
                        "test": f"{test_name}_enhanced_status",
                        "status": "success",
                        "task_status": status,
                        "progress": progress,
                        "has_web_search": has_web_search,
                        "has_tools": has_tools,
                        "task_id": task_id
                    })
                else:
                    print(f"      ❌ Failed to get task status: HTTP {response.status}")
                    
        except Exception as e:
            print(f"      ❌ Task status error: {str(e)}")
    
    async def test_llm_model_availability(self, session):
        """Test if the confirmed LLM models are available"""
        print("\n🤖 Testing LLM Model Availability...")
        
        models_to_test = [
            "qwen3:30b-a3b",
            "devstral:24b"
        ]
        
        try:
            async with session.get("http://localhost:11434/api/tags", timeout=10) as response:
                if response.status == 200:
                    data = await response.json()
                    available_models = [model['name'] for model in data.get('models', [])]
                    
                    for model in models_to_test:
                        if model in available_models:
                            print(f"    ✅ {model}: Available")
                            self.test_results.append({
                                "test": f"model_{model.replace(':', '_')}",
                                "status": "available"
                            })
                        else:
                            print(f"    ❌ {model}: Not found")
                            self.test_results.append({
                                "test": f"model_{model.replace(':', '_')}",
                                "status": "missing"
                            })
                else:
                    print(f"    ❌ Ollama API: HTTP {response.status}")
                    
        except Exception as e:
            print(f"    ❌ Ollama API: {str(e)}")
    
    async def run_enhanced_test_suite(self):
        """Run complete enhanced LLM test suite"""
        print("🚀 Enhanced LLM Integration Test Suite")
        print("=" * 60)
        print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        async with aiohttp.ClientSession() as session:
            # Test LLM model availability
            await self.test_llm_model_availability(session)
            
            # Test direct web search
            await self.test_direct_web_search(session)
            
            # Test MCP capabilities
            await self.test_mcp_capabilities(session)
            
            # Test web search integration
            await self.test_web_search_integration(session)
        
        # Generate comprehensive report
        self.generate_enhanced_report()
    
    def generate_enhanced_report(self):
        """Generate enhanced test report"""
        print("\n" + "=" * 60)
        print("📊 ENHANCED LLM CAPABILITIES TEST RESULTS")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        successful_tests = len([r for r in self.test_results if r['status'] in ['success', 'available']])
        failed_tests = len([r for r in self.test_results if r['status'] in ['failed', 'error', 'missing']])
        unavailable_tests = len([r for r in self.test_results if r['status'] == 'unavailable'])
        
        print(f"Total Tests: {total_tests}")
        print(f"✅ Successful: {successful_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"⚠️  Unavailable: {unavailable_tests}")
        print(f"Success Rate: {(successful_tests/total_tests*100):.1f}%")
        
        print("\nCapability Summary:")
        print("🤖 LLM Models:", "✅" if any(r['status'] == 'available' for r in self.test_results if 'model_' in r['test']) else "❌")
        print("🌐 Web Search:", "✅" if any(r['test'] == 'duckduckgo_search' and r['status'] == 'success' for r in self.test_results) else "❌")
        print("🔧 MCP Server:", "✅" if any(r['test'] == 'mcp_server_health' and r['status'] == 'success' for r in self.test_results) else "⚠️")
        
        # Save results
        with open('enhanced-llm-test-results.json', 'w') as f:
            json.dump({
                "timestamp": datetime.now().isoformat(),
                "summary": {
                    "total_tests": total_tests,
                    "successful": successful_tests,
                    "failed": failed_tests,
                    "unavailable": unavailable_tests,
                    "success_rate": successful_tests/total_tests*100
                },
                "capabilities": {
                    "llm_models": any(r['status'] == 'available' for r in self.test_results if 'model_' in r['test']),
                    "web_search": any(r['test'] == 'duckduckgo_search' and r['status'] == 'success' for r in self.test_results),
                    "mcp_server": any(r['test'] == 'mcp_server_health' and r['status'] == 'success' for r in self.test_results)
                },
                "detailed_results": self.test_results
            }, f, indent=2)
        
        print(f"\n📁 Detailed results saved to: enhanced-llm-test-results.json")
        
        if successful_tests >= total_tests * 0.8:  # 80% success rate
            print("\n🎉 ENHANCED LLM CAPABILITIES READY!")
            print("✅ 24/7 Autonomous Mode with Web Search and Tools is OPERATIONAL!")
        else:
            print(f"\n⚠️  Some capabilities need attention. Review errors above.")

async def main():
    tester = EnhancedLLMTest()
    await tester.run_enhanced_test_suite()

if __name__ == "__main__":
    asyncio.run(main())
