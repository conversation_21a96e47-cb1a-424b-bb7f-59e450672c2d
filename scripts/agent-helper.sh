#!/bin/bash

# Agent Helper Script for VybeCoding.ai
# Autonomous milestone management for AI agents

set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_agent() {
    echo -e "${BLUE}[AGENT-HELPER]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to detect current agent context
detect_agent_context() {
    local context=""
    
    # Check if we're in a story implementation
    if [ -d "story-drafts" ] && [ -n "$(ls story-drafts/*.md 2>/dev/null | head -1)" ]; then
        local current_story=$(ls story-drafts/*.md | grep -E "STORY-[0-9]+-[0-9]+" | head -1)
        if [ -n "$current_story" ]; then
            context="story:$(basename "$current_story" .md)"
        fi
    fi
    
    # Check if we're in src directory (development work)
    if [ -d "src" ] && [ -f "src/package.json" ]; then
        context="${context:+$context,}development"
    fi
    
    # Check for recent commits to determine work type
    local recent_commit=$(git log -1 --pretty=format:"%s" 2>/dev/null || echo "")
    if [[ "$recent_commit" =~ STORY-[0-9]+-[0-9]+ ]]; then
        local story_id=$(echo "$recent_commit" | grep -oE "STORY-[0-9]+-[0-9]+")
        context="${context:+$context,}story:$story_id"
    fi
    
    echo "$context"
}

# Function to suggest milestone type and description
suggest_milestone() {
    local context="$1"
    local type="feature"
    local description="Development checkpoint"
    
    if [[ "$context" =~ story:STORY-([0-9]+)-([0-9]+) ]]; then
        type="story"
        local story_match="${BASH_REMATCH[0]}"
        description="$story_match implementation complete"
    elif [[ "$context" =~ development ]]; then
        # Check recent commits for clues
        local recent_commits=$(git log -5 --pretty=format:"%s" 2>/dev/null || echo "")
        
        if [[ "$recent_commits" =~ [Ff]ix|[Bb]ug ]]; then
            type="bugfix"
            description="Bug fixes and improvements"
        elif [[ "$recent_commits" =~ [Tt]est ]]; then
            type="feature"
            description="Testing infrastructure updates"
        elif [[ "$recent_commits" =~ [Dd]oc ]]; then
            type="feature"
            description="Documentation updates"
        else
            type="feature"
            description="Feature development checkpoint"
        fi
    fi
    
    echo "$type:$description"
}

# Function to auto-create milestone
auto_milestone() {
    local force="$1"
    
    print_agent "Analyzing current work context..."
    
    # Check if we're in a git repository
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        print_error "Not in a git repository"
        return 1
    fi
    
    # Check for uncommitted changes
    if [ -n "$(git status --porcelain)" ]; then
        print_warning "Uncommitted changes detected"
        if [ "$force" != "force" ]; then
            print_agent "Committing changes before milestone creation..."
            git add .
            git commit -m "Auto-commit: Preparing for milestone creation"
        fi
    fi
    
    # Detect context and suggest milestone
    local context=$(detect_agent_context)
    local suggestion=$(suggest_milestone "$context")
    local type=$(echo "$suggestion" | cut -d: -f1)
    local description=$(echo "$suggestion" | cut -d: -f2-)
    
    print_agent "Detected context: $context"
    print_agent "Suggested milestone: $type - $description"
    
    # Create milestone
    ./scripts/auto-milestone.sh create "$type" "$description" force
    
    if [ $? -eq 0 ]; then
        print_success "Autonomous milestone created successfully!"
        return 0
    else
        print_error "Failed to create milestone"
        return 1
    fi
}

# Function to check if milestone is needed
check_milestone_needed() {
    # Check time since last milestone
    local last_milestone_time=$(git log --grep="Milestone" --pretty=format:"%ct" -1 2>/dev/null || echo "0")
    local current_time=$(date +%s)
    local time_diff=$((current_time - last_milestone_time))
    local hours_since=$((time_diff / 3600))
    
    # Check number of commits since last milestone
    local commits_since=$(git rev-list --count HEAD --since="@$last_milestone_time" 2>/dev/null || echo "0")
    
    # Check if significant work has been done
    local files_changed=$(git diff --name-only HEAD~5..HEAD 2>/dev/null | wc -l || echo "0")
    
    print_agent "Time since last milestone: ${hours_since} hours"
    print_agent "Commits since last milestone: $commits_since"
    print_agent "Files changed recently: $files_changed"
    
    # Suggest milestone if:
    # - More than 2 hours of work
    # - More than 5 commits
    # - More than 10 files changed
    if [ "$hours_since" -gt 2 ] || [ "$commits_since" -gt 5 ] || [ "$files_changed" -gt 10 ]; then
        print_warning "Milestone recommended based on work volume"
        return 0
    else
        print_agent "No milestone needed at this time"
        return 1
    fi
}

# Function to show agent status
show_status() {
    print_agent "VybeCoding.ai Agent Status"
    echo "=========================="
    echo
    
    # Git status
    echo "📁 Repository Status:"
    if git rev-parse --git-dir > /dev/null 2>&1; then
        echo "  ✅ Git repository detected"
        echo "  📍 Current branch: $(git branch --show-current)"
        echo "  📝 Uncommitted changes: $(git status --porcelain | wc -l) files"
    else
        echo "  ❌ Not in a git repository"
    fi
    echo
    
    # Milestone status
    echo "🎯 Milestone Status:"
    if [ -f "./scripts/auto-milestone.sh" ]; then
        echo "  ✅ Milestone system available"
        local milestone_count=$(./scripts/auto-milestone.sh list 2>/dev/null | grep -E "^  [0-9]+" | wc -l || echo "0")
        echo "  📊 Total milestones: $milestone_count"
        
        # Show last milestone
        local last_milestone=$(./scripts/auto-milestone.sh list 2>/dev/null | grep -E "^  [0-9]+" | tail -1 || echo "")
        if [ -n "$last_milestone" ]; then
            echo "  🏁 Last milestone: $last_milestone"
        fi
    else
        echo "  ❌ Milestone system not found"
    fi
    echo
    
    # Context detection
    echo "🤖 Agent Context:"
    local context=$(detect_agent_context)
    if [ -n "$context" ]; then
        echo "  📋 Detected context: $context"
        local suggestion=$(suggest_milestone "$context")
        echo "  💡 Suggested milestone: $suggestion"
    else
        echo "  ❓ No specific context detected"
    fi
    echo
    
    # Check if milestone needed
    echo "⏰ Milestone Recommendation:"
    if check_milestone_needed; then
        echo "  🟡 Milestone creation recommended"
    else
        echo "  🟢 No milestone needed currently"
    fi
}

# Main script logic
case "${1:-status}" in
    "auto")
        auto_milestone "$2"
        ;;
    "check")
        check_milestone_needed
        ;;
    "status")
        show_status
        ;;
    "help"|*)
        echo "Agent Helper Script for VybeCoding.ai"
        echo
        echo "Usage:"
        echo "  $0 auto [force]     - Automatically create milestone based on context"
        echo "  $0 check           - Check if milestone creation is recommended"
        echo "  $0 status          - Show comprehensive agent status"
        echo
        echo "Examples:"
        echo "  $0 auto            - Auto-create milestone with context detection"
        echo "  $0 auto force      - Force auto-create even with uncommitted changes"
        echo "  $0 check           - Check if milestone is needed"
        echo "  $0 status          - Show current status and recommendations"
        echo
        echo "This script is designed for autonomous use by AI agents following"
        echo "the Vybe Method guidelines for automatic milestone management."
        ;;
esac
