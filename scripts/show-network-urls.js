#!/usr/bin/env node

const os = require('os');

// Add error handling
process.on('uncaughtException', error => {
  console.error('Script error:', error.message);
  process.exit(1);
});

function getNetworkInterfaces() {
  const interfaces = os.networkInterfaces();
  const addresses = [];

  for (const name of Object.keys(interfaces)) {
    for (const interface of interfaces[name]) {
      // Skip internal (i.e. 127.0.0.1) and non-IPv4 addresses
      if (interface.family === 'IPv4' && !interface.internal) {
        addresses.push({
          name: name,
          address: interface.address,
          netmask: interface.netmask,
        });
      }
    }
  }

  return addresses;
}

function displayNetworkUrls() {
  console.log('\n🌐 VybeCoding.ai Network Access URLs:');
  console.log('═'.repeat(50));

  const interfaces = getNetworkInterfaces();

  if (interfaces.length === 0) {
    console.log('❌ No external network interfaces found');
    console.log('📍 Local access only: http://localhost:5173');
    return;
  }

  console.log('✅ Server will be accessible from these network addresses:');
  console.log('');

  interfaces.forEach((iface, index) => {
    const url = `http://${iface.address}:5173`;
    console.log(`📱 ${iface.name.padEnd(12)} ${url}`);

    // Highlight the main network interface (usually the first one)
    if (index === 0) {
      console.log(`   ${''.padEnd(12)} ⭐ Primary network interface`);
    }
  });

  console.log('');
  console.log('📍 Local access: http://localhost:5173');
  console.log('🔗 Container IP: http://**********:5173 (if applicable)');
  console.log('');
  console.log('💡 Tips:');
  console.log('   • Make sure your firewall allows port 5173');
  console.log('   • Other devices must be on the same network');
  console.log(
    '   • Use the IP address that matches your network (192.168.x.x)'
  );
  console.log('');
  console.log('═'.repeat(50));
  console.log('🚀 Starting development server...');
  console.log('');
}

// Run the function
displayNetworkUrls();
