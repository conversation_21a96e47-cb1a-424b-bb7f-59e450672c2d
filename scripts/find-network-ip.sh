#!/bin/bash

echo "🔍 Finding your network IP addresses..."
echo "═══════════════════════════════════════"

# Try different methods to find network IP
if command -v ip &> /dev/null; then
    echo "📡 Network interfaces (using ip command):"
    ip addr show | grep -E "inet [0-9]" | grep -v "127.0.0.1" | awk '{print "   " $2}' | sed 's/\/.*$//'
elif command -v ifconfig &> /dev/null; then
    echo "📡 Network interfaces (using ifconfig):"
    ifconfig | grep -E "inet [0-9]" | grep -v "127.0.0.1" | awk '{print "   " $2}'
else
    echo "❌ Neither 'ip' nor 'ifconfig' command found"
fi

echo ""
echo "🌐 To access VybeCoding.ai from other devices:"
echo "   1. Use one of the IP addresses above"
echo "   2. Add port :5173"
echo "   3. Example: http://*************:5173"
echo ""
echo "💡 Make sure:"
echo "   • Your firewall allows port 5173"
echo "   • Other devices are on the same network"
echo "   • The development server is running with --host 0.0.0.0"
