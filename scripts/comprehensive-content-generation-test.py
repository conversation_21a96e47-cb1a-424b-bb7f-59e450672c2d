#!/usr/bin/env python3
"""
Comprehensive Content Generation Test for Enhanced MAS System
Validates production readiness and quality compliance across all content types
"""

import asyncio
import json
import logging
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any
import sys

# Add MAS system to path
sys.path.append('/home/<USER>/Projects/vybecoding/method/vybe')

from content_generation_engine import ContentGenerationEngine, OllamaLLMService, ContentRequest, ContentType
from enhanced_quality_validator import EnhancedQualityValidator

class ComprehensiveContentTest:
    """Comprehensive content generation test for Enhanced MAS validation"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.llm_service = OllamaLLMService()
        self.content_engine = ContentGenerationEngine(self.llm_service)
        self.quality_validator = EnhancedQualityValidator()
        
        # Test configuration
        self.test_results = {}
        self.performance_metrics = {}
        self.quality_scores = {}
        
        # Quality standards for audit
        self.quality_standards = {
            'content_depth': 0.98,
            'technical_sophistication': 0.96,
            'structure_quality': 0.95,
            'language_quality': 0.97,
            'vybecoding_compliance': 0.98
        }
        
        # Success criteria
        self.success_criteria = {
            'min_quality_score': 0.95,
            'max_generation_time': 30.0,
            'min_content_length': 500,
            'required_agents': ['vyba', 'qubert', 'codex', 'pixy', 'ducky', 'happy', 'vybro']
        }
    
    async def execute_comprehensive_test(self):
        """Execute comprehensive content generation test"""
        print("🚀 ENHANCED MAS COMPREHENSIVE CONTENT GENERATION TEST")
        print("="*70)
        print("Validating production readiness and quality compliance")
        print("Target: VybeCoding.ai Excellence Standards (98%+)")
        print("="*70)
        
        test_start_time = time.time()
        
        # Test 1: Course Content Generation
        print("\n📚 Test 1: Course Content Generation")
        course_result = await self.test_course_content_generation()
        
        # Test 2: News Article Generation
        print("\n📰 Test 2: News Article Generation")
        news_result = await self.test_news_article_generation()
        
        # Test 3: Technical Documentation Generation
        print("\n📖 Test 3: Technical Documentation Generation")
        docs_result = await self.test_documentation_generation()
        
        # Test 4: Vybe Qube Generation
        print("\n🎯 Test 4: Vybe Qube Generation")
        qube_result = await self.test_vybe_qube_generation()
        
        # Test 5: Quality Audit
        print("\n🔍 Test 5: Comprehensive Quality Audit")
        audit_result = await self.conduct_quality_audit()
        
        # Generate comprehensive report
        total_test_time = time.time() - test_start_time
        final_report = await self.generate_final_report(total_test_time)
        
        return final_report
    
    async def test_course_content_generation(self):
        """Test course content generation using VYBA and QUBERT"""
        print("   Agents: VYBA (Strategic Business Architect) + QUBERT (Product Innovation Director)")
        
        start_time = time.time()
        
        # Create course content request
        course_request = ContentRequest(
            content_type=ContentType.COURSE,
            topic="Advanced AI Development with Multi-Agent Systems",
            target_audience="Professional developers and AI engineers",
            complexity_level="advanced",
            additional_requirements={
                "learning_objectives": True,
                "practical_exercises": True,
                "assessment_criteria": True,
                "duration": "4 weeks",
                "skill_level": "intermediate to advanced"
            }
        )
        
        try:
            # Generate course content
            generation_id = await self.content_engine.generate_content(course_request)
            result = self.content_engine.get_generation_result(generation_id)
            
            generation_time = time.time() - start_time
            
            if result and result.content:
                # Extract course content
                course_content = result.content.get('course_content', '')
                content_length = len(str(course_content))
                
                # Quality validation
                quality_result = self.quality_validator.validate_content(str(course_content))
                
                # Performance metrics
                self.performance_metrics['course'] = {
                    'generation_time': generation_time,
                    'content_length': content_length,
                    'agents_used': list(result.agent_contributions.keys()) if result.agent_contributions else [],
                    'success': True
                }
                
                self.quality_scores['course'] = quality_result
                
                print(f"   ✅ Generated: {content_length} characters in {generation_time:.2f}s")
                print(f"   ✅ Quality Score: {quality_result['overall_score']:.3f}")
                print(f"   ✅ VybeCoding Compliance: {quality_result['vybecoding_compliance']}")
                
                # Save course content
                await self.save_content('course_content.md', course_content)
                
                return {
                    'success': True,
                    'content': course_content,
                    'quality_score': quality_result['overall_score'],
                    'generation_time': generation_time
                }
            else:
                print("   ❌ Course content generation failed")
                return {'success': False, 'error': 'No content generated'}
                
        except Exception as e:
            print(f"   ❌ Course content generation error: {e}")
            return {'success': False, 'error': str(e)}
    
    async def test_news_article_generation(self):
        """Test news article generation using PIXY and DUCKY"""
        print("   Agents: PIXY (Design Systems Visionary) + DUCKY (Quality Assurance Perfectionist)")
        
        start_time = time.time()
        
        # Create news article request
        news_request = ContentRequest(
            content_type=ContentType.NEWS_ARTICLE,
            topic="Breakthrough in AI Agent Coordination: New Multi-Agent Systems Achieve 99% Efficiency",
            target_audience="Tech industry professionals and AI researchers",
            complexity_level="intermediate",
            additional_requirements={
                "citations": True,
                "professional_formatting": True,
                "word_count": 800,
                "include_quotes": True,
                "seo_optimized": True
            }
        )
        
        try:
            # Generate news article
            generation_id = await self.content_engine.generate_content(news_request)
            result = self.content_engine.get_generation_result(generation_id)
            
            generation_time = time.time() - start_time
            
            if result and result.content:
                # Extract article content
                article_content = result.content.get('article_content', '')
                content_length = len(str(article_content))
                
                # Quality validation
                quality_result = self.quality_validator.validate_content(str(article_content))
                
                # Performance metrics
                self.performance_metrics['news'] = {
                    'generation_time': generation_time,
                    'content_length': content_length,
                    'agents_used': list(result.agent_contributions.keys()) if result.agent_contributions else [],
                    'success': True
                }
                
                self.quality_scores['news'] = quality_result
                
                print(f"   ✅ Generated: {content_length} characters in {generation_time:.2f}s")
                print(f"   ✅ Quality Score: {quality_result['overall_score']:.3f}")
                print(f"   ✅ VybeCoding Compliance: {quality_result['vybecoding_compliance']}")
                
                # Save news article
                await self.save_content('news_article.md', article_content)
                
                return {
                    'success': True,
                    'content': article_content,
                    'quality_score': quality_result['overall_score'],
                    'generation_time': generation_time
                }
            else:
                print("   ❌ News article generation failed")
                return {'success': False, 'error': 'No content generated'}
                
        except Exception as e:
            print(f"   ❌ News article generation error: {e}")
            return {'success': False, 'error': str(e)}
    
    async def test_documentation_generation(self):
        """Test documentation generation using CODEX and VYBRO"""
        print("   Agents: CODEX (Technical Architecture Genius) + VYBRO (Implementation Specialist)")
        
        start_time = time.time()
        
        # Create documentation request
        docs_request = ContentRequest(
            content_type=ContentType.DOCUMENTATION,
            topic="Enhanced Multi-Agent System API Reference",
            target_audience="Software developers and system integrators",
            complexity_level="advanced",
            additional_requirements={
                "code_examples": True,
                "api_references": True,
                "implementation_guides": True,
                "troubleshooting": True,
                "best_practices": True
            }
        )
        
        try:
            # Generate documentation
            generation_id = await self.content_engine.generate_content(docs_request)
            result = self.content_engine.get_generation_result(generation_id)
            
            generation_time = time.time() - start_time
            
            if result and result.content:
                # Extract documentation content
                docs_content = result.content.get('documentation', '')
                content_length = len(str(docs_content))
                
                # Quality validation
                quality_result = self.quality_validator.validate_content(str(docs_content))
                
                # Performance metrics
                self.performance_metrics['documentation'] = {
                    'generation_time': generation_time,
                    'content_length': content_length,
                    'agents_used': list(result.agent_contributions.keys()) if result.agent_contributions else [],
                    'success': True
                }
                
                self.quality_scores['documentation'] = quality_result
                
                print(f"   ✅ Generated: {content_length} characters in {generation_time:.2f}s")
                print(f"   ✅ Quality Score: {quality_result['overall_score']:.3f}")
                print(f"   ✅ VybeCoding Compliance: {quality_result['vybecoding_compliance']}")
                
                # Save documentation
                await self.save_content('technical_documentation.md', docs_content)
                
                return {
                    'success': True,
                    'content': docs_content,
                    'quality_score': quality_result['overall_score'],
                    'generation_time': generation_time
                }
            else:
                print("   ❌ Documentation generation failed")
                return {'success': False, 'error': 'No content generated'}
                
        except Exception as e:
            print(f"   ❌ Documentation generation error: {e}")
            return {'success': False, 'error': str(e)}
    
    async def test_vybe_qube_generation(self):
        """Test Vybe Qube generation using HAPPY (Integration Orchestrator)"""
        print("   Agent: HAPPY (Integration Orchestrator) coordinating all agents")
        
        start_time = time.time()
        
        # Create Vybe Qube request
        qube_request = ContentRequest(
            content_type=ContentType.VYBE_QUBE,
            topic="AI-Powered Project Management Dashboard",
            target_audience="Project managers and development teams",
            complexity_level="intermediate",
            additional_requirements={
                "configuration_files": True,
                "deployment_scripts": True,
                "documentation": True,
                "testing_suite": True,
                "monitoring_setup": True
            }
        )
        
        try:
            # Generate Vybe Qube
            generation_id = await self.content_engine.generate_content(qube_request)
            result = self.content_engine.get_generation_result(generation_id)
            
            generation_time = time.time() - start_time
            
            if result and result.content:
                # Extract Vybe Qube content
                qube_content = result.content.get('vybe_qube', '')
                content_length = len(str(qube_content))
                
                # Quality validation
                quality_result = self.quality_validator.validate_content(str(qube_content))
                
                # Performance metrics
                self.performance_metrics['vybe_qube'] = {
                    'generation_time': generation_time,
                    'content_length': content_length,
                    'agents_used': list(result.agent_contributions.keys()) if result.agent_contributions else [],
                    'success': True
                }
                
                self.quality_scores['vybe_qube'] = quality_result
                
                print(f"   ✅ Generated: {content_length} characters in {generation_time:.2f}s")
                print(f"   ✅ Quality Score: {quality_result['overall_score']:.3f}")
                print(f"   ✅ VybeCoding Compliance: {quality_result['vybecoding_compliance']}")
                
                # Save Vybe Qube
                await self.save_content('vybe_qube_package.md', qube_content)
                
                return {
                    'success': True,
                    'content': qube_content,
                    'quality_score': quality_result['overall_score'],
                    'generation_time': generation_time
                }
            else:
                print("   ❌ Vybe Qube generation failed")
                return {'success': False, 'error': 'No content generated'}
                
        except Exception as e:
            print(f"   ❌ Vybe Qube generation error: {e}")
            return {'success': False, 'error': str(e)}
    
    async def conduct_quality_audit(self):
        """Conduct comprehensive quality audit using DUCKY"""
        print("   Agent: DUCKY (Quality Assurance Perfectionist) conducting comprehensive audit")
        
        audit_results = {
            'overall_assessment': {},
            'individual_assessments': {},
            'recommendations': [],
            'compliance_status': {}
        }
        
        # Analyze all generated content
        total_scores = []
        content_types = ['course', 'news', 'documentation', 'vybe_qube']
        
        for content_type in content_types:
            if content_type in self.quality_scores:
                quality_data = self.quality_scores[content_type]
                performance_data = self.performance_metrics.get(content_type, {})
                
                # Individual assessment
                assessment = {
                    'quality_score': quality_data['overall_score'],
                    'individual_scores': quality_data['individual_scores'],
                    'meets_standard': quality_data['vybecoding_compliance'],
                    'generation_time': performance_data.get('generation_time', 0),
                    'content_length': performance_data.get('content_length', 0),
                    'agents_used': performance_data.get('agents_used', [])
                }
                
                audit_results['individual_assessments'][content_type] = assessment
                total_scores.append(quality_data['overall_score'])
                
                # Check against success criteria
                meets_quality = quality_data['overall_score'] >= self.success_criteria['min_quality_score']
                meets_time = performance_data.get('generation_time', 999) <= self.success_criteria['max_generation_time']
                meets_length = performance_data.get('content_length', 0) >= self.success_criteria['min_content_length']
                
                compliance_status = {
                    'quality_compliance': meets_quality,
                    'performance_compliance': meets_time,
                    'content_compliance': meets_length,
                    'overall_compliance': meets_quality and meets_time and meets_length
                }
                
                audit_results['compliance_status'][content_type] = compliance_status
                
                print(f"   📊 {content_type.title()}: {quality_data['overall_score']:.3f} quality, "
                      f"{performance_data.get('generation_time', 0):.1f}s generation")
        
        # Overall assessment
        if total_scores:
            overall_quality = sum(total_scores) / len(total_scores)
            overall_compliance = overall_quality >= self.quality_standards['vybecoding_compliance']
            
            audit_results['overall_assessment'] = {
                'average_quality_score': overall_quality,
                'vybecoding_compliance': overall_compliance,
                'content_types_tested': len(total_scores),
                'successful_generations': len([s for s in total_scores if s >= 0.95])
            }
            
            print(f"   🎯 Overall Quality Score: {overall_quality:.3f}")
            print(f"   🎯 VybeCoding Compliance: {overall_compliance}")
        
        return audit_results
    
    async def save_content(self, filename: str, content: Any):
        """Save generated content to file"""
        try:
            output_dir = Path('/home/<USER>/Projects/vybecoding/logs/content_generation_test')
            output_dir.mkdir(exist_ok=True)
            
            output_file = output_dir / filename
            
            if isinstance(content, dict):
                content_str = json.dumps(content, indent=2)
            else:
                content_str = str(content)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(content_str)
                
        except Exception as e:
            self.logger.error(f"Failed to save content {filename}: {e}")
    
    async def generate_final_report(self, total_test_time: float):
        """Generate comprehensive final report"""
        print("\n" + "="*70)
        print("📊 COMPREHENSIVE CONTENT GENERATION TEST RESULTS")
        print("="*70)
        
        # Calculate overall metrics
        successful_tests = len([m for m in self.performance_metrics.values() if m.get('success', False)])
        total_tests = len(self.performance_metrics)
        success_rate = (successful_tests / total_tests * 100) if total_tests > 0 else 0
        
        # Calculate average quality
        quality_scores = [q['overall_score'] for q in self.quality_scores.values()]
        avg_quality = sum(quality_scores) / len(quality_scores) if quality_scores else 0
        
        # Calculate average generation time
        generation_times = [m['generation_time'] for m in self.performance_metrics.values()]
        avg_generation_time = sum(generation_times) / len(generation_times) if generation_times else 0
        
        # Check success criteria
        meets_quality_criteria = avg_quality >= self.success_criteria['min_quality_score']
        meets_time_criteria = avg_generation_time <= self.success_criteria['max_generation_time']
        
        final_report = {
            'test_summary': {
                'total_test_time': total_test_time,
                'tests_executed': total_tests,
                'successful_tests': successful_tests,
                'success_rate': success_rate,
                'average_quality_score': avg_quality,
                'average_generation_time': avg_generation_time
            },
            'success_criteria_compliance': {
                'quality_compliance': meets_quality_criteria,
                'performance_compliance': meets_time_criteria,
                'overall_compliance': meets_quality_criteria and meets_time_criteria
            },
            'performance_metrics': self.performance_metrics,
            'quality_scores': self.quality_scores,
            'vybecoding_readiness': meets_quality_criteria and meets_time_criteria and success_rate >= 80
        }
        
        # Display results
        print(f"✅ Tests Executed: {total_tests}")
        print(f"✅ Successful Tests: {successful_tests}/{total_tests} ({success_rate:.1f}%)")
        print(f"✅ Average Quality Score: {avg_quality:.3f}")
        print(f"✅ Average Generation Time: {avg_generation_time:.2f}s")
        print(f"✅ VybeCoding Compliance: {meets_quality_criteria}")
        print(f"✅ Performance Compliance: {meets_time_criteria}")
        
        if final_report['vybecoding_readiness']:
            print("\n🎉 ENHANCED MAS SYSTEM: PRODUCTION READY ✅")
            print("   All content types meet VybeCoding.ai excellence standards")
            print("   Performance targets achieved across all agents")
            print("   System ready for autonomous content generation")
        else:
            print("\n⚠️  ENHANCED MAS SYSTEM: NEEDS OPTIMIZATION")
            print("   Some content types require quality improvements")
            print("   Performance optimization may be needed")
        
        # Save final report
        await self.save_content('comprehensive_test_report.json', final_report)
        
        return final_report

async def main():
    """Main test execution function"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    test_system = ComprehensiveContentTest()
    
    try:
        final_report = await test_system.execute_comprehensive_test()
        
        print("\n" + "="*70)
        print("🎯 BMAD METHOD VALIDATION COMPLETE")
        print("="*70)
        
        if final_report['vybecoding_readiness']:
            print("✅ Enhanced MAS System validated for production deployment")
            print("✅ All content types meet VybeCoding.ai excellence standards")
            print("✅ System ready for enterprise-scale content generation")
        else:
            print("⚠️  Enhanced MAS System requires optimization before production")
            print("📊 Detailed results saved for analysis and improvement")
        
        return final_report
        
    except Exception as e:
        print(f"❌ Comprehensive test failed: {e}")
        return None

if __name__ == "__main__":
    asyncio.run(main())
