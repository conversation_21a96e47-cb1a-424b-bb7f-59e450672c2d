#!/usr/bin/env python3
"""
Test Advanced LLM Protocols (June 2025)
Tests MCP, A2A, Agentic Retrieval, Guardrails, and Context Engine
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime

class AdvancedProtocolsTest:
    def __init__(self):
        self.base_url = "http://localhost:5173"
        self.test_results = []
        
        # Protocol endpoints
        self.endpoints = {
            'mcp': 'http://localhost:3002',
            'a2a': 'http://localhost:3003', 
            'agentic_retrieval': 'http://localhost:3004',
            'guardrails': 'http://localhost:3005',
            'vybe_api': self.base_url
        }
        
    async def test_protocol_availability(self, session):
        """Test if all advanced protocol services are available"""
        print("🔍 Testing Advanced Protocol Availability...")
        
        for protocol, endpoint in self.endpoints.items():
            try:
                if protocol == 'vybe_api':
                    # Test main API
                    async with session.get(f"{endpoint}/api/system/metrics", timeout=5) as response:
                        status = "available" if response.status == 200 else "unavailable"
                else:
                    # Test protocol health endpoints
                    async with session.get(f"{endpoint}/health", timeout=5) as response:
                        status = "available" if response.status == 200 else "unavailable"
                
                print(f"    {'✅' if status == 'available' else '❌'} {protocol.upper()}: {status}")
                self.test_results.append({
                    "test": f"protocol_{protocol}",
                    "status": status,
                    "endpoint": endpoint
                })
                
            except Exception as e:
                print(f"    ❌ {protocol.upper()}: error - {str(e)[:50]}")
                self.test_results.append({
                    "test": f"protocol_{protocol}",
                    "status": "error",
                    "error": str(e)[:100]
                })
    
    async def test_enhanced_content_generation(self, session):
        """Test content generation with all advanced protocols enabled"""
        print("\n🚀 Testing Enhanced Content Generation...")
        
        test_cases = [
            {
                "name": "Full Protocol Stack",
                "payload": {
                    "input": "Create an advanced AI course covering latest 2025 developments",
                    "type": "text",
                    "outputTypes": ["course"],
                    "options": {
                        "autonomousMode": True,
                        "enableWebSearch": True,
                        "enableMCP": True,
                        "enableA2A": True,
                        "enableAgenticRetrieval": True,
                        "enableGuardrails": True,
                        "agentId": "test_agent",
                        "contextWindow": 200000,
                        "detectedType": {
                            "type": "course",
                            "confidence": 95,
                            "keywords": ["AI", "2025", "advanced", "course"]
                        }
                    }
                }
            },
            {
                "name": "Agentic Retrieval Focus",
                "payload": {
                    "input": "Explain the latest developments in multi-agent systems",
                    "type": "text",
                    "outputTypes": ["article"],
                    "options": {
                        "autonomousMode": True,
                        "enableAgenticRetrieval": True,
                        "enableGuardrails": True,
                        "agentId": "retrieval_agent",
                        "detectedType": {
                            "type": "article",
                            "confidence": 90,
                            "keywords": ["multi-agent", "systems", "developments"]
                        }
                    }
                }
            }
        ]
        
        for test_case in test_cases:
            try:
                print(f"  Testing {test_case['name']}...")
                start_time = time.time()
                
                async with session.post(f"{self.base_url}/api/vybe/process-content", 
                                      json=test_case['payload'],
                                      timeout=120) as response:
                    if response.status == 200:
                        data = await response.json()
                        duration = time.time() - start_time
                        task_id = data.get('taskId', 'unknown')
                        print(f"    ✅ {test_case['name']}: Task {task_id} created ({duration:.1f}s)")
                        
                        # Wait and check enhanced task status
                        await asyncio.sleep(5)
                        await self.check_enhanced_task_status(session, task_id, test_case['name'])
                        
                    else:
                        error_text = await response.text()
                        print(f"    ❌ {test_case['name']}: HTTP {response.status} - {error_text[:100]}")
                        
            except Exception as e:
                print(f"    ❌ {test_case['name']}: {str(e)}")
    
    async def check_enhanced_task_status(self, session, task_id, test_name):
        """Check task status and look for advanced protocol results"""
        try:
            async with session.get(f"{self.base_url}/api/vybe/task-status/{task_id}") as response:
                if response.status == 200:
                    data = await response.json()
                    status = data.get('status', 'unknown')
                    progress = data.get('progress', 0)
                    result = data.get('result', {})
                    
                    # Check for advanced protocol features
                    has_web_search = 'webSearchResults' in result
                    has_agentic_retrieval = 'agenticRetrievalResults' in result
                    has_guardrails = 'guardrailsScore' in result
                    has_a2a = 'a2aMessages' in result
                    
                    print(f"      📊 Task Status: {status} ({progress}%)")
                    if has_web_search:
                        print(f"      🌐 Web search results: ✅")
                    if has_agentic_retrieval:
                        print(f"      🧠 Agentic retrieval: ✅")
                    if has_guardrails:
                        score = result.get('guardrailsScore', {}).get('overallScore', 0)
                        print(f"      🛡️  Guardrails score: {score:.2f}")
                    if has_a2a:
                        print(f"      🤝 A2A messages: ✅")
                    
                    self.test_results.append({
                        "test": f"{test_name}_enhanced_features",
                        "status": "success",
                        "task_status": status,
                        "progress": progress,
                        "has_web_search": has_web_search,
                        "has_agentic_retrieval": has_agentic_retrieval,
                        "has_guardrails": has_guardrails,
                        "has_a2a": has_a2a,
                        "task_id": task_id
                    })
                else:
                    print(f"      ❌ Failed to get task status: HTTP {response.status}")
                    
        except Exception as e:
            print(f"      ❌ Task status error: {str(e)}")
    
    async def test_context_engine_integration(self, session):
        """Test Augment Code-style context engine integration"""
        print("\n🧠 Testing Context Engine Integration...")
        
        try:
            # Test context retrieval API
            async with session.post(f"{self.base_url}/api/context/retrieve", 
                                  json={
                                      "query": "SvelteKit component patterns",
                                      "max_tokens": 10000,
                                      "include_code": True
                                  },
                                  timeout=10) as response:
                if response.status == 200:
                    data = await response.json()
                    context_items = data.get('items', [])
                    total_tokens = sum(item.get('tokens', 0) for item in context_items)
                    
                    print(f"    ✅ Context retrieval: {len(context_items)} items, {total_tokens} tokens")
                    self.test_results.append({
                        "test": "context_engine_retrieval",
                        "status": "success",
                        "items_count": len(context_items),
                        "total_tokens": total_tokens
                    })
                else:
                    print(f"    ❌ Context retrieval: HTTP {response.status}")
                    self.test_results.append({
                        "test": "context_engine_retrieval",
                        "status": "failed",
                        "error": f"HTTP {response.status}"
                    })
                    
        except Exception as e:
            print(f"    ❌ Context engine: {str(e)}")
            self.test_results.append({
                "test": "context_engine_retrieval",
                "status": "error",
                "error": str(e)
            })
    
    async def run_comprehensive_test(self):
        """Run comprehensive advanced protocols test suite"""
        print("🚀 Advanced LLM Protocols Test Suite (June 2025)")
        print("=" * 70)
        print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        async with aiohttp.ClientSession() as session:
            # Test protocol availability
            await self.test_protocol_availability(session)
            
            # Test context engine integration
            await self.test_context_engine_integration(session)
            
            # Test enhanced content generation
            await self.test_enhanced_content_generation(session)
        
        # Generate comprehensive report
        self.generate_comprehensive_report()
    
    def generate_comprehensive_report(self):
        """Generate comprehensive test report"""
        print("\n" + "=" * 70)
        print("📊 ADVANCED PROTOCOLS TEST RESULTS")
        print("=" * 70)
        
        total_tests = len(self.test_results)
        successful_tests = len([r for r in self.test_results if r['status'] in ['success', 'available']])
        failed_tests = len([r for r in self.test_results if r['status'] in ['failed', 'error', 'unavailable']])
        
        print(f"Total Tests: {total_tests}")
        print(f"✅ Successful: {successful_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"Success Rate: {(successful_tests/total_tests*100):.1f}%")
        
        print("\nProtocol Status Summary:")
        protocols = ['mcp', 'a2a', 'agentic_retrieval', 'guardrails', 'vybe_api']
        for protocol in protocols:
            result = next((r for r in self.test_results if r['test'] == f'protocol_{protocol}'), None)
            if result:
                status_icon = "✅" if result['status'] == 'available' else "❌"
                print(f"  {status_icon} {protocol.upper().replace('_', ' ')}: {result['status']}")
        
        print("\nAdvanced Features Detected:")
        enhanced_features = [r for r in self.test_results if 'enhanced_features' in r['test']]
        for feature in enhanced_features:
            print(f"  🌐 Web Search: {'✅' if feature.get('has_web_search') else '❌'}")
            print(f"  🧠 Agentic Retrieval: {'✅' if feature.get('has_agentic_retrieval') else '❌'}")
            print(f"  🛡️  Guardrails: {'✅' if feature.get('has_guardrails') else '❌'}")
            print(f"  🤝 A2A Communication: {'✅' if feature.get('has_a2a') else '❌'}")
            break
        
        # Save results
        with open('advanced-protocols-test-results.json', 'w') as f:
            json.dump({
                "timestamp": datetime.now().isoformat(),
                "summary": {
                    "total_tests": total_tests,
                    "successful": successful_tests,
                    "failed": failed_tests,
                    "success_rate": successful_tests/total_tests*100
                },
                "protocol_status": {
                    protocol: next((r['status'] for r in self.test_results if r['test'] == f'protocol_{protocol}'), 'unknown')
                    for protocol in protocols
                },
                "detailed_results": self.test_results
            }, f, indent=2)
        
        print(f"\n📁 Detailed results saved to: advanced-protocols-test-results.json")
        
        if successful_tests >= total_tests * 0.7:  # 70% success rate
            print("\n🎉 ADVANCED PROTOCOLS READY!")
            print("✅ June 2025 LLM capabilities are operational!")
        else:
            print(f"\n⚠️  Some protocols need attention. Review errors above.")

async def main():
    tester = AdvancedProtocolsTest()
    await tester.run_comprehensive_test()

if __name__ == "__main__":
    asyncio.run(main())
