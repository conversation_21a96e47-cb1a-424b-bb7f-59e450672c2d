#!/bin/bash
"""
🛡️ START DEVELOPMENT WITH ANTI-SIMULATION PROTECTION
Automatically starts dev server with real-time violation monitoring
"""

set -e

echo "🛡️ VybeCoding.ai Development Server with Anti-Simulation Protection"
echo "=================================================================="

# Function to cleanup background processes
cleanup() {
    echo ""
    echo "🛑 Shutting down protection services..."
    
    # Kill the watcher if it's running
    if [ ! -z "$WATCHER_PID" ]; then
        kill $WATCHER_PID 2>/dev/null || true
        echo "✅ Anti-simulation watcher stopped"
    fi
    
    # Kill dev server
    pkill -f "vite dev" 2>/dev/null || true
    echo "✅ Development server stopped"
    
    exit 0
}

# Set up cleanup on script exit
trap cleanup EXIT INT TERM

echo "🔍 Step 1: Running initial anti-simulation validation..."
if ! npm run validate:no-simulations; then
    echo ""
    echo "🚨 STARTUP BLOCKED: Simulation violations detected!"
    echo "📋 Fix all violations before starting development server"
    echo "🔧 Run: python3 scripts/anti-simulation-validator.py"
    exit 1
fi

echo "✅ Initial validation passed!"
echo ""

echo "🔍 Step 2: Starting real-time violation monitoring..."
# Start the watcher in background
python3 scripts/anti-simulation-watcher.py &
WATCHER_PID=$!
echo "✅ Real-time watcher started (PID: $WATCHER_PID)"
echo ""

echo "🚀 Step 3: Starting development server..."
echo "📍 Server will be available at: http://localhost:5173"
echo "🛡️ Real-time protection: ACTIVE"
echo "⚠️ Any violations will be immediately detected"
echo ""

# Start the actual dev server
npm run dev:simple

# This line should never be reached due to the trap, but just in case
cleanup
