#!/bin/bash
# VybeCoding.ai Documentation Deployment Script
# Deploys documentation to GitHub Pages or other hosting platforms

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DOCS_DIR="$PROJECT_ROOT/docs"
BUILD_DIR="$DOCS_DIR/dist"
DEPLOYMENT_TARGET="${1:-github-pages}"

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check deployment prerequisites
check_prerequisites() {
    log "Checking deployment prerequisites..."
    
    # Check if documentation exists
    if [ ! -d "$DOCS_DIR" ]; then
        error "Documentation directory not found: $DOCS_DIR"
        error "Please run './scripts/generate-docs.sh' first"
        exit 1
    fi
    
    # Check if git is available
    if ! command -v git &> /dev/null; then
        error "Git is required for deployment"
        exit 1
    fi
    
    # Check if we're in a git repository
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        error "Not in a git repository"
        exit 1
    fi
    
    log "Prerequisites check passed"
}

# Generate fresh documentation
generate_fresh_docs() {
    log "Generating fresh documentation..."
    
    cd "$PROJECT_ROOT"
    
    # Run documentation generation
    if [ -f "$SCRIPT_DIR/generate-docs.sh" ]; then
        "$SCRIPT_DIR/generate-docs.sh"
    else
        error "Documentation generation script not found"
        exit 1
    fi
    
    log "Fresh documentation generated"
}

# Build documentation site
build_docs_site() {
    log "Building documentation site..."
    
    # Create build directory
    mkdir -p "$BUILD_DIR"
    
    # Copy all documentation files
    cp -r "$DOCS_DIR"/*.md "$BUILD_DIR/" 2>/dev/null || true
    cp -r "$DOCS_DIR/api" "$BUILD_DIR/" 2>/dev/null || true
    cp -r "$DOCS_DIR/components" "$BUILD_DIR/" 2>/dev/null || true
    
    # Create index.html for GitHub Pages
    cat > "$BUILD_DIR/index.html" << 'EOF'
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VybeCoding.ai Documentation</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            text-align: center;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        .card {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        .card:hover {
            transform: translateY(-2px);
        }
        .card h3 {
            margin-top: 0;
            color: #667eea;
        }
        .card a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        .card a:hover {
            text-decoration: underline;
        }
        .footer {
            text-align: center;
            margin-top: 3rem;
            padding: 1rem;
            color: #666;
            border-top: 1px solid #eee;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 VybeCoding.ai Documentation</h1>
        <p>AI-powered education platform documentation and API reference</p>
    </div>
    
    <div class="grid">
        <div class="card">
            <h3>📚 API Documentation</h3>
            <p>Interactive API documentation with examples and testing capabilities.</p>
            <a href="api/openapi.yaml">OpenAPI Specification</a><br>
            <a href="api/typescript/index.html">TypeScript API Docs</a><br>
            <a href="api/postman-collection.json">Postman Collection</a>
        </div>
        
        <div class="card">
            <h3>🧩 Component Library</h3>
            <p>Svelte component documentation with props and usage examples.</p>
            <a href="components/index.html">Component Documentation</a>
        </div>
        
        <div class="card">
            <h3>🏗️ Project Information</h3>
            <p>Project overview, architecture, and development guides.</p>
            <a href="project-info.md">Project Overview</a><br>
            <a href="setup-guide.md">Setup Guide</a><br>
            <a href="architecture.md">Architecture</a>
        </div>
        
        <div class="card">
            <h3>🔒 Security & Operations</h3>
            <p>Security policies, backup procedures, and operational guides.</p>
            <a href="backup-recovery.md">Backup & Recovery</a><br>
            <a href="github-workflow.md">Development Workflow</a>
        </div>
        
        <div class="card">
            <h3>🤖 AI Integration</h3>
            <p>AI integration policies and multi-agent system documentation.</p>
            <a href="ai-integration-policy.md">AI Integration Policy</a><br>
            <a href="bmad-compliance.md">BMAD Method Compliance</a><br>
            <a href="vybe-compliance.md">Vybe Method Compliance</a>
        </div>
        
        <div class="card">
            <h3>📊 Quick Links</h3>
            <p>Direct access to live system endpoints and monitoring.</p>
            <a href="/api/health" target="_blank">Health Check</a><br>
            <a href="/api/metrics" target="_blank">System Metrics</a><br>
            <a href="/api/security" target="_blank">Security Status</a>
        </div>
    </div>
    
    <div class="footer">
        <p>Generated on: <span id="timestamp"></span></p>
        <p>VybeCoding.ai - AI-powered education platform</p>
    </div>
    
    <script>
        document.getElementById('timestamp').textContent = new Date().toLocaleString();
    </script>
</body>
</html>
EOF
    
    log "Documentation site built: $BUILD_DIR"
}

# Deploy to GitHub Pages
deploy_github_pages() {
    log "Deploying to GitHub Pages..."
    
    cd "$PROJECT_ROOT"
    
    # Check if gh-pages branch exists
    if git show-ref --verify --quiet refs/heads/gh-pages; then
        info "gh-pages branch exists"
    else
        info "Creating gh-pages branch"
        git checkout --orphan gh-pages
        git rm -rf .
        git commit --allow-empty -m "Initial gh-pages commit"
        git checkout main
    fi
    
    # Deploy using git subtree
    git subtree push --prefix=docs/dist origin gh-pages
    
    log "Deployed to GitHub Pages"
    info "Documentation will be available at: https://$(git config --get remote.origin.url | sed 's/.*github.com[:/]\([^/]*\)\/\([^.]*\).*/\1.github.io\/\2/')"
}

# Deploy to custom hosting
deploy_custom() {
    log "Preparing for custom deployment..."
    
    # Create deployment package
    local deploy_package="$PROJECT_ROOT/docs-deployment.tar.gz"
    
    cd "$BUILD_DIR"
    tar -czf "$deploy_package" .
    
    log "Deployment package created: $deploy_package"
    info "Upload this package to your hosting provider"
}

# Main deployment function
main() {
    log "Starting documentation deployment to: $DEPLOYMENT_TARGET"
    
    check_prerequisites
    generate_fresh_docs
    build_docs_site
    
    case "$DEPLOYMENT_TARGET" in
        "github-pages")
            deploy_github_pages
            ;;
        "custom")
            deploy_custom
            ;;
        *)
            error "Unknown deployment target: $DEPLOYMENT_TARGET"
            error "Supported targets: github-pages, custom"
            exit 1
            ;;
    esac
    
    log "Documentation deployment completed successfully!"
}

# Show usage if no arguments
if [ $# -eq 0 ]; then
    echo "Usage: $0 [deployment-target]"
    echo ""
    echo "Deployment targets:"
    echo "  github-pages  Deploy to GitHub Pages (default)"
    echo "  custom        Create deployment package for custom hosting"
    echo ""
    echo "Examples:"
    echo "  $0 github-pages"
    echo "  $0 custom"
    exit 0
fi

# Run main function
main "$@"
