#!/bin/bash

echo "🌐 VybeCoding.ai Network Connectivity Test"
echo "═══════════════════════════════════════════"

# Test local access
echo "📍 Testing local access..."
if curl -s -o /dev/null -w "%{http_code}" http://localhost:5173/ | grep -q "200"; then
    echo "✅ Local access: http://localhost:5173/ - WORKING"
else
    echo "❌ Local access: http://localhost:5173/ - FAILED"
fi

# Test container IP access
echo "🔧 Testing container IP access..."
if curl -s -o /dev/null -w "%{http_code}" http://**********:5173/ | grep -q "200"; then
    echo "✅ Container IP: http://**********:5173/ - WORKING"
else
    echo "❌ Container IP: http://**********:5173/ - FAILED"
fi

# Show network information
echo ""
echo "🔍 Network Information:"
echo "Container IP: $(hostname -I | awk '{print $1}')"
echo "Host IP (from container): $(ip route | grep default | awk '{print $3}')"

# Show VS Code port forwarding status
echo ""
echo "💡 For network access from other devices:"
echo "1. Check VS Code PORTS tab"
echo "2. Right-click port 5173 → 'Change Port Visibility' → 'Public'"
echo "3. Use the forwarded URL shown in VS Code"
echo "4. Or restart the dev container to apply new network settings"

echo ""
echo "🚀 If network access still doesn't work, restart the dev container:"
echo "   Command Palette → 'Dev Containers: Rebuild Container'"
