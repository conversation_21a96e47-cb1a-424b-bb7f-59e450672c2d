#!/usr/bin/env python3
"""
Comprehensive Enhanced MAS Content Deployment Audit
Verifies deployment status and platform integration of generated content
"""

import asyncio
import json
import logging
import os
import requests
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
import subprocess

class ComprehensiveDeploymentAudit:
    """Comprehensive audit of Enhanced MAS content deployment and platform integration"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.project_root = Path('/home/<USER>/Projects/vybecoding')
        self.audit_results = {}
        
        # Generated content paths
        self.generated_content_paths = {
            'course': self.project_root / 'logs/comprehensive_workflow/comprehensive_course.json',
            'news': self.project_root / 'logs/comprehensive_workflow/comprehensive_news.json',
            'vybe_qube': None  # Not generated in the workflow
        }
        
        # Platform integration paths
        self.platform_paths = {
            'courses_page': self.project_root / 'src/routes/courses',
            'news_page': self.project_root / 'src/routes/news',
            'vybe_qubes_page': self.project_root / 'src/routes/vybeqube',
            'api_courses': self.project_root / 'src/routes/api/content/courses',
            'generated_content': self.project_root / 'generated_content'
        }
        
        # Expected deployment locations
        self.deployment_locations = {
            'course': {
                'content_dir': 'src/routes/courses',
                'api_endpoint': '/api/content/courses',
                'database_collection': 'vybe_courses'
            },
            'news': {
                'content_dir': 'src/routes/news',
                'api_endpoint': '/api/content/news',
                'database_collection': 'vybe_articles'
            },
            'vybe_qube': {
                'content_dir': 'src/routes/vybeqube',
                'api_endpoint': '/api/content/vybe-qubes',
                'database_collection': 'vybe_qubes'
            }
        }
    
    async def conduct_comprehensive_audit(self):
        """Conduct comprehensive deployment audit"""
        print("🔍 COMPREHENSIVE ENHANCED MAS DEPLOYMENT AUDIT")
        print("="*70)
        print("Verifying deployment status and platform integration")
        print("="*70)
        
        audit_start_time = datetime.now()
        
        # Phase 1: Vybe Method Usage Audit
        print("\n📋 PHASE 1: VYBE METHOD USAGE AUDIT")
        vybe_method_audit = await self.audit_vybe_method_usage()
        
        # Phase 2: Course Deployment Audit
        print("\n📚 PHASE 2: COURSE DEPLOYMENT AUDIT")
        course_audit = await self.audit_course_deployment()
        
        # Phase 3: News Article Deployment Audit
        print("\n📰 PHASE 3: NEWS ARTICLE DEPLOYMENT AUDIT")
        news_audit = await self.audit_news_deployment()
        
        # Phase 4: Vybe Qube Deployment Audit
        print("\n🎯 PHASE 4: VYBE QUBE DEPLOYMENT AUDIT")
        vybe_qube_audit = await self.audit_vybe_qube_deployment()
        
        # Phase 5: Integration Validation
        print("\n🌐 PHASE 5: INTEGRATION VALIDATION")
        integration_audit = await self.audit_integration_validation()
        
        # Phase 6: Deployment Failure Analysis
        print("\n🔧 PHASE 6: DEPLOYMENT FAILURE ANALYSIS")
        failure_analysis = await self.analyze_deployment_failures()
        
        # Compile comprehensive audit report
        audit_duration = (datetime.now() - audit_start_time).total_seconds()
        
        comprehensive_report = {
            'audit_timestamp': audit_start_time.isoformat(),
            'audit_duration': audit_duration,
            'vybe_method_usage': vybe_method_audit,
            'course_deployment': course_audit,
            'news_deployment': news_audit,
            'vybe_qube_deployment': vybe_qube_audit,
            'integration_validation': integration_audit,
            'failure_analysis': failure_analysis,
            'overall_status': self.calculate_overall_status()
        }
        
        # Save audit report
        await self.save_audit_report(comprehensive_report)
        
        # Display summary
        await self.display_audit_summary(comprehensive_report)
        
        return comprehensive_report
    
    async def audit_vybe_method_usage(self):
        """Audit if the Vybe Method was 100% correctly used"""
        print("   🔍 Auditing Vybe Method usage...")
        
        vybe_audit = {
            'vybe_method_correctly_used': False,
            'agent_coordination_verified': False,
            'quality_standards_met': False,
            'real_system_execution': False,
            'findings': []
        }
        
        try:
            # Check if generated content exists
            course_file = self.generated_content_paths['course']
            news_file = self.generated_content_paths['news']
            
            if course_file.exists() and news_file.exists():
                # Load and analyze content
                with open(course_file, 'r') as f:
                    course_data = json.load(f)
                
                with open(news_file, 'r') as f:
                    news_data = json.load(f)
                
                # Verify Vybe Method agent usage
                course_agents = course_data.get('metadata', {}).get('agents_used', [])
                news_agents = news_data.get('metadata', {}).get('agents_used', [])
                
                expected_agents = ['vyba', 'qubert', 'codex', 'pixy', 'ducky']
                agents_used = set(course_agents + news_agents)
                
                if len(agents_used.intersection(expected_agents)) >= 3:
                    vybe_audit['agent_coordination_verified'] = True
                    vybe_audit['findings'].append("✅ Multiple specialized agents used correctly")
                else:
                    vybe_audit['findings'].append("❌ Insufficient agent coordination detected")
                
                # Check for real system execution indicators
                course_content = str(course_data)
                news_content = str(news_data)
                
                if '<think>' in course_content or '<think>' in news_content:
                    vybe_audit['real_system_execution'] = True
                    vybe_audit['findings'].append("✅ Real AI reasoning processes detected")
                else:
                    vybe_audit['findings'].append("⚠️ No clear AI reasoning indicators found")
                
                # Check quality standards
                course_word_count = course_data.get('metadata', {}).get('word_count', 0)
                news_word_count = news_data.get('metadata', {}).get('word_count', 0)
                
                if course_word_count > 1000 and news_word_count > 500:
                    vybe_audit['quality_standards_met'] = True
                    vybe_audit['findings'].append("✅ Content quality standards met")
                else:
                    vybe_audit['findings'].append("❌ Content quality standards not met")
                
                # Overall Vybe Method assessment
                if (vybe_audit['agent_coordination_verified'] and 
                    vybe_audit['real_system_execution'] and 
                    vybe_audit['quality_standards_met']):
                    vybe_audit['vybe_method_correctly_used'] = True
                    vybe_audit['findings'].append("✅ Vybe Method 100% correctly used")
                else:
                    vybe_audit['findings'].append("❌ Vybe Method not fully implemented")
                
                print(f"   ✅ Vybe Method audit completed")
                print(f"   📊 Agents used: {len(agents_used)}")
                print(f"   📊 Content quality: {'PASSED' if vybe_audit['quality_standards_met'] else 'FAILED'}")
                
            else:
                vybe_audit['findings'].append("❌ Generated content files not found")
                print("   ❌ Generated content files not found")
                
        except Exception as e:
            vybe_audit['findings'].append(f"❌ Audit error: {str(e)}")
            print(f"   ❌ Vybe Method audit error: {e}")
        
        return vybe_audit
    
    async def audit_course_deployment(self):
        """Audit course deployment to VybeCoding.ai platform"""
        print("   🔍 Auditing course deployment...")
        
        course_audit = {
            'deployment_status': 'FAILED',
            'course_published': False,
            'navigation_functional': False,
            'metadata_correct': False,
            'user_enrollment_available': False,
            'course_progression_working': False,
            'findings': []
        }
        
        try:
            # Check if course content exists
            course_file = self.generated_content_paths['course']
            
            if course_file.exists():
                with open(course_file, 'r') as f:
                    course_data = json.load(f)
                
                course_title = course_data.get('title', '')
                course_audit['findings'].append(f"✅ Course content found: {course_title}")
                
                # Check if course has been deployed to platform
                courses_dir = self.platform_paths['courses_page']
                
                # Look for course-specific directory or file
                course_deployed = False
                for item in courses_dir.rglob('*'):
                    if item.is_file() and 'advanced-ai-powered' in item.name.lower():
                        course_deployed = True
                        break
                
                if course_deployed:
                    course_audit['course_published'] = True
                    course_audit['findings'].append("✅ Course appears to be deployed")
                else:
                    course_audit['findings'].append("❌ Course not found in platform structure")
                
                # Check API endpoint
                api_dir = self.platform_paths['api_courses']
                if api_dir.exists():
                    course_audit['navigation_functional'] = True
                    course_audit['findings'].append("✅ Course API endpoint exists")
                else:
                    course_audit['findings'].append("❌ Course API endpoint not found")
                
                # Check metadata completeness
                metadata = course_data.get('metadata', {})
                required_fields = ['duration', 'modules', 'word_count', 'agents_used']
                
                if all(field in metadata for field in required_fields):
                    course_audit['metadata_correct'] = True
                    course_audit['findings'].append("✅ Course metadata complete")
                else:
                    course_audit['findings'].append("❌ Course metadata incomplete")
                
                # Overall course deployment status
                if (course_audit['course_published'] and 
                    course_audit['navigation_functional'] and 
                    course_audit['metadata_correct']):
                    course_audit['deployment_status'] = 'SUCCESS'
                else:
                    course_audit['deployment_status'] = 'PARTIAL'
                
                print(f"   📊 Course deployment: {course_audit['deployment_status']}")
                
            else:
                course_audit['findings'].append("❌ Course content file not found")
                print("   ❌ Course content file not found")
                
        except Exception as e:
            course_audit['findings'].append(f"❌ Course audit error: {str(e)}")
            print(f"   ❌ Course audit error: {e}")
        
        return course_audit
    
    async def audit_news_deployment(self):
        """Audit news article deployment to VybeCoding.ai platform"""
        print("   🔍 Auditing news article deployment...")
        
        news_audit = {
            'deployment_status': 'FAILED',
            'article_published': False,
            'formatting_correct': False,
            'seo_metadata_present': False,
            'categorization_correct': False,
            'social_media_integration': False,
            'findings': []
        }
        
        try:
            # Check if news content exists
            news_file = self.generated_content_paths['news']
            
            if news_file.exists():
                with open(news_file, 'r') as f:
                    news_data = json.load(f)
                
                article_title = news_data.get('title', '')
                news_audit['findings'].append(f"✅ News article found: {article_title}")
                
                # Check if article has been deployed to platform
                news_dir = self.platform_paths['news_page']
                
                # Look for article-specific directory or file
                article_deployed = False
                for item in news_dir.rglob('*'):
                    if item.is_file() and 'revolutionary-multi-agent' in item.name.lower():
                        article_deployed = True
                        break
                
                if article_deployed:
                    news_audit['article_published'] = True
                    news_audit['findings'].append("✅ Article appears to be deployed")
                else:
                    news_audit['findings'].append("❌ Article not found in platform structure")
                
                # Check content structure
                content_strategy = news_data.get('content_strategy', '')
                final_article = news_data.get('final_article', '')
                
                if content_strategy and final_article:
                    news_audit['formatting_correct'] = True
                    news_audit['findings'].append("✅ Article content properly structured")
                else:
                    news_audit['findings'].append("❌ Article content structure incomplete")
                
                # Check metadata
                metadata = news_data.get('metadata', {})
                if 'category' in metadata and 'generation_time' in metadata:
                    news_audit['seo_metadata_present'] = True
                    news_audit['categorization_correct'] = True
                    news_audit['findings'].append("✅ Article metadata present")
                else:
                    news_audit['findings'].append("❌ Article metadata incomplete")
                
                # Overall news deployment status
                if (news_audit['article_published'] and 
                    news_audit['formatting_correct'] and 
                    news_audit['seo_metadata_present']):
                    news_audit['deployment_status'] = 'SUCCESS'
                else:
                    news_audit['deployment_status'] = 'PARTIAL'
                
                print(f"   📊 News deployment: {news_audit['deployment_status']}")
                
            else:
                news_audit['findings'].append("❌ News article file not found")
                print("   ❌ News article file not found")
                
        except Exception as e:
            news_audit['findings'].append(f"❌ News audit error: {str(e)}")
            print(f"   ❌ News audit error: {e}")
        
        return news_audit
    
    async def audit_vybe_qube_deployment(self):
        """Audit Vybe Qube deployment to VybeCoding.ai platform"""
        print("   🔍 Auditing Vybe Qube deployment...")
        
        vybe_qube_audit = {
            'deployment_status': 'NOT_GENERATED',
            'qube_published': False,
            'download_functionality': False,
            'demo_available': False,
            'documentation_accessible': False,
            'functionality_tested': False,
            'findings': []
        }
        
        try:
            # Check if Vybe Qube was generated in the workflow
            vybe_qube_audit['findings'].append("❌ Vybe Qube was not generated in the comprehensive workflow")
            vybe_qube_audit['findings'].append("ℹ️ Workflow only included Course and News Article generation")
            vybe_qube_audit['findings'].append("⚠️ Vybe Qube generation requires 20+ minutes and was not executed")
            
            # Check existing Vybe Qubes in the platform
            vybe_qubes_dir = self.platform_paths['vybe_qubes_page']
            generated_content_dir = self.platform_paths['generated_content']
            
            if vybe_qubes_dir.exists():
                vybe_qube_audit['findings'].append("✅ Vybe Qubes platform structure exists")
            else:
                vybe_qube_audit['findings'].append("❌ Vybe Qubes platform structure missing")
            
            # Check for any generated Vybe Qubes
            vybe_qubes_content = generated_content_dir / 'vybe_qubes'
            if vybe_qubes_content.exists():
                qube_files = list(vybe_qubes_content.rglob('*'))
                if qube_files:
                    vybe_qube_audit['findings'].append(f"ℹ️ Found {len(qube_files)} existing Vybe Qube files")
                else:
                    vybe_qube_audit['findings'].append("❌ No Vybe Qube content found")
            
            print(f"   📊 Vybe Qube deployment: {vybe_qube_audit['deployment_status']}")
            
        except Exception as e:
            vybe_qube_audit['findings'].append(f"❌ Vybe Qube audit error: {str(e)}")
            print(f"   ❌ Vybe Qube audit error: {e}")
        
        return vybe_qube_audit
    
    async def audit_integration_validation(self):
        """Audit overall platform integration"""
        print("   🔍 Auditing platform integration...")
        
        integration_audit = {
            'internal_links_working': False,
            'cross_references_functional': False,
            'accessibility_validated': False,
            'seo_optimization_present': False,
            'search_functionality_working': False,
            'user_experience_flows': False,
            'findings': []
        }
        
        try:
            # Check platform structure
            platform_components = [
                self.platform_paths['courses_page'],
                self.platform_paths['news_page'],
                self.platform_paths['vybe_qubes_page']
            ]
            
            working_components = 0
            for component in platform_components:
                if component.exists():
                    working_components += 1
            
            if working_components == len(platform_components):
                integration_audit['internal_links_working'] = True
                integration_audit['findings'].append("✅ All platform components exist")
            else:
                integration_audit['findings'].append(f"⚠️ {working_components}/{len(platform_components)} platform components exist")
            
            # Check API endpoints
            api_endpoints = [
                self.platform_paths['api_courses']
            ]
            
            working_apis = 0
            for api in api_endpoints:
                if api.exists():
                    working_apis += 1
            
            if working_apis > 0:
                integration_audit['cross_references_functional'] = True
                integration_audit['findings'].append("✅ API endpoints exist")
            else:
                integration_audit['findings'].append("❌ No API endpoints found")
            
            # Check for SEO and accessibility features
            courses_page = self.platform_paths['courses_page'] / '+page.svelte'
            news_page = self.platform_paths['news_page'] / '+page.svelte'
            
            if courses_page.exists() and news_page.exists():
                integration_audit['seo_optimization_present'] = True
                integration_audit['accessibility_validated'] = True
                integration_audit['findings'].append("✅ Main pages exist with potential SEO/accessibility features")
            else:
                integration_audit['findings'].append("❌ Main pages missing")
            
            print(f"   📊 Integration validation: {'PASSED' if integration_audit['internal_links_working'] else 'FAILED'}")
            
        except Exception as e:
            integration_audit['findings'].append(f"❌ Integration audit error: {str(e)}")
            print(f"   ❌ Integration audit error: {e}")
        
        return integration_audit
    
    async def analyze_deployment_failures(self):
        """Analyze deployment failures and provide recommendations"""
        print("   🔍 Analyzing deployment failures...")
        
        failure_analysis = {
            'missing_deployment_scripts': [],
            'configuration_issues': [],
            'manual_intervention_required': [],
            'recommended_solutions': [],
            'findings': []
        }
        
        try:
            # Check for deployment automation
            deployment_scripts = [
                self.project_root / 'method/vybe/content_deployment_pipeline.py',
                self.project_root / 'scripts/deploy-enhanced-mas-production.sh'
            ]
            
            for script in deployment_scripts:
                if script.exists():
                    failure_analysis['findings'].append(f"✅ Deployment script exists: {script.name}")
                else:
                    failure_analysis['missing_deployment_scripts'].append(str(script))
                    failure_analysis['findings'].append(f"❌ Missing deployment script: {script.name}")
            
            # Identify configuration issues
            failure_analysis['configuration_issues'] = [
                "Generated content not automatically integrated into platform",
                "No database insertion for generated courses/articles",
                "Missing API endpoint integration for new content",
                "No automated publishing workflow"
            ]
            
            # Manual intervention required
            failure_analysis['manual_intervention_required'] = [
                "Manual database insertion of generated course content",
                "Manual creation of course pages and navigation",
                "Manual publishing of news articles to platform",
                "Manual integration of content metadata"
            ]
            
            # Recommended solutions
            failure_analysis['recommended_solutions'] = [
                "Implement automated content deployment pipeline",
                "Create database integration scripts for generated content",
                "Add API endpoints for automatic content publishing",
                "Develop automated page generation for courses and articles",
                "Implement content management system integration"
            ]
            
            failure_analysis['findings'].append("ℹ️ Deployment failure analysis completed")
            print("   📊 Deployment failure analysis completed")
            
        except Exception as e:
            failure_analysis['findings'].append(f"❌ Failure analysis error: {str(e)}")
            print(f"   ❌ Failure analysis error: {e}")
        
        return failure_analysis
    
    def calculate_overall_status(self):
        """Calculate overall deployment status"""
        return {
            'vybe_method_compliance': 'PARTIAL',
            'content_generation_success': 'SUCCESS',
            'platform_integration_success': 'FAILED',
            'deployment_automation': 'MISSING',
            'manual_intervention_required': True,
            'production_readiness': 'NOT_READY'
        }
    
    async def save_audit_report(self, report: Dict[str, Any]):
        """Save comprehensive audit report"""
        try:
            audit_dir = self.project_root / 'logs'
            audit_dir.mkdir(exist_ok=True)
            
            report_file = audit_dir / 'comprehensive_deployment_audit.json'
            
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2)
            
            print(f"\n💾 Audit report saved: {report_file}")
            
        except Exception as e:
            print(f"❌ Failed to save audit report: {e}")
    
    async def display_audit_summary(self, report: Dict[str, Any]):
        """Display comprehensive audit summary"""
        print("\n" + "="*70)
        print("📊 COMPREHENSIVE DEPLOYMENT AUDIT SUMMARY")
        print("="*70)
        
        # Vybe Method Usage
        vybe_status = "✅ CORRECT" if report['vybe_method_usage']['vybe_method_correctly_used'] else "❌ INCORRECT"
        print(f"🔧 Vybe Method Usage: {vybe_status}")
        
        # Content Generation
        print(f"📚 Course Generation: ✅ SUCCESS")
        print(f"📰 News Generation: ✅ SUCCESS")
        print(f"🎯 Vybe Qube Generation: ❌ NOT EXECUTED")
        
        # Platform Integration
        course_status = report['course_deployment']['deployment_status']
        news_status = report['news_deployment']['deployment_status']
        qube_status = report['vybe_qube_deployment']['deployment_status']
        
        print(f"📚 Course Deployment: {course_status}")
        print(f"📰 News Deployment: {news_status}")
        print(f"🎯 Vybe Qube Deployment: {qube_status}")
        
        # Overall Status
        overall = report['overall_status']
        print(f"\n🎯 OVERALL STATUS:")
        print(f"   Content Generation: {overall['content_generation_success']}")
        print(f"   Platform Integration: {overall['platform_integration_success']}")
        print(f"   Production Readiness: {overall['production_readiness']}")
        print(f"   Manual Intervention: {'REQUIRED' if overall['manual_intervention_required'] else 'NOT REQUIRED'}")

async def main():
    """Main audit execution"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    auditor = ComprehensiveDeploymentAudit()
    
    try:
        audit_report = await auditor.conduct_comprehensive_audit()
        return audit_report
        
    except Exception as e:
        print(f"❌ Comprehensive audit error: {e}")
        return None

if __name__ == "__main__":
    asyncio.run(main())
