# 📋 Manual Collection Setup for VybeCoding.ai

Since automated collection creation had connectivity issues, please create these collections manually in your Appwrite console.

## 🗄️ **Collections to Create**

### 1. **Users Collection**

- **Collection ID**: `users`
- **Name**: `Users`
- **Permissions**:
  - Read: `read("any")`
  - Write: `write("users")`

**Attributes:**

- `email` (String, 255, Required)
- `name` (String, 255, Required)
- `role` (String, 50, Required, Default: "student")
- `verified` (<PERSON><PERSON>an, Optional, Default: false)
- `subscription` (String, 2000, Optional) - JSON string
- `subscriptionStatus` (String, 50, Optional, Default: "free")
- `subscriptionTier` (String, 50, Optional, Default: "free")

### 2. **Courses Collection**

- **Collection ID**: `courses`
- **Name**: `Courses`
- **Permissions**:
  - Read: `read("any")`
  - Write: `write("instructors")`

**Attributes:**

- `title` (String, 255, Required)
- `description` (String, 2000, Required)
- `difficulty` (String, 50, Required)
- `category` (String, 100, Required)
- `estimatedDuration` (Integer, Required)
- `isPublished` (Boolean, Optional, Default: false)
- `price` (Integer, Optional, Default: 0)
- `thumbnailUrl` (String, 500, Optional)

### 3. **Lessons Collection**

- **Collection ID**: `lessons`
- **Name**: `Lessons`
- **Permissions**:
  - Read: `read("any")`
  - Write: `write("instructors")`

**Attributes:**

- `courseId` (String, 50, Required)
- `title` (String, 255, Required)
- `content` (String, 10000, Required)
- `order` (Integer, Required)
- `estimatedDuration` (Integer, Required)
- `videoUrl` (String, 500, Optional)
- `isPublished` (Boolean, Optional, Default: false)

### 4. **Progress Collection**

- **Collection ID**: `progress`
- **Name**: `Progress`
- **Permissions**:
  - Read: `read("users")`
  - Write: `write("users")`

**Attributes:**

- `userId` (String, 50, Required)
- `courseId` (String, 50, Required)
- `lessonId` (String, 50, Optional)
- `overallProgress` (Float, Optional, Default: 0)
- `totalTimeSpent` (Integer, Optional, Default: 0)
- `completedLessons` (String, 1000, Optional) - JSON array
- `lastAccessed` (DateTime, Optional)

### 5. **Course Purchases Collection**

- **Collection ID**: `course_purchases`
- **Name**: `Course Purchases`
- **Permissions**:
  - Read: `read("users")`
  - Write: `write("users")`

**Attributes:**

- `userId` (String, 50, Required)
- `courseId` (String, 50, Required)
- `purchasedAt` (DateTime, Required)
- `paymentData` (String, 2000, Optional) - JSON string
- `status` (String, 50, Required, Default: "active")
- `expiresAt` (DateTime, Optional)

## 📁 **Storage Buckets to Create**

### 1. **Course Content Bucket**

- **Bucket ID**: `course-content`
- **Name**: `Course Content`
- **Permissions**:
  - Read: `read("any")`
  - Write: `write("instructors")`
- **File Security**: Enabled
- **Maximum File Size**: 52,428,800 bytes (50MB)
- **Allowed Extensions**: `jpg,jpeg,png,gif,webp,mp4,webm,pdf,txt,md`

### 2. **User Uploads Bucket**

- **Bucket ID**: `user-uploads`
- **Name**: `User Uploads`
- **Permissions**:
  - Read: `read("users")`
  - Write: `write("users")`
- **File Security**: Enabled
- **Maximum File Size**: 10,485,760 bytes (10MB)
- **Allowed Extensions**: `jpg,jpeg,png,gif,webp,pdf,txt,js,ts,py,java,cpp`

## 🔧 **Step-by-Step Instructions**

### Creating Collections:

1. Go to your Appwrite console: https://fra.cloud.appwrite.io/console
2. Navigate to **Databases** → **VybeCoding** (683b231d003c1c558e20)
3. Click **"Create Collection"**
4. Enter the Collection ID and Name
5. Set the permissions as specified
6. Click **"Create"**
7. Add attributes one by one using the **"Create Attribute"** button

### Creating Storage Buckets:

1. Navigate to **Storage** in your Appwrite console
2. Click **"Create Bucket"**
3. Enter the Bucket ID and Name
4. Set permissions and file restrictions as specified
5. Click **"Create"**

## ✅ **Verification Checklist**

After creating all collections and buckets, verify:

- [ ] 5 Collections created with correct IDs
- [ ] All attributes added with correct types and constraints
- [ ] Permissions set correctly for each collection
- [ ] 2 Storage buckets created with correct settings
- [ ] File size limits and extensions configured

## 🚀 **Next Steps**

Once collections are created:

1. **Test the platform**: Visit http://localhost:5174
2. **Create Stripe products**: Use the Stripe dashboard to create products
3. **Set up webhook**: Configure Stripe webhook endpoint
4. **Test payments**: Try the pricing page and payment flow

Your VybeCoding.ai platform will be fully functional! 🎉
