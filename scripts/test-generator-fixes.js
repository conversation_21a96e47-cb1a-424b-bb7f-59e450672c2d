/**
 * Test Script for Generator UX Fixes
 * STORY-GEN-002: Validate all generator issues are resolved
 * BMAD Method: Full Stack Dev (James) - Testing & Validation
 */

const BASE_URL = 'http://localhost:5173';

async function testGeneratorFixes() {
  console.log('🧪 TESTING GENERATOR UX FIXES');
  console.log('📋 STORY-GEN-002: Generator UX Issues & Progress Display Fixes');
  console.log('===============================================');

  const results = [];

  try {
    // Test 1: Configuration Card Transition
    console.log('\n🔍 Test 1: Configuration Card Transition');
    const response = await fetch(`${BASE_URL}/generator`);
    const html = await response.text();
    
    // Check if progress interface exists
    if (html.includes('Agents Collaborating') && html.includes('Generation Status')) {
      results.push('✅ PASS: Progress interface exists in HTML');
    } else {
      results.push('❌ FAIL: Progress interface missing from HTML');
    }

    // Test 2: Timestamp Fix
    console.log('\n🔍 Test 2: Timestamp Implementation');
    if (html.includes('new Date().toLocaleString()') || html.includes('generated_at')) {
      results.push('✅ PASS: Real timestamp implementation found');
    } else {
      results.push('❌ FAIL: Still using "Just now" fallback');
    }

    // Test 3: Autonomous Generation API
    console.log('\n🔍 Test 3: Autonomous Generation API');
    const testPayload = {
      url: 'https://futurism.com/problem-vibe-coding',
      prompt: 'AI Development Platform',
      outputType: 'vybe_qube'
    };

    const genResponse = await fetch(`${BASE_URL}/api/autonomous/generate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(testPayload)
    });

    if (genResponse.ok) {
      const genResult = await genResponse.json();
      if (genResult.id && genResult.status === 'initiated') {
        results.push('✅ PASS: Autonomous generation API responds correctly');
        
        // Test 4: Generation Status Polling
        console.log('\n🔍 Test 4: Generation Status Polling');
        await new Promise(resolve => setTimeout(resolve, 3000)); // Wait 3 seconds
        
        const statusResponse = await fetch(`${BASE_URL}/api/autonomous/status/${genResult.id}`);
        if (statusResponse.ok) {
          const statusResult = await statusResponse.json();
          if (statusResult.status && statusResult.phases) {
            results.push('✅ PASS: Generation status polling works');
            
            // Check for proper timestamp in result
            if (statusResult.result && statusResult.result.generated_at) {
              results.push('✅ PASS: Generated content includes timestamp');
            } else {
              results.push('⚠️  WARN: Generated content timestamp not yet available');
            }
          } else {
            results.push('❌ FAIL: Generation status polling incomplete');
          }
        } else {
          results.push('❌ FAIL: Generation status polling failed');
        }
      } else {
        results.push('❌ FAIL: Autonomous generation API response invalid');
      }
    } else {
      results.push('❌ FAIL: Autonomous generation API not responding');
    }

    // Test 5: WebSocket Service
    console.log('\n🔍 Test 5: WebSocket Service Availability');
    try {
      const wsResponse = await fetch(`${BASE_URL}/api/observatory/status`);
      if (wsResponse.ok) {
        const wsResult = await wsResponse.json();
        if (wsResult.status) {
          results.push('✅ PASS: Observatory WebSocket service available');
        } else {
          results.push('⚠️  WARN: Observatory WebSocket service status unknown');
        }
      } else {
        results.push('⚠️  WARN: Observatory WebSocket service not responding');
      }
    } catch (error) {
      results.push('⚠️  WARN: Observatory WebSocket service check failed');
    }

    // Test 6: Protocol Services Health
    console.log('\n🔍 Test 6: Protocol Services Health Check');
    try {
      const healthResponse = await fetch(`${BASE_URL}/api/services/health`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          serviceName: 'ollama',
          port: 11434,
          endpoint: '/'
        })
      });

      if (healthResponse.ok) {
        const healthResult = await healthResponse.json();
        if (typeof healthResult.healthy === 'boolean') {
          results.push('✅ PASS: Health check API returns proper boolean status');
        } else {
          results.push('❌ FAIL: Health check API returns invalid status');
        }
      } else {
        results.push('❌ FAIL: Health check API not responding');
      }
    } catch (error) {
      results.push('❌ FAIL: Health check API error');
    }

  } catch (error) {
    console.error('❌ Test execution error:', error);
    results.push(`❌ FAIL: Test execution error - ${error.message}`);
  }

  // Summary
  console.log('\n📊 TEST RESULTS SUMMARY');
  console.log('========================');
  
  const passCount = results.filter(r => r.startsWith('✅')).length;
  const failCount = results.filter(r => r.startsWith('❌')).length;
  const warnCount = results.filter(r => r.startsWith('⚠️')).length;

  results.forEach(result => console.log(result));

  console.log('\n📈 Statistics:');
  console.log(`  ✅ Passed: ${passCount}`);
  console.log(`  ❌ Failed: ${failCount}`);
  console.log(`  ⚠️  Warnings: ${warnCount}`);

  if (failCount === 0) {
    console.log('\n🎉 ALL TESTS PASSED! Generator UX fixes are working correctly.');
    return true;
  } else {
    console.log('\n⚠️  Some tests failed. Review the issues above.');
    return false;
  }
}

// Run the tests
testGeneratorFixes().then(success => {
  console.log('\n🔄 BMAD Method Context:');
  console.log('Current Phase: Full Stack Dev (James) - Testing & Validation');
  console.log('Story: STORY-GEN-002 - Generator UX Issues & Progress Display Fixes');
  
  if (success) {
    console.log('Status: ✅ READY FOR PRODUCT OWNER VALIDATION');
    console.log('Next: python3 method/bmad/bmad_orchestrator.py "*product-owner-aka-po"');
  } else {
    console.log('Status: 🔧 REQUIRES ADDITIONAL FIXES');
    console.log('Next: Continue development and re-test');
  }
}).catch(error => {
  console.error('❌ Test runner error:', error);
});
