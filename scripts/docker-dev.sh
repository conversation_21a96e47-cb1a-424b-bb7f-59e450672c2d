#!/bin/bash

# VybeCoding.ai Docker Development Workflow Script
# Streamlined commands for Docker-based development

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Detect Docker Compose command
if docker compose version &> /dev/null; then
    COMPOSE_CMD="docker compose"
elif command -v docker-compose &> /dev/null; then
    COMPOSE_CMD="docker-compose"
else
    print_error "Docker Compose is not available"
    exit 1
fi

# Function to start development environment
start_dev() {
    print_status "Starting VybeCoding.ai development environment..."
    
    # Create .env.development if it doesn't exist
    if [ ! -f .env.development ]; then
        print_status "Creating development environment file..."
        cat > .env.development << EOF
NODE_ENV=development
VITE_ENVIRONMENT=development
VITE_HOST=0.0.0.0
VITE_PORT=5173
VITE_APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
VITE_APPWRITE_PROJECT_ID=vybecoding-dev
VITE_APPWRITE_DATABASE_ID=main
DEBUG=true
LOG_LEVEL=debug
EOF
    fi
    
    # Start development containers
    $COMPOSE_CMD -f docker-compose.dev.yml up -d
    
    print_success "Development environment started!"
    print_status "🚀 Application: http://localhost:5173"
    print_status "📊 Logs: $COMPOSE_CMD -f docker-compose.dev.yml logs -f"
    print_status "🛑 Stop: $0 stop"
}

# Function to stop development environment
stop_dev() {
    print_status "Stopping development environment..."
    $COMPOSE_CMD -f docker-compose.dev.yml down
    print_success "Development environment stopped"
}

# Function to restart development environment
restart_dev() {
    print_status "Restarting development environment..."
    stop_dev
    start_dev
}

# Function to view logs
logs() {
    local service=${1:-vybecoding-dev}
    print_status "Showing logs for $service..."
    $COMPOSE_CMD -f docker-compose.dev.yml logs -f $service
}

# Function to execute commands in development container
exec_dev() {
    if [ $# -eq 0 ]; then
        print_status "Opening bash shell in development container..."
        $COMPOSE_CMD -f docker-compose.dev.yml exec vybecoding-dev bash
    else
        print_status "Executing command in development container: $*"
        $COMPOSE_CMD -f docker-compose.dev.yml exec vybecoding-dev "$@"
    fi
}

# Function to install npm packages
npm_install() {
    print_status "Installing npm packages in development container..."
    $COMPOSE_CMD -f docker-compose.dev.yml exec vybecoding-dev npm install
    print_success "Packages installed successfully"
}

# Function to run npm scripts
npm_run() {
    local script=${1:-dev}
    print_status "Running npm script: $script"
    $COMPOSE_CMD -f docker-compose.dev.yml exec vybecoding-dev npm run $script
}

# Function to run tests
test() {
    print_status "Running tests in development container..."
    $COMPOSE_CMD -f docker-compose.dev.yml exec vybecoding-dev npm run test
}

# Function to run linting
lint() {
    print_status "Running ESLint in development container..."
    $COMPOSE_CMD -f docker-compose.dev.yml exec vybecoding-dev npm run lint
}

# Function to format code
format() {
    print_status "Formatting code with Prettier..."
    $COMPOSE_CMD -f docker-compose.dev.yml exec vybecoding-dev npm run format
}

# Function to build for production
build() {
    print_status "Building application for production..."
    $COMPOSE_CMD -f docker-compose.dev.yml exec vybecoding-dev npm run build
    print_success "Build completed"
}

# Function to check container status
status() {
    print_status "Container status:"
    $COMPOSE_CMD -f docker-compose.dev.yml ps
    
    print_status "\nContainer health:"
    docker ps --filter "name=vybecoding-dev" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
}

# Function to rebuild development image
rebuild() {
    print_status "Rebuilding development image..."
    $COMPOSE_CMD -f docker-compose.dev.yml build --no-cache
    print_success "Development image rebuilt"
}

# Function to clean up development environment
clean() {
    print_status "Cleaning up development environment..."
    
    # Stop containers
    $COMPOSE_CMD -f docker-compose.dev.yml down
    
    # Remove volumes
    $COMPOSE_CMD -f docker-compose.dev.yml down -v
    
    # Remove development image
    docker rmi vybecoding:dev 2>/dev/null || true
    
    print_success "Development environment cleaned"
}

# Function to show development environment info
info() {
    echo "VybeCoding.ai Development Environment Information"
    echo "================================================"
    echo ""
    echo "🐳 Docker Compose: $COMPOSE_CMD"
    echo "📁 Project Directory: $(pwd)"
    echo "🔧 Development Port: 5173"
    echo "🔥 HMR Port: 24678"
    echo ""
    echo "📊 Container Status:"
    $COMPOSE_CMD -f docker-compose.dev.yml ps 2>/dev/null || echo "No containers running"
    echo ""
    echo "💾 Volumes:"
    docker volume ls --filter "name=vybecoding" 2>/dev/null || echo "No volumes found"
}

# Function to show help
show_help() {
    echo "VybeCoding.ai Docker Development Workflow"
    echo "========================================"
    echo ""
    echo "Usage: $0 [COMMAND] [ARGS...]"
    echo ""
    echo "Environment Commands:"
    echo "  start              - Start development environment"
    echo "  stop               - Stop development environment"
    echo "  restart            - Restart development environment"
    echo "  status             - Show container status"
    echo "  info               - Show environment information"
    echo "  rebuild            - Rebuild development image"
    echo "  clean              - Clean up development environment"
    echo ""
    echo "Development Commands:"
    echo "  logs [service]     - View container logs"
    echo "  exec [command]     - Execute command in container"
    echo "  shell              - Open bash shell in container"
    echo "  install            - Install npm packages"
    echo "  run [script]       - Run npm script"
    echo ""
    echo "Code Quality Commands:"
    echo "  test               - Run test suite"
    echo "  lint               - Run ESLint"
    echo "  format             - Format code with Prettier"
    echo "  build              - Build for production"
    echo ""
    echo "Examples:"
    echo "  $0 start           - Start development server"
    echo "  $0 logs            - View application logs"
    echo "  $0 exec npm test   - Run tests"
    echo "  $0 shell           - Open container shell"
    echo "  $0 run build       - Build application"
}

# Main script logic
main() {
    case ${1:-help} in
        "start"|"up")
            start_dev
            ;;
        "stop"|"down")
            stop_dev
            ;;
        "restart")
            restart_dev
            ;;
        "logs")
            logs ${2:-vybecoding-dev}
            ;;
        "exec")
            shift
            exec_dev "$@"
            ;;
        "shell"|"bash")
            exec_dev
            ;;
        "install")
            npm_install
            ;;
        "run")
            npm_run ${2:-dev}
            ;;
        "test")
            test
            ;;
        "lint")
            lint
            ;;
        "format")
            format
            ;;
        "build")
            build
            ;;
        "status"|"ps")
            status
            ;;
        "rebuild")
            rebuild
            ;;
        "clean")
            clean
            ;;
        "info")
            info
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# Run main function with all arguments
main "$@"
