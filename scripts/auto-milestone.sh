#!/bin/bash

# Auto-Milestone Git System for VybeCoding.ai
# Automatically creates milestone branches and pushes changes

set -e

# Configuration
MILESTONE_PREFIX="milestone"
MAIN_BRANCH="main"
REMOTE="origin"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[AUTO-MILESTONE]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to get next milestone number
get_next_milestone() {
    local last_milestone=$(git branch -r | grep "$MILESTONE_PREFIX" | sed "s/.*$MILESTONE_PREFIX-//" | sort -n | tail -1)
    if [ -z "$last_milestone" ]; then
        echo "001"
    else
        printf "%03d" $((10#$last_milestone + 1))
    fi
}

# Function to create milestone
create_milestone() {
    local milestone_type="$1"
    local description="$2"
    local force="$3"
    
    # Validate inputs
    if [ -z "$milestone_type" ] || [ -z "$description" ]; then
        print_error "Usage: create_milestone <type> <description> [force]"
        print_error "Types: story, feature, bugfix, release, hotfix"
        exit 1
    fi
    
    # Check if we're in a git repository
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        print_error "Not in a git repository"
        exit 1
    fi
    
    # Check for uncommitted changes
    if [ -n "$(git status --porcelain)" ] && [ "$force" != "force" ]; then
        print_warning "You have uncommitted changes:"
        git status --short
        echo
        read -p "Do you want to commit these changes? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            git add .
            git commit -m "Auto-commit: $description"
        else
            print_error "Please commit or stash your changes first, or use 'force' flag"
            exit 1
        fi
    fi
    
    # Get milestone number
    local milestone_num=$(get_next_milestone)
    local branch_name="$MILESTONE_PREFIX-$milestone_num-$milestone_type"
    local tag_name="v$milestone_num-$milestone_type"
    
    print_status "Creating milestone: $branch_name"
    print_status "Description: $description"
    
    # Create and switch to milestone branch
    git checkout -b "$branch_name"
    
    # Create milestone marker file
    local milestone_file=".milestones/milestone-$milestone_num.md"
    mkdir -p .milestones
    
    cat > "$milestone_file" << EOF
# Milestone $milestone_num: $milestone_type

**Date:** $(date -u +"%Y-%m-%d %H:%M:%S UTC")
**Type:** $milestone_type
**Description:** $description
**Branch:** $branch_name
**Commit:** $(git rev-parse HEAD)

## Changes in this Milestone

$(git log --oneline -10)

## Files Changed

$(git diff --name-only HEAD~5..HEAD 2>/dev/null || echo "Initial commit")

## Rollback Instructions

To rollback to this milestone:
\`\`\`bash
git checkout $branch_name
# or
git reset --hard $tag_name
\`\`\`

## Next Steps

- [ ] Test milestone functionality
- [ ] Update documentation
- [ ] Deploy to staging (if applicable)
- [ ] Create next milestone

---
*Generated by auto-milestone.sh*
EOF
    
    # Add and commit milestone file
    git add "$milestone_file"
    git commit -m "Milestone $milestone_num: $description

Type: $milestone_type
Auto-generated milestone marker

[skip ci]"
    
    # Create tag
    git tag -a "$tag_name" -m "Milestone $milestone_num: $description"
    
    # Push branch and tag
    print_status "Pushing to remote..."
    git push "$REMOTE" "$branch_name"
    git push "$REMOTE" "$tag_name"
    
    # Switch back to main and merge
    git checkout "$MAIN_BRANCH"
    git merge "$branch_name" --no-ff -m "Merge milestone $milestone_num: $description"
    git push "$REMOTE" "$MAIN_BRANCH"
    
    print_success "Milestone $milestone_num created successfully!"
    print_success "Branch: $branch_name"
    print_success "Tag: $tag_name"
    print_success "Milestone file: $milestone_file"
    
    # Show rollback instructions
    echo
    print_status "Rollback options:"
    echo "  1. Rollback to this milestone: git checkout $branch_name"
    echo "  2. Rollback with reset: git reset --hard $tag_name"
    echo "  3. View milestone: cat $milestone_file"
}

# Function to list milestones
list_milestones() {
    print_status "Available milestones:"
    echo
    
    # List milestone files
    if [ -d ".milestones" ]; then
        for file in .milestones/milestone-*.md; do
            if [ -f "$file" ]; then
                local milestone_num=$(basename "$file" .md | sed 's/milestone-//')
                local description=$(grep "^**Description:**" "$file" | sed 's/\*\*Description:\*\* //')
                local date=$(grep "^**Date:**" "$file" | sed 's/\*\*Date:\*\* //')
                echo "  $milestone_num: $description ($date)"
            fi
        done
    else
        print_warning "No milestones found"
    fi
    
    echo
    print_status "Git branches:"
    git branch -r | grep "$MILESTONE_PREFIX" | sed 's/origin\//  /'
    
    echo
    print_status "Git tags:"
    git tag | grep "^v[0-9]" | tail -10
}

# Function to rollback to milestone
rollback_milestone() {
    local milestone_ref="$1"
    local force="$2"
    
    if [ -z "$milestone_ref" ]; then
        print_error "Usage: rollback_milestone <milestone_number|branch|tag> [force]"
        exit 1
    fi
    
    # Check for uncommitted changes
    if [ -n "$(git status --porcelain)" ] && [ "$force" != "force" ]; then
        print_error "You have uncommitted changes. Commit them or use 'force' flag"
        git status --short
        exit 1
    fi
    
    # Determine what type of reference this is
    if [[ "$milestone_ref" =~ ^[0-9]+$ ]]; then
        # It's a milestone number
        local milestone_num=$(printf "%03d" "$milestone_ref")
        local branch_name="$MILESTONE_PREFIX-$milestone_num"
        local tag_name="v$milestone_num"
        
        # Try branch first, then tag
        if git show-ref --verify --quiet "refs/remotes/$REMOTE/$branch_name"; then
            milestone_ref="$REMOTE/$branch_name"
        elif git show-ref --verify --quiet "refs/tags/$tag_name"; then
            milestone_ref="$tag_name"
        else
            print_error "Milestone $milestone_num not found"
            exit 1
        fi
    fi
    
    print_warning "Rolling back to: $milestone_ref"
    print_warning "This will reset your current branch to the milestone state"
    
    if [ "$force" != "force" ]; then
        read -p "Are you sure? (y/n): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_status "Rollback cancelled"
            exit 0
        fi
    fi
    
    # Perform rollback
    git reset --hard "$milestone_ref"
    
    print_success "Rolled back to: $milestone_ref"
    print_status "Current commit: $(git rev-parse HEAD)"
}

# Main script logic
case "${1:-help}" in
    "create")
        create_milestone "$2" "$3" "$4"
        ;;
    "list")
        list_milestones
        ;;
    "rollback")
        rollback_milestone "$2" "$3"
        ;;
    "help"|*)
        echo "Auto-Milestone Git System for VybeCoding.ai"
        echo
        echo "Usage:"
        echo "  $0 create <type> <description> [force]"
        echo "  $0 list"
        echo "  $0 rollback <milestone_number|branch|tag> [force]"
        echo
        echo "Types:"
        echo "  story    - Completed user story (e.g., STORY-1-001)"
        echo "  feature  - New feature implementation"
        echo "  bugfix   - Bug fix or issue resolution"
        echo "  release  - Release candidate or version"
        echo "  hotfix   - Critical production fix"
        echo
        echo "Examples:"
        echo "  $0 create story 'STORY-1-001 Intelligent Course Content Access'"
        echo "  $0 create feature 'Multi-LLM AI service implementation'"
        echo "  $0 list"
        echo "  $0 rollback 001"
        echo "  $0 rollback milestone-001-story"
        ;;
esac
