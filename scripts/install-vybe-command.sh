#!/bin/bash
"""
🚀 INSTALL GLOBAL VYBE COMMAND
Creates a global 'vybe' command that runs npm run dev:host
"""

set -e

echo "🚀 Installing Global 'vybe' Command..."
echo "===================================="

# Get the current project directory
PROJECT_DIR=$(pwd)
echo "📁 Project Directory: $PROJECT_DIR"

# Create the vybe command script
VYBE_SCRIPT="/usr/local/bin/vybe"

echo "📝 Creating global vybe command..."

# Create the vybe command with sudo
sudo tee "$VYBE_SCRIPT" > /dev/null << EOF
#!/bin/bash
"""
🚀 VYBE - VybeCoding.ai Development Command
Runs comprehensive protected development server
"""

# Colors for output
GREEN='\033[0;32m'
CYAN='\033[0;36m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "\${CYAN}🚀 VybeCoding.ai Development Server\${NC}"
echo -e "\${CYAN}===================================\${NC}"

# Check if we're in a VybeCoding project directory
if [ ! -f "package.json" ] || ! grep -q "vybecoding" package.json 2>/dev/null; then
    echo -e "\${RED}❌ Error: Not in a VybeCoding.ai project directory\${NC}"
    echo -e "\${YELLOW}💡 Navigate to your VybeCoding.ai project and try again\${NC}"
    echo ""
    echo -e "\${CYAN}Example:\${NC}"
    echo -e "  cd /path/to/vybecoding"
    echo -e "  vybe"
    exit 1
fi

# Check if npm is available
if ! command -v npm >/dev/null 2>&1; then
    echo -e "\${RED}❌ Error: npm not found\${NC}"
    echo -e "\${YELLOW}💡 Please install Node.js and npm first\${NC}"
    exit 1
fi

# Check if dev:host script exists
if ! npm run --silent 2>/dev/null | grep -q "dev:host"; then
    echo -e "\${RED}❌ Error: dev:host script not found in package.json\${NC}"
    echo -e "\${YELLOW}💡 Make sure you're in the correct VybeCoding.ai project\${NC}"
    exit 1
fi

echo -e "\${GREEN}✅ VybeCoding.ai project detected\${NC}"
echo -e "\${GREEN}✅ npm and dev:host script found\${NC}"
echo ""

# Show what will be started
echo -e "\${CYAN}🛡️ Starting with comprehensive protection:\${NC}"
echo -e "   ✅ Anti-simulation validation"
echo -e "   ✅ Real-time violation monitoring"
echo -e "   ✅ MAS Observatory services"
echo -e "   ✅ Docker container management"
echo -e "   ✅ Network-accessible dev server"
echo ""

# Run the dev:host command
echo -e "\${GREEN}🚀 Executing: npm run dev:host\${NC}"
echo ""

exec npm run dev:host
EOF

# Make the script executable
sudo chmod +x "$VYBE_SCRIPT"

echo "✅ Global vybe command created at: $VYBE_SCRIPT"
echo ""

# Test the command
echo "🧪 Testing the vybe command..."
if command -v vybe >/dev/null 2>&1; then
    echo "✅ vybe command is available globally"
else
    echo "❌ vybe command not found in PATH"
    echo "💡 You may need to restart your terminal or run: source ~/.bashrc"
fi

echo ""
echo "🎉 Installation Complete!"
echo ""
echo "📋 Usage:"
echo "   1. Navigate to any VybeCoding.ai project directory"
echo "   2. Type: vybe"
echo "   3. Enjoy comprehensive protected development!"
echo ""
echo "🌐 The server will be accessible from any PC on your network"
echo "🛡️ Full anti-simulation protection will be active"
echo ""

# Add to shell profiles for persistence
echo "📝 Adding to shell profiles for persistence..."

# Add to .bashrc if it exists
if [ -f "$HOME/.bashrc" ]; then
    if ! grep -q "# VybeCoding.ai global command" "$HOME/.bashrc"; then
        echo "" >> "$HOME/.bashrc"
        echo "# VybeCoding.ai global command" >> "$HOME/.bashrc"
        echo "export PATH=\"/usr/local/bin:\$PATH\"" >> "$HOME/.bashrc"
        echo "✅ Added to ~/.bashrc"
    fi
fi

# Add to .zshrc if it exists
if [ -f "$HOME/.zshrc" ]; then
    if ! grep -q "# VybeCoding.ai global command" "$HOME/.zshrc"; then
        echo "" >> "$HOME/.zshrc"
        echo "# VybeCoding.ai global command" >> "$HOME/.zshrc"
        echo "export PATH=\"/usr/local/bin:\$PATH\"" >> "$HOME/.zshrc"
        echo "✅ Added to ~/.zshrc"
    fi
fi

echo ""
echo "🎯 Quick Start:"
echo "   cd /path/to/your/vybecoding/project"
echo "   vybe"
echo ""
echo "🔄 If 'vybe' command not found, restart terminal or run:"
echo "   source ~/.bashrc"
