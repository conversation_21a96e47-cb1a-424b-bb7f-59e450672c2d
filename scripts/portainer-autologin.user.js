// ==UserScript==
// @name         VybeCoding.ai Portainer Auto Login
// @namespace    http://vybecoding.ai/
// @version      2.0
// @description  Auto-login to Portainer and redirect to containers page for VybeCoding.ai development
// @match        http://localhost:9000/*
// @match        https://localhost:9443/*
// @grant        none
// <AUTHOR> Team
// @updateURL    https://raw.githubusercontent.com/your-repo/vybecoding/main/scripts/portainer-autologin.user.js
// @downloadURL  https://raw.githubusercontent.com/your-repo/vybecoding/main/scripts/portainer-autologin.user.js
// ==/UserScript==

(function () {
  'use strict';

  // Configuration - Update these if needed
  const CONFIG = {
    username: 'admin',
    password: 'S!llyK!tty!!',
    containersPage: 'http://localhost:9000/#!/3/docker/containers',
    debugMode: true,
  };

  // Utility functions
  function log(message, type = 'info') {
    if (!CONFIG.debugMode) return;

    const styles = {
      info: 'color: #2196F3; font-weight: bold;',
      success: 'color: #4CAF50; font-weight: bold;',
      warning: 'color: #FF9800; font-weight: bold;',
      error: 'color: #F44336; font-weight: bold;',
    };

    console.log(
      `%c🚀 VybeCoding.ai Portainer: ${message}`,
      styles[type] || styles.info
    );
  }

  function waitForElement(selector, timeout = 10000) {
    return new Promise((resolve, reject) => {
      const element = document.querySelector(selector);
      if (element) {
        resolve(element);
        return;
      }

      const observer = new MutationObserver(() => {
        const element = document.querySelector(selector);
        if (element) {
          observer.disconnect();
          resolve(element);
        }
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true,
      });

      setTimeout(() => {
        observer.disconnect();
        reject(new Error(`Element ${selector} not found within ${timeout}ms`));
      }, timeout);
    });
  }

  // Auto-login functionality
  async function attemptAutoLogin() {
    try {
      log('Attempting auto-login...');

      // Wait for login form elements
      const userInput = await waitForElement(
        'input[name="username"], input[data-cy="usernameInput"], input[placeholder*="sername" i]'
      );
      const passInput = await waitForElement(
        'input[name="password"], input[data-cy="passwordInput"], input[type="password"]'
      );
      const loginBtn = await waitForElement(
        'button[type="submit"], button[data-cy="loginButton"], button:contains("Login")'
      );

      if (userInput && passInput && loginBtn) {
        log('Login form found, filling credentials...');

        // Clear existing values
        userInput.value = '';
        passInput.value = '';

        // Fill credentials
        userInput.value = CONFIG.username;
        passInput.value = CONFIG.password;

        // Trigger events to ensure form validation
        ['input', 'change', 'keyup'].forEach(eventType => {
          userInput.dispatchEvent(new Event(eventType, { bubbles: true }));
          passInput.dispatchEvent(new Event(eventType, { bubbles: true }));
        });

        // Wait a bit for form validation
        await new Promise(resolve => setTimeout(resolve, 500));

        // Submit form
        loginBtn.click();
        log('Login form submitted!', 'success');

        return true;
      }
    } catch (error) {
      log(`Auto-login failed: ${error.message}`, 'error');
      return false;
    }
  }

  // Redirect to containers page after successful login
  function setupRedirectToContainers() {
    const checkAndRedirect = () => {
      // Check if logged in (JWT token exists)
      const isLoggedIn =
        window.localStorage.getItem('JWT') ||
        window.localStorage.getItem('portainer.JWT') ||
        document.cookie.includes('portainer');

      // Check if we're on home/dashboard page or any non-containers page
      const isOnHomePage =
        window.location.hash === '#!/home' ||
        window.location.hash === '#!/dashboard' ||
        window.location.pathname === '/' ||
        window.location.hash === '#!/';

      // Don't redirect if already on containers page
      const isOnContainersPage =
        window.location.hash.includes('/docker/containers');

      if (isLoggedIn && isOnHomePage && !isOnContainersPage) {
        log('Login successful, redirecting to containers page...', 'success');
        window.location.href = CONFIG.containersPage;
        return true;
      }

      return false;
    };

    // Check immediately
    if (checkAndRedirect()) return;

    // Check periodically for successful login
    const redirectInterval = setInterval(() => {
      if (checkAndRedirect()) {
        clearInterval(redirectInterval);
      }
    }, 1000);

    // Stop checking after 30 seconds
    setTimeout(() => {
      clearInterval(redirectInterval);
      log('Redirect timeout reached', 'warning');
    }, 30000);
  }

  // Initial setup check
  function setupInitialCheck() {
    log('VybeCoding.ai Portainer Auto-Login Script Loaded');
    log(`Current URL: ${window.location.href}`);
    log(`Current Hash: ${window.location.hash}`);

    // Check if already logged in
    const isLoggedIn =
      window.localStorage.getItem('JWT') ||
      window.localStorage.getItem('portainer.JWT');

    if (isLoggedIn) {
      log('Already logged in, checking for redirect...', 'success');
      setupRedirectToContainers();
      return;
    }

    // Check if on login page
    const isLoginPage =
      window.location.hash.includes('init/admin') ||
      window.location.hash.includes('auth') ||
      window.location.pathname.includes('auth') ||
      document.querySelector('input[name="username"], input[type="password"]');

    if (isLoginPage) {
      log('Login page detected, setting up auto-login...');

      // Try immediate login
      attemptAutoLogin().then(success => {
        if (success) {
          setupRedirectToContainers();
        }
      });
    }
  }

  // Enhanced page change detection
  function setupPageChangeDetection() {
    let lastUrl = location.href;

    new MutationObserver(() => {
      const url = location.href;
      if (url !== lastUrl) {
        lastUrl = url;
        log(`Page changed to: ${url}`);

        // Re-run setup on page change
        setTimeout(setupInitialCheck, 500);
      }
    }).observe(document, { subtree: true, childList: true });

    // Also listen for hash changes
    window.addEventListener('hashchange', () => {
      log(`Hash changed to: ${window.location.hash}`);
      setTimeout(setupInitialCheck, 500);
    });
  }

  // Main initialization
  function init() {
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', init);
      return;
    }

    log('Initializing VybeCoding.ai Portainer Auto-Login...', 'info');

    // Setup page change detection
    setupPageChangeDetection();

    // Run initial check
    setupInitialCheck();

    // Add visual indicator
    const indicator = document.createElement('div');
    indicator.innerHTML = '🚀 VybeCoding.ai Auto-Login Active';
    indicator.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: #2196F3;
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 10000;
            font-family: monospace;
        `;
    document.body.appendChild(indicator);

    // Remove indicator after 3 seconds
    setTimeout(() => {
      if (indicator.parentNode) {
        indicator.parentNode.removeChild(indicator);
      }
    }, 3000);
  }

  // Start the script
  init();
})();
