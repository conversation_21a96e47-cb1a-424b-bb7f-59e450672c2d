#!/usr/bin/env node

/**
 * Create Sample Vybe Qubes Data
 * Fixes the "Unable to load Vybe Qubes" error by adding sample data
 */

import { Client, Databases, ID } from 'node-appwrite';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const client = new Client()
  .setEndpoint(
    process.env.VITE_APPWRITE_ENDPOINT || 'https://fra.cloud.appwrite.io/v1'
  )
  .setProject(process.env.VITE_APPWRITE_PROJECT_ID || '683b200e00153d705da3')
  .setKey(
    process.env.APPWRITE_API_KEY ||
      'standard_4df3f240d8fb19f2457fa424db3fdc74caa350a3a32502d2f3d2b07497dda5774c48561544854c472e8ad506cc976e97b4e5f701a3321439bbef2649b4057239b3c633cef8d2ec4f021440a9510f3ad1bc1da3c8f4c44622d1bd6c468ea920a2efc7896e0adb8ae30c3e4bb7e62db7d9d63572caac80a8a9dd396b147357aca6'
  );

const databases = new Databases(client);
const databaseId =
  process.env.VITE_APPWRITE_DATABASE_ID || '683b231d003c1c558e20';

const realVybeQubeExamples = [
  {
    userId: 'vybecoding-platform',
    userName: 'VybeCoding.ai Platform',
    title: 'VybeCoding.ai - Enterprise AI Development Platform',
    description:
      'Production-ready AI education platform built with the Vybe Method. Features Multi-Agent System with 7 specialized AI agents, 100% FOSS technology stack, and enterprise-grade architecture. Demonstrates real-world application of BMAD Method V3 enhanced with autonomous systems.',
    category: 'Educational Platform',
    websiteUrl: 'https://vybecoding.ai',
    sourceCodeUrl: 'https://github.com/Hiram-Ducky/VybeCoding.ai',
    previewImageUrl:
      'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=800&h=400&fit=crop',
    technologies: ['SvelteKit', 'TypeScript', 'Appwrite.io', 'CrewAI', 'AutoGen'],
    vybeMethodUsed: [
      'BMAD Method V3',
      'Multi-Agent Systems',
      'Enterprise Architecture',
      'FOSS Implementation',
    ],
    learningOutcomes: [
      'MAS Coordination',
      'Enterprise Development',
      'FOSS Architecture',
      'Production Deployment',
    ],
    revenueModel: ['Educational Platform', 'Subscription Model'],
    monthlyRevenue: 0, // Platform in development
    createdAt: new Date('2025-06-04').toISOString(),
    updatedAt: new Date('2025-06-04').toISOString(),
  },
  {
    userId: 'bmad-method-demo',
    userName: 'BMAD Method Showcase',
    title: 'BMAD Method V3 - Systematic Development Framework',
    description:
      'Demonstration of the proven BMAD Method V3 framework used in VybeCoding.ai development. Shows systematic approach with 48 completed milestones, comprehensive documentation, and enterprise-grade quality assurance. Real implementation exceeding 160% of original goals.',
    category: 'Development Framework',
    websiteUrl: 'https://github.com/Hiram-Ducky/VybeCoding.ai/tree/main/method/bmad',
    sourceCodeUrl: 'https://github.com/Hiram-Ducky/VybeCoding.ai/tree/main/method',
    previewImageUrl:
      'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=400&fit=crop',
    technologies: ['Python', 'Markdown', 'Git', 'Documentation', 'Quality Assurance'],
    vybeMethodUsed: [
      'Systematic Development',
      'Quality Gates',
      'Documentation-Driven',
      'Milestone Management',
    ],
    learningOutcomes: [
      'Project Management',
      'Quality Assurance',
      'Documentation',
      'Systematic Development',
    ],
    revenueModel: ['Educational Framework', 'Open Source'],
    monthlyRevenue: 0, // Open source framework
    createdAt: new Date('2025-06-04').toISOString(),
    updatedAt: new Date('2025-06-04').toISOString(),
  },
  {
    userId: 'mas-system-demo',
    userName: 'Multi-Agent System',
    title: 'CrewAI + AutoGen Multi-Agent Coordination',
    description:
      'Real implementation of Multi-Agent System using CrewAI and AutoGen frameworks. Features 7 specialized AI agents working together for autonomous development, real-time collaboration, and enterprise-grade coordination. Production-ready MAS architecture.',
    category: 'AI Systems',
    websiteUrl: 'https://github.com/Hiram-Ducky/VybeCoding.ai/tree/main/method/vybe',
    sourceCodeUrl: 'https://github.com/Hiram-Ducky/VybeCoding.ai/tree/main/services',
    previewImageUrl:
      'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=800&h=400&fit=crop',
    technologies: ['CrewAI', 'AutoGen', 'Python', 'LangGraph', 'ChromaDB'],
    vybeMethodUsed: [
      'Agent Coordination',
      'Autonomous Systems',
      'Real-time Collaboration',
      'Enterprise Architecture',
    ],
    learningOutcomes: [
      'Multi-Agent Systems',
      'AI Coordination',
      'Autonomous Development',
      'Enterprise MAS',
    ],
    revenueModel: ['Technology Platform', 'Open Source'],
    monthlyRevenue: 0, // Technology demonstration
    createdAt: new Date('2025-06-04').toISOString(),
    updatedAt: new Date('2025-06-04').toISOString(),
  },
  {
    userId: 'foss-stack-demo',
    userName: 'FOSS Technology Stack',
    title: '100% FOSS Enterprise Architecture',
    description:
      'Complete Free and Open Source Software implementation demonstrating enterprise-grade architecture. Features SvelteKit frontend, Appwrite.io backend, local LLM processing, and privacy-first design. No vendor lock-in, complete transparency.',
    category: 'Technology Stack',
    websiteUrl: 'https://github.com/Hiram-Ducky/VybeCoding.ai',
    sourceCodeUrl: 'https://github.com/Hiram-Ducky/VybeCoding.ai/tree/main/docs',
    previewImageUrl:
      'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=800&h=400&fit=crop',
    technologies: ['SvelteKit', 'Appwrite.io', 'TypeScript', 'Ollama', 'Local LLMs'],
    vybeMethodUsed: [
      'FOSS Implementation',
      'Privacy-First Design',
      'Enterprise Architecture',
      'Local Processing',
    ],
    learningOutcomes: [
      'FOSS Development',
      'Privacy Architecture',
      'Local LLM Integration',
      'Enterprise Deployment',
    ],
    revenueModel: ['Open Source', 'Educational Platform'],
    monthlyRevenue: 0, // Open source technology
    createdAt: new Date('2025-06-04').toISOString(),
    updatedAt: new Date('2025-06-04').toISOString(),
  },
  {
    userId: 'milestone-system-demo',
    userName: 'Development Milestones',
    title: 'Automated Milestone Management System',
    description:
      'Production-ready milestone management system with 48 completed milestones, automated rollback capabilities, comprehensive audit trail, and enterprise-grade quality assurance. Demonstrates systematic development approach.',
    category: 'Development Tools',
    websiteUrl: 'https://github.com/Hiram-Ducky/VybeCoding.ai/tree/main/.milestones',
    sourceCodeUrl: 'https://github.com/Hiram-Ducky/VybeCoding.ai/tree/main/scripts',
    previewImageUrl:
      'https://images.unsplash.com/photo-1455390582262-044cdead277a?w=800&h=400&fit=crop',
    technologies: ['Bash', 'Git', 'GitHub Actions', 'Automation', 'Quality Gates'],
    vybeMethodUsed: [
      'Systematic Development',
      'Quality Assurance',
      'Automated Workflows',
      'Audit Trail Management',
    ],
    learningOutcomes: [
      'Project Management',
      'Automation Systems',
      'Quality Gates',
      'Development Workflows',
    ],
    revenueModel: ['Development Framework', 'Open Source'],
    monthlyRevenue: 0, // Development tool
    createdAt: new Date('2025-06-04').toISOString(),
    updatedAt: new Date('2025-06-04').toISOString(),
  },
];

async function createRealExamples() {
  console.log('🚀 Creating real VybeCoding.ai Vybe Qube examples...\n');

  try {
    // Test connection
    console.log('Testing Appwrite connection...');
    await databases.list();
    console.log('✅ Connected to Appwrite successfully\n');

    // Create real Vybe Qube examples
    for (const qube of realVybeQubeExamples) {
      try {
        const document = await databases.createDocument(
          databaseId,
          'user-vybe-qubes',
          ID.unique(),
          qube
        );
        console.log(`✅ Created Vybe Qube: ${qube.title}`);
      } catch (error) {
        if (error.code === 409) {
          console.log(`⚠️  Vybe Qube "${qube.title}" already exists`);
        } else {
          console.error(`❌ Error creating "${qube.title}":`, error.message);
        }
      }
    }

    console.log('\n🎉 Real VybeCoding.ai examples created successfully!');
    console.log('\nNext steps:');
    console.log('1. Refresh the Vybe Qubes page');
    console.log('2. Verify the real platform examples are displaying');
    console.log('3. Explore the actual VybeCoding.ai technology demonstrations');
  } catch (error) {
    console.error('❌ Failed to create real examples:', error);
    process.exit(1);
  }
}

createRealExamples();
