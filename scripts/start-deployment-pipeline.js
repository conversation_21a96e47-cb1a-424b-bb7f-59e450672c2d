#!/usr/bin/env node

/**
 * Enhanced MAS Deployment Pipeline Startup Script
 * Initializes and starts the automated content deployment system
 */

import { spawn } from 'child_process';
import { existsSync } from 'fs';
import { join } from 'path';

console.log('🚀 Starting Enhanced MAS Deployment Pipeline...\n');

// Configuration
const config = {
  projectRoot: process.cwd(),
  deploymentAPI: 'http://localhost:5173/api/deployment',
  masSystemPath: 'method/vybe',
  requiredServices: [
    'SvelteKit Development Server',
    'Enhanced MAS System',
    'Deployment Orchestrator'
  ]
};

// Check prerequisites
function checkPrerequisites() {
  console.log('📋 Checking prerequisites...');
  
  const requiredFiles = [
    'src/lib/services/deployment-orchestrator.ts',
    'src/lib/services/content-deployment.ts',
    'src/routes/api/deployment/+server.ts',
    'method/vybe/enhanced_mas_system.py',
    'method/vybe/vybe_commands.py'
  ];

  const missingFiles = requiredFiles.filter(file => !existsSync(join(config.projectRoot, file)));
  
  if (missingFiles.length > 0) {
    console.error('❌ Missing required files:');
    missingFiles.forEach(file => console.error(`   - ${file}`));
    process.exit(1);
  }

  console.log('✅ All required files present');
}

// Start SvelteKit development server
function startSvelteKitServer() {
  return new Promise((resolve, reject) => {
    console.log('🌐 Starting SvelteKit development server...');
    
    const server = spawn('npm', ['run', 'dev'], {
      cwd: config.projectRoot,
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let serverReady = false;

    server.stdout.on('data', (data) => {
      const output = data.toString();
      console.log(`[SvelteKit] ${output.trim()}`);
      
      if (output.includes('Local:') && !serverReady) {
        serverReady = true;
        console.log('✅ SvelteKit server is ready');
        resolve(server);
      }
    });

    server.stderr.on('data', (data) => {
      console.error(`[SvelteKit Error] ${data.toString().trim()}`);
    });

    server.on('error', (error) => {
      console.error('❌ Failed to start SvelteKit server:', error);
      reject(error);
    });

    // Timeout after 60 seconds
    setTimeout(() => {
      if (!serverReady) {
        console.error('❌ SvelteKit server startup timeout');
        server.kill();
        reject(new Error('Server startup timeout'));
      }
    }, 60000);
  });
}

// Initialize deployment orchestrator
async function initializeDeploymentOrchestrator() {
  console.log('🔧 Initializing deployment orchestrator...');
  
  try {
    // Wait a moment for the server to be fully ready
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    const response = await fetch(`${config.deploymentAPI}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ action: 'start_monitoring' })
    });

    if (response.ok) {
      const result = await response.json();
      if (result.success) {
        console.log('✅ Deployment orchestrator initialized successfully');
        return true;
      } else {
        console.error('❌ Deployment orchestrator initialization failed:', result.error);
        return false;
      }
    } else {
      console.error('❌ Failed to connect to deployment API:', response.status);
      return false;
    }
  } catch (error) {
    console.error('❌ Error initializing deployment orchestrator:', error.message);
    return false;
  }
}

// Setup database collections
async function setupDatabaseCollections() {
  console.log('🗄️ Setting up database collections...');
  
  return new Promise((resolve, reject) => {
    const setup = spawn('node', ['scripts/setup-deployment-collections.js'], {
      cwd: config.projectRoot,
      stdio: ['pipe', 'pipe', 'pipe']
    });

    setup.stdout.on('data', (data) => {
      console.log(`[DB Setup] ${data.toString().trim()}`);
    });

    setup.stderr.on('data', (data) => {
      console.error(`[DB Setup Error] ${data.toString().trim()}`);
    });

    setup.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Database collections setup completed');
        resolve(true);
      } else {
        console.error('❌ Database setup failed with code:', code);
        reject(new Error(`Database setup failed: ${code}`));
      }
    });

    setup.on('error', (error) => {
      console.error('❌ Database setup error:', error);
      reject(error);
    });
  });
}

// Check Enhanced MAS system status
async function checkEnhancedMASStatus() {
  console.log('🤖 Checking Enhanced MAS system status...');
  
  return new Promise((resolve) => {
    const masCheck = spawn('python3', ['method/vybe/vybe_commands.py', 'status'], {
      cwd: config.projectRoot,
      stdio: ['pipe', 'pipe', 'pipe']
    });

    masCheck.stdout.on('data', (data) => {
      console.log(`[Enhanced MAS] ${data.toString().trim()}`);
    });

    masCheck.stderr.on('data', (data) => {
      console.log(`[Enhanced MAS] ${data.toString().trim()}`);
    });

    masCheck.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Enhanced MAS system is operational');
        resolve(true);
      } else {
        console.log('⚠️ Enhanced MAS system may need initialization');
        resolve(false);
      }
    });

    masCheck.on('error', (error) => {
      console.log('⚠️ Enhanced MAS system check failed:', error.message);
      resolve(false);
    });
  });
}

// Display deployment pipeline status
async function displayPipelineStatus() {
  console.log('\n📊 Enhanced MAS Deployment Pipeline Status:');
  console.log('=' .repeat(60));
  
  try {
    const response = await fetch(`${config.deploymentAPI}?action=status`);
    if (response.ok) {
      const status = await response.json();
      if (status.success) {
        console.log(`✅ Deployment Monitoring: Active`);
        console.log(`📈 Total Deployments: ${status.stats.totalDeployments}`);
        console.log(`🎯 Success Rate: ${status.stats.totalDeployments > 0 ? 
          ((status.stats.successfulDeployments / status.stats.totalDeployments) * 100).toFixed(1) : 0}%`);
        console.log(`⏱️ Active Tasks: ${status.activeTasks.length}`);
      }
    }
  } catch (error) {
    console.log('⚠️ Could not fetch deployment status');
  }
  
  console.log('\n🎯 Ready for Enhanced MAS Content Generation!');
  console.log('\n📋 Next Steps:');
  console.log('   1. Generate content using Enhanced MAS:');
  console.log('      python3 method/vybe/vybe_commands.py generate --type=course --topic="Your Topic"');
  console.log('   2. Content will be automatically deployed to live platform');
  console.log('   3. Monitor deployment at: http://localhost:5173/mas');
  console.log('\n🔗 Useful URLs:');
  console.log('   - Platform: http://localhost:5173');
  console.log('   - MAS Dashboard: http://localhost:5173/mas');
  console.log('   - Deployment API: http://localhost:5173/api/deployment');
  console.log('   - Generated Courses: http://localhost:5173/courses');
  console.log('   - Generated News: http://localhost:5173/news');
  console.log('   - Generated Vybe Qubes: http://localhost:5173/vybeqube');
}

// Main startup sequence
async function main() {
  try {
    // Step 1: Check prerequisites
    checkPrerequisites();
    
    // Step 2: Setup database collections
    await setupDatabaseCollections();
    
    // Step 3: Start SvelteKit server
    const server = await startSvelteKitServer();
    
    // Step 4: Initialize deployment orchestrator
    const orchestratorReady = await initializeDeploymentOrchestrator();
    
    // Step 5: Check Enhanced MAS system
    await checkEnhancedMASStatus();
    
    // Step 6: Display status
    await displayPipelineStatus();
    
    if (orchestratorReady) {
      console.log('\n🎉 Enhanced MAS Deployment Pipeline is fully operational!');
    } else {
      console.log('\n⚠️ Deployment pipeline started with warnings. Check logs above.');
    }
    
    // Keep the process running
    process.on('SIGINT', () => {
      console.log('\n🛑 Shutting down Enhanced MAS Deployment Pipeline...');
      server.kill();
      process.exit(0);
    });
    
  } catch (error) {
    console.error('\n❌ Failed to start Enhanced MAS Deployment Pipeline:', error.message);
    process.exit(1);
  }
}

// Start the pipeline
main();
