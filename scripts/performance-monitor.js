#!/usr/bin/env node

/**
 * Performance Monitoring Script for VybeCoding.ai
 * Monitors application performance and sends alerts when thresholds are exceeded
 */

const https = require('https');
const http = require('http');
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  environments: {
    staging: {
      name: 'Staging',
      url: 'https://staging.vybecoding.ai',
      healthEndpoint: '/api/health',
      metricsEndpoint: '/api/metrics',
      timeout: 10000,
    },
    production: {
      name: 'Production',
      url: 'https://vybecoding.ai',
      healthEndpoint: '/api/health',
      metricsEndpoint: '/api/metrics',
      timeout: 5000,
    },
  },
  thresholds: {
    responseTime: 2000, // ms
    memoryUsage: 512, // MB
    errorRate: 0.05, // 5%
    eventLoopLag: 100, // ms
    uptime: 300, // 5 minutes minimum
  },
  alerting: {
    slack: process.env.SLACK_WEBHOOK_URL,
    discord: process.env.DISCORD_WEBHOOK_URL,
    email: process.env.ALERT_EMAIL,
  },
  monitoring: {
    interval: 60000, // 1 minute
    retries: 3,
    logFile: path.join(__dirname, '..', 'logs', 'performance-monitor.log'),
  },
};

class PerformanceMonitor {
  constructor() {
    this.alerts = new Map(); // Track recent alerts to avoid spam
    this.metrics = new Map(); // Store historical metrics
    this.ensureLogDirectory();
  }

  ensureLogDirectory() {
    const logDir = path.dirname(config.monitoring.logFile);
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
  }

  log(level, message, data = {}) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level,
      message,
      ...data,
    };

    console.log(`[${timestamp}] ${level.toUpperCase()}: ${message}`);

    // Append to log file
    fs.appendFileSync(
      config.monitoring.logFile,
      JSON.stringify(logEntry) + '\n'
    );
  }

  async makeRequest(url, timeout = 5000) {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      const protocol = url.startsWith('https:') ? https : http;

      const req = protocol.get(url, { timeout }, res => {
        let data = '';

        res.on('data', chunk => {
          data += chunk;
        });

        res.on('end', () => {
          const responseTime = Date.now() - startTime;

          try {
            const parsedData = JSON.parse(data);
            resolve({
              statusCode: res.statusCode,
              responseTime,
              data: parsedData,
              success: res.statusCode >= 200 && res.statusCode < 300,
            });
          } catch (error) {
            resolve({
              statusCode: res.statusCode,
              responseTime,
              data: data,
              success: res.statusCode >= 200 && res.statusCode < 300,
            });
          }
        });
      });

      req.on('timeout', () => {
        req.destroy();
        reject(new Error(`Request timeout after ${timeout}ms`));
      });

      req.on('error', error => {
        reject(error);
      });
    });
  }

  async checkHealth(environment) {
    const env = config.environments[environment];
    if (!env) {
      throw new Error(`Unknown environment: ${environment}`);
    }

    const healthUrl = `${env.url}${env.healthEndpoint}`;

    try {
      const response = await this.makeRequest(healthUrl, env.timeout);

      this.log('info', `Health check for ${env.name}`, {
        environment,
        url: healthUrl,
        statusCode: response.statusCode,
        responseTime: response.responseTime,
        status: response.data?.status,
      });

      return {
        environment,
        success: response.success,
        responseTime: response.responseTime,
        status: response.data?.status || 'unknown',
        checks: response.data?.checks || {},
        metrics: response.data?.metrics || {},
      };
    } catch (error) {
      this.log('error', `Health check failed for ${env.name}`, {
        environment,
        error: error.message,
      });

      return {
        environment,
        success: false,
        error: error.message,
        responseTime: null,
        status: 'error',
      };
    }
  }

  async getMetrics(environment) {
    const env = config.environments[environment];
    if (!env) {
      throw new Error(`Unknown environment: ${environment}`);
    }

    const metricsUrl = `${env.url}${env.metricsEndpoint}`;

    try {
      const response = await this.makeRequest(metricsUrl, env.timeout);

      if (response.success) {
        return response.data;
      } else {
        throw new Error(`Metrics endpoint returned ${response.statusCode}`);
      }
    } catch (error) {
      this.log('error', `Metrics collection failed for ${env.name}`, {
        environment,
        error: error.message,
      });
      return null;
    }
  }

  checkThresholds(environment, healthData, metricsData) {
    const alerts = [];
    const alertKey = `${environment}_${Date.now()}`;

    // Response time check
    if (healthData.responseTime > config.thresholds.responseTime) {
      alerts.push({
        type: 'response_time',
        severity: 'warning',
        message: `High response time: ${healthData.responseTime}ms (threshold: ${config.thresholds.responseTime}ms)`,
        value: healthData.responseTime,
        threshold: config.thresholds.responseTime,
      });
    }

    // Memory usage check
    if (
      metricsData?.performance?.memoryUsage?.heapUsed >
      config.thresholds.memoryUsage
    ) {
      alerts.push({
        type: 'memory_usage',
        severity: 'warning',
        message: `High memory usage: ${metricsData.performance.memoryUsage.heapUsed}MB (threshold: ${config.thresholds.memoryUsage}MB)`,
        value: metricsData.performance.memoryUsage.heapUsed,
        threshold: config.thresholds.memoryUsage,
      });
    }

    // Event loop lag check
    if (
      metricsData?.performance?.eventLoopLag > config.thresholds.eventLoopLag
    ) {
      alerts.push({
        type: 'event_loop_lag',
        severity: 'critical',
        message: `High event loop lag: ${metricsData.performance.eventLoopLag}ms (threshold: ${config.thresholds.eventLoopLag}ms)`,
        value: metricsData.performance.eventLoopLag,
        threshold: config.thresholds.eventLoopLag,
      });
    }

    // Error rate check
    if (metricsData?.business?.errorRate > config.thresholds.errorRate) {
      alerts.push({
        type: 'error_rate',
        severity: 'critical',
        message: `High error rate: ${(metricsData.business.errorRate * 100).toFixed(2)}% (threshold: ${(config.thresholds.errorRate * 100).toFixed(2)}%)`,
        value: metricsData.business.errorRate,
        threshold: config.thresholds.errorRate,
      });
    }

    // Uptime check
    if (healthData.metrics?.uptime < config.thresholds.uptime) {
      alerts.push({
        type: 'uptime',
        severity: 'info',
        message: `Service recently restarted: ${Math.floor(healthData.metrics.uptime / 60)} minutes uptime`,
        value: healthData.metrics.uptime,
        threshold: config.thresholds.uptime,
      });
    }

    return alerts;
  }

  async sendAlert(environment, alerts) {
    if (alerts.length === 0) return;

    const criticalAlerts = alerts.filter(a => a.severity === 'critical');
    const warningAlerts = alerts.filter(a => a.severity === 'warning');

    const message =
      `🚨 Performance Alert - ${config.environments[environment].name}\n\n` +
      `Critical Issues: ${criticalAlerts.length}\n` +
      `Warnings: ${warningAlerts.length}\n\n` +
      alerts
        .map(alert => `${alert.severity.toUpperCase()}: ${alert.message}`)
        .join('\n');

    this.log('alert', `Sending performance alert for ${environment}`, {
      environment,
      alertCount: alerts.length,
      criticalCount: criticalAlerts.length,
      warningCount: warningAlerts.length,
    });

    // Send to configured alert channels
    if (config.alerting.slack) {
      await this.sendSlackAlert(message);
    }

    if (config.alerting.discord) {
      await this.sendDiscordAlert(message);
    }
  }

  async sendSlackAlert(message) {
    try {
      const payload = {
        text: message,
        username: 'VybeCoding Performance Monitor',
        icon_emoji: ':warning:',
      };

      await this.makeRequest(config.alerting.slack, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });
    } catch (error) {
      this.log('error', 'Failed to send Slack alert', { error: error.message });
    }
  }

  async sendDiscordAlert(message) {
    try {
      const payload = {
        content: message,
        username: 'VybeCoding Performance Monitor',
      };

      await this.makeRequest(config.alerting.discord, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });
    } catch (error) {
      this.log('error', 'Failed to send Discord alert', {
        error: error.message,
      });
    }
  }

  async monitorEnvironment(environment) {
    this.log('info', `Starting monitoring for ${environment}`);

    try {
      const [healthData, metricsData] = await Promise.all([
        this.checkHealth(environment),
        this.getMetrics(environment),
      ]);

      // Store metrics for historical analysis
      const timestamp = Date.now();
      const metricsKey = `${environment}_${timestamp}`;
      this.metrics.set(metricsKey, { healthData, metricsData, timestamp });

      // Clean up old metrics (keep last 100 entries per environment)
      const environmentMetrics = Array.from(this.metrics.entries())
        .filter(([key]) => key.startsWith(environment))
        .sort(([, a], [, b]) => b.timestamp - a.timestamp);

      if (environmentMetrics.length > 100) {
        environmentMetrics.slice(100).forEach(([key]) => {
          this.metrics.delete(key);
        });
      }

      // Check thresholds and send alerts
      if (healthData.success && metricsData) {
        const alerts = this.checkThresholds(
          environment,
          healthData,
          metricsData
        );
        if (alerts.length > 0) {
          await this.sendAlert(environment, alerts);
        }
      }

      return { healthData, metricsData };
    } catch (error) {
      this.log('error', `Monitoring failed for ${environment}`, {
        error: error.message,
      });
      throw error;
    }
  }

  async start() {
    this.log('info', 'Starting performance monitoring');

    const monitor = async () => {
      for (const environment of Object.keys(config.environments)) {
        try {
          await this.monitorEnvironment(environment);
        } catch (error) {
          this.log('error', `Failed to monitor ${environment}`, {
            error: error.message,
          });
        }

        // Wait between environment checks using real interval
        await new Promise(resolve => {
          const monitorInterval = setInterval(() => {
            clearInterval(monitorInterval);
            resolve();
          }, 1000);
        });
      }
    };

    // Initial monitoring run
    await monitor();

    // Set up interval monitoring
    setInterval(monitor, config.monitoring.interval);

    this.log(
      'info',
      `Performance monitoring started with ${config.monitoring.interval}ms interval`
    );
  }
}

// CLI interface
if (require.main === module) {
  const monitor = new PerformanceMonitor();

  const command = process.argv[2];
  const environment = process.argv[3];

  switch (command) {
    case 'start':
      monitor.start().catch(error => {
        console.error('Failed to start monitoring:', error);
        process.exit(1);
      });
      break;

    case 'check':
      if (!environment) {
        console.error('Usage: node performance-monitor.js check <environment>');
        process.exit(1);
      }

      monitor
        .monitorEnvironment(environment)
        .then(result => {
          console.log(JSON.stringify(result, null, 2));
        })
        .catch(error => {
          console.error('Monitoring check failed:', error);
          process.exit(1);
        });
      break;

    default:
      console.log('Usage:');
      console.log('  node performance-monitor.js start');
      console.log('  node performance-monitor.js check <environment>');
      process.exit(1);
  }
}

module.exports = PerformanceMonitor;
