#!/bin/bash

# Enhanced MAS Production Deployment Script
# Phase 4: Production Deployment with Quality Standards
set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "🚀 ENHANCED MAS PRODUCTION DEPLOYMENT"
    echo "Phase 4: BMAD Method Implementation"
    echo "=================================================="
    echo -e "${NC}"
}

# Configuration
PROJECT_ROOT="/home/<USER>/Projects/vybecoding"
MAS_DIR="$PROJECT_ROOT/method/vybe"
CONFIG_DIR="$PROJECT_ROOT/config"
DEPLOYMENT_CONFIG="$CONFIG_DIR/production_deployment.json"

print_header

# Step 1: Validate Model Availability
print_info "Step 1: Validating Enhanced Model Availability..."

if ! command -v ollama &> /dev/null; then
    print_error "Ollama is not installed or not in PATH"
    exit 1
fi

# Check if enhanced models are available
REQUIRED_MODELS=("qwen3:30b-a3b" "devstral:24b" "llama-3.1-70b")
for model in "${REQUIRED_MODELS[@]}"; do
    if ollama list | grep -q "$model"; then
        print_status "Model $model is available"
    else
        print_warning "Model $model not found, attempting to pull..."
        ollama pull "$model" || {
            print_error "Failed to pull model $model"
            exit 1
        }
    fi
done

# Step 2: Test Agent Communication
print_info "Step 2: Testing Enhanced Agent Communication..."

cd "$MAS_DIR"

# Test basic agent communication
python3 -c "
import sys
sys.path.append('.')
from enhanced_mas_system import EnhancedMASSystem
from content_generation_engine import OllamaLLMService

print('Testing enhanced agent communication...')
llm_service = OllamaLLMService()
mas_system = EnhancedMASSystem(llm_service)

# Test agent initialization
agents = mas_system.get_agent_configs()
print(f'✅ {len(agents)} enhanced agents configured')

# Test model assignments
for agent_id, config in agents.items():
    print(f'✅ Agent {agent_id}: {config.role} -> {config.model_type.value}')

print('✅ Enhanced agent communication test passed')
" || {
    print_error "Enhanced agent communication test failed"
    exit 1
}

print_status "Enhanced agent communication validated"

# Step 3: Verify Quality Standards
print_info "Step 3: Verifying Quality Standards Implementation..."

python3 -c "
import json
import sys
sys.path.append('.')

# Load deployment config
with open('$DEPLOYMENT_CONFIG', 'r') as f:
    config = json.load(f)

quality_standards = config['deployment_config']['quality_standards']
print(f'✅ Minimum quality score: {quality_standards[\"minimum_score\"]}')
print(f'✅ VybeCoding compliance: {quality_standards[\"vybecoding_compliance\"]}')
print(f'✅ BMAD Method enabled: {quality_standards[\"bmad_method_enabled\"]}')

# Verify model configurations
enhanced_models = config['enhanced_models']['primary_models']
print(f'✅ {len(enhanced_models)} enhanced models configured')

for model in enhanced_models:
    print(f'✅ {model[\"name\"]}: {model[\"role\"]} (context: {model[\"context_window\"]})')

print('✅ Quality standards verification passed')
" || {
    print_error "Quality standards verification failed"
    exit 1
}

print_status "Quality standards verified"

# Step 4: Deploy Production Services
print_info "Step 4: Deploying Enhanced Production Services..."

# Kill existing services gracefully
print_info "Stopping existing services..."
pkill -f "real_mas_coordinator.py" || true
pkill -f "mas_exporter.py" || true
sleep 2

# Start enhanced MAS coordinator
print_info "Starting Enhanced MAS Coordinator..."
nohup python3 real_mas_coordinator.py > logs/mas_coordinator_production.log 2>&1 &
MAS_PID=$!
sleep 3

if ps -p $MAS_PID > /dev/null; then
    print_status "Enhanced MAS Coordinator started (PID: $MAS_PID)"
else
    print_error "Failed to start Enhanced MAS Coordinator"
    exit 1
fi

# Start enhanced content generator
print_info "Starting Enhanced Content Generator..."
nohup python3 content_generation_engine.py --production > logs/content_generator_production.log 2>&1 &
CONTENT_PID=$!
sleep 3

if ps -p $CONTENT_PID > /dev/null; then
    print_status "Enhanced Content Generator started (PID: $CONTENT_PID)"
else
    print_error "Failed to start Enhanced Content Generator"
    exit 1
fi

# Start observatory integration
print_info "Starting Observatory Integration..."
nohup python3 observatory_integration.py --production > logs/observatory_production.log 2>&1 &
OBS_PID=$!
sleep 3

if ps -p $OBS_PID > /dev/null; then
    print_status "Observatory Integration started (PID: $OBS_PID)"
else
    print_error "Failed to start Observatory Integration"
    exit 1
fi

print_status "All enhanced production services deployed"

# Step 5: Enable Real-time Monitoring
print_info "Step 5: Enabling Real-time Quality Monitoring..."

# Create monitoring configuration
cat > logs/production_monitoring.json << EOF
{
  "deployment_time": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "services": {
    "mas_coordinator": {"pid": $MAS_PID, "status": "running"},
    "content_generator": {"pid": $CONTENT_PID, "status": "running"},
    "observatory": {"pid": $OBS_PID, "status": "running"}
  },
  "quality_monitoring": {
    "enabled": true,
    "threshold": 0.95,
    "real_time": true
  }
}
EOF

print_status "Real-time monitoring enabled"

# Step 6: Conduct End-to-End Testing
print_info "Step 6: Conducting End-to-End Testing..."

# Test content generation with quality validation
python3 -c "
import asyncio
import sys
sys.path.append('.')
from content_generation_engine import ContentGenerationEngine, OllamaLLMService

async def test_production_content():
    llm_service = OllamaLLMService()
    engine = ContentGenerationEngine(llm_service)
    
    # Test enhanced content generation
    test_prompt = 'Generate a high-quality technical overview of AI-powered development tools'
    result = await engine.generate_content(
        prompt=test_prompt,
        agent_id='vyba',
        quality_threshold=0.95
    )
    
    if result and len(result) > 100:
        print('✅ Enhanced content generation test passed')
        print(f'✅ Generated {len(result)} characters of quality content')
        return True
    else:
        print('❌ Enhanced content generation test failed')
        return False

# Run test
success = asyncio.run(test_production_content())
if not success:
    sys.exit(1)
" || {
    print_error "End-to-end testing failed"
    exit 1
}

print_status "End-to-end testing completed successfully"

# Step 7: Final Validation
print_info "Step 7: Final Production Validation..."

# Validate all services are running
SERVICES=($MAS_PID $CONTENT_PID $OBS_PID)
for pid in "${SERVICES[@]}"; do
    if ps -p $pid > /dev/null; then
        print_status "Service PID $pid is running"
    else
        print_error "Service PID $pid is not running"
        exit 1
    fi
done

# Create deployment summary
cat > logs/production_deployment_summary.log << EOF
Enhanced MAS Production Deployment Summary
==========================================
Deployment Time: $(date -u +"%Y-%m-%dT%H:%M:%SZ")
Environment: Production
Quality Standards: 95%+ VybeCoding.ai compliance
BMAD Method: Enabled

Enhanced Models Deployed:
- Qwen3-30B-A3B: Advanced reasoning & creativity
- Devstral:24b: Enterprise development
- Llama-3.1-70B: Premium content creation

Services Running:
- MAS Coordinator: PID $MAS_PID
- Content Generator: PID $CONTENT_PID  
- Observatory: PID $OBS_PID

Status: ✅ PRODUCTION READY
Quality Framework: ✅ OPERATIONAL
Real-time Monitoring: ✅ ENABLED
EOF

print_status "Enhanced MAS Production Deployment Completed Successfully!"
print_info "Deployment summary saved to logs/production_deployment_summary.log"
print_info "Monitor logs in logs/ directory for ongoing operations"

echo -e "${GREEN}"
echo "=================================================="
echo "🎉 ENHANCED MAS PRODUCTION DEPLOYMENT SUCCESS!"
echo "Quality Standards: 95%+ VybeCoding.ai Compliance"
echo "BMAD Method: Fully Operational"
echo "=================================================="
echo -e "${NC}"
