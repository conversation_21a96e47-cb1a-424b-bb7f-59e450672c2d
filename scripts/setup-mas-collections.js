#!/usr/bin/env node

// Setup MAS-related Appwrite collections for VybeCoding.ai
// Run: node scripts/setup-mas-collections.js

import https from 'https';
import dotenv from 'dotenv';
dotenv.config();

const config = {
  endpoint: 'fra.cloud.appwrite.io',
  projectId: process.env.VITE_APPWRITE_PROJECT_ID,
  databaseId: process.env.VITE_APPWRITE_DATABASE_ID,
  apiKey: process.env.APPWRITE_API_KEY,
};

console.log('🤖 Setting up MAS-related Appwrite collections...\n');
console.log('📊 Configuration:');
console.log(`   Project: ${config.projectId}`);
console.log(`   Database: ${config.databaseId}`);
console.log(`   Endpoint: ${config.endpoint}`);
console.log('');

// Helper function to make API requests
function makeRequest(method, path, data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: config.endpoint,
      port: 443,
      path: `/v1${path}`,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'X-Appwrite-Project': config.projectId,
        'X-Appwrite-Key': config.apiKey,
      },
    };

    const req = https.request(options, res => {
      let responseData = '';

      res.on('data', chunk => {
        responseData += chunk;
      });

      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(parsed);
          } else {
            reject(new Error(parsed.message || `HTTP ${res.statusCode}`));
          }
        } catch (error) {
          reject(new Error(`Parse error: ${responseData}`));
        }
      });
    });

    req.on('error', error => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

// Create collection with error handling
async function createCollection(collectionId, name, permissions) {
  try {
    console.log(`📋 Creating ${name} collection...`);

    const collection = await makeRequest(
      'POST',
      `/databases/${config.databaseId}/collections`,
      {
        collectionId,
        name,
        permissions,
      }
    );

    console.log(`✅ ${name} collection created successfully`);
    return collection;
  } catch (error) {
    if (
      error.message.includes('already exists') ||
      error.message.includes('409')
    ) {
      console.log(`✅ ${name} collection already exists`);
      return { $id: collectionId };
    } else {
      console.log(`⚠️  ${name} collection error: ${error.message}`);
      throw error;
    }
  }
}

// Create attribute with error handling
async function createAttribute(collectionId, type, key, options) {
  try {
    const endpoint = `/databases/${config.databaseId}/collections/${collectionId}/attributes/${type}`;
    await makeRequest('POST', endpoint, { key, ...options });
    console.log(`   ✅ Added ${key} (${type})`);
  } catch (error) {
    if (
      error.message.includes('already exists') ||
      error.message.includes('409')
    ) {
      console.log(`   ✅ ${key} attribute already exists`);
    } else {
      console.log(`   ⚠️  ${key} attribute error: ${error.message}`);
    }
  }
}

async function setupMASCollections() {
  try {
    // 1. MAS Courses Collection
    await createCollection('vybe_courses', 'MAS Generated Courses', [
      'read("any")',
      'write("users")',
    ]);
    await createAttribute('vybe_courses', 'string', 'title', {
      size: 255,
      required: true,
    });
    await createAttribute('vybe_courses', 'string', 'description', {
      size: 2000,
      required: true,
    });
    await createAttribute('vybe_courses', 'string', 'category', {
      size: 100,
      required: true,
    });
    await createAttribute('vybe_courses', 'string', 'difficulty', {
      size: 50,
      required: true,
    });
    await createAttribute('vybe_courses', 'string', 'duration', {
      size: 50,
      required: true,
    });
    await createAttribute('vybe_courses', 'integer', 'lessons', {
      required: true,
    });
    await createAttribute('vybe_courses', 'integer', 'price', {
      required: false,
      default: 0,
    });
    await createAttribute('vybe_courses', 'string', 'instructor', {
      size: 255,
      required: true,
    });
    await createAttribute('vybe_courses', 'float', 'rating', {
      required: false,
      default: 0,
    });
    await createAttribute('vybe_courses', 'integer', 'students', {
      required: false,
      default: 0,
    });
    await createAttribute('vybe_courses', 'string', 'thumbnail', {
      size: 500,
      required: false,
    });
    await createAttribute('vybe_courses', 'string', 'tags', {
      size: 1000,
      required: false,
    });
    await createAttribute('vybe_courses', 'boolean', 'isPublished', {
      required: false,
      default: true,
    });
    await createAttribute('vybe_courses', 'boolean', 'isFeatured', {
      required: false,
      default: false,
    });
    await createAttribute('vybe_courses', 'string', 'agentsUsed', {
      size: 500,
      required: false,
    });
    await createAttribute('vybe_courses', 'float', 'qualityScore', {
      required: false,
      default: 0,
    });

    console.log('');

    // 2. MAS News Articles Collection
    await createCollection('vybe_news', 'MAS Generated News', [
      'read("any")',
      'write("users")',
    ]);
    await createAttribute('vybe_news', 'string', 'title', {
      size: 255,
      required: true,
    });
    await createAttribute('vybe_news', 'string', 'excerpt', {
      size: 500,
      required: true,
    });
    await createAttribute('vybe_news', 'string', 'content', {
      size: 10000,
      required: true,
    });
    await createAttribute('vybe_news', 'string', 'category', {
      size: 100,
      required: true,
    });
    await createAttribute('vybe_news', 'string', 'author', {
      size: 255,
      required: true,
    });
    await createAttribute('vybe_news', 'string', 'thumbnail', {
      size: 500,
      required: false,
    });
    await createAttribute('vybe_news', 'string', 'tags', {
      size: 1000,
      required: false,
    });
    await createAttribute('vybe_news', 'integer', 'readTime', {
      required: false,
      default: 5,
    });
    await createAttribute('vybe_news', 'boolean', 'isPublished', {
      required: false,
      default: true,
    });
    await createAttribute('vybe_news', 'boolean', 'isFeatured', {
      required: false,
      default: false,
    });
    await createAttribute('vybe_news', 'integer', 'views', {
      required: false,
      default: 0,
    });
    await createAttribute('vybe_news', 'integer', 'likes', {
      required: false,
      default: 0,
    });
    await createAttribute('vybe_news', 'string', 'source', {
      size: 255,
      required: false,
    });
    await createAttribute('vybe_news', 'string', 'sourceUrl', {
      size: 500,
      required: false,
    });
    await createAttribute('vybe_news', 'string', 'agentsUsed', {
      size: 500,
      required: false,
    });
    await createAttribute('vybe_news', 'float', 'qualityScore', {
      required: false,
      default: 0,
    });

    console.log('');

    // 3. MAS Vybe Qubes Collection
    await createCollection('vybe_qubes', 'MAS Generated Vybe Qubes', [
      'read("any")',
      'write("users")',
    ]);
    await createAttribute('vybe_qubes', 'string', 'name', {
      size: 255,
      required: true,
    });
    await createAttribute('vybe_qubes', 'string', 'description', {
      size: 2000,
      required: true,
    });
    await createAttribute('vybe_qubes', 'string', 'category', {
      size: 100,
      required: true,
    });
    await createAttribute('vybe_qubes', 'string', 'businessModel', {
      size: 255,
      required: true,
    });
    await createAttribute('vybe_qubes', 'string', 'revenueProjections', {
      size: 1000,
      required: false,
    });
    await createAttribute('vybe_qubes', 'string', 'technologies', {
      size: 1000,
      required: false,
    });
    await createAttribute('vybe_qubes', 'string', 'deploymentUrl', {
      size: 500,
      required: false,
    });
    await createAttribute('vybe_qubes', 'string', 'githubUrl', {
      size: 500,
      required: false,
    });
    await createAttribute('vybe_qubes', 'string', 'status', {
      size: 50,
      required: true,
      default: 'generating',
    });
    await createAttribute('vybe_qubes', 'datetime', 'deployedAt', {
      required: false,
    });
    await createAttribute('vybe_qubes', 'string', 'agentsUsed', {
      size: 500,
      required: false,
    });
    await createAttribute('vybe_qubes', 'float', 'qualityScore', {
      required: false,
      default: 0,
    });
    await createAttribute('vybe_qubes', 'integer', 'estimatedRevenue', {
      required: false,
      default: 0,
    });
    await createAttribute('vybe_qubes', 'boolean', 'isPublic', {
      required: false,
      default: true,
    });
    await createAttribute('vybe_qubes', 'boolean', 'isFeatured', {
      required: false,
      default: false,
    });

    console.log('\n🎉 MAS COLLECTIONS SETUP COMPLETE!');
    console.log('\n✅ Collections Created:');
    console.log('   - vybe_courses (MAS Generated Courses)');
    console.log('   - vybe_news (MAS Generated News)');
    console.log('   - vybe_qubes (MAS Generated Vybe Qubes)');
    console.log('\n🚀 Your MAS collections are ready!');
  } catch (error) {
    console.error('\n❌ MAS setup failed:', error.message);
    process.exit(1);
  }
}

// Run the setup
setupMASCollections();
