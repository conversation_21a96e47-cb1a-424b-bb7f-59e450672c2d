#!/usr/bin/env node

// Complete Appwrite setup via REST API for VybeCoding.ai
// Run: node scripts/setup-appwrite-complete.js

import https from 'https';
import dotenv from 'dotenv';
dotenv.config();

const config = {
  endpoint: 'fra.cloud.appwrite.io',
  projectId: process.env.VITE_APPWRITE_PROJECT_ID,
  databaseId: process.env.VITE_APPWRITE_DATABASE_ID,
  apiKey: process.env.APPWRITE_API_KEY,
};

console.log('🚀 Setting up VybeCoding.ai Appwrite collections...\n');
console.log('📊 Configuration:');
console.log(`   Project: ${config.projectId}`);
console.log(`   Database: ${config.databaseId}`);
console.log(`   Endpoint: ${config.endpoint}`);
console.log('');

// Helper function to make API requests
function makeRequest(method, path, data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: config.endpoint,
      port: 443,
      path: `/v1${path}`,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'X-Appwrite-Project': config.projectId,
        'X-Appwrite-Key': config.apiKey,
      },
    };

    const req = https.request(options, res => {
      let responseData = '';

      res.on('data', chunk => {
        responseData += chunk;
      });

      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(parsed);
          } else {
            reject(new Error(parsed.message || `HTTP ${res.statusCode}`));
          }
        } catch (error) {
          reject(new Error(`Parse error: ${responseData}`));
        }
      });
    });

    req.on('error', error => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

// Create collection with error handling
async function createCollection(collectionId, name, permissions) {
  try {
    console.log(`📋 Creating ${name} collection...`);

    const collection = await makeRequest(
      'POST',
      `/databases/${config.databaseId}/collections`,
      {
        collectionId,
        name,
        permissions,
      }
    );

    console.log(`✅ ${name} collection created successfully`);
    return collection;
  } catch (error) {
    if (
      error.message.includes('already exists') ||
      error.message.includes('409')
    ) {
      console.log(`✅ ${name} collection already exists`);
      return { $id: collectionId };
    } else {
      console.log(`⚠️  ${name} collection error: ${error.message}`);
      throw error;
    }
  }
}

// Create attribute with error handling
async function createAttribute(collectionId, type, key, options) {
  try {
    const endpoint = `/databases/${config.databaseId}/collections/${collectionId}/attributes/${type}`;
    await makeRequest('POST', endpoint, { key, ...options });
    console.log(`   ✅ Added ${key} (${type})`);
  } catch (error) {
    if (
      error.message.includes('already exists') ||
      error.message.includes('409')
    ) {
      console.log(`   ✅ ${key} attribute already exists`);
    } else {
      console.log(`   ⚠️  ${key} attribute error: ${error.message}`);
    }
  }
}

// Create storage bucket
async function createBucket(bucketId, name, permissions, options = {}) {
  try {
    console.log(`📁 Creating ${name} bucket...`);

    const bucket = await makeRequest('POST', '/storage/buckets', {
      bucketId,
      name,
      permissions,
      fileSecurity: true,
      enabled: true,
      ...options,
    });

    console.log(`✅ ${name} bucket created successfully`);
    return bucket;
  } catch (error) {
    if (
      error.message.includes('already exists') ||
      error.message.includes('409')
    ) {
      console.log(`✅ ${name} bucket already exists`);
    } else {
      console.log(`⚠️  ${name} bucket error: ${error.message}`);
    }
  }
}

async function setupCollections() {
  try {
    // 1. Users Collection
    await createCollection('users', 'Users', ['read("any")', 'write("users")']);
    await createAttribute('users', 'string', 'email', {
      size: 255,
      required: true,
    });
    await createAttribute('users', 'string', 'name', {
      size: 255,
      required: true,
    });
    await createAttribute('users', 'string', 'role', {
      size: 50,
      required: true,
      default: 'student',
    });
    await createAttribute('users', 'boolean', 'verified', {
      required: false,
      default: false,
    });
    await createAttribute('users', 'string', 'subscription', {
      size: 2000,
      required: false,
    });
    await createAttribute('users', 'string', 'subscriptionStatus', {
      size: 50,
      required: false,
      default: 'free',
    });
    await createAttribute('users', 'string', 'subscriptionTier', {
      size: 50,
      required: false,
      default: 'free',
    });

    console.log('');

    // 2. Courses Collection
    await createCollection('courses', 'Courses', [
      'read("any")',
      'write("instructors")',
    ]);
    await createAttribute('courses', 'string', 'title', {
      size: 255,
      required: true,
    });
    await createAttribute('courses', 'string', 'description', {
      size: 2000,
      required: true,
    });
    await createAttribute('courses', 'string', 'difficulty', {
      size: 50,
      required: true,
    });
    await createAttribute('courses', 'string', 'category', {
      size: 100,
      required: true,
    });
    await createAttribute('courses', 'integer', 'estimatedDuration', {
      required: true,
    });
    await createAttribute('courses', 'boolean', 'isPublished', {
      required: false,
      default: false,
    });
    await createAttribute('courses', 'integer', 'price', {
      required: false,
      default: 0,
    });
    await createAttribute('courses', 'string', 'thumbnailUrl', {
      size: 500,
      required: false,
    });

    console.log('');

    // 3. Lessons Collection
    await createCollection('lessons', 'Lessons', [
      'read("any")',
      'write("instructors")',
    ]);
    await createAttribute('lessons', 'string', 'courseId', {
      size: 50,
      required: true,
    });
    await createAttribute('lessons', 'string', 'title', {
      size: 255,
      required: true,
    });
    await createAttribute('lessons', 'string', 'content', {
      size: 10000,
      required: true,
    });
    await createAttribute('lessons', 'integer', 'order', { required: true });
    await createAttribute('lessons', 'integer', 'estimatedDuration', {
      required: true,
    });
    await createAttribute('lessons', 'string', 'videoUrl', {
      size: 500,
      required: false,
    });
    await createAttribute('lessons', 'boolean', 'isPublished', {
      required: false,
      default: false,
    });

    console.log('');

    // 4. Progress Collection
    await createCollection('progress', 'Progress', [
      'read("users")',
      'write("users")',
    ]);
    await createAttribute('progress', 'string', 'userId', {
      size: 50,
      required: true,
    });
    await createAttribute('progress', 'string', 'courseId', {
      size: 50,
      required: true,
    });
    await createAttribute('progress', 'string', 'lessonId', {
      size: 50,
      required: false,
    });
    await createAttribute('progress', 'float', 'overallProgress', {
      required: false,
      default: 0,
    });
    await createAttribute('progress', 'integer', 'totalTimeSpent', {
      required: false,
      default: 0,
    });
    await createAttribute('progress', 'string', 'completedLessons', {
      size: 1000,
      required: false,
    });
    await createAttribute('progress', 'datetime', 'lastAccessed', {
      required: false,
    });

    console.log('');

    // 5. Course Purchases Collection
    await createCollection('course_purchases', 'Course Purchases', [
      'read("users")',
      'write("users")',
    ]);
    await createAttribute('course_purchases', 'string', 'userId', {
      size: 50,
      required: true,
    });
    await createAttribute('course_purchases', 'string', 'courseId', {
      size: 50,
      required: true,
    });
    await createAttribute('course_purchases', 'datetime', 'purchasedAt', {
      required: true,
    });
    await createAttribute('course_purchases', 'string', 'paymentData', {
      size: 2000,
      required: false,
    });
    await createAttribute('course_purchases', 'string', 'status', {
      size: 50,
      required: true,
      default: 'active',
    });
    await createAttribute('course_purchases', 'datetime', 'expiresAt', {
      required: false,
    });

    console.log('');

    // Storage Buckets
    await createBucket(
      'course-content',
      'Course Content',
      ['read("any")', 'write("instructors")'],
      {
        maximumFileSize: 52428800, // 50MB
        allowedFileExtensions: [
          'jpg',
          'jpeg',
          'png',
          'gif',
          'webp',
          'mp4',
          'webm',
          'pdf',
          'txt',
          'md',
        ],
      }
    );

    await createBucket(
      'user-uploads',
      'User Uploads',
      ['read("users")', 'write("users")'],
      {
        maximumFileSize: 10485760, // 10MB
        allowedFileExtensions: [
          'jpg',
          'jpeg',
          'png',
          'gif',
          'webp',
          'pdf',
          'txt',
          'js',
          'ts',
          'py',
          'java',
          'cpp',
        ],
      }
    );

    console.log('\n🎉 APPWRITE SETUP COMPLETE!');
    console.log('\n✅ Collections Created:');
    console.log('   - users (with 7 attributes)');
    console.log('   - courses (with 8 attributes)');
    console.log('   - lessons (with 7 attributes)');
    console.log('   - progress (with 7 attributes)');
    console.log('   - course_purchases (with 6 attributes)');
    console.log('\n✅ Storage Buckets Created:');
    console.log('   - course-content (50MB limit)');
    console.log('   - user-uploads (10MB limit)');
    console.log('\n🚀 Your VybeCoding.ai database is ready!');
  } catch (error) {
    console.error('\n❌ Setup failed:', error.message);
    process.exit(1);
  }
}

// Run the setup
setupCollections();
