#!/bin/bash
# VybeCoding.ai Performance Test Runner
# Comprehensive performance testing automation

set -e

# Make script executable
chmod +x "$0" 2>/dev/null || true

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
RESULTS_DIR="${PROJECT_ROOT}/results"
TESTS_DIR="${PROJECT_ROOT}/tests/load"
BASE_URL="${BASE_URL:-http://localhost:3000}"
ENVIRONMENT="${ENVIRONMENT:-local}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
}

warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

info() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] INFO:${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log "Checking performance testing prerequisites..."
    
    # Check if k6 is installed
    if ! command -v k6 &> /dev/null; then
        error "k6 is not installed. Please install k6 from https://k6.io/docs/getting-started/installation/"
        exit 1
    fi
    
    # Check if Node.js is available for report generation
    if ! command -v node &> /dev/null; then
        warning "Node.js not found. Report generation may be limited."
    fi
    
    # Check if Docker is running (for test environment)
    if ! docker info &> /dev/null; then
        warning "Docker is not running. Some tests may fail."
    fi
    
    # Check if test files exist
    local required_tests=("basic-load-test.js" "student-workflow-test.js" "vybe-qube-generation-test.js" "stress-test.js")
    for test in "${required_tests[@]}"; do
        if [ ! -f "${TESTS_DIR}/${test}" ]; then
            error "Required test file not found: ${test}"
            exit 1
        fi
    done
    
    log "✅ Prerequisites check completed"
}

# Setup test environment
setup_test_environment() {
    log "Setting up test environment..."
    
    # Create results directory
    mkdir -p "$RESULTS_DIR"
    
    # Create timestamp for this test run
    local timestamp=$(date +%Y%m%d_%H%M%S)
    export TEST_RUN_ID="perf_test_${timestamp}"
    export RESULTS_SUBDIR="${RESULTS_DIR}/${TEST_RUN_ID}"
    mkdir -p "$RESULTS_SUBDIR"
    
    # Start test environment if needed
    if [ "$ENVIRONMENT" = "local" ]; then
        log "Starting local test environment..."
        
        # Check if application is running
        if ! curl -f "$BASE_URL/api/health" &>/dev/null; then
            warning "Application not responding at $BASE_URL"
            
            # Try to start with docker-compose
            if [ -f "${PROJECT_ROOT}/docker-compose.yml" ]; then
                log "Starting application with docker-compose..."
                cd "$PROJECT_ROOT"
                docker-compose up -d
                
                # Wait for application to be ready
                local attempts=0
                while [ $attempts -lt 30 ]; do
                    if curl -f "$BASE_URL/api/health" &>/dev/null; then
                        log "Application is ready"
                        break
                    fi
                    sleep 5
                    ((attempts++))
                done
                
                if [ $attempts -eq 30 ]; then
                    error "Application failed to start within timeout"
                    exit 1
                fi
            else
                error "Application is not running and no docker-compose.yml found"
                exit 1
            fi
        else
            log "Application is already running at $BASE_URL"
        fi
    fi
    
    # Setup test data
    setup_test_data
    
    log "✅ Test environment ready"
}

# Setup test data
setup_test_data() {
    log "Setting up test data..."
    
    # Create test users if needed
    if [ -f "${SCRIPT_DIR}/create-test-users.js" ]; then
        node "${SCRIPT_DIR}/create-test-users.js" || warning "Failed to create test users"
    fi
    
    # Create test courses if needed
    if [ -f "${SCRIPT_DIR}/create-test-courses.js" ]; then
        node "${SCRIPT_DIR}/create-test-courses.js" || warning "Failed to create test courses"
    fi
    
    log "Test data setup completed"
}

# Run individual test
run_test() {
    local test_name="$1"
    local test_file="$2"
    local output_prefix="$3"
    
    log "Running $test_name..."
    
    local start_time=$(date +%s)
    
    # Run k6 test with multiple output formats
    k6 run \
        --env BASE_URL="$BASE_URL" \
        --env ENVIRONMENT="$ENVIRONMENT" \
        --out json="${RESULTS_SUBDIR}/${output_prefix}.json" \
        --out csv="${RESULTS_SUBDIR}/${output_prefix}.csv" \
        "${TESTS_DIR}/${test_file}" \
        > "${RESULTS_SUBDIR}/${output_prefix}.log" 2>&1
    
    local exit_code=$?
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    if [ $exit_code -eq 0 ]; then
        log "✅ $test_name completed successfully in ${duration}s"
    else
        error "❌ $test_name failed (exit code: $exit_code)"
        cat "${RESULTS_SUBDIR}/${output_prefix}.log"
    fi
    
    return $exit_code
}

# Run all performance tests
run_all_tests() {
    log "Starting comprehensive performance test suite..."
    
    local tests_passed=0
    local tests_total=0
    
    # Basic Load Test
    ((tests_total++))
    if run_test "Basic Load Test" "basic-load-test.js" "basic-load"; then
        ((tests_passed++))
    fi
    
    # Wait between tests to allow system recovery
    sleep 30
    
    # Student Workflow Test
    ((tests_total++))
    if run_test "Student Workflow Test" "student-workflow-test.js" "student-workflow"; then
        ((tests_passed++))
    fi
    
    sleep 30
    
    # Vybe Qube Generation Test
    ((tests_total++))
    if run_test "Vybe Qube Generation Test" "vybe-qube-generation-test.js" "vybe-qube-generation"; then
        ((tests_passed++))
    fi
    
    sleep 60 # Longer wait before stress test
    
    # Stress Test (optional - can be skipped with --no-stress)
    if [ "$SKIP_STRESS_TEST" != "true" ]; then
        ((tests_total++))
        if run_test "Stress Test" "stress-test.js" "stress-test"; then
            ((tests_passed++))
        fi
    fi
    
    log "Performance test suite completed: $tests_passed/$tests_total tests passed"
    
    return $((tests_total - tests_passed))
}

# Generate comprehensive report
generate_report() {
    log "Generating performance test report..."
    
    local report_file="${RESULTS_SUBDIR}/performance-report.html"
    local summary_file="${RESULTS_SUBDIR}/performance-summary.json"
    
    # Create comprehensive report
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>VybeCoding.ai Performance Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .pass { color: green; }
        .fail { color: red; }
        .warning { color: orange; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>VybeCoding.ai Performance Test Report</h1>
        <p><strong>Test Run ID:</strong> $TEST_RUN_ID</p>
        <p><strong>Date:</strong> $(date)</p>
        <p><strong>Environment:</strong> $ENVIRONMENT</p>
        <p><strong>Base URL:</strong> $BASE_URL</p>
    </div>
EOF
    
    # Add test results sections
    for test_type in basic-load student-workflow vybe-qube-generation stress-test; do
        if [ -f "${RESULTS_SUBDIR}/${test_type}.json" ]; then
            echo "<div class=\"test-section\">" >> "$report_file"
            echo "<h2>$(echo $test_type | tr '-' ' ' | sed 's/\b\w/\U&/g') Test Results</h2>" >> "$report_file"
            
            # Extract key metrics from JSON (simplified)
            if command -v jq &> /dev/null; then
                local json_file="${RESULTS_SUBDIR}/${test_type}.json"
                echo "<table>" >> "$report_file"
                echo "<tr><th>Metric</th><th>Value</th><th>Status</th></tr>" >> "$report_file"
                
                # Add basic metrics
                local http_reqs=$(jq -r '.metrics.http_reqs.values.count // "N/A"' "$json_file")
                local http_req_duration=$(jq -r '.metrics.http_req_duration.values.avg // "N/A"' "$json_file")
                local http_req_failed=$(jq -r '.metrics.http_req_failed.values.rate // "N/A"' "$json_file")
                
                echo "<tr><td>Total Requests</td><td>$http_reqs</td><td>-</td></tr>" >> "$report_file"
                echo "<tr><td>Avg Response Time</td><td>${http_req_duration}ms</td><td>-</td></tr>"  >> "$report_file"
                echo "<tr><td>Error Rate</td><td>${http_req_failed}%</td><td>-</td></tr>" >> "$report_file"
                
                echo "</table>" >> "$report_file"
            else
                echo "<p>jq not available - detailed metrics not shown</p>" >> "$report_file"
            fi
            
            echo "</div>" >> "$report_file"
        fi
    done
    
    echo "</body></html>" >> "$report_file"
    
    # Create summary JSON
    cat > "$summary_file" << EOF
{
    "testRunId": "$TEST_RUN_ID",
    "timestamp": "$(date -Iseconds)",
    "environment": "$ENVIRONMENT",
    "baseUrl": "$BASE_URL",
    "testsCompleted": $(ls "${RESULTS_SUBDIR}"/*.json 2>/dev/null | wc -l),
    "resultFiles": {
        "report": "$report_file",
        "summary": "$summary_file",
        "rawResults": "$RESULTS_SUBDIR"
    }
}
EOF
    
    log "✅ Performance report generated: $report_file"
    log "📊 Summary available: $summary_file"
}

# Cleanup test environment
cleanup_test_environment() {
    log "Cleaning up test environment..."
    
    # Stop test environment if we started it
    if [ "$ENVIRONMENT" = "local" ] && [ "$CLEANUP_ENVIRONMENT" = "true" ]; then
        if [ -f "${PROJECT_ROOT}/docker-compose.yml" ]; then
            cd "$PROJECT_ROOT"
            docker-compose down || warning "Failed to stop docker-compose"
        fi
    fi
    
    # Archive old results (keep last 10 runs)
    if [ -d "$RESULTS_DIR" ]; then
        local old_results=$(ls -1t "$RESULTS_DIR" | tail -n +11)
        if [ -n "$old_results" ]; then
            echo "$old_results" | while read -r old_result; do
                rm -rf "${RESULTS_DIR}/${old_result}"
            done
            log "Cleaned up old test results"
        fi
    fi
    
    log "✅ Cleanup completed"
}

# Send notifications
send_notifications() {
    local status="$1"
    local message="$2"
    
    # Slack notification
    if [ -n "$SLACK_WEBHOOK_URL" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"🚀 VybeCoding.ai Performance Test $status: $message\"}" \
            "$SLACK_WEBHOOK_URL" &>/dev/null || true
    fi
    
    # Discord notification
    if [ -n "$DISCORD_WEBHOOK_URL" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"content\":\"🚀 VybeCoding.ai Performance Test $status: $message\"}" \
            "$DISCORD_WEBHOOK_URL" &>/dev/null || true
    fi
}

# Main execution function
main() {
    local start_time=$(date)
    log "Starting VybeCoding.ai performance test suite at $start_time"
    
    # Setup
    check_prerequisites
    setup_test_environment
    
    # Run tests
    local test_failures=0
    if ! run_all_tests; then
        test_failures=$?
    fi
    
    # Generate reports
    generate_report
    
    # Cleanup
    cleanup_test_environment
    
    # Summary
    local end_time=$(date)
    local status="Success"
    local message="Performance testing completed successfully"
    
    if [ $test_failures -gt 0 ]; then
        status="Failed"
        message="$test_failures test(s) failed"
    fi
    
    log "Performance test suite completed at $end_time"
    log "Status: $status"
    log "Results available in: $RESULTS_SUBDIR"
    
    # Send notifications
    send_notifications "$status" "$message"
    
    exit $test_failures
}

# Handle script arguments
case "${1:-}" in
    --help|-h)
        echo "VybeCoding.ai Performance Test Runner"
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h          Show this help message"
        echo "  --no-stress         Skip stress testing"
        echo "  --no-cleanup        Don't cleanup environment after tests"
        echo "  --basic-only        Run only basic load test"
        echo "  --student-only      Run only student workflow test"
        echo "  --qube-only         Run only Vybe Qube generation test"
        echo "  --stress-only       Run only stress test"
        echo ""
        echo "Environment Variables:"
        echo "  BASE_URL            Application base URL (default: http://localhost:3000)"
        echo "  ENVIRONMENT         Test environment (default: local)"
        echo "  SLACK_WEBHOOK_URL   Slack webhook for notifications"
        echo "  DISCORD_WEBHOOK_URL Discord webhook for notifications"
        exit 0
        ;;
    --no-stress)
        export SKIP_STRESS_TEST="true"
        main
        ;;
    --no-cleanup)
        export CLEANUP_ENVIRONMENT="false"
        main
        ;;
    --basic-only)
        check_prerequisites
        setup_test_environment
        run_test "Basic Load Test" "basic-load-test.js" "basic-load"
        generate_report
        cleanup_test_environment
        ;;
    --student-only)
        check_prerequisites
        setup_test_environment
        run_test "Student Workflow Test" "student-workflow-test.js" "student-workflow"
        generate_report
        cleanup_test_environment
        ;;
    --qube-only)
        check_prerequisites
        setup_test_environment
        run_test "Vybe Qube Generation Test" "vybe-qube-generation-test.js" "vybe-qube-generation"
        generate_report
        cleanup_test_environment
        ;;
    --stress-only)
        check_prerequisites
        setup_test_environment
        run_test "Stress Test" "stress-test.js" "stress-test"
        generate_report
        cleanup_test_environment
        ;;
    *)
        main "$@"
        ;;
esac
