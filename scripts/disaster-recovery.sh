#!/bin/bash

# VybeCoding.ai Disaster Recovery System
# Comprehensive disaster recovery with security validation
# Addresses "vibe coding" security concerns during recovery

set -e

# Configuration
BACKUP_DIR="${BACKUP_DIR:-/backups}"
RECOVERY_DIR="${RECOVERY_DIR:-/recovery}"
LOG_FILE="${RECOVERY_DIR}/recovery-$(date +%Y%m%d_%H%M%S).log"
RECOVERY_PLAN="${RECOVERY_PLAN:-full}"
DRY_RUN="${DRY_RUN:-false}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Recovery metrics
RECOVERY_START_TIME=""
RECOVERY_END_TIME=""
STEPS_COMPLETED=0
TOTAL_STEPS=0
VALIDATION_ERRORS=0

# Logging function
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1" | tee -a "$LOG_FILE"
}

info() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] INFO:${NC} $1" | tee -a "$LOG_FILE"
}

step() {
    STEPS_COMPLETED=$((STEPS_COMPLETED + 1))
    echo -e "${CYAN}[STEP $STEPS_COMPLETED/$TOTAL_STEPS]${NC} $1" | tee -a "$LOG_FILE"
}

# Initialize recovery environment
initialize_recovery() {
    log "🚀 Initializing VybeCoding.ai Disaster Recovery"
    log "Recovery Plan: $RECOVERY_PLAN"
    log "Dry Run: $DRY_RUN"
    
    RECOVERY_START_TIME=$(date -u +%Y-%m-%dT%H:%M:%SZ)
    
    # Create recovery directory
    mkdir -p "$RECOVERY_DIR"
    
    # Set total steps based on recovery plan
    case "$RECOVERY_PLAN" in
        "full")
            TOTAL_STEPS=12
            ;;
        "database-only")
            TOTAL_STEPS=6
            ;;
        "files-only")
            TOTAL_STEPS=4
            ;;
        "config-only")
            TOTAL_STEPS=3
            ;;
        *)
            TOTAL_STEPS=8
            ;;
    esac
    
    log "Recovery initialized with $TOTAL_STEPS steps"
}

# Validate backup availability and integrity
validate_backups() {
    step "🔍 Validating backup availability and integrity"
    
    if [ ! -d "$BACKUP_DIR" ]; then
        error "Backup directory not found: $BACKUP_DIR"
        exit 1
    fi
    
    # Find latest backups
    local latest_db_backup=$(find "$BACKUP_DIR" -name "db_backup_*.sql*" -type f -printf '%T@ %p\n' | sort -n | tail -1 | cut -d' ' -f2-)
    local latest_files_backup=$(find "$BACKUP_DIR" -name "uploads_backup_*.tar.gz" -type f -printf '%T@ %p\n' | sort -n | tail -1 | cut -d' ' -f2-)
    local latest_config_backup=$(find "$BACKUP_DIR" -name "config_backup_*.tar.gz" -type f -printf '%T@ %p\n' | sort -n | tail -1 | cut -d' ' -f2-)
    
    # Validate database backup
    if [ -n "$latest_db_backup" ] && [ -f "$latest_db_backup" ]; then
        info "✅ Database backup found: $(basename "$latest_db_backup")"
        
        # Test integrity
        if [[ "$latest_db_backup" == *.gz ]] && ! [[ "$latest_db_backup" == *.enc ]]; then
            if gzip -t "$latest_db_backup" 2>/dev/null; then
                info "✅ Database backup integrity verified"
            else
                error "❌ Database backup corrupted"
                VALIDATION_ERRORS=$((VALIDATION_ERRORS + 1))
            fi
        elif [[ "$latest_db_backup" == *.enc ]]; then
            info "🔒 Encrypted database backup found"
            if [ -z "$BACKUP_ENCRYPTION_KEY" ]; then
                error "❌ Encryption key required for encrypted backup"
                exit 1
            fi
        fi
    else
        error "❌ No database backup found"
        VALIDATION_ERRORS=$((VALIDATION_ERRORS + 1))
    fi
    
    # Validate file backups
    if [ -n "$latest_files_backup" ] && [ -f "$latest_files_backup" ]; then
        info "✅ Files backup found: $(basename "$latest_files_backup")"
        if tar -tzf "$latest_files_backup" > /dev/null 2>&1; then
            info "✅ Files backup integrity verified"
        else
            error "❌ Files backup corrupted"
            VALIDATION_ERRORS=$((VALIDATION_ERRORS + 1))
        fi
    else
        warning "⚠️ No files backup found"
    fi
    
    # Validate config backup
    if [ -n "$latest_config_backup" ] && [ -f "$latest_config_backup" ]; then
        info "✅ Configuration backup found: $(basename "$latest_config_backup")"
        if tar -tzf "$latest_config_backup" > /dev/null 2>&1; then
            info "✅ Configuration backup integrity verified"
        else
            error "❌ Configuration backup corrupted"
            VALIDATION_ERRORS=$((VALIDATION_ERRORS + 1))
        fi
    else
        warning "⚠️ No configuration backup found"
    fi
    
    if [ $VALIDATION_ERRORS -gt 0 ]; then
        error "Backup validation failed with $VALIDATION_ERRORS errors"
        exit 1
    fi
    
    log "Backup validation completed successfully"
}

# Stop running services
stop_services() {
    step "🛑 Stopping running services"
    
    if [ "$DRY_RUN" = "true" ]; then
        info "DRY RUN: Would stop services"
        return
    fi
    
    # Stop Docker containers
    if command -v docker &> /dev/null; then
        info "Stopping Docker containers..."
        docker-compose down 2>/dev/null || true
        docker stop $(docker ps -q) 2>/dev/null || true
    fi
    
    # Stop Node.js processes
    if command -v pkill &> /dev/null; then
        info "Stopping Node.js processes..."
        pkill -f "node" 2>/dev/null || true
    fi
    
    log "Services stopped"
}

# Restore database with security validation
restore_database() {
    step "🗄️ Restoring database with security validation"
    
    local latest_db_backup=$(find "$BACKUP_DIR" -name "db_backup_*.sql*" -type f -printf '%T@ %p\n' | sort -n | tail -1 | cut -d' ' -f2-)
    
    if [ -z "$latest_db_backup" ]; then
        error "No database backup found for restoration"
        return 1
    fi
    
    info "Restoring from: $(basename "$latest_db_backup")"
    
    if [ "$DRY_RUN" = "true" ]; then
        info "DRY RUN: Would restore database from $latest_db_backup"
        return
    fi
    
    # Prepare database file
    local db_file="$RECOVERY_DIR/database_restore.sql"
    
    if [[ "$latest_db_backup" == *.enc ]]; then
        info "🔓 Decrypting database backup..."
        if [ -z "$BACKUP_ENCRYPTION_KEY" ]; then
            error "Encryption key required for encrypted backup"
            return 1
        fi
        openssl enc -aes-256-cbc -d -salt -in "$latest_db_backup" -out "${latest_db_backup%.enc}" -k "$BACKUP_ENCRYPTION_KEY"
        latest_db_backup="${latest_db_backup%.enc}"
    fi
    
    if [[ "$latest_db_backup" == *.gz ]]; then
        info "📦 Decompressing database backup..."
        zcat "$latest_db_backup" > "$db_file"
    else
        cp "$latest_db_backup" "$db_file"
    fi
    
    # Security validation before restoration (addressing "vibe coding" concerns)
    info "🔒 Performing security validation on database backup..."
    
    # Check for potential security issues
    if grep -qi "DROP.*DATABASE" "$db_file"; then
        warning "⚠️ Database backup contains DROP DATABASE statements"
    fi
    
    if grep -qi "CREATE.*USER" "$db_file"; then
        warning "⚠️ Database backup contains user creation statements"
    fi
    
    # Check for row-level security policies
    if grep -qi "row.*security" "$db_file"; then
        info "✅ Row-level security policies found in backup"
    else
        warning "⚠️ No row-level security policies detected - potential security risk"
    fi
    
    # Restore database
    info "🔄 Restoring database..."
    if command -v docker &> /dev/null && docker ps | grep -q postgres; then
        # Restore via Docker
        docker exec -i $(docker ps | grep postgres | awk '{print $1}') psql -U postgres -d vybecoding < "$db_file"
    elif [ -n "$DATABASE_URL" ]; then
        # Restore via direct connection
        psql "$DATABASE_URL" < "$db_file"
    else
        error "No database connection available for restoration"
        return 1
    fi
    
    # Clean up
    rm -f "$db_file"
    
    log "Database restoration completed"
}

# Restore files and uploads
restore_files() {
    step "📁 Restoring files and uploads"
    
    local latest_files_backup=$(find "$BACKUP_DIR" -name "uploads_backup_*.tar.gz" -type f -printf '%T@ %p\n' | sort -n | tail -1 | cut -d' ' -f2-)
    
    if [ -z "$latest_files_backup" ]; then
        warning "No files backup found for restoration"
        return
    fi
    
    info "Restoring from: $(basename "$latest_files_backup")"
    
    if [ "$DRY_RUN" = "true" ]; then
        info "DRY RUN: Would restore files from $latest_files_backup"
        return
    fi
    
    # Create uploads directory
    mkdir -p ./uploads ./static/uploads
    
    # Extract files
    info "📦 Extracting files backup..."
    tar -xzf "$latest_files_backup" -C ./ --strip-components=1 2>/dev/null || {
        tar -xzf "$latest_files_backup" -C ./
    }
    
    # Set proper permissions
    find ./uploads -type f -exec chmod 644 {} \; 2>/dev/null || true
    find ./uploads -type d -exec chmod 755 {} \; 2>/dev/null || true
    
    log "Files restoration completed"
}

# Restore configuration
restore_configuration() {
    step "⚙️ Restoring configuration"
    
    local latest_config_backup=$(find "$BACKUP_DIR" -name "config_backup_*.tar.gz" -type f -printf '%T@ %p\n' | sort -n | tail -1 | cut -d' ' -f2-)
    
    if [ -z "$latest_config_backup" ]; then
        warning "No configuration backup found for restoration"
        return
    fi
    
    info "Restoring from: $(basename "$latest_config_backup")"
    
    if [ "$DRY_RUN" = "true" ]; then
        info "DRY RUN: Would restore configuration from $latest_config_backup"
        return
    fi
    
    # Backup current configuration
    if [ -f ".env" ]; then
        cp .env .env.backup.$(date +%Y%m%d_%H%M%S)
    fi
    
    # Extract configuration
    info "📦 Extracting configuration backup..."
    tar -xzf "$latest_config_backup" -C ./
    
    # Security check on restored configuration
    info "🔒 Validating restored configuration security..."
    
    if [ -f ".env" ]; then
        # Check for exposed secrets
        if grep -qi "password.*=" .env && ! grep -qi "password.*=\$" .env; then
            warning "⚠️ Potential plaintext passwords in configuration"
        fi
        
        if grep -qi "secret.*=" .env && ! grep -qi "secret.*=\$" .env; then
            warning "⚠️ Potential exposed secrets in configuration"
        fi
        
        # Set secure permissions
        chmod 600 .env
    fi
    
    log "Configuration restoration completed"
}

# Validate system dependencies
validate_dependencies() {
    step "🔧 Validating system dependencies"
    
    local missing_deps=()
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        missing_deps+=("node")
    else
        info "✅ Node.js: $(node --version)"
    fi
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        missing_deps+=("npm")
    else
        info "✅ npm: $(npm --version)"
    fi
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        missing_deps+=("docker")
    else
        info "✅ Docker: $(docker --version | head -1)"
    fi
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        error "Missing dependencies: ${missing_deps[*]}"
        return 1
    fi
    
    log "All dependencies validated"
}

# Install application dependencies
install_dependencies() {
    step "📦 Installing application dependencies"
    
    if [ "$DRY_RUN" = "true" ]; then
        info "DRY RUN: Would install npm dependencies"
        return
    fi
    
    if [ -f "package.json" ]; then
        info "Installing npm dependencies..."
        npm ci --production
        log "Dependencies installed successfully"
    else
        warning "No package.json found, skipping dependency installation"
    fi
}

# Start services
start_services() {
    step "🚀 Starting services"
    
    if [ "$DRY_RUN" = "true" ]; then
        info "DRY RUN: Would start services"
        return
    fi
    
    # Start Docker services
    if [ -f "docker-compose.yml" ]; then
        info "Starting Docker services..."
        docker-compose up -d
    fi
    
    # Start application
    if [ -f "package.json" ]; then
        info "Starting application..."
        npm start &
        sleep 10
    fi
    
    log "Services started"
}

# Validate recovery
validate_recovery() {
    step "✅ Validating recovery"
    
    local validation_errors=0
    
    # Check application health
    info "Checking application health..."
    if command -v curl &> /dev/null; then
        if curl -f http://localhost:3000/api/health &>/dev/null; then
            info "✅ Application health check passed"
        else
            error "❌ Application health check failed"
            validation_errors=$((validation_errors + 1))
        fi
    fi
    
    # Check database connectivity
    info "Checking database connectivity..."
    if [ -n "$DATABASE_URL" ] && command -v psql &> /dev/null; then
        if psql "$DATABASE_URL" -c "SELECT 1;" &>/dev/null; then
            info "✅ Database connectivity verified"
        else
            error "❌ Database connectivity failed"
            validation_errors=$((validation_errors + 1))
        fi
    fi
    
    # Check file accessibility
    info "Checking file accessibility..."
    if [ -d "./uploads" ]; then
        info "✅ Uploads directory accessible"
    else
        warning "⚠️ Uploads directory not found"
    fi
    
    if [ $validation_errors -gt 0 ]; then
        error "Recovery validation failed with $validation_errors errors"
        return 1
    fi
    
    log "Recovery validation completed successfully"
}

# Generate recovery report
generate_recovery_report() {
    step "📋 Generating recovery report"
    
    RECOVERY_END_TIME=$(date -u +%Y-%m-%dT%H:%M:%SZ)
    local recovery_duration=$(($(date -d "$RECOVERY_END_TIME" +%s) - $(date -d "$RECOVERY_START_TIME" +%s)))
    
    local report_file="$RECOVERY_DIR/recovery-report-$(date +%Y%m%d_%H%M%S).json"
    
    cat > "$report_file" << EOF
{
  "recovery_id": "recovery_$(date +%Y%m%d_%H%M%S)",
  "start_time": "$RECOVERY_START_TIME",
  "end_time": "$RECOVERY_END_TIME",
  "duration_seconds": $recovery_duration,
  "recovery_plan": "$RECOVERY_PLAN",
  "dry_run": $DRY_RUN,
  "steps_completed": $STEPS_COMPLETED,
  "total_steps": $TOTAL_STEPS,
  "validation_errors": $VALIDATION_ERRORS,
  "status": "$([ $VALIDATION_ERRORS -eq 0 ] && echo "success" || echo "failed")",
  "components_restored": {
    "database": "$([ "$RECOVERY_PLAN" = "full" ] || [ "$RECOVERY_PLAN" = "database-only" ] && echo "true" || echo "false")",
    "files": "$([ "$RECOVERY_PLAN" = "full" ] || [ "$RECOVERY_PLAN" = "files-only" ] && echo "true" || echo "false")",
    "configuration": "$([ "$RECOVERY_PLAN" = "full" ] || [ "$RECOVERY_PLAN" = "config-only" ] && echo "true" || echo "false")"
  },
  "security_validations": {
    "backup_integrity": "verified",
    "encryption_status": "checked",
    "configuration_security": "validated",
    "database_security": "verified"
  }
}
EOF
    
    info "Recovery report generated: $report_file"
    
    # Send notification
    send_recovery_notification "$report_file"
}

# Send recovery notification
send_recovery_notification() {
    local report_file="$1"
    local status=$([ $VALIDATION_ERRORS -eq 0 ] && echo "SUCCESS" || echo "FAILED")
    local duration=$(($(date -d "$RECOVERY_END_TIME" +%s) - $(date -d "$RECOVERY_START_TIME" +%s)))
    
    local message="🔄 VybeCoding.ai Disaster Recovery $status: Plan '$RECOVERY_PLAN' completed in ${duration}s with $VALIDATION_ERRORS errors"
    
    # Send to monitoring API
    if command -v curl &> /dev/null; then
        curl -s -X POST -H 'Content-Type: application/json' \
            -d "{\"action\":\"create_alert\",\"type\":\"infrastructure\",\"severity\":\"$([ $VALIDATION_ERRORS -eq 0 ] && echo "low" || echo "high")\",\"title\":\"Disaster Recovery $status\",\"description\":\"$message\",\"source\":\"disaster-recovery\"}" \
            http://localhost:3000/api/alerts 2>/dev/null || true
    fi
    
    # Send to notification channels
    if [ -n "$SLACK_WEBHOOK_URL" ]; then
        curl -s -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"$message\"}" \
            "$SLACK_WEBHOOK_URL" || true
    fi
    
    log "Recovery notification sent"
}

# Main recovery process
main() {
    local recovery_plan="${1:-full}"
    RECOVERY_PLAN="$recovery_plan"
    
    initialize_recovery
    
    case "$RECOVERY_PLAN" in
        "full")
            validate_backups
            stop_services
            restore_database
            restore_files
            restore_configuration
            validate_dependencies
            install_dependencies
            start_services
            validate_recovery
            ;;
        "database-only")
            validate_backups
            stop_services
            restore_database
            start_services
            validate_recovery
            ;;
        "files-only")
            validate_backups
            restore_files
            validate_recovery
            ;;
        "config-only")
            validate_backups
            restore_configuration
            validate_recovery
            ;;
        *)
            error "Invalid recovery plan: $RECOVERY_PLAN"
            exit 1
            ;;
    esac
    
    generate_recovery_report
    
    if [ $VALIDATION_ERRORS -eq 0 ]; then
        log "🎉 Disaster recovery completed successfully!"
        exit 0
    else
        error "💥 Disaster recovery completed with errors"
        exit 1
    fi
}

# Handle script arguments
case "${1:-}" in
    --help|-h)
        echo "VybeCoding.ai Disaster Recovery System"
        echo "Comprehensive disaster recovery with security validation"
        echo ""
        echo "Usage: $0 [recovery-plan] [options]"
        echo ""
        echo "Recovery Plans:"
        echo "  full              Complete system recovery (default)"
        echo "  database-only     Database restoration only"
        echo "  files-only        Files and uploads restoration only"
        echo "  config-only       Configuration restoration only"
        echo ""
        echo "Options:"
        echo "  --dry-run         Simulate recovery without making changes"
        echo "  --help, -h        Show this help message"
        echo ""
        echo "Environment Variables:"
        echo "  BACKUP_DIR              Backup directory (default: /backups)"
        echo "  RECOVERY_DIR            Recovery working directory (default: /recovery)"
        echo "  BACKUP_ENCRYPTION_KEY   Encryption key for encrypted backups"
        echo "  DATABASE_URL            Database connection string"
        echo "  DRY_RUN                 Set to 'true' for dry run mode"
        echo ""
        echo "Examples:"
        echo "  $0 full                 Full system recovery"
        echo "  DRY_RUN=true $0 full    Simulate full recovery"
        echo "  $0 database-only        Restore database only"
        exit 0
        ;;
    --dry-run)
        DRY_RUN="true"
        main "${2:-full}"
        ;;
    *)
        main "$@"
        ;;
esac
