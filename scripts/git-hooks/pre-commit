#!/bin/bash

# Pre-commit hook for VybeCoding.ai
# Runs validation checks before allowing commits

set -e

echo "🔍 Running pre-commit validation..."

# Check if we're in the src directory for tests
if [ -d "src" ]; then
    cd src
fi

# Run tests if they exist
if [ -f "package.json" ] && grep -q "test" package.json; then
    echo "🧪 Running tests..."
    npm test -- --run --reporter=basic 2>/dev/null || {
        echo "❌ Tests failed. Commit aborted."
        exit 1
    }
    echo "✅ Tests passed"
fi

# TypeScript check - Re-enabled after fixing import paths and case conflicts
echo "🔍 Running TypeScript check..."
if command -v tsc >/dev/null 2>&1; then
    if ! tsc --noEmit --skipLibCheck 2>/dev/null; then
        echo "⚠️  TypeScript check found issues (non-blocking for now)"
    else
        echo "✅ TypeScript check passed"
    fi
else
    echo "⚠️  TypeScript compiler not found, skipping check"
fi

# Check for TODO/FIXME comments in staged files
echo "📝 Checking for TODO/FIXME comments..."
staged_files=$(git diff --cached --name-only --diff-filter=ACM | grep -E '\.(ts|js|svelte)$' || true)
if [ -n "$staged_files" ]; then
    todo_count=$(git diff --cached | grep -E '^\+.*\b(TODO|FIXME|XXX|HACK)\b' | wc -l || echo "0")
    if [ "$todo_count" -gt 0 ]; then
        echo "⚠️  Found $todo_count TODO/FIXME comments in staged changes"
        echo "Consider addressing these before committing:"
        git diff --cached | grep -E '^\+.*\b(TODO|FIXME|XXX|HACK)\b' || true
        echo
        read -p "Continue with commit? (y/n): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo "❌ Commit aborted by user"
            exit 1
        fi
    fi
fi

echo "✅ Pre-commit validation passed"
exit 0
