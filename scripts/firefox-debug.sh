#!/bin/bash
# Firefox Developer Edition Debug Launcher
# Enhanced debugging setup for VybeCoding Platform

FIREFOX_DEV="/home/<USER>/firefox-dev/firefox"
PROFILE_DIR="/tmp/firefox-debug-profile"
URL="${1:-http://localhost:5173}"

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${BLUE}🦊 Firefox Developer Edition Debug Launcher${NC}"
echo -e "${GREEN}=======================================${NC}"

# Create a clean debug profile
if [ -d "$PROFILE_DIR" ]; then
    rm -rf "$PROFILE_DIR"
fi
mkdir -p "$PROFILE_DIR"

# Create user.js with optimal debug settings
cat > "$PROFILE_DIR/user.js" << 'EOF'
// Enhanced Developer Tools Configuration
user_pref("devtools.toolbox.host", "window");
user_pref("devtools.toolbox.selectedTool", "webconsole");
user_pref("devtools.console.timestampMessages", true);
user_pref("devtools.debugger.auto-pretty-print", true);
user_pref("devtools.performance.enabled", true);
user_pref("devtools.responsive.enabled", true);
user_pref("devtools.memory.enabled", true);
user_pref("devtools.storage.enabled", true);
user_pref("devtools.netmonitor.enabled", true);
user_pref("devtools.application.enabled", true);

// Console filters - enable all
user_pref("devtools.webconsole.filter.net", true);
user_pref("devtools.webconsole.filter.netxhr", true);
user_pref("devtools.webconsole.filter.css", true);
user_pref("devtools.webconsole.filter.js", true);
user_pref("devtools.webconsole.filter.error", true);
user_pref("devtools.webconsole.filter.warn", true);
user_pref("devtools.webconsole.filter.info", true);
user_pref("devtools.webconsole.filter.log", true);
user_pref("devtools.webconsole.filter.debug", true);

// Performance optimizations for debugging
user_pref("devtools.debugger.features.async-captured-stacks", true);
user_pref("devtools.debugger.features.async-live-stacks", true);
user_pref("devtools.debugger.call-stack-visible", true);
user_pref("devtools.debugger.scopes-visible", true);
user_pref("devtools.debugger.workers", true);

// Network monitoring
user_pref("devtools.netmonitor.persistlog", true);
user_pref("devtools.netmonitor.features.requestBlocking", true);

// Security preferences for local development
user_pref("security.fileuri.strict_origin_policy", false);
user_pref("privacy.file_unique_origin", false);
user_pref("security.mixed_content.block_active_content", false);
user_pref("security.mixed_content.block_display_content", false);

// Disable popup blocking for debug windows
user_pref("dom.disable_open_during_load", false);

// Enable experimental features
user_pref("devtools.experiments.enabled", true);
user_pref("devtools.every-frame-visible", true);

// Auto-open devtools
user_pref("devtools.toolbox.host", "window");
user_pref("devtools.toolbox.zoomValue", "1.0");
EOF

echo -e "${YELLOW}📋 Debug Settings:${NC}"
echo -e "  • Enhanced Developer Tools enabled"
echo -e "  • All console filters active"
echo -e "  • Performance monitoring enabled"
echo -e "  • Network debugging optimized"
echo -e "  • Async stack traces enabled"
echo -e "  • Security relaxed for local dev"
echo ""

echo -e "${GREEN}🚀 Starting Firefox Developer Edition...${NC}"
echo -e "${BLUE}URL: ${URL}${NC}"
echo ""

# Launch Firefox with debug configuration
"$FIREFOX_DEV" \
    --profile "$PROFILE_DIR" \
    --new-instance \
    --no-remote \
    --devtools \
    --jsconsole \
    --disable-web-security \
    --disable-features=VizDisplayCompositor \
    --allow-running-insecure-content \
    --disable-extensions-except="" \
    --load-extension="" \
    "$URL" &

FIREFOX_PID=$!

echo -e "${GREEN}✅ Firefox Developer Edition started with PID: $FIREFOX_PID${NC}"
echo -e "${YELLOW}🔧 Developer Tools should auto-open${NC}"
echo -e "${BLUE}📊 Debug Monitor: http://localhost:5173/debug-monitor.html${NC}"
echo ""
echo -e "${RED}Press Ctrl+C to stop${NC}"

# Wait for user interrupt
trap "echo -e '\n${YELLOW}🔄 Closing Firefox...${NC}'; kill $FIREFOX_PID 2>/dev/null; exit 0" INT
wait $FIREFOX_PID
