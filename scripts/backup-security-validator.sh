#!/bin/bash

# VybeCoding.ai Backup Security Validator
# Enhanced security validation for backups addressing "vibe coding" vulnerabilities
# Validates backup integrity, encryption, and security configurations

set -e

# Configuration
BACKUP_DIR="${BACKUP_DIR:-/backups}"
LOG_FILE="${BACKUP_DIR}/security-validation.log"
SECURITY_REPORT="${BACKUP_DIR}/security-report-$(date +%Y%m%d_%H%M%S).json"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Security validation results
SECURITY_SCORE=100
CRITICAL_ISSUES=()
HIGH_ISSUES=()
MEDIUM_ISSUES=()
LOW_ISSUES=()
RECOMMENDATIONS=()

# Logging function
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1" | tee -a "$LOG_FILE"
}

info() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] INFO:${NC} $1" | tee -a "$LOG_FILE"
}

# Add security issue
add_issue() {
    local severity="$1"
    local issue="$2"
    local points="$3"
    
    case "$severity" in
        "critical")
            CRITICAL_ISSUES+=("$issue")
            SECURITY_SCORE=$((SECURITY_SCORE - points))
            ;;
        "high")
            HIGH_ISSUES+=("$issue")
            SECURITY_SCORE=$((SECURITY_SCORE - points))
            ;;
        "medium")
            MEDIUM_ISSUES+=("$issue")
            SECURITY_SCORE=$((SECURITY_SCORE - points))
            ;;
        "low")
            LOW_ISSUES+=("$issue")
            SECURITY_SCORE=$((SECURITY_SCORE - points))
            ;;
    esac
}

# Add recommendation
add_recommendation() {
    RECOMMENDATIONS+=("$1")
}

# Validate backup encryption
validate_encryption() {
    log "🔒 Validating backup encryption..."
    
    local encrypted_files=0
    local unencrypted_files=0
    
    # Check for encrypted backup files
    while IFS= read -r -d '' file; do
        if [[ "$file" == *.enc ]]; then
            encrypted_files=$((encrypted_files + 1))
            info "✅ Encrypted backup found: $(basename "$file")"
        else
            unencrypted_files=$((unencrypted_files + 1))
            warning "⚠️ Unencrypted backup found: $(basename "$file")"
        fi
    done < <(find "$BACKUP_DIR" -name "*.gz" -o -name "*.sql" -o -name "*.enc" -print0 2>/dev/null)
    
    if [ $unencrypted_files -gt 0 ]; then
        add_issue "high" "Found $unencrypted_files unencrypted backup files" 15
        add_recommendation "Enable backup encryption with BACKUP_ENCRYPTION_KEY"
    fi
    
    if [ $encrypted_files -eq 0 ] && [ $unencrypted_files -gt 0 ]; then
        add_issue "critical" "No encrypted backups found - all backups are unencrypted" 25
        add_recommendation "Implement mandatory backup encryption"
    fi
    
    log "Encryption validation completed: $encrypted_files encrypted, $unencrypted_files unencrypted"
}

# Validate backup integrity
validate_integrity() {
    log "🔍 Validating backup integrity..."
    
    local corrupted_files=0
    local valid_files=0
    
    # Check compressed files
    while IFS= read -r -d '' file; do
        if [[ "$file" == *.gz ]]; then
            if gzip -t "$file" 2>/dev/null; then
                valid_files=$((valid_files + 1))
                info "✅ Integrity verified: $(basename "$file")"
            else
                corrupted_files=$((corrupted_files + 1))
                error "❌ Corrupted backup: $(basename "$file")"
                add_issue "critical" "Corrupted backup file: $(basename "$file")" 20
            fi
        elif [[ "$file" == *.tar.gz ]]; then
            if tar -tzf "$file" > /dev/null 2>&1; then
                valid_files=$((valid_files + 1))
                info "✅ Archive integrity verified: $(basename "$file")"
            else
                corrupted_files=$((corrupted_files + 1))
                error "❌ Corrupted archive: $(basename "$file")"
                add_issue "critical" "Corrupted archive file: $(basename "$file")" 20
            fi
        fi
    done < <(find "$BACKUP_DIR" -name "*.gz" -o -name "*.tar.gz" -print0 2>/dev/null)
    
    if [ $corrupted_files -gt 0 ]; then
        add_recommendation "Investigate backup corruption causes and re-run backups"
    fi
    
    log "Integrity validation completed: $valid_files valid, $corrupted_files corrupted"
}

# Validate backup permissions and access controls
validate_permissions() {
    log "🔐 Validating backup permissions and access controls..."
    
    # Check backup directory permissions
    local backup_perms=$(stat -c "%a" "$BACKUP_DIR" 2>/dev/null || echo "000")
    if [ "$backup_perms" != "700" ] && [ "$backup_perms" != "750" ]; then
        add_issue "medium" "Backup directory permissions too permissive: $backup_perms" 10
        add_recommendation "Set backup directory permissions to 700 or 750"
    fi
    
    # Check individual backup file permissions
    local insecure_files=0
    while IFS= read -r -d '' file; do
        local file_perms=$(stat -c "%a" "$file" 2>/dev/null || echo "000")
        if [ "${file_perms:0:1}" -gt "6" ] || [ "${file_perms:1:1}" -gt "0" ] || [ "${file_perms:2:1}" -gt "0" ]; then
            insecure_files=$((insecure_files + 1))
            warning "⚠️ Insecure file permissions: $(basename "$file") ($file_perms)"
        fi
    done < <(find "$BACKUP_DIR" -type f -print0 2>/dev/null)
    
    if [ $insecure_files -gt 0 ]; then
        add_issue "medium" "Found $insecure_files backup files with insecure permissions" 8
        add_recommendation "Set backup file permissions to 600 (owner read/write only)"
    fi
    
    log "Permission validation completed"
}

# Validate backup storage locations
validate_storage_locations() {
    log "☁️ Validating backup storage locations..."
    
    local storage_locations=0
    
    # Check for cloud storage configuration
    if [ -n "$AWS_S3_BUCKET" ] && command -v aws &> /dev/null; then
        storage_locations=$((storage_locations + 1))
        info "✅ AWS S3 storage configured"
    fi
    
    if [ -n "$GCS_BUCKET" ] && command -v gsutil &> /dev/null; then
        storage_locations=$((storage_locations + 1))
        info "✅ Google Cloud Storage configured"
    fi
    
    if [ -n "$AZURE_STORAGE_ACCOUNT" ] && command -v az &> /dev/null; then
        storage_locations=$((storage_locations + 1))
        info "✅ Azure Blob Storage configured"
    fi
    
    if [ $storage_locations -eq 0 ]; then
        add_issue "high" "No cloud storage configured - single point of failure" 15
        add_recommendation "Configure at least one cloud storage provider for backup redundancy"
    elif [ $storage_locations -eq 1 ]; then
        add_issue "medium" "Only one backup storage location configured" 8
        add_recommendation "Configure multiple storage locations for better redundancy"
    fi
    
    log "Storage validation completed: $storage_locations locations configured"
}

# Validate database security configurations (addressing "vibe coding" issues)
validate_database_security() {
    log "🗄️ Validating database security configurations..."
    
    # Check for database backup content security
    local latest_db_backup=$(find "$BACKUP_DIR" -name "db_backup_*.sql*" -type f -printf '%T@ %p\n' | sort -n | tail -1 | cut -d' ' -f2-)
    
    if [ -n "$latest_db_backup" ] && [ -f "$latest_db_backup" ]; then
        info "Analyzing latest database backup: $(basename "$latest_db_backup")"
        
        # Check if backup contains sensitive patterns (simulated check)
        local temp_file="/tmp/db_check_$$"
        
        if [[ "$latest_db_backup" == *.gz ]]; then
            zcat "$latest_db_backup" > "$temp_file" 2>/dev/null || {
                warning "Could not decompress database backup for analysis"
                return
            }
        elif [[ "$latest_db_backup" == *.enc ]]; then
            info "✅ Database backup is encrypted - skipping content analysis"
            return
        else
            cp "$latest_db_backup" "$temp_file"
        fi
        
        # Check for potential security issues in database schema
        if grep -qi "password.*varchar" "$temp_file" 2>/dev/null; then
            add_issue "medium" "Database may contain plaintext password fields" 10
            add_recommendation "Ensure all passwords are properly hashed in database"
        fi
        
        if grep -qi "api_key.*varchar" "$temp_file" 2>/dev/null; then
            add_issue "medium" "Database may contain API keys in plaintext" 10
            add_recommendation "Encrypt or hash API keys in database"
        fi
        
        # Check for row-level security policies (addressing "vibe coding" article concerns)
        if ! grep -qi "row.*security" "$temp_file" 2>/dev/null; then
            add_issue "high" "No row-level security policies detected in database backup" 15
            add_recommendation "Implement row-level security (RLS) policies to prevent data exposure"
        fi
        
        # Clean up
        rm -f "$temp_file"
    else
        add_issue "medium" "No database backup found for security analysis" 10
    fi
    
    log "Database security validation completed"
}

# Validate backup retention and lifecycle
validate_retention() {
    log "📅 Validating backup retention and lifecycle..."
    
    local retention_days="${RETENTION_DAYS:-30}"
    local old_backups=$(find "$BACKUP_DIR" -type f -mtime +$retention_days 2>/dev/null | wc -l)
    
    if [ $old_backups -gt 0 ]; then
        add_issue "low" "Found $old_backups backups older than retention policy ($retention_days days)" 5
        add_recommendation "Run cleanup to remove old backups: ./scripts/backup-system.sh --cleanup"
    fi
    
    # Check backup frequency
    local recent_backups=$(find "$BACKUP_DIR" -type f -mtime -1 2>/dev/null | wc -l)
    if [ $recent_backups -eq 0 ]; then
        add_issue "high" "No backups created in the last 24 hours" 15
        add_recommendation "Ensure backup schedule is running correctly"
    fi
    
    log "Retention validation completed"
}

# Validate monitoring and alerting
validate_monitoring() {
    log "📊 Validating backup monitoring and alerting..."
    
    local notification_channels=0
    
    if [ -n "$SLACK_WEBHOOK_URL" ]; then
        notification_channels=$((notification_channels + 1))
        info "✅ Slack notifications configured"
    fi
    
    if [ -n "$DISCORD_WEBHOOK_URL" ]; then
        notification_channels=$((notification_channels + 1))
        info "✅ Discord notifications configured"
    fi
    
    if [ -n "$NOTIFICATION_EMAIL" ]; then
        notification_channels=$((notification_channels + 1))
        info "✅ Email notifications configured"
    fi
    
    if [ $notification_channels -eq 0 ]; then
        add_issue "medium" "No notification channels configured for backup alerts" 10
        add_recommendation "Configure Slack, Discord, or email notifications for backup status"
    fi
    
    log "Monitoring validation completed: $notification_channels notification channels"
}

# Generate security report
generate_security_report() {
    log "📋 Generating security report..."
    
    # Ensure minimum score
    if [ $SECURITY_SCORE -lt 0 ]; then
        SECURITY_SCORE=0
    fi
    
    # Determine overall status
    local status="secure"
    if [ ${#CRITICAL_ISSUES[@]} -gt 0 ] || [ $SECURITY_SCORE -lt 60 ]; then
        status="critical"
    elif [ ${#HIGH_ISSUES[@]} -gt 0 ] || [ $SECURITY_SCORE -lt 80 ]; then
        status="warning"
    fi
    
    # Create JSON report
    cat > "$SECURITY_REPORT" << EOF
{
  "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "backup_directory": "$BACKUP_DIR",
  "security_score": $SECURITY_SCORE,
  "status": "$status",
  "issues": {
    "critical": [$(printf '"%s",' "${CRITICAL_ISSUES[@]}" | sed 's/,$//')]
    "high": [$(printf '"%s",' "${HIGH_ISSUES[@]}" | sed 's/,$//')]
    "medium": [$(printf '"%s",' "${MEDIUM_ISSUES[@]}" | sed 's/,$//')]
    "low": [$(printf '"%s",' "${LOW_ISSUES[@]}" | sed 's/,$//')]
  },
  "recommendations": [$(printf '"%s",' "${RECOMMENDATIONS[@]}" | sed 's/,$//')]
  "validation_checks": {
    "encryption": "completed",
    "integrity": "completed",
    "permissions": "completed",
    "storage_locations": "completed",
    "database_security": "completed",
    "retention": "completed",
    "monitoring": "completed"
  }
}
EOF
    
    log "Security report generated: $SECURITY_REPORT"
}

# Display security summary
display_summary() {
    echo ""
    echo "🔒 BACKUP SECURITY VALIDATION SUMMARY"
    echo "======================================"
    echo ""
    echo "Security Score: $SECURITY_SCORE/100"
    echo "Overall Status: $([ $SECURITY_SCORE -ge 80 ] && echo "✅ SECURE" || [ $SECURITY_SCORE -ge 60 ] && echo "⚠️ WARNING" || echo "❌ CRITICAL")"
    echo ""
    
    if [ ${#CRITICAL_ISSUES[@]} -gt 0 ]; then
        echo "🚨 CRITICAL ISSUES (${#CRITICAL_ISSUES[@]}):"
        printf '  • %s\n' "${CRITICAL_ISSUES[@]}"
        echo ""
    fi
    
    if [ ${#HIGH_ISSUES[@]} -gt 0 ]; then
        echo "⚠️ HIGH ISSUES (${#HIGH_ISSUES[@]}):"
        printf '  • %s\n' "${HIGH_ISSUES[@]}"
        echo ""
    fi
    
    if [ ${#MEDIUM_ISSUES[@]} -gt 0 ]; then
        echo "📋 MEDIUM ISSUES (${#MEDIUM_ISSUES[@]}):"
        printf '  • %s\n' "${MEDIUM_ISSUES[@]}"
        echo ""
    fi
    
    if [ ${#LOW_ISSUES[@]} -gt 0 ]; then
        echo "ℹ️ LOW ISSUES (${#LOW_ISSUES[@]}):"
        printf '  • %s\n' "${LOW_ISSUES[@]}"
        echo ""
    fi
    
    if [ ${#RECOMMENDATIONS[@]} -gt 0 ]; then
        echo "💡 RECOMMENDATIONS:"
        printf '  • %s\n' "${RECOMMENDATIONS[@]}"
        echo ""
    fi
    
    echo "📊 Report saved to: $SECURITY_REPORT"
    echo ""
}

# Send security alert if critical issues found
send_security_alert() {
    if [ ${#CRITICAL_ISSUES[@]} -gt 0 ] || [ $SECURITY_SCORE -lt 60 ]; then
        local alert_message="🚨 CRITICAL: VybeCoding.ai backup security validation failed (Score: $SECURITY_SCORE/100). ${#CRITICAL_ISSUES[@]} critical issues found."
        
        # Send to monitoring API
        if command -v curl &> /dev/null; then
            curl -s -X POST -H 'Content-Type: application/json' \
                -d "{\"action\":\"create_alert\",\"type\":\"security\",\"severity\":\"critical\",\"title\":\"Backup Security Validation Failed\",\"description\":\"$alert_message\",\"source\":\"backup-security-validator\"}" \
                http://localhost:3000/api/alerts 2>/dev/null || true
        fi
        
        # Send to notification channels
        if [ -n "$SLACK_WEBHOOK_URL" ]; then
            curl -s -X POST -H 'Content-type: application/json' \
                --data "{\"text\":\"$alert_message\"}" \
                "$SLACK_WEBHOOK_URL" || true
        fi
    fi
}

# Main validation process
main() {
    log "🔒 Starting VybeCoding.ai Backup Security Validation"
    log "Addressing security concerns from 'vibe coding' vulnerabilities"
    
    # Create backup directory if it doesn't exist
    mkdir -p "$BACKUP_DIR"
    
    # Run all validation checks
    validate_encryption
    validate_integrity
    validate_permissions
    validate_storage_locations
    validate_database_security
    validate_retention
    validate_monitoring
    
    # Generate report and summary
    generate_security_report
    display_summary
    send_security_alert
    
    log "Security validation completed"
    
    # Exit with appropriate code
    if [ ${#CRITICAL_ISSUES[@]} -gt 0 ] || [ $SECURITY_SCORE -lt 60 ]; then
        exit 1
    elif [ ${#HIGH_ISSUES[@]} -gt 0 ] || [ $SECURITY_SCORE -lt 80 ]; then
        exit 2
    else
        exit 0
    fi
}

# Handle script arguments
case "${1:-}" in
    --help|-h)
        echo "VybeCoding.ai Backup Security Validator"
        echo "Validates backup security addressing 'vibe coding' vulnerabilities"
        echo ""
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h          Show this help message"
        echo "  --report-only       Generate report without validation"
        echo ""
        echo "Exit Codes:"
        echo "  0                   All security checks passed"
        echo "  1                   Critical security issues found"
        echo "  2                   High security issues found"
        exit 0
        ;;
    --report-only)
        generate_security_report
        echo "Security report generated: $SECURITY_REPORT"
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
