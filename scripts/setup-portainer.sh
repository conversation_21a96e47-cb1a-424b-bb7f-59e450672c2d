#!/bin/bash

# VybeCoding.ai Portainer Setup Script
# Sets up <PERSON><PERSON><PERSON> with secure admin password

set -e

echo "🐳 Setting up Portainer for VybeCoding.ai..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default admin password (change this!)
PORTAINER_PASSWORD="${PORTAINER_PASSWORD:-VybeCoding2024!}"

echo -e "${BLUE}📋 Portainer Setup Configuration:${NC}"
echo -e "  • Web UI: http://localhost:9000"
echo -e "  • HTTPS UI: https://localhost:9443"
echo -e "  • Admin Password: ${PORTAINER_PASSWORD}"
echo -e "  • Container Name: vybecoding-portainer"
echo ""

# Create password file for Portainer
echo -e "${YELLOW}🔐 Creating Portainer admin password...${NC}"
echo -n "$PORTAINER_PASSWORD" | docker run --rm -i portainer/helper-reset-password

# Start Portainer
echo -e "${YELLOW}🚀 Starting Portainer...${NC}"
docker compose -f docker-compose.development.yml up -d portainer

# Wait for Portainer to be ready
echo -e "${YELLOW}⏳ Waiting for Portainer to start...${NC}"
sleep 10

# Check if Portainer is running
if docker ps | grep -q "vybecoding-portainer"; then
    echo -e "${GREEN}✅ Portainer is running successfully!${NC}"
    echo ""
    echo -e "${BLUE}🌐 Access Portainer:${NC}"
    echo -e "  • Web UI: ${GREEN}http://localhost:9000${NC}"
    echo -e "  • HTTPS UI: ${GREEN}https://localhost:9443${NC}"
    echo -e "  • Username: ${GREEN}admin${NC}"
    echo -e "  • Password: ${GREEN}${PORTAINER_PASSWORD}${NC}"
    echo ""
    echo -e "${BLUE}📚 What you can do with Portainer:${NC}"
    echo -e "  • Manage all Docker containers"
    echo -e "  • View container logs and stats"
    echo -e "  • Manage Docker images and volumes"
    echo -e "  • Monitor resource usage"
    echo -e "  • Deploy new containers via UI"
    echo ""
    echo -e "${YELLOW}💡 Pro Tip:${NC} Bookmark http://localhost:9000 for easy access!"
else
    echo -e "${RED}❌ Failed to start Portainer. Check Docker logs:${NC}"
    echo "docker logs vybecoding-portainer"
    exit 1
fi

echo -e "${GREEN}🎉 Portainer setup complete!${NC}"
