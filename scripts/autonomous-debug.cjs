#!/usr/bin/env node
/**
 * Autonomous Debug Mode for VybeCoding Platform
 * Monitors browser console, server logs, and provides autonomous debugging capabilities
 */

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');

class AutonomousDebugger {
  constructor() {
    this.isRunning = false;
    this.serverProcess = null;
    this.debugLogs = [];
    this.consoleErrors = [];
    this.logFile = path.join(__dirname, '../logs/autonomous-debug.log');

    // Ensure logs directory exists
    const logsDir = path.dirname(this.logFile);
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
    }
  }

  log(message, level = 'INFO') {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] [${level}] ${message}`;

    console.log(logEntry);
    this.debugLogs.push(logEntry);

    // Write to log file
    fs.appendFileSync(this.logFile, logEntry + '\n');
  }

  async startServer() {
    return new Promise((resolve, reject) => {
      this.log('Starting Vite development server...');

      this.serverProcess = spawn(
        'npx',
        ['vite', 'dev', '--host', '--port', '5173'],
        {
          cwd: path.join(__dirname, '..'),
          stdio: ['pipe', 'pipe', 'pipe'],
        }
      );

      this.serverProcess.stdout.on('data', data => {
        const output = data.toString();
        this.log(`SERVER: ${output.trim()}`);

        if (output.includes('Local:') || output.includes('ready in')) {
          this.log('Server is ready!', 'SUCCESS');
          resolve();
        }
      });

      this.serverProcess.stderr.on('data', data => {
        const error = data.toString();
        this.log(`SERVER ERROR: ${error.trim()}`, 'ERROR');
        this.consoleErrors.push(error);
      });

      this.serverProcess.on('error', error => {
        this.log(`Failed to start server: ${error.message}`, 'ERROR');
        reject(error);
      });

      // Timeout after 30 seconds
      setTimeout(() => {
        if (!this.isRunning) {
          reject(new Error('Server startup timeout'));
        }
      }, 30000);
    });
  }

  setupConsoleMonitoring() {
    this.log('Setting up browser console monitoring...');

    // Create a simple HTML page with console monitoring
    const monitoringScript = `
<!DOCTYPE html>
<html>
<head>
    <title>Autonomous Debug Monitor</title>
    <style>
        body { font-family: monospace; background: #1a1a1a; color: #00ff00; padding: 20px; }
        .error { color: #ff4444; }
        .warn { color: #ffaa00; }
        .info { color: #4444ff; }
        .log { color: #00ff00; }
        #console { height: 400px; overflow-y: scroll; border: 1px solid #333; padding: 10px; }
        #controls { margin-bottom: 20px; }
        button { background: #333; color: #fff; border: 1px solid #555; padding: 10px; margin: 5px; }
    </style>
</head>
<body>
    <h1>🚀 Autonomous Debug Monitor</h1>
    <div id="controls">
        <button onclick="clearConsole()">Clear Console</button>
        <button onclick="testError()">Test Error</button>
        <button onclick="testWarning()">Test Warning</button>
        <button onclick="exportLogs()">Export Logs</button>
    </div>
    <div id="console"></div>
    
    <script>
        const consoleDiv = document.getElementById('console');
        const originalConsole = { ...console };
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toISOString();
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = \`[\${timestamp}] [\${type.toUpperCase()}] \${message}\`;
            consoleDiv.appendChild(div);
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
            
            // Send to parent if in debug mode
            if (window.autonomousDebugger) {
                window.autonomousDebugger.logConsoleMessage(message, type);
            }
        }
        
        // Override console methods
        console.log = function(...args) {
            originalConsole.log.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalConsole.error.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalConsole.warn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };
        
        console.info = function(...args) {
            originalConsole.info.apply(console, args);
            addToConsole(args.join(' '), 'info');
        };
        
        // Catch unhandled errors
        window.onerror = function(message, source, lineno, colno, error) {
            addToConsole(\`Error: \${message} at \${source}:\${lineno}:\${colno}\`, 'error');
            return false;
        };
        
        // Catch unhandled promise rejections
        window.addEventListener('unhandledrejection', function(event) {
            addToConsole(\`Unhandled Promise Rejection: \${event.reason}\`, 'error');
        });
        
        function clearConsole() {
            consoleDiv.innerHTML = '';
        }
        
        function testError() {
            console.error('This is a test error message');
        }
        
        function testWarning() {
            console.warn('This is a test warning message');
        }
        
        function exportLogs() {
            const logs = Array.from(consoleDiv.children).map(div => div.textContent).join('\\n');
            const blob = new Blob([logs], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'debug-logs.txt';
            a.click();
            URL.revokeObjectURL(url);
        }
        
        // Initialize
        console.log('Autonomous Debug Monitor initialized');
        console.log('Server URL: http://localhost:5173');
        console.log('Monitor ready for debugging...');
    </script>
</body>
</html>
    `;

    // Write the monitoring page
    const monitorPath = path.join(__dirname, '../static/debug-monitor.html');
    fs.writeFileSync(monitorPath, monitoringScript);

    this.log(`Debug monitor page created at: ${monitorPath}`);
  }

  async startAutonomousMode() {
    this.log('🚀 Starting Autonomous Debug Mode...');
    this.isRunning = true;

    try {
      // Start the server
      await this.startServer();

      // Setup console monitoring
      this.setupConsoleMonitoring();

      // Open debug monitor in browser
      setTimeout(() => {
        this.openDebugMonitor();
      }, 2000);

      // Start monitoring loop
      this.startMonitoringLoop();

      this.log('✅ Autonomous Debug Mode is now active!', 'SUCCESS');
      this.log('🌐 Server: http://localhost:5173', 'INFO');
      this.log(
        '🔍 Debug Monitor: http://localhost:5173/debug-monitor.html',
        'INFO'
      );
    } catch (error) {
      this.log(
        `Failed to start autonomous debug mode: ${error.message}`,
        'ERROR'
      );
      process.exit(1);
    }
  }

  openDebugMonitor() {
    const debugUrl = 'http://localhost:5173/debug-monitor.html';

    // Try to open in browser using the $BROWSER environment variable or default browser
    const openCommand = process.env.BROWSER
      ? `"${process.env.BROWSER}" "${debugUrl}"`
      : `xdg-open "${debugUrl}" || open "${debugUrl}" || start "${debugUrl}"`;

    exec(openCommand, error => {
      if (error) {
        this.log(`Could not auto-open browser: ${error.message}`, 'WARN');
        this.log(`Please manually open: ${debugUrl}`, 'INFO');
      } else {
        this.log('Debug monitor opened in browser', 'SUCCESS');
      }
    });
  }

  startMonitoringLoop() {
    setInterval(() => {
      this.checkServerHealth();
      this.analyzeErrors();
    }, 10000); // Check every 10 seconds
  }

  checkServerHealth() {
    const { exec } = require('child_process');

    exec(
      'curl -s -o /dev/null -w "%{http_code}" http://localhost:5173',
      (error, stdout) => {
        if (error || stdout !== '200') {
          this.log('Server health check failed', 'WARN');
        }
      }
    );
  }

  analyzeErrors() {
    if (this.consoleErrors.length > 0) {
      this.log(
        `Found ${this.consoleErrors.length} console errors for analysis`,
        'INFO'
      );

      // Basic error analysis
      const errorPatterns = {
        network: /network|fetch|connection|cors/i,
        syntax: /syntax|parse|unexpected/i,
        reference: /reference|undefined|null/i,
        type: /type|expected|invalid/i,
      };

      this.consoleErrors.forEach(error => {
        for (const [type, pattern] of Object.entries(errorPatterns)) {
          if (pattern.test(error)) {
            this.log(
              `Detected ${type} error: ${error.substring(0, 100)}...`,
              'ANALYSIS'
            );
            break;
          }
        }
      });

      // Clear analyzed errors
      this.consoleErrors = [];
    }
  }

  stop() {
    this.log('Stopping Autonomous Debug Mode...');
    this.isRunning = false;

    if (this.serverProcess) {
      this.serverProcess.kill();
      this.log('Server process terminated');
    }

    this.log('Autonomous Debug Mode stopped', 'INFO');
  }
}

// CLI interface
if (require.main === module) {
  const autonomousDebugger = new AutonomousDebugger();

  process.on('SIGINT', () => {
    autonomousDebugger.stop();
    process.exit(0);
  });

  process.on('SIGTERM', () => {
    autonomousDebugger.stop();
    process.exit(0);
  });

  autonomousDebugger.startAutonomousMode().catch(error => {
    console.error('Failed to start autonomous debug mode:', error);
    process.exit(1);
  });
}

module.exports = AutonomousDebugger;
