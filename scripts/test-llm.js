#!/usr/bin/env node

/**
 * Test LLM Integration
 * Quick test to verify <PERSON><PERSON><PERSON> is working
 */

async function testOllama() {
  console.log('🧪 Testing Ollama LLM Integration...');
  
  try {
    // Test with a smaller, faster model first
    const response = await fetch('http://localhost:11434/api/generate', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        model: 'qwen3:latest', // Use the smaller 8B model for faster response
        prompt: 'Hello! Please respond with just "AI is working!"',
        stream: false,
        options: {
          temperature: 0.1,
          num_predict: 10
        }
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    console.log('✅ Ollama Response:', data.response);
    console.log('📊 Tokens:', data.eval_count);
    console.log('⏱️  Duration:', Math.round(data.total_duration / 1000000) + 'ms');
    
    return true;
  } catch (error) {
    console.error('❌ Ollama Test Failed:', error.message);
    return false;
  }
}

async function testAvailableModels() {
  console.log('\n📋 Checking Available Models...');
  
  try {
    const response = await fetch('http://localhost:11434/api/tags');
    const data = await response.json();
    
    console.log('Available models:');
    data.models.forEach(model => {
      console.log(`  - ${model.name} (${Math.round(model.size / 1024 / 1024 / 1024 * 10) / 10}GB)`);
    });
    
    return data.models;
  } catch (error) {
    console.error('❌ Failed to get models:', error.message);
    return [];
  }
}

async function main() {
  console.log('🚀 VybeCoding.ai LLM Integration Test\n');
  
  // Test available models
  const models = await testAvailableModels();
  
  if (models.length === 0) {
    console.log('❌ No models available. Is Ollama running?');
    process.exit(1);
  }
  
  // Test Ollama generation
  const ollamaWorking = await testOllama();
  
  if (ollamaWorking) {
    console.log('\n✅ LLM Integration Test PASSED!');
    console.log('🎯 Ready for content generation');
  } else {
    console.log('\n❌ LLM Integration Test FAILED!');
    console.log('🔧 Check Ollama service and model availability');
    process.exit(1);
  }
}

main().catch(console.error);
