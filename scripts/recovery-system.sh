#!/bin/bash
# VybeCoding.ai Recovery System
# Comprehensive disaster recovery and restoration system

set -e

# Configuration
BACKUP_DIR="${BACKUP_DIR:-/backups}"
RECOVERY_DATE="${1:-latest}"
RECOVERY_TYPE="${2:-full}"
LOG_FILE="${BACKUP_DIR}/recovery.log"
ENCRYPTION_KEY="${BACKUP_ENCRYPTION_KEY:-}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1" | tee -a "$LOG_FILE"
}

info() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] INFO:${NC} $1" | tee -a "$LOG_FILE"
}

# Safety confirmation function
confirm_recovery() {
    echo -e "${RED}⚠️  DISASTER RECOVERY WARNING ⚠️${NC}"
    echo "This will restore VybeCoding.ai from backup and may overwrite current data."
    echo ""
    echo "Recovery Details:"
    echo "  Date: $RECOVERY_DATE"
    echo "  Type: $RECOVERY_TYPE"
    echo "  Backup Directory: $BACKUP_DIR"
    echo ""
    
    if [ "$RECOVERY_DATE" = "latest" ]; then
        local latest_backup=$(find "$BACKUP_DIR" -name "db_backup_*.sql.gz*" -o -name "db_backup_*.sql.gz.enc" | sort | tail -1)
        if [ -n "$latest_backup" ]; then
            local backup_date=$(basename "$latest_backup" | sed 's/db_backup_\(.*\)\.sql\.gz.*/\1/')
            echo "  Latest Backup Found: $backup_date"
        else
            error "No backup files found in $BACKUP_DIR"
            exit 1
        fi
    fi
    
    echo ""
    read -p "Are you absolutely sure you want to proceed with recovery? (type 'YES' to confirm): " -r
    if [[ ! $REPLY == "YES" ]]; then
        echo "Recovery cancelled by user"
        exit 1
    fi
    
    echo ""
    log "Recovery confirmed by user, proceeding..."
}

# Pre-recovery checks
pre_recovery_checks() {
    log "Performing pre-recovery checks..."
    
    # Check if backup directory exists
    if [ ! -d "$BACKUP_DIR" ]; then
        error "Backup directory not found: $BACKUP_DIR"
        exit 1
    fi
    
    # Check for required tools
    for tool in docker gzip tar; do
        if ! command -v "$tool" &> /dev/null; then
            error "Required tool '$tool' is not installed"
            exit 1
        fi
    done
    
    # Check available disk space
    available_space=$(df . | awk 'NR==2 {print $4}')
    required_space=2097152  # 2GB in KB
    
    if [ "$available_space" -lt "$required_space" ]; then
        error "Insufficient disk space for recovery. Available: ${available_space}KB, Required: ${required_space}KB"
        exit 1
    fi
    
    log "Pre-recovery checks completed successfully"
}

# Find backup files function
find_backup_files() {
    local date_pattern="$1"
    
    if [ "$date_pattern" = "latest" ]; then
        # Find latest backup files
        DB_BACKUP_FILE=$(find "$BACKUP_DIR" -name "db_backup_*.sql.gz" -o -name "db_backup_*.sql.gz.enc" | sort | tail -1)
        UPLOADS_BACKUP_FILE=$(find "$BACKUP_DIR" -name "uploads_backup_*.tar.gz" | sort | tail -1)
        CONFIG_BACKUP_FILE=$(find "$BACKUP_DIR" -name "config_backup_*.tar.gz" | sort | tail -1)
        LOGS_BACKUP_FILE=$(find "$BACKUP_DIR" -name "logs_backup_*.tar.gz" | sort | tail -1)
    else
        # Find specific date backup files
        DB_BACKUP_FILE="${BACKUP_DIR}/db_backup_${date_pattern}.sql.gz"
        if [ ! -f "$DB_BACKUP_FILE" ]; then
            DB_BACKUP_FILE="${BACKUP_DIR}/db_backup_${date_pattern}.sql.gz.enc"
        fi
        UPLOADS_BACKUP_FILE="${BACKUP_DIR}/uploads_backup_${date_pattern}.tar.gz"
        CONFIG_BACKUP_FILE="${BACKUP_DIR}/config_backup_${date_pattern}.tar.gz"
        LOGS_BACKUP_FILE="${BACKUP_DIR}/logs_backup_${date_pattern}.tar.gz"
    fi
    
    # Verify database backup exists
    if [ ! -f "$DB_BACKUP_FILE" ]; then
        error "Database backup file not found: $DB_BACKUP_FILE"
        exit 1
    fi
    
    log "Found backup files:"
    log "  Database: $DB_BACKUP_FILE"
    [ -f "$UPLOADS_BACKUP_FILE" ] && log "  Uploads: $UPLOADS_BACKUP_FILE"
    [ -f "$CONFIG_BACKUP_FILE" ] && log "  Config: $CONFIG_BACKUP_FILE"
    [ -f "$LOGS_BACKUP_FILE" ] && log "  Logs: $LOGS_BACKUP_FILE"
}

# Create recovery point function
create_recovery_point() {
    log "Creating recovery point before restoration..."
    
    local recovery_point_dir="${BACKUP_DIR}/recovery_point_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$recovery_point_dir"
    
    # Backup current database if possible
    if docker ps | grep -q appwrite; then
        log "Creating current database snapshot..."
        docker exec appwrite-db pg_dump -U postgres vybecoding > "${recovery_point_dir}/current_db.sql" 2>/dev/null || true
    fi
    
    # Backup current configuration files
    config_files=(".env" ".env.production" "package.json" "docker-compose.yml")
    for file in "${config_files[@]}"; do
        if [ -f "$file" ]; then
            cp "$file" "$recovery_point_dir/" 2>/dev/null || true
        fi
    done
    
    log "Recovery point created at: $recovery_point_dir"
    echo "$recovery_point_dir" > "${BACKUP_DIR}/.last_recovery_point"
}

# Database recovery function
recover_database() {
    log "Starting database recovery..."
    
    local temp_sql_file="/tmp/recovery_db_$(date +%s).sql"
    
    # Decrypt and decompress backup file
    if [[ "$DB_BACKUP_FILE" == *.enc ]]; then
        if [ -z "$ENCRYPTION_KEY" ]; then
            error "Encrypted backup found but no encryption key provided"
            exit 1
        fi
        log "Decrypting database backup..."
        openssl enc -aes-256-cbc -d -in "$DB_BACKUP_FILE" -out "${DB_BACKUP_FILE%.enc}" -k "$ENCRYPTION_KEY"
        DB_BACKUP_FILE="${DB_BACKUP_FILE%.enc}"
    fi
    
    log "Decompressing database backup..."
    gunzip -c "$DB_BACKUP_FILE" > "$temp_sql_file"
    
    # Stop application services
    log "Stopping application services..."
    docker-compose down 2>/dev/null || true
    
    # Wait for services to stop
    sleep 10
    
    # Start database service only
    log "Starting database service..."
    docker-compose up -d appwrite-db 2>/dev/null || docker-compose up -d db 2>/dev/null || {
        warning "Could not start database via docker-compose, attempting manual start"
    }
    
    # Wait for database to be ready
    log "Waiting for database to be ready..."
    local max_attempts=30
    local attempt=0
    
    while [ $attempt -lt $max_attempts ]; do
        if docker exec appwrite-db pg_isready -U postgres 2>/dev/null; then
            break
        fi
        sleep 2
        ((attempt++))
    done
    
    if [ $attempt -eq $max_attempts ]; then
        error "Database failed to start within timeout"
        exit 1
    fi
    
    # Restore database
    log "Restoring database from backup..."
    if docker exec -i appwrite-db psql -U postgres -d vybecoding < "$temp_sql_file"; then
        log "✅ Database recovery completed successfully"
    else
        error "❌ Database recovery failed"
        rm -f "$temp_sql_file"
        exit 1
    fi
    
    # Cleanup
    rm -f "$temp_sql_file"
}

# File system recovery function
recover_files() {
    log "Starting file system recovery..."
    
    # Restore uploads
    if [ -f "$UPLOADS_BACKUP_FILE" ]; then
        log "Restoring uploads..."
        if tar -xzf "$UPLOADS_BACKUP_FILE" -C / 2>/dev/null; then
            log "✅ Uploads restored successfully"
        else
            warning "Failed to restore uploads"
        fi
    else
        warning "No uploads backup found, skipping"
    fi
    
    # Restore configuration
    if [ -f "$CONFIG_BACKUP_FILE" ]; then
        log "Restoring configuration files..."
        if tar -xzf "$CONFIG_BACKUP_FILE" -C . 2>/dev/null; then
            log "✅ Configuration restored successfully"
        else
            warning "Failed to restore configuration"
        fi
    else
        warning "No configuration backup found, skipping"
    fi
    
    # Restore logs (optional)
    if [ -f "$LOGS_BACKUP_FILE" ] && [ "$RECOVERY_TYPE" = "full" ]; then
        log "Restoring logs..."
        if tar -xzf "$LOGS_BACKUP_FILE" -C / 2>/dev/null; then
            log "✅ Logs restored successfully"
        else
            warning "Failed to restore logs"
        fi
    fi
}

# Post-recovery health check function
post_recovery_health_check() {
    log "Performing post-recovery health checks..."
    
    # Start all application services
    log "Starting all application services..."
    docker-compose up -d
    
    # Wait for services to start
    log "Waiting for services to initialize..."
    sleep 30
    
    local health_check_passed=true
    
    # Check database connectivity
    if docker exec appwrite-db pg_isready -U postgres 2>/dev/null; then
        log "✅ Database health check passed"
    else
        error "❌ Database health check failed"
        health_check_passed=false
    fi
    
    # Check application health endpoint
    local max_attempts=10
    local attempt=0
    
    while [ $attempt -lt $max_attempts ]; do
        if curl -f http://localhost:3000/health &>/dev/null || curl -f http://localhost:3000/api/health &>/dev/null; then
            log "✅ Application health check passed"
            break
        fi
        sleep 5
        ((attempt++))
    done
    
    if [ $attempt -eq $max_attempts ]; then
        error "❌ Application health check failed"
        health_check_passed=false
    fi
    
    # Check container status
    local unhealthy_containers=$(docker ps --filter "health=unhealthy" --format "table {{.Names}}" | tail -n +2)
    if [ -n "$unhealthy_containers" ]; then
        error "❌ Unhealthy containers detected: $unhealthy_containers"
        health_check_passed=false
    else
        log "✅ All containers are healthy"
    fi
    
    if [ "$health_check_passed" = false ]; then
        error "Post-recovery health checks failed"
        exit 1
    fi
    
    log "✅ All post-recovery health checks passed"
}

# Send recovery notification
send_recovery_notification() {
    local status="$1"
    local message="$2"
    
    # Slack notification
    if [ -n "$SLACK_WEBHOOK_URL" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"🔄 VybeCoding.ai Recovery $status: $message\"}" \
            "$SLACK_WEBHOOK_URL" &>/dev/null || true
    fi
    
    # Discord notification
    if [ -n "$DISCORD_WEBHOOK_URL" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"content\":\"🔄 VybeCoding.ai Recovery $status: $message\"}" \
            "$DISCORD_WEBHOOK_URL" &>/dev/null || true
    fi
}

# Main recovery process
main() {
    local start_time=$(date)
    log "Starting recovery process at $start_time"
    log "Recovery parameters: Date=$RECOVERY_DATE, Type=$RECOVERY_TYPE"
    
    # Safety checks and confirmation
    confirm_recovery
    pre_recovery_checks
    find_backup_files "$RECOVERY_DATE"
    create_recovery_point
    
    # Perform recovery
    if [ "$RECOVERY_TYPE" = "database-only" ]; then
        recover_database
    elif [ "$RECOVERY_TYPE" = "files-only" ]; then
        recover_files
    else
        # Full recovery
        recover_database
        recover_files
    fi
    
    # Post-recovery validation
    post_recovery_health_check
    
    local end_time=$(date)
    local success_message="Recovery completed successfully at $end_time (Type: $RECOVERY_TYPE, Date: $RECOVERY_DATE)"
    log "$success_message"
    send_recovery_notification "Success" "$success_message"
    
    echo ""
    echo -e "${GREEN}🎉 Recovery completed successfully!${NC}"
    echo "Recovery type: $RECOVERY_TYPE"
    echo "Backup date: $RECOVERY_DATE"
    echo "Started: $start_time"
    echo "Completed: $end_time"
}

# Handle script arguments
case "${1:-}" in
    --help|-h)
        echo "VybeCoding.ai Recovery System"
        echo "Usage: $0 [backup_date] [recovery_type] [options]"
        echo ""
        echo "Arguments:"
        echo "  backup_date         Backup date (YYYYMMDD_HHMMSS) or 'latest' (default: latest)"
        echo "  recovery_type       Recovery type: full, database-only, files-only (default: full)"
        echo ""
        echo "Options:"
        echo "  --help, -h          Show this help message"
        echo "  --list              List available backups"
        echo "  --verify            Verify backup integrity"
        echo ""
        echo "Examples:"
        echo "  $0                           # Restore from latest backup (full recovery)"
        echo "  $0 latest database-only      # Restore database only from latest backup"
        echo "  $0 20240115_120000          # Restore from specific backup date"
        echo "  $0 --list                   # List available backups"
        exit 0
        ;;
    --list)
        echo "Available backups in $BACKUP_DIR:"
        find "$BACKUP_DIR" -name "db_backup_*.sql.gz*" | sort | while read -r backup; do
            local date=$(basename "$backup" | sed 's/db_backup_\(.*\)\.sql\.gz.*/\1/')
            local size=$(du -h "$backup" | cut -f1)
            echo "  $date ($size)"
        done
        exit 0
        ;;
    --verify)
        echo "Verifying backup integrity..."
        find "$BACKUP_DIR" -name "*.gz" -exec gzip -t {} \; -print
        find "$BACKUP_DIR" -name "*.tar.gz" -exec tar -tzf {} \; -print > /dev/null
        echo "Backup verification completed"
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
