#!/bin/bash
# VybeCoding.ai Documentation Generation Script
# Generates comprehensive API and code documentation

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DOCS_DIR="$PROJECT_ROOT/docs"
API_DOCS_DIR="$DOCS_DIR/api"
COMPONENTS_DOCS_DIR="$DOCS_DIR/components"

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_dependencies() {
    log "Checking documentation generation dependencies..."
    
    local missing_deps=()
    
    if ! command -v node &> /dev/null; then
        missing_deps+=("node")
    fi
    
    if ! command -v npm &> /dev/null; then
        missing_deps+=("npm")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        error "Missing dependencies: ${missing_deps[*]}"
        error "Please install the missing dependencies and try again."
        exit 1
    fi
    
    log "All dependencies are available."
}

# Install documentation dependencies if needed
install_doc_dependencies() {
    log "Installing documentation dependencies..."
    
    cd "$PROJECT_ROOT"
    
    # Check if documentation dependencies are installed
    if ! npm list swagger-jsdoc &> /dev/null; then
        info "Installing swagger-jsdoc..."
        npm install --save-dev swagger-jsdoc
    fi
    
    if ! npm list typedoc &> /dev/null; then
        info "Installing typedoc..."
        npm install --save-dev typedoc
    fi
    
    if ! npm list sveld &> /dev/null; then
        info "Installing sveld..."
        npm install --save-dev sveld
    fi
    
    log "Documentation dependencies installed."
}

# Generate OpenAPI/Swagger documentation
generate_api_docs() {
    log "Generating API documentation..."
    
    cd "$PROJECT_ROOT"
    
    # Create API docs directory
    mkdir -p "$API_DOCS_DIR"
    
    # Generate OpenAPI specification
    info "Generating OpenAPI specification..."
    npx swagger-jsdoc -d "$API_DOCS_DIR/swagger-config.js" -o "$API_DOCS_DIR/openapi.yaml" "src/routes/api/**/*.ts"

    if [ $? -eq 0 ]; then
        log "OpenAPI specification generated successfully: $API_DOCS_DIR/openapi.yaml"
    else
        error "Failed to generate OpenAPI specification"
        return 1
    fi

    # Generate Swagger UI
    info "Generating Swagger UI..."
    mkdir -p "$API_DOCS_DIR/swagger-ui"
    if command -v swagger-ui-dist-cli &> /dev/null; then
        npx swagger-ui-dist-cli -f "$API_DOCS_DIR/openapi.yaml" -d "$API_DOCS_DIR/swagger-ui"
        log "Swagger UI generated: $API_DOCS_DIR/swagger-ui"
    else
        warning "swagger-ui-dist-cli not available, skipping Swagger UI generation"
    fi

    # Generate ReDoc documentation
    info "Generating ReDoc documentation..."
    if command -v redoc-cli &> /dev/null; then
        npx redoc-cli build "$API_DOCS_DIR/openapi.yaml" --output "$API_DOCS_DIR/redoc.html"
        log "ReDoc documentation generated: $API_DOCS_DIR/redoc.html"
    else
        warning "redoc-cli not available, skipping ReDoc generation"
    fi

    # Generate Postman collection
    info "Generating Postman collection..."
    if command -v openapi-to-postman &> /dev/null; then
        npx openapi-to-postman -s "$API_DOCS_DIR/openapi.yaml" -o "$API_DOCS_DIR/postman-collection.json"
        log "Postman collection generated: $API_DOCS_DIR/postman-collection.json"
    else
        warning "openapi-to-postman not available, skipping Postman collection generation"
    fi
}

# Generate TypeScript documentation
generate_typescript_docs() {
    log "Generating TypeScript documentation..."
    
    cd "$PROJECT_ROOT"
    
    # Generate TypeDoc documentation
    info "Running TypeDoc..."
    npx typedoc --out "$API_DOCS_DIR/typescript" src/lib
    
    if [ $? -eq 0 ]; then
        log "TypeScript documentation generated: $API_DOCS_DIR/typescript"
    else
        error "Failed to generate TypeScript documentation"
        return 1
    fi
}

# Generate Svelte component documentation
generate_component_docs() {
    log "Generating Svelte component documentation..."

    cd "$PROJECT_ROOT"

    # Create components docs directory
    mkdir -p "$COMPONENTS_DOCS_DIR"

    # Generate component documentation with sveld
    info "Running sveld for component documentation..."
    npx sveld --glob "src/lib/components/**/*.svelte" --output "$COMPONENTS_DOCS_DIR"

    if [ $? -eq 0 ]; then
        log "Component documentation generated: $COMPONENTS_DOCS_DIR"
    else
        error "Failed to generate component documentation"
        return 1
    fi
}

# Generate Storybook documentation
generate_storybook_docs() {
    log "Generating Storybook documentation..."

    cd "$PROJECT_ROOT"

    # Build Storybook
    info "Building Storybook..."
    if command -v storybook &> /dev/null; then
        npx storybook build -o "$DOCS_DIR/storybook"

        if [ $? -eq 0 ]; then
            log "Storybook documentation generated: $DOCS_DIR/storybook"
        else
            error "Failed to generate Storybook documentation"
            return 1
        fi
    else
        warning "Storybook not available, skipping Storybook generation"
    fi
}

# Validate generated documentation
validate_docs() {
    log "Validating generated documentation..."
    
    # Validate OpenAPI specification
    if [ -f "$API_DOCS_DIR/openapi.yaml" ]; then
        info "Validating OpenAPI specification..."
        if command -v swagger-codegen &> /dev/null; then
            swagger-codegen validate -i "$API_DOCS_DIR/openapi.yaml"
            if [ $? -eq 0 ]; then
                log "OpenAPI specification is valid"
            else
                warning "OpenAPI specification validation failed"
            fi
        else
            warning "swagger-codegen not available, skipping OpenAPI validation"
        fi
    fi
    
    # Check for broken links in markdown files
    if command -v markdown-link-check &> /dev/null; then
        info "Checking for broken links in documentation..."
        find "$DOCS_DIR" -name "*.md" -exec markdown-link-check {} \;
    else
        warning "markdown-link-check not available, skipping link validation"
    fi
}

# Generate documentation summary
generate_summary() {
    log "Generating documentation summary..."
    
    local summary_file="$DOCS_DIR/documentation-summary.md"
    
    cat > "$summary_file" << EOF
# VybeCoding.ai Documentation Summary

Generated on: $(date)

## 📚 Available Documentation

### API Documentation
- **OpenAPI Specification**: [openapi.yaml](api/openapi.yaml)
- **TypeScript API Docs**: [typescript/](api/typescript/)
- **Postman Collection**: [postman-collection.json](api/postman-collection.json)

### Component Documentation
- **Svelte Components**: [components/](components/)

### Project Documentation
- **Project Overview**: [project-info.md](project-info.md)
- **Setup Guide**: [setup-guide.md](setup-guide.md)
- **Architecture**: [architecture.md](architecture.md)
- **Backup & Recovery**: [backup-recovery.md](backup-recovery.md)

## 🔗 Quick Links

- **API Health Check**: \`GET /api/health\`
- **System Metrics**: \`GET /api/metrics\`
- **Security Status**: \`GET /api/security\`

## 📊 Documentation Statistics

EOF

    # Add file counts
    echo "- **Total Markdown Files**: $(find "$DOCS_DIR" -name "*.md" | wc -l)" >> "$summary_file"
    echo "- **API Endpoints Documented**: $(grep -r "@swagger" src/routes/api/ | wc -l)" >> "$summary_file"
    echo "- **Components Documented**: $(find src/lib/components -name "*.svelte" | wc -l)" >> "$summary_file"
    
    log "Documentation summary generated: $summary_file"
}

# Main execution
main() {
    log "Starting VybeCoding.ai documentation generation..."

    check_dependencies
    install_doc_dependencies

    # Generate all documentation types
    generate_api_docs
    generate_typescript_docs
    generate_component_docs
    generate_storybook_docs

    # Validate and summarize
    validate_docs
    generate_summary

    log "Documentation generation completed successfully!"
    log "Documentation available in: $DOCS_DIR"

    # Show quick access URLs
    echo ""
    info "Quick Access:"
    info "- API Documentation: file://$API_DOCS_DIR/openapi.yaml"
    info "- Swagger UI: file://$API_DOCS_DIR/swagger-ui/index.html"
    info "- ReDoc: file://$API_DOCS_DIR/redoc.html"
    info "- TypeScript Docs: file://$API_DOCS_DIR/typescript/index.html"
    info "- Components Docs: file://$COMPONENTS_DOCS_DIR/index.html"
    info "- Storybook: file://$DOCS_DIR/storybook/index.html"
    info "- Documentation Summary: file://$DOCS_DIR/documentation-summary.md"
}

# Run main function
main "$@"
