#!/usr/bin/env python3
"""
Vybe Method CLI
Simple command interface for the Vybe Method system
"""

import asyncio
import json
import sys
from pathlib import Path

# Add method/vybe to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "method" / "vybe"))

try:
    from vybe_commands import VybeCommandSystem

    VYBE_AVAILABLE = True
except ImportError as e:
    VYBE_AVAILABLE = False
    print(f"❌ Vybe commands not available: {e}")


def print_banner():
    """Print Vybe Method banner"""
    print(
        """
╭─────────────────────────────────────────────────────────────╮
│                    🚀 VYBE METHOD CLI                       │
│                AI-Powered Development System                │
╰─────────────────────────────────────────────────────────────╯
"""
    )


def print_help():
    """Print help information"""
    print(
        """
📋 Available Commands:

🚀 Core Commands:
  /vybe start     → Initialize Vybe Method system
  /vybe status    → Check system health and agent availability  
  /vybe analyze   → Deep codebase analysis with unlimited context

🤖 Agent Commands:
  /vybe analyst   → Activate Wendy for project analysis
  /vybe pm        → Switch to Bill for requirements and PRD creation
  /vybe architect → Engage <PERSON><PERSON> for technical architecture design
  /vybe designer  → Work with <PERSON> for UI/UX specifications

🔧 Workflow Commands:
  /vybe collaborate <task>  → Multi-agent task execution
  /vybe consensus <decision> → 4-layer validation process

📊 System Commands:
  /vybe help      → Show this help message
  /vybe version   → Show version information

Examples:
  ./scripts/vybe start
  ./scripts/vybe status
  ./scripts/vybe analyze "authentication system"
"""
    )


def format_output(result):
    """Format command output for display"""
    if not isinstance(result, dict):
        return str(result)

    status = result.get("status", "unknown")

    if status == "success":
        print("✅ SUCCESS")
    elif status == "error":
        print("❌ ERROR")
    else:
        print(f"📊 STATUS: {status.upper()}")

    # Print main message
    if "message" in result:
        print(f"💬 {result['message']}")

    # Print components status for start/status commands
    if "components" in result:
        print("\n🔧 Components:")
        components = result["components"]

        if "mas_coordinator" in components:
            mas = components["mas_coordinator"]
            status_icon = "✅" if mas.get("available") else "❌"
            print(
                f"  {status_icon} MAS Coordinator: {mas.get('agents_count', 0)} agents"
            )

            if "agents_list" in mas:
                for agent in mas["agents_list"]:
                    print(f"    🤖 {agent}")

        if "context_engine" in components:
            ctx = components["context_engine"]
            status_icon = "✅" if ctx.get("available") else "❌"
            print(f"  {status_icon} Vector Context Engine")

    # Print analysis results
    if "analysis" in result:
        print(f"\n📊 Analysis Results (ID: {result.get('analysis_id', 'unknown')}):")
        print(f"⏱️  Execution time: {result.get('execution_time', 0):.2f}s")
        print(f"🎯 Target: {result.get('target', 'unknown')}")
        print("\n📝 Analysis:")

        # Show first 500 characters of analysis
        analysis = result["analysis"]
        if len(analysis) > 500:
            print(analysis[:500] + "...")
            print(f"\n[Full analysis: {len(analysis)} characters]")
        else:
            print(analysis)

    # Print timestamp
    if "timestamp" in result:
        print(f"\n🕐 {result['timestamp']}")


async def run_command(command, args):
    """Run a Vybe command"""
    if not VYBE_AVAILABLE:
        print("❌ Vybe system not available. Check installation.")
        return False

    vybe = VybeCommandSystem()

    try:
        if command == "start":
            result = await vybe.vybe_start()
        elif command == "status":
            result = await vybe.vybe_status()
        elif command == "analyze":
            target = args[0] if args else None
            result = await vybe.vybe_analyze(target)
        else:
            print(f"❌ Unknown command: {command}")
            print("💡 Use '/vybe help' for available commands")
            return False

        format_output(result)
        return result.get("status") == "success"

    except Exception as e:
        print(f"❌ Command failed: {e}")
        return False


def main():
    """Main CLI entry point"""
    # Parse command line arguments
    if len(sys.argv) < 2:
        print_banner()
        print_help()
        return

    # Handle /vybe prefix
    command = sys.argv[1]
    if command.startswith("/vybe"):
        command = command[6:]  # Remove '/vybe ' prefix

    args = sys.argv[2:] if len(sys.argv) > 2 else []

    # Handle special commands
    if command in ["help", "--help", "-h"]:
        print_banner()
        print_help()
        return
    elif command in ["version", "--version", "-v"]:
        print("🚀 Vybe Method CLI v1.0.0")
        print("🏗️  VybeCoding.ai AI Education Platform")
        return

    # Run async command
    print_banner()
    success = asyncio.run(run_command(command, args))

    if not success:
        sys.exit(1)


if __name__ == "__main__":
    main()
