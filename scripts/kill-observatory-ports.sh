#!/bin/bash

echo "🛑 MAS Observatory Port Cleanup"
echo "═══════════════════════════════"

# Observatory ports that need to be free (excluding common dev tool ports)
# Removed: 80, 443 (web servers), 3000 (common dev port), 8000/8001 (common dev APIs)
OBSERVATORY_PORTS=(9090 19999 16686 5601 9200 6379)

# Function to safely kill Observatory-specific processes
kill_port() {
    local port=$1
    local processes=$(lsof -ti:$port 2>/dev/null || true)

    if [ -z "$processes" ]; then
        echo "✅ Port $port is free"
        return 0
    fi

    # Check if processes are Observatory-related before killing
    for pid in $processes; do
        local cmd=$(ps -p $pid -o comm= 2>/dev/null || true)
        local args=$(ps -p $pid -o args= 2>/dev/null || true)

        # Only kill if it's clearly Observatory/Docker related
        if [[ "$args" == *"prometheus"* ]] || [[ "$args" == *"grafana"* ]] || [[ "$args" == *"netdata"* ]] || [[ "$args" == *"jaeger"* ]] || [[ "$args" == *"kibana"* ]] || [[ "$args" == *"elasticsearch"* ]] || [[ "$args" == *"redis"* ]] || [[ "$cmd" == *"docker"* ]]; then
            echo "🔍 Killing Observatory process on port $port (PID: $pid)"
            kill -9 $pid 2>/dev/null || true
        else
            echo "⚠️  Skipping non-Observatory process on port $port (PID: $pid): $cmd"
        fi
    done

    sleep 1
}

echo "🤖 Stopping Observatory containers..."
docker-compose -f mas-observatory/docker-compose.yml down 2>/dev/null || true

echo "🧹 Freeing Observatory ports..."
for port in "${OBSERVATORY_PORTS[@]}"; do
    kill_port $port
done

echo ""
echo "✅ All Observatory ports are now free!"
echo "🚀 Ready to start Observatory services"
