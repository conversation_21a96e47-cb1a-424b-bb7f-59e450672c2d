#!/usr/bin/env python3
"""
Test 24/7 Autonomous Mode with Real LLM Models
Tests the complete autonomous content generation pipeline
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime

class AutonomousModeTest:
    def __init__(self):
        self.base_url = "http://localhost:5173"
        self.ollama_url = "http://localhost:11434"
        self.test_results = []
        
    async def test_ollama_direct(self, session):
        """Test Ollama API directly with confirmed working models"""
        print("🧪 Testing Ollama API directly...")
        
        models_to_test = [
            "qwen3:30b-a3b",
            "devstral:24b"
        ]
        
        for model in models_to_test:
            try:
                print(f"  Testing {model}...")
                start_time = time.time()
                
                async with session.post(f"{self.ollama_url}/api/generate", json={
                    "model": model,
                    "prompt": "Respond with exactly: 'Model working correctly'",
                    "stream": False,
                    "options": {
                        "temperature": 0.1,
                        "num_predict": 10
                    }
                }, timeout=30) as response:
                    if response.status == 200:
                        data = await response.json()
                        duration = time.time() - start_time
                        print(f"    ✅ {model}: {data.get('response', 'No response')[:50]}... ({duration:.1f}s)")
                        self.test_results.append({
                            "test": f"ollama_{model}",
                            "status": "success",
                            "duration": duration,
                            "response": data.get('response', '')[:100]
                        })
                    else:
                        print(f"    ❌ {model}: HTTP {response.status}")
                        self.test_results.append({
                            "test": f"ollama_{model}",
                            "status": "failed",
                            "error": f"HTTP {response.status}"
                        })
                        
            except Exception as e:
                print(f"    ❌ {model}: {str(e)}")
                self.test_results.append({
                    "test": f"ollama_{model}",
                    "status": "error",
                    "error": str(e)
                })
    
    async def test_vybe_content_generation(self, session):
        """Test VybeCoding.ai content generation API"""
        print("\n🎯 Testing VybeCoding.ai Content Generation...")
        
        test_requests = [
            {
                "name": "Course Generation",
                "payload": {
                    "input": "Create a beginner course on AI fundamentals",
                    "type": "text",
                    "outputTypes": ["course"],
                    "options": {
                        "autonomousMode": True,
                        "detectedType": {
                            "type": "course",
                            "confidence": 95,
                            "keywords": ["AI", "fundamentals", "beginner"]
                        }
                    }
                }
            },
            {
                "name": "News Article Generation", 
                "payload": {
                    "input": "Write about latest AI developments",
                    "type": "text",
                    "outputTypes": ["news"],
                    "options": {
                        "autonomousMode": True,
                        "detectedType": {
                            "type": "article",
                            "confidence": 90,
                            "keywords": ["AI", "developments", "news"]
                        }
                    }
                }
            }
        ]
        
        for test_case in test_requests:
            try:
                print(f"  Testing {test_case['name']}...")
                start_time = time.time()
                
                async with session.post(f"{self.base_url}/api/vybe/process-content", 
                                      json=test_case['payload'],
                                      timeout=60) as response:
                    if response.status == 200:
                        data = await response.json()
                        duration = time.time() - start_time
                        task_id = data.get('taskId', 'unknown')
                        print(f"    ✅ {test_case['name']}: Task {task_id} created ({duration:.1f}s)")
                        
                        # Wait a moment and check task status
                        await asyncio.sleep(2)
                        await self.check_task_status(session, task_id, test_case['name'])
                        
                    else:
                        error_text = await response.text()
                        print(f"    ❌ {test_case['name']}: HTTP {response.status} - {error_text[:100]}")
                        self.test_results.append({
                            "test": test_case['name'],
                            "status": "failed",
                            "error": f"HTTP {response.status}"
                        })
                        
            except Exception as e:
                print(f"    ❌ {test_case['name']}: {str(e)}")
                self.test_results.append({
                    "test": test_case['name'],
                    "status": "error", 
                    "error": str(e)
                })
    
    async def check_task_status(self, session, task_id, test_name):
        """Check the status of a content generation task"""
        try:
            async with session.get(f"{self.base_url}/api/vybe/task-status/{task_id}") as response:
                if response.status == 200:
                    data = await response.json()
                    status = data.get('status', 'unknown')
                    progress = data.get('progress', 0)
                    print(f"      📊 Task Status: {status} ({progress}%)")
                    
                    self.test_results.append({
                        "test": f"{test_name}_status",
                        "status": "success",
                        "task_status": status,
                        "progress": progress,
                        "task_id": task_id
                    })
                else:
                    print(f"      ❌ Failed to get task status: HTTP {response.status}")
                    
        except Exception as e:
            print(f"      ❌ Task status error: {str(e)}")
    
    async def test_mas_observatory(self, session):
        """Test MAS Observatory endpoints"""
        print("\n🔭 Testing MAS Observatory...")
        
        endpoints = [
            "/api/system/metrics",
            "/api/mas/agents/status", 
            "/api/docker/status"
        ]
        
        for endpoint in endpoints:
            try:
                async with session.get(f"{self.base_url}{endpoint}") as response:
                    if response.status == 200:
                        data = await response.json()
                        print(f"    ✅ {endpoint}: OK")
                        self.test_results.append({
                            "test": f"observatory_{endpoint.split('/')[-1]}",
                            "status": "success"
                        })
                    else:
                        print(f"    ❌ {endpoint}: HTTP {response.status}")
                        self.test_results.append({
                            "test": f"observatory_{endpoint.split('/')[-1]}",
                            "status": "failed",
                            "error": f"HTTP {response.status}"
                        })
                        
            except Exception as e:
                print(f"    ❌ {endpoint}: {str(e)}")
                self.test_results.append({
                    "test": f"observatory_{endpoint.split('/')[-1]}",
                    "status": "error",
                    "error": str(e)
                })
    
    async def run_full_test(self):
        """Run complete autonomous mode test suite"""
        print("🚀 VybeCoding.ai 24/7 Autonomous Mode Test")
        print("=" * 50)
        print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        async with aiohttp.ClientSession() as session:
            # Test Ollama directly
            await self.test_ollama_direct(session)
            
            # Test VybeCoding.ai content generation
            await self.test_vybe_content_generation(session)
            
            # Test MAS Observatory
            await self.test_mas_observatory(session)
        
        # Generate test report
        self.generate_report()
    
    def generate_report(self):
        """Generate comprehensive test report"""
        print("\n" + "=" * 50)
        print("📊 TEST RESULTS SUMMARY")
        print("=" * 50)
        
        total_tests = len(self.test_results)
        successful_tests = len([r for r in self.test_results if r['status'] == 'success'])
        failed_tests = len([r for r in self.test_results if r['status'] in ['failed', 'error']])
        
        print(f"Total Tests: {total_tests}")
        print(f"✅ Successful: {successful_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"Success Rate: {(successful_tests/total_tests*100):.1f}%")
        
        print("\nDetailed Results:")
        for result in self.test_results:
            status_icon = "✅" if result['status'] == 'success' else "❌"
            print(f"  {status_icon} {result['test']}: {result['status']}")
            if 'error' in result:
                print(f"      Error: {result['error']}")
            if 'duration' in result:
                print(f"      Duration: {result['duration']:.1f}s")
        
        # Save results to file
        with open('autonomous-mode-test-results.json', 'w') as f:
            json.dump({
                "timestamp": datetime.now().isoformat(),
                "summary": {
                    "total_tests": total_tests,
                    "successful": successful_tests,
                    "failed": failed_tests,
                    "success_rate": successful_tests/total_tests*100
                },
                "detailed_results": self.test_results
            }, f, indent=2)
        
        print(f"\n📁 Detailed results saved to: autonomous-mode-test-results.json")
        
        if successful_tests == total_tests:
            print("\n🎉 ALL TESTS PASSED! 24/7 Autonomous Mode is READY!")
        else:
            print(f"\n⚠️  {failed_tests} tests failed. Review errors above.")

async def main():
    tester = AutonomousModeTest()
    await tester.run_full_test()

if __name__ == "__main__":
    asyncio.run(main())
