#!/bin/bash

# Setup Git Automation for VybeCoding.ai
# Configures automated milestone system and git hooks

set -e

echo "🚀 Setting up Git automation for VybeCoding.ai..."

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[SETUP]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check if we're in a git repository
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    echo "❌ Not in a git repository. Please run 'git init' first."
    exit 1
fi

# Create necessary directories
print_status "Creating directories..."
mkdir -p .milestones
mkdir -p scripts/git-hooks
mkdir -p .github/workflows

# Install git hooks
print_status "Installing git hooks..."
if [ -f "scripts/git-hooks/pre-commit" ]; then
    cp scripts/git-hooks/pre-commit .git/hooks/pre-commit
    chmod +x .git/hooks/pre-commit
    print_success "Pre-commit hook installed"
fi

# Create GitHub Actions workflow for automated testing
print_status "Creating GitHub Actions workflow..."
cat > .github/workflows/milestone-validation.yml << 'EOF'
name: Milestone Validation

on:
  push:
    branches: [ main, 'milestone-*' ]
  pull_request:
    branches: [ main ]

jobs:
  validate:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'
        cache-dependency-path: 'src/package-lock.json'
    
    - name: Install dependencies
      run: |
        cd src
        npm ci
    
    - name: Run tests
      run: |
        cd src
        npm test -- --run
    
    - name: Type check
      run: |
        cd src
        npx tsc --noEmit
    
    - name: Build check
      run: |
        cd src
        npm run build
    
    - name: Validate milestone
      if: startsWith(github.ref, 'refs/heads/milestone-')
      run: |
        echo "🎯 Validating milestone branch: ${{ github.ref_name }}"
        if [ -f ".milestones/$(echo ${{ github.ref_name }} | sed 's/milestone-/milestone-/' | cut -d'-' -f1-2).md" ]; then
          echo "✅ Milestone documentation found"
        else
          echo "❌ Milestone documentation missing"
          exit 1
        fi
EOF

# Create package.json scripts for milestone management
print_status "Adding npm scripts..."
if [ -f "src/package.json" ]; then
    # Add milestone scripts to package.json
    cd src
    npm pkg set scripts.milestone:create="cd .. && ./scripts/auto-milestone.sh create"
    npm pkg set scripts.milestone:list="cd .. && ./scripts/auto-milestone.sh list"
    npm pkg set scripts.milestone:rollback="cd .. && ./scripts/auto-milestone.sh rollback"
    cd ..
    print_success "Added milestone npm scripts"
fi

# Create VS Code tasks for easy milestone management
print_status "Creating VS Code tasks..."
mkdir -p .vscode
cat > .vscode/tasks.json << 'EOF'
{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "Create Milestone",
            "type": "shell",
            "command": "./scripts/auto-milestone.sh",
            "args": ["create", "${input:milestoneType}", "${input:milestoneDescription}"],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared"
            },
            "problemMatcher": []
        },
        {
            "label": "List Milestones",
            "type": "shell",
            "command": "./scripts/auto-milestone.sh",
            "args": ["list"],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared"
            }
        },
        {
            "label": "Rollback to Milestone",
            "type": "shell",
            "command": "./scripts/auto-milestone.sh",
            "args": ["rollback", "${input:milestoneNumber}"],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared"
            }
        }
    ],
    "inputs": [
        {
            "id": "milestoneType",
            "description": "Milestone type",
            "default": "story",
            "type": "pickString",
            "options": [
                "story",
                "feature",
                "bugfix",
                "release",
                "hotfix"
            ]
        },
        {
            "id": "milestoneDescription",
            "description": "Milestone description",
            "default": "",
            "type": "promptString"
        },
        {
            "id": "milestoneNumber",
            "description": "Milestone number to rollback to",
            "default": "001",
            "type": "promptString"
        }
    ]
}
EOF

# Create .gitignore additions
print_status "Updating .gitignore..."
cat >> .gitignore << 'EOF'

# Milestone system
.milestones/*.tmp
.milestone-temp/

# Development
*.log
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/settings.json
.idea/
EOF

# Set up git aliases for milestone management
print_status "Setting up git aliases..."
git config alias.milestone '!f() { ./scripts/auto-milestone.sh "$@"; }; f'
git config alias.ms '!f() { ./scripts/auto-milestone.sh "$@"; }; f'
git config alias.rollback '!f() { ./scripts/auto-milestone.sh rollback "$@"; }; f'

# Create initial milestone if none exists
if [ ! -d ".milestones" ] || [ -z "$(ls -A .milestones 2>/dev/null)" ]; then
    print_status "Creating initial milestone..."
    ./scripts/auto-milestone.sh create story "Initial VybeCoding.ai platform setup" force
fi

print_success "Git automation setup complete!"
echo
echo "🎯 Available commands:"
echo "  ./scripts/auto-milestone.sh create story 'Description'"
echo "  ./scripts/auto-milestone.sh list"
echo "  ./scripts/auto-milestone.sh rollback 001"
echo "  git milestone create story 'Description'"
echo "  npm run milestone:create (from src/)"
echo
echo "📝 VS Code users can use Ctrl+Shift+P -> 'Tasks: Run Task' -> 'Create Milestone'"
echo
echo "🔄 Automatic features enabled:"
echo "  ✅ Pre-commit validation hooks"
echo "  ✅ GitHub Actions CI/CD"
echo "  ✅ Milestone documentation"
echo "  ✅ Automated branching and tagging"
echo "  ✅ Easy rollback system"
