#!/bin/bash
# VybeCoding.ai Backup System Installer
# Automated installation and configuration of the backup and recovery system

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BACKUP_DIR="${BACKUP_DIR:-/backups}"
CONFIG_DIR="${PROJECT_ROOT}/config"
DOCS_DIR="${PROJECT_ROOT}/docs"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
}

warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

info() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] INFO:${NC} $1"
}

# Check if running as root
check_permissions() {
    if [ "$EUID" -eq 0 ]; then
        warning "Running as root. This is not recommended for security reasons."
        read -p "Continue anyway? (y/N): " -r
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    local missing_tools=()
    
    # Check required tools
    for tool in docker docker-compose gzip tar openssl curl; do
        if ! command -v "$tool" &> /dev/null; then
            missing_tools+=("$tool")
        fi
    done
    
    if [ ${#missing_tools[@]} -gt 0 ]; then
        error "Missing required tools: ${missing_tools[*]}"
        echo "Please install the missing tools and run this script again."
        exit 1
    fi
    
    # Check Docker is running
    if ! docker info &> /dev/null; then
        error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    
    log "✅ All prerequisites met"
}

# Create directories
create_directories() {
    log "Creating backup directories..."
    
    # Create backup directory
    if [ ! -d "$BACKUP_DIR" ]; then
        if [ -w "$(dirname "$BACKUP_DIR")" ]; then
            mkdir -p "$BACKUP_DIR"
            log "Created backup directory: $BACKUP_DIR"
        else
            error "Cannot create backup directory: $BACKUP_DIR"
            echo "Please run: sudo mkdir -p $BACKUP_DIR && sudo chown $USER:$USER $BACKUP_DIR"
            exit 1
        fi
    else
        log "Backup directory already exists: $BACKUP_DIR"
    fi
    
    # Set proper permissions
    chmod 700 "$BACKUP_DIR"
    log "Set backup directory permissions to 700"
    
    # Create subdirectories
    mkdir -p "${BACKUP_DIR}/reports"
    mkdir -p "${BACKUP_DIR}/temp"
    mkdir -p "${BACKUP_DIR}/recovery-points"
    
    log "✅ Directory structure created"
}

# Make scripts executable
setup_scripts() {
    log "Setting up backup scripts..."
    
    local scripts=(
        "backup-system.sh"
        "recovery-system.sh"
        "backup-monitor.sh"
        "disaster-recovery-test.sh"
    )
    
    for script in "${scripts[@]}"; do
        local script_path="${SCRIPT_DIR}/${script}"
        if [ -f "$script_path" ]; then
            chmod +x "$script_path"
            log "Made executable: $script"
        else
            warning "Script not found: $script"
        fi
    done
    
    log "✅ Scripts configured"
}

# Setup configuration
setup_configuration() {
    log "Setting up configuration..."
    
    local config_file="${CONFIG_DIR}/backup-config.env"
    local example_config="${CONFIG_DIR}/backup-config.env.example"
    
    # Create config directory if it doesn't exist
    mkdir -p "$CONFIG_DIR"
    
    # Copy example config if main config doesn't exist
    if [ ! -f "$config_file" ]; then
        if [ -f "$example_config" ]; then
            cp "$example_config" "$config_file"
            log "Created configuration file from example"
        else
            # Create basic config file
            cat > "$config_file" << EOF
# VybeCoding.ai Backup Configuration
BACKUP_DIR=$BACKUP_DIR
RETENTION_DAYS=30
ALERT_THRESHOLD_HOURS=25
STORAGE_ALERT_THRESHOLD=80
LOG_LEVEL=INFO
VERIFY_BACKUPS=true
AUTO_CLEANUP=true
EOF
            log "Created basic configuration file"
        fi
        
        # Set secure permissions
        chmod 600 "$config_file"
        log "Set configuration file permissions to 600"
        
        warning "Please edit $config_file to configure your backup settings"
    else
        log "Configuration file already exists"
    fi
    
    log "✅ Configuration setup complete"
}

# Install cron jobs
install_cron_jobs() {
    log "Installing cron jobs..."
    
    # Check if cron jobs already exist
    if crontab -l 2>/dev/null | grep -q "backup-system.sh"; then
        warning "Backup cron jobs already exist"
        read -p "Replace existing cron jobs? (y/N): " -r
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log "Skipping cron job installation"
            return
        fi
    fi
    
    # Create temporary cron file
    local temp_cron="/tmp/vybecoding_backup_cron_$$"
    
    # Get existing cron jobs (excluding our backup jobs)
    crontab -l 2>/dev/null | grep -v "backup-system.sh\|backup-monitor.sh\|disaster-recovery-test.sh" > "$temp_cron" || true
    
    # Add our cron jobs
    cat >> "$temp_cron" << EOF

# VybeCoding.ai Backup System
# Daily backup at 2:00 AM
0 2 * * * ${SCRIPT_DIR}/backup-system.sh >> ${BACKUP_DIR}/cron.log 2>&1

# Backup monitoring every 6 hours
0 */6 * * * ${SCRIPT_DIR}/backup-monitor.sh >> ${BACKUP_DIR}/cron.log 2>&1

# Weekly disaster recovery test on Sunday at 3:00 AM
0 3 * * 0 ${SCRIPT_DIR}/disaster-recovery-test.sh >> ${BACKUP_DIR}/cron.log 2>&1
EOF
    
    # Install new cron jobs
    crontab "$temp_cron"
    rm "$temp_cron"
    
    log "✅ Cron jobs installed"
    log "Backup schedule: Daily at 2:00 AM"
    log "Monitoring schedule: Every 6 hours"
    log "DR test schedule: Weekly on Sunday at 3:00 AM"
}

# Test backup system
test_backup_system() {
    log "Testing backup system..."
    
    # Test backup script
    info "Running backup system test..."
    if "${SCRIPT_DIR}/backup-system.sh" --help &> /dev/null; then
        log "✅ Backup script is functional"
    else
        error "❌ Backup script test failed"
        return 1
    fi
    
    # Test monitoring script
    info "Running monitoring system test..."
    if "${SCRIPT_DIR}/backup-monitor.sh" --help &> /dev/null; then
        log "✅ Monitoring script is functional"
    else
        error "❌ Monitoring script test failed"
        return 1
    fi
    
    # Test recovery script
    info "Running recovery system test..."
    if "${SCRIPT_DIR}/recovery-system.sh" --help &> /dev/null; then
        log "✅ Recovery script is functional"
    else
        error "❌ Recovery script test failed"
        return 1
    fi
    
    # Test DR script
    info "Running DR test system test..."
    if "${SCRIPT_DIR}/disaster-recovery-test.sh" --help &> /dev/null; then
        log "✅ DR test script is functional"
    else
        error "❌ DR test script test failed"
        return 1
    fi
    
    log "✅ All backup system tests passed"
}

# Run initial backup
run_initial_backup() {
    log "Running initial backup..."
    
    read -p "Run initial backup now? (Y/n): " -r
    if [[ $REPLY =~ ^[Nn]$ ]]; then
        log "Skipping initial backup"
        return
    fi
    
    # Source configuration
    local config_file="${CONFIG_DIR}/backup-config.env"
    if [ -f "$config_file" ]; then
        set -a
        source "$config_file"
        set +a
    fi
    
    # Run backup
    info "Starting initial backup (this may take a few minutes)..."
    if "${SCRIPT_DIR}/backup-system.sh"; then
        log "✅ Initial backup completed successfully"
        
        # Run monitoring check
        info "Running backup verification..."
        if "${SCRIPT_DIR}/backup-monitor.sh" --integrity; then
            log "✅ Backup verification passed"
        else
            warning "⚠️ Backup verification had issues"
        fi
    else
        error "❌ Initial backup failed"
        return 1
    fi
}

# Generate installation report
generate_report() {
    log "Generating installation report..."
    
    local report_file="${BACKUP_DIR}/installation-report-$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "VybeCoding.ai Backup System Installation Report"
        echo "Generated: $(date)"
        echo "=============================================="
        echo ""
        echo "INSTALLATION DETAILS:"
        echo "--------------------"
        echo "Project Root: $PROJECT_ROOT"
        echo "Backup Directory: $BACKUP_DIR"
        echo "Configuration: ${CONFIG_DIR}/backup-config.env"
        echo "Scripts Directory: $SCRIPT_DIR"
        echo ""
        echo "INSTALLED COMPONENTS:"
        echo "--------------------"
        echo "✅ Backup System Script"
        echo "✅ Recovery System Script"
        echo "✅ Backup Monitor Script"
        echo "✅ Disaster Recovery Test Script"
        echo "✅ Configuration Files"
        echo "✅ Cron Jobs"
        echo ""
        echo "CRON SCHEDULE:"
        echo "-------------"
        crontab -l | grep -E "backup-system|backup-monitor|disaster-recovery"
        echo ""
        echo "NEXT STEPS:"
        echo "----------"
        echo "1. Review and customize configuration: ${CONFIG_DIR}/backup-config.env"
        echo "2. Configure cloud storage credentials (optional)"
        echo "3. Set up notification webhooks (optional)"
        echo "4. Test backup system: ${SCRIPT_DIR}/backup-system.sh"
        echo "5. Monitor backup health: ${SCRIPT_DIR}/backup-monitor.sh"
        echo ""
        echo "DOCUMENTATION:"
        echo "-------------"
        echo "- Backup & Recovery Guide: ${DOCS_DIR}/backup-recovery.md"
        echo "- Configuration Reference: ${CONFIG_DIR}/backup-config.env"
        echo ""
        echo "SUPPORT:"
        echo "-------"
        echo "- Technical Support: <EMAIL>"
        echo "- Documentation: docs.vybecoding.ai"
    } > "$report_file"
    
    log "Installation report generated: $report_file"
    echo "$report_file"
}

# Main installation function
main() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                VybeCoding.ai Backup System Installer         ║"
    echo "║                                                              ║"
    echo "║  This installer will set up automated backup and recovery    ║"
    echo "║  systems for your VybeCoding.ai platform.                   ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    echo ""
    
    log "Starting VybeCoding.ai Backup System installation..."
    
    # Run installation steps
    check_permissions
    check_prerequisites
    create_directories
    setup_scripts
    setup_configuration
    install_cron_jobs
    test_backup_system
    run_initial_backup
    
    # Generate report
    local report_file=$(generate_report)
    
    echo ""
    echo -e "${GREEN}🎉 Installation completed successfully!${NC}"
    echo ""
    echo "📋 Installation Summary:"
    echo "  ✅ Backup directories created"
    echo "  ✅ Scripts configured and made executable"
    echo "  ✅ Configuration files set up"
    echo "  ✅ Cron jobs installed"
    echo "  ✅ System tests passed"
    echo "  ✅ Initial backup completed"
    echo ""
    echo "📄 Installation report: $report_file"
    echo ""
    echo "🔧 Next Steps:"
    echo "  1. Review configuration: ${CONFIG_DIR}/backup-config.env"
    echo "  2. Set up cloud storage (optional)"
    echo "  3. Configure notifications (optional)"
    echo "  4. Read documentation: ${DOCS_DIR}/backup-recovery.md"
    echo ""
    echo "🚀 Your backup system is now active and will run automatically!"
}

# Handle script arguments
case "${1:-}" in
    --help|-h)
        echo "VybeCoding.ai Backup System Installer"
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h          Show this help message"
        echo "  --uninstall         Uninstall backup system"
        echo "  --test-only         Run tests only (no installation)"
        echo ""
        echo "Environment Variables:"
        echo "  BACKUP_DIR          Backup directory (default: /backups)"
        exit 0
        ;;
    --uninstall)
        echo "Uninstalling VybeCoding.ai Backup System..."
        # Remove cron jobs
        crontab -l 2>/dev/null | grep -v "backup-system.sh\|backup-monitor.sh\|disaster-recovery-test.sh" | crontab -
        echo "Cron jobs removed"
        echo "Note: Backup files and configuration are preserved"
        echo "To completely remove: rm -rf $BACKUP_DIR"
        exit 0
        ;;
    --test-only)
        check_prerequisites
        test_backup_system
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
