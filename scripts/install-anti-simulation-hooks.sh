#!/bin/bash
"""
🛡️ INSTALL ANTI-SIMULATION ENFORCEMENT HOOKS
Prevents any simulations/mocks/placeholders from being committed
"""

set -e

echo "🛡️ Installing Anti-Simulation Enforcement System..."

# Create .git/hooks directory if it doesn't exist
mkdir -p .git/hooks

# Create pre-commit hook
cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash
"""
🛡️ PRE-COMMIT ANTI-SIMULATION VALIDATION
Blocks commits containing simulations, mocks, or placeholders
"""

echo "🛡️ Running Anti-Simulation Validation..."

# Run the validator
python3 scripts/anti-simulation-validator.py

# Check exit code
if [ $? -ne 0 ]; then
    echo ""
    echo "🚨 COMMIT BLOCKED: Simulation violations detected!"
    echo "📋 Fix all violations before committing:"
    echo "   - Remove setTimeout() fake delays"
    echo "   - Replace mock data with real API calls"
    echo "   - Remove TODO/PLACEHOLDER comments"
    echo "   - Use real database connections"
    echo "   - Connect to actual services"
    echo ""
    echo "✅ Use these REAL endpoints instead:"
    echo "   /api/vybe/status - Real Vybe system status"
    echo "   /api/content/generate - Real content generation"
    echo "   /api/mas/agents/status - Real MAS agent status"
    echo "   /api/observatory/status - Real observatory status"
    echo ""
    exit 1
fi

echo "✅ Anti-Simulation validation passed!"
EOF

# Make pre-commit hook executable
chmod +x .git/hooks/pre-commit

# Create pre-push hook for additional safety
cat > .git/hooks/pre-push << 'EOF'
#!/bin/bash
"""
🛡️ PRE-PUSH ANTI-SIMULATION VALIDATION
Final check before pushing to remote
"""

echo "🛡️ Final Anti-Simulation Check before push..."

# Run comprehensive validation
python3 scripts/anti-simulation-validator.py

if [ $? -ne 0 ]; then
    echo ""
    echo "🚨 PUSH BLOCKED: Simulation violations detected!"
    echo "🔧 Run validation manually: python3 scripts/anti-simulation-validator.py"
    exit 1
fi

echo "✅ Push validation passed!"
EOF

# Make pre-push hook executable
chmod +x .git/hooks/pre-push

# Create package.json script for manual validation
if [ -f package.json ]; then
    # Add validation script to package.json
    python3 -c "
import json
import sys

try:
    with open('package.json', 'r') as f:
        package = json.load(f)
    
    if 'scripts' not in package:
        package['scripts'] = {}
    
    package['scripts']['validate:no-simulations'] = 'python3 scripts/anti-simulation-validator.py'
    package['scripts']['validate:pre-commit'] = 'python3 scripts/anti-simulation-validator.py'
    
    with open('package.json', 'w') as f:
        json.dump(package, f, indent=2)
    
    print('✅ Added validation scripts to package.json')
except Exception as e:
    print(f'⚠️  Could not update package.json: {e}')
"
fi

# Create VSCode settings for real-time validation
mkdir -p .vscode
cat > .vscode/settings.json << 'EOF'
{
  "files.associations": {
    "*.svelte": "svelte"
  },
  "editor.codeActionsOnSave": {
    "source.fixAll": true
  },
  "typescript.preferences.includePackageJsonAutoImports": "off",
  "svelte.enable-ts-plugin": true,
  "search.exclude": {
    "**/node_modules": true,
    "**/.git": true,
    "**/dist": true,
    "**/.svelte-kit": true
  },
  "files.watcherExclude": {
    "**/node_modules/**": true,
    "**/.git/**": true,
    "**/dist/**": true,
    "**/.svelte-kit/**": true
  },
  "editor.rulers": [80, 120],
  "editor.wordWrap": "bounded",
  "editor.wordWrapColumn": 120,
  "typescript.suggest.autoImports": false,
  "javascript.suggest.autoImports": false
}
EOF

echo ""
echo "🎉 Anti-Simulation Enforcement System Installed!"
echo ""
echo "📋 What was installed:"
echo "   ✅ Pre-commit hook - Blocks commits with violations"
echo "   ✅ Pre-push hook - Final validation before push"
echo "   ✅ npm script: npm run validate:no-simulations"
echo "   ✅ VSCode settings for better development"
echo ""
echo "🛡️ Protection Features:"
echo "   🚫 Blocks setTimeout() fake delays"
echo "   🚫 Blocks mock/fake data objects"
echo "   🚫 Blocks TODO/PLACEHOLDER comments"
echo "   🚫 Blocks fake API endpoints"
echo "   🚫 Blocks simulation patterns"
echo ""
echo "✅ Enforces Real Connections:"
echo "   ✓ Real API endpoints (/api/vybe/, /api/content/, etc.)"
echo "   ✓ Real database calls (Appwrite)"
echo "   ✓ Real WebSocket connections"
echo "   ✓ Real service integrations"
echo ""
echo "🔧 Manual validation: python3 scripts/anti-simulation-validator.py"
echo "🔧 Quick check: npm run validate:no-simulations"
