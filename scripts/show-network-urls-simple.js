const os = require('os');

try {
  console.log('\n🌐 VybeCoding.ai Network Access URLs:');
  console.log('═'.repeat(50));
  
  const interfaces = os.networkInterfaces();
  const addresses = [];

  for (const name of Object.keys(interfaces)) {
    for (const iface of interfaces[name]) {
      if (iface.family === 'IPv4' && !iface.internal) {
        addresses.push({
          name: name,
          address: iface.address
        });
      }
    }
  }
  
  if (addresses.length === 0) {
    console.log('❌ No external network interfaces found');
    console.log('📍 Local access only: http://localhost:5173');
  } else {
    console.log('✅ Server will be accessible from these network addresses:');
    console.log('');
    
    addresses.forEach((addr, index) => {
      const url = `http://${addr.address}:5173`;
      console.log(`📱 ${addr.name.padEnd(12)} ${url}`);
      
      if (index === 0) {
        console.log(`   ${''.padEnd(12)} ⭐ Primary network interface`);
      }
    });
  }
  
  console.log('');
  console.log('📍 Local access: http://localhost:5173');
  console.log('🔗 Container IP: http://**********:5173 (if applicable)');
  console.log('');
  console.log('💡 Tips:');
  console.log('   • Make sure your firewall allows port 5173');
  console.log('   • Other devices must be on the same network');
  console.log('   • Use the IP address that matches your network (192.168.x.x)');
  console.log('');
  console.log('═'.repeat(50));
  console.log('🚀 Starting development server...');
  console.log('');
  
} catch (error) {
  console.error('Error getting network interfaces:', error.message);
  console.log('📍 Fallback: Server will be available at http://localhost:5173');
  console.log('🌐 Network access: Use your computer\'s IP address with port 5173');
}
