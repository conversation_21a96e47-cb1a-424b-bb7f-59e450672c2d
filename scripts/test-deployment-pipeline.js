#!/usr/bin/env node

/**
 * Enhanced MAS Deployment Pipeline Integration Test
 * Tests the complete workflow from content generation to live deployment
 */

import { existsSync, readFileSync } from 'fs';
import { join } from 'path';

console.log('🧪 Testing Enhanced MAS Deployment Pipeline...\n');

const config = {
  apiBase: 'http://localhost:5173',
  testTimeout: 120000, // 2 minutes
  testContent: {
    course: {
      title: 'Test AI Course Generation',
      target_audience: 'developers',
      content_type: 'course'
    },
    news_article: {
      title: 'Test AI News Article',
      target_audience: 'tech professionals',
      content_type: 'news_article'
    },
    vybe_qube: {
      title: 'Test AI Vybe Qube',
      target_audience: 'entrepreneurs',
      content_type: 'vybe_qube'
    }
  }
};

let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  tests: []
};

// Test helper functions
function logTest(name, status, details = '') {
  const icon = status === 'PASS' ? '✅' : '❌';
  console.log(`${icon} ${name} - ${status}`);
  if (details) console.log(`   ${details}`);
  
  testResults.total++;
  if (status === 'PASS') {
    testResults.passed++;
  } else {
    testResults.failed++;
  }
  
  testResults.tests.push({ name, status, details });
}

async function makeRequest(url, options = {}) {
  try {
    const response = await fetch(url, {
      timeout: 10000,
      ...options
    });
    return {
      ok: response.ok,
      status: response.status,
      data: await response.json()
    };
  } catch (error) {
    return {
      ok: false,
      status: 0,
      error: error.message
    };
  }
}

// Test 1: Check if deployment API is accessible
async function testDeploymentAPIAccess() {
  console.log('🔍 Testing deployment API access...');
  
  const result = await makeRequest(`${config.apiBase}/api/deployment`);
  
  if (result.ok && result.data.success) {
    logTest('Deployment API Access', 'PASS', 'API is responding correctly');
    return true;
  } else {
    logTest('Deployment API Access', 'FAIL', `API error: ${result.error || result.status}`);
    return false;
  }
}

// Test 2: Check deployment orchestrator status
async function testDeploymentOrchestratorStatus() {
  console.log('🔍 Testing deployment orchestrator status...');
  
  const result = await makeRequest(`${config.apiBase}/api/deployment?action=status`);
  
  if (result.ok && result.data.success) {
    logTest('Deployment Orchestrator Status', 'PASS', 
      `Monitoring: ${result.data.isMonitoring ? 'Active' : 'Inactive'}`);
    return result.data;
  } else {
    logTest('Deployment Orchestrator Status', 'FAIL', 
      `Status check failed: ${result.error || result.status}`);
    return null;
  }
}

// Test 3: Test content generation API
async function testContentGenerationAPI() {
  console.log('🔍 Testing content generation API...');
  
  const testPayload = {
    content_type: 'course',
    topic: 'Test Course for Pipeline',
    target_audience: 'developers',
    requirements: {
      test_mode: true
    }
  };
  
  const result = await makeRequest(`${config.apiBase}/api/content/vybe-generate`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(testPayload)
  });
  
  if (result.ok && result.data.success) {
    logTest('Content Generation API', 'PASS', `Generation ID: ${result.data.id}`);
    return result.data.id;
  } else {
    logTest('Content Generation API', 'FAIL', 
      `Generation failed: ${result.data?.error || result.error}`);
    return null;
  }
}

// Test 4: Test forced deployment
async function testForcedDeployment() {
  console.log('🔍 Testing forced deployment...');
  
  const testContent = {
    title: 'Test Deployment Content',
    description: 'This is a test content for deployment pipeline verification',
    content: '<h1>Test Content</h1><p>This content was generated for testing purposes.</p>',
    target_audience: 'developers',
    generated_at: new Date().toISOString(),
    agents_used: ['TEST_AGENT']
  };
  
  const result = await makeRequest(`${config.apiBase}/api/deployment`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      action: 'force_deployment',
      contentData: testContent,
      contentType: 'course'
    })
  });
  
  if (result.ok && result.data.success) {
    logTest('Forced Deployment', 'PASS', `Task ID: ${result.data.task?.id}`);
    return result.data.task;
  } else {
    logTest('Forced Deployment', 'FAIL', 
      `Deployment failed: ${result.data?.error || result.error}`);
    return null;
  }
}

// Test 5: Verify deployed content accessibility
async function testDeployedContentAccess(deploymentTask) {
  if (!deploymentTask || !deploymentTask.deploymentUrl) {
    logTest('Deployed Content Access', 'SKIP', 'No deployment URL available');
    return false;
  }
  
  console.log('🔍 Testing deployed content accessibility...');
  
  // Wait a moment for deployment to complete
  await new Promise(resolve => setTimeout(resolve, 5000));
  
  const result = await makeRequest(`${config.apiBase}${deploymentTask.deploymentUrl}`);
  
  if (result.ok) {
    logTest('Deployed Content Access', 'PASS', 
      `Content accessible at ${deploymentTask.deploymentUrl}`);
    return true;
  } else {
    logTest('Deployed Content Access', 'FAIL', 
      `Content not accessible: ${result.status}`);
    return false;
  }
}

// Test 6: Check file system deployment
async function testFileSystemDeployment() {
  console.log('🔍 Testing file system deployment...');
  
  const expectedPaths = [
    'src/lib/services/deployment-orchestrator.ts',
    'src/lib/services/content-deployment.ts',
    'src/routes/api/deployment/+server.ts'
  ];
  
  let allFilesExist = true;
  const missingFiles = [];
  
  for (const path of expectedPaths) {
    if (!existsSync(join(process.cwd(), path))) {
      allFilesExist = false;
      missingFiles.push(path);
    }
  }
  
  if (allFilesExist) {
    logTest('File System Deployment', 'PASS', 'All deployment files present');
    return true;
  } else {
    logTest('File System Deployment', 'FAIL', 
      `Missing files: ${missingFiles.join(', ')}`);
    return false;
  }
}

// Test 7: Test Enhanced MAS integration
async function testEnhancedMASIntegration() {
  console.log('🔍 Testing Enhanced MAS integration...');
  
  const masSystemPath = join(process.cwd(), 'method/vybe/enhanced_mas_system.py');
  
  if (!existsSync(masSystemPath)) {
    logTest('Enhanced MAS Integration', 'FAIL', 'Enhanced MAS system file not found');
    return false;
  }
  
  try {
    const masContent = readFileSync(masSystemPath, 'utf-8');
    const hasDeploymentIntegration = masContent.includes('_trigger_deployment');
    
    if (hasDeploymentIntegration) {
      logTest('Enhanced MAS Integration', 'PASS', 'Deployment integration found in MAS system');
      return true;
    } else {
      logTest('Enhanced MAS Integration', 'FAIL', 'Deployment integration not found in MAS system');
      return false;
    }
  } catch (error) {
    logTest('Enhanced MAS Integration', 'FAIL', `Error reading MAS system: ${error.message}`);
    return false;
  }
}

// Test 8: Test database collections
async function testDatabaseCollections() {
  console.log('🔍 Testing database collections setup...');
  
  // This would ideally test actual database connectivity
  // For now, we'll check if the setup script exists
  const setupScriptPath = join(process.cwd(), 'scripts/setup-deployment-collections.js');
  
  if (existsSync(setupScriptPath)) {
    logTest('Database Collections Setup', 'PASS', 'Setup script available');
    return true;
  } else {
    logTest('Database Collections Setup', 'FAIL', 'Setup script not found');
    return false;
  }
}

// Main test runner
async function runTests() {
  console.log('🚀 Starting Enhanced MAS Deployment Pipeline Tests\n');
  
  try {
    // Test 1: API Access
    const apiAccessible = await testDeploymentAPIAccess();
    if (!apiAccessible) {
      console.log('\n❌ Cannot proceed with tests - API not accessible');
      return;
    }
    
    // Test 2: Orchestrator Status
    const orchestratorStatus = await testDeploymentOrchestratorStatus();
    
    // Test 3: Content Generation
    const generationId = await testContentGenerationAPI();
    
    // Test 4: Forced Deployment
    const deploymentTask = await testForcedDeployment();
    
    // Test 5: Content Access
    await testDeployedContentAccess(deploymentTask);
    
    // Test 6: File System
    await testFileSystemDeployment();
    
    // Test 7: MAS Integration
    await testEnhancedMASIntegration();
    
    // Test 8: Database Setup
    await testDatabaseCollections();
    
  } catch (error) {
    console.error('\n❌ Test execution failed:', error.message);
    testResults.failed++;
  }
  
  // Display results
  console.log('\n📊 Test Results Summary:');
  console.log('=' .repeat(50));
  console.log(`Total Tests: ${testResults.total}`);
  console.log(`Passed: ${testResults.passed} ✅`);
  console.log(`Failed: ${testResults.failed} ❌`);
  console.log(`Success Rate: ${testResults.total > 0 ? 
    ((testResults.passed / testResults.total) * 100).toFixed(1) : 0}%`);
  
  if (testResults.failed > 0) {
    console.log('\n❌ Failed Tests:');
    testResults.tests
      .filter(test => test.status === 'FAIL')
      .forEach(test => console.log(`   - ${test.name}: ${test.details}`));
  }
  
  if (testResults.passed === testResults.total) {
    console.log('\n🎉 All tests passed! Enhanced MAS Deployment Pipeline is fully operational.');
  } else {
    console.log('\n⚠️ Some tests failed. Please review the issues above.');
  }
  
  console.log('\n🔗 Next Steps:');
  console.log('   1. Fix any failed tests');
  console.log('   2. Run: node scripts/start-deployment-pipeline.js');
  console.log('   3. Generate content using Enhanced MAS');
  console.log('   4. Monitor deployments at http://localhost:5173/mas');
}

// Run the tests
runTests().catch(error => {
  console.error('❌ Test runner failed:', error);
  process.exit(1);
});
