#!/usr/bin/env node

/**
 * VybeCoding.ai Auto Portainer Launcher
 * Automatically opens Portainer and handles login when starting dev server
 */

import { spawn, exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs/promises';
import path from 'path';

const execAsync = promisify(exec);

// Configuration
const PORTAINER_URL = 'http://localhost:9000';
const VYBECODING_URL = 'http://localhost:5173';
const VYBECODING_PORT = 5173;
const PORTAINER_USERNAME = 'admin';
const PORTAINER_PASSWORD = 'S!llyK!tty!!';
const CONTAINERS_PAGE = `${PORTAINER_URL}/#!/3/docker/containers`;

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

/**
 * Check if Portainer is running
 */
async function checkPortainerStatus() {
  try {
    const { stdout } = await execAsync(
      'docker ps --filter "name=portainer" --format "{{.Names}}"'
    );
    return stdout.trim().includes('portainer');
  } catch (error) {
    return false;
  }
}

/**
 * Start Portainer if not running
 */
async function startPortainer() {
  log('🐳 Starting Portainer...', 'blue');
  try {
    await execAsync(`docker run -d \
      --name portainer \
      --restart unless-stopped \
      -p 9000:9000 \
      -p 9443:9443 \
      -v /var/run/docker.sock:/var/run/docker.sock \
      -v portainer_data:/data \
      portainer/portainer-ce:latest`);
    log('✅ Portainer started successfully!', 'green');
    return true;
  } catch (error) {
    if (error.message.includes('already in use')) {
      log('ℹ️  Portainer container already exists, starting it...', 'yellow');
      try {
        await execAsync('docker start portainer');
        log('✅ Portainer restarted successfully!', 'green');
        return true;
      } catch (startError) {
        log(
          `❌ Failed to start existing Portainer: ${startError.message}`,
          'red'
        );
        return false;
      }
    } else {
      log(`❌ Failed to start Portainer: ${error.message}`, 'red');
      return false;
    }
  }
}

/**
 * Wait for Portainer to be ready
 */
async function waitForPortainer(maxAttempts = 30) {
  log('⏳ Waiting for Portainer to be ready...', 'yellow');

  for (let i = 0; i < maxAttempts; i++) {
    try {
      const response = await fetch(PORTAINER_URL);
      if (response.ok) {
        log('✅ Portainer is ready!', 'green');
        return true;
      }
    } catch (error) {
      // Portainer not ready yet
    }

    await new Promise(resolve => {
      const waitInterval = setInterval(() => {
        clearInterval(waitInterval);
        resolve();
      }, 1000);
    });
    process.stdout.write('.');
  }

  console.log('');
  log('❌ Portainer failed to start within timeout', 'red');
  return false;
}

/**
 * Generate auto-login script for browser
 */
function generateAutoLoginScript() {
  return `
// VybeCoding.ai Portainer Auto-Login
(function() {
    const username = "${PORTAINER_USERNAME}";
    const password = "${PORTAINER_PASSWORD}";
    const containersPage = "${CONTAINERS_PAGE}";
    
    console.log('🚀 VybeCoding.ai Auto-Login Script Loaded');
    
    // Auto-login if on login page
    function attemptLogin() {
        const userInput = document.querySelector('input[name="username"], input[data-cy="usernameInput"]');
        const passInput = document.querySelector('input[name="password"], input[data-cy="passwordInput"]');
        const loginBtn = document.querySelector('button[type="submit"], button[data-cy="loginButton"]');
        
        if (userInput && passInput && loginBtn) {
            console.log('🔐 Auto-filling login credentials...');
            userInput.value = username;
            passInput.value = password;
            
            // Trigger input events
            userInput.dispatchEvent(new Event('input', { bubbles: true }));
            passInput.dispatchEvent(new Event('input', { bubbles: true }));
            
            setTimeout(() => {
                loginBtn.click();
                console.log('✅ Login submitted automatically');
            }, 100);
            
            return true;
        }
        return false;
    }
    
    // Redirect to containers page after login
    function redirectToContainers() {
        if (window.localStorage.getItem('JWT') && 
            (window.location.hash === '#!/home' || window.location.hash === '#!/dashboard')) {
            console.log('🔄 Redirecting to containers page...');
            window.location.href = containersPage;
        }
    }
    
    // Check if we're on login page
    if (window.location.hash.includes('init/admin') || 
        window.location.hash.includes('auth') || 
        window.location.pathname.includes('auth')) {
        
        // Try login immediately
        if (!attemptLogin()) {
            // If elements not found, wait for them
            const observer = new MutationObserver(() => {
                if (attemptLogin()) {
                    observer.disconnect();
                }
            });
            observer.observe(document.body, { childList: true, subtree: true });
        }
    }
    
    // Monitor for successful login and redirect
    const redirectInterval = setInterval(() => {
        redirectToContainers();
        
        // Stop checking after 30 seconds
        setTimeout(() => clearInterval(redirectInterval), 30000);
    }, 500);
    
})();
`;
}

/**
 * Open both Portainer and VybeCoding.ai in browser
 */
async function openBrowserTabs() {
  log('🌐 Opening browser tabs...', 'cyan');

  const script = generateAutoLoginScript();
  const tempScriptPath = path.join(
    process.cwd(),
    '.tmp-portainer-autologin.js'
  );

  try {
    // Save auto-login script temporarily
    await fs.writeFile(tempScriptPath, script);

    // Determine browser command
    const openCommand =
      process.platform === 'darwin'
        ? 'open'
        : process.platform === 'win32'
          ? 'start'
          : 'xdg-open';

    // Try to open in Firefox Developer Edition first (if available)
    let browserCommand = 'firefox-developer-edition';
    let browserName = '🦊 Firefox Developer Edition';

    try {
      await execAsync('firefox-developer-edition --version');
    } catch {
      // Fallback to regular Firefox
      try {
        await execAsync('firefox --version');
        browserCommand = 'firefox';
        browserName = '🦊 Firefox';
      } catch {
        // Fallback to default browser
        browserCommand = openCommand;
        browserName = '🌐 Default browser';
      }
    }

    // Open Portainer directly to containers page
    if (browserCommand === openCommand) {
      spawn(openCommand, [CONTAINERS_PAGE], { detached: true });
    } else {
      spawn(browserCommand, [CONTAINERS_PAGE], { detached: true });
    }
    log(`${browserName} - Portainer containers page opened`, 'green');

    // Wait a moment, then open VybeCoding.ai in a new tab
    setTimeout(() => {
      if (browserCommand === openCommand) {
        spawn(openCommand, [VYBECODING_URL], { detached: true });
      } else {
        // For Firefox, open in new tab
        spawn(browserCommand, ['--new-tab', VYBECODING_URL], {
          detached: true,
        });
      }
      log(`${browserName} - VybeCoding.ai opened`, 'green');
    }, 2000);

    // Clean up temp script after a delay
    setTimeout(async () => {
      try {
        await fs.unlink(tempScriptPath);
      } catch (error) {
        // Ignore cleanup errors
      }
    }, 5000);
  } catch (error) {
    log(`❌ Failed to open browser: ${error.message}`, 'red');
  }
}

/**
 * Main function
 */
async function main() {
  log('🚀 VybeCoding.ai Auto Portainer Setup', 'magenta');
  log('=====================================', 'magenta');

  // Check if Portainer is running
  const isRunning = await checkPortainerStatus();

  if (!isRunning) {
    // Start Portainer
    const started = await startPortainer();
    if (!started) {
      process.exit(1);
    }

    // Wait for it to be ready
    const ready = await waitForPortainer();
    if (!ready) {
      process.exit(1);
    }
  } else {
    log('✅ Portainer is already running', 'green');
  }

  // Open both Portainer and VybeCoding.ai in browser
  await openBrowserTabs();

  // Get network IP for network access info
  let networkIP = 'localhost';
  try {
    const { stdout } = await execAsync("hostname -I | awk '{print $1}'");
    networkIP = stdout.trim() || 'localhost';
  } catch (error) {
    // Fallback to localhost if can't get IP
  }

  log('', 'reset');
  log('🎉 Development environment ready!', 'green');
  log(`🐳 Portainer: ${PORTAINER_URL}`, 'cyan');
  log(`⚡ VybeCoding.ai: ${VYBECODING_URL}`, 'cyan');
  if (networkIP !== 'localhost') {
    log(
      `🌐 Network Access: http://${networkIP}:${VYBECODING_PORT}/`,
      'magenta'
    );
    log(`🌐 Portainer Network: http://${networkIP}:9000/`, 'magenta');
  }
  log(`👤 Username: ${PORTAINER_USERNAME}`, 'cyan');
  log(`🔑 Password: ${PORTAINER_PASSWORD}`, 'cyan');
  log('', 'reset');
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    log(`❌ Error: ${error.message}`, 'red');
    process.exit(1);
  });
}

export { main as autoPortainer };
