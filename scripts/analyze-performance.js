#!/usr/bin/env node
// VybeCoding.ai Performance Analysis Tool
// Analyzes K6 test results and generates insights

const fs = require('fs');
const path = require('path');

// Performance thresholds and targets
const PERFORMANCE_TARGETS = {
  responseTime: {
    excellent: 500, // < 500ms
    good: 1000, // < 1s
    acceptable: 2000, // < 2s
    poor: 5000, // < 5s
  },
  throughput: {
    minimum: 100, // 100 RPS minimum
    target: 500, // 500 RPS target
    excellent: 1000, // 1000+ RPS excellent
  },
  errorRate: {
    excellent: 0.01, // < 1%
    good: 0.05, // < 5%
    acceptable: 0.1, // < 10%
  },
  availability: {
    minimum: 0.99, // 99% minimum
    target: 0.999, // 99.9% target
  },
};

// Main analysis function
function analyzePerformanceResults(resultsFile) {
  console.log('🔍 Analyzing performance test results...');

  if (!fs.existsSync(resultsFile)) {
    console.error(`❌ Results file not found: ${resultsFile}`);
    process.exit(1);
  }

  try {
    const rawData = fs.readFileSync(resultsFile, 'utf8');
    const results = JSON.parse(rawData);

    const analysis = {
      summary: extractSummary(results),
      performance: analyzePerformance(results),
      recommendations: generateRecommendations(results),
      trends: analyzeTrends(results),
      alerts: generateAlerts(results),
    };

    // Generate reports
    generateConsoleReport(analysis);
    generateJSONReport(analysis, resultsFile);
    generateMarkdownReport(analysis, resultsFile);

    // Return exit code based on performance
    const overallScore = calculateOverallScore(analysis);
    console.log(`\n📊 Overall Performance Score: ${overallScore}/100`);

    if (overallScore >= 80) {
      console.log('✅ Performance is excellent!');
      process.exit(0);
    } else if (overallScore >= 60) {
      console.log('⚠️  Performance is acceptable but has room for improvement');
      process.exit(0);
    } else {
      console.log(
        '❌ Performance issues detected - immediate attention required'
      );
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ Error analyzing results:', error.message);
    process.exit(1);
  }
}

// Extract summary metrics
function extractSummary(results) {
  const metrics = results.metrics || {};

  return {
    testDuration: results.state?.testRunDurationMs || 0,
    totalRequests: metrics.http_reqs?.values?.count || 0,
    requestRate: metrics.http_reqs?.values?.rate || 0,
    avgResponseTime: metrics.http_req_duration?.values?.avg || 0,
    p95ResponseTime: metrics.http_req_duration?.values?.['p(95)'] || 0,
    p99ResponseTime: metrics.http_req_duration?.values?.['p(99)'] || 0,
    errorRate: metrics.http_req_failed?.values?.rate || 0,
    maxVUs: metrics.vus_max?.values?.max || 0,
    dataReceived: metrics.data_received?.values?.count || 0,
    dataSent: metrics.data_sent?.values?.count || 0,
  };
}

// Analyze performance metrics
function analyzePerformance(results) {
  const summary = extractSummary(results);
  const metrics = results.metrics || {};

  // Response time analysis
  const responseTimeScore = analyzeResponseTime(
    summary.avgResponseTime,
    summary.p95ResponseTime
  );

  // Throughput analysis
  const throughputScore = analyzeThroughput(summary.requestRate);

  // Error rate analysis
  const errorRateScore = analyzeErrorRate(summary.errorRate);

  // Resource utilization (if available)
  const resourceScore = analyzeResourceUtilization(metrics);

  // Custom metrics analysis
  const customMetricsScore = analyzeCustomMetrics(metrics);

  return {
    responseTime: responseTimeScore,
    throughput: throughputScore,
    errorRate: errorRateScore,
    resources: resourceScore,
    customMetrics: customMetricsScore,
    overall: Math.round(
      (responseTimeScore.score + throughputScore.score + errorRateScore.score) /
        3
    ),
  };
}

// Analyze response time performance
function analyzeResponseTime(avg, p95) {
  let score = 100;
  let rating = 'excellent';
  let issues = [];

  if (avg > PERFORMANCE_TARGETS.responseTime.poor) {
    score = 20;
    rating = 'poor';
    issues.push('Average response time is very high');
  } else if (avg > PERFORMANCE_TARGETS.responseTime.acceptable) {
    score = 40;
    rating = 'needs improvement';
    issues.push('Average response time exceeds acceptable threshold');
  } else if (avg > PERFORMANCE_TARGETS.responseTime.good) {
    score = 70;
    rating = 'good';
  } else if (avg > PERFORMANCE_TARGETS.responseTime.excellent) {
    score = 85;
    rating = 'very good';
  }

  if (p95 > avg * 3) {
    score -= 10;
    issues.push('High variability in response times (P95 >> average)');
  }

  return {
    score: Math.max(0, score),
    rating,
    avgResponseTime: avg,
    p95ResponseTime: p95,
    issues,
    recommendations: generateResponseTimeRecommendations(avg, p95),
  };
}

// Analyze throughput performance
function analyzeThroughput(rps) {
  let score = 100;
  let rating = 'excellent';
  let issues = [];

  if (rps < PERFORMANCE_TARGETS.throughput.minimum) {
    score = 30;
    rating = 'poor';
    issues.push('Throughput below minimum acceptable level');
  } else if (rps < PERFORMANCE_TARGETS.throughput.target) {
    score = 60;
    rating = 'needs improvement';
    issues.push('Throughput below target level');
  } else if (rps < PERFORMANCE_TARGETS.throughput.excellent) {
    score = 80;
    rating = 'good';
  }

  return {
    score,
    rating,
    requestsPerSecond: rps,
    issues,
    recommendations: generateThroughputRecommendations(rps),
  };
}

// Analyze error rate
function analyzeErrorRate(errorRate) {
  let score = 100;
  let rating = 'excellent';
  let issues = [];

  if (errorRate > PERFORMANCE_TARGETS.errorRate.acceptable) {
    score = 20;
    rating = 'poor';
    issues.push('Error rate is unacceptably high');
  } else if (errorRate > PERFORMANCE_TARGETS.errorRate.good) {
    score = 50;
    rating = 'needs improvement';
    issues.push('Error rate exceeds good threshold');
  } else if (errorRate > PERFORMANCE_TARGETS.errorRate.excellent) {
    score = 80;
    rating = 'good';
  }

  return {
    score,
    rating,
    errorRate: errorRate * 100, // Convert to percentage
    issues,
    recommendations: generateErrorRateRecommendations(errorRate),
  };
}

// Analyze resource utilization
function analyzeResourceUtilization(metrics) {
  // This would be enhanced with actual resource metrics
  return {
    score: 75, // Default score when resource metrics not available
    rating: 'unknown',
    issues: [],
    recommendations: ['Implement resource monitoring for better analysis'],
  };
}

// Analyze custom metrics
function analyzeCustomMetrics(metrics) {
  const customMetrics = {};
  const issues = [];
  const recommendations = [];

  // Analyze student-specific metrics
  if (metrics.student_login_success) {
    const loginRate = metrics.student_login_success.values?.rate || 0;
    customMetrics.studentLoginSuccess = loginRate;
    if (loginRate < 0.95) {
      issues.push('Student login success rate below 95%');
      recommendations.push('Investigate authentication system performance');
    }
  }

  // Analyze Vybe Qube generation metrics
  if (metrics.qube_generation_success) {
    const generationRate = metrics.qube_generation_success.values?.rate || 0;
    customMetrics.qubeGenerationSuccess = generationRate;
    if (generationRate < 0.8) {
      issues.push('Vybe Qube generation success rate below 80%');
      recommendations.push('Optimize AI generation pipeline');
    }
  }

  if (metrics.qube_generation_duration) {
    const avgGenerationTime = metrics.qube_generation_duration.values?.avg || 0;
    customMetrics.avgGenerationTime = avgGenerationTime;
    if (avgGenerationTime > 45000) {
      issues.push('Vybe Qube generation time exceeds 45 seconds');
      recommendations.push(
        'Optimize AI model performance or implement caching'
      );
    }
  }

  return {
    score: issues.length === 0 ? 90 : Math.max(50, 90 - issues.length * 20),
    metrics: customMetrics,
    issues,
    recommendations,
  };
}

// Generate recommendations
function generateRecommendations(results) {
  const recommendations = [];
  const performance = analyzePerformance(results);

  // Response time recommendations
  recommendations.push(...performance.responseTime.recommendations);

  // Throughput recommendations
  recommendations.push(...performance.throughput.recommendations);

  // Error rate recommendations
  recommendations.push(...performance.errorRate.recommendations);

  // Custom metrics recommendations
  recommendations.push(...performance.customMetrics.recommendations);

  // General recommendations
  if (performance.overall < 70) {
    recommendations.push(
      'Consider implementing performance monitoring in production'
    );
    recommendations.push('Set up automated performance regression testing');
  }

  return [...new Set(recommendations)]; // Remove duplicates
}

// Generate response time recommendations
function generateResponseTimeRecommendations(avg, p95) {
  const recommendations = [];

  if (avg > 2000) {
    recommendations.push(
      'Implement caching strategy for frequently accessed data'
    );
    recommendations.push('Optimize database queries and add proper indexing');
    recommendations.push('Consider using a CDN for static assets');
  }

  if (p95 > avg * 2) {
    recommendations.push('Investigate response time variability');
    recommendations.push('Consider implementing connection pooling');
  }

  return recommendations;
}

// Generate throughput recommendations
function generateThroughputRecommendations(rps) {
  const recommendations = [];

  if (rps < 500) {
    recommendations.push('Consider horizontal scaling with load balancers');
    recommendations.push('Optimize application code for better concurrency');
    recommendations.push('Implement asynchronous processing where possible');
  }

  return recommendations;
}

// Generate error rate recommendations
function generateErrorRateRecommendations(errorRate) {
  const recommendations = [];

  if (errorRate > 0.05) {
    recommendations.push(
      'Implement circuit breaker pattern for external services'
    );
    recommendations.push('Add proper error handling and retry mechanisms');
    recommendations.push('Monitor and alert on error rate spikes');
  }

  return recommendations;
}

// Analyze trends (placeholder for future enhancement)
function analyzeTrends(results) {
  return {
    message: 'Trend analysis requires historical data',
    recommendations: ['Implement continuous performance monitoring'],
  };
}

// Generate alerts
function generateAlerts(results) {
  const alerts = [];
  const performance = analyzePerformance(results);

  if (performance.responseTime.score < 50) {
    alerts.push({
      level: 'critical',
      message: 'Response time performance is poor',
      action: 'Immediate optimization required',
    });
  }

  if (performance.errorRate.score < 50) {
    alerts.push({
      level: 'critical',
      message: 'Error rate is too high',
      action: 'Investigate and fix error sources',
    });
  }

  if (performance.throughput.score < 50) {
    alerts.push({
      level: 'warning',
      message: 'Throughput below target',
      action: 'Consider scaling or optimization',
    });
  }

  return alerts;
}

// Calculate overall performance score
function calculateOverallScore(analysis) {
  const weights = {
    responseTime: 0.3,
    throughput: 0.25,
    errorRate: 0.3,
    customMetrics: 0.15,
  };

  const performance = analysis.performance;

  return Math.round(
    performance.responseTime.score * weights.responseTime +
      performance.throughput.score * weights.throughput +
      performance.errorRate.score * weights.errorRate +
      performance.customMetrics.score * weights.customMetrics
  );
}

// Generate console report
function generateConsoleReport(analysis) {
  console.log('\n📊 Performance Analysis Report');
  console.log('================================');

  const perf = analysis.performance;

  console.log(
    `\n🚀 Response Time: ${perf.responseTime.rating} (${perf.responseTime.score}/100)`
  );
  console.log(`   Average: ${Math.round(perf.responseTime.avgResponseTime)}ms`);
  console.log(`   P95: ${Math.round(perf.responseTime.p95ResponseTime)}ms`);

  console.log(
    `\n📈 Throughput: ${perf.throughput.rating} (${perf.throughput.score}/100)`
  );
  console.log(
    `   Requests/sec: ${Math.round(perf.throughput.requestsPerSecond)}`
  );

  console.log(
    `\n❌ Error Rate: ${perf.errorRate.rating} (${perf.errorRate.score}/100)`
  );
  console.log(`   Error Rate: ${perf.errorRate.errorRate.toFixed(2)}%`);

  if (analysis.alerts.length > 0) {
    console.log('\n🚨 Alerts:');
    analysis.alerts.forEach(alert => {
      console.log(`   ${alert.level.toUpperCase()}: ${alert.message}`);
    });
  }

  if (analysis.recommendations.length > 0) {
    console.log('\n💡 Recommendations:');
    analysis.recommendations.slice(0, 5).forEach(rec => {
      console.log(`   • ${rec}`);
    });
  }
}

// Generate JSON report
function generateJSONReport(analysis, resultsFile) {
  const reportFile = resultsFile.replace('.json', '-analysis.json');
  fs.writeFileSync(reportFile, JSON.stringify(analysis, null, 2));
  console.log(`\n📄 Detailed analysis saved to: ${reportFile}`);
}

// Generate Markdown report
function generateMarkdownReport(analysis, resultsFile) {
  const reportFile = resultsFile.replace('.json', '-report.md');

  const markdown = `# Performance Test Analysis Report

## Summary
- **Overall Score**: ${calculateOverallScore(analysis)}/100
- **Response Time**: ${analysis.performance.responseTime.rating} (${analysis.performance.responseTime.score}/100)
- **Throughput**: ${analysis.performance.throughput.rating} (${analysis.performance.throughput.score}/100)
- **Error Rate**: ${analysis.performance.errorRate.rating} (${analysis.performance.errorRate.score}/100)

## Key Metrics
- **Average Response Time**: ${Math.round(analysis.performance.responseTime.avgResponseTime)}ms
- **P95 Response Time**: ${Math.round(analysis.performance.responseTime.p95ResponseTime)}ms
- **Requests per Second**: ${Math.round(analysis.performance.throughput.requestsPerSecond)}
- **Error Rate**: ${analysis.performance.errorRate.errorRate.toFixed(2)}%

## Recommendations
${analysis.recommendations.map(rec => `- ${rec}`).join('\n')}

## Alerts
${analysis.alerts.map(alert => `- **${alert.level.toUpperCase()}**: ${alert.message}`).join('\n')}
`;

  fs.writeFileSync(reportFile, markdown);
  console.log(`📄 Markdown report saved to: ${reportFile}`);
}

// Main execution
if (require.main === module) {
  const resultsFile = process.argv[2];

  if (!resultsFile) {
    console.error('Usage: node analyze-performance.js <results.json>');
    process.exit(1);
  }

  analyzePerformanceResults(resultsFile);
}

module.exports = {
  analyzePerformanceResults,
  PERFORMANCE_TARGETS,
};
