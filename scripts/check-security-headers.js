#!/usr/bin/env node

/**
 * Security Headers Checker
 * Validates that important security headers are present in the application
 */

const https = require('https');
const http = require('http');
const { URL } = require('url');

const REQUIRED_HEADERS = {
  'x-frame-options': {
    required: true,
    description: 'Prevents clickjacking attacks',
    validValues: ['DENY', 'SAMEORIGIN'],
  },
  'x-content-type-options': {
    required: true,
    description: 'Prevents MIME type sniffing',
    validValues: ['nosniff'],
  },
  'x-xss-protection': {
    required: true,
    description: 'Enables XSS filtering',
    validValues: ['1; mode=block', '0'],
  },
  'strict-transport-security': {
    required: true,
    description: 'Enforces HTTPS connections',
    validValues: null, // Any value is acceptable
  },
  'content-security-policy': {
    required: false, // Optional but recommended
    description: 'Controls resource loading',
    validValues: null,
  },
  'referrer-policy': {
    required: false,
    description: 'Controls referrer information',
    validValues: ['no-referrer', 'same-origin', 'strict-origin'],
  },
};

async function checkSecurityHeaders(url) {
  return new Promise((resolve, reject) => {
    const parsedUrl = new URL(url);
    const client = parsedUrl.protocol === 'https:' ? https : http;

    const req = client.request(parsedUrl, res => {
      const headers = {};

      // Normalize header names to lowercase
      Object.keys(res.headers).forEach(key => {
        headers[key.toLowerCase()] = res.headers[key];
      });

      resolve({
        statusCode: res.statusCode,
        headers: headers,
      });
    });

    req.on('error', reject);
    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

function validateHeaders(headers) {
  const results = {
    passed: [],
    failed: [],
    warnings: [],
    score: 0,
  };

  let totalRequired = 0;
  let passedRequired = 0;

  Object.entries(REQUIRED_HEADERS).forEach(([headerName, config]) => {
    const headerValue = headers[headerName];
    const exists = !!headerValue;

    if (config.required) {
      totalRequired++;
    }

    if (exists) {
      const isValid =
        !config.validValues ||
        config.validValues.some(valid =>
          headerValue.toLowerCase().includes(valid.toLowerCase())
        );

      if (isValid) {
        results.passed.push({
          header: headerName,
          value: headerValue,
          description: config.description,
        });

        if (config.required) {
          passedRequired++;
        }
      } else {
        results.failed.push({
          header: headerName,
          value: headerValue,
          description: config.description,
          issue: 'Invalid value',
        });
      }
    } else {
      if (config.required) {
        results.failed.push({
          header: headerName,
          value: null,
          description: config.description,
          issue: 'Header missing',
        });
      } else {
        results.warnings.push({
          header: headerName,
          value: null,
          description: config.description,
          issue: 'Optional header missing',
        });
      }
    }
  });

  results.score =
    totalRequired > 0 ? (passedRequired / totalRequired) * 100 : 100;

  return results;
}

function printResults(url, results) {
  console.log(`\\n🔒 Security Headers Report for: ${url}`);
  console.log('='.repeat(60));

  console.log(`\\n📊 Overall Score: ${results.score.toFixed(1)}%`);

  if (results.passed.length > 0) {
    console.log('\\n✅ Passed Headers:');
    results.passed.forEach(item => {
      console.log(`  • ${item.header}: ${item.value}`);
      console.log(`    ${item.description}`);
    });
  }

  if (results.failed.length > 0) {
    console.log('\\n❌ Failed Headers:');
    results.failed.forEach(item => {
      console.log(`  • ${item.header}: ${item.issue}`);
      console.log(`    ${item.description}`);
      if (item.value) {
        console.log(`    Current value: ${item.value}`);
      }
    });
  }

  if (results.warnings.length > 0) {
    console.log('\\n⚠️  Warnings (Optional Headers):');
    results.warnings.forEach(item => {
      console.log(`  • ${item.header}: ${item.issue}`);
      console.log(`    ${item.description}`);
    });
  }

  console.log('\\n' + '='.repeat(60));

  if (results.score >= 80) {
    console.log('🎉 Great! Your security headers are well configured.');
  } else if (results.score >= 60) {
    console.log(
      '⚠️  Good start, but consider adding the missing required headers.'
    );
  } else {
    console.log(
      '🚨 Security headers need attention. Please add the missing required headers.'
    );
  }
}

async function main() {
  const url = process.argv[2] || 'http://localhost:4173';

  try {
    console.log(`Checking security headers for: ${url}`);

    const response = await checkSecurityHeaders(url);

    if (response.statusCode >= 400) {
      console.error(`❌ HTTP Error: ${response.statusCode}`);
      process.exit(1);
    }

    const results = validateHeaders(response.headers);
    printResults(url, results);

    // Exit with error code if score is below 80%
    if (results.score < 80) {
      process.exit(1);
    }
  } catch (error) {
    console.error(`❌ Error checking headers: ${error.message}`);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
