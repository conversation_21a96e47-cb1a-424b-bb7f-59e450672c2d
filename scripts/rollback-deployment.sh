#!/bin/bash

# VybeCoding.ai Emergency Rollback Script
# 
# This script provides emergency rollback capabilities for production deployments
# Usage: ./scripts/rollback-deployment.sh [environment] [target-version]

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
LOG_FILE="$PROJECT_ROOT/logs/rollback-$(date +%Y%m%d_%H%M%S).log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

# Create logs directory if it doesn't exist
mkdir -p "$PROJECT_ROOT/logs"

# Parse arguments
ENVIRONMENT=${1:-production}
TARGET_VERSION=${2:-}

log "🔄 Starting emergency rollback for $ENVIRONMENT environment"
log "📁 Project root: $PROJECT_ROOT"
log "📝 Log file: $LOG_FILE"

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(staging|production)$ ]]; then
    error "Invalid environment: $ENVIRONMENT. Must be 'staging' or 'production'"
    exit 1
fi

# Function to check if we're in a git repository
check_git_repo() {
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        error "Not in a git repository"
        exit 1
    fi
}

# Function to get the last successful deployment
get_last_deployment() {
    log "🔍 Finding last successful deployment..."
    
    # Look for deployment tags
    local last_tag=$(git tag --sort=-version:refname | grep -E "^(v|release-)" | head -1)
    
    if [ -z "$last_tag" ]; then
        # Fallback to last commit on main branch
        last_tag=$(git rev-parse HEAD~1)
        warning "No deployment tags found, using previous commit: $last_tag"
    else
        log "Found last deployment tag: $last_tag"
    fi
    
    echo "$last_tag"
}

# Function to create rollback branch
create_rollback_branch() {
    local target_ref=$1
    local rollback_branch="rollback-$(date +%Y%m%d_%H%M%S)"
    
    log "🌿 Creating rollback branch: $rollback_branch"
    
    # Ensure we're on the main branch
    git checkout main
    git pull origin main
    
    # Create rollback branch from target reference
    git checkout -b "$rollback_branch" "$target_ref"
    
    echo "$rollback_branch"
}

# Function to verify rollback target
verify_rollback_target() {
    local target_ref=$1
    
    log "✅ Verifying rollback target: $target_ref"
    
    # Check if the reference exists
    if ! git rev-parse --verify "$target_ref" > /dev/null 2>&1; then
        error "Invalid rollback target: $target_ref"
        exit 1
    fi
    
    # Get commit information
    local commit_hash=$(git rev-parse "$target_ref")
    local commit_date=$(git show -s --format=%ci "$target_ref")
    local commit_message=$(git show -s --format=%s "$target_ref")
    
    log "📋 Rollback target details:"
    log "   Commit: $commit_hash"
    log "   Date: $commit_date"
    log "   Message: $commit_message"
    
    # Confirm rollback
    echo
    warning "⚠️  EMERGENCY ROLLBACK CONFIRMATION ⚠️"
    echo "Environment: $ENVIRONMENT"
    echo "Target: $target_ref ($commit_hash)"
    echo "Date: $commit_date"
    echo
    read -p "Are you sure you want to proceed with this rollback? (yes/no): " confirm
    
    if [ "$confirm" != "yes" ]; then
        log "❌ Rollback cancelled by user"
        exit 0
    fi
}

# Function to perform the rollback
perform_rollback() {
    local rollback_branch=$1
    
    log "🚀 Performing rollback deployment..."
    
    # Push rollback branch
    git push origin "$rollback_branch"
    
    # Create emergency deployment
    if [ "$ENVIRONMENT" = "production" ]; then
        log "🏭 Triggering production rollback deployment..."
        
        # Create a release for the rollback
        local rollback_tag="rollback-$(date +%Y%m%d_%H%M%S)"
        git tag "$rollback_tag"
        git push origin "$rollback_tag"
        
        # Trigger GitHub Actions workflow
        if command -v gh > /dev/null 2>&1; then
            gh workflow run deploy-production.yml \
                --ref "$rollback_branch" \
                --field version="$rollback_tag"
        else
            warning "GitHub CLI not available. Manual deployment trigger required."
            log "Please manually trigger production deployment with:"
            log "  Branch: $rollback_branch"
            log "  Tag: $rollback_tag"
        fi
        
    elif [ "$ENVIRONMENT" = "staging" ]; then
        log "🧪 Triggering staging rollback deployment..."
        
        # For staging, we can push directly to develop branch
        git checkout develop
        git reset --hard "$rollback_branch"
        git push --force-with-lease origin develop
        
        log "Staging rollback will be automatically deployed via CI/CD"
    fi
}

# Function to monitor rollback deployment
monitor_deployment() {
    log "👀 Monitoring rollback deployment..."
    
    local health_url
    if [ "$ENVIRONMENT" = "production" ]; then
        health_url="https://vybecoding.ai/api/health"
    else
        health_url="https://vybecoding-staging.vercel.app/api/health"
    fi
    
    log "🏥 Health check URL: $health_url"
    
    # Wait for deployment to complete
    log "⏳ Waiting for deployment to stabilize..."
    sleep 60
    
    # Perform health checks
    local max_attempts=10
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        log "🔍 Health check attempt $attempt/$max_attempts..."
        
        if curl -s -f "$health_url" > /dev/null; then
            success "✅ Health check passed!"
            
            # Get deployment info
            local health_response=$(curl -s "$health_url")
            log "📊 Deployment status: $health_response"
            break
        else
            warning "⚠️ Health check failed (attempt $attempt/$max_attempts)"
            
            if [ $attempt -eq $max_attempts ]; then
                error "❌ Health checks failed after $max_attempts attempts"
                error "🚨 Manual intervention required!"
                exit 1
            fi
            
            sleep 30
        fi
        
        ((attempt++))
    done
}

# Function to cleanup rollback artifacts
cleanup() {
    log "🧹 Cleaning up rollback artifacts..."
    
    # Switch back to main branch
    git checkout main
    
    # Optionally remove rollback branch (keep for audit trail)
    # git branch -D "$rollback_branch"
    
    success "✅ Rollback cleanup completed"
}

# Main execution
main() {
    log "🚨 EMERGENCY ROLLBACK INITIATED 🚨"
    
    # Validate prerequisites
    check_git_repo
    
    # Determine rollback target
    if [ -z "$TARGET_VERSION" ]; then
        TARGET_VERSION=$(get_last_deployment)
    fi
    
    # Verify rollback target
    verify_rollback_target "$TARGET_VERSION"
    
    # Create rollback branch
    local rollback_branch=$(create_rollback_branch "$TARGET_VERSION")
    
    # Perform rollback
    perform_rollback "$rollback_branch"
    
    # Monitor deployment
    monitor_deployment
    
    # Cleanup
    cleanup
    
    success "🎉 Emergency rollback completed successfully!"
    log "📋 Rollback summary:"
    log "   Environment: $ENVIRONMENT"
    log "   Target: $TARGET_VERSION"
    log "   Branch: $rollback_branch"
    log "   Log file: $LOG_FILE"
    
    echo
    success "✅ $ENVIRONMENT environment has been rolled back to $TARGET_VERSION"
    log "📝 Please review the deployment and update incident documentation"
}

# Handle script interruption
trap 'error "❌ Rollback interrupted! Check system state manually."; exit 1' INT TERM

# Execute main function
main "$@"
