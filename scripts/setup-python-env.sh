#!/bin/bash

# 🐍 Python Virtual Environment Setup for VybeCoding.ai
# Solves package version conflicts by creating isolated environments

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_header() {
    echo -e "${BLUE}[PYTHON-ENV]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Project configuration
PROJECT_NAME="vybecoding"
VENV_DIR=".venv"
PYTHON_VERSION="3.10"

print_header "Setting up Python Virtual Environment for VybeCoding.ai"
echo "=================================================="

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    print_error "Python 3 is not installed. Please install Python 3.8+ first."
    exit 1
fi

PYTHON_CMD=$(command -v python3)
CURRENT_VERSION=$($PYTHON_CMD --version | cut -d' ' -f2)
print_header "Found Python: $CURRENT_VERSION"

# Check if virtual environment already exists
if [ -d "$VENV_DIR" ]; then
    print_warning "Virtual environment already exists at $VENV_DIR"
    read -p "Do you want to recreate it? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_header "Removing existing virtual environment..."
        rm -rf "$VENV_DIR"
    else
        print_header "Using existing virtual environment"
        source "$VENV_DIR/bin/activate"
        print_success "Virtual environment activated"
        exit 0
    fi
fi

# Create virtual environment
print_header "Creating virtual environment..."
$PYTHON_CMD -m venv "$VENV_DIR"

# Activate virtual environment
print_header "Activating virtual environment..."
source "$VENV_DIR/bin/activate"

# Upgrade pip
print_header "Upgrading pip..."
pip install --upgrade pip

# Install core dependencies for VybeCoding.ai
print_header "Installing VybeCoding.ai dependencies..."

# Core web framework dependencies
pip install \
    fastapi==0.115.9 \
    uvicorn[standard]==0.34.2 \
    pydantic==2.11.4

# AI and ML dependencies
pip install \
    httpx==0.28.1 \
    openai==1.58.1 \
    anthropic==0.40.0

# Database and storage
pip install \
    chromadb==1.0.8 \
    sentence-transformers==3.3.1

# File monitoring and utilities
pip install \
    watchdog==6.0.0 \
    python-dotenv==1.1.0 \
    toml==0.10.2

# Development and testing
pip install \
    pytest==8.3.4 \
    pytest-asyncio==0.25.0 \
    black==24.10.0 \
    flake8==7.1.1

print_success "Core dependencies installed successfully!"

# Create requirements.txt
print_header "Generating requirements.txt..."
pip freeze > requirements.txt
print_success "Requirements saved to requirements.txt"

# Create activation script
print_header "Creating activation script..."
cat > activate-env.sh << 'EOF'
#!/bin/bash
# VybeCoding.ai Python Environment Activation Script

if [ -d ".venv" ]; then
    source .venv/bin/activate
    echo "✅ VybeCoding.ai Python environment activated"
    echo "📦 Python: $(python --version)"
    echo "📍 Virtual env: $(which python)"
    echo ""
    echo "🎯 Quick commands:"
    echo "  deactivate     - Exit virtual environment"
    echo "  pip list       - Show installed packages"
    echo "  pip install -r requirements.txt  - Install dependencies"
else
    echo "❌ Virtual environment not found. Run ./scripts/setup-python-env.sh first"
    exit 1
fi
EOF

chmod +x activate-env.sh

# Create VS Code settings for Python environment
print_header "Configuring VS Code Python settings..."
mkdir -p .vscode

cat > .vscode/settings.json << EOF
{
    "python.defaultInterpreterPath": "./.venv/bin/python",
    "python.terminal.activateEnvironment": true,
    "python.linting.enabled": true,
    "python.linting.flake8Enabled": true,
    "python.formatting.provider": "black",
    "python.testing.pytestEnabled": true,
    "python.testing.pytestArgs": [
        "vybe-agent/tests"
    ],
    "files.exclude": {
        "**/__pycache__": true,
        "**/*.pyc": true,
        ".venv": false
    }
}
EOF

print_success "VS Code settings configured"

# Create environment info script
cat > check-env.py << 'EOF'
#!/usr/bin/env python3
"""
Check VybeCoding.ai Python Environment Status
"""

import sys
import subprocess
import pkg_resources

def check_python_version():
    version = sys.version_info
    print(f"🐍 Python Version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major == 3 and version.minor >= 8:
        print("✅ Python version is compatible")
        return True
    else:
        print("❌ Python 3.8+ required")
        return False

def check_virtual_env():
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ Running in virtual environment")
        print(f"📍 Environment path: {sys.prefix}")
        return True
    else:
        print("❌ Not running in virtual environment")
        return False

def check_packages():
    required_packages = [
        'fastapi', 'uvicorn', 'pydantic', 'httpx', 
        'chromadb', 'sentence-transformers', 'watchdog'
    ]
    
    print("\n📦 Package Status:")
    all_installed = True
    
    for package in required_packages:
        try:
            version = pkg_resources.get_distribution(package).version
            print(f"  ✅ {package}: {version}")
        except pkg_resources.DistributionNotFound:
            print(f"  ❌ {package}: Not installed")
            all_installed = False
    
    return all_installed

def main():
    print("🔍 VybeCoding.ai Python Environment Check")
    print("=" * 50)
    
    python_ok = check_python_version()
    venv_ok = check_virtual_env()
    packages_ok = check_packages()
    
    print("\n📊 Summary:")
    if python_ok and venv_ok and packages_ok:
        print("✅ Environment is ready for VybeCoding.ai development!")
    else:
        print("❌ Environment needs setup. Run ./scripts/setup-python-env.sh")

if __name__ == "__main__":
    main()
EOF

chmod +x check-env.py

print_success "Environment check script created"

# Final summary
echo ""
print_header "🎉 Python Environment Setup Complete!"
echo "=================================================="
print_success "Virtual environment created at: $VENV_DIR"
print_success "Activation script created: activate-env.sh"
print_success "VS Code configured for Python development"
print_success "Environment check script: check-env.py"

echo ""
print_header "🚀 Next Steps:"
echo "1. Activate environment: source activate-env.sh"
echo "2. Check status: python check-env.py"
echo "3. Start development: python vybe-agent/start_vybe.py"

echo ""
print_header "💡 Tips:"
echo "• Always activate the environment before development"
echo "• Use 'deactivate' to exit the virtual environment"
echo "• Run 'pip install -r requirements.txt' to restore dependencies"
echo "• VS Code will automatically use the virtual environment"

print_success "Ready for conflict-free Python development! 🐍✨"
