#!/bin/bash

# VybeCoding.ai Environment Setup Script
# Sets up Docker environment for development, staging, or production

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
ENVIRONMENT=${1:-development}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# Help function
show_help() {
    cat << EOF
VybeCoding.ai Environment Setup

Usage: $0 [ENVIRONMENT] [OPTIONS]

ENVIRONMENTS:
  development    Set up development environment with hot reload
  staging        Set up staging environment for testing
  production     Set up production environment

OPTIONS:
  --with-appwrite    Include local Appwrite instance (development only)
  --with-monitoring  Include monitoring stack (production only)
  --with-nginx       Include Nginx reverse proxy
  --clean           Clean existing containers and volumes
  --help            Show this help message

EXAMPLES:
  $0 development --with-appwrite
  $0 production --with-monitoring
  $0 staging --clean

EOF
}

# Parse arguments
WITH_APPWRITE=false
WITH_MONITORING=false
WITH_NGINX=false
CLEAN=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --with-appwrite)
            WITH_APPWRITE=true
            shift
            ;;
        --with-monitoring)
            WITH_MONITORING=true
            shift
            ;;
        --with-nginx)
            WITH_NGINX=true
            shift
            ;;
        --clean)
            CLEAN=true
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        development|staging|production)
            ENVIRONMENT=$1
            shift
            ;;
        *)
            error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(development|staging|production)$ ]]; then
    error "Invalid environment: $ENVIRONMENT"
    echo "Valid environments: development, staging, production"
    exit 1
fi

log "Setting up $ENVIRONMENT environment..."

# Change to project root
cd "$PROJECT_ROOT"

# Clean existing containers if requested
if [ "$CLEAN" = true ]; then
    log "Cleaning existing containers and volumes..."
    
    # Stop and remove containers
    docker-compose -f docker-compose.yml down --remove-orphans 2>/dev/null || true
    docker-compose -f docker-compose.${ENVIRONMENT}.yml down --remove-orphans 2>/dev/null || true
    
    # Remove volumes (with confirmation for production)
    if [ "$ENVIRONMENT" = "production" ]; then
        warning "You are about to remove production volumes!"
        read -p "Are you sure? (yes/no): " confirm
        if [ "$confirm" = "yes" ]; then
            docker-compose -f docker-compose.${ENVIRONMENT}.yml down -v
        fi
    else
        docker-compose -f docker-compose.${ENVIRONMENT}.yml down -v 2>/dev/null || true
    fi
    
    success "Cleanup completed"
fi

# Set up environment file
ENV_FILE=".env.${ENVIRONMENT}"
ENV_TEMPLATE=".env.template"

log "Setting up environment configuration..."

# Create environment file from template if it doesn't exist
if [ ! -f "$ENV_FILE" ]; then
    if [ -f "$ENV_TEMPLATE" ]; then
        cp "$ENV_TEMPLATE" "$ENV_FILE"
        log "Created $ENV_FILE from template"
    else
        # Create basic environment file
        cat > "$ENV_FILE" << EOF
# VybeCoding.ai ${ENVIRONMENT^} Environment
NODE_ENV=${ENVIRONMENT}
VITE_ENVIRONMENT=${ENVIRONMENT}

# Appwrite Configuration
VITE_APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
VITE_APPWRITE_PROJECT_ID=${ENVIRONMENT}-project-id
VITE_APPWRITE_DATABASE_ID=${ENVIRONMENT}-database-id

# Application Configuration
PORT=3000
REDIS_PORT=6379
NGINX_PORT=80
NGINX_SSL_PORT=443

# Security (change in production)
REDIS_PASSWORD=your-redis-password
GRAFANA_PASSWORD=your-grafana-password

# Feature Flags
VITE_ENABLE_DEBUG_MODE=$( [ "$ENVIRONMENT" = "production" ] && echo "false" || echo "true" )
VITE_ENABLE_ANALYTICS=$( [ "$ENVIRONMENT" = "production" ] && echo "true" || echo "false" )
VITE_LOG_LEVEL=$( [ "$ENVIRONMENT" = "production" ] && echo "error" || echo "debug" )
EOF
        log "Created basic $ENV_FILE"
    fi
fi

# Validate required environment variables
log "Validating environment configuration..."

required_vars=(
    "VITE_APPWRITE_ENDPOINT"
    "VITE_APPWRITE_PROJECT_ID"
    "VITE_APPWRITE_DATABASE_ID"
)

missing_vars=()
for var in "${required_vars[@]}"; do
    if ! grep -q "^${var}=" "$ENV_FILE" || grep -q "^${var}=$" "$ENV_FILE"; then
        missing_vars+=("$var")
    fi
done

if [ ${#missing_vars[@]} -gt 0 ]; then
    warning "Missing or empty required variables in $ENV_FILE:"
    for var in "${missing_vars[@]}"; do
        echo "  - $var"
    done
    warning "Please update $ENV_FILE with proper values"
fi

# Set up Docker Compose configuration
COMPOSE_FILES="-f docker-compose.yml"

if [ -f "docker-compose.${ENVIRONMENT}.yml" ]; then
    COMPOSE_FILES="$COMPOSE_FILES -f docker-compose.${ENVIRONMENT}.yml"
fi

# Set up profiles
PROFILES=""

if [ "$WITH_APPWRITE" = true ] && [ "$ENVIRONMENT" = "development" ]; then
    PROFILES="$PROFILES --profile with-appwrite"
    log "Including local Appwrite instance"
fi

if [ "$WITH_MONITORING" = true ] && [ "$ENVIRONMENT" = "production" ]; then
    PROFILES="$PROFILES --profile with-monitoring"
    log "Including monitoring stack"
fi

if [ "$WITH_NGINX" = true ]; then
    PROFILES="$PROFILES --profile with-nginx"
    log "Including Nginx reverse proxy"
fi

# Create necessary directories
log "Creating necessary directories..."
mkdir -p logs nginx/ssl monitoring/grafana/provisioning redis

# Set up Nginx configuration if needed
if [ "$WITH_NGINX" = true ] && [ ! -f "nginx/nginx.conf" ]; then
    log "Creating basic Nginx configuration..."
    mkdir -p nginx
    cat > nginx/nginx.conf << 'EOF'
events {
    worker_connections 1024;
}

http {
    upstream vybecoding {
        server vybecoding:3000;
    }

    server {
        listen 80;
        server_name localhost;

        location / {
            proxy_pass http://vybecoding;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /health {
            proxy_pass http://vybecoding/api/health;
        }
    }
}
EOF
fi

# Validate Docker Compose configuration
log "Validating Docker Compose configuration..."
if docker-compose $COMPOSE_FILES --env-file "$ENV_FILE" config > /dev/null; then
    success "Docker Compose configuration is valid"
else
    error "Docker Compose configuration validation failed"
    exit 1
fi

# Display setup summary
log "Environment setup completed!"
echo
success "Configuration Summary:"
echo "  Environment: $ENVIRONMENT"
echo "  Environment file: $ENV_FILE"
echo "  Compose files: $COMPOSE_FILES"
echo "  Profiles: $PROFILES"
echo
log "To start the environment, run:"
echo "  docker-compose $COMPOSE_FILES --env-file $ENV_FILE up -d $PROFILES"
echo
log "To view logs, run:"
echo "  docker-compose $COMPOSE_FILES logs -f"
echo
log "To stop the environment, run:"
echo "  docker-compose $COMPOSE_FILES down"

# Offer to start the environment
echo
read -p "Would you like to start the environment now? (y/n): " start_now

if [[ $start_now =~ ^[Yy]$ ]]; then
    log "Starting $ENVIRONMENT environment..."
    docker-compose $COMPOSE_FILES --env-file "$ENV_FILE" up -d $PROFILES
    
    # Wait for services to be ready
    log "Waiting for services to be ready..."
    sleep 10
    
    # Check health
    if [ "$ENVIRONMENT" = "development" ]; then
        health_url="http://localhost:5173"
    else
        health_url="http://localhost:3000/api/health"
    fi
    
    if curl -s -f "$health_url" > /dev/null 2>&1; then
        success "Environment is ready!"
        echo "  Access the application at: $health_url"
    else
        warning "Environment started but health check failed"
        echo "  Check logs with: docker-compose $COMPOSE_FILES logs"
    fi
fi
