#!/bin/bash
# 🧪 SIMPLE TEST VERSION OF VYBE COMMAND
# Bypasses validation for testing

set -e

echo "🧪 Testing VybeCoding.ai Development Server"
echo "=========================================="
echo "🌐 Network Access: ENABLED (--host)"
echo "🛡️ Simplified test version"
echo ""

# Function to cleanup
cleanup() {
    echo ""
    echo "🛑 Stopping test server..."
    pkill -f "vite dev" 2>/dev/null || true
    lsof -ti:5173 2>/dev/null | xargs kill -9 2>/dev/null || true
    echo "✅ Test server stopped"
    exit 0
}

# Set up cleanup on script exit
trap cleanup EXIT INT TERM

echo "🧹 Step 1: Cleaning up port 5173..."
lsof -ti:5173 2>/dev/null | xargs kill -9 2>/dev/null || true
echo "✅ Port cleaned up"
echo ""

echo "🌐 Step 2: Starting development server with network access..."
echo "📍 Server URLs:"
echo "   🏠 Local:    http://localhost:5173"
echo "   🌐 Network:  http://$(hostname -I | awk '{print $1}'):5173"
echo ""
echo "🛑 Press Ctrl+C to stop"
echo ""

# Start the development server
echo "🚀 Starting Vite development server..."
npx vite dev --host --port 5173 --strictPort
