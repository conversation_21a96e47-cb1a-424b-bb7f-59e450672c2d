#!/bin/bash

# VybeCoding.ai Docker Setup Script
# Initializes Docker environment for development and production

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Docker is installed and running
check_docker() {
    print_status "Checking Docker installation..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    
    print_success "Docker is installed and running"
}

# Function to check if Docker Compose is available
check_docker_compose() {
    print_status "Checking Docker Compose..."
    
    if docker compose version &> /dev/null; then
        print_success "Docker Compose (v2) is available"
        COMPOSE_CMD="docker compose"
    elif command -v docker-compose &> /dev/null; then
        print_success "Docker Compose (v1) is available"
        COMPOSE_CMD="docker-compose"
    else
        print_error "Docker Compose is not available. Please install Docker Compose."
        exit 1
    fi
}

# Function to create environment files if they don't exist
setup_environment_files() {
    print_status "Setting up environment files..."
    
    # Development environment
    if [ ! -f .env.development ]; then
        cat > .env.development << EOF
# VybeCoding.ai Development Environment
NODE_ENV=development
VITE_ENVIRONMENT=development
VITE_HOST=0.0.0.0
VITE_PORT=5173

# Appwrite Configuration (Development)
VITE_APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
VITE_APPWRITE_PROJECT_ID=vybecoding-dev
VITE_APPWRITE_DATABASE_ID=main

# Development specific settings
DEBUG=true
LOG_LEVEL=debug
EOF
        print_success "Created .env.development"
    fi
    
    # Production environment template
    if [ ! -f .env.production.template ]; then
        cat > .env.production.template << EOF
# VybeCoding.ai Production Environment Template
# Copy to .env.production and fill in actual values

NODE_ENV=production
VITE_ENVIRONMENT=production
PORT=3000
HOST=0.0.0.0

# Appwrite Configuration (Production)
VITE_APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
VITE_APPWRITE_PROJECT_ID=your-production-project-id
VITE_APPWRITE_DATABASE_ID=main

# Security settings
LOG_LEVEL=info
DEBUG=false
EOF
        print_success "Created .env.production.template"
    fi
}

# Function to build Docker images
build_images() {
    local environment=${1:-development}
    
    print_status "Building Docker images for $environment environment..."
    
    case $environment in
        "development")
            docker build -f Dockerfile.dev -t vybecoding:dev .
            print_success "Development image built successfully"
            ;;
        "production")
            docker build -f Dockerfile -t vybecoding:prod .
            print_success "Production image built successfully"
            ;;
        "all")
            docker build -f Dockerfile.dev -t vybecoding:dev .
            docker build -f Dockerfile -t vybecoding:prod .
            print_success "All images built successfully"
            ;;
        *)
            print_error "Invalid environment: $environment"
            exit 1
            ;;
    esac
}

# Function to start development environment
start_development() {
    print_status "Starting development environment..."
    
    $COMPOSE_CMD -f docker-compose.dev.yml up -d
    
    print_success "Development environment started!"
    print_status "Application will be available at: http://localhost:5173"
    print_status "To view logs: $COMPOSE_CMD -f docker-compose.dev.yml logs -f"
}

# Function to start production environment
start_production() {
    print_status "Starting production environment..."
    
    if [ ! -f .env.production ]; then
        print_error ".env.production file not found. Please create it from .env.production.template"
        exit 1
    fi
    
    $COMPOSE_CMD up -d
    
    print_success "Production environment started!"
    print_status "Application will be available at: http://localhost:3000"
    print_status "To view logs: $COMPOSE_CMD logs -f"
}

# Function to run tests
run_tests() {
    print_status "Running tests in Docker environment..."
    
    $COMPOSE_CMD -f docker-compose.test.yml up --build --abort-on-container-exit
    
    print_success "Tests completed!"
}

# Function to stop all containers
stop_all() {
    print_status "Stopping all VybeCoding.ai containers..."
    
    $COMPOSE_CMD -f docker-compose.dev.yml down 2>/dev/null || true
    $COMPOSE_CMD down 2>/dev/null || true
    $COMPOSE_CMD -f docker-compose.test.yml down 2>/dev/null || true
    
    print_success "All containers stopped"
}

# Function to clean up Docker resources
cleanup() {
    print_status "Cleaning up Docker resources..."
    
    # Stop all containers
    stop_all
    
    # Remove unused images
    docker image prune -f
    
    # Remove unused volumes
    docker volume prune -f
    
    # Remove unused networks
    docker network prune -f
    
    print_success "Docker cleanup completed"
}

# Function to show help
show_help() {
    echo "VybeCoding.ai Docker Setup Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  setup              - Initial Docker environment setup"
    echo "  build [env]        - Build Docker images (dev|prod|all)"
    echo "  dev                - Start development environment"
    echo "  prod               - Start production environment"
    echo "  test               - Run tests in Docker"
    echo "  stop               - Stop all containers"
    echo "  cleanup            - Clean up Docker resources"
    echo "  help               - Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 setup           - Set up Docker environment"
    echo "  $0 build dev       - Build development image"
    echo "  $0 dev             - Start development server"
    echo "  $0 test            - Run test suite"
}

# Main script logic
main() {
    case ${1:-help} in
        "setup")
            check_docker
            check_docker_compose
            setup_environment_files
            build_images "all"
            print_success "Docker setup completed!"
            print_status "Run '$0 dev' to start development environment"
            ;;
        "build")
            check_docker
            build_images ${2:-development}
            ;;
        "dev")
            check_docker
            check_docker_compose
            start_development
            ;;
        "prod")
            check_docker
            check_docker_compose
            start_production
            ;;
        "test")
            check_docker
            check_docker_compose
            run_tests
            ;;
        "stop")
            check_docker_compose
            stop_all
            ;;
        "cleanup")
            check_docker
            cleanup
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# Run main function with all arguments
main "$@"
