#!/bin/bash
# VybeCoding.ai Documentation Setup Validation
# Validates that all documentation components are properly configured

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# Check if file exists
check_file() {
    local file="$1"
    local description="$2"
    
    if [ -f "$file" ]; then
        success "$description exists: $file"
        return 0
    else
        error "$description missing: $file"
        return 1
    fi
}

# Check if directory exists
check_directory() {
    local dir="$1"
    local description="$2"
    
    if [ -d "$dir" ]; then
        success "$description exists: $dir"
        return 0
    else
        error "$description missing: $dir"
        return 1
    fi
}

# Validate documentation structure
validate_structure() {
    log "Validating documentation structure..."
    
    local errors=0
    
    # Check core documentation files
    check_file "$PROJECT_ROOT/docs/index.md" "Main documentation index" || ((errors++))
    check_file "$PROJECT_ROOT/docs/api/swagger-config.js" "Swagger configuration" || ((errors++))
    check_file "$PROJECT_ROOT/docs/api/index.md" "API documentation index" || ((errors++))
    check_file "$PROJECT_ROOT/docs/components/index.md" "Components documentation index" || ((errors++))
    
    # Check Storybook configuration
    check_file "$PROJECT_ROOT/.storybook/main.ts" "Storybook main configuration" || ((errors++))
    check_file "$PROJECT_ROOT/.storybook/preview.ts" "Storybook preview configuration" || ((errors++))
    
    # Check VitePress configuration
    check_file "$PROJECT_ROOT/docs/.vitepress/config.ts" "VitePress configuration" || ((errors++))
    
    # Check documentation scripts
    check_file "$PROJECT_ROOT/scripts/generate-docs.sh" "Documentation generation script" || ((errors++))
    check_file "$PROJECT_ROOT/scripts/deploy-docs.sh" "Documentation deployment script" || ((errors++))
    
    # Check GitHub Actions workflow
    check_file "$PROJECT_ROOT/.github/workflows/docs.yml" "Documentation workflow" || ((errors++))
    
    if [ $errors -eq 0 ]; then
        success "All documentation structure checks passed!"
    else
        error "Found $errors documentation structure issues"
    fi
    
    return $errors
}

# Validate package.json scripts
validate_scripts() {
    log "Validating package.json documentation scripts..."
    
    local package_json="$PROJECT_ROOT/package.json"
    local errors=0
    
    if [ ! -f "$package_json" ]; then
        error "package.json not found"
        return 1
    fi
    
    # Check for required scripts
    local required_scripts=(
        "docs:api"
        "docs:typedoc"
        "docs:components"
        "docs:storybook"
        "docs:storybook:build"
        "docs:build"
        "docs:deploy"
        "docs:validate"
    )
    
    for script in "${required_scripts[@]}"; do
        if grep -q "\"$script\":" "$package_json"; then
            success "Script '$script' found in package.json"
        else
            error "Script '$script' missing from package.json"
            ((errors++))
        fi
    done
    
    if [ $errors -eq 0 ]; then
        success "All required scripts found in package.json!"
    else
        error "Found $errors missing scripts in package.json"
    fi
    
    return $errors
}

# Validate API documentation annotations
validate_api_docs() {
    log "Validating API documentation annotations..."
    
    local api_dir="$PROJECT_ROOT/src/routes/api"
    local errors=0
    
    if [ ! -d "$api_dir" ]; then
        error "API directory not found: $api_dir"
        return 1
    fi
    
    # Count API files with Swagger annotations
    local api_files=$(find "$api_dir" -name "*.ts" | wc -l)
    local documented_files=$(grep -r "@swagger" "$api_dir" --include="*.ts" | cut -d: -f1 | sort -u | wc -l)
    
    info "Found $api_files API files"
    info "Found $documented_files files with Swagger documentation"
    
    if [ $documented_files -gt 0 ]; then
        success "API documentation annotations found!"
    else
        warning "No Swagger documentation annotations found in API files"
        ((errors++))
    fi
    
    return $errors
}

# Validate component stories
validate_component_stories() {
    log "Validating component stories..."
    
    local components_dir="$PROJECT_ROOT/src/lib/components"
    local errors=0
    
    if [ ! -d "$components_dir" ]; then
        error "Components directory not found: $components_dir"
        return 1
    fi
    
    # Count components and stories
    local component_files=$(find "$components_dir" -name "*.svelte" | wc -l)
    local story_files=$(find "$components_dir" -name "*.stories.ts" -o -name "*.story.ts" | wc -l)
    
    info "Found $component_files Svelte components"
    info "Found $story_files Storybook stories"
    
    if [ $story_files -gt 0 ]; then
        success "Component stories found!"
    else
        warning "No Storybook stories found for components"
        ((errors++))
    fi
    
    return $errors
}

# Generate summary report
generate_summary() {
    log "Generating documentation setup summary..."
    
    local summary_file="$PROJECT_ROOT/docs/documentation-setup-summary.md"
    
    cat > "$summary_file" << EOF
# VybeCoding.ai Documentation Setup Summary

Generated on: $(date)

## 📚 Documentation Components

### ✅ Configured Components

- **API Documentation**: OpenAPI/Swagger with interactive UI
- **Component Library**: Storybook with interactive examples
- **Documentation Site**: VitePress with comprehensive guides
- **TypeScript Docs**: TypeDoc for code documentation
- **Automated Deployment**: GitHub Actions workflow

### 📋 Documentation Structure

\`\`\`
docs/
├── index.md                    # Main documentation index
├── api/
│   ├── index.md               # API documentation
│   ├── swagger-config.js      # Swagger configuration
│   └── openapi.yaml          # Generated OpenAPI spec
├── components/
│   └── index.md              # Component documentation
├── .vitepress/
│   └── config.ts             # VitePress configuration
└── storybook/                # Generated Storybook

.storybook/
├── main.ts                   # Storybook configuration
└── preview.ts               # Storybook preview settings

src/lib/components/
├── ui/
│   ├── Button.stories.ts     # Example story
│   └── Card.stories.ts       # Example story
└── ...                      # Other component stories
\`\`\`

### 🚀 Available Commands

\`\`\`bash
# Generate all documentation
npm run docs:build

# Development servers
npm run docs:storybook         # Component documentation
npm run docs:site             # VitePress site
npm run docs:swagger          # API documentation

# Individual generation
npm run docs:api              # OpenAPI specification
npm run docs:typedoc          # TypeScript documentation
npm run docs:components       # Component documentation

# Deployment
npm run docs:deploy           # Deploy to GitHub Pages
\`\`\`

### 🔗 Quick Links

- **API Documentation**: [docs/api/](api/)
- **Component Library**: [docs/components/](components/)
- **Storybook**: [docs/storybook/](storybook/)
- **GitHub Workflow**: [.github/workflows/docs.yml](../.github/workflows/docs.yml)

---

**Status**: ✅ Documentation automation setup complete
**Next Steps**: Install dependencies and run \`npm run docs:build\`
EOF

    success "Documentation setup summary generated: $summary_file"
}

# Main validation function
main() {
    log "Starting VybeCoding.ai documentation setup validation..."
    
    local total_errors=0
    
    validate_structure || ((total_errors += $?))
    validate_scripts || ((total_errors += $?))
    validate_api_docs || ((total_errors += $?))
    validate_component_stories || ((total_errors += $?))
    
    generate_summary
    
    echo ""
    if [ $total_errors -eq 0 ]; then
        success "🎉 Documentation setup validation completed successfully!"
        success "All components are properly configured."
        info "Next steps:"
        info "1. Install documentation dependencies: npm install"
        info "2. Generate documentation: npm run docs:build"
        info "3. Deploy documentation: npm run docs:deploy"
    else
        error "❌ Documentation setup validation found $total_errors issues"
        error "Please review the errors above and fix them before proceeding."
    fi
    
    return $total_errors
}

# Run main function
main "$@"
