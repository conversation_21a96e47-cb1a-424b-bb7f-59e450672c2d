#!/bin/bash

# VybeCoding.ai Performance Testing Suite
# Comprehensive load testing and performance validation

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
TESTS_DIR="$PROJECT_ROOT/tests"
REPORTS_DIR="$PROJECT_ROOT/reports/performance"
LOG_FILE="$REPORTS_DIR/performance-test-$(date +%Y%m%d_%H%M%S).log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Test configuration
BASE_URL="${BASE_URL:-http://localhost:3000}"
K6_VERSION="${K6_VERSION:-latest}"
CONCURRENT_USERS="${CONCURRENT_USERS:-100}"
TEST_DURATION="${TEST_DURATION:-5m}"
RAMP_UP_TIME="${RAMP_UP_TIME:-2m}"

# Logging function
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1" | tee -a "$LOG_FILE"
}

info() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] INFO:${NC} $1" | tee -a "$LOG_FILE"
}

step() {
    echo -e "${CYAN}[STEP]${NC} $1" | tee -a "$LOG_FILE"
}

# Initialize performance testing environment
initialize_testing() {
    log "🚀 Initializing VybeCoding.ai Performance Testing Suite"
    
    # Create reports directory
    mkdir -p "$REPORTS_DIR"
    
    # Check if K6 is installed
    if ! command -v k6 &> /dev/null; then
        error "K6 is not installed. Please install K6 first."
        info "Installation: https://k6.io/docs/getting-started/installation/"
        exit 1
    fi
    
    # Verify K6 version
    local k6_version=$(k6 version | head -1)
    info "K6 Version: $k6_version"
    
    # Check application availability
    if ! curl -f "$BASE_URL/api/health" &>/dev/null; then
        error "Application not accessible at $BASE_URL"
        info "Please ensure the application is running before starting tests"
        exit 1
    fi
    
    log "Performance testing environment initialized"
}

# Run load tests
run_load_tests() {
    step "📊 Running Load Tests"
    
    local test_results=()
    
    # User Registration Load Test
    info "Running user registration load test..."
    local reg_result=$(run_k6_test "load/user-registration.js" "registration")
    test_results+=("Registration: $reg_result")
    
    # Course Navigation Load Test
    info "Running course navigation load test..."
    local nav_result=$(run_k6_test "load/course-navigation.js" "navigation")
    test_results+=("Navigation: $nav_result")
    
    # API Endpoints Load Test
    info "Running API endpoints load test..."
    local api_result=$(run_k6_test "load/api-endpoints.js" "api")
    test_results+=("API: $api_result")
    
    # Display results summary
    log "Load Test Results Summary:"
    for result in "${test_results[@]}"; do
        info "  $result"
    done
}

# Run stress tests
run_stress_tests() {
    step "🔥 Running Stress Tests"
    
    warning "Starting stress tests - these will push the system to its limits"
    
    # Breaking Point Analysis
    info "Running breaking point stress test..."
    local stress_result=$(run_k6_test "stress/breaking-point.js" "stress")
    
    # Memory Stress Test
    info "Running memory stress test..."
    local memory_result=$(run_k6_test "stress/memory-stress.js" "memory")
    
    # Database Stress Test
    info "Running database stress test..."
    local db_result=$(run_k6_test "stress/database-stress.js" "database")
    
    log "Stress Test Results:"
    info "  Breaking Point: $stress_result"
    info "  Memory Stress: $memory_result"
    info "  Database Stress: $db_result"
}

# Run spike tests
run_spike_tests() {
    step "⚡ Running Spike Tests"
    
    info "Running traffic spike simulation..."
    local spike_result=$(run_k6_test "spike/traffic-spike.js" "spike")
    
    log "Spike Test Results:"
    info "  Traffic Spike: $spike_result"
}

# Run endurance tests
run_endurance_tests() {
    step "⏱️ Running Endurance Tests"
    
    warning "Starting endurance tests - these will run for extended periods"
    
    info "Running 30-minute endurance test..."
    local endurance_result=$(run_k6_test "endurance/long-running.js" "endurance")
    
    log "Endurance Test Results:"
    info "  30-minute test: $endurance_result"
}

# Run individual K6 test
run_k6_test() {
    local test_file="$1"
    local test_name="$2"
    local test_path="$TESTS_DIR/$test_file"
    
    if [ ! -f "$test_path" ]; then
        error "Test file not found: $test_path"
        echo "FAILED"
        return 1
    fi
    
    local report_file="$REPORTS_DIR/${test_name}-$(date +%Y%m%d_%H%M%S).json"
    local html_report="$REPORTS_DIR/${test_name}-$(date +%Y%m%d_%H%M%S).html"
    
    # Run K6 test with JSON output
    if k6 run \
        --out json="$report_file" \
        --summary-export="$html_report" \
        -e BASE_URL="$BASE_URL" \
        "$test_path" 2>&1 | tee -a "$LOG_FILE"; then
        
        # Parse results
        local avg_duration=$(jq -r '.metrics.http_req_duration.values.avg // "N/A"' "$report_file" 2>/dev/null || echo "N/A")
        local p95_duration=$(jq -r '.metrics.http_req_duration.values["p(95)"] // "N/A"' "$report_file" 2>/dev/null || echo "N/A")
        local error_rate=$(jq -r '.metrics.http_req_failed.values.rate // "N/A"' "$report_file" 2>/dev/null || echo "N/A")
        
        echo "PASSED (Avg: ${avg_duration}ms, P95: ${p95_duration}ms, Errors: ${error_rate}%)"
    else
        echo "FAILED"
        return 1
    fi
}

# Generate performance report
generate_performance_report() {
    step "📋 Generating Performance Report"
    
    local report_file="$REPORTS_DIR/performance-summary-$(date +%Y%m%d_%H%M%S).html"
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>VybeCoding.ai Performance Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .header { background: #f4f4f4; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; }
        .metric { background: #e8f5e8; padding: 10px; margin: 5px 0; border-radius: 3px; }
        .warning { background: #fff3cd; padding: 10px; margin: 5px 0; border-radius: 3px; }
        .error { background: #f8d7da; padding: 10px; margin: 5px 0; border-radius: 3px; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>VybeCoding.ai Performance Test Report</h1>
        <p><strong>Generated:</strong> $(date)</p>
        <p><strong>Target URL:</strong> $BASE_URL</p>
        <p><strong>Test Duration:</strong> $TEST_DURATION</p>
        <p><strong>Max Concurrent Users:</strong> $CONCURRENT_USERS</p>
    </div>
    
    <div class="section">
        <h2>Test Summary</h2>
        <div class="metric">
            <strong>Load Tests:</strong> User registration, course navigation, API endpoints
        </div>
        <div class="metric">
            <strong>Stress Tests:</strong> Breaking point analysis, memory stress, database stress
        </div>
        <div class="metric">
            <strong>Spike Tests:</strong> Traffic spike simulation
        </div>
        <div class="metric">
            <strong>Endurance Tests:</strong> Long-running stability validation
        </div>
    </div>
    
    <div class="section">
        <h2>Performance Metrics</h2>
        <table>
            <tr>
                <th>Test Type</th>
                <th>Average Response Time</th>
                <th>95th Percentile</th>
                <th>Error Rate</th>
                <th>Status</th>
            </tr>
            <tr>
                <td>User Registration</td>
                <td id="reg-avg">-</td>
                <td id="reg-p95">-</td>
                <td id="reg-error">-</td>
                <td id="reg-status">-</td>
            </tr>
            <tr>
                <td>Course Navigation</td>
                <td id="nav-avg">-</td>
                <td id="nav-p95">-</td>
                <td id="nav-error">-</td>
                <td id="nav-status">-</td>
            </tr>
            <tr>
                <td>API Endpoints</td>
                <td id="api-avg">-</td>
                <td id="api-p95">-</td>
                <td id="api-error">-</td>
                <td id="api-status">-</td>
            </tr>
        </table>
    </div>
    
    <div class="section">
        <h2>Recommendations</h2>
        <div class="metric">
            <strong>Performance Optimization:</strong> Monitor response times and optimize slow endpoints
        </div>
        <div class="metric">
            <strong>Scalability:</strong> Consider horizontal scaling if breaking point is reached
        </div>
        <div class="metric">
            <strong>Monitoring:</strong> Implement real-time performance monitoring in production
        </div>
        <div class="metric">
            <strong>Regular Testing:</strong> Run performance tests before each major release
        </div>
    </div>
    
    <div class="section">
        <h2>Test Files</h2>
        <p>Detailed test results are available in the following files:</p>
        <ul>
EOF

    # Add links to individual test reports
    for report in "$REPORTS_DIR"/*.json; do
        if [ -f "$report" ]; then
            local filename=$(basename "$report")
            echo "            <li><a href=\"$filename\">$filename</a></li>" >> "$report_file"
        fi
    done

    cat >> "$report_file" << EOF
        </ul>
    </div>
</body>
</html>
EOF
    
    info "Performance report generated: $report_file"
}

# Monitor system resources during tests
monitor_resources() {
    step "📊 Monitoring System Resources"
    
    local monitor_file="$REPORTS_DIR/resource-monitor-$(date +%Y%m%d_%H%M%S).log"
    
    # Start resource monitoring in background
    (
        while true; do
            echo "$(date '+%Y-%m-%d %H:%M:%S') - $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1) - $(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')" >> "$monitor_file"
            sleep 10
        done
    ) &
    
    local monitor_pid=$!
    echo $monitor_pid > "$REPORTS_DIR/monitor.pid"
    
    info "Resource monitoring started (PID: $monitor_pid)"
}

# Stop resource monitoring
stop_monitoring() {
    if [ -f "$REPORTS_DIR/monitor.pid" ]; then
        local monitor_pid=$(cat "$REPORTS_DIR/monitor.pid")
        kill $monitor_pid 2>/dev/null || true
        rm -f "$REPORTS_DIR/monitor.pid"
        info "Resource monitoring stopped"
    fi
}

# Cleanup function
cleanup() {
    stop_monitoring
    log "Performance testing cleanup completed"
}

# Main execution function
main() {
    local test_type="${1:-all}"
    
    # Set up cleanup trap
    trap cleanup EXIT
    
    initialize_testing
    monitor_resources
    
    case "$test_type" in
        "load")
            run_load_tests
            ;;
        "stress")
            run_stress_tests
            ;;
        "spike")
            run_spike_tests
            ;;
        "endurance")
            run_endurance_tests
            ;;
        "all")
            run_load_tests
            run_stress_tests
            run_spike_tests
            # Skip endurance tests in "all" mode by default
            ;;
        *)
            error "Invalid test type: $test_type"
            echo "Usage: $0 [load|stress|spike|endurance|all]"
            exit 1
            ;;
    esac
    
    generate_performance_report
    
    log "🎉 Performance testing completed successfully!"
    info "Reports available in: $REPORTS_DIR"
}

# Handle script arguments
case "${1:-}" in
    --help|-h)
        echo "VybeCoding.ai Performance Testing Suite"
        echo "Comprehensive load testing and performance validation"
        echo ""
        echo "Usage: $0 [test-type] [options]"
        echo ""
        echo "Test Types:"
        echo "  load              Run load tests (default)"
        echo "  stress            Run stress tests"
        echo "  spike             Run spike tests"
        echo "  endurance         Run endurance tests"
        echo "  all               Run all test types (except endurance)"
        echo ""
        echo "Options:"
        echo "  --help, -h        Show this help message"
        echo ""
        echo "Environment Variables:"
        echo "  BASE_URL              Target application URL (default: http://localhost:3000)"
        echo "  CONCURRENT_USERS      Maximum concurrent users (default: 100)"
        echo "  TEST_DURATION         Test duration (default: 5m)"
        echo "  RAMP_UP_TIME          Ramp up time (default: 2m)"
        echo ""
        echo "Examples:"
        echo "  $0 load               Run load tests only"
        echo "  BASE_URL=https://app.vybecoding.ai $0 stress"
        echo "  CONCURRENT_USERS=500 $0 all"
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
