#!/usr/bin/env node

// Create sample course content for VybeCoding.ai
// Run: node scripts/create-sample-content.js

require('dotenv').config();

const sampleCourses = [
  {
    id: 'vybe-method-intro',
    title: 'Introduction to the Vybe Method',
    description:
      'Learn the revolutionary Vybe Method that combines BMAD methodology with Multi-Agent Systems for AI-powered development.',
    difficulty: 'beginner',
    category: 'Methodology',
    estimatedDuration: 120, // 2 hours
    price: 0, // Free course
    isPublished: true,
    thumbnailUrl: '/images/courses/vybe-method-intro.jpg',
    lessons: [
      {
        id: 'lesson-1',
        title: 'What is the Vybe Method?',
        content: `# What is the Vybe Method?

The Vybe Method is a revolutionary approach to AI-powered software development that combines:

## Core Components

### 1. BMAD Method V3
- **B**uild: Rapid prototyping with AI assistance
- **M**easure: Real-time analytics and feedback
- **A**nalyze: AI-driven insights and optimization
- **D**ecide: Data-informed decision making

### 2. Multi-Agent Systems (MAS)
- Specialized AI agents for different development tasks
- Collaborative problem-solving approach
- Autonomous code generation and review
- Continuous learning and improvement

## Why Vybe Method Works

1. **Proven Foundation**: Built on the successful BMAD methodology
2. **AI Integration**: Seamlessly incorporates AI tools and agents
3. **Educational Focus**: Designed for learning and skill development
4. **Real Results**: Generates actual profitable applications

## Learning Outcomes

By the end of this course, you'll understand:
- The core principles of the Vybe Method
- How to integrate AI agents into your workflow
- Building profitable applications with AI assistance
- Best practices for AI-powered development

Let's begin your journey into the future of software development!`,
        order: 1,
        estimatedDuration: 30,
        isPublished: true,
      },
      {
        id: 'lesson-2',
        title: 'BMAD Method Fundamentals',
        content: `# BMAD Method Fundamentals

The BMAD Method forms the foundation of the Vybe Method. Let's explore each component:

## Build Phase
- Rapid prototyping techniques
- AI-assisted code generation
- Component-first development
- Iterative design principles

## Measure Phase
- Analytics integration
- Performance monitoring
- User behavior tracking
- A/B testing strategies

## Analyze Phase
- Data interpretation
- Pattern recognition
- AI-powered insights
- Bottleneck identification

## Decide Phase
- Data-driven decisions
- Strategic planning
- Resource allocation
- Next iteration planning

## Practical Exercise

Try implementing a simple BMAD cycle:
1. Build a basic landing page
2. Measure visitor interactions
3. Analyze the data collected
4. Decide on improvements

This cycle forms the backbone of successful AI-powered development.`,
        order: 2,
        estimatedDuration: 45,
        isPublished: true,
      },
      {
        id: 'lesson-3',
        title: 'Multi-Agent Systems Introduction',
        content: `# Multi-Agent Systems in Development

Multi-Agent Systems (MAS) revolutionize how we approach software development by using specialized AI agents.

## Types of Development Agents

### 1. Analyst Agent (Mary)
- Requirements gathering
- Market research
- User story creation
- Competitive analysis

### 2. Product Manager Agent (John)
- Product roadmap planning
- Feature prioritization
- Stakeholder communication
- Release planning

### 3. Architect Agent (Alex)
- System design
- Technology selection
- Scalability planning
- Security considerations

### 4. Designer Agent (Maya)
- UI/UX design
- User experience optimization
- Accessibility compliance
- Design system creation

### 5. Developer Agent (Larry)
- Code implementation
- Testing and debugging
- Performance optimization
- Documentation

## Agent Collaboration

Agents work together through:
- Shared knowledge base
- Communication protocols
- Consensus mechanisms
- Quality validation

## Benefits of MAS Approach

1. **Specialization**: Each agent excels in their domain
2. **Efficiency**: Parallel processing of tasks
3. **Quality**: Multiple validation layers
4. **Learning**: Continuous improvement through feedback

Start thinking about how these agents can enhance your development workflow!`,
        order: 3,
        estimatedDuration: 45,
        isPublished: true,
      },
    ],
  },
  {
    id: 'ai-coding-fundamentals',
    title: 'AI-Powered Coding Fundamentals',
    description:
      'Master the basics of coding with AI assistance. Learn how to leverage AI tools for faster, better code development.',
    difficulty: 'intermediate',
    category: 'Programming',
    estimatedDuration: 180, // 3 hours
    price: 2900, // $29
    isPublished: true,
    thumbnailUrl: '/images/courses/ai-coding-fundamentals.jpg',
    lessons: [
      {
        id: 'lesson-1',
        title: 'Setting Up Your AI Development Environment',
        content: `# Setting Up Your AI Development Environment

Creating the perfect environment for AI-assisted development is crucial for success.

## Essential Tools

### 1. Code Editors with AI Integration
- **VS Code** with GitHub Copilot
- **Cursor** for AI-first development
- **Replit** for collaborative coding
- **CodeSandbox** for rapid prototyping

### 2. AI Coding Assistants
- **GitHub Copilot**: Code completion and generation
- **Tabnine**: AI-powered autocomplete
- **Codeium**: Free AI coding assistant
- **Amazon CodeWhisperer**: AWS-integrated assistant

### 3. Local AI Models
- **Ollama**: Run models locally
- **LM Studio**: User-friendly interface
- **GPT4All**: Privacy-focused AI
- **Code Llama**: Specialized for coding

## Configuration Best Practices

\`\`\`json
{
  "editor.inlineSuggest.enabled": true,
  "github.copilot.enable": {
    "*": true,
    "yaml": false,
    "plaintext": false
  },
  "editor.suggestSelection": "first",
  "editor.tabCompletion": "on"
}
\`\`\`

## Workflow Integration

1. **Start with prompts**: Clear, specific instructions
2. **Iterate quickly**: Use AI for rapid prototyping
3. **Review and refine**: Always validate AI-generated code
4. **Learn patterns**: Understand what AI suggests and why

Your AI development environment is the foundation of productive coding!`,
        order: 1,
        estimatedDuration: 45,
        isPublished: true,
      },
    ],
  },
];

function generateCourseFiles() {
  console.log('🚀 Creating sample course content for VybeCoding.ai...\n');

  // Create courses directory structure
  const fs = require('fs');
  const path = require('path');

  const coursesDir = path.join(process.cwd(), 'static', 'courses');
  if (!fs.existsSync(coursesDir)) {
    fs.mkdirSync(coursesDir, { recursive: true });
  }

  // Generate course data files
  sampleCourses.forEach(course => {
    const courseDir = path.join(coursesDir, course.id);
    if (!fs.existsSync(courseDir)) {
      fs.mkdirSync(courseDir, { recursive: true });
    }

    // Create course metadata
    const courseData = {
      ...course,
      lessons: course.lessons.map(lesson => lesson.id),
    };

    fs.writeFileSync(
      path.join(courseDir, 'course.json'),
      JSON.stringify(courseData, null, 2)
    );

    // Create lesson files
    course.lessons.forEach(lesson => {
      fs.writeFileSync(path.join(courseDir, `${lesson.id}.md`), lesson.content);

      // Create lesson metadata
      const lessonData = {
        id: lesson.id,
        title: lesson.title,
        order: lesson.order,
        estimatedDuration: lesson.estimatedDuration,
        isPublished: lesson.isPublished,
        courseId: course.id,
      };

      fs.writeFileSync(
        path.join(courseDir, `${lesson.id}.json`),
        JSON.stringify(lessonData, null, 2)
      );
    });

    console.log(`✅ Created course: ${course.title}`);
    console.log(`   📁 Directory: static/courses/${course.id}`);
    console.log(`   📚 Lessons: ${course.lessons.length}`);
    console.log('');
  });

  // Create course catalog
  const catalog = {
    courses: sampleCourses.map(course => ({
      id: course.id,
      title: course.title,
      description: course.description,
      difficulty: course.difficulty,
      category: course.category,
      estimatedDuration: course.estimatedDuration,
      price: course.price,
      isPublished: course.isPublished,
      thumbnailUrl: course.thumbnailUrl,
      lessonCount: course.lessons.length,
    })),
    categories: ['Methodology', 'Programming', 'AI Tools', 'Business'],
    difficulties: ['beginner', 'intermediate', 'advanced', 'masterclass'],
  };

  fs.writeFileSync(
    path.join(coursesDir, 'catalog.json'),
    JSON.stringify(catalog, null, 2)
  );

  console.log('🎉 SAMPLE CONTENT CREATED!');
  console.log('\n✅ Generated:');
  console.log(`   📚 ${sampleCourses.length} courses`);
  console.log(
    `   📖 ${sampleCourses.reduce((total, course) => total + course.lessons.length, 0)} lessons`
  );
  console.log('   📋 Course catalog');
  console.log('\n📁 Files created in: static/courses/');
  console.log('\n🚀 Your platform now has sample content to test with!');
}

// Run the content generation
generateCourseFiles();
