#!/usr/bin/env node

/**
 * Deployment Monitoring Script
 *
 * Monitors deployment health and performance for VybeCoding.ai
 * Used by CI/CD pipelines and operations teams
 */

import https from 'https';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, '..');

/**
 * Environment configurations
 */
const environments = {
  staging: {
    name: 'Staging',
    url: 'https://vybecoding-staging.vercel.app',
    healthEndpoint: '/api/health',
    expectedStatus: 200,
    timeout: 30000,
  },
  production: {
    name: 'Production',
    url: 'https://vybecoding.ai',
    healthEndpoint: '/api/health',
    expectedStatus: 200,
    timeout: 15000,
  },
};

/**
 * Make HTTP request with timeout
 */
function makeRequest(url, timeout = 30000) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();

    const req = https.get(url, { timeout }, res => {
      let data = '';

      res.on('data', chunk => {
        data += chunk;
      });

      res.on('end', () => {
        const responseTime = Date.now() - startTime;

        try {
          const parsedData = data ? JSON.parse(data) : {};
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: parsedData,
            responseTime,
            success: res.statusCode >= 200 && res.statusCode < 300,
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: data,
            responseTime,
            success: res.statusCode >= 200 && res.statusCode < 300,
            parseError: error.message,
          });
        }
      });
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error(`Request timeout after ${timeout}ms`));
    });

    req.on('error', error => {
      reject(error);
    });
  });
}

/**
 * Check health endpoint
 */
async function checkHealth(environment) {
  const config = environments[environment];
  if (!config) {
    throw new Error(`Unknown environment: ${environment}`);
  }

  const healthUrl = `${config.url}${config.healthEndpoint}`;

  console.log(`🏥 Checking health for ${config.name}: ${healthUrl}`);

  try {
    const response = await makeRequest(healthUrl, config.timeout);

    const result = {
      environment,
      url: healthUrl,
      timestamp: new Date().toISOString(),
      statusCode: response.statusCode,
      responseTime: response.responseTime,
      success: response.success,
      data: response.data,
    };

    if (response.success) {
      console.log(`✅ ${config.name} health check passed`);
      console.log(`   Status: ${response.statusCode}`);
      console.log(`   Response time: ${response.responseTime}ms`);

      if (response.data && response.data.status) {
        console.log(`   Health status: ${response.data.status}`);
        console.log(
          `   Environment: ${response.data.environment || 'unknown'}`
        );
        console.log(`   Version: ${response.data.version || 'unknown'}`);
      }
    } else {
      console.log(`❌ ${config.name} health check failed`);
      console.log(`   Status: ${response.statusCode}`);
      console.log(`   Response time: ${response.responseTime}ms`);
    }

    return result;
  } catch (error) {
    console.log(`❌ ${config.name} health check error: ${error.message}`);

    return {
      environment,
      url: healthUrl,
      timestamp: new Date().toISOString(),
      success: false,
      error: error.message,
    };
  }
}

/**
 * Check multiple endpoints
 */
async function checkEndpoints(environment, endpoints = []) {
  const config = environments[environment];
  const results = [];

  // Default endpoints to check
  const defaultEndpoints = ['/', '/api/health', '/workspace', '/courses'];

  const endpointsToCheck = endpoints.length > 0 ? endpoints : defaultEndpoints;

  console.log(
    `🔍 Checking ${endpointsToCheck.length} endpoints for ${config.name}...`
  );

  for (const endpoint of endpointsToCheck) {
    const url = `${config.url}${endpoint}`;

    try {
      const response = await makeRequest(url, config.timeout);

      const result = {
        endpoint,
        url,
        statusCode: response.statusCode,
        responseTime: response.responseTime,
        success: response.success,
      };

      results.push(result);

      if (response.success) {
        console.log(
          `   ✅ ${endpoint} - ${response.statusCode} (${response.responseTime}ms)`
        );
      } else {
        console.log(
          `   ❌ ${endpoint} - ${response.statusCode} (${response.responseTime}ms)`
        );
      }
    } catch (error) {
      console.log(`   ❌ ${endpoint} - Error: ${error.message}`);
      results.push({
        endpoint,
        url,
        success: false,
        error: error.message,
      });
    }
  }

  return results;
}

/**
 * Generate monitoring report
 */
function generateReport(healthResults, endpointResults) {
  const timestamp = new Date().toISOString();

  const report = {
    timestamp,
    summary: {
      totalEnvironments: healthResults.length,
      healthyEnvironments: healthResults.filter(r => r.success).length,
      totalEndpoints: endpointResults.reduce(
        (sum, env) => sum + env.results.length,
        0
      ),
      healthyEndpoints: endpointResults.reduce(
        (sum, env) => sum + env.results.filter(r => r.success).length,
        0
      ),
    },
    health: healthResults,
    endpoints: endpointResults,
  };

  // Write report to file
  const reportFile = path.join(
    projectRoot,
    'logs',
    `deployment-monitor-${Date.now()}.json`
  );

  // Ensure logs directory exists
  const logsDir = path.dirname(reportFile);
  if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
  }

  fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));

  console.log(`\n📊 Monitoring report saved: ${reportFile}`);

  return report;
}

/**
 * Main monitoring function
 */
async function monitor(
  environmentsToCheck = ['staging', 'production'],
  options = {}
) {
  console.log('🚀 Starting deployment monitoring...\n');

  const healthResults = [];
  const endpointResults = [];

  for (const environment of environmentsToCheck) {
    console.log(`\n🔍 Monitoring ${environment} environment:`);

    // Check health endpoint
    const healthResult = await checkHealth(environment);
    healthResults.push(healthResult);

    // Check additional endpoints if requested
    if (options.checkEndpoints) {
      const endpoints = await checkEndpoints(environment, options.endpoints);
      endpointResults.push({
        environment,
        results: endpoints,
      });
    }

    // Wait between environment checks using real interval
    if (
      environmentsToCheck.indexOf(environment) <
      environmentsToCheck.length - 1
    ) {
      await new Promise(resolve => {
        const checkInterval = setInterval(() => {
          clearInterval(checkInterval);
          resolve();
        }, 2000);
      });
    }
  }

  // Generate report
  const report = generateReport(healthResults, endpointResults);

  // Summary
  console.log('\n📋 Monitoring Summary:');
  console.log(`   Environments checked: ${report.summary.totalEnvironments}`);
  console.log(`   Healthy environments: ${report.summary.healthyEnvironments}`);

  if (options.checkEndpoints) {
    console.log(`   Endpoints checked: ${report.summary.totalEndpoints}`);
    console.log(`   Healthy endpoints: ${report.summary.healthyEndpoints}`);
  }

  // Exit with appropriate code
  const allHealthy =
    report.summary.healthyEnvironments === report.summary.totalEnvironments;

  if (allHealthy) {
    console.log('\n✅ All systems operational');
    process.exit(0);
  } else {
    console.log('\n❌ Some systems are unhealthy');
    process.exit(1);
  }
}

/**
 * CLI interface
 */
function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  if (command === 'help' || command === '--help' || command === '-h') {
    console.log(`
VybeCoding.ai Deployment Monitor

Usage:
  node scripts/monitor-deployment.js [environment] [options]

Commands:
  monitor [env]     Monitor specific environment (staging|production)
  monitor all       Monitor all environments (default)
  health [env]      Quick health check only
  endpoints [env]   Check all endpoints for environment

Options:
  --endpoints       Include endpoint checks
  --timeout=ms      Set request timeout (default: 30000)

Examples:
  node scripts/monitor-deployment.js
  node scripts/monitor-deployment.js staging
  node scripts/monitor-deployment.js production --endpoints
  node scripts/monitor-deployment.js health staging
`);
    return;
  }

  // Parse arguments
  const environmentArg = args[0];
  const options = {
    checkEndpoints: args.includes('--endpoints'),
    timeout:
      args.find(arg => arg.startsWith('--timeout='))?.split('=')[1] || 30000,
  };

  let environmentsToCheck = ['staging', 'production'];

  if (
    environmentArg &&
    environmentArg !== 'all' &&
    environments[environmentArg]
  ) {
    environmentsToCheck = [environmentArg];
  }

  // Quick health check mode
  if (command === 'health') {
    const env = args[1] || 'production';
    checkHealth(env)
      .then(() => process.exit(0))
      .catch(() => process.exit(1));
    return;
  }

  // Endpoint check mode
  if (command === 'endpoints') {
    const env = args[1] || 'production';
    options.checkEndpoints = true;
    environmentsToCheck = [env];
  }

  // Run monitoring
  monitor(environmentsToCheck, options).catch(error => {
    console.error('❌ Monitoring failed:', error.message);
    process.exit(1);
  });
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { monitor, checkHealth, checkEndpoints };
