#!/bin/bash

# Complete VybeCoding.ai Platform Setup
# Run: chmod +x scripts/complete-setup.sh && ./scripts/complete-setup.sh

echo "🚀 VybeCoding.ai Complete Platform Setup"
echo "========================================"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ID="683b200e00153d705da3"
DATABASE_ID="683b231d003c1c558e20"
ENDPOINT="https://fra.cloud.appwrite.io/v1"

echo -e "${BLUE}📊 Configuration:${NC}"
echo "   Project ID: $PROJECT_ID"
echo "   Database ID: $DATABASE_ID"
echo "   Endpoint: $ENDPOINT"
echo ""

# Step 1: Create sample course content
echo -e "${YELLOW}📚 STEP 1: Creating Sample Course Content${NC}"
echo "Creating course directories..."

mkdir -p static/courses/vybe-method-intro
mkdir -p static/courses/ai-coding-fundamentals
mkdir -p static/images/courses

# Create Vybe Method Intro course
cat > static/courses/vybe-method-intro/course.json << 'EOF'
{
  "id": "vybe-method-intro",
  "title": "Introduction to the Vybe Method",
  "description": "Learn the revolutionary Vybe Method that combines BMAD methodology with Multi-Agent Systems for AI-powered development.",
  "difficulty": "beginner",
  "category": "Methodology",
  "estimatedDuration": 120,
  "price": 0,
  "isPublished": true,
  "thumbnailUrl": "/images/courses/vybe-method-intro.jpg",
  "lessons": ["lesson-1", "lesson-2", "lesson-3"]
}
EOF

cat > static/courses/vybe-method-intro/lesson-1.md << 'EOF'
# What is the Vybe Method?

The Vybe Method is a revolutionary approach to AI-powered software development that combines:

## Core Components

### 1. BMAD Method V3
- **B**uild: Rapid prototyping with AI assistance
- **M**easure: Real-time analytics and feedback
- **A**nalyze: AI-driven insights and optimization
- **D**ecide: Data-informed decision making

### 2. Multi-Agent Systems (MAS)
- Specialized AI agents for different development tasks
- Collaborative problem-solving approach
- Autonomous code generation and review
- Continuous learning and improvement

## Why Vybe Method Works

1. **Proven Foundation**: Built on the successful BMAD methodology
2. **AI Integration**: Seamlessly incorporates AI tools and agents
3. **Educational Focus**: Designed for learning and skill development
4. **Real Results**: Generates actual profitable applications

## Learning Outcomes

By the end of this course, you'll understand:
- The core principles of the Vybe Method
- How to integrate AI agents into your workflow
- Building profitable applications with AI assistance
- Best practices for AI-powered development

Let's begin your journey into the future of software development!
EOF

cat > static/courses/vybe-method-intro/lesson-1.json << 'EOF'
{
  "id": "lesson-1",
  "title": "What is the Vybe Method?",
  "order": 1,
  "estimatedDuration": 30,
  "isPublished": true,
  "courseId": "vybe-method-intro"
}
EOF

# Create course catalog
cat > static/courses/catalog.json << 'EOF'
{
  "courses": [
    {
      "id": "vybe-method-intro",
      "title": "Introduction to the Vybe Method",
      "description": "Learn the revolutionary Vybe Method that combines BMAD methodology with Multi-Agent Systems for AI-powered development.",
      "difficulty": "beginner",
      "category": "Methodology",
      "estimatedDuration": 120,
      "price": 0,
      "isPublished": true,
      "thumbnailUrl": "/images/courses/vybe-method-intro.jpg",
      "lessonCount": 3
    },
    {
      "id": "ai-coding-fundamentals",
      "title": "AI-Powered Coding Fundamentals",
      "description": "Master the basics of coding with AI assistance. Learn how to leverage AI tools for faster, better code development.",
      "difficulty": "intermediate",
      "category": "Programming",
      "estimatedDuration": 180,
      "price": 2900,
      "isPublished": true,
      "thumbnailUrl": "/images/courses/ai-coding-fundamentals.jpg",
      "lessonCount": 5
    }
  ],
  "categories": ["Methodology", "Programming", "AI Tools", "Business"],
  "difficulties": ["beginner", "intermediate", "advanced", "masterclass"]
}
EOF

echo -e "${GREEN}✅ Sample course content created${NC}"
echo ""

# Step 2: Create placeholder images
echo -e "${YELLOW}🖼️  STEP 2: Creating Placeholder Images${NC}"

# Create simple placeholder images (SVG)
cat > static/images/courses/vybe-method-intro.svg << 'EOF'
<svg width="400" height="200" xmlns="http://www.w3.org/2000/svg">
  <rect width="400" height="200" fill="#6366f1"/>
  <text x="200" y="100" text-anchor="middle" fill="white" font-size="24" font-family="Arial">Vybe Method</text>
  <text x="200" y="130" text-anchor="middle" fill="white" font-size="16" font-family="Arial">Introduction Course</text>
</svg>
EOF

cat > static/images/courses/ai-coding-fundamentals.svg << 'EOF'
<svg width="400" height="200" xmlns="http://www.w3.org/2000/svg">
  <rect width="400" height="200" fill="#059669"/>
  <text x="200" y="100" text-anchor="middle" fill="white" font-size="24" font-family="Arial">AI Coding</text>
  <text x="200" y="130" text-anchor="middle" fill="white" font-size="16" font-family="Arial">Fundamentals</text>
</svg>
EOF

echo -e "${GREEN}✅ Placeholder images created${NC}"
echo ""

# Step 3: Create test user data
echo -e "${YELLOW}👤 STEP 3: Creating Test User Data${NC}"

mkdir -p static/data

cat > static/data/test-users.json << 'EOF'
{
  "testUsers": [
    {
      "email": "<EMAIL>",
      "name": "Test Student",
      "role": "student",
      "subscriptionTier": "free"
    },
    {
      "email": "<EMAIL>", 
      "name": "Pro User",
      "role": "student",
      "subscriptionTier": "pro"
    },
    {
      "email": "<EMAIL>",
      "name": "Test Instructor", 
      "role": "instructor",
      "subscriptionTier": "enterprise"
    }
  ]
}
EOF

echo -e "${GREEN}✅ Test user data created${NC}"
echo ""

# Step 4: Create development scripts
echo -e "${YELLOW}🔧 STEP 4: Creating Development Scripts${NC}"

cat > scripts/dev-helpers.sh << 'EOF'
#!/bin/bash

# Development helper scripts for VybeCoding.ai

case "$1" in
  "start")
    echo "🚀 Starting VybeCoding.ai development server..."
    npm run dev
    ;;
  "test")
    echo "🧪 Running tests..."
    npm run test
    ;;
  "build")
    echo "🏗️  Building for production..."
    npm run build
    ;;
  "preview")
    echo "👀 Preview production build..."
    npm run preview
    ;;
  "lint")
    echo "🔍 Linting code..."
    npm run lint
    ;;
  *)
    echo "VybeCoding.ai Development Helper"
    echo "Usage: ./scripts/dev-helpers.sh [command]"
    echo ""
    echo "Commands:"
    echo "  start   - Start development server"
    echo "  test    - Run tests"
    echo "  build   - Build for production"
    echo "  preview - Preview production build"
    echo "  lint    - Lint code"
    ;;
esac
EOF

chmod +x scripts/dev-helpers.sh

echo -e "${GREEN}✅ Development scripts created${NC}"
echo ""

# Step 5: Create README for manual steps
echo -e "${YELLOW}📋 STEP 5: Creating Setup Instructions${NC}"

cat > MANUAL-SETUP-STEPS.md << 'EOF'
# 🎯 Manual Setup Steps for VybeCoding.ai

## ✅ Completed Automatically
- [x] Sample course content created
- [x] Placeholder images generated  
- [x] Test user data prepared
- [x] Development scripts ready

## 📋 Manual Steps Required

### 1. Appwrite Collections (5 minutes)
Go to: https://fra.cloud.appwrite.io/console/project/683b200e00153d705da3/databases/database/683b231d003c1c558e20

Create these collections:
- `users` - User management
- `courses` - Course catalog  
- `lessons` - Lesson content
- `progress` - Learning progress
- `course_purchases` - Payment records

### 2. Stripe Products (10 minutes)
Go to: https://dashboard.stripe.com/products

Create these products:
- Pro Plan ($29/month)
- Enterprise Plan ($99/month)

Update price IDs in: `src/lib/config/stripe.js`

### 3. Test Your Platform
1. Visit: http://localhost:5175
2. Test pricing page: http://localhost:5175/pricing
3. Try user registration
4. Browse sample courses

## 🚀 You're Ready to Launch!
EOF

echo -e "${GREEN}✅ Setup instructions created${NC}"
echo ""

# Final summary
echo -e "${GREEN}🎉 SETUP COMPLETE!${NC}"
echo ""
echo -e "${BLUE}✅ What's Ready:${NC}"
echo "   📚 Sample course content"
echo "   🖼️  Placeholder images"
echo "   👤 Test user data"
echo "   🔧 Development scripts"
echo "   📋 Manual setup guide"
echo ""
echo -e "${YELLOW}📋 Next Steps:${NC}"
echo "   1. Create Appwrite collections (see MANUAL-SETUP-STEPS.md)"
echo "   2. Create Stripe products"
echo "   3. Test your platform at http://localhost:5175"
echo ""
echo -e "${GREEN}🚀 Your VybeCoding.ai platform is ready for testing!${NC}"
