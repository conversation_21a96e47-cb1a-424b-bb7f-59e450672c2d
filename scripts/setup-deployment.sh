#!/bin/bash

# VybeCoding.ai Deployment Setup Script
# Sets up environment variables and deployment configuration

set -e

echo "🚀 VybeCoding.ai Deployment Setup"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "This script must be run from the project root directory"
    exit 1
fi

# Get deployment environment
ENVIRONMENT=${1:-staging}
if [ "$ENVIRONMENT" != "staging" ] && [ "$ENVIRONMENT" != "production" ]; then
    print_error "Invalid environment. Use 'staging' or 'production'"
    exit 1
fi

print_info "Setting up deployment for: $ENVIRONMENT"

# Create .env file for the environment
ENV_FILE="src/.env.${ENVIRONMENT}"
print_info "Creating environment file: $ENV_FILE"

# Get git information
GIT_COMMIT=$(git rev-parse HEAD 2>/dev/null || echo "unknown")
GIT_BRANCH=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown")
BUILD_TIME=$(date -u +"%Y-%m-%dT%H:%M:%SZ")

# Create environment file
cat > "$ENV_FILE" << EOF
# VybeCoding.ai ${ENVIRONMENT^} Environment Configuration
# Generated on: $BUILD_TIME

# Environment
VITE_ENVIRONMENT=$ENVIRONMENT
VITE_BUILD_TIME=$BUILD_TIME
VITE_COMMIT_HASH=$GIT_COMMIT
VITE_BRANCH=$GIT_BRANCH

# Appwrite Configuration (${ENVIRONMENT^})
VITE_APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
VITE_APPWRITE_PROJECT_ID=\${${ENVIRONMENT^^}_APPWRITE_PROJECT_ID}
VITE_APPWRITE_DATABASE_ID=\${${ENVIRONMENT^^}_APPWRITE_DATABASE_ID}

# Debug Settings
VITE_DEBUG=$([ "$ENVIRONMENT" = "staging" ] && echo "true" || echo "false")

# Analytics (disabled for staging)
VITE_ANALYTICS_ENABLED=$([ "$ENVIRONMENT" = "production" ] && echo "true" || echo "false")

# Local LLM Configuration (Development/MAS only)
VITE_LOCAL_LLM_ENDPOINT=http://localhost:11434
VITE_OLLAMA_ENDPOINT=http://localhost:11434
EOF

print_status "Environment file created: $ENV_FILE"

# Create Vercel configuration
VERCEL_CONFIG="vercel.${ENVIRONMENT}.json"
print_info "Creating Vercel configuration: $VERCEL_CONFIG"

cat > "$VERCEL_CONFIG" << EOF
{
  "name": "vybecoding-${ENVIRONMENT}",
  "version": 2,
  "framework": "sveltekit",
  "buildCommand": "cd src && npm run build",
  "outputDirectory": "src/build",
  "installCommand": "cd src && npm ci",
  "devCommand": "cd src && npm run dev",
  "env": {
    "VITE_ENVIRONMENT": "$ENVIRONMENT",
    "VITE_BUILD_TIME": "$BUILD_TIME",
    "VITE_COMMIT_HASH": "$GIT_COMMIT",
    "VITE_BRANCH": "$GIT_BRANCH"
  },
  "build": {
    "env": {
      "VITE_APPWRITE_ENDPOINT": "https://cloud.appwrite.io/v1",
      "VITE_APPWRITE_PROJECT_ID": "@${ENVIRONMENT}_appwrite_project_id",
      "VITE_APPWRITE_DATABASE_ID": "@${ENVIRONMENT}_appwrite_database_id",
      "VITE_DEBUG": "$([ "$ENVIRONMENT" = "staging" ] && echo "true" || echo "false")"
    }
  },
  "functions": {
    "src/routes/api/**": {
      "runtime": "nodejs18.x"
    }
  },
  "headers": [
    {
      "source": "/api/(.*)",
      "headers": [
        {
          "key": "Access-Control-Allow-Origin",
          "value": "*"
        },
        {
          "key": "Access-Control-Allow-Methods",
          "value": "GET, POST, PUT, DELETE, OPTIONS"
        },
        {
          "key": "Access-Control-Allow-Headers",
          "value": "Content-Type, Authorization"
        }
      ]
    }
  ],
  "rewrites": [
    {
      "source": "/api/(.*)",
      "destination": "/api/\$1"
    }
  ]
}
EOF

print_status "Vercel configuration created: $VERCEL_CONFIG"

# Create deployment script
DEPLOY_SCRIPT="scripts/deploy-${ENVIRONMENT}.sh"
print_info "Creating deployment script: $DEPLOY_SCRIPT"

cat > "$DEPLOY_SCRIPT" << 'EOF'
#!/bin/bash

# VybeCoding.ai Deployment Script for ENVIRONMENT_PLACEHOLDER
set -e

ENVIRONMENT="ENVIRONMENT_PLACEHOLDER"
echo "🚀 Deploying VybeCoding.ai to $ENVIRONMENT"

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check prerequisites
if ! command -v vercel &> /dev/null; then
    print_error "Vercel CLI is not installed. Install with: npm install -g vercel"
    exit 1
fi

if [ ! -f "vercel.$ENVIRONMENT.json" ]; then
    print_error "Vercel configuration not found: vercel.$ENVIRONMENT.json"
    exit 1
fi

# Run tests before deployment
print_status "Running tests..."
cd src
npm test -- --run

# Type check
print_status "Type checking..."
npx tsc --noEmit

# Build check
print_status "Build verification..."
npm run build

# Deploy to Vercel
print_status "Deploying to $ENVIRONMENT..."
cd ..

if [ "$ENVIRONMENT" = "production" ]; then
    vercel deploy --prod --local-config="vercel.$ENVIRONMENT.json"
else
    vercel deploy --local-config="vercel.$ENVIRONMENT.json"
fi

print_status "Deployment to $ENVIRONMENT completed!"
EOF

# Replace placeholder with actual environment
sed -i "s/ENVIRONMENT_PLACEHOLDER/$ENVIRONMENT/g" "$DEPLOY_SCRIPT"
chmod +x "$DEPLOY_SCRIPT"

print_status "Deployment script created: $DEPLOY_SCRIPT"

# Create GitHub Secrets documentation
SECRETS_DOC="docs/deployment-secrets.md"
mkdir -p docs
print_info "Creating secrets documentation: $SECRETS_DOC"

cat > "$SECRETS_DOC" << EOF
# GitHub Secrets Configuration

This document lists the required GitHub Secrets for VybeCoding.ai deployment.

## Required Secrets

### Vercel Configuration
- \`VERCEL_TOKEN\`: Vercel deployment token
- \`VERCEL_ORG_ID\`: Vercel organization ID
- \`VERCEL_STAGING_PROJECT_ID\`: Staging project ID
- \`VERCEL_PRODUCTION_PROJECT_ID\`: Production project ID

### Staging Environment
- \`STAGING_APPWRITE_ENDPOINT\`: https://cloud.appwrite.io/v1
- \`STAGING_APPWRITE_PROJECT_ID\`: Staging Appwrite project ID
- \`STAGING_APPWRITE_DATABASE_ID\`: Staging database ID

### Production Environment
- \`PRODUCTION_APPWRITE_ENDPOINT\`: https://cloud.appwrite.io/v1
- \`PRODUCTION_APPWRITE_PROJECT_ID\`: Production Appwrite project ID
- \`PRODUCTION_APPWRITE_DATABASE_ID\`: Production database ID

## Setup Instructions

1. Go to your GitHub repository settings
2. Navigate to Secrets and variables > Actions
3. Add each secret with the corresponding value
4. Ensure all secrets are properly configured before running deployments

## Verification

You can verify the secrets are working by:
1. Running the staging deployment workflow
2. Checking the health endpoint: \`/api/health\`
3. Verifying environment variables in the deployment logs

## Security Notes

- Never commit secrets to the repository
- Rotate secrets regularly
- Use environment-specific secrets
- Monitor secret usage in deployment logs
EOF

print_status "Secrets documentation created: $SECRETS_DOC"

# Summary
echo ""
echo "🎉 Deployment setup completed for $ENVIRONMENT!"
echo ""
echo "📁 Files created:"
echo "   - $ENV_FILE"
echo "   - $VERCEL_CONFIG"
echo "   - $DEPLOY_SCRIPT"
echo "   - $SECRETS_DOC"
echo ""
echo "📋 Next steps:"
echo "   1. Configure GitHub Secrets (see $SECRETS_DOC)"
echo "   2. Set up Vercel projects for staging and production"
echo "   3. Configure Appwrite projects for each environment"
echo "   4. Test deployment with: ./$DEPLOY_SCRIPT"
echo ""
print_warning "Remember to configure all required secrets before deploying!"
