#!/usr/bin/env python3
"""
Generate Real Content for VybeCoding.ai Platform
Uses real MAS agents to create courses, news articles, and Vybe Qubes
NO SIMULATIONS - All content is generated by real autonomous agents
"""

import asyncio
import aiohttp
import json
from datetime import datetime

class ContentGenerator:
    def __init__(self):
        self.base_url = "http://localhost:5173"
        self.mas_coordinator_url = "http://localhost:8765"
        
    async def generate_course(self, topic: str, audience: str, complexity: str):
        """Generate a real course using MAS agents"""
        print(f"🎓 Generating course: {topic}")

        payload = {
            "input": topic,
            "type": "text",
            "outputTypes": ["course"],
            "options": {
                "autonomousMode": True,
                "detectedType": {
                    "type": "course",
                    "confidence": 85,
                    "keywords": topic.split()[:3]
                }
            }
        }
        
        async with aiohttp.ClientSession() as session:
            try:
                async with session.post(
                    f"{self.base_url}/api/vybe/process-content",
                    json=payload,
                    headers={"Content-Type": "application/json"}
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        print(f"✅ Course generated: {result.get('title', 'Untitled')}")
                        return result
                    else:
                        error = await response.text()
                        print(f"❌ Course generation failed: {error}")
                        return None
            except Exception as e:
                print(f"❌ Error generating course: {e}")
                return None
    
    async def generate_news_article(self, topic: str):
        """Generate a real news article using MAS agents"""
        print(f"📰 Generating news article: {topic}")

        payload = {
            "input": topic,
            "type": "text",
            "outputTypes": ["news"],
            "options": {
                "autonomousMode": True,
                "detectedType": {
                    "type": "article",
                    "confidence": 90,
                    "keywords": topic.split()[:3]
                }
            }
        }
        
        async with aiohttp.ClientSession() as session:
            try:
                async with session.post(
                    f"{self.base_url}/api/vybe/process-content",
                    json=payload,
                    headers={"Content-Type": "application/json"}
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        print(f"✅ News article generated: {result.get('title', 'Untitled')}")
                        return result
                    else:
                        error = await response.text()
                        print(f"❌ News generation failed: {error}")
                        return None
            except Exception as e:
                print(f"❌ Error generating news: {e}")
                return None
    
    async def generate_vybe_qube(self, concept: str):
        """Generate a real Vybe Qube using MAS agents"""
        print(f"🎯 Generating Vybe Qube: {concept}")

        payload = {
            "input": concept,
            "type": "text",
            "outputTypes": ["article"],  # Use article type for Vybe Qubes
            "options": {
                "autonomousMode": True,
                "detectedType": {
                    "type": "website",
                    "confidence": 80,
                    "keywords": concept.split()[:3]
                }
            }
        }
        
        async with aiohttp.ClientSession() as session:
            try:
                async with session.post(
                    f"{self.base_url}/api/vybe/process-content",
                    json=payload,
                    headers={"Content-Type": "application/json"}
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        print(f"✅ Vybe Qube generated: {result.get('title', 'Untitled')}")
                        return result
                    else:
                        error = await response.text()
                        print(f"❌ Vybe Qube generation failed: {error}")
                        return None
            except Exception as e:
                print(f"❌ Error generating Vybe Qube: {e}")
                return None

async def main():
    """Generate sample content for the platform"""
    print("🚀 Starting VybeCoding.ai Content Generation")
    print("=" * 50)
    
    generator = ContentGenerator()
    
    # Sample content to generate
    courses = [
        ("Building AI-Powered Web Apps with SvelteKit", "developers", "intermediate"),
        ("Introduction to the Vybe Method", "beginners", "beginner"),
        ("Advanced Multi-Agent Systems", "AI engineers", "advanced")
    ]
    
    news_topics = [
        "Latest Developments in Local LLM Technology",
        "FOSS vs Proprietary AI Tools: 2025 Analysis",
        "VybeCoding.ai Platform Updates and New Features"
    ]
    
    vybe_qubes = [
        "AI-Powered E-commerce Platform",
        "Local LLM Development Environment",
        "Educational Content Management System"
    ]
    
    results = {
        "courses": [],
        "news": [],
        "vybe_qubes": [],
        "generated_at": datetime.now().isoformat()
    }
    
    # Generate courses
    print("\n📚 Generating Courses...")
    for topic, audience, complexity in courses:
        course = await generator.generate_course(topic, audience, complexity)
        if course:
            results["courses"].append(course)
        await asyncio.sleep(2)  # Rate limiting
    
    # Generate news articles
    print("\n📰 Generating News Articles...")
    for topic in news_topics:
        article = await generator.generate_news_article(topic)
        if article:
            results["news"].append(article)
        await asyncio.sleep(2)  # Rate limiting
    
    # Generate Vybe Qubes
    print("\n🎯 Generating Vybe Qubes...")
    for concept in vybe_qubes:
        qube = await generator.generate_vybe_qube(concept)
        if qube:
            results["vybe_qubes"].append(qube)
        await asyncio.sleep(2)  # Rate limiting
    
    # Save results
    with open('generated-content-results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print("\n" + "=" * 50)
    print("✅ Content Generation Complete!")
    print(f"📊 Generated: {len(results['courses'])} courses, {len(results['news'])} articles, {len(results['vybe_qubes'])} Vybe Qubes")
    print("📁 Results saved to: generated-content-results.json")

if __name__ == "__main__":
    asyncio.run(main())
