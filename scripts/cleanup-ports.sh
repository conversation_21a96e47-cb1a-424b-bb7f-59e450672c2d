#!/bin/bash

# VybeCoding.ai Port Cleanup Script
# Ensures port 5173 is always available for development

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Target port
TARGET_PORT=5173

echo -e "${BLUE}🧹 VybeCoding.ai Port Cleanup${NC}"
echo -e "${BLUE}==============================${NC}"
echo -e "${CYAN}Target Port: ${TARGET_PORT}${NC}"
echo ""

# Function to kill processes on a specific port
kill_port() {
    local port=$1
    local processes=$(lsof -ti:$port 2>/dev/null || true)
    
    if [ -n "$processes" ]; then
        echo -e "${YELLOW}🔍 Found processes on port $port:${NC}"
        lsof -i:$port 2>/dev/null || true
        echo ""
        
        echo -e "${RED}💀 Killing processes on port $port...${NC}"
        echo "$processes" | xargs kill -9 2>/dev/null || true
        
        # Wait a moment for processes to die
        sleep 1
        
        # Verify port is free
        local remaining=$(lsof -ti:$port 2>/dev/null || true)
        if [ -z "$remaining" ]; then
            echo -e "${GREEN}✅ Port $port is now free${NC}"
        else
            echo -e "${RED}❌ Some processes still running on port $port${NC}"
            lsof -i:$port 2>/dev/null || true
        fi
        echo ""
    else
        echo -e "${GREEN}✅ Port $port is already free${NC}"
    fi
}

# Function to kill all Vite/Node development servers (but not this script)
kill_dev_servers() {
    echo -e "${YELLOW}🔍 Looking for Vite/Node development servers...${NC}"

    # Get current script PID and parent PIDs to avoid killing ourselves
    local current_pid=$$
    local parent_pid=$PPID
    local npm_pids=$(pgrep -f "npm.*run.*dev" 2>/dev/null || true)

    # Kill Vite processes (but not this script or its parents)
    local vite_pids=$(pgrep -f "vite.*dev" 2>/dev/null || true)
    if [ -n "$vite_pids" ]; then
        for pid in $vite_pids; do
            # Skip if it's current script, parent, or npm process
            if [ "$pid" != "$current_pid" ] && [ "$pid" != "$parent_pid" ] && ! echo "$npm_pids" | grep -q "$pid"; then
                echo -e "${RED}💀 Killing Vite dev server (PID: $pid)...${NC}"
                kill -9 "$pid" 2>/dev/null || true
            fi
        done
    fi

    echo -e "${GREEN}✅ Development server cleanup complete${NC}"
    echo ""
}

# Function to show current port usage
show_port_status() {
    echo -e "${CYAN}📊 Current Port Status:${NC}"
    echo -e "${CYAN}======================${NC}"
    
    for port in 5173 5174 5175 5176 5177 5178 5179 5180; do
        local usage=$(lsof -i:$port 2>/dev/null || true)
        if [ -n "$usage" ]; then
            echo -e "${YELLOW}Port $port: ${RED}OCCUPIED${NC}"
            echo "$usage" | head -1
        else
            echo -e "${YELLOW}Port $port: ${GREEN}FREE${NC}"
        fi
    done
    echo ""
}

# Main execution
main() {
    # Show initial status
    show_port_status
    
    # Kill development servers first
    kill_dev_servers
    
    # Specifically target port 5173 and common alternatives
    for port in 5173 5174 5175 5176 5177 5178 5179 5180; do
        kill_port $port
    done
    
    # Final status check
    echo -e "${BLUE}🎯 Final Port Status:${NC}"
    show_port_status
    
    # Verify target port is free
    local target_check=$(lsof -ti:$TARGET_PORT 2>/dev/null || true)
    if [ -z "$target_check" ]; then
        echo -e "${GREEN}🎉 Port $TARGET_PORT is ready for use!${NC}"
        exit 0
    else
        echo -e "${RED}❌ Port $TARGET_PORT is still occupied${NC}"
        lsof -i:$TARGET_PORT 2>/dev/null || true
        exit 1
    fi
}

# Handle command line arguments
case "${1:-}" in
    --show|-s)
        show_port_status
        exit 0
        ;;
    --target|-t)
        if [ -n "$2" ]; then
            TARGET_PORT=$2
            echo -e "${CYAN}🎯 Using custom target port: $TARGET_PORT${NC}"
        fi
        ;;
    --help|-h)
        echo "VybeCoding.ai Port Cleanup Script"
        echo ""
        echo "Usage: $0 [OPTIONS]"
        echo ""
        echo "Options:"
        echo "  --show, -s          Show current port status only"
        echo "  --target, -t PORT   Set custom target port (default: 5173)"
        echo "  --help, -h          Show this help message"
        echo ""
        echo "Examples:"
        echo "  $0                  Clean up ports and ensure 5173 is free"
        echo "  $0 --show          Show current port usage"
        echo "  $0 --target 3000   Clean up and ensure port 3000 is free"
        exit 0
        ;;
esac

# Run main function
main
