#!/bin/bash

echo "🌐 VybeCoding.ai Network Development Server"
echo "═══════════════════════════════════════════"
echo "📍 Local: http://localhost:5173"
echo "🌐 Network: http://[YOUR-HOST-IP]:5173"
echo "💡 Port 5173 is forwarded from container to host"
echo "🔧 Container IP: http://**********:5173 (internal)"
echo ""

echo "🧹 Aggressively ensuring all required ports are available..."

# Observatory ports that need to be free (conservative list)
OBSERVATORY_PORTS=(9090 19999 16686 5601 9200 6379)
DEV_PORTS=(5173)

# Function to safely kill specific processes
kill_port() {
    local port=$1

    if [ "$port" == "5173" ]; then
        # Aggressive killing for our dev port
        local processes=$(lsof -ti:$port 2>/dev/null || true)
        if [ -n "$processes" ]; then
            echo "🔍 Killing processes on dev port $port..."
            echo "$processes" | xargs kill -9 2>/dev/null || true
        else
            echo "✅ Port $port is free"
        fi
    else
        # Conservative killing for Observatory ports
        local processes=$(lsof -ti:$port 2>/dev/null || true)

        if [ -z "$processes" ]; then
            echo "✅ Port $port is free"
            return 0
        fi

        # Check if processes are Observatory-related before killing
        for pid in $processes; do
            local args=$(ps -p $pid -o args= 2>/dev/null || true)

            # Only kill if it's clearly Observatory/Docker related
            if [[ "$args" == *"prometheus"* ]] || [[ "$args" == *"grafana"* ]] || [[ "$args" == *"netdata"* ]] || [[ "$args" == *"jaeger"* ]] || [[ "$args" == *"kibana"* ]] || [[ "$args" == *"elasticsearch"* ]] || [[ "$args" == *"redis"* ]] || [[ "$args" == *"docker"* ]]; then
                echo "🔍 Killing Observatory process on port $port (PID: $pid)"
                kill -9 $pid 2>/dev/null || true
            else
                echo "⚠️  Skipping non-Observatory process on port $port (PID: $pid)"
            fi
        done
    fi

    sleep 1
}

# Function to kill all Observatory ports
kill_observatory_ports() {
    echo "🤖 Freeing Observatory ports..."
    for port in "${OBSERVATORY_PORTS[@]}"; do
        kill_port $port
    done
}

# Function to kill dev server ports
kill_dev_ports() {
    echo "⚡ Freeing development server ports..."
    for port in "${DEV_PORTS[@]}"; do
        kill_port $port
    done
}

# Kill any existing processes first
echo "🛑 Stopping any existing services..."
pkill -f "vite dev" 2>/dev/null || true
pkill -f "node.*vite" 2>/dev/null || true
pkill -f "mas-observatory" 2>/dev/null || true

# Stop any existing Observatory containers
echo "🐳 Stopping existing Observatory containers..."
docker-compose -f mas-observatory/docker-compose.yml down 2>/dev/null || true

sleep 2

# Kill all required ports
kill_dev_ports
kill_observatory_ports

# Wait a bit more to ensure ports are definitely free
sleep 3

echo ""
echo "🚀 Starting MAS Observatory Control..."

# Start MAS Observatory in background
echo "🤖 Starting MAS Observatory Control System..."
python3 mas-observatory-control.py start-observatory &
MAS_PID=$!

# Give Observatory time to start
sleep 3

echo ""
echo "🚀 Starting development server on port 5173..."

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🛑 Shutting down services..."
    if [ ! -z "$MAS_PID" ]; then
        echo "🤖 Stopping MAS Observatory..."
        kill $MAS_PID 2>/dev/null || true
        python3 mas-observatory-control.py stop-observatory 2>/dev/null || true
    fi
    echo "✅ Cleanup complete"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Start the server with explicit port forcing
npx vite dev --host 0.0.0.0 --port 5173 --strictPort &
VITE_PID=$!

echo ""
echo "🎉 VybeCoding.ai Development Environment Ready!"
echo "═══════════════════════════════════════════════"
echo "📍 Main App: http://localhost:5173"
echo "🤖 MAS Control: http://localhost:5173/mas"
echo "📊 Observatory: http://localhost:3001 (admin/vybe_observatory)"
echo "📈 Prometheus: http://localhost:9091"
echo "⚡ Hardware Monitor: http://localhost:19998"
echo "🔍 Jaeger Tracing: http://localhost:16686"
echo "📊 Kibana Logs: http://localhost:5601"
echo "🐳 Portainer: http://localhost:9001"
echo ""
echo "💡 Press Ctrl+C to stop all services"

# Wait for vite process
wait $VITE_PID
