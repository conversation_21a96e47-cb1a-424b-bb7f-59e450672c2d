#!/bin/bash

# Check VybeCoding.ai database status
# Run: chmod +x scripts/check-database-status.sh && ./scripts/check-database-status.sh

echo "🔍 VybeCoding.ai Database Status Check"
echo "====================================="
echo ""

# Configuration
PROJECT_ID="683b200e00153d705da3"
DATABASE_ID="683b231d003c1c558e20"
API_KEY="standard_4df3f240d8fb19f2457fa424db3fdc74caa350a3a32502d2f3d2b07497dda5774c48561544854c472e8ad506cc976e97b4e5f701a3321439bbef2649b4057239b3c633cef8d2ec4f021440a9510f3ad1bc1da3c8f4c44622d1bd6c468ea920a2efc7896e0adb8ae30c3e4bb7e62db7d9d63572caac80a8a9dd396b147357aca6"
ENDPOINT="https://fra.cloud.appwrite.io/v1"

echo "📊 Configuration:"
echo "   Project: $PROJECT_ID"
echo "   Database: $DATABASE_ID"
echo "   Endpoint: $ENDPOINT"
echo ""

echo "📋 Checking Collections..."
echo ""

# Get collections
COLLECTIONS=$(curl -s -X GET \
    "$ENDPOINT/databases/$DATABASE_ID/collections" \
    -H "X-Appwrite-Project: $PROJECT_ID" \
    -H "X-Appwrite-Key: $API_KEY")

echo "Raw API Response:"
echo "$COLLECTIONS" | jq '.'
echo ""

# Parse and display collection info
echo "✅ Collections Found:"
echo "$COLLECTIONS" | jq -r '.collections[] | "   - \(.name) (\(.$id)) - \(.attributes | length) attributes"'
echo ""

# Check for required collections
REQUIRED_COLLECTIONS=("users" "courses" "lessons" "progress" "course_purchases")
FOUND_COLLECTIONS=$(echo "$COLLECTIONS" | jq -r '.collections[].name')

echo "📋 Collection Status:"
for collection in "${REQUIRED_COLLECTIONS[@]}"; do
    if echo "$FOUND_COLLECTIONS" | grep -q "$collection"; then
        echo "   ✅ $collection - Found"
    else
        echo "   ❌ $collection - Missing"
    fi
done
echo ""

# Check storage buckets
echo "🗄️  Checking Storage Buckets..."
BUCKETS=$(curl -s -X GET \
    "$ENDPOINT/storage/buckets" \
    -H "X-Appwrite-Project: $PROJECT_ID" \
    -H "X-Appwrite-Key: $API_KEY")

echo "✅ Storage Buckets:"
echo "$BUCKETS" | jq -r '.buckets[]? | "   - \(.name) (\(.$id))"'
echo ""

# Summary
TOTAL_COLLECTIONS=$(echo "$COLLECTIONS" | jq '.total')
TOTAL_BUCKETS=$(echo "$BUCKETS" | jq '.total // 0')

echo "📊 Summary:"
echo "   Collections: $TOTAL_COLLECTIONS/5 required"
echo "   Storage Buckets: $TOTAL_BUCKETS/2 required"
echo ""

if [ "$TOTAL_COLLECTIONS" -ge 3 ]; then
    echo "🎉 Database is functional! Your platform can work with existing collections."
    echo "💡 You can manually create missing collections in the Appwrite console if needed."
else
    echo "⚠️  Some collections are missing. Please create them manually in the Appwrite console."
fi

echo ""
echo "🚀 Your VybeCoding.ai platform is ready to test!"
echo "   Visit: http://localhost:5175"
