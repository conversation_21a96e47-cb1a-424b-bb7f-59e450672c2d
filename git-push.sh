#!/bin/bash
# VybeCoding.ai GitHub Push Workflow
# Maintains GitHub Copilot context with regular updates

echo "🚀 VybeCoding.ai GitHub Push Workflow"
echo "=================================="

# Check for uncommitted changes
if [[ -n $(git status -s) ]]; then
    echo "📝 Found uncommitted changes:"
    git status -s
    echo ""
    
    # Add all changes
    git add .
    
    # Prompt for commit message or use default
    if [ -z "$1" ]; then
        read -p "💬 Enter commit message (or press Enter for auto-generated): " commit_msg
        if [ -z "$commit_msg" ]; then
            commit_msg="Update: $(date '+%Y-%m-%d %H:%M') - Development progress"
        fi
    else
        commit_msg="$*"
    fi
    
    # Commit changes
    git commit -m "$commit_msg"
    echo "✅ Committed: $commit_msg"
else
    echo "✅ Working directory clean - no changes to commit"
fi

# Push to GitHub
echo "📤 Pushing to GitHub..."
git push origin main

if [ $? -eq 0 ]; then
    echo "🎉 Successfully pushed to GitHub!"
    echo "🤖 GitHub Copilot context updated"
    echo ""
    echo "Repository: https://github.com/Hiram-Ducky/VybeCoding.ai"
else
    echo "❌ Push failed. Please check your connection and try again."
    exit 1
fi
