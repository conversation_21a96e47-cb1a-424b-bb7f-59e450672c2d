import { defineConfig } from 'vitest/config';

export default defineConfig({
  test: {
    include: [
      'src/lib/tests/basic-*.test.ts',
      'src/lib/tests/component-*.test.ts',
      'src/lib/tests/integration-*.test.ts',
      'src/lib/tests/deployment-*.test.ts',
      'src/lib/tests/testing-infrastructure-*.test.ts',
      'src/lib/tests/service-*.test.ts',
      'src/lib/tests/e2e-*.test.ts',
      'src/lib/tests/performance-*.test.ts',
      'src/lib/tests/visual-*.test.ts',
      'src/lib/tests/cicd-*.test.ts',
    ],
    environment: 'jsdom',
    globals: true,
    setupFiles: ['./tests/setup-basic.js'],
  },
  define: {
    __DEV__: true,
  },
});
