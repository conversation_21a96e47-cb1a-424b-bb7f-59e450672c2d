# VybeCoding.ai Backup & Recovery Configuration
# This file contains all configuration options for the backup and recovery system

# =============================================================================
# BASIC CONFIGURATION
# =============================================================================

# Backup directory (where backups are stored locally)
BACKUP_DIR=/backups

# Backup retention period (days)
RETENTION_DAYS=30

# Backup encryption key (leave empty to disable encryption)
# Generate with: openssl rand -base64 32
BACKUP_ENCRYPTION_KEY=

# =============================================================================
# SCHEDULING CONFIGURATION
# =============================================================================

# Backup schedule (cron format)
# Default: Daily at 2:00 AM
BACKUP_SCHEDULE="0 2 * * *"

# Monitoring check schedule (cron format)
# Default: Every 6 hours
MONITOR_SCHEDULE="0 */6 * * *"

# Disaster recovery test schedule (cron format)
# Default: Weekly on Sunday at 3:00 AM
DR_TEST_SCHEDULE="0 3 * * 0"

# =============================================================================
# ALERT THRESHOLDS
# =============================================================================

# Alert if no backup in X hours
ALERT_THRESHOLD_HOURS=25

# Alert if storage usage exceeds X percent
STORAGE_ALERT_THRESHOLD=80

# Recovery Time Objective (seconds) - 4 hours
RTO_TARGET=14400

# Recovery Point Objective (seconds) - 1 hour
RPO_TARGET=3600

# =============================================================================
# CLOUD STORAGE CONFIGURATION
# =============================================================================

# AWS S3 Configuration
AWS_S3_BUCKET=
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1

# Google Cloud Storage Configuration
GCS_BUCKET=
GOOGLE_APPLICATION_CREDENTIALS=

# Azure Blob Storage Configuration
AZURE_STORAGE_ACCOUNT=
AZURE_STORAGE_KEY=
AZURE_CONTAINER=vybecoding-backups

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# Database connection (if not using Docker)
DATABASE_URL=

# Database backup options
DB_BACKUP_COMPRESSION=true
DB_BACKUP_ENCRYPTION=true

# =============================================================================
# NOTIFICATION CONFIGURATION
# =============================================================================

# Slack Webhook URL for notifications
SLACK_WEBHOOK_URL=

# Discord Webhook URL for notifications
DISCORD_WEBHOOK_URL=

# Email notifications
NOTIFICATION_EMAIL=
SMTP_SERVER=
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=
SMTP_USE_TLS=true

# =============================================================================
# ADVANCED CONFIGURATION
# =============================================================================

# Parallel backup jobs
BACKUP_PARALLEL_JOBS=2

# Backup verification
VERIFY_BACKUPS=true

# Cloud upload after backup
UPLOAD_TO_CLOUD=true

# Cleanup old backups automatically
AUTO_CLEANUP=true

# Log level (DEBUG, INFO, WARN, ERROR)
LOG_LEVEL=INFO

# Maximum log file size (MB)
MAX_LOG_SIZE=100

# =============================================================================
# DISASTER RECOVERY CONFIGURATION
# =============================================================================

# Test database name for DR testing
TEST_DATABASE_NAME=vybecoding_dr_test

# DR test data validation
DR_TEST_VALIDATION=true

# DR test cleanup
DR_TEST_CLEANUP=true

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Backup file permissions
BACKUP_FILE_PERMISSIONS=600

# Backup directory permissions
BACKUP_DIR_PERMISSIONS=700

# Enable backup integrity checks
INTEGRITY_CHECKS=true

# Enable backup signing (requires GPG)
BACKUP_SIGNING=false
GPG_KEY_ID=

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================

# Compression level (1-9, 9 is best compression)
COMPRESSION_LEVEL=6

# I/O nice level for backup processes (0-7, 7 is lowest priority)
IONICE_LEVEL=7

# CPU nice level for backup processes (-20 to 19, 19 is lowest priority)
CPU_NICE_LEVEL=10

# Maximum bandwidth for cloud uploads (KB/s, 0 = unlimited)
UPLOAD_BANDWIDTH_LIMIT=0

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================

# Health check endpoint
HEALTH_CHECK_URL=http://localhost:3000/health

# Health check timeout (seconds)
HEALTH_CHECK_TIMEOUT=30

# Backup monitoring enabled
BACKUP_MONITORING=true

# Performance monitoring enabled
PERFORMANCE_MONITORING=true

# =============================================================================
# DEVELOPMENT/TESTING CONFIGURATION
# =============================================================================

# Enable debug mode
DEBUG_MODE=false

# Dry run mode (don't actually perform operations)
DRY_RUN=false

# Test mode (use test configurations)
TEST_MODE=false

# Skip cloud operations in test mode
SKIP_CLOUD_IN_TEST=true

# =============================================================================
# LEGACY SUPPORT
# =============================================================================

# Support for legacy backup formats
LEGACY_SUPPORT=true

# Legacy backup directory
LEGACY_BACKUP_DIR=/legacy-backups

# =============================================================================
# CUSTOM HOOKS
# =============================================================================

# Pre-backup hook script
PRE_BACKUP_HOOK=

# Post-backup hook script
POST_BACKUP_HOOK=

# Pre-recovery hook script
PRE_RECOVERY_HOOK=

# Post-recovery hook script
POST_RECOVERY_HOOK=

# =============================================================================
# EXAMPLE CONFIGURATIONS
# =============================================================================

# Example AWS S3 Configuration:
# AWS_S3_BUCKET=my-vybecoding-backups
# AWS_ACCESS_KEY_ID=AKIAIOSFODNN7EXAMPLE
# AWS_SECRET_ACCESS_KEY=wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY
# AWS_DEFAULT_REGION=us-west-2

# Example Slack Notification:
# SLACK_WEBHOOK_URL=*****************************************************************************

# Example Email Notification:
# NOTIFICATION_EMAIL=<EMAIL>
# SMTP_SERVER=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USERNAME=<EMAIL>
# SMTP_PASSWORD=your-app-password

# Example Encryption Key Generation:
# openssl rand -base64 32

# =============================================================================
# USAGE NOTES
# =============================================================================

# 1. Copy this file to /etc/vybecoding/backup-config.env for system-wide config
# 2. Or copy to ~/.vybecoding/backup-config.env for user-specific config
# 3. Or set environment variables directly in your shell/container
# 4. Restart backup services after configuration changes
# 5. Test configuration with: ./scripts/backup-monitor.sh --help
# 6. Run disaster recovery test with: ./scripts/disaster-recovery-test.sh

# =============================================================================
# SECURITY WARNINGS
# =============================================================================

# WARNING: This file contains sensitive information including:
# - Database credentials
# - Cloud storage keys
# - Encryption keys
# - Webhook URLs
#
# Ensure this file has proper permissions:
# chmod 600 backup-config.env
#
# Never commit this file to version control with real credentials!
# Use environment variables or secure secret management in production.

# =============================================================================
# SUPPORT
# =============================================================================

# For support and documentation:
# - Check docs/backup-recovery.md
# - Run scripts with --help flag
# - Contact: <EMAIL>
