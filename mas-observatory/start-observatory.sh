#!/bin/bash

# 🚀 MAS Observatory Startup Script
# Revolutionary AI Agent Monitoring for VybeCoding.ai

set -e

echo "🚀 Starting MAS Observatory - Revolutionary AI Agent Monitoring"
echo "================================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}❌ Docker is not running. Please start Docker first.${NC}"
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null 2>&1; then
    echo -e "${RED}❌ Docker Compose is not available. Please install Docker Compose.${NC}"
    exit 1
fi

# Set Docker Compose command
if docker compose version &> /dev/null 2>&1; then
    DOCKER_COMPOSE="docker compose"
else
    DOCKER_COMPOSE="docker-compose"
fi

echo -e "${BLUE}🔍 Checking system requirements...${NC}"

# Check available memory
TOTAL_MEM=$(free -m | awk 'NR==2{printf "%.0f", $2}')
if [ "$TOTAL_MEM" -lt 8192 ]; then
    echo -e "${YELLOW}⚠️  Warning: Less than 8GB RAM available. Observatory may run slowly.${NC}"
fi

# Check available disk space
AVAILABLE_SPACE=$(df -BG . | awk 'NR==2 {print $4}' | sed 's/G//')
if [ "$AVAILABLE_SPACE" -lt 10 ]; then
    echo -e "${YELLOW}⚠️  Warning: Less than 10GB disk space available.${NC}"
fi

echo -e "${GREEN}✅ System requirements check completed${NC}"

# Create necessary directories
echo -e "${BLUE}📁 Creating necessary directories...${NC}"
mkdir -p grafana/dashboards grafana/datasources grafana/plugins
mkdir -p prometheus/data
mkdir -p elasticsearch/data
mkdir -p nginx/ssl
mkdir -p mas-exporter/config
mkdir -p logs

echo -e "${GREEN}✅ Directories created${NC}"

# Set proper permissions
echo -e "${BLUE}🔒 Setting permissions...${NC}"
chmod +x start-observatory.sh
chmod -R 755 grafana/
chmod -R 755 prometheus/
chmod -R 755 mas-exporter/

echo -e "${GREEN}✅ Permissions set${NC}"

# Pull latest images
echo -e "${BLUE}📦 Pulling latest Docker images...${NC}"
$DOCKER_COMPOSE pull

# Build custom images
echo -e "${BLUE}🔨 Building custom MAS exporter...${NC}"
$DOCKER_COMPOSE build mas-exporter

# Start the observatory
echo -e "${PURPLE}🚀 Starting MAS Observatory services...${NC}"
$DOCKER_COMPOSE up -d

# Wait for services to be ready
echo -e "${BLUE}⏳ Waiting for services to start...${NC}"
sleep 10

# Check service health
echo -e "${BLUE}🏥 Checking service health...${NC}"

services=("grafana:3000" "prometheus:9090" "netdata:19999" "jaeger:16686" "kibana:5601" "mas-exporter:8001")
all_healthy=true

for service in "${services[@]}"; do
    IFS=':' read -r name port <<< "$service"
    if curl -s -f "http://localhost:$port" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ $name is healthy${NC}"
    else
        echo -e "${RED}❌ $name is not responding${NC}"
        all_healthy=false
    fi
done

if [ "$all_healthy" = true ]; then
    echo -e "${GREEN}🎉 All services are healthy!${NC}"
else
    echo -e "${YELLOW}⚠️  Some services may still be starting. Check logs with: $DOCKER_COMPOSE logs${NC}"
fi

# Display access information
echo ""
echo -e "${CYAN}🌐 MAS Observatory Access Information${NC}"
echo "========================================"
echo -e "${GREEN}📊 Primary Dashboard (Grafana):${NC}     http://localhost:3000"
echo -e "   Username: admin"
echo -e "   Password: vybe_observatory"
echo ""
echo -e "${GREEN}📈 Metrics (Prometheus):${NC}           http://localhost:9090"
echo -e "${GREEN}⚡ Real-time Monitoring (Netdata):${NC}  http://localhost:19999"
echo -e "${GREEN}🔍 Distributed Tracing (Jaeger):${NC}   http://localhost:16686"
echo -e "${GREEN}📊 Log Analytics (Kibana):${NC}         http://localhost:5601"
echo -e "${GREEN}🤖 MAS API:${NC}                        http://localhost:8001"
echo -e "${GREEN}🐳 Container Management (Portainer):${NC} http://localhost:9000"
echo ""
echo -e "${PURPLE}🌐 Unified Access (Nginx):${NC}         http://localhost"
echo ""

# Display real-time status
echo -e "${CYAN}📊 Real-time System Status${NC}"
echo "=========================="

# Show Docker container status
echo -e "${BLUE}🐳 Container Status:${NC}"
$DOCKER_COMPOSE ps

echo ""
echo -e "${GREEN}🚀 MAS Observatory is now running!${NC}"
echo ""
echo -e "${YELLOW}💡 Quick Start Tips:${NC}"
echo "1. Open http://localhost:3000 for the main dashboard"
echo "2. Use admin/vybe_observatory to login to Grafana"
echo "3. The MAS Observatory dashboard shows real-time agent activity"
echo "4. Check http://localhost:19999 for detailed hardware monitoring"
echo "5. View agent conversations in real-time through the dashboard"
echo ""
echo -e "${BLUE}📚 Documentation:${NC} See README.md for detailed usage instructions"
echo -e "${BLUE}🛑 To stop:${NC} Run './stop-observatory.sh' or '$DOCKER_COMPOSE down'"
echo ""
echo -e "${PURPLE}🎯 The future of AI agent monitoring is here!${NC}"
