{"dashboard": {"id": null, "title": "🚀 MAS Observatory - VybeCoding.ai", "tags": ["mas", "ai-agents", "vybecoding"], "style": "dark", "timezone": "browser", "refresh": "1s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "🤖 Agent Status Overview", "type": "stat", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "targets": [{"expr": "mas_agent_status", "legendFormat": "{{agent_id}}", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"displayMode": "list", "orientation": "horizontal"}, "mappings": [{"options": {"0": {"text": "Offline", "color": "red"}}, "type": "value"}, {"options": {"1": {"text": "Active", "color": "green"}}, "type": "value"}], "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}}}}, {"id": 2, "title": "🖥️ Hardware Performance", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "targets": [{"expr": "mas_gpu_utilization_percent", "legendFormat": "GPU Utilization %", "refId": "A"}, {"expr": "mas_cpu_usage_percent", "legendFormat": "CPU Usage %", "refId": "B"}, {"expr": "mas_ram_used_bytes / mas_ram_total_bytes * 100", "legendFormat": "RAM Usage %", "refId": "C"}], "yAxes": [{"label": "Percentage", "max": 100, "min": 0}]}, {"id": 3, "title": "🌡️ Temperature Monitoring", "type": "graph", "gridPos": {"h": 6, "w": 8, "x": 0, "y": 8}, "targets": [{"expr": "mas_gpu_temperature_celsius", "legendFormat": "GPU Temperature °C", "refId": "A"}, {"expr": "mas_cpu_temperature_celsius", "legendFormat": "CPU Temperature °C", "refId": "B"}], "yAxes": [{"label": "Temperature (°C)", "max": 100, "min": 0}], "alert": {"conditions": [{"evaluator": {"params": [80], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "5m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "alerting", "for": "1m", "frequency": "10s", "handler": 1, "name": "High GPU Temperature", "noDataState": "no_data", "notifications": []}}, {"id": 4, "title": "💾 Memory Usage", "type": "graph", "gridPos": {"h": 6, "w": 8, "x": 8, "y": 8}, "targets": [{"expr": "mas_gpu_memory_used_bytes / 1024 / 1024 / 1024", "legendFormat": "GPU Memory (GB)", "refId": "A"}, {"expr": "mas_ram_used_bytes / 1024 / 1024 / 1024", "legendFormat": "System RAM (GB)", "refId": "B"}], "yAxes": [{"label": "Memory (GB)", "min": 0}]}, {"id": 5, "title": "⚡ Agent Performance", "type": "graph", "gridPos": {"h": 6, "w": 8, "x": 16, "y": 8}, "targets": [{"expr": "rate(mas_agent_tasks_total[1m])", "legendFormat": "{{agent_id}} Tasks/min", "refId": "A"}], "yAxes": [{"label": "Tasks per minute", "min": 0}]}, {"id": 6, "title": "💬 Real-time Agent Conversations", "type": "logs", "gridPos": {"h": 10, "w": 24, "x": 0, "y": 14}, "targets": [{"expr": "{job=\"mas-agents\"} |= \"conversation\"", "refId": "A"}], "options": {"showTime": true, "showLabels": false, "showCommonLabels": false, "wrapLogMessage": false, "prettifyLogMessage": false, "enableLogDetails": true, "dedupStrategy": "none", "sortOrder": "Descending"}}, {"id": 7, "title": "📊 Content Generation Pipeline", "type": "table", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}, "targets": [{"expr": "mas_content_generated_total", "legendFormat": "{{type}}", "refId": "A", "format": "table"}], "transformations": [{"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"type": "Content Type", "Value": "Total Generated"}}}]}, {"id": 8, "title": "📈 Content Quality Scores", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}, "targets": [{"expr": "mas_content_quality_score", "legendFormat": "{{type}} Quality", "refId": "A"}], "yAxes": [{"label": "Quality Score", "max": 1, "min": 0}]}, {"id": 9, "title": "📁 Codebase Activity", "type": "graph", "gridPos": {"h": 6, "w": 12, "x": 0, "y": 32}, "targets": [{"expr": "rate(mas_codebase_changes_total[5m])", "legendFormat": "{{file_type}} Changes/min", "refId": "A"}], "yAxes": [{"label": "Changes per minute", "min": 0}]}, {"id": 10, "title": "🌐 System Overview", "type": "stat", "gridPos": {"h": 6, "w": 12, "x": 12, "y": 32}, "targets": [{"expr": "mas_uptime_seconds / 3600", "legendFormat": "Uptime (hours)", "refId": "A"}, {"expr": "mas_active_connections", "legendFormat": "Active Connections", "refId": "B"}, {"expr": "mas_codebase_files_total", "legendFormat": "Total Files", "refId": "C"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"displayMode": "list", "orientation": "horizontal"}}}}], "templating": {"list": [{"name": "agent", "type": "query", "query": "label_values(mas_agent_status, agent_id)", "refresh": 1, "includeAll": true, "multi": true}]}, "annotations": {"list": [{"name": "Agent Events", "datasource": "Prometheus", "enable": true, "expr": "changes(mas_agent_status[1m]) > 0", "iconColor": "rgba(0, 211, 255, 1)", "titleFormat": "Agent Status Change", "textFormat": "{{agent_id}} status changed"}]}}}