# 🚀 MAS Observatory - Grafana Data Sources Configuration

apiVersion: 1

datasources:
  # 📊 Prometheus - Primary metrics source
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
    jsonData:
      timeInterval: "1s"
      queryTimeout: "60s"
      httpMethod: "POST"
    
  # 🔍 Elasticsearch - Logs and search
  - name: Elasticsearch
    type: elasticsearch
    access: proxy
    url: http://elasticsearch:9200
    database: "mas-logs-*"
    jsonData:
      interval: "Daily"
      timeField: "@timestamp"
      esVersion: "8.0.0"
      maxConcurrentShardRequests: 5
      logMessageField: "message"
      logLevelField: "level"
    
  # 🔍 Jaeger - Distributed tracing
  - name: Jaeger
    type: jaeger
    access: proxy
    url: http://jaeger:16686
    jsonData:
      tracesToLogs:
        datasourceUid: "elasticsearch"
        tags: ["job", "instance", "pod", "namespace"]
        mappedTags: [{"key": "service.name", "value": "service"}]
        mapTagNamesEnabled: false
        spanStartTimeShift: "1h"
        spanEndTimeShift: "1h"
        filterByTraceID: false
        filterBySpanID: false
    
  # ⚡ Netdata - Real-time system metrics
  - name: Netdata
    type: prometheus
    access: proxy
    url: http://netdata:19999/api/v1/allmetrics?format=prometheus
    jsonData:
      timeInterval: "1s"
      queryTimeout: "30s"
      httpMethod: "GET"
    
  # 🌐 MAS API - Custom metrics endpoint (using Prometheus format)
  - name: MAS-API
    type: prometheus
    access: proxy
    url: http://mas-exporter:8001/metrics
    jsonData:
      timeInterval: "5s"
      queryTimeout: "30s"
      httpMethod: "GET"
