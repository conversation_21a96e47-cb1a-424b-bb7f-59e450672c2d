# 🚀 MAS OBSERVATORY - Prometheus Configuration
# Metrics collection for VybeCoding.ai MAS agents and infrastructure

global:
  scrape_interval: 5s  # Scrape targets every 5 seconds for real-time monitoring
  evaluation_interval: 5s  # Evaluate rules every 5 seconds

# 📊 SCRAPE CONFIGURATIONS
scrape_configs:
  # 🤖 MAS Agent Metrics
  - job_name: 'mas-agents'
    static_configs:
      - targets: ['mas-exporter:8000']
    scrape_interval: 1s  # Real-time agent monitoring
    metrics_path: /metrics
    
  # ⚡ System Hardware Metrics (Netdata)
  - job_name: 'netdata'
    static_configs:
      - targets: ['netdata:19999']
    metrics_path: /api/v1/allmetrics
    params:
      format: ['prometheus']
    scrape_interval: 2s  # Real-time hardware monitoring
    
  # 📊 Grafana Metrics
  - job_name: 'grafana'
    static_configs:
      - targets: ['grafana:3000']
    metrics_path: /metrics
    
  # 🔍 Elasticsearch Metrics
  - job_name: 'elasticsearch'
    static_configs:
      - targets: ['elasticsearch:9200']
    metrics_path: /_prometheus/metrics
    
  # 🔄 Redis Metrics
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    
  # 🐳 Docker Container Metrics
  - job_name: 'docker'
    static_configs:
      - targets: ['host.docker.internal:9323']
    
  # 🖥️ Node Exporter (Host System)
  - job_name: 'node'
    static_configs:
      - targets: ['host.docker.internal:9100']

# 🚨 ALERTING RULES
rule_files:
  - "alert_rules.yml"

# 📢 ALERTMANAGER CONFIGURATION
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
