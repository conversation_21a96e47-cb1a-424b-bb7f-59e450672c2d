#!/bin/bash

# 🛑 MAS Observatory Stop Script

set -e

echo "🛑 Stopping MAS Observatory..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Set Docker Compose command
if docker compose version &> /dev/null 2>&1; then
    DOCKER_COMPOSE="docker compose"
else
    DOCKER_COMPOSE="docker-compose"
fi

echo -e "${BLUE}🔍 Checking running services...${NC}"
$DOCKER_COMPOSE ps

echo -e "${YELLOW}⏳ Stopping all MAS Observatory services...${NC}"
$DOCKER_COMPOSE down

echo -e "${GREEN}✅ MAS Observatory stopped successfully${NC}"
echo ""
echo -e "${BLUE}💡 To start again:${NC} Run './start-observatory.sh'"
echo -e "${BLUE}🗑️  To remove all data:${NC} Run '$DOCKER_COMPOSE down -v'"
