version: '3.8'

# 🚀 MAS OBSERVATORY - Revolutionary AI Agent Monitoring Stack
# Enterprise-grade FOSS monitoring for VybeCoding.ai MAS
# Think Portainer for AI Agents + NASA Mission Control

services:
  # 📊 GRAFANA - Primary Dashboard Interface
  grafana:
    image: grafana/grafana:latest
    container_name: mas-grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=vybe_observatory
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
    networks:
      - mas-network
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.grafana.rule=Host(`observatory.vybecoding.local`)"

  # 📈 PROMETHEUS - Metrics Collection & Storage
  prometheus:
    image: prom/prometheus:latest
    container_name: mas-prometheus
    ports:
      - "9091:9090"
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    networks:
      - mas-network
    restart: unless-stopped

  # ⚡ NETDATA - Real-time System Monitoring (RTX 5090, CPU, RAM)
  netdata:
    image: netdata/netdata:latest
    container_name: mas-netdata
    ports:
      - "19999:19999"
    cap_add:
      - SYS_PTRACE
    security_opt:
      - apparmor:unconfined
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - /etc/passwd:/host/etc/passwd:ro
      - /etc/group:/host/etc/group:ro
    environment:
      - NETDATA_CLAIM_TOKEN=${NETDATA_CLAIM_TOKEN:-}
      - NETDATA_CLAIM_URL=https://app.netdata.cloud
    networks:
      - mas-network
    restart: unless-stopped

  # 🔍 JAEGER - Distributed Tracing (Agent Conversations)
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: mas-jaeger
    ports:
      - "16686:16686"  # Jaeger UI
      - "14268:14268"  # HTTP collector
      - "6831:6831/udp"  # UDP collector
    environment:
      - COLLECTOR_OTLP_ENABLED=true
      - SPAN_STORAGE_TYPE=memory
    networks:
      - mas-network
    restart: unless-stopped

  # 🔍 ELASTICSEARCH - Log Storage & Search
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: mas-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
    networks:
      - mas-network
    restart: unless-stopped

  # 📊 KIBANA - Log Analytics & Visualization
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: mas-kibana
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - mas-network
    restart: unless-stopped

  # 🧠 MAS COORDINATOR - Agent Communication Hub
  mas-coordinator:
    build:
      context: ../method/vybe
      dockerfile: Dockerfile.coordinator
    container_name: mas-coordinator
    ports:
      - "8765:8765"  # WebSocket server for agent communication
      - "8766:8766"  # REST API for task management
    environment:
      - VYBECODING_ROOT=/workspace
      - OLLAMA_HOST=host.docker.internal
      - OLLAMA_PORT=11434
    volumes:
      - /home/<USER>/Projects/vybecoding:/workspace
      - ./coordinator-data:/app/data
    networks:
      - mas-network
    restart: unless-stopped
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # 🤖 MAS METRICS EXPORTER - Custom Agent Metrics
  mas-exporter:
    build:
      context: ./mas-exporter
      dockerfile: Dockerfile
    container_name: mas-metrics-exporter
    ports:
      - "8002:8000"  # Prometheus metrics endpoint
      - "8003:8001"  # WebSocket event stream
    environment:
      - MAS_COORDINATOR_HOST=mas-coordinator
      - MAS_COORDINATOR_PORT=8765
      - VYBECODING_ROOT=/workspace
    volumes:
      - /home/<USER>/Projects/vybecoding:/workspace:ro
      - ./mas-exporter/config:/app/config
    networks:
      - mas-network
    restart: unless-stopped
    depends_on:
      - mas-coordinator

  # 🔄 REDIS - Real-time Data Cache
  redis:
    image: redis:alpine
    container_name: mas-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - mas-network
    restart: unless-stopped

  # 🌐 NGINX - Reverse Proxy & Load Balancer
  nginx:
    image: nginx:alpine
    container_name: mas-nginx
    ports:
      - "8080:80"
      - "8443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - grafana
      - kibana
      - jaeger
    networks:
      - mas-network
    restart: unless-stopped

  # 📱 PORTAINER - Container Management (Bonus)
  portainer:
    image: portainer/portainer-ce:latest
    container_name: mas-portainer
    ports:
      - "9001:9000"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - portainer-data:/data
    networks:
      - mas-network
    restart: unless-stopped

# 💾 PERSISTENT VOLUMES
volumes:
  grafana-data:
    driver: local
  prometheus-data:
    driver: local
  elasticsearch-data:
    driver: local
  redis-data:
    driver: local
  portainer-data:
    driver: local
  coordinator-data:
    driver: local

# 🌐 NETWORK CONFIGURATION
networks:
  mas-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
