# 🚀 MAS Observatory - Custom Metrics Exporter
# Exports VybeCoding.ai MAS agent metrics to Prometheus

FROM python:3.11-slim

LABEL maintainer="VybeCoding.ai"
LABEL description="MAS Observatory Metrics Exporter"

# 📦 INSTALL SYSTEM DEPENDENCIES
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# 📁 SET WORKING DIRECTORY
WORKDIR /app

# 📋 COPY REQUIREMENTS
COPY requirements.txt .

# 🔧 INSTALL PYTHON DEPENDENCIES
RUN pip install --no-cache-dir -r requirements.txt

# 📂 COPY APPLICATION CODE
COPY . .

# 🔒 CREATE NON-ROOT USER
RUN useradd -m -u 1000 masuser && chown -R masuser:masuser /app
USER masuser

# 🌐 EXPOSE PORTS
EXPOSE 8000 8001

# 🚀 START APPLICATION
CMD ["python", "mas_exporter.py"]
