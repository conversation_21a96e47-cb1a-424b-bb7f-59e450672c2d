#!/usr/bin/env python3
"""
🚀 MAS Observatory - Metrics Exporter
Revolutionary real-time monitoring for VybeCoding.ai MAS agents

Features:
- Real-time agent status and performance metrics
- Hardware monitoring (RTX 5090, CPU, RAM)
- Agent conversation tracking
- Content generation monitoring
- Codebase change detection
- WebSocket event streaming
"""

import asyncio
import json
import logging
import time
import os
import psutil
import GPUtil
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict

# Prometheus metrics
from prometheus_client import start_http_server, Gauge, Counter, Histogram, Info
import websockets
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

# FastAPI for REST endpoints
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
import uvicorn

# Redis for caching
import redis

# OpenTelemetry for tracing
from opentelemetry import trace
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from opentelemetry.exporter.jaeger.thrift import JaegerExporter


@dataclass
class AgentMetrics:
    """Agent performance metrics"""
    agent_id: str
    status: str  # active, idle, error, offline
    tasks_completed: int
    tasks_failed: int
    response_time_ms: float
    memory_usage_mb: float
    cpu_usage_percent: float
    last_activity: datetime
    conversations_count: int
    quality_score: float


@dataclass
class HardwareMetrics:
    """Hardware performance metrics"""
    gpu_utilization: float
    gpu_temperature: float
    gpu_memory_used: float
    gpu_memory_total: float
    cpu_usage: float
    cpu_temperature: float
    ram_used: float
    ram_total: float
    disk_usage: float
    network_io: Dict[str, float]


@dataclass
class ContentMetrics:
    """Content generation metrics"""
    content_id: str
    content_type: str  # course, news, vybe_qube, docs
    generation_time_ms: float
    quality_score: float
    agent_contributions: Dict[str, float]
    status: str  # generating, completed, failed
    created_at: datetime


class MASObservatoryExporter:
    """
    🚀 MAS Observatory Metrics Exporter
    
    Exports comprehensive metrics for VybeCoding.ai MAS:
    - Agent performance and status
    - Hardware utilization (RTX 5090, CPU, RAM)
    - Content generation tracking
    - Real-time event streaming
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.setup_logging()
        
        # Configuration
        self.workspace_root = Path(os.getenv('VYBECODING_ROOT', '/workspace'))
        self.mas_host = os.getenv('MAS_COORDINATOR_HOST', 'localhost')
        self.mas_port = int(os.getenv('MAS_COORDINATOR_PORT', '8765'))
        
        # Redis connection
        self.redis_client = redis.Redis(host='redis', port=6379, decode_responses=True)
        
        # WebSocket clients for real-time streaming
        self.websocket_clients: set = set()
        
        # File system observer
        self.file_observer = Observer()
        
        # Initialize Prometheus metrics
        self.setup_prometheus_metrics()
        
        # Initialize OpenTelemetry tracing
        self.setup_tracing()
        
        # FastAPI app for REST endpoints
        self.app = FastAPI(title="MAS Observatory API", version="1.0.0")
        self.setup_fastapi_routes()

        # Event-driven architecture components
        self._monitoring_event = asyncio.Event()
        self._error_recovery_event = asyncio.Event()

        self.logger.info("🚀 MAS Observatory Exporter initialized")
    
    def setup_logging(self):
        """Configure logging"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    
    def setup_prometheus_metrics(self):
        """Initialize Prometheus metrics"""
        # 🤖 Agent Metrics
        self.agent_status = Gauge('mas_agent_status', 'Agent status (1=active, 0=inactive)', ['agent_id'])
        self.agent_tasks_total = Counter('mas_agent_tasks_total', 'Total tasks completed', ['agent_id'])
        self.agent_tasks_failed = Counter('mas_agent_tasks_failed_total', 'Total tasks failed', ['agent_id'])
        self.agent_response_time = Histogram('mas_agent_response_time_seconds', 'Agent response time', ['agent_id'])
        self.agent_memory_usage = Gauge('mas_agent_memory_usage_bytes', 'Agent memory usage', ['agent_id'])
        self.agent_conversations = Gauge('mas_agent_conversations_total', 'Agent conversations count', ['agent_id'])
        self.agent_quality_score = Gauge('mas_agent_quality_score', 'Agent quality score', ['agent_id'])
        
        # 🖥️ Hardware Metrics
        self.gpu_utilization = Gauge('mas_gpu_utilization_percent', 'GPU utilization percentage')
        self.gpu_temperature = Gauge('mas_gpu_temperature_celsius', 'GPU temperature')
        self.gpu_memory_used = Gauge('mas_gpu_memory_used_bytes', 'GPU memory used')
        self.gpu_memory_total = Gauge('mas_gpu_memory_total_bytes', 'GPU memory total')
        self.cpu_usage = Gauge('mas_cpu_usage_percent', 'CPU usage percentage')
        self.cpu_temperature = Gauge('mas_cpu_temperature_celsius', 'CPU temperature')
        self.ram_used = Gauge('mas_ram_used_bytes', 'RAM used')
        self.ram_total = Gauge('mas_ram_total_bytes', 'RAM total')
        self.disk_usage = Gauge('mas_disk_usage_percent', 'Disk usage percentage')
        
        # 📝 Content Generation Metrics
        self.content_generated_total = Counter('mas_content_generated_total', 'Total content generated', ['type'])
        self.content_generation_time = Histogram('mas_content_generation_time_seconds', 'Content generation time', ['type'])
        self.content_quality_score = Gauge('mas_content_quality_score', 'Content quality score', ['content_id', 'type'])
        
        # 📁 Codebase Metrics
        self.codebase_changes_total = Counter('mas_codebase_changes_total', 'Total codebase changes', ['file_type'])
        self.codebase_files_total = Gauge('mas_codebase_files_total', 'Total files in codebase')
        
        # 🌐 System Metrics
        self.mas_uptime_seconds = Gauge('mas_uptime_seconds', 'MAS uptime in seconds')
        self.mas_active_connections = Gauge('mas_active_connections', 'Active WebSocket connections')
        
        self.logger.info("📊 Prometheus metrics initialized")
    
    def setup_tracing(self):
        """Initialize OpenTelemetry tracing"""
        trace.set_tracer_provider(TracerProvider())
        tracer = trace.get_tracer(__name__)
        
        # Configure Jaeger exporter
        jaeger_exporter = JaegerExporter(
            agent_host_name="jaeger",
            agent_port=6831,
        )
        
        span_processor = BatchSpanProcessor(jaeger_exporter)
        trace.get_tracer_provider().add_span_processor(span_processor)
        
        self.tracer = tracer
        self.logger.info("🔍 OpenTelemetry tracing initialized")
    
    def setup_fastapi_routes(self):
        """Setup FastAPI REST endpoints"""
        
        @self.app.get("/health")
        async def health_check():
            return {"status": "healthy", "timestamp": datetime.now().isoformat()}
        
        @self.app.get("/metrics/agents")
        async def get_agent_metrics():
            """Get current agent metrics"""
            return await self.get_all_agent_metrics()
        
        @self.app.get("/metrics/hardware")
        async def get_hardware_metrics():
            """Get current hardware metrics"""
            return asdict(await self.collect_hardware_metrics())
        
        @self.app.get("/metrics/content")
        async def get_content_metrics():
            """Get content generation metrics"""
            return await self.get_content_generation_metrics()
        
        @self.app.websocket("/ws")
        async def websocket_endpoint(websocket: WebSocket):
            """WebSocket endpoint for real-time updates"""
            await websocket.accept()
            self.websocket_clients.add(websocket)
            try:
                while True:
                    await websocket.receive_text()
            except WebSocketDisconnect:
                self.websocket_clients.remove(websocket)
    
    async def start_monitoring(self):
        """Start all monitoring components"""
        self.logger.info("🚀 Starting MAS Observatory monitoring...")
        
        # Start Prometheus metrics server
        start_http_server(8000)
        self.logger.info("📊 Prometheus metrics server started on port 8000")
        
        # Start file system monitoring
        self.start_file_monitoring()
        
        # Start main monitoring loop
        asyncio.create_task(self.monitoring_loop())
        
        # Start FastAPI server
        config = uvicorn.Config(self.app, host="0.0.0.0", port=8001, log_level="info")
        server = uvicorn.Server(config)
        await server.serve()
    
    async def monitoring_loop(self):
        """Main monitoring loop"""
        start_time = time.time()
        
        while True:
            try:
                # Update uptime
                self.mas_uptime_seconds.set(time.time() - start_time)
                
                # Collect and update metrics
                await self.collect_agent_metrics()
                await self.collect_hardware_metrics()
                await self.collect_content_metrics()
                await self.collect_codebase_metrics()
                
                # Update active connections
                self.mas_active_connections.set(len(self.websocket_clients))
                
                # Broadcast real-time updates
                await self.broadcast_real_time_updates()
                
                # Event-driven waiting for next collection
                try:
                    await asyncio.wait_for(self._monitoring_event.wait(), timeout=1.0)
                    self._monitoring_event.clear()
                except asyncio.TimeoutError:
                    pass  # Continue normal monitoring cycle

            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                # Event-driven error recovery
                try:
                    await asyncio.wait_for(self._error_recovery_event.wait(), timeout=5.0)
                    self._error_recovery_event.clear()
                except asyncio.TimeoutError:
                    pass  # Continue after timeout
    
    async def collect_agent_metrics(self):
        """Collect metrics from MAS agents"""
        try:
            # Get agent status from MAS coordinator
            agents = ['vyba', 'qubert', 'codex', 'pixy', 'ducky', 'happy', 'vybro']
            
            for agent_id in agents:
                # Simulate agent metrics (replace with actual MAS coordinator calls)
                metrics = await self.get_agent_status(agent_id)
                
                if metrics:
                    self.agent_status.labels(agent_id=agent_id).set(1 if metrics.status == 'active' else 0)
                    self.agent_memory_usage.labels(agent_id=agent_id).set(metrics.memory_usage_mb * 1024 * 1024)
                    self.agent_conversations.labels(agent_id=agent_id).set(metrics.conversations_count)
                    self.agent_quality_score.labels(agent_id=agent_id).set(metrics.quality_score)
                
        except Exception as e:
            self.logger.error(f"Error collecting agent metrics: {e}")
    
    async def collect_hardware_metrics(self) -> HardwareMetrics:
        """Collect hardware performance metrics"""
        try:
            # GPU metrics
            gpus = GPUtil.getGPUs()
            gpu = gpus[0] if gpus else None
            
            gpu_util = gpu.load * 100 if gpu else 0
            gpu_temp = gpu.temperature if gpu else 0
            gpu_mem_used = gpu.memoryUsed * 1024 * 1024 if gpu else 0
            gpu_mem_total = gpu.memoryTotal * 1024 * 1024 if gpu else 0
            
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_temp = 0  # Would need platform-specific implementation
            
            # Memory metrics
            memory = psutil.virtual_memory()
            ram_used = memory.used
            ram_total = memory.total
            
            # Disk metrics
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            
            # Network metrics
            net_io = psutil.net_io_counters()
            network_io = {
                'bytes_sent': net_io.bytes_sent,
                'bytes_recv': net_io.bytes_recv
            }
            
            # Update Prometheus metrics
            self.gpu_utilization.set(gpu_util)
            self.gpu_temperature.set(gpu_temp)
            self.gpu_memory_used.set(gpu_mem_used)
            self.gpu_memory_total.set(gpu_mem_total)
            self.cpu_usage.set(cpu_percent)
            self.cpu_temperature.set(cpu_temp)
            self.ram_used.set(ram_used)
            self.ram_total.set(ram_total)
            self.disk_usage.set(disk_percent)
            
            return HardwareMetrics(
                gpu_utilization=gpu_util,
                gpu_temperature=gpu_temp,
                gpu_memory_used=gpu_mem_used,
                gpu_memory_total=gpu_mem_total,
                cpu_usage=cpu_percent,
                cpu_temperature=cpu_temp,
                ram_used=ram_used,
                ram_total=ram_total,
                disk_usage=disk_percent,
                network_io=network_io
            )
            
        except Exception as e:
            self.logger.error(f"Error collecting hardware metrics: {e}")
            return HardwareMetrics(0, 0, 0, 0, 0, 0, 0, 0, 0, {})
    
    async def get_agent_status(self, agent_id: str) -> Optional[AgentMetrics]:
        """Get status for a specific agent"""
        try:
            # Try to get from Redis cache first
            cached = self.redis_client.get(f"agent:{agent_id}")
            if cached:
                data = json.loads(cached)
                return AgentMetrics(**data)
            
            # Get real agent metrics from MAS coordinator
            try:
                # Connect to real MAS coordinator
                from method.vybe.mas_coordinator import MASCoordinator
                coordinator = MASCoordinator()

                # Get actual agent status
                agent_status = coordinator.get_agent_status(agent_id)
                if agent_status:
                    metrics = AgentMetrics(
                        agent_id=agent_id,
                        status=agent_status.get('state', 'unknown'),
                        tasks_completed=agent_status.get('performance_metrics', {}).get('tasks_completed', 0),
                        tasks_failed=agent_status.get('performance_metrics', {}).get('tasks_failed', 0),
                        response_time_ms=agent_status.get('performance_metrics', {}).get('avg_response_time', 0),
                        memory_usage_mb=agent_status.get('performance_metrics', {}).get('memory_usage', 0),
                        cpu_usage_percent=agent_status.get('utilization', 0) * 100,
                        last_activity=datetime.fromisoformat(agent_status['last_heartbeat']) if agent_status.get('last_heartbeat') else datetime.now(),
                        conversations_count=agent_status.get('performance_metrics', {}).get('conversations', 0),
                        quality_score=agent_status.get('performance_metrics', {}).get('quality_score', 0.0)
                    )
                else:
                    # Agent not found in coordinator
                    return None
            except Exception as e:
                self.logger.error(f"Failed to get real agent metrics for {agent_id}: {e}")
                return None
            
            # Cache for 5 seconds
            self.redis_client.setex(f"agent:{agent_id}", 5, json.dumps(asdict(metrics), default=str))
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"Error getting agent status for {agent_id}: {e}")
            return None

    async def collect_content_metrics(self):
        """Collect content generation metrics"""
        try:
            # Scan for generated content
            content_dirs = [
                self.workspace_root / "src" / "lib" / "data" / "courses",
                self.workspace_root / "src" / "lib" / "data" / "news",
                self.workspace_root / "docs" / "generated",
                self.workspace_root / "generated_content"
            ]

            for content_dir in content_dirs:
                if content_dir.exists():
                    for file_path in content_dir.glob("*.json"):
                        try:
                            with open(file_path) as f:
                                content_data = json.load(f)

                            content_type = content_data.get('content_type', 'unknown')
                            quality_score = content_data.get('quality_scores', {})
                            avg_quality = sum(quality_score.values()) / len(quality_score) if quality_score else 0.5

                            # Update metrics
                            self.content_generated_total.labels(type=content_type).inc()
                            self.content_quality_score.labels(
                                content_id=content_data.get('id', 'unknown'),
                                type=content_type
                            ).set(avg_quality)

                        except Exception as e:
                            self.logger.error(f"Error processing content file {file_path}: {e}")

        except Exception as e:
            self.logger.error(f"Error collecting content metrics: {e}")

    async def collect_codebase_metrics(self):
        """Collect codebase metrics"""
        try:
            if self.workspace_root.exists():
                # Count files by type
                file_counts = {}
                total_files = 0

                for file_path in self.workspace_root.rglob("*"):
                    if file_path.is_file() and not any(part.startswith('.') for part in file_path.parts):
                        total_files += 1
                        suffix = file_path.suffix or 'no_extension'
                        file_counts[suffix] = file_counts.get(suffix, 0) + 1

                self.codebase_files_total.set(total_files)

        except Exception as e:
            self.logger.error(f"Error collecting codebase metrics: {e}")

    def start_file_monitoring(self):
        """Start file system monitoring"""
        class CodebaseHandler(FileSystemEventHandler):
            def __init__(self, exporter):
                self.exporter = exporter

            def on_modified(self, event):
                if not event.is_directory:
                    file_path = Path(event.src_path)
                    file_type = file_path.suffix or 'no_extension'
                    self.exporter.codebase_changes_total.labels(file_type=file_type).inc()

                    # Broadcast real-time event
                    asyncio.create_task(self.exporter.broadcast_file_change(event.src_path))

        handler = CodebaseHandler(self)
        self.file_observer.schedule(handler, str(self.workspace_root), recursive=True)
        self.file_observer.start()
        self.logger.info("📁 File system monitoring started")

    async def broadcast_real_time_updates(self):
        """Broadcast real-time updates to WebSocket clients"""
        if not self.websocket_clients:
            return

        try:
            # Collect current metrics
            agent_metrics = await self.get_all_agent_metrics()
            hardware_metrics = asdict(await self.collect_hardware_metrics())

            update_data = {
                'type': 'metrics_update',
                'timestamp': datetime.now().isoformat(),
                'data': {
                    'agents': agent_metrics,
                    'hardware': hardware_metrics,
                    'active_connections': len(self.websocket_clients)
                }
            }

            # Broadcast to all connected clients
            message = json.dumps(update_data)
            disconnected_clients = set()

            for client in self.websocket_clients:
                try:
                    await client.send_text(message)
                except Exception:
                    disconnected_clients.add(client)

            # Remove disconnected clients
            self.websocket_clients -= disconnected_clients

        except Exception as e:
            self.logger.error(f"Error broadcasting updates: {e}")

    async def broadcast_file_change(self, file_path: str):
        """Broadcast file change event"""
        if not self.websocket_clients:
            return

        try:
            event_data = {
                'type': 'file_change',
                'timestamp': datetime.now().isoformat(),
                'data': {
                    'file_path': file_path,
                    'change_type': 'modified'
                }
            }

            message = json.dumps(event_data)
            for client in self.websocket_clients:
                try:
                    await client.send_text(message)
                except Exception:
                    pass

        except Exception as e:
            self.logger.error(f"Error broadcasting file change: {e}")

    async def get_all_agent_metrics(self) -> List[Dict]:
        """Get metrics for all agents"""
        agents = ['vyba', 'qubert', 'codex', 'pixy', 'ducky', 'happy', 'vybro']
        metrics = []

        for agent_id in agents:
            agent_metrics = await self.get_agent_status(agent_id)
            if agent_metrics:
                metrics.append(asdict(agent_metrics))

        return metrics

    async def get_content_generation_metrics(self) -> Dict:
        """Get content generation metrics"""
        try:
            # Get recent content from Redis or filesystem
            content_metrics = {
                'total_generated': 0,
                'by_type': {},
                'recent_items': []
            }

            # Scan content directories
            content_dirs = [
                self.workspace_root / "src" / "lib" / "data" / "courses",
                self.workspace_root / "src" / "lib" / "data" / "news",
                self.workspace_root / "docs" / "generated"
            ]

            for content_dir in content_dirs:
                if content_dir.exists():
                    for file_path in content_dir.glob("*.json"):
                        try:
                            with open(file_path) as f:
                                content_data = json.load(f)

                            content_type = content_data.get('content_type', 'unknown')
                            content_metrics['total_generated'] += 1
                            content_metrics['by_type'][content_type] = content_metrics['by_type'].get(content_type, 0) + 1

                            # Add to recent items (last 10)
                            if len(content_metrics['recent_items']) < 10:
                                content_metrics['recent_items'].append({
                                    'id': content_data.get('id'),
                                    'title': content_data.get('title'),
                                    'type': content_type,
                                    'created_at': content_data.get('created_at')
                                })

                        except Exception:
                            continue

            return content_metrics

        except Exception as e:
            self.logger.error(f"Error getting content metrics: {e}")
            return {'total_generated': 0, 'by_type': {}, 'recent_items': []}


async def main():
    """Main entry point"""
    exporter = MASObservatoryExporter()
    await exporter.start_monitoring()


if __name__ == "__main__":
    asyncio.run(main())
