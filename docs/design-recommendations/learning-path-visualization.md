# Learning Path Visualization Architecture

## Intelligent Learning Journey Interface

```svelte
<!-- Learning Path Visualization Component -->
<section class="learning-paths-section">
  <header class="section-header">
    <h2 class="section-title">Your Learning Journey</h2>
    <div class="view-toggles">
      <button
        class="view-toggle {viewMode === 'path' ? 'active' : ''}"
        on:click={() => setViewMode('path')}
      >
        <GitBranch class="toggle-icon" />
        Path View
      </button>
      <button
        class="view-toggle {viewMode === 'grid' ? 'active' : ''}"
        on:click={() => setViewMode('grid')}
      >
        <Grid class="toggle-icon" />
        Grid View
      </button>
      <button
        class="view-toggle {viewMode === 'timeline' ? 'active' : ''}"
        on:click={() => setViewMode('timeline')}
      >
        <Calendar class="toggle-icon" />
        Timeline View
      </button>
    </div>
  </header>

  {#if viewMode === 'path'}
    <!-- Interactive Learning Path Visualization -->
    <div class="learning-path-canvas" bind:this={pathCanvas}>
      <svg class="path-connections" viewBox="0 0 1200 800">
        {#each pathConnections as connection}
          <path
            d={connection.path}
            class="connection-line {connection.status}"
            stroke-dasharray={connection.isActive ? '0' : '5,5'}
          />
        {/each}
      </svg>

      {#each learningPaths as path, pathIndex}
        <div class="learning-path-track" style="--path-index: {pathIndex}">
          <div class="path-header">
            <div class="path-icon-container">
              <svelte:component this={path.icon} class="path-icon" />
            </div>
            <div class="path-info">
              <h3 class="path-title">{path.title}</h3>
              <div class="path-meta">
                <span class="path-duration">{path.totalDuration}</span>
                <span class="path-difficulty">{path.difficulty}</span>
              </div>
            </div>
            <div class="path-progress">
              <CircularProgress
                value={path.completionPercentage}
                size="48"
                strokeWidth="4"
                color={path.color}
              />
            </div>
          </div>

          <div class="course-nodes">
            {#each path.courses as course, courseIndex}
              <div
                class="course-node {course.status}"
                style="--course-index: {courseIndex}"
                on:click={() => selectCourse(course)}
                role="button"
                tabindex="0"
              >
                <div class="node-content">
                  <div class="course-thumbnail-mini">
                    <img src={course.thumbnail} alt={course.title} />
                    {#if course.status === 'completed'}
                      <div class="completion-badge">
                        <CheckCircle class="completion-icon" />
                      </div>
                    {:else if course.status === 'in-progress'}
                      <div class="progress-badge">
                        <div class="progress-ring" style="--progress: {course.progress}%"></div>
                      </div>
                    {:else if course.status === 'locked'}
                      <div class="lock-badge">
                        <Lock class="lock-icon" />
                      </div>
                    {/if}
                  </div>

                  <div class="course-info-mini">
                    <h4 class="course-title-mini">{course.title}</h4>
                    <div class="course-meta-mini">
                      <span class="duration">{course.duration}</span>
                      <span class="difficulty">{course.difficulty}</span>
                    </div>
                  </div>
                </div>

                <!-- Hover Details Panel -->
                <div class="course-hover-panel">
                  <div class="panel-content">
                    <h5>{course.title}</h5>
                    <p>{course.description}</p>
                    <div class="panel-stats">
                      <div class="stat">
                        <Clock class="stat-icon" />
                        {course.duration}
                      </div>
                      <div class="stat">
                        <Users class="stat-icon" />
                        {course.enrolledStudents.toLocaleString()}
                      </div>
                      <div class="stat">
                        <Star class="stat-icon" />
                        {course.rating}
                      </div>
                    </div>
                    <div class="panel-actions">
                      {#if course.status === 'available'}
                        <Button size="sm" variant="primary">Start Course</Button>
                      {:else if course.status === 'in-progress'}
                        <Button size="sm" variant="success">Continue</Button>
                      {:else if course.status === 'completed'}
                        <Button size="sm" variant="ghost">Review</Button>
                      {:else}
                        <Button size="sm" variant="ghost" disabled>Complete prerequisites</Button>
                      {/if}
                    </div>
                  </div>
                </div>
              </div>

              <!-- Connection Line to Next Course -->
              {#if courseIndex < path.courses.length - 1}
                <div
                  class="course-connector {getConnectorStatus(
                    course,
                    path.courses[courseIndex + 1]
                  )}"
                >
                  <div class="connector-line"></div>
                  <div class="connector-arrow">
                    <ArrowRight class="arrow-icon" />
                  </div>
                </div>
              {/if}
            {/each}
          </div>

          <!-- Path Completion Milestone -->
          <div class="path-milestone {path.isCompleted ? 'achieved' : 'pending'}">
            <div class="milestone-icon">
              <Trophy class="trophy-icon" />
            </div>
            <div class="milestone-info">
              <h4>Path Completion</h4>
              <p>Earn {path.rewardPoints} points and unlock advanced features</p>
            </div>
          </div>
        </div>
      {/each}
    </div>
  {/if}

  {#if viewMode === 'timeline'}
    <!-- Timeline View -->
    <div class="timeline-view">
      <div class="timeline-axis">
        {#each timelineMarkers as marker}
          <div class="timeline-marker" style="left: {marker.position}%">
            <div class="marker-dot"></div>
            <div class="marker-label">{marker.label}</div>
          </div>
        {/each}
      </div>

      <div class="timeline-courses">
        {#each scheduledCourses as course}
          <div
            class="timeline-course"
            style="left: {course.startPosition}%; width: {course.duration}%"
          >
            <div class="timeline-course-content">
              <h4>{course.title}</h4>
              <p>{course.startDate} - {course.endDate}</p>
            </div>
          </div>
        {/each}
      </div>
    </div>
  {/if}
</section>
```

## Advanced Learning Path Styles

```css
.learning-paths-section {
  padding: 40px 0;
  background: linear-gradient(
    135deg,
    rgba(15, 23, 42, 0.95) 0%,
    rgba(30, 41, 59, 0.95) 50%,
    rgba(15, 23, 42, 0.95) 100%
  );
  border-radius: 24px;
  margin: 40px 0;
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 40px 40px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.section-title {
  font-size: 2.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.view-toggles {
  display: flex;
  gap: 8px;
  background: rgba(255, 255, 255, 0.05);
  padding: 4px;
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.view-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border-radius: 8px;
  background: transparent;
  color: rgba(255, 255, 255, 0.7);
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.view-toggle.active {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.learning-path-canvas {
  position: relative;
  padding: 40px;
  min-height: 600px;
  overflow-x: auto;
  overflow-y: visible;
}

.path-connections {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.connection-line {
  fill: none;
  stroke: rgba(59, 130, 246, 0.3);
  stroke-width: 2;
  transition: all 0.3s ease;
}

.connection-line.active {
  stroke: rgba(59, 130, 246, 0.8);
  stroke-width: 3;
}

.connection-line.completed {
  stroke: rgba(16, 185, 129, 0.8);
  stroke-width: 3;
}

.learning-path-track {
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin: 40px 0;
  padding: 24px;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 2;
}

.path-header {
  display: flex;
  align-items: center;
  gap: 20px;
}

.path-icon-container {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
}

.path-icon {
  width: 32px;
  height: 32px;
  color: white;
}

.path-info {
  flex: 1;
}

.path-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  margin: 0 0 8px;
}

.path-meta {
  display: flex;
  gap: 16px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
}

.course-nodes {
  display: flex;
  gap: 16px;
  align-items: center;
  overflow-x: auto;
  padding: 20px 0;
}

.course-node {
  flex-shrink: 0;
  position: relative;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.course-node:hover {
  transform: translateY(-8px) scale(1.05);
  z-index: 10;
}

.course-node:hover .course-hover-panel {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.node-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  width: 200px;
  transition: all 0.3s ease;
}

.course-node.completed .node-content {
  border-color: rgba(16, 185, 129, 0.5);
  background: rgba(16, 185, 129, 0.1);
}

.course-node.in-progress .node-content {
  border-color: rgba(59, 130, 246, 0.5);
  background: rgba(59, 130, 246, 0.1);
}

.course-node.locked .node-content {
  border-color: rgba(107, 114, 128, 0.3);
  background: rgba(107, 114, 128, 0.05);
  opacity: 0.6;
}

.course-thumbnail-mini {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 12px;
  overflow: hidden;
}

.course-thumbnail-mini img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.completion-badge,
.progress-badge,
.lock-badge {
  position: absolute;
  bottom: -8px;
  right: -8px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(15, 23, 42, 1);
}

.completion-badge {
  background: linear-gradient(135deg, #10b981, #059669);
}

.progress-badge {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.lock-badge {
  background: linear-gradient(135deg, #6b7280, #4b5563);
}

.course-info-mini {
  text-align: center;
}

.course-title-mini {
  font-size: 14px;
  font-weight: 600;
  color: white;
  margin: 0 0 4px;
  line-height: 1.3;
}

.course-meta-mini {
  display: flex;
  gap: 8px;
  justify-content: center;
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
}

.course-hover-panel {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(20px);
  width: 300px;
  background: rgba(15, 23, 42, 0.98);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 16px;
  padding: 20px;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(20px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
  z-index: 100;
}

.panel-content h5 {
  color: white;
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px;
}

.panel-content p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  line-height: 1.4;
  margin: 0 0 16px;
}

.panel-stats {
  display: flex;
  gap: 16px;
  margin: 16px 0;
}

.stat {
  display: flex;
  align-items: center;
  gap: 4px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
}

.stat-icon {
  width: 14px;
  height: 14px;
}

.course-connector {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 16px;
}

.connector-line {
  height: 2px;
  width: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 1px;
  transition: all 0.3s ease;
}

.course-connector.completed .connector-line {
  background: linear-gradient(90deg, #10b981, #059669);
}

.course-connector.active .connector-line {
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

.connector-arrow {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow-icon {
  width: 12px;
  height: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.path-milestone {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.03);
  border: 2px dashed rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  margin-top: 20px;
}

.path-milestone.achieved {
  border-color: rgba(245, 158, 11, 0.5);
  background: rgba(245, 158, 11, 0.1);
}

.milestone-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #f59e0b, #d97706);
  display: flex;
  align-items: center;
  justify-content: center;
}

.trophy-icon {
  width: 24px;
  height: 24px;
  color: white;
}

/* Timeline View Styles */
.timeline-view {
  position: relative;
  padding: 40px;
  min-height: 400px;
}

.timeline-axis {
  position: relative;
  height: 2px;
  background: rgba(255, 255, 255, 0.2);
  margin: 40px 0 60px;
}

.timeline-marker {
  position: absolute;
  top: -8px;
  transform: translateX(-50%);
}

.marker-dot {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border: 3px solid rgba(15, 23, 42, 1);
}

.marker-label {
  position: absolute;
  top: 24px;
  left: 50%;
  transform: translateX(-50%);
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

.timeline-courses {
  position: relative;
  height: 200px;
}

.timeline-course {
  position: absolute;
  top: 0;
  height: 60px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.8), rgba(139, 92, 246, 0.8));
  border-radius: 8px;
  padding: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.timeline-course-content h4 {
  color: white;
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 4px;
}

.timeline-course-content p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    gap: 20px;
    align-items: stretch;
  }

  .course-nodes {
    flex-direction: column;
    align-items: stretch;
  }

  .course-node {
    width: 100%;
  }

  .node-content {
    flex-direction: row;
    text-align: left;
    width: auto;
  }

  .course-hover-panel {
    position: static;
    transform: none;
    opacity: 1;
    visibility: visible;
    margin-top: 16px;
    width: auto;
  }
}
```

## Key Features of Learning Path Visualization:

1. **Multiple View Modes**: Path, Grid, and Timeline views for different learning preferences
2. **Progress Tracking**: Visual indicators for completed, in-progress, and locked courses
3. **Interactive Connections**: Shows prerequisite relationships between courses
4. **Hover Details**: Rich information panels without navigation
5. **Milestone Recognition**: Clear completion goals and rewards
6. **Responsive Design**: Works seamlessly on all device sizes
7. **Accessibility**: Keyboard navigation and screen reader support
