# Enhanced Course Discovery Interface

## Recommended Filter Sidebar Component

```svelte
<!-- Advanced Course Discovery Sidebar -->
<aside class="course-discovery-sidebar">
  <!-- Smart Search -->
  <div class="search-container">
    <div class="relative">
      <Search class="search-icon" />
      <input
        type="search"
        placeholder="Search courses, skills, instructors..."
        bind:value={searchQuery}
        class="smart-search-input"
      />
      {#if searchSuggestions.length > 0}
        <div class="search-suggestions">
          {#each searchSuggestions as suggestion}
            <button class="suggestion-item" on:click={() => selectSuggestion(suggestion)}>
              {suggestion.title}
              <span class="suggestion-type">{suggestion.type}</span>
            </button>
          {/each}
        </div>
      {/if}
    </div>
  </div>

  <!-- Learning Path Filters -->
  <div class="filter-section">
    <h3 class="filter-title">Learning Paths</h3>
    <div class="learning-path-filters">
      {#each learningPaths as path}
        <button
          class="path-filter {selectedPath === path.id ? 'active' : ''}"
          on:click={() => filterByPath(path.id)}
        >
          <svelte:component this={path.icon} class="path-icon" />
          <span>{path.name}</span>
          <Badge class="course-count">{path.courseCount}</Badge>
        </button>
      {/each}
    </div>
  </div>

  <!-- Skill Level Matrix -->
  <div class="filter-section">
    <h3 class="filter-title">Skill Progression</h3>
    <div class="skill-matrix">
      {#each skillLevels as level, index}
        <div class="skill-level {selectedSkillLevel === level.id ? 'active' : ''}">
          <div class="level-indicator" style="--level: {index + 1}">
            <svelte:component this={level.icon} />
          </div>
          <div class="level-info">
            <span class="level-name">{level.name}</span>
            <span class="level-duration">{level.avgDuration}</span>
          </div>
          <div class="level-progress">
            <div class="progress-bar" style="width: {level.completionRate}%"></div>
          </div>
        </div>
      {/each}
    </div>
  </div>

  <!-- Dynamic Tags -->
  <div class="filter-section">
    <h3 class="filter-title">Technologies & Skills</h3>
    <div class="tag-cloud">
      {#each popularTags as tag}
        <button
          class="skill-tag {selectedTags.includes(tag.name) ? 'selected' : ''}"
          style="--frequency: {tag.frequency}"
          on:click={() => toggleTag(tag.name)}
        >
          {tag.name}
          <span class="tag-count">{tag.count}</span>
        </button>
      {/each}
    </div>
  </div>

  <!-- Smart Recommendations -->
  <div class="filter-section">
    <h3 class="filter-title">Recommended for You</h3>
    <div class="recommendation-cards">
      {#each personalizedRecommendations as recommendation}
        <div class="mini-course-card" on:click={() => goToCourse(recommendation.id)}>
          <div class="card-header">
            <img src={recommendation.thumbnail} alt={recommendation.title} />
            <div class="match-score">
              <Star class="match-icon" />
              {recommendation.matchScore}%
            </div>
          </div>
          <div class="card-content">
            <h4>{recommendation.title}</h4>
            <p class="recommendation-reason">{recommendation.reason}</p>
          </div>
        </div>
      {/each}
    </div>
  </div>
</aside>
```

## Enhanced Styles

```css
.course-discovery-sidebar {
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(59, 130, 246, 0.2);
  min-height: 100vh;
  padding: 2rem;
  width: 400px;
  position: sticky;
  top: 0;
  overflow-y: auto;
}

.smart-search-input {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px 20px 16px 50px;
  width: 100%;
  color: white;
  font-size: 16px;
  transition: all 0.3s ease;
}

.smart-search-input:focus {
  border-color: rgba(59, 130, 246, 0.5);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
  outline: none;
}

.skill-matrix {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.skill-level {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.05);
  cursor: pointer;
  transition: all 0.3s ease;
}

.skill-level:hover {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
}

.level-indicator {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background: linear-gradient(
    135deg,
    hsl(calc(var(--level) * 60), 70%, 60%),
    hsl(calc(var(--level) * 60), 70%, 40%)
  );
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.tag-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.skill-tag {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 8px 16px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: calc(0.5 + var(--frequency, 0) * 0.5);
}

.skill-tag:hover {
  background: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.5);
  color: white;
}

.skill-tag.selected {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.8), rgba(139, 92, 246, 0.8));
  border-color: rgba(59, 130, 246, 0.8);
  color: white;
}

.recommendation-cards {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.mini-course-card {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.mini-course-card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(59, 130, 246, 0.3);
  transform: translateY(-2px);
}
```
