# Enhanced Course Card Architecture

## Progressive Disclosure Course Card Design

```svelte
<!-- Enhanced Course Card with Progressive Disclosure -->
<article class="enhanced-course-card" data-course-id={course.id}>
  <!-- Card States: collapsed, expanded, detailed -->
  <div class="card-container {cardState}" on:click={() => handleCardInteraction()}>
    <!-- Primary Information Layer (Always Visible) -->
    <header class="card-header">
      <div class="course-thumbnail-container">
        <img src={course.thumbnailUrl} alt={course.title} class="course-thumbnail" loading="lazy" />
        <div class="overlay-badges">
          {#if course.featured}
            <Badge class="featured-badge" variant="gradient">
              <Star class="badge-icon" />
              Featured
            </Badge>
          {/if}
          <Badge class="price-badge" variant={course.price === 0 ? 'success' : 'primary'}>
            {formatPrice(course.price)}
          </Badge>
        </div>
        <div class="quick-actions">
          <button class="bookmark-btn" on:click|stopPropagation={() => toggleBookmark()}>
            <Bookmark class="bookmark-icon {isBookmarked ? 'active' : ''}" />
          </button>
          <button class="preview-btn" on:click|stopPropagation={() => openPreview()}>
            <Play class="preview-icon" />
          </button>
        </div>
      </div>

      <div class="primary-info">
        <div class="course-meta-line">
          <CategoryBadge category={course.category} />
          <DifficultyIndicator level={course.difficulty} />
        </div>
        <h3 class="course-title">{course.title}</h3>
        <div class="instructor-line">
          <img
            src={course.instructor.avatar}
            alt={course.instructor.name}
            class="instructor-avatar"
          />
          <span class="instructor-name">{course.instructor.name}</span>
          <InstructorRating rating={course.instructor.rating} />
        </div>
      </div>
    </header>

    <!-- Secondary Information Layer (Hover/Focus Expansion) -->
    <div class="card-expansion {cardState === 'expanded' ? 'visible' : 'hidden'}">
      <div class="course-description">
        <p>{course.description}</p>
      </div>

      <div class="quick-stats-grid">
        <StatItem icon={Clock} label="Duration" value={formatDuration(course.estimatedDuration)} />
        <StatItem icon={BookOpen} label="Lessons" value={course.lessonCount} />
        <StatItem icon={Users} label="Students" value={course.studentCount.toLocaleString()} />
        <StatItem icon={Star} label="Rating" value={course.rating} showStars={true} />
      </div>

      <div class="learning-outcomes">
        <h4 class="outcomes-title">You'll Learn:</h4>
        <ul class="outcomes-list">
          {#each course.learningOutcomes?.slice(0, 3) as outcome}
            <li class="outcome-item">
              <CheckCircle class="outcome-icon" />
              {outcome}
            </li>
          {/each}
        </ul>
      </div>

      <div class="skill-tags">
        {#each course.tags.slice(0, 4) as tag}
          <span class="skill-tag">{tag}</span>
        {/each}
        {#if course.tags.length > 4}
          <span class="more-tags">+{course.tags.length - 4} more</span>
        {/if}
      </div>
    </div>

    <!-- Tertiary Information Layer (Detailed View) -->
    <div class="card-details {cardState === 'detailed' ? 'visible' : 'hidden'}">
      <div class="detailed-content">
        <CourseProgressPreview {course} />
        <InstructorProfile instructor={course.instructor} />
        <StudentReviews reviews={course.recentReviews} />
        <PrerequisitesList prerequisites={course.prerequisites} />
      </div>
    </div>

    <!-- Action Footer -->
    <footer class="card-footer">
      <div class="action-group">
        {#if course.isEnrolled}
          <Button class="primary-action continue-btn" on:click={() => continueCourse()}>
            <Play class="action-icon" />
            Continue Learning
            <div class="progress-indicator" style="width: {course.progress}%"></div>
          </Button>
        {:else}
          <Button class="primary-action enroll-btn" on:click={() => enrollInCourse()}>
            <BookOpen class="action-icon" />
            {course.price === 0 ? 'Start Free' : 'Enroll Now'}
          </Button>
        {/if}

        <div class="secondary-actions">
          <Button variant="ghost" size="sm" on:click={() => viewDetails()}>View Details</Button>
          <Button variant="ghost" size="sm" on:click={() => shareCourse()}>
            <Share class="share-icon" />
          </Button>
        </div>
      </div>
    </footer>
  </div>
</article>
```

## Enhanced Card Interactions & Animations

```css
.enhanced-course-card {
  position: relative;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-container {
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 24px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform-style: preserve-3d;
}

.card-container:hover {
  transform: translateY(-8px) rotateX(2deg);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.3);
}

.card-container.expanded {
  transform: translateY(-12px) scale(1.02);
  z-index: 10;
}

.card-container.detailed {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(1.1);
  width: 90vw;
  max-width: 800px;
  height: 90vh;
  z-index: 1000;
  overflow-y: auto;
}

.course-thumbnail-container {
  position: relative;
  aspect-ratio: 16/9;
  overflow: hidden;
}

.course-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.card-container:hover .course-thumbnail {
  transform: scale(1.05);
}

.overlay-badges {
  position: absolute;
  top: 12px;
  left: 12px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.quick-actions {
  position: absolute;
  top: 12px;
  right: 12px;
  display: flex;
  gap: 8px;
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.3s ease;
}

.card-container:hover .quick-actions {
  opacity: 1;
  transform: translateY(0);
}

.card-expansion {
  padding: 20px;
  max-height: 0;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-expansion.visible {
  max-height: 500px;
  opacity: 1;
}

.quick-stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin: 16px 0;
}

.learning-outcomes {
  margin: 16px 0;
}

.outcomes-list {
  list-style: none;
  padding: 0;
  margin: 8px 0 0;
}

.outcome-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 4px 0;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

.outcome-icon {
  color: #10b981;
  width: 16px;
  height: 16px;
}

.skill-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin: 16px 0;
}

.skill-tag {
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  color: rgba(59, 130, 246, 0.9);
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.card-footer {
  padding: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  background: rgba(0, 0, 0, 0.2);
}

.primary-action {
  width: 100%;
  position: relative;
  overflow: hidden;
}

.continue-btn {
  background: linear-gradient(135deg, #10b981, #059669);
}

.enroll-btn {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.progress-indicator {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background: rgba(255, 255, 255, 0.3);
  transition: width 0.3s ease;
}

/* Responsive Design */
@media (max-width: 768px) {
  .card-container.detailed {
    width: 95vw;
    height: 95vh;
    transform: translate(-50%, -50%);
  }

  .quick-stats-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .card-expansion {
    padding: 16px;
  }
}

/* Accessibility Enhancements */
@media (prefers-reduced-motion: reduce) {
  .enhanced-course-card,
  .card-container,
  .course-thumbnail,
  .card-expansion {
    transition: none;
  }

  .card-container:hover {
    transform: none;
  }
}

.card-container:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}
```

## Interactive Component Features

1. **Progressive Disclosure**: Three levels of information (collapsed, expanded, detailed)
2. **Smart Hover States**: Contextual information appears on hover
3. **Quick Actions**: Bookmark, preview, and share without navigating away
4. **Accessibility First**: Keyboard navigation, reduced motion support, proper focus management
5. **Mobile Optimized**: Touch-friendly interactions and responsive design
6. **Performance Optimized**: Lazy loading, efficient animations, minimal repaints
