# AUGMENT CODE RULES 2025 - VYBECODING.AI EDITION

**Updated:** June 2, 2025  
**Version:** 2025.1  
**Project:** VybeCoding.ai AI Education Platform  
**Context:** Community-first autonomous MAS development

## 🎯 CORE PRINCIPLES

1. **PLANNING PREVENTS FAILURES:** Thorough upfront planning eliminates 80% of development problems
2. **AGENT SPECIALIZATION:** Each AI agent has specific role and expertise
3. **DOCUMENT-DRIVEN DEVELOPMENT:** Artifacts pass context between agents and phases
4. **ITERATIVE REFINEMENT:** Build, test, learn, adapt
5. **QUALITY GATES:** Validation checkpoints ensure alignment and completeness
6. **COMMUNITY-FIRST:** "Rising tide lifts all ships" - collaboration over competition
7. **PROOF THROUGH EXECUTION:** Platform demonstrates AI-powered development effectiveness
8. **METHOD-AGNOSTIC:** Support BMAD Method, Vybe Method, and community methodologies
9. **<PERSON>TONOMOUS INTELLIGENCE:** MAS systems operate with minimal human intervention
10. **ED<PERSON><PERSON>IONAL TRANSPARENCY:** All code and processes must be teachable

## 🏗️ TECHNOLOGY STACK & ARCHITECTURE

### **MAS Framework (100% FOSS)**

- **CrewAI:** Role-based agent collaboration (Apache 2.0)
- **AutoGen:** Enterprise-grade agent coordination (MIT)
- **LangGraph:** State management for workflows (MIT)
- **Model Context Protocol (MCP):** Tool integration standard
- **Guardrails AI:** Content validation and safety (Apache 2.0)
- **ChromaDB:** Vector database for semantic search (Apache 2.0)

### **Platform Architecture**

- **Frontend:** SvelteKit + Tailwind CSS v4 + TypeScript (performance + developer experience)
- **Backend:** Appwrite.io Cloud (99.99% SLA + comprehensive services)
- **Database:** Appwrite Database (managed PostgreSQL with real-time)
- **Authentication:** Appwrite Auth (multi-provider + enterprise SSO)
- **CDN:** Global content delivery with edge computing
- **Monitoring:** Real-time observability and performance tracking

### **LLM Strategy (100% Local FOSS)**

- **Primary Coding:** Qwen3-30B-A3B (Apache 2.0) - Advanced reasoning and code generation
- **Code Specialization:** Devstral-Small-2505 (specialized coding tasks)
- **General Reasoning:** Gemma 2 27B (Google open weights) - Complex problem solving
- **Fast Responses:** DeepSeek-Coder 7B (MIT license) - Quick iterations
- **Content Generation:** Llama 3.1 70B (Meta open weights) - Educational content
- **Quality Validation:** Local fine-tuned models for fact-checking and plagiarism detection

### **Web Research & Integration**

- **News APIs:** Real-time information gathering from multiple sources
- **Academic Databases:** Scholarly article and research paper access
- **Documentation Scrapers:** Official framework and library documentation
- **Social Media Monitoring:** Developer community trends and discussions
- **Fact Verification:** Multi-source cross-referencing for accuracy

## 🛠️ IMPLEMENTATION STANDARDS FOR ALL AGENTS

### **Core Standards**

- **NO PLACEHOLDERS:** Every function must work, every API must respond, every component must render
- **REAL CONNECTIONS:** Use actual database connections, API calls, and service integrations
- **VERIFIED COMPONENTS FIRST:** Prioritize tested, certified, and battle-proven components over custom builds
- **ERROR HANDLING:** Implement proper error handling with meaningful messages
- **TESTING:** Include tests that verify actual functionality
- **DOCUMENTATION:** Update documentation with working examples
- **VERIFICATION:** Test implementation before marking as complete
- **FOSS-FIRST:** Prefer open source solutions with proper licensing
- **EDUCATIONAL TRANSPARENCY:** Code should be readable and teachable

### **VybeCoding.ai Specific Standards**

- **AUTONOMOUS CAPABILITY:** All features must support MAS automation
- **COMMUNITY INTEGRATION:** Consider repository showcase and knowledge sharing
- **METHOD SUPPORT:** Ensure compatibility with BMAD and Vybe Methods
- **REVENUE VALIDATION:** Features should support Vybe Qube generation
- **SCALABILITY:** Design for 100s of outputs per month
- **QUALITY ASSURANCE:** 99.9% accuracy, 100% plagiarism-free content
- **REAL-TIME COLLABORATION:** Support multi-agent coordination
- **WEB RESEARCH:** Integrate real-time information gathering

### **File Management & Cleanup Guidelines**

**✅ PRESERVE (Never Delete):**

- **Validation Scripts:** `verify-project.py`, `validate_vybe.py`, etc.
- **Configuration Helpers:** Setup scripts, environment configs
- **Project Utilities:** Build scripts, deployment helpers
- **Test Files:** Unit tests, integration tests, test data
- **Documentation:** All `.md` files, guides, specifications
- **Agent Definitions:** All agent `.md` files and knowledge base
- **Story Files:** BMAD Method story and epic files
- **MAS Components:** Multi-agent system configurations and workflows
- **Community Content:** Repository showcases, news, blog content

**🗑️ CLEAN UP (Safe to Remove):**

- **Debug Outputs:** Console logs, temporary debug files
- **Build Artifacts:** Compiled files, cache directories (`__pycache__`, `node_modules/.cache`)
- **Temporary Downloads:** One-time API responses, temp data files
- **IDE Temp Files:** `.tmp`, `.swp`, editor backup files
- **Test Artifacts:** Coverage reports, temporary test databases

## ✅ SUCCESS CRITERIA

### **Functional Requirements**

✅ All systems must be fully functional, not placeholder  
✅ Real integrations with actual services  
✅ Comprehensive testing with passing test suites  
✅ Production-ready configurations  
✅ Complete documentation with working examples  
✅ Monitoring and alerting for all components  
✅ Document-driven development with proper handoffs between agents  
✅ Quality validation at each phase transition

### **VybeCoding.ai Specific Requirements**

✅ Autonomous MAS capable of minimal-input → complete-output generation  
✅ Community features supporting repository showcase and knowledge sharing  
✅ Method-agnostic support for BMAD, Vybe, and community methodologies  
✅ Real-time web research and fact verification capabilities  
✅ Plagiarism-free, hallucination-free content generation  
✅ Automated deployment and monitoring for generated websites  
✅ Multi-agent consensus and collaboration systems  
✅ Educational content aligned with learning objectives

## 🔄 WORKFLOW GUIDANCE

### **Updated BMAD Sequence (Official Names) - CURRENT PROJECT STATUS**

1. **✅ Analyst (Wendy):** Research, brainstorm, and create project briefs - **COMPLETE**
2. **✅ Product Manager (Bill):** Create comprehensive PRD and requirements - **COMPLETE**
3. **✅ Architect (Timmy):** Design technical architecture and system design - **COMPLETE**
4. **✅ Design Architect (Karen):** Create UI/UX specifications and frontend architecture - **COMPLETE**
5. **🎯 Product Owner (Jimmy):** Validate all documents align and quality assurance - **NEXT**
6. **⏳ Scrum Master (Fran):** Generate user stories and sprint planning - **PENDING**
7. **⏳ Developer (Rodney/James):** Implement stories with comprehensive testing - **PENDING**

### **Vybe Method Integration**

- **Autonomous Execution:** MAS agents can execute entire workflows independently
- **Real-Time Collaboration:** Multiple agents work simultaneously with consensus
- **Quality Validation:** 4-layer validation system with human escalation
- **Community Integration:** All outputs contribute to community knowledge base

### **Best Practices**

- **Follow BMAD sequence:** Maintain proven agent workflow
- **Create artifacts:** Each agent produces specific deliverables
- **Validate handoffs:** Ensure context transfers properly between agents
- **One story at a time:** Focus prevents scope creep and ensures quality
- **Update documentation:** Keep artifacts current as you build
- **Use quality checklists:** Validate at each phase transition
- **Community contribution:** Share knowledge and celebrate others' work
- **Method flexibility:** Adapt to different development methodologies

## 📚 KNOWLEDGE BASE ACCESS

### **BMAD Method Knowledge Base**

- **Agent Personas:** 8 specialist agent personalities and capabilities (Wendy, Bill, Timmy, Karen, Jimmy, Fran, Rodney, James)
- **Task Definitions:** Specific instructions for each development phase (13 task files)
- **Templates:** Document templates for consistent deliverables (7 template files)
- **Checklists:** Quality validation and alignment verification (7 checklist files)
- **Workflow Guidance:** Step-by-step BMAD Method implementation

### **Vybe Method Knowledge Base**

- **MAS Coordination:** Multi-agent system orchestration and consensus
- **Autonomous Workflows:** Self-executing development processes
- **Quality Assurance:** Plagiarism detection, fact verification, code validation
- **Community Integration:** Repository showcase, knowledge sharing, collaboration
- **Revenue Generation:** Vybe Qube creation and monetization strategies

### **Community Knowledge Base**

- **Repository Showcase:** Curated collection of community projects
- **Method Documentation:** Support for various development methodologies
- **Best Practices:** Proven patterns and techniques from community
- **Educational Content:** Courses, articles, tutorials from community experts

## 🎮 QUICK START

### **BMAD Method Activation**

```bash
# Initialize BMAD system
cd /home/<USER>/Projects/vybecoding
python3 method/bmad/bmad_commands.py start

# Check system status
python3 method/bmad/bmad_commands.py status

# Activate specific agents (CURRENT SEQUENCE)
python3 method/bmad/bmad_orchestrator.py "*analyst"     # ✅ Wendy - COMPLETE
python3 method/bmad/bmad_orchestrator.py "*pm"          # ✅ Bill - COMPLETE
python3 method/bmad/bmad_orchestrator.py "*architect"   # ✅ Timmy - COMPLETE
python3 method/bmad/bmad_orchestrator.py "*designer"    # ✅ Karen - COMPLETE
python3 method/bmad/bmad_orchestrator.py "*po"          # 🎯 Jimmy - NEXT
python3 method/bmad/bmad_orchestrator.py "*sm"          # ⏳ Fran - PENDING
python3 method/bmad/bmad_orchestrator.py "*dev"         # ⏳ Rodney/James - PENDING
```

### **Vybe Method Activation**

```bash
# Initialize Vybe MAS system
python3 method/vybe/vybe_commands.py start

# Check MAS status
python3 method/vybe/vybe_commands.py status

# Begin autonomous generation
python3 method/vybe/vybe_commands.py generate --input "URL + prompt"
```

## 📝 CODING STANDARDS & PATTERNS

### **File Naming Conventions**

- **Documentation:** `kebab-case.md` (BMAD Method standard)
- **Components:** `PascalCase.svelte` (SvelteKit convention)
- **Utils/Lib:** `camelCase.js/.ts` (JavaScript standard)
- **Stories:** `MAS-XXX-descriptive-name.md` (Updated story format)
- **Epics:** `epic-N-descriptive-name/` (BMAD epic structure)
- **Agent Files:** `agent-name.md` (Agent persona definitions)

### **SvelteKit Specific**

- **Routes:** Use SvelteKit's file-based routing in `src/routes/`
- **Components:** Reusable components in `src/lib/components/`
- **Stores:** Global state management in `src/lib/stores/`
- **Utils:** Helper functions in `src/lib/utils/`
- **Types:** TypeScript definitions in `src/lib/types/`
- **MAS Integration:** Agent communication components in `src/lib/mas/`

### **Security & Reliability Guidelines**

- **Input Validation:** All user inputs must pass validation
- **Educational Content:** Must be age-appropriate and curriculum-aligned
- **Code Generation:** Must be security-scanned before execution
- **Multi-Agent Consensus:** 4-layer validation with 3/4 agent agreement
- **Human Escalation:** For complex or uncertain scenarios
- **Plagiarism Prevention:** All content must be original and verified
- **Fact Verification:** Multi-source cross-referencing required
- **Community Safety:** Content moderation and quality standards

## 📋 EXECUTION GUIDELINES

### **Core Guidelines**

- Do as many tasks as possible without stopping to maximize message usage limits
- Update project documentation to reflect ACTUAL implementation status
- Supplement knowledge with 2025 web searches when needed
- Follow document-driven development principles
- Maintain quality gates between agent transitions

### **VybeCoding.ai Specific Guidelines**

- **Architecture-first:** Check docs/architecture.md for technical decisions
- **BMAD Method:** Follow story-driven development from method/bmad/artifacts/
- **Community focus:** All features must support knowledge sharing and collaboration
- **Educational focus:** All features must serve learning objectives
- **Enterprise reliability:** Security and reliability are non-negotiable
- **Multi-agent context:** Consider MAS coordination in all AI features
- **Revenue validation:** Features should support Vybe Qube generation
- **Method flexibility:** Support multiple development methodologies
- **Quality assurance:** Implement plagiarism detection and fact verification
- **Real-time research:** Integrate web research capabilities

## 🎯 QUICK REFERENCE FOR DEVELOPMENT

### **When working on VybeCoding.ai, remember:**

- **Community-first:** "Rising tide lifts all ships" philosophy
- **Method-agnostic:** Support BMAD, Vybe, and community methods
- **Autonomous capability:** Design for MAS automation
- **Quality standards:** 99.9% accuracy, 100% plagiarism-free
- **Educational transparency:** Code must be teachable
- **FOSS preference:** Use open source solutions when possible
- **Real-time research:** Integrate web information gathering
- **Multi-agent coordination:** Consider agent collaboration
- **Revenue generation:** Support Vybe Qube creation
- **Community integration:** Repository showcase and knowledge sharing

### **Current Project Status (June 2, 2025):**

- **✅ BMAD Method:** 100% compliant v3.1 with 8 functional agents
- **✅ Platform:** Production-ready SvelteKit + Appwrite.io
- **✅ MAS Framework:** CrewAI + AutoGen + LangGraph coordination (100% FOSS)
- **✅ Autonomous Architecture:** Complete technical design by Timmy
- **✅ UI/UX Design:** Complete autonomous interface by Karen
- **🎯 Current Phase:** Jimmy validation → Fran stories → Rodney/James implementation
- **🚀 Goal:** Autonomous MAS with URL + prompt → complete output capability
