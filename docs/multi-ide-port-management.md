# 🔧 Multi-IDE Port Management for VybeCoding.ai

This guide explains how VybeCoding.ai guarantees port 5173 usage across multiple IDEs simultaneously.

## 🎯 Problem Solved

When running VybeCoding.ai in multiple IDEs simultaneously (VS Code, VS Code Insiders, Cursor, Windsurf), each IDE would try to start a dev server, causing port conflicts and using different ports (5173, 5174, 5175, etc.).

**Our solution ensures ALL IDEs always use port 5173.**

## 🚀 How It Works

### **Automatic Port Cleanup**

Every dev command automatically clears port 5173 before starting:

```bash
# This runs before every dev server start
lsof -ti:5173 | xargs kill -9 2>/dev/null || true
```

### **Forced Port Assignment**

All Vite commands explicitly use port 5173:

```bash
vite dev --host --port 5173
```

### **Network Access Enabled**

The `--host` flag makes the server accessible from other devices on your network.

## 📋 Available Commands

### **Primary Development Commands:**

```bash
# Full development setup (recommended)
npm run dev                    # ✅ Port 5173 guaranteed + Portainer + network access

# Simple dev server only
npm run dev:simple            # ✅ Port 5173 guaranteed + network access

# Preview production build
npm run preview               # ✅ Port 5173 guaranteed + network access
```

### **Alternative Development Commands:**

```bash
# Development with manual Portainer
npm run dev:with-portainer    # ✅ Port 5173 guaranteed

# Full development (alternative)
npm run dev:full              # ✅ Port 5173 guaranteed
```

### **Port Management Commands:**

```bash
# Check current port status
npm run ports:status

# Manual port cleanup (comprehensive)
npm run ports:cleanup

# Quick port 5173 cleanup
lsof -ti:5173 | xargs kill -9 2>/dev/null || true
```

## 🖥️ Multi-IDE Workflow

### **Scenario: 4 IDEs Running Simultaneously**

1. **VS Code:** `npm run dev` → Port 5173 ✅
2. **VS Code Insiders:** `npm run dev` → Kills previous, uses Port 5173 ✅
3. **Cursor:** `npm run dev` → Kills previous, uses Port 5173 ✅
4. **Windsurf:** `npm run dev` → Kills previous, uses Port 5173 ✅

**Result:** Only the most recently started IDE serves on port 5173.

### **Expected Behavior:**

- ✅ **Consistent URL:** Always `http://localhost:5173/`
- ✅ **Network Access:** Always `http://[YOUR_IP]:5173/`
- ✅ **No Port Conflicts:** Previous servers automatically killed
- ✅ **Instant Switching:** Switch between IDEs seamlessly

## 🌐 Network Access URLs

When you run any dev command, you'll see:

```bash
➜  Local:   http://localhost:5173/        # Always port 5173
➜  Network: http://************:5173/     # Your network IP + port 5173
➜  Network: http://**********:5173/       # Docker network + port 5173
```

### **Access from Other Devices:**

- **Phone/Tablet:** `http://************:5173/`
- **Another Computer:** `http://************:5173/`
- **Virtual Machine:** `http://************:5173/`

## 🔧 Technical Implementation

### **Port Cleanup Command:**

```bash
lsof -ti:5173 | xargs kill -9 2>/dev/null || true
```

**What it does:**

- `lsof -ti:5173` - Lists process IDs using port 5173
- `xargs kill -9` - Forcefully kills those processes
- `2>/dev/null || true` - Suppresses errors if no processes found

### **Vite Configuration:**

```bash
vite dev --host --port 5173
```

**Flags explained:**

- `--host` - Binds to `0.0.0.0` (network accessible)
- `--port 5173` - Forces specific port (no auto-increment)

## 🛠️ Troubleshooting

### **Port Still Occupied?**

```bash
# Check what's using port 5173
lsof -i:5173

# Manual cleanup
npm run ports:cleanup

# Force kill everything on port 5173
sudo lsof -ti:5173 | xargs kill -9
```

### **Permission Denied?**

```bash
# If you get permission errors, try with sudo
sudo lsof -ti:5173 | xargs kill -9
```

### **Network Access Not Working?**

```bash
# Check firewall (Ubuntu/Debian)
sudo ufw allow 5173

# Check if port is accessible
netstat -tlnp | grep 5173
```

### **Multiple Processes Still Running?**

```bash
# Comprehensive cleanup
npm run ports:cleanup

# Check status
npm run ports:status
```

## 🎯 IDE-Specific Notes

### **VS Code / VS Code Insiders:**

- Terminal integration works seamlessly
- Port forwarding automatically configured
- Live reload works across all instances

### **Cursor:**

- Full compatibility with port management
- Network access works perfectly
- Auto-refresh on file changes

### **Windsurf:**

- Complete port 5173 support
- Network URLs accessible
- Hot module replacement functional

## 🚀 Best Practices

### **Recommended Workflow:**

1. **Start in primary IDE:** `npm run dev`
2. **Switch to another IDE:** `npm run dev` (automatically takes over)
3. **Check network access:** Use `http://[YOUR_IP]:5173/` on mobile
4. **Monitor in Portainer:** Container management at `http://localhost:9000/`

### **Bookmarks to Save:**

- **Local Development:** `http://localhost:5173/`
- **Network Access:** `http://************:5173/` (use your actual IP)
- **Portainer:** `http://localhost:9000/`

### **Quick Commands:**

```bash
# Start development (most common)
npm run dev

# Check port status
npm run ports:status

# Manual cleanup if needed
npm run ports:cleanup
```

## 🎉 Benefits

✅ **Consistent Experience:** Same URL across all IDEs  
✅ **No Port Conflicts:** Automatic cleanup prevents issues  
✅ **Network Testing:** Test on real devices easily  
✅ **IDE Flexibility:** Switch between IDEs without reconfiguration  
✅ **Team Collaboration:** Share consistent URLs with team  
✅ **Mobile Development:** Test responsive design on actual devices

Your multi-IDE development workflow is now completely streamlined! 🚀
