# Story 1.001: Create Multiple Portfolios

## Status: Draft

## Story

- As a developer
- I want to create multiple portfolios
- so that I can organize my projects by theme, technology, or career stage

## Acceptance Criteria (ACs)

1. User can create up to 5 separate portfolios
2. Each portfolio has customizable title, description, and branding
3. Portfolio visibility can be set to public, private, or employer-only
4. User can navigate between portfolios easily
5. Portfolio creation includes validation for required fields
6. User receives confirmation when portfolio is successfully created

## Tasks / Subtasks

- [ ] Task 1: Design portfolio data model (AC: 1, 2, 3)
  - [ ] Define portfolio schema with title, description, visibility settings
  - [ ] Add portfolio limit validation (max 5 per user)
  - [ ] Create database migration for portfolio table
- [ ] Task 2: Implement portfolio creation API (AC: 1, 2, 3, 5, 6)
  - [ ] Create POST /api/portfolios endpoint
  - [ ] Add input validation for required fields
  - [ ] Implement portfolio limit checking
  - [ ] Add error handling and success responses
- [ ] Task 3: Build portfolio creation UI (AC: 2, 4, 5, 6)
  - [ ] Create portfolio creation form component
  - [ ] Add title and description input fields
  - [ ] Implement visibility selector (public/private/employer-only)
  - [ ] Add form validation and error display
  - [ ] Show success confirmation after creation
- [ ] Task 4: Implement portfolio navigation (AC: 4)
  - [ ] Create portfolio switcher component
  - [ ] Add portfolio list in user dashboard
  - [ ] Implement portfolio selection state management
  - [ ] Add breadcrumb navigation for current portfolio

## Dev Technical Guidance

### Database Schema

```sql
CREATE TABLE portfolios (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id),
  title VARCHAR(100) NOT NULL,
  description TEXT,
  visibility VARCHAR(20) NOT NULL DEFAULT 'private' CHECK (visibility IN ('public', 'private', 'employer-only')),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_portfolios_user_id ON portfolios(user_id);
```

### API Endpoints

- `POST /api/portfolios` - Create new portfolio
- `GET /api/portfolios` - List user's portfolios
- `GET /api/portfolios/:id` - Get specific portfolio
- `PUT /api/portfolios/:id` - Update portfolio
- `DELETE /api/portfolios/:id` - Delete portfolio

### Frontend Components

- `PortfolioCreationForm` - Form for creating new portfolios
- `PortfolioSwitcher` - Navigation component for switching between portfolios
- `PortfolioList` - Display list of user's portfolios
- `VisibilitySelector` - Component for selecting portfolio visibility

## Story Progress Notes

### Agent Model Used: `<To be filled by implementing agent>`

### Completion Notes List

{To be filled during implementation}

### Change Log

| Date    | Change                 | Author   |
| ------- | ---------------------- | -------- |
| 2025-01 | Initial story creation | Bob (SM) |
