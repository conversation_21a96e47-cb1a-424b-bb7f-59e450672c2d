# 🐳 Dev Container Port Management Guide

This guide explains how to handle port conflicts between regular development and dev container environments.

## 🚨 Common Error: "Port Already in Use"

When you see this VS Code error:

```
A port the container is setup to bind to is already in use. See the Dev Containers terminal for details.
```

**This means:** Your regular dev server (port 5173) conflicts with the dev container trying to use the same port.

## 🔧 Solutions

### **Solution 1: Stop Regular Dev Server (Recommended)**

```bash
# Stop any process using port 5173
lsof -ti:5173 | xargs kill -9 2>/dev/null || true

# Or use our cleanup command
npm run ports:cleanup
```

**Then click "Retry" in VS Code.**

### **Solution 2: Use Different Ports (Parallel Development)**

We've configured the dev container to use **port 5175** externally while keeping **port 5173** internally:

#### **Port Configuration:**

- **Regular Development:** `http://localhost:5173/`
- **Dev Container:** `http://localhost:5175/` (external) → `5173` (internal)
- **Portainer:** `http://localhost:9000/`

#### **Parallel Workflow:**

```bash
# Terminal 1: Regular development
npm run dev                    # → http://localhost:5173/

# Terminal 2: Dev container (VS Code)
# Click "Reopen in Container"  # → http://localhost:5175/
```

### **Solution 3: Choose Your Development Mode**

#### **Option A: Regular Development (Recommended)**

- ✅ Faster startup
- ✅ Direct file system access
- ✅ All IDEs supported (VS Code, Cursor, Windsurf)
- ✅ Port 5173 guaranteed
- ✅ Network access enabled

```bash
npm run dev  # Use this for daily development
```

#### **Option B: Dev Container Development**

- ✅ Isolated environment
- ✅ Consistent across team
- ✅ Docker-in-Docker support
- ✅ VS Code integration
- ⚠️ Slightly slower file watching

```bash
# In VS Code: Ctrl+Shift+P → "Dev Containers: Reopen in Container"
# Access at: http://localhost:5175/
```

## 📋 Port Reference

| Service           | Regular Dev | Dev Container | Purpose              |
| ----------------- | ----------- | ------------- | -------------------- |
| **VybeCoding.ai** | `:5173`     | `:5175`       | Main app             |
| **Portainer**     | `:9000`     | `:9000`       | Container management |
| **Appwrite**      | `:8080`     | `:8080`       | Backend API          |
| **MariaDB**       | `:3306`     | `:3306`       | Database             |
| **Redis**         | `:6379`     | `:6379`       | Cache                |

## 🛠️ Troubleshooting

### **"Port Already in Use" Error**

#### **Quick Fix:**

```bash
# Kill everything on port 5173
lsof -ti:5173 | xargs kill -9 2>/dev/null || true

# Then retry in VS Code
```

#### **Check What's Using the Port:**

```bash
# See what's using port 5173
lsof -i:5173

# See all development ports
npm run ports:status
```

### **Dev Container Won't Start**

#### **Check Docker:**

```bash
# Ensure Docker is running
docker --version
docker ps

# Check for container conflicts
docker ps -a | grep vybecoding
```

#### **Rebuild Container:**

```bash
# In VS Code: Ctrl+Shift+P → "Dev Containers: Rebuild Container"
```

### **Can't Access Dev Container App**

#### **Check Port Mapping:**

```bash
# Verify container is running on correct port
docker ps | grep vybecoding-dev

# Should show: 0.0.0.0:5175->5173/tcp
```

#### **Access URLs:**

- **Dev Container:** `http://localhost:5175/`
- **Network Access:** `http://[YOUR_IP]:5175/`

## 🚀 Best Practices

### **Daily Development Workflow:**

#### **For Most Development (Recommended):**

```bash
# Use regular development
npm run dev  # → http://localhost:5173/
```

#### **For Container Testing:**

```bash
# Stop regular dev first
npm run ports:cleanup

# Then use dev container
# VS Code: "Reopen in Container" → http://localhost:5175/
```

### **Team Collaboration:**

#### **Consistent Environment:**

- Use dev containers for team consistency
- Document which ports team members should use
- Share network access URLs for testing

#### **Individual Preference:**

- Regular development for speed
- Dev containers for isolation
- Both work with same codebase

## 🎯 Quick Commands

```bash
# Check port status
npm run ports:status

# Clean up all ports
npm run ports:cleanup

# Start regular development
npm run dev

# Kill specific port
lsof -ti:5173 | xargs kill -9 2>/dev/null || true

# Check Docker containers
docker ps | grep vybecoding
```

## 📱 Network Access

### **Regular Development:**

- **Local:** `http://localhost:5173/`
- **Network:** `http://[YOUR_IP]:5173/`

### **Dev Container:**

- **Local:** `http://localhost:5175/`
- **Network:** `http://[YOUR_IP]:5175/`

### **Mobile Testing:**

Both environments support network access for testing on mobile devices.

## 🎉 Summary

- **Port 5173:** Regular development (recommended)
- **Port 5175:** Dev container (when needed)
- **Automatic cleanup:** Prevents conflicts
- **Network access:** Both modes support mobile testing
- **Flexible workflow:** Choose what works best for you

Your development environment now handles both regular and containerized development seamlessly! 🚀
