# VybeCoding.ai Docker Container Guide

**Document Type:** Technical Documentation  
**Created:** January 2025  
**Status:** Complete - Container Infrastructure Implemented

---

## 🐳 **Container Architecture Overview**

VybeCoding.ai uses a comprehensive Docker-based container architecture that provides:

- **Development Consistency**: Identical environments across all team members
- **Production Reliability**: Optimized, secure containers for deployment
- **Testing Isolation**: Dedicated testing environments
- **Scalability**: Foundation for horizontal scaling

## 📁 **Container Files Structure**

```
├── Dockerfile                    # Multi-stage production build
├── Dockerfile.dev               # Development environment
├── docker-compose.yml           # Production compose (full stack)
├── docker-compose.dev.yml       # Development compose
├── docker-compose.test.yml      # Testing environment
├── .dockerignore               # Build context optimization
├── .devcontainer/              # VS Code dev container
│   ├── devcontainer.json       # Dev container configuration
│   └── Dockerfile              # Dev container specific
└── scripts/
    ├── docker-setup.sh         # Initial setup script
    ├── docker-dev.sh           # Development workflow
    └── docker-build.sh         # Production build script
```

## 🚀 **Quick Start Guide**

### **1. Initial Setup**

```bash
# Set up Docker environment
./scripts/docker-setup.sh setup

# Start development environment
./scripts/docker-dev.sh start
```

### **2. Development Workflow**

```bash
# Start development server
./scripts/docker-dev.sh start

# View logs
./scripts/docker-dev.sh logs

# Execute commands in container
./scripts/docker-dev.sh exec npm test

# Open shell in container
./scripts/docker-dev.sh shell

# Stop development environment
./scripts/docker-dev.sh stop
```

### **3. Production Build**

```bash
# Build production image
./scripts/docker-build.sh prod

# Test production image
./scripts/docker-build.sh test

# Full release build
./scripts/docker-build.sh release
```

## 🏗️ **Container Specifications**

### **Development Container (Dockerfile.dev)**

- **Base Image**: Node.js 20 Alpine Linux
- **Size**: ~300MB (with dev dependencies)
- **Features**:
  - Hot module replacement (HMR)
  - Development tools (git, vim, curl)
  - Volume mounting for live code editing
  - Non-root user for security
  - Health checks

### **Production Container (Dockerfile)**

- **Base Image**: Node.js 20 Alpine Linux
- **Size**: <200MB (optimized)
- **Features**:
  - Multi-stage build for minimal size
  - Security hardening
  - Non-root user execution
  - Health checks
  - Signal handling with dumb-init

### **Testing Container (docker-compose.test.yml)**

- **Features**:
  - Isolated test environment
  - Mock Appwrite services
  - Browser testing support (Playwright)
  - Coverage reporting
  - CI/CD integration

## 🔧 **Environment Configuration**

### **Development Environment Variables**

```bash
NODE_ENV=development
VITE_ENVIRONMENT=development
VITE_HOST=0.0.0.0
VITE_PORT=5173
VITE_APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
VITE_APPWRITE_PROJECT_ID=vybecoding-dev
VITE_APPWRITE_DATABASE_ID=main
DEBUG=true
LOG_LEVEL=debug
```

### **Production Environment Variables**

```bash
NODE_ENV=production
VITE_ENVIRONMENT=production
PORT=3000
HOST=0.0.0.0
VITE_APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
VITE_APPWRITE_PROJECT_ID=vybecoding-prod
VITE_APPWRITE_DATABASE_ID=main
LOG_LEVEL=info
DEBUG=false
```

## 🔒 **Security Features**

### **Container Security**

- **Non-root execution**: All containers run as non-privileged users
- **Minimal attack surface**: Alpine Linux base with minimal packages
- **Security updates**: Automated security updates in build process
- **Vulnerability scanning**: Trivy integration for security scanning

### **Build Security**

- **Multi-stage builds**: Separate build and runtime environments
- **Optimized .dockerignore**: Excludes sensitive files from build context
- **Secret management**: Environment variables for sensitive data
- **Image signing**: Support for container image signing

## 📊 **Performance Optimization**

### **Build Performance**

- **Layer caching**: Optimized Dockerfile layer ordering
- **Multi-stage builds**: Minimal production image size
- **Build context optimization**: .dockerignore excludes unnecessary files
- **Parallel builds**: Support for multi-platform builds

### **Runtime Performance**

- **Resource limits**: Memory and CPU limits in production
- **Health checks**: Automated health monitoring
- **Graceful shutdown**: Proper signal handling
- **Log management**: Structured logging with rotation

## 🧪 **Testing Strategy**

### **Container Testing**

```bash
# Run unit tests in container
./scripts/docker-dev.sh test

# Run integration tests
docker-compose -f docker-compose.test.yml up --abort-on-container-exit

# Run E2E tests with Playwright
docker-compose -f docker-compose.test.yml --profile e2e up
```

### **Security Testing**

```bash
# Scan for vulnerabilities
./scripts/docker-build.sh scan

# Test container functionality
./scripts/docker-build.sh test
```

## 🚀 **Deployment Integration**

### **CI/CD Integration**

The container infrastructure integrates with GitHub Actions for:

- **Automated builds**: On every commit and release
- **Security scanning**: Vulnerability detection
- **Multi-platform builds**: AMD64 and ARM64 support
- **Registry publishing**: GitHub Container Registry

### **Production Deployment**

```bash
# Deploy to production
docker-compose up -d

# Deploy with specific version
VERSION=v1.0.0 docker-compose up -d

# Rolling update
docker-compose up -d --no-deps vybecoding
```

## 🔍 **Monitoring and Debugging**

### **Container Monitoring**

```bash
# Check container status
./scripts/docker-dev.sh status

# View container logs
./scripts/docker-dev.sh logs

# Monitor resource usage
docker stats vybecoding-dev
```

### **Debugging**

```bash
# Access container shell
./scripts/docker-dev.sh shell

# Execute debugging commands
./scripts/docker-dev.sh exec ps aux

# Inspect container
docker inspect vybecoding-dev
```

## 📋 **Troubleshooting**

### **Common Issues**

1. **Port conflicts**: Change ports in docker-compose files
2. **Permission issues**: Ensure Docker daemon is running
3. **Build failures**: Check .dockerignore and dependencies
4. **Memory issues**: Increase Docker memory limits

### **Recovery Procedures**

```bash
# Clean up containers and images
./scripts/docker-dev.sh clean

# Rebuild from scratch
./scripts/docker-dev.sh rebuild

# Reset development environment
./scripts/docker-setup.sh setup
```

## 🎯 **Best Practices**

### **Development**

- Use volume mounts for live code editing
- Keep containers running for faster development
- Use health checks for reliability
- Monitor resource usage

### **Production**

- Use multi-stage builds for minimal size
- Implement proper health checks
- Use non-root users for security
- Monitor container metrics

### **Maintenance**

- Regular security updates
- Image vulnerability scanning
- Resource usage monitoring
- Log rotation and cleanup

---

**Status**: ✅ **Container Infrastructure Complete**  
**Implementation**: All Docker files, scripts, and documentation ready  
**Next Steps**: Integration with CI/CD pipeline and production deployment
