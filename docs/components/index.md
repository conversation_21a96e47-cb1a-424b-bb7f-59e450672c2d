# Component Library

VybeCoding.ai features a comprehensive component library built with <PERSON>velte, designed for educational applications and AI-powered interfaces.

## Overview

Our component library provides:

- **80+ Components**: Covering UI, business logic, and educational features
- **TypeScript Support**: Full type safety and IntelliSense
- **Accessibility**: WCAG 2.1 AA compliant components
- **Responsive Design**: Mobile-first, responsive layouts
- **Theme Support**: Light/dark themes with custom branding
- **Storybook Documentation**: Interactive component playground

## Component Categories

### UI Components (`/ui`)

Basic building blocks for user interfaces:

- **Button** - Versatile button with multiple variants
- **Card** - Flexible content containers
- **Input** - Form input components
- **Dialog** - Modal dialogs and overlays
- **Badge** - Status indicators and labels
- **Progress** - Progress bars and loading states

### Business Components (`/business`)

Domain-specific components for VybeCoding.ai:

- **MayaDesigner** - AI-powered design interface
- **VybeQubeGenerator** - Website generation interface
- **AutonomousGenerationInterface** - MAS control panel

### Educational Components (`/educational`)

Learning-focused components:

- **CourseProgress** - Track learning progress
- **InteractiveTutorial** - Step-by-step tutorials
- **LearningPathCard** - Course pathway visualization
- **SkillAssessment** - Knowledge testing interface

### Workspace Components (`/workspace`)

Development environment components:

- **CodeEditor** - Monaco-based code editor
- **FileExplorer** - File system navigation
- **AIChatInterface** - AI assistant integration
- **WorkspaceLayout** - Development workspace layout

### Analytics Components (`/analytics`)

Monitoring and metrics visualization:

- **PerformanceChart** - System performance graphs
- **SystemHealthMonitor** - Real-time health monitoring
- **UserBehaviorAnalytics** - User interaction tracking
- **MetricsWidget** - Key performance indicators

## Getting Started

### Installation

Components are included in the main VybeCoding.ai package:

```bash
npm install vybecoding-platform
```

### Basic Usage

```svelte
<script>
  import { Button, Card } from '$lib/components/ui';
</script>

<Card class="p-6">
  <h2>Welcome to VybeCoding.ai</h2>
  <p>Start building amazing educational experiences.</p>
  <Button variant="primary">Get Started</Button>
</Card>
```

### TypeScript Support

All components include full TypeScript definitions:

```typescript
import type { ButtonProps } from '$lib/components/ui/Button.svelte';

const buttonConfig: ButtonProps = {
  variant: 'primary',
  size: 'lg',
  disabled: false,
};
```

## Interactive Documentation

Explore components interactively with our Storybook:

**[🚀 Open Storybook](../storybook/)**

The Storybook includes:

- **Live Examples**: See components in action
- **Props Documentation**: Complete API reference
- **Accessibility Tests**: Built-in a11y validation
- **Responsive Testing**: Test across device sizes
- **Theme Switching**: Preview light/dark themes

## Design System

### Colors

```css
/* Primary Colors */
--vybe-primary: #667eea;
--vybe-secondary: #764ba2;

/* Semantic Colors */
--vybe-success: #10b981;
--vybe-warning: #f59e0b;
--vybe-error: #ef4444;
--vybe-info: #3b82f6;
```

### Typography

```css
/* Font Families */
--font-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
--font-mono: 'Fira Code', 'Monaco', 'Cascadia Code', monospace;

/* Font Sizes */
--text-xs: 0.75rem;
--text-sm: 0.875rem;
--text-base: 1rem;
--text-lg: 1.125rem;
--text-xl: 1.25rem;
```

### Spacing

```css
/* Spacing Scale */
--space-1: 0.25rem;
--space-2: 0.5rem;
--space-4: 1rem;
--space-6: 1.5rem;
--space-8: 2rem;
--space-12: 3rem;
```

## Accessibility

All components follow accessibility best practices:

- **Keyboard Navigation**: Full keyboard support
- **Screen Reader Support**: Proper ARIA labels and roles
- **Color Contrast**: WCAG AA compliant contrast ratios
- **Focus Management**: Visible focus indicators
- **Semantic HTML**: Proper HTML structure

## Contributing

### Adding New Components

1. Create component in appropriate category folder
2. Add TypeScript definitions
3. Write Storybook stories
4. Include accessibility tests
5. Update documentation

### Component Template

```svelte
<script lang="ts">
  export let variant: 'primary' | 'secondary' = 'primary';
  export let size: 'sm' | 'md' | 'lg' = 'md';
  export let disabled = false;

  // Component logic here
</script>

<div
  class="component-base {variant} {size}"
  class:disabled
  role="button"
  tabindex={disabled ? -1 : 0}
  aria-disabled={disabled}
>
  <slot />
</div>

<style>
  .component-base {
    /* Component styles */
  }
</style>
```

## Testing

Components include comprehensive tests:

```bash
# Run component tests
npm run test:components

# Run accessibility tests
npm run test:a11y

# Run visual regression tests
npm run test:visual
```

## Performance

Our components are optimized for performance:

- **Tree Shaking**: Only import what you use
- **Lazy Loading**: Components load on demand
- **Bundle Size**: Minimal runtime overhead
- **SSR Support**: Server-side rendering compatible

## Browser Support

- **Chrome**: 90+
- **Firefox**: 88+
- **Safari**: 14+
- **Edge**: 90+

## Resources

- **[Storybook](../storybook/)** - Interactive component documentation
- **[GitHub](https://github.com/Hiram-Ducky/VybeCoding.ai)** - Source code and issues
- **[Design System](../design-system/)** - Complete design guidelines
