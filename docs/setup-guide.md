# 🎯 Manual Setup Steps for VybeCoding.ai

## ✅ Completed Automatically

- [x] Sample course content created
- [x] Placeholder images generated
- [x] Test user data prepared
- [x] Development scripts ready

## 📋 Manual Steps Required

### 1. Appwrite Collections (5 minutes)

Go to: https://fra.cloud.appwrite.io/console/project/683b200e00153d705da3/databases/database/683b231d003c1c558e20

Create these collections:

- `users` - User management
- `courses` - Course catalog
- `lessons` - Lesson content
- `progress` - Learning progress
- `course_purchases` - Payment records

### 2. Stripe Products (10 minutes)

Go to: https://dashboard.stripe.com/products

Create these products:

- Pro Plan ($29/month)
- Enterprise Plan ($99/month)

Update price IDs in: `src/lib/config/stripe.js`

### 3. Test Your Platform

1. Visit: http://localhost:5175
2. Test pricing page: http://localhost:5175/pricing
3. Try user registration
4. Browse sample courses

## 🚀 You're Ready to Launch!
