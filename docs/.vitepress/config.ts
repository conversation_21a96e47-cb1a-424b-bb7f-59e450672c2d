import { defineConfig } from 'vitepress';

export default defineConfig({
  title: 'VybeCoding.ai Documentation',
  description: 'AI-powered education platform documentation and API reference',
  base: '/VybeCoding.ai/',

  themeConfig: {
    logo: '/logo.svg',

    nav: [
      { text: 'Home', link: '/' },
      { text: 'API', link: '/api/' },
      { text: 'Components', link: '/components/' },
      { text: 'Guides', link: '/guides/' },
      { text: 'Architecture', link: '/architecture/' },
    ],

    sidebar: {
      '/api/': [
        {
          text: 'API Documentation',
          items: [
            { text: 'Overview', link: '/api/' },
            { text: 'Authentication', link: '/api/authentication' },
            { text: 'Health Check', link: '/api/health' },
            { text: 'Metrics', link: '/api/metrics' },
            { text: 'Security', link: '/api/security' },
            { text: 'Vybe Qubes', link: '/api/vybe-qubes' },
            { text: 'Interactive Explorer', link: '/api/swagger-ui/' },
          ],
        },
      ],

      '/components/': [
        {
          text: 'Component Library',
          items: [
            { text: 'Overview', link: '/components/' },
            { text: 'UI Components', link: '/components/ui' },
            { text: 'Business Components', link: '/components/business' },
            { text: 'Educational Components', link: '/components/educational' },
            { text: 'Workspace Components', link: '/components/workspace' },
            { text: 'Storybook', link: '/storybook/' },
          ],
        },
      ],

      '/guides/': [
        {
          text: 'Development Guides',
          items: [
            { text: 'Getting Started', link: '/guides/getting-started' },
            { text: 'Setup Guide', link: '/guides/setup' },
            { text: 'Development Workflow', link: '/guides/workflow' },
            { text: 'Testing', link: '/guides/testing' },
            { text: 'Deployment', link: '/guides/deployment' },
          ],
        },
      ],

      '/architecture/': [
        {
          text: 'Architecture',
          items: [
            { text: 'Overview', link: '/architecture/' },
            { text: 'System Design', link: '/architecture/system-design' },
            { text: 'Database Schema', link: '/architecture/database' },
            { text: 'Security', link: '/architecture/security' },
            { text: 'Performance', link: '/architecture/performance' },
          ],
        },
      ],
    },

    socialLinks: [
      { icon: 'github', link: 'https://github.com/Hiram-Ducky/VybeCoding.ai' },
    ],

    footer: {
      message: 'Released under the MIT License.',
      copyright: 'Copyright © 2024 VybeCoding.ai',
    },

    search: {
      provider: 'local',
    },

    editLink: {
      pattern:
        'https://github.com/Hiram-Ducky/VybeCoding.ai/edit/main/docs/:path',
      text: 'Edit this page on GitHub',
    },
  },

  markdown: {
    theme: {
      light: 'github-light',
      dark: 'github-dark',
    },
    lineNumbers: true,
  },

  head: [
    ['link', { rel: 'icon', href: '/favicon.ico' }],
    ['meta', { name: 'theme-color', content: '#667eea' }],
    ['meta', { property: 'og:type', content: 'website' }],
    ['meta', { property: 'og:locale', content: 'en' }],
    ['meta', { property: 'og:title', content: 'VybeCoding.ai Documentation' }],
    ['meta', { property: 'og:site_name', content: 'VybeCoding.ai' }],
    [
      'meta',
      {
        property: 'og:url',
        content: 'https://hiram-ducky.github.io/VybeCoding.ai/',
      },
    ],
  ],
});
