# 🐳 Portainer Auto-Login Setup for VybeCoding.ai

This guide will help you set up automatic Portainer login when starting your development server.

## 🚀 Quick Start

When you run `npm run dev`, it will now automatically:

1. ✅ Start Portainer (if not running)
2. ✅ Open Portainer in your browser
3. ✅ Auto-login with your credentials
4. ✅ Redirect to the containers page
5. ✅ Start the Vite dev server

## 📋 Available Commands

```bash
# Start dev server with auto Portainer (recommended)
npm run dev

# Start dev server without Portainer
npm run dev:simple

# Just open Portainer with auto-login
npm run portainer:open

# Start dev server with Portainer (alternative)
npm run dev:with-portainer

# Full development setup
npm run dev:full
```

## 🔧 Browser Extension Setup (Optional but Recommended)

For the best experience, install the Tampermonkey userscript:

### Step 1: Install Tampermonkey

- **Chrome/Edge:** [Tampermonkey Extension](https://chrome.google.com/webstore/detail/tampermonkey/dhdgffkkebhmkfjojejmpbldmpobfkfo)
- **Firefox:** [Tampermonkey Add-on](https://addons.mozilla.org/en-US/firefox/addon/tampermonkey/)

### Step 2: Install VybeCoding.ai Auto-Login Script

1. Open Tampermonkey Dashboard
2. Click "Create a new script"
3. Copy the contents of `scripts/portainer-autologin.user.js`
4. Paste into the editor
5. Save (Ctrl+S)

### Step 3: Configure (if needed)

The script uses these default credentials:

- **Username:** `admin`
- **Password:** `VybeCoding2024!`

To change them, edit the `CONFIG` object in the userscript:

```javascript
const CONFIG = {
  username: 'your-username',
  password: 'your-password',
  containersPage: 'http://localhost:9000/#!/3/docker/containers',
  debugMode: true,
};
```

## 🎯 How It Works

### Automatic Flow:

1. **Dev Server Start:** `npm run dev` triggers the auto-Portainer script
2. **Portainer Check:** Script checks if Portainer is running
3. **Auto-Start:** If not running, starts Portainer container
4. **Browser Launch:** Opens Portainer in your default browser
5. **Auto-Login:** Tampermonkey script detects login page and fills credentials
6. **Auto-Redirect:** After login, redirects to containers page
7. **Dev Server:** Vite dev server starts in parallel

### Manual Control:

```bash
# Start/stop Portainer manually
npm run portainer:start
npm run portainer:stop

# View Portainer logs
npm run portainer:logs

# Reset Portainer (removes container and data)
npm run portainer:reset
```

## 🔍 Troubleshooting

### Portainer Won't Start

```bash
# Check if Docker is running
docker --version
docker ps

# Check for port conflicts
sudo netstat -tulpn | grep :9000

# Reset Portainer completely
npm run portainer:reset
```

### Auto-Login Not Working

1. **Check Tampermonkey:** Ensure the script is enabled
2. **Check Console:** Open browser dev tools and look for script logs
3. **Check Credentials:** Verify username/password in the script
4. **Manual Login:** Try logging in manually first

### Browser Not Opening

The script tries to open Firefox Developer Edition first, then falls back to your default browser. To force a specific browser:

```bash
# Edit scripts/auto-portainer.js and modify the browser opening logic
```

## 🎨 Customization

### Change Default Credentials

Edit `scripts/auto-portainer.js`:

```javascript
const PORTAINER_USERNAME = 'your-username';
const PORTAINER_PASSWORD = 'your-password';
```

### Change Default Page

Edit the userscript to redirect to a different page:

```javascript
const CONFIG = {
  containersPage: 'http://localhost:9000/#!/3/docker/images', // Images page
  // or
  containersPage: 'http://localhost:9000/#!/3/docker/volumes', // Volumes page
};
```

### Disable Auto-Login

If you want Portainer to open but not auto-login:

```bash
# Use the simple dev command
npm run dev:simple

# Then manually open Portainer
npm run portainer:open
```

## 🔐 Security Notes

- The auto-login credentials are stored in plain text in the scripts
- This is intended for local development only
- Never use this setup in production
- Consider using environment variables for credentials if sharing the codebase

## 🎉 Benefits

✅ **Streamlined Workflow:** One command starts everything  
✅ **No Manual Steps:** Automatic login and navigation  
✅ **Consistent Setup:** Same experience across all developers  
✅ **Time Saving:** No more manual Portainer setup each time  
✅ **Visual Feedback:** Clear console output showing what's happening

## 📚 Next Steps

Once set up, your typical development workflow becomes:

```bash
# Start everything
npm run dev

# Code in your editor while monitoring containers in Portainer
# Portainer automatically opens at: http://localhost:9000
# Dev server runs at: http://localhost:5173
```

Happy coding! 🚀
