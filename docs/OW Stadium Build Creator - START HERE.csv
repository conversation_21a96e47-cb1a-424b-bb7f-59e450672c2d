🏁 Welcome to the Stadium Mode Build Guide!
"This tool is designed to help you theorycraft and quickly reference Stadium mode builds during matches.
Follow the simple steps below to get started!"

🚀 Step 1: Make Your Own Copy
"1. Go to File > Make a copy
2. Save it into your own Google Drive
This will let you fully edit and use the tool"

"🛠️ Step 2: Create Your Own Builds in the ""Buids"" Tab"
"1. Each Build will consist of 7 rows to make up the 7 possible rounds
2. Select your hero first in the first row of the build (the following rows will auto populate)
     a. Powers and Items will populate in the dropwdown menus based on the hero you select
3. Name your build in the first row of the build (the following rows will auto populate)
4. Set the difficulty of your build in the first row of the build (the following rows will auto populate)
     a. You can set a difficulty based on things like mechanical skill required to make the build work or the high cost to make it work
5. Select your Powers for each odd numbered round
6. Select your items for each round (keep an eye on your round cost!)
7. Add any notes or strategies you want to remember for each round if relevant
8. See my example Juno build for how all of the above is done"

"🧩 Step 3: Reference and Use Your Builds in the ""Guide"" Tab"
"1. Select a Hero in cell C2.
2. Select a Build in cell G2 (Only builds available for the selected hero will appear!).
3. The recommended Powers, Items, Round Costs, and Strategy Notes will automatically appear below, round-by-round.
4. Use this during Stadium matches to plan purchases and upgrades between rounds!"

⚡ Quick Tips and Notes
"- This sheet is great on a phone or tablet if you have the Google Sheets app
- If you change the Hero on the Guide tab, your Build selection will automatically clear so you can pick a valid one.
- If no builds exist for a selected Hero, “No Available Builds” will show.
- Because the sheet runs on App Script automations, some automations (like the above) will take several seconds to load, so be patient :)
- When choosing items for your build, the ones at the bottom of the dropdown will the ones specific to the hero youve chosen (the ones above that are for all heroes)
- Each item uses a pair of emojis to represent the type (🔫weapon, 💫ability, or 🛟survival) and the rarity (🟢common, 🔵rare, or 🟣epic)
- There are restrictions set across each tab to warn you before you edit a cell that should not be edited (ignore at your own risk!)
- Share builds with others! Inspire others or help new players with your fave builds by screenshotting the Guide tab and sharing!
- This tool is my first draft, so there may be minor errors. If so, let me know and I will try and fix them!"

🙌 Credits
"- Designed by Dibblin
- Technical help by ChatGPT (lol)"

"🎮 Now go dominate Stadium mode!
(except if you play againt me. Then let me win)"