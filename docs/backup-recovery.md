# VybeCoding.ai Backup & Recovery System

## Overview

The VybeCoding.ai Backup & Recovery System provides comprehensive data protection and disaster recovery capabilities for the educational platform. This system ensures business continuity with automated backups, integrity verification, and tested recovery procedures.

## 🎯 Key Features

- **Automated Backups**: Scheduled database, file, and configuration backups
- **Encryption**: AES-256 encryption for sensitive backup data
- **Cloud Storage**: Multi-cloud support (AWS S3, Google Cloud, Azure)
- **Integrity Verification**: Automated backup integrity checks
- **Disaster Recovery**: Comprehensive recovery procedures and testing
- **Monitoring**: Real-time backup health monitoring and alerting
- **RTO/RPO Compliance**: Meets 4-hour RTO and 1-hour RPO targets

## 📋 System Components

### Core Scripts

| Script                      | Purpose               | Usage                                        |
| --------------------------- | --------------------- | -------------------------------------------- |
| `backup-system.sh`          | Main backup execution | `./scripts/backup-system.sh`                 |
| `recovery-system.sh`        | Disaster recovery     | `./scripts/recovery-system.sh [date] [type]` |
| `backup-monitor.sh`         | Health monitoring     | `./scripts/backup-monitor.sh`                |
| `disaster-recovery-test.sh` | DR testing            | `./scripts/disaster-recovery-test.sh`        |

### Configuration

- **Main Config**: `config/backup-config.env`
- **Environment Variables**: See configuration section below
- **Cron Jobs**: Automated scheduling

## 🚀 Quick Start

### 1. Initial Setup

```bash
# Create backup directory
sudo mkdir -p /backups
sudo chown $USER:$USER /backups

# Make scripts executable
chmod +x scripts/backup-*.sh scripts/recovery-*.sh scripts/disaster-*.sh

# Copy and configure settings
cp config/backup-config.env.example config/backup-config.env
nano config/backup-config.env
```

### 2. First Backup

```bash
# Run manual backup
./scripts/backup-system.sh

# Verify backup
./scripts/backup-monitor.sh --integrity
```

### 3. Setup Automation

```bash
# Install cron jobs
crontab -e

# Add these lines:
0 2 * * * /path/to/vybecoding/scripts/backup-system.sh
0 */6 * * * /path/to/vybecoding/scripts/backup-monitor.sh
0 3 * * 0 /path/to/vybecoding/scripts/disaster-recovery-test.sh
```

## ⚙️ Configuration

### Environment Variables

```bash
# Basic Configuration
export BACKUP_DIR="/backups"
export RETENTION_DAYS="30"
export BACKUP_ENCRYPTION_KEY="your-encryption-key"

# Cloud Storage (AWS S3 example)
export AWS_S3_BUCKET="vybecoding-backups"
export AWS_ACCESS_KEY_ID="your-access-key"
export AWS_SECRET_ACCESS_KEY="your-secret-key"

# Notifications
export SLACK_WEBHOOK_URL="https://hooks.slack.com/services/..."
export NOTIFICATION_EMAIL="<EMAIL>"
```

### Encryption Key Generation

```bash
# Generate secure encryption key
openssl rand -base64 32
```

## 📊 Backup Types

### Database Backups

- **Format**: PostgreSQL dump (SQL)
- **Compression**: gzip
- **Encryption**: AES-256 (optional)
- **Frequency**: Daily
- **Retention**: 30 days

### File Backups

- **Uploads**: User-uploaded content
- **Configuration**: Application config files
- **Logs**: Application logs (last 7 days)
- **Format**: tar.gz archives

### Cloud Storage

- **AWS S3**: Standard-IA storage class
- **Google Cloud**: Nearline storage
- **Azure Blob**: Cool tier
- **Sync**: Automatic after local backup

## 🔄 Recovery Procedures

### Full System Recovery

```bash
# Full recovery from latest backup
./scripts/recovery-system.sh latest full

# Recovery from specific date
./scripts/recovery-system.sh 20240115_120000 full
```

### Partial Recovery

```bash
# Database only
./scripts/recovery-system.sh latest database-only

# Files only
./scripts/recovery-system.sh latest files-only
```

### Recovery Process

1. **Safety Confirmation**: User must type "YES" to confirm
2. **Pre-Recovery Checks**: Verify prerequisites and backup files
3. **Recovery Point Creation**: Backup current state before recovery
4. **Service Shutdown**: Stop application services
5. **Data Restoration**: Restore database and files
6. **Service Startup**: Restart all services
7. **Health Verification**: Validate system health
8. **Notification**: Send recovery status alerts

## 🔍 Monitoring & Alerting

### Health Checks

- **Backup Freshness**: Alert if no backup in 25 hours
- **Backup Integrity**: Verify backup file integrity
- **Storage Usage**: Alert if storage > 80% full
- **Cloud Connectivity**: Test cloud storage access
- **Schedule Verification**: Confirm cron jobs are active

### Monitoring Commands

```bash
# Full health check
./scripts/backup-monitor.sh

# Specific checks
./scripts/backup-monitor.sh --freshness
./scripts/backup-monitor.sh --integrity
./scripts/backup-monitor.sh --storage
./scripts/backup-monitor.sh --cloud

# Generate report
./scripts/backup-monitor.sh --report
```

### Alert Channels

- **Slack**: Real-time notifications
- **Discord**: Community alerts
- **Email**: Critical notifications
- **Logs**: Detailed logging

## 🧪 Disaster Recovery Testing

### Automated Testing

```bash
# Full DR test suite
./scripts/disaster-recovery-test.sh

# Individual tests
./scripts/disaster-recovery-test.sh --integrity
./scripts/disaster-recovery-test.sh --database
./scripts/disaster-recovery-test.sh --files
./scripts/disaster-recovery-test.sh --rto
./scripts/disaster-recovery-test.sh --rpo
```

### Test Coverage

- **Backup Integrity**: Verify all backup files
- **Database Recovery**: Test database restoration
- **File Recovery**: Test file restoration
- **RTO Testing**: Verify 4-hour recovery target
- **RPO Testing**: Verify 1-hour backup frequency

### Test Schedule

- **Weekly**: Automated DR tests every Sunday
- **Monthly**: Full system recovery test
- **Quarterly**: Business continuity exercise

## 📈 Performance & Optimization

### Backup Performance

- **Compression**: Level 6 (balance of speed/size)
- **Parallel Jobs**: 2 concurrent backup operations
- **I/O Priority**: Low priority to avoid impact
- **Bandwidth Limiting**: Configurable upload limits

### Storage Optimization

- **Incremental Backups**: Future enhancement
- **Deduplication**: Cloud storage native features
- **Lifecycle Policies**: Automatic archival
- **Cleanup**: Automated old backup removal

## 🔒 Security

### Encryption

- **Algorithm**: AES-256-CBC
- **Key Management**: Environment variables
- **At Rest**: Encrypted backup files
- **In Transit**: HTTPS/TLS for cloud uploads

### Access Control

- **File Permissions**: 600 for backup files
- **Directory Permissions**: 700 for backup directories
- **User Access**: Restricted to backup user
- **Cloud IAM**: Least privilege access

### Compliance

- **Data Protection**: GDPR/CCPA compliant
- **Audit Trail**: Complete backup/recovery logs
- **Retention**: Configurable retention policies
- **Verification**: Integrity and authenticity checks

## 🚨 Troubleshooting

### Common Issues

#### Backup Failures

```bash
# Check disk space
df -h /backups

# Check permissions
ls -la /backups

# Check database connectivity
docker exec appwrite-db pg_isready -U postgres

# Review logs
tail -f /backups/backup.log
```

#### Recovery Issues

```bash
# Verify backup integrity
./scripts/backup-monitor.sh --integrity

# Check available backups
./scripts/recovery-system.sh --list

# Test recovery in dry-run mode
DRY_RUN=true ./scripts/recovery-system.sh
```

#### Cloud Storage Issues

```bash
# Test AWS S3 connectivity
aws s3 ls s3://your-bucket/

# Test Google Cloud Storage
gsutil ls gs://your-bucket/

# Test Azure Blob Storage
az storage blob list --container-name your-container
```

### Log Analysis

```bash
# Backup logs
tail -f /backups/backup.log

# Recovery logs
tail -f /backups/recovery.log

# Monitoring logs
tail -f /backups/backup-monitor.log

# DR test logs
tail -f /backups/dr-test.log
```

## 📞 Support & Maintenance

### Regular Maintenance

- **Weekly**: Review backup reports
- **Monthly**: Test recovery procedures
- **Quarterly**: Update retention policies
- **Annually**: Review and update DR plan

### Support Contacts

- **Technical Support**: <EMAIL>
- **Emergency**: <EMAIL>
- **Documentation**: docs.vybecoding.ai

### Version History

- **v1.0**: Initial backup system implementation
- **v1.1**: Added cloud storage support
- **v1.2**: Enhanced monitoring and alerting
- **v1.3**: Disaster recovery testing automation

## 📚 Additional Resources

- [Backup Configuration Reference](backup-config.env)
- [Recovery Runbook](recovery-runbook.md)
- [Monitoring Dashboard](monitoring-dashboard.md)
- [Security Guidelines](security-guidelines.md)

## 🔧 Installation Script

For automated setup, use the installation script:

```bash
# Run the backup system installer
./scripts/install-backup-system.sh

# This will:
# - Create backup directories
# - Set proper permissions
# - Install cron jobs
# - Configure monitoring
# - Run initial backup test
```

---

**Last Updated**: January 2025
**Version**: 1.3
**Maintainer**: VybeCoding.ai DevOps Team
