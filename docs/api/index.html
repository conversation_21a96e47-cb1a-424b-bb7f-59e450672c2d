<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>VybeCoding.ai API Documentation</title>
    <style>
      body {
        font-family:
          -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        line-height: 1.6;
        color: #333;
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
        background: #f8f9fa;
      }
      .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 10px;
        margin-bottom: 2rem;
        text-align: center;
      }
      .grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
      }
      .card {
        background: white;
        padding: 1.5rem;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
      }
      .card:hover {
        transform: translateY(-2px);
      }
      .card h3 {
        margin-top: 0;
        color: #667eea;
      }
      .card a {
        color: #667eea;
        text-decoration: none;
        font-weight: 500;
        display: inline-block;
        margin: 0.25rem 0;
      }
      .card a:hover {
        text-decoration: underline;
      }
      .endpoint {
        background: #f8f9fa;
        padding: 0.5rem;
        border-radius: 4px;
        margin: 0.5rem 0;
        font-family: 'Monaco', 'Menlo', monospace;
        font-size: 0.9rem;
      }
      .method {
        display: inline-block;
        padding: 0.2rem 0.5rem;
        border-radius: 3px;
        color: white;
        font-weight: bold;
        margin-right: 0.5rem;
        font-size: 0.8rem;
      }
      .get {
        background: #28a745;
      }
      .post {
        background: #007bff;
      }
      .put {
        background: #ffc107;
        color: #333;
      }
      .delete {
        background: #dc3545;
      }
      .footer {
        text-align: center;
        margin-top: 3rem;
        padding: 1rem;
        color: #666;
        border-top: 1px solid #eee;
      }
      .status-indicator {
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin-right: 0.5rem;
      }
      .status-healthy {
        background: #28a745;
      }
      .status-warning {
        background: #ffc107;
      }
      .status-error {
        background: #dc3545;
      }
    </style>
  </head>
  <body>
    <div class="header">
      <h1>🚀 VybeCoding.ai API Documentation</h1>
      <p>Comprehensive API reference for the AI-powered education platform</p>
      <div id="api-status">
        <span class="status-indicator status-healthy"></span>
        <span>API Status: <span id="status-text">Checking...</span></span>
      </div>
    </div>

    <div class="grid">
      <div class="card">
        <h3>📚 API Specifications</h3>
        <p>
          Interactive API documentation with examples and testing capabilities.
        </p>
        <a href="openapi.yaml">📄 OpenAPI Specification</a><br />
        <a href="postman-collection.json">📮 Postman Collection</a><br />
        <a href="typescript/index.html">📘 TypeScript Documentation</a>
      </div>

      <div class="card">
        <h3>🏥 Health & Monitoring</h3>
        <p>System health checks and performance monitoring endpoints.</p>
        <div class="endpoint">
          <span class="method get">GET</span>/api/health
        </div>
        <div class="endpoint">
          <span class="method get">GET</span>/api/metrics
        </div>
        <div class="endpoint">
          <span class="method post">POST</span>/api/metrics
        </div>
      </div>

      <div class="card">
        <h3>🔒 Security</h3>
        <p>Security status and vulnerability assessment endpoints.</p>
        <div class="endpoint">
          <span class="method get">GET</span>/api/security
        </div>
        <p>
          <strong>Authentication:</strong> Bearer Token, API Key, or Session
          Cookie
        </p>
      </div>

      <div class="card">
        <h3>👥 User Management</h3>
        <p>User authentication, profiles, and account management.</p>
        <div class="endpoint">
          <span class="method post">POST</span>/api/auth/login
        </div>
        <div class="endpoint">
          <span class="method post">POST</span>/api/auth/register
        </div>
        <div class="endpoint">
          <span class="method get">GET</span>/api/users/profile
        </div>
      </div>

      <div class="card">
        <h3>📚 Course Management</h3>
        <p>Course creation, management, and content delivery.</p>
        <div class="endpoint">
          <span class="method get">GET</span>/api/courses
        </div>
        <div class="endpoint">
          <span class="method post">POST</span>/api/courses
        </div>
        <div class="endpoint">
          <span class="method get">GET</span>/api/courses/{id}
        </div>
      </div>

      <div class="card">
        <h3>🎨 Vybe Qubes</h3>
        <p>AI-powered website generation and customization.</p>
        <div class="endpoint">
          <span class="method post">POST</span>/api/vybe-qubes/generate
        </div>
        <div class="endpoint">
          <span class="method get">GET</span>/api/vybe-qubes/{id}
        </div>
        <div class="endpoint">
          <span class="method put">PUT</span>/api/vybe-qubes/{id}
        </div>
      </div>
    </div>

    <div class="card">
      <h3>🔗 Quick Testing</h3>
      <p>Test API endpoints directly from this page:</p>
      <button
        onclick="testHealthEndpoint()"
        style="
          margin: 0.5rem;
          padding: 0.5rem 1rem;
          background: #28a745;
          color: white;
          border: none;
          border-radius: 4px;
          cursor: pointer;
        "
      >
        Test Health Check
      </button>
      <button
        onclick="testMetricsEndpoint()"
        style="
          margin: 0.5rem;
          padding: 0.5rem 1rem;
          background: #007bff;
          color: white;
          border: none;
          border-radius: 4px;
          cursor: pointer;
        "
      >
        Test Metrics
      </button>
      <button
        onclick="testSecurityEndpoint()"
        style="
          margin: 0.5rem;
          padding: 0.5rem 1rem;
          background: #ffc107;
          color: #333;
          border: none;
          border-radius: 4px;
          cursor: pointer;
        "
      >
        Test Security
      </button>

      <div
        id="test-results"
        style="
          margin-top: 1rem;
          padding: 1rem;
          background: #f8f9fa;
          border-radius: 4px;
          display: none;
        "
      >
        <h4>Test Results:</h4>
        <pre
          id="test-output"
          style="
            background: #fff;
            padding: 1rem;
            border-radius: 4px;
            overflow-x: auto;
          "
        ></pre>
      </div>
    </div>

    <div class="footer">
      <p>Generated on: <span id="timestamp"></span></p>
      <p>VybeCoding.ai - AI-powered education platform</p>
      <p><a href="../index.html">← Back to Documentation Home</a></p>
    </div>

    <script>
      // Set timestamp
      document.getElementById('timestamp').textContent =
        new Date().toLocaleString();

      // Check API status
      async function checkApiStatus() {
        try {
          const response = await fetch('/api/health');
          const data = await response.json();

          const statusElement = document.getElementById('status-text');
          const indicatorElement = document.querySelector('.status-indicator');

          if (response.ok && data.status === 'healthy') {
            statusElement.textContent = 'Healthy';
            indicatorElement.className = 'status-indicator status-healthy';
          } else {
            statusElement.textContent = 'Degraded';
            indicatorElement.className = 'status-indicator status-warning';
          }
        } catch (error) {
          const statusElement = document.getElementById('status-text');
          const indicatorElement = document.querySelector('.status-indicator');
          statusElement.textContent = 'Offline';
          indicatorElement.className = 'status-indicator status-error';
        }
      }

      // Test endpoints
      async function testEndpoint(url, method = 'GET', body = null) {
        const resultsDiv = document.getElementById('test-results');
        const outputPre = document.getElementById('test-output');

        resultsDiv.style.display = 'block';
        outputPre.textContent = 'Testing...';

        try {
          const options = { method };
          if (body) {
            options.headers = { 'Content-Type': 'application/json' };
            options.body = JSON.stringify(body);
          }

          const response = await fetch(url, options);
          const data = await response.json();

          outputPre.textContent = JSON.stringify(
            {
              status: response.status,
              statusText: response.statusText,
              data: data,
            },
            null,
            2
          );
        } catch (error) {
          outputPre.textContent = `Error: ${error.message}`;
        }
      }

      function testHealthEndpoint() {
        testEndpoint('/api/health');
      }

      function testMetricsEndpoint() {
        testEndpoint('/api/metrics');
      }

      function testSecurityEndpoint() {
        testEndpoint('/api/security');
      }

      // Check status on load
      checkApiStatus();

      // Refresh status every 30 seconds
      setInterval(checkApiStatus, 30000);
    </script>
  </body>
</html>
