# VybeCoding.ai API Documentation

Welcome to the VybeCoding.ai API! Our REST API provides programmatic access to all platform features, enabling you to build powerful integrations and applications.

## 🚀 Quick Start

### Base URL

```
Production:  https://api.vybecoding.ai/v1
Staging:     https://staging-api.vybecoding.ai/v1
Development: http://localhost:5173/api
```

### Authentication

All API requests require authentication using one of these methods:

#### Bearer <PERSON>ken (Recommended)

```bash
curl -H "Authorization: Bearer YOUR_API_TOKEN" \
     https://api.vybecoding.ai/v1/courses
```

#### API Key

```bash
curl -H "X-API-Key: YOUR_API_KEY" \
     https://api.vybecoding.ai/v1/courses
```

### Get Your API Key

1. Sign in to [VybeCoding.ai](https://vybecoding.ai)
2. Go to Settings → Developer
3. Generate a new API key
4. Copy and securely store your key

## 📚 API Endpoints

### Authentication

| Method | Endpoint         | Description       |
| ------ | ---------------- | ----------------- |
| POST   | `/auth/login`    | User login        |
| POST   | `/auth/register` | User registration |
| POST   | `/auth/logout`   | User logout       |
| GET    | `/auth/me`       | Get current user  |
| POST   | `/auth/refresh`  | Refresh token     |

### Users

| Method | Endpoint             | Description            |
| ------ | -------------------- | ---------------------- |
| GET    | `/users/profile`     | Get user profile       |
| PUT    | `/users/profile`     | Update user profile    |
| GET    | `/users/preferences` | Get user preferences   |
| PUT    | `/users/preferences` | Update preferences     |
| GET    | `/users/analytics`   | Get learning analytics |

### Courses

| Method | Endpoint                | Description        |
| ------ | ----------------------- | ------------------ |
| GET    | `/courses`              | List all courses   |
| GET    | `/courses/{id}`         | Get course details |
| POST   | `/courses`              | Create new course  |
| PUT    | `/courses/{id}`         | Update course      |
| DELETE | `/courses/{id}`         | Delete course      |
| GET    | `/courses/{id}/lessons` | Get course lessons |

### Vybe Qubes

| Method | Endpoint             | Description       |
| ------ | -------------------- | ----------------- |
| GET    | `/qubes`             | List user's Qubes |
| GET    | `/qubes/{id}`        | Get Qube details  |
| POST   | `/qubes`             | Create new Qube   |
| PUT    | `/qubes/{id}`        | Update Qube       |
| DELETE | `/qubes/{id}`        | Delete Qube       |
| POST   | `/qubes/{id}/deploy` | Deploy Qube       |

### AI Services

| Method | Endpoint              | Description            |
| ------ | --------------------- | ---------------------- |
| POST   | `/ai/code-review`     | Analyze code           |
| POST   | `/ai/generate`        | Generate content       |
| POST   | `/ai/chat`            | AI assistant chat      |
| GET    | `/ai/recommendations` | Get recommendations    |
| POST   | `/ai/personalize`     | Update personalization |

### Revenue

| Method | Endpoint                | Description         |
| ------ | ----------------------- | ------------------- |
| GET    | `/revenue/summary`      | Revenue summary     |
| GET    | `/revenue/transactions` | Transaction history |
| GET    | `/revenue/analytics`    | Revenue analytics   |
| POST   | `/revenue/payout`       | Request payout      |
| GET    | `/revenue/payouts`      | Payout history      |

## 📝 Request/Response Format

### Request Headers

```
Content-Type: application/json
Authorization: Bearer YOUR_TOKEN
X-API-Key: YOUR_API_KEY (alternative)
```

### Response Format

All responses follow this structure:

#### Success Response

```json
{
  "success": true,
  "data": {
    // Response data here
  },
  "message": "Operation completed successfully",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

#### Error Response

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input parameters",
    "details": {
      "field": "email",
      "issue": "Invalid email format"
    }
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## 🔍 Example Requests

### Get All Courses

```bash
curl -X GET "https://api.vybecoding.ai/v1/courses" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

Response:

```json
{
  "success": true,
  "data": {
    "courses": [
      {
        "id": "course_123",
        "title": "Introduction to Vybe Method",
        "description": "Learn the fundamentals of rapid skill acquisition",
        "level": "beginner",
        "duration": 120,
        "price": 29.99,
        "rating": 4.8,
        "enrollmentCount": 1250
      }
    ],
    "total": 25,
    "page": 1,
    "limit": 20
  }
}
```

### Create a Vybe Qube

```bash
curl -X POST "https://api.vybecoding.ai/v1/qubes" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "My Portfolio Website",
    "description": "A responsive portfolio showcasing my projects",
    "template": "portfolio",
    "technologies": ["HTML", "CSS", "JavaScript"]
  }'
```

Response:

```json
{
  "success": true,
  "data": {
    "qube": {
      "id": "qube_456",
      "title": "My Portfolio Website",
      "description": "A responsive portfolio showcasing my projects",
      "status": "draft",
      "createdAt": "2024-01-01T12:00:00Z",
      "deploymentUrl": null
    }
  },
  "message": "Vybe Qube created successfully"
}
```

### AI Code Review

```bash
curl -X POST "https://api.vybecoding.ai/v1/ai/code-review" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "function hello() { console.log(\"Hello World\"); }",
    "language": "javascript",
    "context": "learning_exercise"
  }'
```

Response:

```json
{
  "success": true,
  "data": {
    "analysis": {
      "qualityScore": 85,
      "suggestions": [
        {
          "type": "improvement",
          "message": "Consider adding parameter validation",
          "line": 1,
          "severity": "medium"
        }
      ],
      "strengths": ["Clean syntax", "Proper function declaration"],
      "improvements": ["Add error handling", "Include documentation"]
    }
  }
}
```

## 📊 Rate Limits

### Rate Limit Tiers

| Tier       | Requests/Hour | Burst Limit |
| ---------- | ------------- | ----------- |
| Free       | 1,000         | 100/minute  |
| Pro        | 10,000        | 500/minute  |
| Enterprise | Custom        | Custom      |

### Rate Limit Headers

```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
```

### Handling Rate Limits

When you exceed rate limits, you'll receive a `429 Too Many Requests` response:

```json
{
  "success": false,
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "Rate limit exceeded. Try again in 60 seconds.",
    "retryAfter": 60
  }
}
```

## 🔒 Security

### API Key Security

- **Never expose API keys** in client-side code
- **Use environment variables** to store keys
- **Rotate keys regularly** for enhanced security
- **Use different keys** for different environments

### HTTPS Only

All API requests must use HTTPS. HTTP requests will be rejected.

### Input Validation

All input is validated and sanitized. Invalid requests return detailed error messages.

## 📈 Monitoring

### Health Check

```bash
curl https://api.vybecoding.ai/v1/health
```

Response:

```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00Z",
  "version": "1.0.0",
  "uptime": 99.99
}
```

### Status Page

Monitor API status at: https://status.vybecoding.ai

## 🛠️ SDKs & Libraries

### Official SDKs

- **JavaScript/Node.js**: `npm install @vybecoding/sdk`
- **Python**: `pip install vybecoding-sdk`
- **PHP**: `composer require vybecoding/sdk`

### Community SDKs

- **Ruby**: `gem install vybecoding`
- **Go**: `go get github.com/vybecoding/go-sdk`
- **C#**: `dotnet add package VybeCoding.SDK`

## 📞 Support

### Getting Help

- **Documentation**: Start here for comprehensive guides
- **Community Forum**: Ask questions and get community support
- **Support Tickets**: Contact our technical support team
- **Discord**: Join our developer community

### Contact

- **Email**: <EMAIL>
- **Discord**: [Developer Community](https://discord.gg/vybecoding-dev)
- **GitHub**: [Report Issues](https://github.com/vybecoding/api-issues)

## 📄 Terms & Policies

- **[API Terms of Service](../legal/api-terms.md)**
- **[Rate Limiting Policy](./rate-limits.md)**
- **[Data Usage Policy](../legal/data-usage.md)**
- **[Privacy Policy](../legal/privacy.md)**

---

**Need help?** Contact our API support <NAME_EMAIL>

_Last Updated: January 2025_
