// Swagger JSDoc configuration for VybeCoding.ai API documentation
module.exports = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'VybeCoding.ai API',
      version: '1.0.0',
      description: 'AI-powered education platform API for the Vybe Method',
      contact: {
        name: 'VybeCoding.ai Support',
        email: '<EMAIL>',
        url: 'https://vybecoding.ai/support',
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT',
      },
      termsOfService: 'https://vybecoding.ai/terms',
    },
    servers: [
      {
        url: 'https://api.vybecoding.ai/v1',
        description: 'Production server',
      },
      {
        url: 'https://staging-api.vybecoding.ai/v1',
        description: 'Staging server',
      },
      {
        url: 'http://localhost:5173/api',
        description: 'Development server',
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'JWT token obtained from Appwrite authentication',
        },
        apiKey: {
          type: 'apiKey',
          in: 'header',
          name: 'X-API-Key',
          description: 'API key for service-to-service authentication',
        },
        appwriteSession: {
          type: 'apiKey',
          in: 'cookie',
          name: 'a_session_console',
          description: 'Appwrite session cookie',
        },
      },
      schemas: {
        Error: {
          type: 'object',
          properties: {
            error: {
              type: 'string',
              description: 'Error message',
            },
            code: {
              type: 'string',
              description: 'Error code',
            },
            timestamp: {
              type: 'string',
              format: 'date-time',
              description: 'Error timestamp',
            },
          },
          required: ['error'],
        },
        HealthStatus: {
          type: 'object',
          properties: {
            status: {
              type: 'string',
              enum: ['healthy', 'degraded', 'unhealthy'],
            },
            timestamp: {
              type: 'string',
              format: 'date-time',
            },
            version: {
              type: 'string',
            },
            environment: {
              type: 'string',
            },
            checks: {
              type: 'object',
              properties: {
                server: {
                  type: 'string',
                  enum: ['ok', 'warning', 'error'],
                },
                database: {
                  type: 'string',
                  enum: ['ok', 'warning', 'error'],
                },
                appwrite: {
                  type: 'string',
                  enum: ['ok', 'warning', 'error'],
                },
              },
            },
          },
        },
        SecurityStatus: {
          type: 'object',
          properties: {
            overall: {
              type: 'string',
              enum: ['secure', 'warning', 'critical'],
            },
            timestamp: {
              type: 'string',
              format: 'date-time',
            },
            checks: {
              type: 'object',
              properties: {
                dependencies: {
                  $ref: '#/components/schemas/SecurityCheck',
                },
                headers: {
                  $ref: '#/components/schemas/SecurityCheck',
                },
                authentication: {
                  $ref: '#/components/schemas/SecurityCheck',
                },
                encryption: {
                  $ref: '#/components/schemas/SecurityCheck',
                },
                compliance: {
                  $ref: '#/components/schemas/SecurityCheck',
                },
              },
            },
          },
        },
        SecurityCheck: {
          type: 'object',
          properties: {
            status: {
              type: 'string',
              enum: ['pass', 'warn', 'fail'],
            },
            score: {
              type: 'number',
              minimum: 0,
              maximum: 100,
            },
            message: {
              type: 'string',
            },
            details: {
              type: 'array',
              items: {
                type: 'string',
              },
            },
          },
        },
        Metrics: {
          type: 'object',
          properties: {
            timestamp: {
              type: 'string',
              format: 'date-time',
            },
            performance: {
              type: 'object',
              properties: {
                responseTime: {
                  type: 'number',
                  description: 'Response time in milliseconds',
                },
                memoryUsage: {
                  type: 'object',
                  properties: {
                    rss: { type: 'number' },
                    heapTotal: { type: 'number' },
                    heapUsed: { type: 'number' },
                    external: { type: 'number' },
                  },
                },
                uptime: {
                  type: 'number',
                  description: 'Server uptime in seconds',
                },
              },
            },
            business: {
              type: 'object',
              properties: {
                activeUsers: {
                  type: 'number',
                  description: 'Number of active users',
                },
                coursesCreated: {
                  type: 'number',
                  description: 'Total courses created',
                },
                vybeQubesGenerated: {
                  type: 'number',
                  description: 'Total Vybe Qubes generated',
                },
                errorRate: {
                  type: 'number',
                  description: 'Error rate percentage',
                },
              },
            },
          },
        },
      },
    },
    tags: [
      {
        name: 'Health',
        description: 'System health and monitoring endpoints',
      },
      {
        name: 'Security',
        description: 'Security status and vulnerability scanning',
      },
      {
        name: 'Metrics',
        description: 'Performance and business metrics',
      },
      {
        name: 'Authentication',
        description: 'User authentication and authorization',
      },
      {
        name: 'Courses',
        description: 'Course management and content',
      },
      {
        name: 'Vybe Qubes',
        description: 'AI-powered website generation',
      },
      {
        name: 'Users',
        description: 'User management and profiles',
      },
    ],
  },
  apis: ['./src/routes/api/**/*.ts', './src/lib/api/**/*.ts'],
};
