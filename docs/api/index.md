# API Documentation

VybeCoding.ai provides a comprehensive REST API for integrating with the platform's AI-powered education features.

## Overview

The VybeCoding.ai API is built with modern standards and provides:

- **RESTful Design**: Clean, predictable URLs and HTTP methods
- **JSON Responses**: All responses are in JSON format
- **Authentication**: Secure authentication via Appwrite
- **Rate Limiting**: Fair usage policies to ensure service quality
- **Comprehensive Documentation**: Interactive documentation with examples

## Base URLs

| Environment | Base URL                               |
| ----------- | -------------------------------------- |
| Production  | `https://api.vybecoding.ai/v1`         |
| Staging     | `https://staging-api.vybecoding.ai/v1` |
| Development | `http://localhost:5173/api`            |

## Authentication

The API uses Appwrite authentication. Include your session token in requests:

```bash
curl -H "Authorization: Bearer YOUR_SESSION_TOKEN" \
     https://api.vybecoding.ai/v1/health
```

## Quick Start

### 1. Check System Health

```bash
curl https://api.vybecoding.ai/v1/health
```

### 2. Get System Metrics

```bash
curl https://api.vybecoding.ai/v1/metrics
```

### 3. Generate a Vybe Qube

```bash
curl -X POST https://api.vybecoding.ai/v1/vybe/generate-qube \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"prompt": "Create a landing page for a tech startup"}'
```

## API Endpoints

### System Endpoints

- **GET /health** - System health check
- **GET /metrics** - Performance and business metrics
- **GET /security** - Security status and scanning

### Vybe Qube Endpoints

- **POST /vybe/generate-qube** - Generate a new Vybe Qube
- **GET /vybe/qube-status/{id}** - Check generation status
- **GET /vybe/status** - Vybe system status

### User Management

- **GET /users/profile** - Get user profile
- **PUT /users/profile** - Update user profile
- **GET /users/courses** - Get user's courses

## Interactive Documentation

For hands-on API exploration, use our interactive documentation tools:

- **[Swagger UI](swagger-ui/)** - Test APIs directly in your browser
- **[ReDoc](redoc.html)** - Clean, responsive API documentation
- **[OpenAPI Specification](openapi.yaml)** - Machine-readable API spec

## SDKs and Libraries

### JavaScript/TypeScript

```bash
npm install @vybecoding/api-client
```

```typescript
import { VybeAPI } from '@vybecoding/api-client';

const api = new VybeAPI({
  baseURL: 'https://api.vybecoding.ai/v1',
  token: 'your-session-token',
});

const health = await api.health.check();
```

### Python

```bash
pip install vybecoding-api
```

```python
from vybecoding import VybeAPI

api = VybeAPI(
    base_url='https://api.vybecoding.ai/v1',
    token='your-session-token'
)

health = api.health.check()
```

## Error Handling

The API uses standard HTTP status codes and returns detailed error information:

```json
{
  "error": "Invalid request parameters",
  "code": "VALIDATION_ERROR",
  "timestamp": "2024-01-15T10:30:00Z",
  "details": {
    "field": "prompt",
    "message": "Prompt is required"
  }
}
```

## Rate Limiting

API requests are rate limited to ensure fair usage:

- **Free Tier**: 100 requests per hour
- **Pro Tier**: 1,000 requests per hour
- **Enterprise**: Custom limits

Rate limit headers are included in all responses:

```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: **********
```

## Support

- **Documentation Issues**: [GitHub Issues](https://github.com/Hiram-Ducky/VybeCoding.ai/issues)
- **API Support**: [<EMAIL>](mailto:<EMAIL>)
- **Community**: [Discord Server](https://discord.gg/vybecoding)
