# VybeCoding.ai Documentation Index

**Project:** AI-Powered Education Platform
**Status:** ✅ Production Ready - 100% BMAD Method v3.1 Compliant
**Last Updated:** January 2025

## 📚 **Core Documentation**

### **📋 API Documentation**

- [API Overview](api/) - Complete API reference and documentation
- [OpenAPI Specification](api/openapi.yaml) - Machine-readable API specification
- [Interactive API Explorer](api/swagger-ui/) - Test APIs directly in browser
- [Alternative API Docs](api/redoc.html) - Clean, responsive API documentation
- [TypeScript API Docs](api/typescript/) - Generated TypeScript documentation

### **🧩 Component Library**

- [Component Overview](components/) - Complete component documentation
- [Storybook](storybook/) - Interactive component playground
- [UI Components](components/ui/) - Basic UI building blocks
- [Business Components](components/business/) - Domain-specific components
- [Educational Components](components/educational/) - Learning-focused components

### **📖 Project Overview**

- [project-brief.md](project-brief.md) - Executive summary and project goals
- [project-info.md](project-info.md) - Technical details, quick start guide, and current status
- [project-history.md](project-history.md) - Complete development history and milestones
- [setup-guide.md](setup-guide.md) - Installation and configuration instructions

### **✅ Method Compliance**

- [bmad-compliance.md](bmad-compliance.md) - Official BMAD Method v3.1 compliance verification
- [vybe-compliance.md](vybe-compliance.md) - Vybe Method MAS compliance verification
- [compliance-implementation-summary.md](compliance-implementation-summary.md) - Complete implementation details

### **🏗️ Architecture & Design**

- [verified-components.md](verified-components.md) - Tested and approved components
- [branding-strategy.md](branding-strategy.md) - Visual identity and brand guidelines

### **💼 Business & Strategy**

- [market-analysis.md](market-analysis.md) - Market research and competitive analysis
- [communication-standards.md](communication-standards.md) - Team communication guidelines

### **⚙️ Operations**

- [backup-recovery.md](backup-recovery.md) - Data protection and disaster recovery
- [github-workflow.md](github-workflow.md) - Development workflow and Git practices

### **🤖 AI Integration**

- [ai-integration-policy.md](ai-integration-policy.md) - Guidelines for AI tool usage
- [ai-news-platform.md](ai-news-platform.md) - AI news and updates integration

## 🔗 **Quick Links**

### **📊 Live System Endpoints**

- **Health Check**: [/api/health](/api/health) - System health status
- **Metrics**: [/api/metrics](/api/metrics) - Performance and business metrics
- **Security**: [/api/security](/api/security) - Security status and scanning

### **📚 Documentation Tools**

- **Interactive API Explorer**: [Swagger UI](api/swagger-ui/) - Test APIs directly
- **Alternative API Docs**: [ReDoc](api/redoc.html) - Clean API documentation
- **Component Storybook**: [Storybook](storybook/) - Interactive component playground
- **TypeScript API Docs**: [TypeDoc](api/typescript/) - Generated code documentation

## 🚀 **Quick Start Guides**

### **📖 Documentation Development**

```bash
# Generate all documentation
npm run docs:build

# Start documentation development
npm run docs:storybook     # Component documentation
npm run docs:site          # VitePress documentation site
npm run docs:swagger       # API documentation server

# Deploy documentation
npm run docs:deploy
```

### **BMAD Method v3.1 (Human-AI Collaboration)**

```bash
# Initialize official BMAD system
python3 method/bmad/bmad_commands.py start
python3 method/bmad/bmad_orchestrator.py "*help"

# Sequential workflow: *analyst → *pm → *architect → *design-architect → *po → *sm → *dev
```

### **Vybe Method MAS (Autonomous)**

```bash
# Start autonomous MAS system
python3 method/vybe/start_vybe.py
./scripts/agent-helper.sh status

# Autonomous commands: /vybe collaborate, /vybe consensus, /vybe generate vybe-qube
```

### **Development**

```bash
npm run dev          # Start development server
npm run test         # Run tests
npm run build        # Build for production
./scripts/agent-helper.sh auto  # Create milestone
```

## 📁 **Directory Structure**

```
docs/
├── index.md (this file)
├── project-brief.md
├── project-info.md
├── project-history.md
├── setup-guide.md
├── bmad-compliance.md
├── vybe-compliance.md
├── compliance-implementation-summary.md
├── verified-components.md
├── branding-strategy.md
├── market-analysis.md
├── communication-standards.md
├── backup-recovery.md
├── github-workflow.md
├── ai-integration-policy.md
├── ai-news-platform.md
└── stories/
    └── 1.001.story.md
```

## 🎮 **Method Integration**

### **BMAD Method v3.1 (Official)**

- **Agents:** Wendy (Analyst), Bill (PM), Timmy (Architect), Karen (Design Architect), Jimmy (PO), Fran (SM), Rodney (Frontend Dev), James (Full Stack Dev)
- **Commands:** `*analyst`, `*pm`, `*architect`, `*design-architect`, `*po`, `*sm`, `*dev-frontend`, `*dev-fullstack`
- **Workflow:** Sequential, document-driven, human-guided

### **Vybe Method MAS (Extension)**

- **Agents:** VYBA, QUBERT, CODEX, PIXY, DUCKY, HAPPY, VYBRO
- **Commands:** `/vybe collaborate`, `/vybe consensus`, `/vybe generate vybe-qube`
- **Workflow:** Parallel, autonomous, consensus-driven

## 🏆 **Key Achievements**

- ✅ 100% BMAD Method v3.1 compliance achieved
- ✅ Autonomous Multi-Agent System (MAS) implemented
- ✅ Revenue-generating Vybe Qube system operational
- ✅ Production-ready codebase with comprehensive testing
- ✅ Enterprise-grade security and reliability

## 📝 **Contributing**

When adding new documentation:

1. Follow the established naming conventions
2. Update this index file
3. Include proper cross-references
4. Maintain consistent formatting
5. Add quick start guides for new features

---

**Last Updated:** January 2025
**Maintained by:** VybeCoding.ai Development Team
**Next Phase:** Educational content delivery and advanced MAS features
