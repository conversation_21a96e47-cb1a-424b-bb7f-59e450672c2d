# 🌐 Network Access Guide for VybeCoding.ai

This guide explains how to access your VybeCoding.ai development server from other devices on your network.

## 🚀 Quick Start

All development commands now include `--host` flag for network access:

```bash
# Start dev server with network access
npm run dev

# Or just the dev server
npm run dev:simple

# Preview build with network access
npm run preview
```

## 📱 Access URLs

When you run the dev server, you'll see multiple URLs:

```bash
➜  Local:   http://localhost:5173/        # Your computer only
➜  Network: http://************:5173/     # Your network IP
➜  Network: http://**********:5173/       # Docker network
```

## 🔗 Network Access Options

### **Option 1: Use Your Computer's IP Address**

- **URL Format:** `http://[YOUR_IP]:5173/`
- **Example:** `http://************:5173/`
- **Best For:** Other devices on your WiFi network

### **Option 2: Find Your IP Address**

```bash
# On Linux/Mac
ip addr show | grep "inet " | grep -v 127.0.0.1

# Or use hostname
hostname -I

# On Windows
ipconfig | findstr "IPv4"
```

### **Option 3: Use QR Code (Optional)**

You can generate a QR code for easy mobile access:

```bash
# Install qrencode (optional)
sudo apt install qrencode

# Generate QR code for your network URL
echo "http://$(hostname -I | awk '{print $1}'):5173" | qrencode -t UTF8
```

## 📱 Device Access Examples

### **From Phone/Tablet:**

1. Connect to same WiFi network
2. Open browser
3. Go to: `http://************:5173/` (use your actual IP)

### **From Another Computer:**

1. Connect to same network
2. Open browser
3. Go to: `http://************:5173/` (use your actual IP)

### **From Virtual Machine:**

1. Use bridge network mode
2. Go to: `http://************:5173/` (use your actual IP)

## 🔧 Troubleshooting

### **Can't Access from Other Devices?**

#### **Check Firewall:**

```bash
# Ubuntu/Debian - Allow port 5173
sudo ufw allow 5173

# Or disable firewall temporarily for testing
sudo ufw disable
```

#### **Check Network:**

```bash
# Test if port is accessible
netstat -tlnp | grep 5173

# Test from another device
ping ************  # Use your actual IP
```

#### **Check Vite Configuration:**

The `--host` flag should make Vite bind to `0.0.0.0` instead of `127.0.0.1`.

### **Port Already in Use?**

Vite will automatically try the next available port:

- Default: `5173`
- If busy: `5174`, `5175`, etc.

### **Docker Network Issues?**

If using Docker containers, make sure ports are properly exposed in `docker-compose.yml`.

## 🛡️ Security Notes

### **Development Only:**

- Network access is for development only
- Don't expose development servers to the internet
- Use proper authentication in production

### **Firewall Recommendations:**

```bash
# Allow only local network access
sudo ufw allow from ***********/24 to any port 5173

# Or allow specific IP
sudo ufw allow from ************* to any port 5173
```

## 🎯 Common Use Cases

### **Mobile Testing:**

- Test responsive design on real devices
- Test touch interactions
- Test different screen sizes

### **Cross-Browser Testing:**

- Test on different devices/browsers
- Share with team members
- Demo to stakeholders

### **Development Collaboration:**

- Multiple developers on same network
- Design review sessions
- Real-time collaboration

## 📋 Network Access Checklist

- ✅ Run dev server with `--host` flag
- ✅ Note the Network URL from console output
- ✅ Ensure devices are on same WiFi network
- ✅ Check firewall allows port 5173
- ✅ Test access from another device
- ✅ Bookmark the network URL for easy access

## 🚀 Pro Tips

### **Bookmark Network URL:**

Save `http://[YOUR_IP]:5173/` in your mobile browser for quick access.

### **Use Static IP:**

Consider setting a static IP for your development machine to avoid URL changes.

### **Network Monitoring:**

Use Portainer to monitor network connections and container networking.

### **Multiple Ports:**

- **VybeCoding.ai:** `http://[YOUR_IP]:5173/`
- **Portainer:** `http://[YOUR_IP]:9000/`
- **Preview Build:** `http://[YOUR_IP]:4173/` (when using `npm run preview`)

## 🎉 Example Workflow

```bash
# 1. Start development environment
npm run dev

# 2. Note the Network URL from output
# ➜  Network: http://************:5173/

# 3. Open on mobile device
# Go to: http://************:5173/

# 4. Develop with live reload on all devices!
```

Your VybeCoding.ai site is now accessible across your entire network! 🌐
