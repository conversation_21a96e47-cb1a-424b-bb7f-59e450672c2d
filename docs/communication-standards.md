# Communication Standards

**Project:** VybeCoding.ai Developer Portfolio Platform  
**Purpose:** Communication protocols for BMAD method agents

## Core Communication Protocols

### **Agent Communication Standards**

- **Format**: Structured markdown with clear action items
- **Responses**: Concise, actionable, with next steps
- **Documentation**: All outputs include file paths and context
- **Handoffs**: Clear deliverables between PM → Dev → QA agents

### **Message Types**

1. **Task Assignment** - Clear requirements with acceptance criteria
2. **Status Update** - Progress with blockers/completions
3. **Deliverable Handoff** - Completed work with validation checklist
4. **Issue Escalation** - Problems requiring input from other agents

### **Documentation Requirements**

- Include file paths in all code references
- Link to relevant project documents
- Specify validation criteria for deliverables
- Maintain context for agent workflow continuity
