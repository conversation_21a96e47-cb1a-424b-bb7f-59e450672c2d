# VybeCoding.ai Docker Compose - Development Environment
version: '3.8'

services:
  # VS Code Dev Container Service
  vybecoding-dev:
    build:
      context: .
      dockerfile: .devcontainer/Dockerfile
    ports:
      - '5175:5173' # Dev container uses external port 5175
      - '3000:3000'
    environment:
      - NODE_ENV=development
      - VITE_ENVIRONMENT=development
      - VITE_HOST=0.0.0.0
      - VITE_PORT=5173
      - VITE_APPWRITE_ENDPOINT=${VITE_APPWRITE_ENDPOINT:-http://localhost:8080/v1}
      - VITE_APPWRITE_PROJECT_ID=${VITE_APPWRITE_PROJECT_ID:-development}
      - VITE_APPWRITE_DATABASE_ID=${VITE_APPWRITE_DATABASE_ID:-development}
      - VITE_ENABLE_DEBUG_MODE=true
      - VITE_ENABLE_HOT_RELOAD=true
    volumes:
      # Mount source code for hot reload
      - .:/app
      - /app/node_modules
      - ./logs:/app/logs
      # Mount Docker socket for Docker-in-Docker
      - /var/run/docker.sock:/var/run/docker.sock
      # VS Code server extensions
      - vscode-server-extensions:/home/<USER>/.vscode-server/extensions
      - vscode-server-insiders:/home/<USER>/.vscode-server-insiders
    restart: unless-stopped
    networks:
      - vybecoding_dev_network
    stdin_open: true
    tty: true
    # Enable Docker-in-Docker capabilities
    privileged: true

  # Standard Development Service (for non-VS Code usage)
  vybecoding-dev-standard:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - '5174:5173'
    environment:
      - NODE_ENV=development
      - VITE_ENVIRONMENT=development
      - VITE_HOST=0.0.0.0
      - VITE_PORT=5173
      - VITE_APPWRITE_ENDPOINT=${VITE_APPWRITE_ENDPOINT:-http://localhost:8080/v1}
      - VITE_APPWRITE_PROJECT_ID=${VITE_APPWRITE_PROJECT_ID:-development}
      - VITE_APPWRITE_DATABASE_ID=${VITE_APPWRITE_DATABASE_ID:-development}
      - VITE_ENABLE_DEBUG_MODE=true
      - VITE_ENABLE_HOT_RELOAD=true
    volumes:
      # Mount source code for hot reload
      - .:/app
      - /app/node_modules
      - ./logs:/app/logs
    restart: unless-stopped
    networks:
      - vybecoding_dev_network
    stdin_open: true
    tty: true
    profiles:
      - standard-dev

  # Development Redis
  redis-dev:
    image: redis:7-alpine
    ports:
      - '6379:6379'
    volumes:
      - redis_dev_data:/data
    restart: unless-stopped
    networks:
      - vybecoding_dev_network
    command: redis-server --appendonly yes

  # Local Appwrite instance for development
  appwrite-dev:
    image: appwrite/appwrite:1.4
    ports:
      - '8080:80'
      - '8443:443'
    environment:
      - _APP_ENV=development
      - _APP_WORKER_PER_CORE=6
      - _APP_LOCALE=en
      - _APP_CONSOLE_WHITELIST_ROOT=enabled
      - _APP_CONSOLE_WHITELIST_EMAILS=
      - _APP_CONSOLE_WHITELIST_IPS=
      - _APP_SYSTEM_EMAIL_NAME=VybeCoding.ai
      - _APP_SYSTEM_EMAIL_ADDRESS=<EMAIL>
      - _APP_SYSTEM_SECURITY_EMAIL_ADDRESS=<EMAIL>
      - _APP_SYSTEM_RESPONSE_FORMAT=
      - _APP_OPTIONS_ABUSE=enabled
      - _APP_OPTIONS_FORCE_HTTPS=disabled
      - _APP_OPENSSL_KEY_V1=your-secret-key
      - _APP_DOMAIN=localhost
      - _APP_DOMAIN_TARGET=localhost
      - _APP_REDIS_HOST=redis-dev
      - _APP_REDIS_PORT=6379
      - _APP_DB_HOST=mariadb-dev
      - _APP_DB_PORT=3306
      - _APP_DB_SCHEMA=appwrite
      - _APP_DB_USER=user
      - _APP_DB_PASS=password
      - _APP_INFLUXDB_HOST=influxdb-dev
      - _APP_INFLUXDB_PORT=8086
      - _APP_STATSD_HOST=telegraf-dev
      - _APP_STATSD_PORT=8125
    volumes:
      - appwrite_dev_data:/storage
    depends_on:
      - mariadb-dev
      - redis-dev
    restart: unless-stopped
    networks:
      - vybecoding_dev_network
    profiles:
      - with-appwrite

  # MariaDB for Appwrite
  mariadb-dev:
    image: mariadb:10.7
    environment:
      - MYSQL_ROOT_PASSWORD=rootpassword
      - MYSQL_DATABASE=appwrite
      - MYSQL_USER=user
      - MYSQL_PASSWORD=password
    volumes:
      - mariadb_dev_data:/var/lib/mysql
    restart: unless-stopped
    networks:
      - vybecoding_dev_network
    profiles:
      - with-appwrite

  # InfluxDB for Appwrite metrics
  influxdb-dev:
    image: appwrite/influxdb:1.5.0
    environment:
      - INFLUXDB_HTTP_ENABLED=true
    volumes:
      - influxdb_dev_data:/var/lib/influxdb
    restart: unless-stopped
    networks:
      - vybecoding_dev_network
    profiles:
      - with-appwrite

  # Telegraf for Appwrite metrics
  telegraf-dev:
    image: appwrite/telegraf:1.4.0
    restart: unless-stopped
    networks:
      - vybecoding_dev_network
    profiles:
      - with-appwrite

  # Portainer - Docker Management UI
  portainer:
    image: portainer/portainer-ce:latest
    container_name: vybecoding-portainer
    ports:
      - '9000:9000'
      - '9443:9443'
    volumes:
      # Portainer data persistence
      - portainer_data:/data
      # Docker socket for container management
      - /var/run/docker.sock:/var/run/docker.sock
    restart: unless-stopped
    networks:
      - vybecoding_dev_network
    labels:
      - 'traefik.enable=false'
      - 'com.vybecoding.service=portainer'
      - 'com.vybecoding.description=Docker Management UI'

volumes:
  redis_dev_data:
    driver: local
  appwrite_dev_data:
    driver: local
  mariadb_dev_data:
    driver: local
  influxdb_dev_data:
    driver: local
  # Portainer data persistence
  portainer_data:
    driver: local
  # VS Code server volumes for dev container
  vscode-server-extensions:
    driver: local
  vscode-server-insiders:
    driver: local

networks:
  vybecoding_dev_network:
    driver: bridge
