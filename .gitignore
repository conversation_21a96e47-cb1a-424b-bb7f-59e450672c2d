# Dependencies
node_modules/
.venv/
venv/
env/
.env.local
.env.development.local
.env.test.local
.env.production.local

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# IDE
.vscode/settings.json.bak
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Build outputs
.svelte-kit/
build/
dist/
.output/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
.nyc_output/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Large files and binary assets
*.zip
*.tar.gz
*.rar
*.7z
*.dmg
*.iso
*.img

# Machine Learning Models (if any)
*.pkl
*.model
*.h5
*.pb

# Temporary files
*.tmp
*.temp
.temp/
.tmp/

# Backup files
*.bak
*.backup
*.orig

# Additional Python cache patterns
**/__pycache__/
*.pyc
*.pyo
*.pyd

# Additional log patterns
**/*.log
logs/**/*.log

# Local development certificates
*.pem
*.key
*.crt

# Large test files
test-data/
fixtures/large/
.local/bin/ollama
