# Multi-Environment Management Configuration

# This file defines environment-specific configurations and management strategies

## Environment Structure

### 🌱 Development (local)

- **Purpose**: Local development and testing
- **URL**: http://localhost:5173
- **Database**: Local SQLite/development DB
- **Features**: Hot reload, debug mode, development tools
- **Access**: Developer only

### 🧪 Staging

- **Purpose**: Pre-production testing and validation
- **URL**: https://vybecoding-staging.vercel.app
- **Database**: Staging database (subset of production data)
- **Features**: Production-like environment with test data
- **Access**: Internal team, stakeholders for review

### 🚀 Production

- **Purpose**: Live application serving real users
- **URL**: https://vybecoding.app
- **Database**: Production database
- **Features**: Optimized build, monitoring, analytics
- **Access**: Public users

## Environment Variables Management

### Common Variables (All Environments)

```bash
# Application Configuration
VITE_APP_NAME="VybeCoding"
VITE_APP_VERSION="1.0.0"

# Feature Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_DEBUG_MODE=false
VITE_ENABLE_PERFORMANCE_MONITORING=true
```

### Development Environment (.env.local)

```bash
VITE_APP_ENV=development
VITE_API_URL=http://localhost:3000/api
VITE_ENABLE_DEBUG_MODE=true
VITE_ENABLE_HOT_RELOAD=true
VITE_LOG_LEVEL=debug
```

### Staging Environment (.env.staging)

```bash
VITE_APP_ENV=staging
VITE_API_URL=https://api-staging.vybecoding.app
VITE_APPWRITE_ENDPOINT=https://staging-appwrite.vybecoding.app/v1
VITE_APPWRITE_PROJECT=vybecoding-staging
VITE_ENABLE_DEBUG_MODE=false
VITE_LOG_LEVEL=info
```

### Production Environment (.env.production)

```bash
VITE_APP_ENV=production
VITE_API_URL=https://api.vybecoding.app
VITE_APPWRITE_ENDPOINT=https://appwrite.vybecoding.app/v1
VITE_APPWRITE_PROJECT=vybecoding-prod
VITE_ENABLE_DEBUG_MODE=false
VITE_LOG_LEVEL=error
```

## Deployment Strategy

### Branch → Environment Mapping

- `main` → Production (requires manual approval)
- `develop` → Staging (automatic)
- `feature/*` → Development (local testing)

### Deployment Gates

1. **Development to Staging**: Automated on merge to develop
2. **Staging to Production**: Manual approval required
3. **Hotfixes**: Can go directly to production with approval

## Environment-Specific Features

### Feature Flags by Environment

```typescript
// src/lib/config/features.ts
export const features = {
  development: {
    debugPanel: true,
    mockData: true,
    testingUtils: true,
    analytics: false,
  },
  staging: {
    debugPanel: false,
    mockData: true,
    testingUtils: true,
    analytics: true,
  },
  production: {
    debugPanel: false,
    mockData: false,
    testingUtils: false,
    analytics: true,
  },
};
```

### Database Configuration

- **Development**: Local SQLite with seed data
- **Staging**: PostgreSQL with anonymized production subset
- **Production**: PostgreSQL with full dataset and backups

### Monitoring & Logging

- **Development**: Console logging only
- **Staging**: File logging + basic monitoring
- **Production**: Full monitoring stack (Sentry, Analytics, Performance)

## Security Configuration

### Environment Access Control

```yaml
# GitHub Environments Settings
staging:
  protection_rules:
    - type: required_reviewers
      users: ['@VybeCoding']
  environment_variables:
    - STAGING_API_KEY
    - STAGING_DATABASE_URL

production:
  protection_rules:
    - type: required_reviewers
      users: ['@VybeCoding']
    - type: wait_timer
      minutes: 5
  environment_variables:
    - PRODUCTION_API_KEY
    - PRODUCTION_DATABASE_URL
    - ANALYTICS_KEY
```

### Secret Management

- Development: `.env.local` (gitignored)
- Staging: GitHub Environment Secrets
- Production: GitHub Environment Secrets + additional security

## Maintenance & Updates

### Environment Sync Strategy

1. **Weekly**: Staging data refresh from production
2. **Monthly**: Dependency updates across all environments
3. **Quarterly**: Security audit and environment review

### Rollback Procedures

- **Staging**: Git revert + redeploy
- **Production**: Blue-green deployment with instant rollback

### Health Checks

- **Development**: Basic functionality tests
- **Staging**: Full integration test suite
- **Production**: Comprehensive monitoring + alerts

## Migration Procedures

### Adding New Environment

1. Create environment-specific configuration files
2. Set up GitHub Environment with protection rules
3. Configure deployment pipeline
4. Add monitoring and alerting
5. Document access procedures

### Environment Promotion Process

1. Code changes deployed to development
2. Automated testing and validation
3. Promotion to staging with stakeholder review
4. Manual approval for production deployment
5. Post-deployment monitoring and validation
