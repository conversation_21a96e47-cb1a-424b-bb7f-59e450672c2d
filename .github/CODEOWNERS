# Code Owners Configuration
# This file defines who must review changes to specific parts of the codebase
# GitHub will automatically request reviews from code owners for relevant files

# Global owners (fallback for any file not specifically covered)
* @VybeCoding

# Core application code
/src/ @VybeCoding
/app.* @VybeCoding
/vite.config.* @VybeCoding
/svelte.config.js @VybeCoding

# Configuration files
/package.json @VybeCoding
/tsconfig.json @VybeCoding
/tailwind.config.js @VybeCoding
/postcss.config.js @VybeCoding
/eslint.config.js @VybeCoding
/prettier.config.js @VybeCoding
/vitest.config.ts @VybeCoding

# Environment and deployment
/.env* @VybeCoding
/Dockerfile* @VybeCoding
/docker-compose*.yml @VybeCoding
/appwrite.json @VybeCoding

# CI/CD and automation
/.github/ @VybeCoding
/.vscode/ @VybeCoding
/scripts/ @VybeCoding

# Documentation (require review for major changes)
/README.md @VybeCoding
/docs/ @VybeCoding
/DEPLOYMENT-STATUS.md @VybeCoding
/QUICK-SETUP.md @VybeCoding

# Python components
/requirements.txt @VybeCoding
/vybe-agent/ @VybeCoding
*.py @VybeCoding

# Security-sensitive files
/.gitignore @VybeCoding
/check-env.py @VybeCoding

# Test files (ensure test quality)
/tests/ @VybeCoding
/test_*.py @VybeCoding
*.test.* @VybeCoding
*.spec.* @VybeCoding
