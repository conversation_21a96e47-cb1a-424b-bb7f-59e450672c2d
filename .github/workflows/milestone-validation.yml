name: Milestone Validation

on:
  workflow_dispatch:  # Only run manually until tests are fixed
  # push:
  #   branches: [main, 'milestone-*']
  # pull_request:
  #   branches: [main]

jobs:
  validate:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: 'src/package-lock.json'

      - name: Install dependencies
        run: |
          cd src
          npm ci

      - name: Run tests
        run: |
          cd src
          npm test -- --run

      - name: Type check
        run: |
          cd src
          npx tsc --noEmit

      - name: Build check
        run: |
          cd src
          npm run build

      - name: Validate milestone
        if: startsWith(github.ref, 'refs/heads/milestone-')
        run: |
          echo "🎯 Validating milestone branch: ${{ github.ref_name }}"
          if [ -f ".milestones/$(echo ${{ github.ref_name }} | sed 's/milestone-/milestone-/' | cut -d'-' -f1-2).md" ]; then
            echo "✅ Milestone documentation found"
          else
            echo "❌ Milestone documentation missing"
            exit 1
          fi
