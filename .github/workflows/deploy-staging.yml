name: Deploy to Staging

on:
  push:
    branches: [develop]
  pull_request:
    branches: [develop]

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run tests
        run: npm test -- --run

      - name: Run type check
        run: npm run check

      - name: Run build test
        run: npm run build

  deploy-staging:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop' && github.event_name == 'push'

    environment:
      name: staging
      url: https://vybecoding-staging.vercel.app

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build application
        run: npm run build
        env:
          VITE_APPWRITE_ENDPOINT: ${{ secrets.STAGING_APPWRITE_ENDPOINT }}
          VITE_APPWRITE_PROJECT_ID: ${{ secrets.STAGING_APPWRITE_PROJECT_ID }}
          VITE_APPWRITE_DATABASE_ID: ${{ secrets.STAGING_APPWRITE_DATABASE_ID }}
          VITE_ENVIRONMENT: staging

      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_STAGING_PROJECT_ID }}
          working-directory: ./
          scope: ${{ secrets.VERCEL_ORG_ID }}

      - name: Run post-deployment health checks
        run: |
          echo "🏥 Running health checks..."
          sleep 30  # Wait for deployment to stabilize

          # Check if staging site is accessible
          response=$(curl -s -o /dev/null -w "%{http_code}" https://vybecoding-staging.vercel.app)
          if [ $response -eq 200 ]; then
            echo "✅ Staging site is accessible"
          else
            echo "❌ Staging site health check failed (HTTP $response)"
            exit 1
          fi

          # Check if API endpoints are working
          api_response=$(curl -s -o /dev/null -w "%{http_code}" https://vybecoding-staging.vercel.app/api/health)
          if [ $api_response -eq 200 ]; then
            echo "✅ API health check passed"
          else
            echo "⚠️ API health check returned HTTP $api_response"
          fi

      - name: Notify deployment success
        if: success()
        run: |
          echo "🚀 Staging deployment successful!"
          echo "📍 URL: https://vybecoding-staging.vercel.app"
          echo "🔍 Commit: ${{ github.sha }}"
          echo "👤 Author: ${{ github.actor }}"

      - name: Notify deployment failure
        if: failure()
        run: |
          echo "❌ Staging deployment failed!"
          echo "🔍 Commit: ${{ github.sha }}"
          echo "👤 Author: ${{ github.actor }}"
          echo "📋 Check logs for details"

  security-scan:
    needs: test
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Run security audit
        run: npm audit --audit-level=high

      - name: Check for vulnerabilities
        run: |
          echo "🔒 Running security checks..."
          # Check for common security issues
          if grep -r "console.log" src/ --include="*.ts" --include="*.js" --include="*.svelte"; then
            echo "⚠️ Found console.log statements - remove for production"
          fi

          # Check for hardcoded secrets (basic check)
          if grep -r -i "password\|secret\|key" src/ --include="*.ts" --include="*.js" --include="*.svelte" | grep -v "// " | grep -v "/*"; then
            echo "⚠️ Potential hardcoded secrets found - review carefully"
          fi

          echo "✅ Basic security scan completed"
