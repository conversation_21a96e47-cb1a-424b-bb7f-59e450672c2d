name: Documentation

on:
  push:
    branches: [main]
    paths:
      - 'src/**'
      - 'docs/**'
      - '.storybook/**'
      - 'package.json'
      - 'scripts/generate-docs.sh'
      - 'scripts/deploy-docs.sh'
  pull_request:
    branches: [main]
    paths:
      - 'src/**'
      - 'docs/**'
      - '.storybook/**'
  workflow_dispatch:

permissions:
  contents: read
  pages: write
  id-token: write

concurrency:
  group: 'pages'
  cancel-in-progress: false

jobs:
  build-docs:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Generate documentation
        run: |
          chmod +x scripts/generate-docs.sh
          ./scripts/generate-docs.sh

      - name: Build VitePress site
        run: npm run docs:site:build

      - name: Validate documentation
        run: |
          npm run docs:validate-all || echo "Validation warnings found"

      - name: Upload documentation artifacts
        uses: actions/upload-artifact@v4
        with:
          name: documentation
          path: |
            docs/
            !docs/node_modules/
          retention-days: 30

      - name: Setup Pages
        if: github.ref == 'refs/heads/main'
        uses: actions/configure-pages@v4

      - name: Upload to GitHub Pages
        if: github.ref == 'refs/heads/main'
        uses: actions/upload-pages-artifact@v3
        with:
          path: docs/.vitepress/dist

  deploy-docs:
    if: github.ref == 'refs/heads/main'
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    needs: build-docs

    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4

  test-docs:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Test documentation links
        run: |
          npm run docs:test-links || echo "Link check completed with warnings"

      - name: Test API documentation
        run: |
          npm run docs:validate || echo "API validation completed with warnings"

      - name: Test Storybook build
        run: |
          npm run docs:storybook:build

      - name: Accessibility test
        run: |
          # Install axe-core for accessibility testing
          npm install -g @axe-core/cli

          # Start a simple server for testing
          npx http-server docs -p 8080 &
          sleep 5

          # Run accessibility tests
          axe http://localhost:8080 --exit || echo "Accessibility tests completed with warnings"
