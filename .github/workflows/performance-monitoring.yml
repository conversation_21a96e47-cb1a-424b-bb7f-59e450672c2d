name: 📊 Performance Monitoring

on:
  workflow_dispatch:  # Only run manually until tests are fixed
    inputs:
      environment:
        description: 'Environment to monitor'
        required: true
        default: 'production'
        type: choice
        options:
          - production
          - staging
  # push:
  #   branches: [main]
  # schedule:
  #   # Run performance monitoring daily at 2 AM UTC
  #   - cron: '0 2 * * *'

env:
  NODE_VERSION: '20'

jobs:
  lighthouse-audit:
    name: 🔍 Lighthouse Performance Audit
    runs-on: ubuntu-latest
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🏗️ Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: 📦 Install Lighthouse CI
        run: npm install -g @lhci/cli@0.12.x

      - name: 🔍 Run Lighthouse CI
        run: |
          lhci autorun
        env:
          LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}
          LHCI_TARGET_URL: ${{ github.event.inputs.environment == 'staging' && secrets.STAGING_URL || secrets.PRODUCTION_URL }}

      - name: 📊 Upload Lighthouse results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: lighthouse-results
          path: |
            .lighthouseci/
            lighthouse-results.json

  performance-budget:
    name: 📈 Performance Budget Check
    runs-on: ubuntu-latest
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🏗️ Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🏗️ Build application
        run: npm run build

      - name: 📊 Analyze bundle size
        run: |
          npx bundlesize

      - name: 📈 Bundle analyzer
        run: |
          npm run analyze
        continue-on-error: true

  uptime-monitoring:
    name: 🌐 Uptime & Health Check
    runs-on: ubuntu-latest
    strategy:
      matrix:
        environment: [production, staging]
    steps:
      - name: 🏥 Health check
        run: |
          HEALTH_URL="${{ matrix.environment == 'production' && secrets.PRODUCTION_URL || secrets.STAGING_URL }}/health"

          echo "Checking health endpoint: $HEALTH_URL"

          RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "$HEALTH_URL" || echo "000")

          if [ "$RESPONSE" -eq "200" ]; then
            echo "✅ ${{ matrix.environment }} is healthy (HTTP $RESPONSE)"
          else
            echo "❌ ${{ matrix.environment }} health check failed (HTTP $RESPONSE)"
            exit 1
          fi

      - name: 🔍 Response time check
        run: |
          URL="${{ matrix.environment == 'production' && secrets.PRODUCTION_URL || secrets.STAGING_URL }}"

          echo "Measuring response time for: $URL"

          RESPONSE_TIME=$(curl -s -o /dev/null -w "%{time_total}" "$URL")

          echo "Response time: ${RESPONSE_TIME}s"

          # Alert if response time > 3 seconds
          if (( $(echo "$RESPONSE_TIME > 3.0" | bc -l) )); then
            echo "⚠️ Slow response time detected: ${RESPONSE_TIME}s"
            echo "slow-response=true" >> $GITHUB_OUTPUT
          else
            echo "✅ Response time within acceptable range: ${RESPONSE_TIME}s"
          fi

  security-headers:
    name: 🔒 Security Headers Check
    runs-on: ubuntu-latest
    strategy:
      matrix:
        environment: [production, staging]
    steps:
      - name: 🔍 Check security headers
        run: |
          URL="${{ matrix.environment == 'production' && secrets.PRODUCTION_URL || secrets.STAGING_URL }}"

          echo "Checking security headers for: $URL"

          HEADERS=$(curl -s -I "$URL")

          echo "## Security Headers Report for ${{ matrix.environment }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          # Check for important security headers
          if echo "$HEADERS" | grep -qi "x-frame-options"; then
            echo "✅ X-Frame-Options header present" >> $GITHUB_STEP_SUMMARY
          else
            echo "❌ X-Frame-Options header missing" >> $GITHUB_STEP_SUMMARY
          fi

          if echo "$HEADERS" | grep -qi "x-content-type-options"; then
            echo "✅ X-Content-Type-Options header present" >> $GITHUB_STEP_SUMMARY
          else
            echo "❌ X-Content-Type-Options header missing" >> $GITHUB_STEP_SUMMARY
          fi

          if echo "$HEADERS" | grep -qi "strict-transport-security"; then
            echo "✅ Strict-Transport-Security header present" >> $GITHUB_STEP_SUMMARY
          else
            echo "❌ Strict-Transport-Security header missing" >> $GITHUB_STEP_SUMMARY
          fi

  create-report:
    name: 📋 Create Performance Report
    needs: [lighthouse-audit, performance-budget, uptime-monitoring, security-headers]
    runs-on: ubuntu-latest
    if: always()
    steps:
      - name: 📋 Generate performance summary
        run: |
          echo "## 📊 Performance Monitoring Report" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "- **Date**: $(date -u)" >> $GITHUB_STEP_SUMMARY
          echo "- **Trigger**: ${{ github.event_name }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Environment**: ${{ github.event.inputs.environment || 'production' }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Job Results:" >> $GITHUB_STEP_SUMMARY
          echo "- Lighthouse Audit: ${{ needs.lighthouse-audit.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- Performance Budget: ${{ needs.performance-budget.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- Uptime Monitoring: ${{ needs.uptime-monitoring.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- Security Headers: ${{ needs.security-headers.result }}" >> $GITHUB_STEP_SUMMARY
