name: Documentation Generation and Deployment

on:
  push:
    branches: [main]
    paths:
      - 'src/routes/api/**/*.ts'
      - 'src/lib/**/*.ts'
      - 'src/lib/components/**/*.svelte'
      - 'docs/**/*.md'
      - 'docs/api/swagger-config.js'
      - '.github/workflows/documentation.yml'
  pull_request:
    branches: [main]
    paths:
      - 'src/routes/api/**/*.ts'
      - 'src/lib/**/*.ts'
      - 'src/lib/components/**/*.svelte'
      - 'docs/**/*.md'
  workflow_dispatch:
    inputs:
      deploy_target:
        description: 'Deployment target'
        required: false
        default: 'github-pages'
        type: choice
        options:
          - github-pages
          - custom

permissions:
  contents: read
  pages: write
  id-token: write

concurrency:
  group: 'pages'
  cancel-in-progress: false

jobs:
  generate-docs:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Install documentation dependencies
        run: |
          npm install --save-dev swagger-jsdoc@^6.2.8
          npm install --save-dev typedoc@^0.26.11
          npm install --save-dev sveld@^0.19.1
          npm install --save-dev markdown-link-check@^3.12.2

      - name: Generate API documentation
        run: |
          mkdir -p docs/api
          npx swagger-jsdoc -d docs/api/swagger-config.js -o docs/api/openapi.yaml "src/routes/api/**/*.ts"

      - name: Generate TypeScript documentation
        run: |
          npx typedoc --out docs/api/typescript src/lib

      - name: Generate component documentation
        run: |
          mkdir -p docs/components
          npx sveld --glob "src/lib/components/**/*.svelte" --output docs/components

      - name: Generate Postman collection
        run: |
          if command -v openapi-to-postman &> /dev/null; then
            npx openapi-to-postman -s docs/api/openapi.yaml -o docs/api/postman-collection.json
          else
            echo "openapi-to-postman not available, skipping"
          fi

      - name: Validate OpenAPI specification
        run: |
          if command -v swagger-codegen &> /dev/null; then
            swagger-codegen validate -i docs/api/openapi.yaml
          else
            echo "swagger-codegen not available, skipping validation"
          fi

      - name: Check for broken links
        run: |
          npx markdown-link-check docs/**/*.md || true

      - name: Generate documentation summary
        run: |
          cat > docs/documentation-summary.md << EOF
          # VybeCoding.ai Documentation Summary

          Generated on: $(date)
          Commit: ${{ github.sha }}
          Branch: ${{ github.ref_name }}

          ## 📚 Available Documentation

          ### API Documentation
          - **OpenAPI Specification**: [openapi.yaml](api/openapi.yaml)
          - **TypeScript API Docs**: [typescript/](api/typescript/)
          - **Postman Collection**: [postman-collection.json](api/postman-collection.json)

          ### Component Documentation
          - **Svelte Components**: [components/](components/)

          ### Project Documentation
          - **Project Overview**: [project-info.md](project-info.md)
          - **Setup Guide**: [setup-guide.md](setup-guide.md)
          - **Architecture**: [architecture.md](architecture.md)
          - **Backup & Recovery**: [backup-recovery.md](backup-recovery.md)

          ## 🔗 Quick Links

          - **API Health Check**: \`GET /api/health\`
          - **System Metrics**: \`GET /api/metrics\`
          - **Security Status**: \`GET /api/security\`

          ## 📊 Documentation Statistics

          - **Total Markdown Files**: $(find docs -name "*.md" | wc -l)
          - **API Endpoints Documented**: $(grep -r "@swagger" src/routes/api/ | wc -l)
          - **Components Documented**: $(find src/lib/components -name "*.svelte" | wc -l)
          EOF

      - name: Build documentation site
        run: |
          mkdir -p docs/dist
          cp -r docs/*.md docs/dist/ 2>/dev/null || true
          cp -r docs/api docs/dist/ 2>/dev/null || true
          cp -r docs/components docs/dist/ 2>/dev/null || true

          # Create index.html
          cat > docs/dist/index.html << 'EOF'
          <!DOCTYPE html>
          <html lang="en">
          <head>
              <meta charset="UTF-8">
              <meta name="viewport" content="width=device-width, initial-scale=1.0">
              <title>VybeCoding.ai Documentation</title>
              <style>
                  body {
                      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                      line-height: 1.6;
                      color: #333;
                      max-width: 1200px;
                      margin: 0 auto;
                      padding: 20px;
                      background: #f8f9fa;
                  }
                  .header {
                      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                      color: white;
                      padding: 2rem;
                      border-radius: 10px;
                      margin-bottom: 2rem;
                      text-align: center;
                  }
                  .grid {
                      display: grid;
                      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                      gap: 1.5rem;
                      margin-bottom: 2rem;
                  }
                  .card {
                      background: white;
                      padding: 1.5rem;
                      border-radius: 8px;
                      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                      transition: transform 0.2s;
                  }
                  .card:hover {
                      transform: translateY(-2px);
                  }
                  .card h3 {
                      margin-top: 0;
                      color: #667eea;
                  }
                  .card a {
                      color: #667eea;
                      text-decoration: none;
                      font-weight: 500;
                  }
                  .card a:hover {
                      text-decoration: underline;
                  }
                  .footer {
                      text-align: center;
                      margin-top: 3rem;
                      padding: 1rem;
                      color: #666;
                      border-top: 1px solid #eee;
                  }
              </style>
          </head>
          <body>
              <div class="header">
                  <h1>🚀 VybeCoding.ai Documentation</h1>
                  <p>AI-powered education platform documentation and API reference</p>
              </div>
              
              <div class="grid">
                  <div class="card">
                      <h3>📚 API Documentation</h3>
                      <p>Interactive API documentation with examples and testing capabilities.</p>
                      <a href="api/openapi.yaml">OpenAPI Specification</a><br>
                      <a href="api/typescript/index.html">TypeScript API Docs</a><br>
                      <a href="api/postman-collection.json">Postman Collection</a>
                  </div>
                  
                  <div class="card">
                      <h3>🧩 Component Library</h3>
                      <p>Svelte component documentation with props and usage examples.</p>
                      <a href="components/index.html">Component Documentation</a>
                  </div>
                  
                  <div class="card">
                      <h3>🏗️ Project Information</h3>
                      <p>Project overview, architecture, and development guides.</p>
                      <a href="project-info.md">Project Overview</a><br>
                      <a href="setup-guide.md">Setup Guide</a><br>
                      <a href="architecture.md">Architecture</a>
                  </div>
                  
                  <div class="card">
                      <h3>🔒 Security & Operations</h3>
                      <p>Security policies, backup procedures, and operational guides.</p>
                      <a href="backup-recovery.md">Backup & Recovery</a><br>
                      <a href="github-workflow.md">Development Workflow</a>
                  </div>
                  
                  <div class="card">
                      <h3>🤖 AI Integration</h3>
                      <p>AI integration policies and multi-agent system documentation.</p>
                      <a href="ai-integration-policy.md">AI Integration Policy</a><br>
                      <a href="bmad-compliance.md">BMAD Method Compliance</a><br>
                      <a href="vybe-compliance.md">Vybe Method Compliance</a>
                  </div>
                  
                  <div class="card">
                      <h3>📊 Quick Links</h3>
                      <p>Direct access to live system endpoints and monitoring.</p>
                      <a href="/api/health" target="_blank">Health Check</a><br>
                      <a href="/api/metrics" target="_blank">System Metrics</a><br>
                      <a href="/api/security" target="_blank">Security Status</a>
                  </div>
              </div>
              
              <div class="footer">
                  <p>Generated on: <span id="timestamp"></span></p>
                  <p>Commit: ${{ github.sha }}</p>
                  <p>VybeCoding.ai - AI-powered education platform</p>
              </div>
              
              <script>
                  document.getElementById('timestamp').textContent = new Date().toLocaleString();
              </script>
          </body>
          </html>
          EOF

      - name: Upload documentation artifacts
        uses: actions/upload-artifact@v3
        with:
          name: documentation
          path: |
            docs/dist/
            docs/api/openapi.yaml
            docs/api/postman-collection.json

      - name: Setup Pages
        if: github.ref == 'refs/heads/main'
        uses: actions/configure-pages@v3

      - name: Upload to GitHub Pages
        if: github.ref == 'refs/heads/main'
        uses: actions/upload-pages-artifact@v2
        with:
          path: docs/dist

  deploy:
    if: github.ref == 'refs/heads/main'
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    needs: generate-docs

    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v2

  validate-docs:
    runs-on: ubuntu-latest
    needs: generate-docs

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Download documentation artifacts
        uses: actions/download-artifact@v3
        with:
          name: documentation
          path: docs/dist

      - name: Validate documentation quality
        run: |
          echo "Validating documentation quality..."

          # Check if required files exist
          required_files=(
            "docs/dist/index.html"
            "docs/dist/api/openapi.yaml"
            "docs/dist/documentation-summary.md"
          )

          for file in "${required_files[@]}"; do
            if [ ! -f "$file" ]; then
              echo "❌ Required file missing: $file"
              exit 1
            else
              echo "✅ Found: $file"
            fi
          done

          echo "📊 Documentation validation completed successfully!"
