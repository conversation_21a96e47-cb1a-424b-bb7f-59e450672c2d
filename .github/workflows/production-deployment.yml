name: Production Deployment

on:
  release:
    types: [published]
  workflow_dispatch:
    inputs:
      version:
        description: 'Version to deploy (e.g., v1.0.0)'
        required: true
        type: string
      skip_tests:
        description: 'Skip tests (emergency deployment only)'
        required: false
        default: false
        type: boolean

env:
  NODE_VERSION: '20'
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PRODUCTION_PROJECT_ID }}

jobs:
  pre-deployment-validation:
    name: Pre-deployment Validation
    runs-on: ubuntu-latest
    if: github.event.inputs.skip_tests != 'true'
    outputs:
      validation-passed: ${{ steps.validation-result.outputs.passed }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.version || github.ref }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'src/package-lock.json'

      - name: Install dependencies
        run: |
          cd src
          npm ci

      - name: Run comprehensive test suite
        id: comprehensive-tests
        run: |
          cd src
          npm test -- --run --coverage --reporter=verbose

      - name: Type checking
        id: type-check
        run: |
          cd src
          npx tsc --noEmit

      - name: Production build verification
        id: build-check
        run: |
          cd src
          NODE_ENV=production npm run build

      - name: Security audit
        id: security-audit
        run: |
          cd src
          npm audit --audit-level=moderate

      - name: Set validation result
        id: validation-result
        run: |
          if [[ "${{ steps.comprehensive-tests.outcome }}" == "success" && 
                "${{ steps.type-check.outcome }}" == "success" && 
                "${{ steps.build-check.outcome }}" == "success" && 
                "${{ steps.security-audit.outcome }}" == "success" ]]; then
            echo "passed=true" >> $GITHUB_OUTPUT
          else
            echo "passed=false" >> $GITHUB_OUTPUT
          fi

  backup-current-production:
    name: Backup Current Production
    runs-on: ubuntu-latest
    needs: pre-deployment-validation
    if: always() && (needs.pre-deployment-validation.outputs.validation-passed == 'true' || github.event.inputs.skip_tests == 'true')

    steps:
      - name: Create production backup
        run: |
          echo "🔄 Creating production backup..."
          # This would typically backup the current production state
          # For now, we'll create a backup reference
          echo "Production backup created at $(date)"
          echo "Backup ID: backup-$(date +%Y%m%d-%H%M%S)" >> $GITHUB_STEP_SUMMARY

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [pre-deployment-validation, backup-current-production]
    if: always() && (needs.pre-deployment-validation.outputs.validation-passed == 'true' || github.event.inputs.skip_tests == 'true')
    environment:
      name: production
      url: ${{ steps.deploy.outputs.production-url }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.version || github.ref }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'src/package-lock.json'

      - name: Install Vercel CLI
        run: npm install --global vercel@latest

      - name: Pull Vercel Environment Information
        run: vercel pull --yes --environment=production --token=${{ secrets.VERCEL_TOKEN }}
        working-directory: ./src

      - name: Build Project Artifacts
        run: vercel build --prod --token=${{ secrets.VERCEL_TOKEN }}
        working-directory: ./src
        env:
          # Production Environment Variables
          VITE_APPWRITE_ENDPOINT: ${{ secrets.PRODUCTION_APPWRITE_ENDPOINT }}
          VITE_APPWRITE_PROJECT_ID: ${{ secrets.PRODUCTION_APPWRITE_PROJECT_ID }}
          VITE_APPWRITE_DATABASE_ID: ${{ secrets.PRODUCTION_APPWRITE_DATABASE_ID }}
          VITE_ENVIRONMENT: production
          VITE_DEBUG: false
          NODE_ENV: production

      - name: Deploy to Production
        id: deploy
        run: |
          url=$(vercel deploy --prebuilt --prod --token=${{ secrets.VERCEL_TOKEN }})
          echo "production-url=$url" >> $GITHUB_OUTPUT
        working-directory: ./src

      - name: Run production health checks
        run: |
          echo "🏥 Running comprehensive health checks on production..."
          sleep 60  # Wait for deployment to be fully ready

          # Basic health check
          response=$(curl -s -o /dev/null -w "%{http_code}" "${{ steps.deploy.outputs.production-url }}/")
          if [ "$response" != "200" ]; then
            echo "❌ Basic health check failed: HTTP $response"
            exit 1
          fi

          # API health check
          api_response=$(curl -s -o /dev/null -w "%{http_code}" "${{ steps.deploy.outputs.production-url }}/api/health")
          if [ "$api_response" != "200" ]; then
            echo "⚠️ API health check warning: HTTP $api_response"
          fi

          echo "✅ Production deployment health checks passed"

      - name: Update deployment status
        if: always()
        run: |
          if [ "${{ job.status }}" == "success" ]; then
            echo "✅ Production deployment successful: ${{ steps.deploy.outputs.production-url }}"
            echo "🚀 VybeCoding.ai is now live in production!" >> $GITHUB_STEP_SUMMARY
          else
            echo "❌ Production deployment failed"
            echo "🚨 Production deployment failed - rollback may be required" >> $GITHUB_STEP_SUMMARY
          fi

  post-deployment-validation:
    name: Post-deployment Validation
    runs-on: ubuntu-latest
    needs: deploy-production
    if: needs.deploy-production.result == 'success'

    steps:
      - name: Run smoke tests
        run: |
          echo "🧪 Running post-deployment smoke tests..."

          # Test critical user journeys
          production_url="${{ needs.deploy-production.outputs.production-url }}"

          # Test homepage
          if ! curl -f -s "$production_url/" > /dev/null; then
            echo "❌ Homepage smoke test failed"
            exit 1
          fi

          # Test course page (if exists)
          if ! curl -f -s "$production_url/courses" > /dev/null; then
            echo "⚠️ Courses page not accessible (may be expected)"
          fi

          echo "✅ Smoke tests passed"

      - name: Performance validation
        run: |
          echo "⚡ Running performance validation..."

          # Basic performance check (could be enhanced with Lighthouse CI)
          production_url="${{ needs.deploy-production.outputs.production-url }}"

          start_time=$(date +%s%N)
          curl -s "$production_url/" > /dev/null
          end_time=$(date +%s%N)

          duration=$(( (end_time - start_time) / 1000000 ))  # Convert to milliseconds

          if [ $duration -gt 3000 ]; then
            echo "⚠️ Page load time is slow: ${duration}ms"
          else
            echo "✅ Page load time acceptable: ${duration}ms"
          fi

  rollback-on-failure:
    name: Rollback on Failure
    runs-on: ubuntu-latest
    needs: [deploy-production, post-deployment-validation]
    if: failure() && needs.deploy-production.result == 'success'

    steps:
      - name: Initiate rollback
        run: |
          echo "🔄 Initiating production rollback..."
          echo "This would typically:"
          echo "1. Restore previous Vercel deployment"
          echo "2. Restore database state if needed"
          echo "3. Verify rollback success"
          echo "4. Notify team of rollback completion"

          # For now, this is a placeholder
          echo "⚠️ Manual rollback may be required"

  notify-deployment:
    name: Notify Deployment Status
    runs-on: ubuntu-latest
    needs: [deploy-production, post-deployment-validation, rollback-on-failure]
    if: always()

    steps:
      - name: Notify success
        if: needs.deploy-production.result == 'success' && needs.post-deployment-validation.result == 'success'
        run: |
          echo "🎉 Production deployment successful!"
          echo "🌐 VybeCoding.ai is live: ${{ needs.deploy-production.outputs.production-url }}"
          echo "📊 All health checks and validations passed"
          # Add success notification webhook here

      - name: Notify failure
        if: needs.deploy-production.result == 'failure' || needs.post-deployment-validation.result == 'failure'
        run: |
          echo "🚨 Production deployment failed!"
          echo "Deploy status: ${{ needs.deploy-production.result }}"
          echo "Validation status: ${{ needs.post-deployment-validation.result }}"
          echo "Rollback status: ${{ needs.rollback-on-failure.result }}"
          # Add failure notification webhook here
