name: Security Scanning

on:
  workflow_dispatch:  # Only run manually until tests are fixed
    inputs:
      scan_type:
        description: 'Type of security scan to run'
        required: true
        default: 'full'
        type: choice
        options:
          - full
          - dependencies
          - code
          - container
  # push:
  #   branches: [main, develop, milestone-*]
  # pull_request:
  #   branches: [main]
  # schedule:
  #   # Run daily at 2 AM UTC
  #   - cron: '0 2 * * *'

jobs:
  dependency-scan:
    name: Dependency Security Scan
    runs-on: ubuntu-latest
    if: ${{ github.event.inputs.scan_type == 'full' || github.event.inputs.scan_type == 'dependencies' || github.event.inputs.scan_type == '' }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run npm audit
        run: |
          npm audit --audit-level=moderate --json > npm-audit-results.json || true
          npm audit --audit-level=moderate
        continue-on-error: true

      - name: Run Snyk to check for vulnerabilities
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high --json > snyk-results.json
        continue-on-error: true

      - name: Upload Snyk results to GitHub Code Scanning
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: snyk.sarif
        continue-on-error: true

      - name: Upload dependency scan results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: dependency-scan-results
          path: |
            npm-audit-results.json
            snyk-results.json
          retention-days: 30

  code-security-scan:
    name: Code Security Analysis
    runs-on: ubuntu-latest
    if: ${{ github.event.inputs.scan_type == 'full' || github.event.inputs.scan_type == 'code' || github.event.inputs.scan_type == '' }}

    permissions:
      actions: read
      contents: read
      security-events: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Initialize CodeQL
        uses: github/codeql-action/init@v3
        with:
          languages: javascript, typescript
          queries: security-extended,security-and-quality

      - name: Autobuild
        uses: github/codeql-action/autobuild@v3

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3
        with:
          category: '/language:javascript'

      - name: Run ESLint security scan
        run: |
          npx eslint . --ext .js,.ts,.svelte --format json > eslint-security-results.json || true
        continue-on-error: true

      - name: Upload code scan results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: code-scan-results
          path: eslint-security-results.json
          retention-days: 30

  container-security-scan:
    name: Container Security Scan
    runs-on: ubuntu-latest
    if: ${{ github.event.inputs.scan_type == 'full' || github.event.inputs.scan_type == 'container' || github.event.inputs.scan_type == '' }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Build Docker image
        run: |
          docker build -t vybecoding:security-test .

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: 'vybecoding:security-test'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

      - name: Run Trivy for JSON output
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: 'vybecoding:security-test'
          format: 'json'
          output: 'trivy-results.json'
        continue-on-error: true

      - name: Upload container scan results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: container-scan-results
          path: trivy-results.json
          retention-days: 30

  web-security-scan:
    name: Web Application Security Scan
    runs-on: ubuntu-latest
    if: ${{ github.event.inputs.scan_type == 'full' || github.event.inputs.scan_type == '' }}

    services:
      app:
        image: node:18
        ports:
          - 3000:3000

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build application
        run: npm run build

      - name: Start application
        run: |
          npm start &
          sleep 30
        env:
          NODE_ENV: test

      - name: Wait for application to be ready
        run: |
          timeout 60 bash -c 'until curl -f http://localhost:3000/api/health; do sleep 2; done'

      - name: Run OWASP ZAP Baseline Scan
        uses: zaproxy/action-baseline@v0.10.0
        with:
          target: 'http://localhost:3000'
          rules_file_name: '.zap/rules.tsv'
          cmd_options: '-a'
        continue-on-error: true

      - name: Upload ZAP scan results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: zap-scan-results
          path: report_html.html
          retention-days: 30

  security-report:
    name: Generate Security Report
    runs-on: ubuntu-latest
    needs: [dependency-scan, code-security-scan, container-security-scan, web-security-scan]
    if: always()

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download all scan results
        uses: actions/download-artifact@v4
        with:
          path: scan-results

      - name: Generate security report
        run: |
          echo "# Security Scan Report" > security-report.md
          echo "Generated on: $(date)" >> security-report.md
          echo "" >> security-report.md

          echo "## Scan Summary" >> security-report.md
          echo "- Dependency Scan: ${{ needs.dependency-scan.result }}" >> security-report.md
          echo "- Code Security Scan: ${{ needs.code-security-scan.result }}" >> security-report.md
          echo "- Container Security Scan: ${{ needs.container-security-scan.result }}" >> security-report.md
          echo "- Web Security Scan: ${{ needs.web-security-scan.result }}" >> security-report.md
          echo "" >> security-report.md

          echo "## Recommendations" >> security-report.md
          if [ "${{ needs.dependency-scan.result }}" != "success" ]; then
            echo "- Review and update vulnerable dependencies" >> security-report.md
          fi
          if [ "${{ needs.code-security-scan.result }}" != "success" ]; then
            echo "- Address code security issues identified by CodeQL" >> security-report.md
          fi
          if [ "${{ needs.container-security-scan.result }}" != "success" ]; then
            echo "- Update base images and fix container vulnerabilities" >> security-report.md
          fi
          if [ "${{ needs.web-security-scan.result }}" != "success" ]; then
            echo "- Address web application security issues" >> security-report.md
          fi

      - name: Upload security report
        uses: actions/upload-artifact@v4
        with:
          name: security-report
          path: security-report.md
          retention-days: 90

      - name: Comment on PR (if applicable)
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const report = fs.readFileSync('security-report.md', 'utf8');

            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `## 🔒 Security Scan Results\n\n${report}`
            });

  notify-security-team:
    name: Notify Security Team
    runs-on: ubuntu-latest
    needs: [dependency-scan, code-security-scan, container-security-scan]
    if: failure() && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop')

    steps:
      - name: Send Slack notification
        if: ${{ secrets.SLACK_WEBHOOK_URL }}
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          text: '🚨 Security scan failed on ${{ github.ref_name }} branch'
          webhook_url: ${{ secrets.SLACK_WEBHOOK_URL }}

      - name: Send Discord notification
        if: ${{ secrets.DISCORD_WEBHOOK_URL }}
        uses: Ilshidur/action-discord@master
        env:
          DISCORD_WEBHOOK: ${{ secrets.DISCORD_WEBHOOK_URL }}
        with:
          args: '🚨 Security scan failed on {{ EVENT_PAYLOAD.ref }} branch. Please review the security issues immediately.'
