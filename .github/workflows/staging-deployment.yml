name: Staging Deployment

on:
  push:
    branches: [develop]
  workflow_dispatch:
    inputs:
      force_deploy:
        description: 'Force deployment even if tests fail'
        required: false
        default: false
        type: boolean

env:
  NODE_VERSION: '20'
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_STAGING_PROJECT_ID }}

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    outputs:
      test-passed: ${{ steps.test-result.outputs.passed }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'src/package-lock.json'

      - name: Install dependencies
        run: |
          cd src
          npm ci

      - name: Run unit tests
        id: unit-tests
        run: |
          cd src
          npm test -- --run --reporter=verbose

      - name: Type checking
        id: type-check
        run: |
          cd src
          npx tsc --noEmit

      - name: Lint checking
        id: lint-check
        run: |
          cd src
          npm run lint

      - name: Build verification
        id: build-check
        run: |
          cd src
          npm run build

      - name: Set test result
        id: test-result
        run: |
          if [[ "${{ steps.unit-tests.outcome }}" == "success" && 
                "${{ steps.type-check.outcome }}" == "success" && 
                "${{ steps.lint-check.outcome }}" == "success" && 
                "${{ steps.build-check.outcome }}" == "success" ]]; then
            echo "passed=true" >> $GITHUB_OUTPUT
          else
            echo "passed=false" >> $GITHUB_OUTPUT
          fi

  security-scan:
    name: Security Scanning
    runs-on: ubuntu-latest
    needs: test

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'src/package-lock.json'

      - name: Install dependencies
        run: |
          cd src
          npm ci

      - name: Run npm audit
        run: |
          cd src
          npm audit --audit-level=high

      - name: Run CodeQL Analysis
        uses: github/codeql-action/init@v3
        with:
          languages: javascript

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [test, security-scan]
    if: needs.test.outputs.test-passed == 'true' || github.event.inputs.force_deploy == 'true'
    environment:
      name: staging
      url: ${{ steps.deploy.outputs.preview-url }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'src/package-lock.json'

      - name: Install Vercel CLI
        run: npm install --global vercel@latest

      - name: Pull Vercel Environment Information
        run: vercel pull --yes --environment=preview --token=${{ secrets.VERCEL_TOKEN }}
        working-directory: ./src

      - name: Build Project Artifacts
        run: vercel build --token=${{ secrets.VERCEL_TOKEN }}
        working-directory: ./src
        env:
          # Staging Environment Variables
          VITE_APPWRITE_ENDPOINT: ${{ secrets.STAGING_APPWRITE_ENDPOINT }}
          VITE_APPWRITE_PROJECT_ID: ${{ secrets.STAGING_APPWRITE_PROJECT_ID }}
          VITE_APPWRITE_DATABASE_ID: ${{ secrets.STAGING_APPWRITE_DATABASE_ID }}
          VITE_ENVIRONMENT: staging
          VITE_DEBUG: true

      - name: Deploy Project Artifacts to Vercel
        id: deploy
        run: |
          url=$(vercel deploy --prebuilt --token=${{ secrets.VERCEL_TOKEN }})
          echo "preview-url=$url" >> $GITHUB_OUTPUT
        working-directory: ./src

      - name: Run staging health checks
        run: |
          echo "🏥 Running health checks on staging deployment..."
          sleep 30  # Wait for deployment to be ready

          # Basic health check
          response=$(curl -s -o /dev/null -w "%{http_code}" "${{ steps.deploy.outputs.preview-url }}/")
          if [ "$response" != "200" ]; then
            echo "❌ Health check failed: HTTP $response"
            exit 1
          fi

          echo "✅ Staging deployment health check passed"

      - name: Update deployment status
        if: always()
        run: |
          if [ "${{ job.status }}" == "success" ]; then
            echo "✅ Staging deployment successful: ${{ steps.deploy.outputs.preview-url }}"
          else
            echo "❌ Staging deployment failed"
          fi

  notify:
    name: Notify Team
    runs-on: ubuntu-latest
    needs: [test, security-scan, deploy-staging]
    if: always()

    steps:
      - name: Notify on success
        if: needs.deploy-staging.result == 'success'
        run: |
          echo "🚀 Staging deployment successful!"
          echo "URL: ${{ needs.deploy-staging.outputs.preview-url }}"
          # Add Slack/Discord webhook notification here if configured

      - name: Notify on failure
        if: needs.deploy-staging.result == 'failure' || needs.test.result == 'failure' || needs.security-scan.result == 'failure'
        run: |
          echo "❌ Staging deployment pipeline failed"
          echo "Test result: ${{ needs.test.result }}"
          echo "Security scan result: ${{ needs.security-scan.result }}"
          echo "Deploy result: ${{ needs.deploy-staging.result }}"
          # Add failure notification here
