# Branch Protection and Repository Settings Configuration

# This file documents the recommended GitHub repository settings

# These need to be configured manually in GitHub repository settings

## Branch Protection Rules for 'main' branch:

### Required Status Checks:

- ✅ Require status checks to pass before merging
- ✅ Require branches to be up to date before merging
- Required status checks:
  - `CI/CD Pipeline`
  - `Code Quality Check`
  - `Security Scan`
  - `Build Verification`

### Pull Request Requirements:

- ✅ Require pull request reviews before merging
- Required reviewers: 1
- ✅ Dismiss stale PR approvals when new commits are pushed
- ✅ Require review from code owners (when CODEOWNERS file exists)
- ✅ Restrict pushes that create files larger than 100MB

### Branch Management:

- ✅ Restrict pushes to matching branches
- Allowed actors: Repository administrators only
- ✅ Allow force pushes: Disabled for safety
- ✅ Allow deletions: Disabled for safety

### Additional Settings:

- ✅ Require signed commits (recommended for production)
- ✅ Require linear history
- ✅ Include administrators in restrictions

## Repository Security Settings:

### Vulnerability Alerts:

- ✅ Dependency graph enabled
- ✅ Dependabot alerts enabled
- ✅ Dependabot security updates enabled
- ✅ Dependabot version updates enabled (via .github/dependabot.yml)

### Code Scanning:

- ✅ CodeQL analysis enabled
- ✅ Secret scanning enabled
- ✅ Push protection for secrets enabled

### Repository Access:

- Base permissions: Read
- Admin access: Repository owner only
- Write access: Collaborators only (if any)

## GitHub Actions Settings:

### Workflow Permissions:

- Actions permissions: Allow VybeCoding, and select non-VybeCoding, actions and reusable workflows
- Fork pull request workflows: Require approval for all outside collaborators
- Workflow permissions: Read repository contents and packages permissions

### Environments:

- staging: Requires manual approval for deployments
- production: Requires manual approval + specific reviewers

## Manual Configuration Steps:

1. Go to Repository Settings → Branches
2. Add branch protection rule for 'main'
3. Configure all settings listed above
4. Go to Security & Analysis → Enable all recommended features
5. Go to Actions → General → Configure workflow permissions
6. Go to Environments → Set up staging and production environments

## Configuration Verification:

After manual setup, verify:

- [ ] Try to push directly to main (should be blocked)
- [ ] Create a PR and verify required checks
- [ ] Verify Dependabot is creating PRs for updates
- [ ] Verify security scanning is active
- [ ] Test deployment environments require approval
