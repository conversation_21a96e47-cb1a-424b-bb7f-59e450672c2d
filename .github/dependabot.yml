# GitHub Dependabot configuration for automated dependency updates
# This enables security updates and keeps dependencies current

version: 2
updates:
  # Enable version updates for npm (JavaScript/TypeScript dependencies)
  - package-ecosystem: 'npm'
    directory: '/'
    schedule:
      interval: 'weekly'
      day: 'monday'
      time: '09:00'
    open-pull-requests-limit: 10
    reviewers:
      - '@VybeCoding'
    assignees:
      - '@VybeCoding'
    commit-message:
      prefix: 'npm:'
      prefix-development: 'npm(dev):'
      include: 'scope'
    labels:
      - 'dependencies'
      - 'npm'

  # Enable version updates for pip (Python dependencies)
  - package-ecosystem: 'pip'
    directory: '/'
    schedule:
      interval: 'weekly'
      day: 'monday'
      time: '09:00'
    open-pull-requests-limit: 10
    reviewers:
      - '@VybeCoding'
    assignees:
      - '@VybeCoding'
    commit-message:
      prefix: 'pip:'
      include: 'scope'
    labels:
      - 'dependencies'
      - 'python'

  # Enable version updates for GitHub Actions
  - package-ecosystem: 'github-actions'
    directory: '/'
    schedule:
      interval: 'weekly'
      day: 'monday'
      time: '09:00'
    open-pull-requests-limit: 5
    reviewers:
      - '@VybeCoding'
    assignees:
      - '@VybeCoding'
    commit-message:
      prefix: 'actions:'
      include: 'scope'
    labels:
      - 'dependencies'
      - 'github-actions'

  # Enable version updates for Docker
  - package-ecosystem: 'docker'
    directory: '/'
    schedule:
      interval: 'weekly'
      day: 'monday'
      time: '09:00'
    open-pull-requests-limit: 5
    reviewers:
      - '@VybeCoding'
    assignees:
      - '@VybeCoding'
    commit-message:
      prefix: 'docker:'
      include: 'scope'
    labels:
      - 'dependencies'
      - 'docker'
