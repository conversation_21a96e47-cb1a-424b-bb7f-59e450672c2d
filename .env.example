# VybeCoding.ai Platform Environment Variables

# Appwrite Configuration (Required)
VITE_APPWRITE_ENDPOINT=https://fra.cloud.appwrite.io/v1
VITE_APPWRITE_PROJECT_ID=683b200e00153d705da3
VITE_APPWRITE_DATABASE_ID=vybecoding-main

# LLM Provider API Keys (At least one required)
VITE_OPENAI_API_KEY=your-openai-api-key
VITE_ANTHROPIC_API_KEY=your-anthropic-api-key

# Local LLM Configuration (Optional)
VITE_LOCAL_LLM_ENDPOINT=http://localhost:11434
VITE_OLLAMA_ENDPOINT=http://localhost:11434

# OAuth Provider Configuration (Optional)
VITE_GOOGLE_CLIENT_ID=your-google-client-id
VITE_GITHUB_CLIENT_ID=your-github-client-id

# Security Configuration (Optional)
VITE_GUARDRAILS_ENDPOINT=your-guardrails-endpoint
VITE_GUARDRAILS_API_KEY=your-guardrails-api-key

# Development Configuration
NODE_ENV=development
VITE_APP_ENV=development
