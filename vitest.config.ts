import { defineConfig } from 'vitest/config';
import { sveltekit } from '@sveltejs/kit/vite';

export default defineConfig({
  plugins: [sveltekit()],
  test: {
    include: ['src/**/*.{test,spec}.{js,ts}', 'tests/**/*.{test,spec}.{js,ts}'],
    exclude: [
      'node_modules/**',
      'build/**',
      '.svelte-kit/**',
      'dist/**',
      'coverage/**',
      '**/node_modules/**',
      '**/.svelte-kit/**',
    ],
    environment: 'jsdom',
    globals: true,
    setupFiles: ['./tests/setup.js'],
    server: {
      deps: {
        inline: [/^svelte\//, /@sveltejs\/kit/, /@testing-library\/svelte/],
        external: ['monaco-editor', 'pyodide', 'appwrite'],
      },
    },
    alias: {
      $lib: 'src/lib',
      $app: '.svelte-kit/runtime/app',
      $types: 'src/lib/types',
      $config: 'src/lib/config',
      $components: 'src/lib/components',
      $stores: 'src/lib/stores',
      $utils: 'src/lib/utils',
      $services: 'src/lib/services',
    },
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'lcov'],
      reportsDirectory: './coverage',
      exclude: [
        'node_modules/',
        'src/lib/tests/',
        'tests/mocks/',
        '**/*.d.ts',
        '**/*.config.*',
        '**/coverage/**',
        '**/*.stories.*',
        '**/test-utils/**',
        'src/app.html',
        'src/service-worker.ts',
        'vite.config.ts',
        'vitest.config.ts',
        'svelte.config.js',
        'tailwind.config.js',
        'postcss.config.js',
      ],
      thresholds: {
        global: {
          branches: 85,
          functions: 85,
          lines: 85,
          statements: 85,
        },
        'src/lib/components/': {
          branches: 90,
          functions: 90,
          lines: 90,
          statements: 90,
        },
        'src/lib/services/': {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80,
        },
      },
    },
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: true,
        isolate: false,
      },
    },
    testTimeout: 10000,
    hookTimeout: 10000,
    teardownTimeout: 5000,
    silent: false,
    reporters: ['verbose', 'json', 'html'],
    outputFile: {
      json: './test-results/results.json',
      html: './test-results/index.html',
    },
  },
  resolve: {
    alias: {
      $lib: new URL('./src/lib', import.meta.url).pathname,
      $app: new URL(
        './node_modules/@sveltejs/kit/src/runtime/app',
        import.meta.url
      ).pathname,
      $types: new URL('./src/lib/types', import.meta.url).pathname,
      $config: new URL('./src/lib/config', import.meta.url).pathname,
      $components: new URL('./src/lib/components', import.meta.url).pathname,
      $stores: new URL('./src/lib/stores', import.meta.url).pathname,
      $utils: new URL('./src/lib/utils', import.meta.url).pathname,
      $services: new URL('./src/lib/services', import.meta.url).pathname,
    },
  },
  define: {
    __TEST__: true,
    __DEV__: true,
  },
  esbuild: {
    target: 'node14',
  },
  ssr: {
    noExternal: ['@testing-library/svelte'],
  },
});
