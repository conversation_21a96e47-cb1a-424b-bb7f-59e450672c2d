import { defineConfig } from 'vitest/config';

export default defineConfig({
  test: {
    include: [
      'src/lib/utils/testUtils.test.ts',
      'src/lib/tests/api-handlers.test.ts',
    ],
    environment: 'jsdom',
    globals: true,
    setupFiles: [],
    server: {
      deps: {
        external: ['monaco-editor', 'pyodide', 'appwrite'],
      },
    },
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json'],
      exclude: [
        'node_modules/',
        '**/*.d.ts',
        '**/*.config.*',
        '**/coverage/**',
      ],
    },
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: true,
      },
    },
    testTimeout: 5000,
    hookTimeout: 5000,
    teardownTimeout: 2000,
    silent: false,
    reporters: ['verbose'],
  },
  define: {
    __TEST__: true,
    __DEV__: true,
  },
});
