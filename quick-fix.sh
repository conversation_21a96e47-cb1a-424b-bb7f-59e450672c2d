#!/bin/bash

echo "🔧 Quick fix for workflow issues..."

# 1. Create missing config file
mkdir -p src/lib
cat > src/lib/config.ts << 'EOF'
export const config = {
  appwrite: {
    databaseId: '683b231d003c1c558e20',
    endpoint: 'https://cloud.appwrite.io/v1',
    projectId: 'vybecoding-platform'
  }
};
EOF

# 2. Create missing validation utils
cat > src/lib/utils/validation.ts << 'EOF'
export function validateUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

export function validatePrompt(prompt: string): boolean {
  return prompt.trim().length > 0;
}

export function validateAutonomousInput(input: any): boolean {
  return input && typeof input === 'object';
}

export function createValidator(rules: any) {
  return (input: any) => true;
}
EOF

# 3. Create missing output type detection
cat > src/lib/utils/outputTypeDetection.ts << 'EOF'
export function detectOutputType(output: string): string {
  if (output.includes('error')) return 'error';
  if (output.includes('warning')) return 'warning';
  return 'info';
}

export function createDetectionDebouncer(fn: Function, delay: number) {
  let timeoutId: NodeJS.Timeout;
  return (...args: any[]) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => fn.apply(null, args), delay);
  };
}
EOF

# 4. Create missing vybe storage
mkdir -p src/lib/server
cat > src/lib/server/vybe-storage.ts << 'EOF'
const tasks = new Map();

export function setTask(id: string, task: any) {
  tasks.set(id, task);
}

export function getTask(id: string) {
  return tasks.get(id);
}
EOF

echo "✅ Quick fixes applied!"
