# VybeCoding.ai Development Docker Compose
# Optimized for local development with hot reloading

version: '3.8'

services:
  vybecoding-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: vybecoding-dev
    ports:
      - '5173:5173' # Vite dev server
      - '24678:24678' # Vite HMR
    volumes:
      # Mount source code for hot reloading
      - .:/app
      - /app/node_modules # Prevent overwriting node_modules
      - dev-cache:/app/.vite
    environment:
      - NODE_ENV=development
      - VITE_HOST=0.0.0.0
      - VITE_PORT=5173
      - VITE_APPWRITE_ENDPOINT=${VITE_APPWRITE_ENDPOINT_DEV:-https://cloud.appwrite.io/v1}
      - VITE_APPWRITE_PROJECT_ID=${VITE_APPWRITE_PROJECT_ID_DEV:-vybecoding-dev}
      - VITE_APPWRITE_DATABASE_ID=${VITE_APPWRITE_DATABASE_ID_DEV:-main}
    networks:
      - vybecoding-dev
    restart: unless-stopped
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:5173']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Optional: Local database for development (if needed)
  # postgres-dev:
  #   image: postgres:15-alpine
  #   container_name: vybecoding-postgres-dev
  #   environment:
  #     POSTGRES_DB: vybecoding_dev
  #     POSTGRES_USER: developer
  #     POSTGRES_PASSWORD: devpassword
  #   ports:
  #     - "5432:5432"
  #   volumes:
  #     - postgres-dev-data:/var/lib/postgresql/data
  #   networks:
  #     - vybecoding-dev

volumes:
  dev-cache:
    driver: local
  # postgres-dev-data:
  #   driver: local

networks:
  vybecoding-dev:
    driver: bridge
