# GitHub Copilot Instructions for VybeCoding.ai

## 🎯 Project Overview

**VybeCoding.ai** is an AI-powered education platform that teaches the **Vybe Method** (rebranded BMad Method) for building profitable applications using AI tools. The platform features autonomous Multi-Agent Systems (MAS) that generate live revenue-producing websites ("Vybe Qubes") as proof of concept demonstrations.

### Core Value Proposition

**"Learn the Vybe Method → Build Your Own Ideas → Watch Live Proof It Works"**

Students learn universal AI-native development methodologies while observing autonomous systems generate real profitable websites that validate the effectiveness of the teaching.

## 🏗️ Architecture & Technology Stack

### **MAS Framework (100% FOSS)**

- **CrewAI**: Role-based agent collaboration (Apache 2.0)
- **AutoGen**: Enterprise-grade agent coordination (MIT)
- **LangGraph**: State management for workflows (MIT)
- **Model Context Protocol (MCP)**: Tool integration standard

### **LLM Strategy (100% Local FOSS)**

- **Primary Coding**: Qwen3-30B-A3B (Apache 2.0)
- **Code Generation**: Devstral-Small (specialized coding)
- **General Reasoning**: Gemma 2 27B (Google open weights)
- **Fast Responses**: DeepSeek-Coder 7B (MIT license)
- **Infrastructure**: RTX 5090 32GB + Ollama for model serving

### **Platform Architecture**

- **Frontend**: SvelteKit (performance + developer experience)
- **Backend**: Appwrite.io Cloud (99.99% SLA + comprehensive services)
- **Database**: Appwrite Database (managed PostgreSQL with real-time)
- **Authentication**: Appwrite Auth (multi-provider + enterprise SSO)
- **File Storage**: Appwrite Storage (CDN + automatic optimization)

### **Knowledge Management**

- **LlamaIndex Agentic Retrieval**: Replacing traditional RAG
- **Multi-Index Composite Retrieval**: Specialized knowledge bases
- **Query Router Agent**: Intent classification and route selection
- **Response Synthesis Agent**: Context aggregation and verification

### **Enterprise Reliability (2025 Standards)**

- **Guardrails AI**: Input/output validation with 50+ validators
- **Promptfoo Enterprise**: Security testing and vulnerability scanning
- **LangSmith**: Comprehensive observability and performance tracking
- **Multi-Agent Consensus**: 3/4 agent validation requirement
- **Zero-Hallucination Architecture**: 4-layer validation system

## 📁 Project Structure & File Organization

### **BMad Method Compliance**

This project follows the BMad Method v3 structure:

```
vybecoding/
├── docs/                    # 📖 Architecture & design documents
│   ├── README.md           # 📋 Documentation index
│   ├── prd.md             # 📋 Product Requirements Document
│   ├── architecture.md    # 🏗️ Technical architecture decisions
│   ├── autonomous-debugging-design.md  # 🔧 Operational reliability
│   ├── agent-interaction-patterns.md  # 🤖 MAS communication
│   └── communication-protocols.md     # 📡 Technical protocols
├── story-drafts/           # 📝 Epic-based user stories
│   ├── epic-1-content-generation/    # AI content creation
│   ├── epic-2-vybe-qubes/           # Interactive learning modules
│   ├── epic-3-personalization/     # Adaptive learning
│   ├── epic-4-community/           # Social features
│   └── templates/                   # BMad story templates
├── src/                    # 💻 Source code (SvelteKit + MAS)
├── tests/                  # 🧪 Testing framework
└── bmad-agent/            # 🤖 BMad orchestration system
```

### **Document Relationships**

- **project-brief.md** → **prd.md** → **architecture.md** (planning flow)
- **architecture.md** ↔ **autonomous-debugging-design.md** (technical integration)
- **agent-interaction-patterns.md** + **communication-protocols.md** (MAS implementation)
- **README.md** serves as documentation index with cross-references

## 🤖 Multi-Agent System Design

### **Agent Architecture**

```
Master Orchestrator (AutoGen)
├── MAS Cluster Controller (CrewAI)
│   ├── Market Research Agent
│   ├── Technical Architecture Agent
│   ├── UI/UX Design Agent
│   ├── Full-Stack Development Agent
│   ├── Content Creation Agent
│   ├── SEO Optimization Agent
│   ├── Monetization Strategy Agent
│   └── Quality Assurance Agent
├── Workflow State Manager (LangGraph)
├── Enterprise Reliability Layer
│   ├── Guardrails AI (Input/Output Validation)
│   ├── Multi-Agent Consensus Network
│   ├── Human-in-the-Loop Triggers
│   └── Continuous Security Monitoring
└── Observability & Recovery (LangSmith + Promptfoo)
```

### **Vybe Qube Types**

1. **MAS Vybe Qubes**: AI-generated demonstration websites (Appwrite.io hosted)
2. **User Vybe Qubes**: Student-created projects (student choice hosting)

### **Cluster Specialization**

- **Alpha Cluster**: SaaS specialists (15-20 active qubes)
- **Beta Cluster**: E-commerce specialists (15-20 active qubes)
- **Gamma Cluster**: Content specialists (15-20 active qubes)
- **Auto-Spawn Clusters**: Scale based on demand triggers

## 📝 Coding Standards & Patterns

### **File Naming Conventions**

- **Documentation**: `kebab-case.md` (BMad Method standard)
- **Components**: `PascalCase.svelte` (SvelteKit convention)
- **Utils/Lib**: `camelCase.js/.ts` (JavaScript standard)
- **Stories**: `STORY-XXX-descriptive-name.md` (BMad story format)
- **Epics**: `epic-N-descriptive-name/` (BMad epic structure)

### **Code Organization Principles**

- **Separation of Concerns**: Clear distinction between MAS, platform, and educational content
- **FOSS-First**: Prefer open source solutions with proper licensing
- **Educational Transparency**: Code should be readable and teachable
- **Enterprise Reliability**: All code must pass security and reliability standards

### **SvelteKit Specific**

- **Routes**: Use SvelteKit's file-based routing in `src/routes/`
- **Components**: Reusable components in `src/lib/components/`
- **Stores**: Global state management in `src/lib/stores/`
- **Utils**: Helper functions in `src/lib/utils/`
- **Types**: TypeScript definitions in `src/lib/types/`

### **MAS Integration**

- **Agent Configs**: Store in `bmad-agent/` following BMad Method templates
- **CrewAI Crews**: Define agent roles and tasks clearly
- **AutoGen Coordination**: Use for complex multi-agent workflows
- **LangGraph States**: Manage workflow state transitions

### **Appwrite.io Integration**

- **Database Collections**: Follow Appwrite naming conventions
- **Functions**: Use Appwrite Functions for serverless backend logic
- **Authentication**: Implement Appwrite Auth for user management
- **Storage**: Use Appwrite Storage for file management with CDN

## 🛡️ Security & Reliability Guidelines

### **Input Validation**

- **All user inputs** must pass Guardrails AI validation
- **Educational content** must be age-appropriate and curriculum-aligned
- **Code generation** must be security-scanned before execution
- **Prompt injection** prevention required for all AI interactions

### **Multi-Agent Consensus**

- **3/4 agent agreement** required for content generation
- **Confidence scoring** >85% threshold for responses
- **Human escalation** for complex or uncertain scenarios
- **Source attribution** for all educational claims

### **Zero-Hallucination Requirements**

- **Multi-source validation** against 3+ knowledge sources
- **Factual accuracy** >99.5% for educational content
- **Continuous monitoring** of AI response quality
- **Automatic flagging** of low-confidence responses

### **Educational Safety**

- **Age-appropriate content** filtering
- **Instructor oversight** dashboard
- **Student report mechanisms** for inappropriate content
- **Curriculum compliance** verification

## 💰 Business Logic & Monetization

### **Revenue Streams**

1. **Educational Subscriptions**: Students pay for Vybe Method courses
2. **MAS Qube Revenue**: Demonstration websites generate real income
3. **Pro Features**: Advanced tools and mentorship access
4. **Enterprise Licensing**: B2B sales of Vybe Method curriculum

### **Success Metrics**

- **Qube Success Rate**: >75% achieve $500+/month within 90 days
- **Student Success**: >60% of Pro subscribers submit profitable qubes
- **Platform Uptime**: >99.9% availability
- **Cost Efficiency**: <$0.50 per user per month infrastructure

### **Scaling Strategy**

- **Horizontal clusters**: Add specialized MAS clusters as demand grows
- **Revenue triggers**: $100K ARR per cluster → spawn next cluster
- **Hardware investment**: Self-funded through generated revenue

## 🎓 Educational Content Standards

### **Vybe Method Curriculum**

- **Universal methodology** applicable to any project type
- **AI tool integration** with practical examples
- **Real-world validation** through MAS-generated demonstrations
- **Step-by-step guidance** from idea to profitable application

### **Content Quality Requirements**

- **Technical accuracy** verified by expert agents
- **Learning objective alignment** with curriculum standards
- **Progressive difficulty** from beginner to advanced
- **Practical exercises** with real-world applications

### **Assessment & Validation**

- **Project submissions** through integrated workspace
- **Peer review system** for student projects
- **Revenue verification** for Vybe Qube success
- **Portfolio showcase** for successful graduates

## 🔧 Development Workflow

### **BMad Method Integration**

1. **Requirements**: Use story-drafts for development planning
2. **Architecture**: Reference docs/architecture.md for technical decisions
3. **Implementation**: Follow Epic → Story → Code workflow
4. **Testing**: Comprehensive testing in tests/ directory
5. **Documentation**: Update docs/ as features are built

### **Git Workflow**

- **Feature branches**: `feature/epic-N-story-XXX-description`
- **Commit messages**: Follow conventional commits format
- **Pull requests**: Require review and passing tests
- **Documentation**: Update relevant docs with code changes

### **AI-Assisted Development**

- **Context-aware**: Use this file for project understanding
- **Pattern-consistent**: Follow established architectural patterns
- **Documentation-driven**: Generate code that matches documentation
- **Test-inclusive**: Include appropriate test coverage

### **Quality Gates**

- **Security scan**: Promptfoo validation required
- **Performance**: Meet latency and throughput requirements
- **Educational**: Content must meet learning standards
- **Reliability**: Enterprise-grade error handling and monitoring

## 🎯 Key Success Factors

### **Technical Excellence**

- **Enterprise reliability** with 99.9%+ uptime
- **Zero-hallucination** AI content generation
- **Scalable architecture** supporting millions of users
- **Security-first** approach to AI and user data

### **Educational Impact**

- **Proven methodology** with measurable student success
- **Real-world validation** through revenue-generating demonstrations
- **Continuous improvement** based on student feedback and success metrics
- **Industry recognition** as leading AI education platform

### **Business Sustainability**

- **Self-funding growth** through generated revenue
- **Diversified income streams** reducing dependency risk
- **Competitive moats** through enterprise reliability
- **Market leadership** in AI development education

---

## 🚀 Quick Reference for GitHub Copilot

When working on VybeCoding.ai, remember:

- **Architecture-first**: Check docs/architecture.md for technical decisions
- **BMad Method**: Follow story-driven development from story-drafts/
- **FOSS preference**: Use open source solutions when possible
- **Educational focus**: All features must serve learning objectives
- **Enterprise reliability**: Security and reliability are non-negotiable
- **Multi-agent context**: Consider MAS coordination in all AI features
- **Revenue validation**: Features should support real business outcomes

---

_This document provides GitHub Copilot with comprehensive context about VybeCoding.ai's architecture, patterns, and requirements. Update this file as the project evolves to maintain accurate AI assistance._
