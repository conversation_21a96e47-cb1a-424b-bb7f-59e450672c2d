# TECHNICAL ARCHITECTURE: STORY-2-002 Container Infrastructure

**Architect:** <PERSON><PERSON> (BMAD Method Architect)  
**Date:** June 3, 2025  
**Epic:** 2 - Production Deployment Pipeline  
**Story:** STORY-2-002 Container Infrastructure & Environment Management  
**Dependencies:** Bill's PRD, Wendy's Analysis, Existing Docker Setup

## 🏗️ ARCHITECTURE OVERVIEW

This technical architecture defines the comprehensive container infrastructure for VybeCoding.ai, building upon the existing Docker foundation to create a production-ready, secure, and developer-friendly containerized environment that supports the SvelteKit + Appwrite + Local LLM stack.

### **CURRENT STATE ANALYSIS:**

```
✅ EXISTING INFRASTRUCTURE:
├── Dockerfile (production build)
├── Dockerfile.dev (development environment)
├── docker-compose.yml (base configuration)
├── docker-compose.development.yml (dev environment)
├── docker-compose.production.yml (prod environment)
├── GitHub Actions CI/CD (staging/production)
└── Vercel deployment integration

🔄 ENHANCEMENT REQUIREMENTS:
├── VS Code dev container integration
├── Security scanning automation
├── Testing environment setup
├── Container registry optimization
├── Monitoring and observability
└── Performance optimization
```

## 🎯 ARCHITECTURE PRINCIPLES

### **1. Developer-First Experience**

- **One-Command Setup:** `npm run dev:container` starts complete environment
- **VS Code Integration:** Full dev container support with extensions
- **Hot Reload:** Instant feedback for code changes
- **Debugging:** Integrated debugging tools and browser dev tools

### **2. Security-Integrated Design**

- **Vulnerability Scanning:** Automated security scanning in all builds
- **Non-Root Execution:** All containers run as non-root users
- **Secrets Management:** Environment-based secret injection
- **Minimal Attack Surface:** Distroless production images

### **3. Production-Ready Scalability**

- **Multi-Stage Builds:** Optimized for size and performance
- **Health Checks:** Comprehensive health monitoring
- **Resource Limits:** Defined CPU and memory constraints
- **Kubernetes-Ready:** Orchestration-ready configuration

## 🏛️ CONTAINER ARCHITECTURE DESIGN

### **Container Hierarchy**

```
┌─────────────────────────────────────────────────────────────┐
│                    VybeCoding.ai Container Stack            │
├─────────────────────────────────────────────────────────────┤
│  🔧 Development Tier                                        │
│  ├── vybecoding-dev (SvelteKit + Hot Reload)               │
│  ├── vybecoding-devcontainer (VS Code Integration)         │
│  └── vybecoding-test (Testing Environment)                 │
├─────────────────────────────────────────────────────────────┤
│  🚀 Production Tier                                         │
│  ├── vybecoding-app (Optimized SvelteKit)                  │
│  ├── vybecoding-nginx (Reverse Proxy + Static Assets)      │
│  └── vybecoding-monitoring (Metrics Collection)            │
├─────────────────────────────────────────────────────────────┤
│  🔒 Security Tier                                           │
│  ├── Security Scanner (Trivy + Snyk)                       │
│  ├── Secrets Manager (Environment Variables)               │
│  └── Network Policies (Container Isolation)                │
└─────────────────────────────────────────────────────────────┘
```

### **Multi-Stage Build Architecture**

```dockerfile
# Enhanced Production Dockerfile
FROM node:18-alpine AS base
# Security hardening and dependency installation

FROM base AS dependencies
# Install and cache dependencies with security scanning

FROM dependencies AS builder
# Build application with optimization

FROM gcr.io/distroless/nodejs18-debian11 AS runtime
# Minimal production runtime with security
```

## 🔧 TECHNICAL IMPLEMENTATION SPECIFICATIONS

### **1. Enhanced Development Environment**

#### **VS Code Dev Container Configuration**

```json
// .devcontainer/devcontainer.json
{
  "name": "VybeCoding.ai Development",
  "dockerComposeFile": "../docker-compose.development.yml",
  "service": "vybecoding-devcontainer",
  "workspaceFolder": "/app",
  "features": {
    "ghcr.io/devcontainers/features/docker-in-docker:2": {},
    "ghcr.io/devcontainers/features/github-cli:1": {}
  },
  "customizations": {
    "vscode": {
      "extensions": [
        "svelte.svelte-vscode",
        "bradlc.vscode-tailwindcss",
        "esbenp.prettier-vscode",
        "dbaeumer.vscode-eslint",
        "ms-vscode.vscode-typescript-next",
        "ms-azuretools.vscode-docker"
      ],
      "settings": {
        "typescript.preferences.includePackageJsonAutoImports": "on",
        "svelte.enable-ts-plugin": true
      }
    }
  },
  "forwardPorts": [5173, 3000, 8080],
  "postCreateCommand": "npm install && npm run dev:setup"
}
```

#### **Development Container Dockerfile**

```dockerfile
# .devcontainer/Dockerfile
FROM node:18-alpine

# Install development tools
RUN apk add --no-cache \
    git \
    curl \
    bash \
    docker-cli \
    docker-compose

# Create non-root user
RUN addgroup -g 1001 -S vscode && \
    adduser -S vscode -u 1001 -G vscode

# Install global development tools
RUN npm install -g @sveltejs/kit @playwright/test

# Set up workspace
WORKDIR /app
USER vscode

# Development environment variables
ENV NODE_ENV=development
ENV VITE_HOST=0.0.0.0
ENV VITE_PORT=5173
```

### **2. Production-Optimized Container**

#### **Multi-Stage Production Build**

```dockerfile
# Enhanced Dockerfile
FROM node:18-alpine AS base
RUN apk add --no-cache dumb-init curl
WORKDIR /app

FROM base AS dependencies
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

FROM base AS builder
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build && npm prune --production

FROM gcr.io/distroless/nodejs18-debian11 AS runtime
COPY --from=builder /usr/bin/dumb-init /usr/bin/dumb-init
COPY --from=builder /app/build /app/build
COPY --from=builder /app/node_modules /app/node_modules
COPY --from=builder /app/package.json /app/package.json

WORKDIR /app
EXPOSE 3000
USER 1001

HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:3000/api/health || exit 1

ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "build"]
```

### **3. Testing Environment Architecture**

#### **Isolated Testing Container**

```yaml
# docker-compose.test.yml
version: '3.8'
services:
  vybecoding-test:
    build:
      context: .
      dockerfile: Dockerfile.test
    environment:
      - NODE_ENV=test
      - VITE_APPWRITE_ENDPOINT=http://appwrite-mock:8080/v1
    depends_on:
      - appwrite-mock
      - test-db
    volumes:
      - ./coverage:/app/coverage
    command: npm run test:ci

  appwrite-mock:
    image: mockserver/mockserver:5.15.0
    ports:
      - '8080:1080'
    environment:
      MOCKSERVER_INITIALIZATION_JSON_PATH: /config/appwrite-mock.json
    volumes:
      - ./test/mocks:/config

  test-db:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: vybecoding_test
      POSTGRES_USER: test
      POSTGRES_PASSWORD: test
    tmpfs:
      - /var/lib/postgresql/data
```

### **4. Security Integration Architecture**

#### **Container Security Scanning**

```yaml
# Security scanning in CI/CD
security-scan:
  runs-on: ubuntu-latest
  steps:
    - name: Build security-scanned image
      run: |
        docker build -t vybecoding:security-scan .

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: 'vybecoding:security-scan'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Run Snyk container scan
      uses: snyk/actions/docker@master
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
      with:
        image: vybecoding:security-scan
```

## 📊 PERFORMANCE OPTIMIZATION SPECIFICATIONS

### **Build Performance Targets**

- **Full Build Time:** < 5 minutes (Target: 3 minutes)
- **Incremental Build:** < 1 minute (Target: 30 seconds)
- **Image Size:** < 100MB production (Target: 80MB)
- **Layer Cache Hit Rate:** > 80% (Target: 90%)

### **Runtime Performance Targets**

- **Container Startup:** < 30 seconds (Target: 15 seconds)
- **Memory Usage:** < 512MB base (Target: 256MB)
- **CPU Usage:** < 50% under normal load
- **Health Check Response:** < 3 seconds

### **Optimization Strategies**

#### **Build Optimization**

```dockerfile
# Layer caching optimization
FROM node:18-alpine AS deps
COPY package*.json ./
RUN npm ci --only=production

# Separate build dependencies
FROM node:18-alpine AS build-deps
COPY package*.json ./
RUN npm ci

# Build stage with cached dependencies
FROM build-deps AS builder
COPY . .
RUN npm run build
```

#### **Runtime Optimization**

```yaml
# Resource limits and requests
deploy:
  resources:
    limits:
      cpus: '1.0'
      memory: 512M
    reservations:
      cpus: '0.25'
      memory: 128M
```

## 🔍 MONITORING & OBSERVABILITY ARCHITECTURE

### **Container Metrics Collection**

```yaml
# Monitoring integration
monitoring:
  image: prom/node-exporter:latest
  volumes:
    - /proc:/host/proc:ro
    - /sys:/host/sys:ro
    - /:/rootfs:ro
  command:
    - '--path.procfs=/host/proc'
    - '--path.rootfs=/rootfs'
    - '--path.sysfs=/host/sys'
    - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
```

### **Health Check Endpoints**

```typescript
// Enhanced health check API
export async function GET() {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version,
    environment: process.env.NODE_ENV,
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    dependencies: {
      appwrite: await checkAppwriteConnection(),
      database: await checkDatabaseConnection(),
    },
  };

  return json(health);
}
```

## 🚀 DEPLOYMENT INTEGRATION ARCHITECTURE

### **Container Registry Workflow**

```yaml
# GitHub Container Registry integration
build-and-push:
  runs-on: ubuntu-latest
  steps:
    - name: Build and push container
      uses: docker/build-push-action@v5
      with:
        context: .
        push: true
        tags: |
          ghcr.io/hiram-ducky/vybecoding:latest
          ghcr.io/hiram-ducky/vybecoding:${{ github.sha }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
```

### **Kubernetes-Ready Configuration**

```yaml
# kubernetes/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: vybecoding-app
spec:
  replicas: 3
  selector:
    matchLabels:
      app: vybecoding
  template:
    metadata:
      labels:
        app: vybecoding
    spec:
      containers:
        - name: vybecoding
          image: ghcr.io/hiram-ducky/vybecoding:latest
          ports:
            - containerPort: 3000
          resources:
            requests:
              memory: '128Mi'
              cpu: '250m'
            limits:
              memory: '512Mi'
              cpu: '1000m'
          livenessProbe:
            httpGet:
              path: /api/health
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 10
```

## 📋 IMPLEMENTATION ROADMAP

### **Phase 1: Foundation Enhancement (Days 1-3)**

1. **VS Code Dev Container Setup**

   - Create `.devcontainer/` configuration
   - Enhance development Dockerfile
   - Test VS Code integration

2. **Security Integration**
   - Add Trivy and Snyk scanning
   - Implement security policies
   - Update CI/CD workflows

### **Phase 2: Testing Environment (Days 4-5)**

1. **Test Infrastructure**

   - Create `docker-compose.test.yml`
   - Set up mock services
   - Implement test automation

2. **Performance Optimization**
   - Enhance multi-stage builds
   - Implement layer caching
   - Optimize image sizes

### **Phase 3: Production Readiness (Days 6-7)**

1. **Monitoring Integration**

   - Add Prometheus metrics
   - Implement health checks
   - Set up logging

2. **Registry & Deployment**
   - Container registry automation
   - Kubernetes configuration
   - Documentation completion

## ✅ ARCHITECTURE VALIDATION CRITERIA

### **Technical Validation**

- [ ] All containers build successfully with security scans
- [ ] VS Code dev container fully functional
- [ ] Testing environment operational with mock services
- [ ] Production containers meet performance targets
- [ ] Monitoring and health checks working

### **Performance Validation**

- [ ] Build times meet targets (< 5 minutes full, < 1 minute incremental)
- [ ] Image sizes optimized (< 100MB production)
- [ ] Runtime performance within limits (< 512MB memory)
- [ ] Health checks respond within 3 seconds

### **Security Validation**

- [ ] All security scans pass with zero high/critical vulnerabilities
- [ ] Containers run as non-root users
- [ ] Secrets properly managed through environment variables
- [ ] Network policies and isolation configured

---

## 🎯 NEXT STEPS

**Ready for Implementation:** Architecture approved for Karen (Designer) UI/UX design phase  
**Timeline:** 1 week for complete implementation  
**Success Criteria:** Foundation ready for STORY-2-003 (Performance Monitoring)

---

**Architecture Complete** ✅  
**Ready for Design Phase** 🎨
