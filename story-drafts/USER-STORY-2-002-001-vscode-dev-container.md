# USER-STORY-2-002-001: VS Code Dev Container Setup

**Story ID:** USER-STORY-2-002-001  
**Parent Story:** STORY-2-002 Container Infrastructure  
**Sprint:** Container Infrastructure Sprint  
**Story Points:** 3  
**Priority:** CRITICAL  
**Assignee:** TBD (Developer)  
**Status:** Ready for Development

## 📋 USER STORY

**As a** developer new to VybeCoding.ai  
**I want** a one-click VS Code dev container setup  
**So that** I can start contributing to the codebase within 5 minutes

## 🎯 BUSINESS VALUE

This story is **CRITICAL** for achieving the primary business goal of reducing developer onboarding time from 4 hours to 5 minutes - a 4,700% improvement in developer productivity.

### **Impact:**

- **Developer Experience:** Eliminates complex local setup requirements
- **Team Productivity:** Instant development environment for all team members
- **Consistency:** Identical development environment across all platforms
- **Educational Value:** Students learn professional development practices

## ✅ ACCEPTANCE CRITERIA

### **AC-001: Dev Container Configuration**

- [ ] `.devcontainer/devcontainer.json` file created with complete configuration
- [ ] Container name: "VybeCoding.ai Development"
- [ ] Docker Compose integration with development services
- [ ] Workspace folder correctly mapped to `/app`
- [ ] Features include Docker-in-Docker and GitHub CLI

### **AC-002: VS Code Extensions**

- [ ] SvelteKit support (`svelte.svelte-vscode`)
- [ ] Tailwind CSS IntelliSense (`bradlc.vscode-tailwindcss`)
- [ ] Prettier formatter (`esbenp.prettier-vscode`)
- [ ] ESLint integration (`dbaeumer.vscode-eslint`)
- [ ] TypeScript support (`ms-vscode.vscode-typescript-next`)
- [ ] Docker tools (`ms-azuretools.vscode-docker`)
- [ ] All extensions auto-install on container creation

### **AC-003: Development Dockerfile**

- [ ] Custom `.devcontainer/Dockerfile` based on Node.js 18 Alpine
- [ ] Development tools installed (git, curl, bash, docker-cli)
- [ ] Non-root user `vscode` (UID 1001) created
- [ ] Global development tools (`@sveltejs/kit`, `@playwright/test`)
- [ ] Proper workspace permissions and ownership

### **AC-004: Environment Configuration**

- [ ] Development environment variables set (`NODE_ENV=development`)
- [ ] Vite configuration for container (`VITE_HOST=0.0.0.0`, `VITE_PORT=5173`)
- [ ] Port forwarding configured (5173, 3000, 8080)
- [ ] Post-create command: `npm install && npm run dev:setup`

### **AC-005: Performance Requirements**

- [ ] Container creation time < 2 minutes
- [ ] Post-create command execution < 3 minutes
- [ ] Total setup time < 5 minutes (including VS Code startup)
- [ ] Hot reload functionality working within 30 seconds

### **AC-006: Cross-Platform Compatibility**

- [ ] Works on Windows with WSL2
- [ ] Works on macOS (Intel and Apple Silicon)
- [ ] Works on Linux distributions
- [ ] Docker Desktop integration functional
- [ ] VS Code Remote-Containers extension compatible

## 🔧 TECHNICAL SPECIFICATIONS

### **File Structure:**

```
.devcontainer/
├── devcontainer.json
├── Dockerfile
└── docker-compose.yml (if needed)
```

### **Key Configuration Elements:**

#### **devcontainer.json:**

```json
{
  "name": "VybeCoding.ai Development",
  "dockerComposeFile": "../docker-compose.development.yml",
  "service": "vybecoding-dev",
  "workspaceFolder": "/app",
  "features": {
    "ghcr.io/devcontainers/features/docker-in-docker:2": {},
    "ghcr.io/devcontainers/features/github-cli:1": {}
  },
  "customizations": {
    "vscode": {
      "extensions": [...],
      "settings": {...}
    }
  },
  "forwardPorts": [5173, 3000, 8080],
  "postCreateCommand": "npm install && npm run dev:setup"
}
```

#### **Development Dockerfile:**

```dockerfile
FROM node:18-alpine

# Install development tools
RUN apk add --no-cache git curl bash docker-cli docker-compose

# Create non-root user
RUN addgroup -g 1001 -S vscode && \
    adduser -S vscode -u 1001 -G vscode

# Install global tools
RUN npm install -g @sveltejs/kit @playwright/test

WORKDIR /app
USER vscode

ENV NODE_ENV=development
ENV VITE_HOST=0.0.0.0
ENV VITE_PORT=5173
```

## 🧪 TESTING REQUIREMENTS

### **Manual Testing:**

- [ ] Fresh VS Code installation can open dev container
- [ ] All extensions load correctly
- [ ] Development server starts successfully
- [ ] Hot reload works for SvelteKit changes
- [ ] TypeScript compilation works
- [ ] ESLint and Prettier function correctly

### **Automated Testing:**

- [ ] Container build succeeds in CI/CD
- [ ] Post-create command executes without errors
- [ ] Port forwarding configuration validated
- [ ] Extension installation verified

### **Performance Testing:**

- [ ] Setup time measurement (target: < 5 minutes)
- [ ] Container resource usage monitoring
- [ ] Hot reload performance validation
- [ ] Cross-platform compatibility testing

## 📚 DOCUMENTATION REQUIREMENTS

### **Developer Documentation:**

- [ ] Setup instructions in README.md
- [ ] Troubleshooting guide for common issues
- [ ] VS Code Remote-Containers extension installation
- [ ] Platform-specific setup notes

### **Educational Content:**

- [ ] "What is a Dev Container?" explanation
- [ ] Benefits of containerized development
- [ ] Best practices for container development
- [ ] Integration with VybeCoding.ai learning objectives

## 🔗 DEPENDENCIES

### **Prerequisites:**

- [ ] Docker Desktop installed and running
- [ ] VS Code with Remote-Containers extension
- [ ] Git repository cloned locally
- [ ] Basic Docker Compose configuration exists

### **Blockers:**

- None identified

### **Related Stories:**

- USER-STORY-2-002-004 (Documentation) - depends on this story
- USER-STORY-2-002-005 (Testing Environment) - builds on this foundation

## 📊 SUCCESS METRICS

### **Primary Metrics:**

- **Setup Time:** < 5 minutes from clone to running development server
- **Success Rate:** 100% successful setup across all platforms
- **Team Adoption:** 100% of developers using dev containers within 1 week
- **Support Tickets:** < 1 setup-related support ticket per month

### **Secondary Metrics:**

- **Container Performance:** < 512MB memory usage
- **Hot Reload Speed:** < 1 second change detection
- **Extension Load Time:** < 30 seconds for all extensions
- **Cross-Platform Compatibility:** Works on Windows, macOS, Linux

## 🎯 DEFINITION OF DONE

### **Code Complete:**

- [ ] All acceptance criteria implemented and tested
- [ ] Code reviewed and approved by senior developer
- [ ] No critical or high-severity security vulnerabilities
- [ ] Performance benchmarks met

### **Quality Assurance:**

- [ ] Manual testing completed on all target platforms
- [ ] Automated tests pass in CI/CD pipeline
- [ ] Documentation updated and reviewed
- [ ] Cross-platform compatibility verified

### **Business Validation:**

- [ ] Product Owner acceptance testing completed
- [ ] 5-minute setup time validated with new team member
- [ ] Educational value confirmed with learning objectives
- [ ] Integration with existing development workflow verified

---

## 🚀 READY FOR IMPLEMENTATION

**Story Status:** Ready for Developer Assignment  
**Estimated Completion:** Day 1 of Sprint  
**Risk Level:** Low (well-defined requirements, proven technology)

**Next Steps:**

1. Assign to developer (Rodney/James)
2. Create development branch
3. Implement dev container configuration
4. Test across all platforms
5. Update documentation
6. Validate 5-minute setup goal

---

**Story Created by:** Fran (Scrum Master)  
**Creation Date:** June 3, 2025  
**Last Updated:** June 3, 2025
