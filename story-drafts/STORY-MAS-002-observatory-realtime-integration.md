# 📋 **STORY-MAS-002: Observatory Real-Time Integration**

**Epic:** MAS Critical Fixes Implementation  
**Story Points:** 8 (Medium)  
**Priority:** P0 - Critical  
**Sprint:** MAS Fix Sprint 1  
**Assignee:** Developer (<PERSON>)  
**Created:** January 2025  
**Status:** Ready for Development

## 📖 **User Story**

**As a** VybeCoding.ai platform administrator  
**I want** the Observatory to show real agent conversations and activities  
**So that** I can monitor actual MAS operations instead of simulated data

## 🎯 **Acceptance Criteria**

### **AC1: Real Agent Conversations**
- [ ] **GIVEN** agents are generating content via Vybe Method
- [ ] **WHEN** I view the Observatory monitoring tab
- [ ] **THEN** I see actual agent conversations in real-time
- [ ] **AND** conversations show real agent names (VYBA, QUBERT, CODEX, etc.)
- [ ] **AND** conversations contain actual content generation discussions
- [ ] **AND** no simulated or fake conversations are displayed

### **AC2: Live Activity Feed**
- [ ] **GIVEN** agents are performing activities (web search, file access, etc.)
- [ ] **WHEN** I view the Observatory monitoring tab
- [ ] **THEN** I see real-time activity updates
- [ ] **AND** activities show actual URLs being searched
- [ ] **AND** activities show actual files being accessed
- [ ] **AND** activities show actual agent coordination

### **AC3: File Change Tracking**
- [ ] **GIVEN** agents are modifying codebase files
- [ ] **WHEN** file changes occur
- [ ] **THEN** the Observatory shows real file modifications
- [ ] **AND** file changes include actual file paths
- [ ] **AND** file changes show modification timestamps
- [ ] **AND** file changes indicate which agent made the change

### **AC4: WebSocket Real-Time Updates**
- [ ] **GIVEN** the Observatory is open
- [ ] **WHEN** agent activities occur
- [ ] **THEN** updates appear immediately without page refresh
- [ ] **AND** WebSocket connection is stable and reliable
- [ ] **AND** connection failures are handled gracefully with reconnection

### **AC5: Agent Status Monitoring**
- [ ] **GIVEN** the Vybe Method MAS is running
- [ ] **WHEN** I view agent status
- [ ] **THEN** I see real agent availability and current tasks
- [ ] **AND** agent status updates in real-time
- [ ] **AND** inactive agents are clearly indicated

## 🔧 **Technical Requirements**

### **Implementation Components**
1. **ObservatoryRealTimeService** (`src/lib/services/observatory-realtime.ts`)
   - WebSocket connection management
   - Real-time data processing
   - Agent conversation handling
   - Activity feed management
   - File change tracking

2. **Vybe Method WebSocket Server** (`method/vybe/websocket_server.py`)
   - Agent communication broadcasting
   - Activity event streaming
   - File change notifications
   - Agent status updates

3. **Agent Communication Bridge** (`method/vybe/agent_communication_bridge.py`)
   - Capture real agent conversations
   - Broadcast agent activities
   - Track file modifications
   - Monitor agent status

### **WebSocket Message Schema**
```typescript
interface AgentMessage {
  type: 'agent_conversation' | 'agent_activity' | 'file_change' | 'agent_status';
  timestamp: string;
  agent: string;
  data: {
    conversation?: {
      from: string;
      to: string;
      message: string;
      context: string;
    };
    activity?: {
      action: string;
      target: string;
      details: string;
    };
    file_change?: {
      file_path: string;
      action: 'created' | 'modified' | 'deleted';
      size: number;
    };
    status?: {
      agent: string;
      status: 'active' | 'idle' | 'busy' | 'offline';
      current_task: string;
    };
  };
}
```

### **Observatory UI Updates**
```svelte
<!-- src/routes/generator/+page.svelte -->
<script>
  import { ObservatoryRealTimeService } from '$lib/services/observatory-realtime';
  
  let realTimeService: ObservatoryRealTimeService;
  let agentConversations: AgentMessage[] = [];
  let agentActivities: AgentMessage[] = [];
  let fileChanges: AgentMessage[] = [];
  let agentStatus: Record<string, any> = {};
  
  onMount(async () => {
    realTimeService = new ObservatoryRealTimeService();
    
    // Connect to real Vybe Method WebSocket
    await realTimeService.connect();
    
    // Subscribe to real-time updates
    realTimeService.onMessage((message: AgentMessage) => {
      switch (message.type) {
        case 'agent_conversation':
          agentConversations = [message, ...agentConversations].slice(0, 50);
          break;
        case 'agent_activity':
          agentActivities = [message, ...agentActivities].slice(0, 50);
          break;
        case 'file_change':
          fileChanges = [message, ...fileChanges].slice(0, 50);
          break;
        case 'agent_status':
          agentStatus[message.agent] = message.data.status;
          break;
      }
    });
  });
</script>
```

## 🧪 **Testing Requirements**

### **Unit Tests**
- [ ] ObservatoryRealTimeService.connect()
- [ ] ObservatoryRealTimeService.onMessage()
- [ ] ObservatoryRealTimeService.handleReconnection()
- [ ] WebSocket message parsing and validation
- [ ] Agent status tracking accuracy

### **Integration Tests**
- [ ] End-to-end WebSocket communication
- [ ] Real agent conversation capture and display
- [ ] Activity feed real-time updates
- [ ] File change tracking accuracy
- [ ] Connection failure and recovery

### **Manual Testing Checklist**
- [ ] Start content generation and verify real conversations appear
- [ ] Verify agent activities show actual web searches and file access
- [ ] Test WebSocket connection stability over extended periods
- [ ] Verify file changes are tracked accurately
- [ ] Test connection recovery after network interruption
- [ ] Verify no simulated data appears in Observatory

## 📊 **Definition of Done**

- [ ] All acceptance criteria met and tested
- [ ] Real agent conversations displayed (no simulations)
- [ ] Live activity feed shows actual agent activities
- [ ] File changes tracked and displayed accurately
- [ ] WebSocket connection stable and reliable
- [ ] Unit tests written and passing (>90% coverage)
- [ ] Integration tests written and passing
- [ ] Manual testing completed successfully
- [ ] Code reviewed and approved
- [ ] Documentation updated
- [ ] Performance benchmarks met (<100ms message latency)

## 🚨 **Risks and Mitigation**

### **Risk 1: WebSocket Connection Stability**
- **Risk:** WebSocket connections may be unstable or drop frequently
- **Mitigation:** Implement automatic reconnection with exponential backoff

### **Risk 2: Message Volume Overload**
- **Risk:** High volume of agent messages may overwhelm the UI
- **Mitigation:** Implement message throttling and pagination

### **Risk 3: Agent Communication Capture**
- **Risk:** Capturing real agent conversations may be technically challenging
- **Mitigation:** Implement robust logging and message interception in Vybe Method

## 🔗 **Dependencies**

- **Depends on:** Vybe Method MAS running and generating content
- **Blocks:** STORY-MAS-004 (Autonomous Mode) - needs real monitoring
- **Related:** STORY-MAS-001 (Content Deployment), STORY-MAS-003 (Protocol Services)

## 📝 **Notes**

- This story is critical for transparency and debugging of MAS operations
- Real-time performance is crucial - users need immediate feedback
- Error handling must be robust for production reliability
- Consider implementing message filtering and search capabilities
- WebSocket security should be considered for production deployment

**This story transforms the Observatory from showing simulated data to providing real-time visibility into actual MAS operations.**
