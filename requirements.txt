annotated-types==0.7.0
anthropic==0.40.0
anyio==4.9.0
asgiref==3.8.1
attrs==25.3.0
backoff==2.2.1
bcrypt==4.3.0
black==24.10.0
build==1.2.2.post1
cachetools==5.5.2
certifi==2025.4.26
charset-normalizer==3.4.2
chromadb==1.0.8
click==8.2.1
coloredlogs==15.0.1
Deprecated==1.2.18
distro==1.9.0
durationpy==0.10
exceptiongroup==1.3.0
fastapi==0.115.9
filelock==3.18.0
flake8==7.1.1
flatbuffers==25.2.10
fsspec==2025.5.1
google-auth==2.40.2
googleapis-common-protos==1.70.0
grpcio==1.71.0
h11==0.16.0
hf-xet==1.1.2
httpcore==1.0.9
httptools==0.6.4
httpx==0.28.1
huggingface-hub==0.32.3
humanfriendly==10.0
idna==3.10
importlib_metadata==8.6.1
importlib_resources==6.5.2
iniconfig==2.1.0
Jinja2==3.1.6
jiter==0.10.0
joblib==1.5.1
jsonschema==4.24.0
jsonschema-specifications==2025.4.1
kubernetes==32.0.1
markdown-it-py==3.0.0
MarkupSafe==3.0.2
mccabe==0.7.0
mdurl==0.1.2
mmh3==5.1.0
mpmath==1.3.0
mypy_extensions==1.1.0
networkx==3.4.2
numpy==2.2.6
nvidia-cublas-cu12==12.6.4.1
nvidia-cuda-cupti-cu12==12.6.80
nvidia-cuda-nvrtc-cu12==12.6.77
nvidia-cuda-runtime-cu12==12.6.77
nvidia-cudnn-cu12==9.5.1.17
nvidia-cufft-cu12==11.3.0.4
nvidia-cufile-cu12==1.11.1.6
nvidia-curand-cu12==10.3.7.77
nvidia-cusolver-cu12==11.7.1.2
nvidia-cusparse-cu12==12.5.4.2
nvidia-cusparselt-cu12==0.6.3
nvidia-nccl-cu12==2.26.2
nvidia-nvjitlink-cu12==12.6.85
nvidia-nvtx-cu12==12.6.77
oauthlib==3.2.2
onnxruntime==1.22.0
openai==1.58.1
opentelemetry-api==1.33.1
opentelemetry-exporter-otlp-proto-common==1.33.1
opentelemetry-exporter-otlp-proto-grpc==1.33.1
opentelemetry-instrumentation==0.54b1
opentelemetry-instrumentation-asgi==0.54b1
opentelemetry-instrumentation-fastapi==0.54b1
opentelemetry-proto==1.33.1
opentelemetry-sdk==1.33.1
opentelemetry-semantic-conventions==0.54b1
opentelemetry-util-http==0.54b1
orjson==3.10.18
overrides==7.7.0
packaging==25.0
pathspec==0.12.1
pillow==11.2.1
platformdirs==4.3.8
pluggy==1.6.0
posthog==4.2.0
protobuf==5.29.5
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycodestyle==2.12.1
pydantic==2.11.4
pydantic_core==2.33.2
pyflakes==3.2.0
Pygments==2.19.1
PyPika==0.48.9
pyproject_hooks==1.2.0
pytest==8.3.4
pytest-asyncio==0.25.0
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
PyYAML==6.0.2
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
requests-oauthlib==2.0.0
rich==14.0.0
rpds-py==0.25.1
rsa==4.9.1
safetensors==0.5.3
scikit-learn==1.6.1
scipy==1.15.3
sentence-transformers==3.3.1
shellingham==1.5.4
six==1.17.0
sniffio==1.3.1
starlette==0.45.3
sympy==1.14.0
tenacity==9.1.2
threadpoolctl==3.6.0
tokenizers==0.21.1
toml==0.10.2
tomli==2.2.1
torch==2.7.0
tqdm==4.67.1
transformers==4.52.4
triton==3.3.0
typer==0.16.0
typing-inspection==0.4.1
typing_extensions==4.13.2
urllib3==2.4.0
uvicorn==0.34.2
uvloop==0.21.0
watchdog==6.0.0
watchfiles==1.0.5
websocket-client==1.8.0
websockets==15.0.1
wrapt==1.17.2
zipp==3.22.0
