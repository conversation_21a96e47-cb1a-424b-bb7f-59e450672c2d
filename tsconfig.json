{"extends": "./.svelte-kit/tsconfig.json", "compilerOptions": {"allowJs": true, "checkJs": false, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "skipLibCheck": true, "sourceMap": true, "strict": false, "moduleResolution": "bundler", "target": "ES2022", "module": "ESNext", "lib": ["ES2022", "DOM", "DOM.Iterable"], "allowSyntheticDefaultImports": true, "declaration": true, "declarationMap": true, "noEmit": true, "noImplicitAny": false, "noImplicitReturns": false, "noImplicitThis": false, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false}, "include": ["**/*.d.ts", "**/*.ts", "**/*.svelte", "src/**/*.js"], "exclude": ["node_modules/**", "build/**", ".svelte-kit/**", "scripts/**", "method/**", "tests/**", "src/lib/tests/**", "src/lib/autonomous-debugging/**", "**/*.stories.ts", "src/lib/components/ui/utils/accessibility.ts", "src/lib/components/ui/utils/analytics.ts", "src/lib/config/features.ts", "src/lib/services/aiCodeReview.ts", "src/lib/services/aiPersonalization.ts", "src/lib/services/appwrite.ts", "src/lib/services/auth.ts", "src/lib/services/courses.ts", "src/lib/services/ai/**", "src/lib/services/appwrite.ts", "src/lib/services/codeExecution.ts", "src/lib/services/collaborationService.ts", "src/lib/services/communityService.ts", "src/lib/services/contentService.ts", "src/lib/services/exerciseSystem.ts", "src/lib/services/githubIntegration.ts", "src/lib/services/progress.ts", "src/lib/services/revenueTracking.ts", "src/lib/services/userVybeQubes.ts", "src/lib/services/vybeMonitoring.ts", "src/lib/services/vybeQubeDeployment.ts", "src/routes/**/*.ts", "src/lib/services/webrtcService.ts", "src/lib/services/presenceService.ts", "src/lib/services/websocketService.ts", "src/lib/services/websocketManager.ts", "src/lib/services/analyticsService.ts", "src/lib/stores/theme.ts", "src/lib/stores/workspace.ts", "src/lib/stores/aiAssistant.ts", "src/lib/stores/collaboration.ts", "src/lib/stores/projects.ts", "src/routes/ws/**", "vybe-agent/**"]}