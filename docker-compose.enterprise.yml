# VybeCoding.ai Enterprise Deployment - Phase 6
# Production Scaling & Enterprise Deployment
version: '3.8'

services:
  # Load Balancer - HAProxy for enterprise-grade load balancing
  load-balancer:
    image: haproxy:2.8-alpine
    container_name: vybecoding-lb
    ports:
      - '80:80'
      - '443:443'
      - '8404:8404'  # HAProxy stats
    volumes:
      - ./enterprise/haproxy/haproxy.cfg:/usr/local/etc/haproxy/haproxy.cfg:ro
      - ./enterprise/ssl:/etc/ssl/certs:ro
      - haproxy_logs:/var/log/haproxy
    restart: always
    networks:
      - vybecoding_enterprise
    healthcheck:
      test: ['CMD', 'haproxy', '-c', '-f', '/usr/local/etc/haproxy/haproxy.cfg']
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 256M

  # VybeCoding.ai Application - Multiple instances for scaling
  vybecoding-app-1:
    build:
      context: .
      dockerfile: Dockerfile.enterprise
    container_name: vybecoding-app-1
    environment:
      - NODE_ENV=production
      - VITE_ENVIRONMENT=enterprise
      - INSTANCE_ID=app-1
      - CLUSTER_MODE=true
      - REDIS_URL=redis://redis-cluster:6379
      - DATABASE_POOL_SIZE=20
      - MAX_CONCURRENT_REQUESTS=1000
    volumes:
      - ./logs:/app/logs:rw
      - vybecoding_enterprise_data:/app/data
      - ./enterprise/config:/app/config:ro
    restart: always
    networks:
      - vybecoding_enterprise
    depends_on:
      - redis-cluster
      - postgres-primary
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3000/api/health']
      interval: 15s
      timeout: 5s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          cpus: '4.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 1G

  vybecoding-app-2:
    build:
      context: .
      dockerfile: Dockerfile.enterprise
    container_name: vybecoding-app-2
    environment:
      - NODE_ENV=production
      - VITE_ENVIRONMENT=enterprise
      - INSTANCE_ID=app-2
      - CLUSTER_MODE=true
      - REDIS_URL=redis://redis-cluster:6379
      - DATABASE_POOL_SIZE=20
      - MAX_CONCURRENT_REQUESTS=1000
    volumes:
      - ./logs:/app/logs:rw
      - vybecoding_enterprise_data:/app/data
      - ./enterprise/config:/app/config:ro
    restart: always
    networks:
      - vybecoding_enterprise
    depends_on:
      - redis-cluster
      - postgres-primary
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3000/api/health']
      interval: 15s
      timeout: 5s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          cpus: '4.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 1G

  vybecoding-app-3:
    build:
      context: .
      dockerfile: Dockerfile.enterprise
    container_name: vybecoding-app-3
    environment:
      - NODE_ENV=production
      - VITE_ENVIRONMENT=enterprise
      - INSTANCE_ID=app-3
      - CLUSTER_MODE=true
      - REDIS_URL=redis://redis-cluster:6379
      - DATABASE_POOL_SIZE=20
      - MAX_CONCURRENT_REQUESTS=1000
    volumes:
      - ./logs:/app/logs:rw
      - vybecoding_enterprise_data:/app/data
      - ./enterprise/config:/app/config:ro
    restart: always
    networks:
      - vybecoding_enterprise
    depends_on:
      - redis-cluster
      - postgres-primary
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3000/api/health']
      interval: 15s
      timeout: 5s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          cpus: '4.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 1G

  # Redis Cluster for high-performance caching
  redis-cluster:
    image: redis:7-alpine
    container_name: vybecoding-redis-cluster
    ports:
      - '6379:6379'
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    volumes:
      - redis_enterprise_data:/data
      - ./enterprise/redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
    restart: always
    networks:
      - vybecoding_enterprise
    command: redis-server /usr/local/etc/redis/redis.conf --requirepass ${REDIS_PASSWORD}
    healthcheck:
      test: ['CMD', 'redis-cli', '-a', '${REDIS_PASSWORD}', 'ping']
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 512M

  # PostgreSQL Primary for enterprise data
  postgres-primary:
    image: postgres:15-alpine
    container_name: vybecoding-postgres-primary
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-vybecoding}
      - POSTGRES_USER=${POSTGRES_USER:-vybecoding}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_MAX_CONNECTIONS=200
    volumes:
      - postgres_enterprise_data:/var/lib/postgresql/data
      - ./enterprise/postgres/postgresql.conf:/etc/postgresql/postgresql.conf:ro
      - ./enterprise/postgres/init:/docker-entrypoint-initdb.d:ro
    restart: always
    networks:
      - vybecoding_enterprise
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U ${POSTGRES_USER:-vybecoding}']
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '4.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 1G

  # CDN Cache Server (Varnish)
  cdn-cache:
    image: varnish:7.4-alpine
    container_name: vybecoding-cdn
    ports:
      - '8080:80'
    volumes:
      - ./enterprise/varnish/default.vcl:/etc/varnish/default.vcl:ro
    environment:
      - VARNISH_SIZE=1G
    restart: always
    networks:
      - vybecoding_enterprise
    depends_on:
      - load-balancer
    healthcheck:
      test: ['CMD', 'varnishstat', '-1']
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 512M

  # Enterprise Monitoring Stack
  prometheus-enterprise:
    image: prom/prometheus:latest
    container_name: vybecoding-prometheus-enterprise
    ports:
      - '9090:9090'
    volumes:
      - ./enterprise/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./enterprise/prometheus/rules:/etc/prometheus/rules:ro
      - prometheus_enterprise_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    restart: always
    networks:
      - vybecoding_enterprise
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 512M

  # Grafana Enterprise Dashboard
  grafana-enterprise:
    image: grafana/grafana-enterprise:latest
    container_name: vybecoding-grafana-enterprise
    ports:
      - '3001:3000'
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_ENTERPRISE_LICENSE_TEXT=${GRAFANA_ENTERPRISE_LICENSE}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_ANALYTICS_REPORTING_ENABLED=false
    volumes:
      - grafana_enterprise_data:/var/lib/grafana
      - ./enterprise/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./enterprise/grafana/dashboards:/var/lib/grafana/dashboards:ro
    restart: always
    networks:
      - vybecoding_enterprise
    depends_on:
      - prometheus-enterprise
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 256M

  # Auto-scaler service
  auto-scaler:
    build:
      context: ./enterprise/auto-scaler
      dockerfile: Dockerfile
    container_name: vybecoding-auto-scaler
    environment:
      - DOCKER_HOST=unix:///var/run/docker.sock
      - PROMETHEUS_URL=http://prometheus-enterprise:9090
      - SCALE_UP_THRESHOLD=80
      - SCALE_DOWN_THRESHOLD=20
      - MIN_INSTANCES=3
      - MAX_INSTANCES=10
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./enterprise/auto-scaler/config:/app/config:ro
    restart: always
    networks:
      - vybecoding_enterprise
    depends_on:
      - prometheus-enterprise
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.1'
          memory: 128M

volumes:
  vybecoding_enterprise_data:
    driver: local
  redis_enterprise_data:
    driver: local
  postgres_enterprise_data:
    driver: local
  prometheus_enterprise_data:
    driver: local
  grafana_enterprise_data:
    driver: local
  haproxy_logs:
    driver: local

networks:
  vybecoding_enterprise:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
    driver_opts:
      com.docker.network.bridge.name: vybecoding-enterprise
