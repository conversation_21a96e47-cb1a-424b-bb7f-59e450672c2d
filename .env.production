# VybeCoding.ai Production Environment Configuration
# This file contains production-specific environment variables
# Actual secrets should be stored in GitHub Secrets

# Environment
VITE_ENVIRONMENT=production
VITE_APP_NAME=VybeCoding.ai
VITE_APP_VERSION=1.0.0

# Appwrite Configuration (Production)
VITE_APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
VITE_APPWRITE_PROJECT_ID=production-project-id
VITE_APPWRITE_DATABASE_ID=production-database-id

# API Configuration
VITE_API_BASE_URL=https://vybecoding.ai/api
VITE_API_TIMEOUT=15000

# Feature Flags (Production)
VITE_ENABLE_DEBUG_MODE=false
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_ERROR_REPORTING=true
VITE_ENABLE_PERFORMANCE_MONITORING=true

# Security Settings (Production)
VITE_ENABLE_CSP=true
VITE_ENABLE_HTTPS_ONLY=true
VITE_ENABLE_SECURE_COOKIES=true
VITE_ENABLE_HSTS=true

# Logging Configuration
VITE_LOG_LEVEL=error
VITE_ENABLE_CONSOLE_LOGS=false

# Development Tools (Production)
VITE_ENABLE_DEVTOOLS=false
VITE_ENABLE_HOT_RELOAD=false
VITE_ENABLE_SOURCE_MAPS=false

# External Services (Production)
VITE_ENABLE_THIRD_PARTY_SCRIPTS=true
VITE_GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX
VITE_SENTRY_DSN=https://<EMAIL>/project

# Build Configuration
VITE_BUILD_TARGET=production
VITE_OPTIMIZE_BUNDLE=true
VITE_GENERATE_SOURCEMAP=false
VITE_MINIFY_CODE=true

# Cache Configuration
VITE_CACHE_STRATEGY=cache-first
VITE_CACHE_TTL=3600

# Performance Settings
VITE_ENABLE_COMPRESSION=true
VITE_ENABLE_PRELOAD=true
VITE_ENABLE_PREFETCH=true

# Deployment Information
VITE_DEPLOY_TIMESTAMP=
VITE_COMMIT_HASH=
VITE_BRANCH_NAME=main
