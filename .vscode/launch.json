{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "firefox",
      "request": "launch",
      "name": "🦊 Firefox Developer Edition Debug",
      "url": "http://localhost:5173",
      "webRoot": "${workspaceFolder}",
      "firefoxExecutable": "/home/<USER>/firefox-dev/firefox",
      "preferences": {
        "devtools.toolbox.host": "window",
        "devtools.console.timestampMessages": true,
        "devtools.debugger.auto-pretty-print": true,
        "devtools.performance.enabled": true,
        "devtools.responsive.enabled": true,
        "devtools.memory.enabled": true,
        "devtools.storage.enabled": true,
        "devtools.netmonitor.enabled": true,
        "devtools.webconsole.filter.net": true,
        "devtools.webconsole.filter.netxhr": true,
        "devtools.webconsole.filter.css": true,
        "devtools.webconsole.filter.js": true,
        "devtools.webconsole.filter.error": true,
        "devtools.webconsole.filter.warn": true,
        "devtools.webconsole.filter.info": true,
        "devtools.webconsole.filter.log": true,
        "devtools.webconsole.filter.debug": true
      }
    },
    {
      "type": "firefox",
      "request": "launch",
      "name": "🔥 Firefox Debug Monitor",
      "url": "http://localhost:5173/debug-monitor.html",
      "webRoot": "${workspaceFolder}",
      "firefoxExecutable": "/home/<USER>/firefox-dev/firefox",
      "preferences": {
        "devtools.toolbox.host": "window",
        "devtools.console.timestampMessages": true,
        "devtools.debugger.auto-pretty-print": true,
        "devtools.performance.enabled": true,
        "devtools.responsive.enabled": true,
        "devtools.memory.enabled": true,
        "devtools.storage.enabled": true,
        "devtools.netmonitor.enabled": true,
        "devtools.webconsole.filter.net": true,
        "devtools.webconsole.filter.netxhr": true,
        "devtools.webconsole.filter.css": true,
        "devtools.webconsole.filter.js": true,
        "devtools.webconsole.filter.error": true,
        "devtools.webconsole.filter.warn": true,
        "devtools.webconsole.filter.info": true,
        "devtools.webconsole.filter.log": true,
        "devtools.webconsole.filter.debug": true
      }
    },
    {
      "type": "chrome",
      "request": "launch",
      "name": "🌐 Chrome Debug (Fallback)",
      "url": "http://localhost:5173",
      "webRoot": "${workspaceFolder}",
      "sourceMaps": true,
      "trace": true,
      "runtimeArgs": ["--auto-open-devtools-for-tabs", "--enable-logging", "--log-level=0"]
    },
    {
      "type": "node",
      "request": "launch",
      "name": "🔧 Debug Vite Dev Server",
      "program": "${workspaceFolder}/node_modules/.bin/vite",
      "args": ["dev", "--host", "--port", "5173"],
      "cwd": "${workspaceFolder}",
      "env": {
        "NODE_ENV": "development",
        "DEBUG": "*"
      },
      "console": "integratedTerminal",
      "sourceMaps": true,
      "restart": true
    }
  ]
}
