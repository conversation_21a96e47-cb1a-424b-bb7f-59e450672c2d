{"version": "2.0.0", "tasks": [{"label": "Create Milestone", "type": "shell", "command": "./scripts/auto-milestone.sh", "args": ["create", "${input:milestoneType}", "${input:milestoneDescription}"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "List Milestones", "type": "shell", "command": "./scripts/auto-milestone.sh", "args": ["list"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "Rollback to Milestone", "type": "shell", "command": "./scripts/auto-milestone.sh", "args": ["rollback", "${input:milestoneNumber}"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "🦊 Start Firefox Debug Session", "type": "shell", "command": "./scripts/firefox-debug.sh", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}, "isBackground": true, "problemMatcher": []}, {"label": "🔥 Firefox Debug Monitor Only", "type": "shell", "command": "./scripts/firefox-debug.sh", "args": ["http://localhost:5173/debug-monitor.html"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}, "isBackground": true, "problemMatcher": []}, {"label": "🚀 Dev Server + Firefox Debug", "dependsOrder": "sequence", "dependsOn": ["Start Dev Server", "🦊 Start Firefox Debug Session"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "shared"}}, {"label": "Start Dev Server", "type": "shell", "command": "npm", "args": ["run", "dev:simple"], "group": "build", "isBackground": true, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}], "inputs": [{"id": "milestoneType", "description": "Milestone type", "default": "story", "type": "pickString", "options": ["story", "feature", "bugfix", "release", "hotfix"]}, {"id": "milestoneDescription", "description": "Milestone description", "default": "", "type": "promptString"}, {"id": "milestoneNumber", "description": "Milestone number to rollback to", "default": "001", "type": "promptString"}]}