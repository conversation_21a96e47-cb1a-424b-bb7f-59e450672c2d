{"files.associations": {"*.svelte": "svelte"}, "editor.codeActionsOnSave": {"source.fixAll": "explicit"}, "typescript.preferences.includePackageJsonAutoImports": "off", "svelte.enable-ts-plugin": true, "search.exclude": {"**/node_modules": true, "**/.git": true, "**/dist": true, "**/.svelte-kit": true}, "files.watcherExclude": {"**/node_modules/**": true, "**/.git/**": true, "**/dist/**": true, "**/.svelte-kit/**": true}, "editor.rulers": [80, 120], "editor.wordWrap": "bounded", "editor.wordWrapColumn": 120, "typescript.suggest.autoImports": false, "javascript.suggest.autoImports": false, "todo-tree.general.tags": ["TODO", "FIXME", "PLACEHOLDER", "MOCK", "FAKE", "SIMULATE"], "todo-tree.highlights.defaultHighlight": {"icon": "alert", "type": "text", "foreground": "red", "background": "yellow", "opacity": 50, "iconColour": "red"}, "emeraldwalk.runonsave": {"commands": [{"match": "\\.(ts|js|svelte)$", "cmd": "npm run validate:no-simulations"}]}}