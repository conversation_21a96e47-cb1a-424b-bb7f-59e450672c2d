#!/usr/bin/env python3
"""
BMAD-Vybe Method Integration Bridge
Provides seamless transition between BMAD Method v3.1 and Vybe Method MAS
"""

import asyncio
import json
import logging
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, Union

# Setup paths
current_dir = Path(__file__).parent
bmad_dir = current_dir / "bmad"
vybe_dir = current_dir / "vybe"

# Add paths to sys.path
sys.path.insert(0, str(current_dir))
sys.path.insert(0, str(bmad_dir))
sys.path.insert(0, str(vybe_dir))

# Import BMAD components with fallback
try:
    from bmad.bmad_commands import BMADCommandSystem
except ImportError:
    try:
        from bmad_commands import BMADCommandSystem
    except ImportError:
        class BMADCommandSystem:
            def __init__(self):
                pass
            def bmad_status(self):
                return {"status": "mock_bmad"}

# Import Vybe components with fallback  
try:
    from vybe.vybe_commands import VybeMethodCommands
except ImportError:
    try:
        from vybe_commands import VybeMethodCommands
    except ImportError:
        class VybeMethodCommands:
            def __init__(self):
                pass
            def agent_status(self):
                return {"status": "mock_vybe"}

# Setup logging first
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

try:
    from vybe.real_mas_coordinator import RealMASCoordinator
except ImportError:
    try:
        from real_mas_coordinator import RealMASCoordinator
    except ImportError:
        logger.error("RealMASCoordinator not found. Please ensure the Vybe Method MAS is properly installed.")
        raise ImportError("RealMASCoordinator is required for BMAD-Vybe integration")


class BMADVybeIntegrationBridge:
    """
    Integration bridge between BMAD Method v3.1 and Vybe Method MAS
    
    Provides:
    - Seamless agent mapping between methods
    - Educational transition from sequential to autonomous workflows
    - Coordinated artifact sharing
    - Unified command interface
    """

    def __init__(self):
        self.bmad_system = BMADCommandSystem()
        self.vybe_system = VybeCommandSystem()
        self.bmad_orchestrator = BMADOrchestrator()
        
        # Will be initialized when needed
        self.mas_coordinator = None
        
        self.integration_status = {
            "bmad_active": False,
            "vybe_active": False,
            "bridge_mode": "sequential",  # sequential, parallel, hybrid
            "last_transition": None,
            "shared_artifacts": []
        }
        
        # Official BMAD v3.1 to Vybe Method agent mapping (IDE Version)
        self.agent_mapping = {
            # BMAD Method v3.1 agents → Vybe Method MAS agents
            "analyst": "vyba",      # Wendy → VYBA (analysis & research)
            "pm": "qubert",         # Bill → Qubert (product management)
            "architect": "codex",   # Timmy → Codex (architecture)
            "design-architect": "pixy",  # Karen → Pixy (design)
            "po": "happy",          # Jimmy → Happy (validation)
            "sm": "ducky",          # Fran → Ducky (planning)
            "dev-frontend": "vybro", # Rodney → Vybro (development)
            "dev-fullstack": "vybro", # James → Vybro (full-stack)
            "platform-engineer": "codex"  # Alex → Codex (infrastructure)
        }
        
        # Reverse mapping for Vybe → BMAD transitions
        self.reverse_mapping = {v: k for k, v in self.agent_mapping.items()}
        
        logger.info("BMAD-Vybe Integration Bridge initialized")

    async def initialize_bridge(self) -> Dict[str, Any]:
        """Initialize the integration bridge"""
        logger.info("🌉 Initializing BMAD-Vybe Integration Bridge...")
        
        try:
            # Initialize BMAD system
            bmad_status = await self.bmad_system.bmad_start()
            self.integration_status["bmad_active"] = bmad_status["success"]
            
            # Initialize Vybe system (lazy initialization)
            self.integration_status["vybe_active"] = True
            
            # Create shared artifacts directory
            shared_dir = Path(__file__).parent.parent / "shared_artifacts"
            shared_dir.mkdir(exist_ok=True)
            
            # Create subdirectories for different artifact types
            for subdir in ["analysis", "requirements", "architecture", "design", "stories", "code"]:
                (shared_dir / subdir).mkdir(exist_ok=True)
            
            return {
                "success": True,
                "message": "BMAD-Vybe Integration Bridge ready",
                "bmad_status": self.integration_status["bmad_active"],
                "vybe_status": self.integration_status["vybe_active"],
                "agent_mappings": len(self.agent_mapping),
                "shared_artifacts_path": str(shared_dir)
            }
            
        except Exception as e:
            logger.error(f"Bridge initialization error: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to initialize integration bridge"
            }

    async def bmad_to_vybe_transition(self, bmad_agent: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Transition from BMAD Method sequential workflow to Vybe Method MAS
        
        Args:
            bmad_agent: The BMAD agent that completed work
            context: Context and artifacts from BMAD workflow
        """
        logger.info(f"🔄 Transitioning from BMAD agent '{bmad_agent}' to Vybe MAS...")
        
        if bmad_agent not in self.agent_mapping:
            return {
                "success": False,
                "error": f"Unknown BMAD agent: {bmad_agent}",
                "available_agents": list(self.agent_mapping.keys())
            }
        
        try:
            # Map to corresponding Vybe agent
            vybe_agent = self.agent_mapping[bmad_agent]
            
            # Initialize MAS coordinator if needed
            if not self.mas_coordinator:
                self.mas_coordinator = RealMASCoordinator()
                await self.mas_coordinator.initialize()
            
            # Transfer artifacts and context
            transition_data = {
                "source_method": "BMAD_v3.1",
                "source_agent": bmad_agent,
                "target_method": "Vybe_MAS",
                "target_agent": vybe_agent,
                "context": context or {},
                "timestamp": datetime.now().isoformat(),
                "transition_type": "sequential_to_autonomous"
            }
            
            # Save transition data for reference
            await self._save_transition_data(transition_data)
            
            # Activate corresponding Vybe agent with context
            vybe_result = await self._activate_vybe_agent(vybe_agent, transition_data)
            
            self.integration_status["last_transition"] = transition_data
            self.integration_status["bridge_mode"] = "hybrid"
            
            return {
                "success": True,
                "message": f"Successfully transitioned from BMAD {bmad_agent} to Vybe {vybe_agent}",
                "transition_data": transition_data,
                "vybe_result": vybe_result
            }
            
        except Exception as e:
            logger.error(f"Transition error: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to transition from {bmad_agent} to Vybe MAS"
            }

    async def vybe_to_bmad_handoff(self, vybe_agent: str, deliverables: Dict[str, Any]) -> Dict[str, Any]:
        """
        Hand off Vybe MAS autonomous results back to BMAD Method for review/validation
        
        Args:
            vybe_agent: The Vybe agent that completed autonomous work
            deliverables: Autonomous deliverables from Vybe MAS
        """
        logger.info(f"🔄 Handing off from Vybe agent '{vybe_agent}' to BMAD Method...")
        
        if vybe_agent not in self.reverse_mapping:
            return {
                "success": False,
                "error": f"Unknown Vybe agent: {vybe_agent}",
                "available_agents": list(self.reverse_mapping.keys())
            }
        
        try:
            # Map back to corresponding BMAD agent
            bmad_agent = self.reverse_mapping[vybe_agent]
            
            # Prepare handoff data
            handoff_data = {
                "source_method": "Vybe_MAS",
                "source_agent": vybe_agent,
                "target_method": "BMAD_v3.1",
                "target_agent": bmad_agent,
                "deliverables": deliverables,
                "timestamp": datetime.now().isoformat(),
                "handoff_type": "autonomous_to_review"
            }
            
            # Save handoff data
            await self._save_transition_data(handoff_data)
            
            # Format deliverables for BMAD review
            bmad_context = await self._format_for_bmad_review(handoff_data)
            
            return {
                "success": True,
                "message": f"Handoff ready: Vybe {vybe_agent} → BMAD {bmad_agent}",
                "handoff_data": handoff_data,
                "bmad_context": bmad_context,
                "review_agent": bmad_agent
            }
            
        except Exception as e:
            logger.error(f"Handoff error: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to handoff from {vybe_agent} to BMAD Method"
            }

    async def execute_hybrid_workflow(self, task_description: str, preferred_mode: str = "bmad_first") -> Dict[str, Any]:
        """
        Execute a hybrid workflow combining BMAD Method education with Vybe Method automation
        
        Args:
            task_description: Description of the task to be completed
            preferred_mode: "bmad_first", "vybe_first", or "parallel"
        """
        logger.info(f"🔄 Executing hybrid workflow: {preferred_mode}")
        
        try:
            workflow_result = {
                "workflow_id": f"hybrid_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "task_description": task_description,
                "mode": preferred_mode,
                "phases": [],
                "final_deliverables": {}
            }
            
            if preferred_mode == "bmad_first":
                # Educational phase: BMAD Method walkthrough
                bmad_phase = await self._execute_bmad_educational_phase(task_description)
                workflow_result["phases"].append(bmad_phase)
                
                # Autonomous phase: Vybe Method implementation
                vybe_phase = await self._execute_vybe_autonomous_phase(task_description, bmad_phase["context"])
                workflow_result["phases"].append(vybe_phase)
                
            elif preferred_mode == "vybe_first":
                # Rapid prototyping: Vybe Method autonomous execution
                vybe_phase = await self._execute_vybe_autonomous_phase(task_description)
                workflow_result["phases"].append(vybe_phase)
                
                # Educational review: BMAD Method analysis
                bmad_phase = await self._execute_bmad_review_phase(vybe_phase["deliverables"])
                workflow_result["phases"].append(bmad_phase)
                
            elif preferred_mode == "parallel":
                # Parallel execution with consensus validation
                bmad_task = self._execute_bmad_educational_phase(task_description)
                vybe_task = self._execute_vybe_autonomous_phase(task_description)
                
                bmad_phase, vybe_phase = await asyncio.gather(bmad_task, vybe_task)
                
                workflow_result["phases"].extend([bmad_phase, vybe_phase])
                
                # Consensus validation
                consensus_phase = await self._execute_consensus_validation(bmad_phase, vybe_phase)
                workflow_result["phases"].append(consensus_phase)
            
            return {
                "success": True,
                "workflow_result": workflow_result,
                "message": f"Hybrid workflow completed successfully ({preferred_mode})"
            }
            
        except Exception as e:
            logger.error(f"Hybrid workflow error: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to execute hybrid workflow"
            }

    async def get_integration_status(self) -> Dict[str, Any]:
        """Get current integration bridge status"""
        return {
            "bridge_status": self.integration_status,
            "agent_mappings": self.agent_mapping,
            "bmad_agents_available": len(self.bmad_system.bmad_agents),
            "vybe_agents_available": 7,  # VYBA, Qubert, Codex, Pixy, Happy, Ducky, Vybro
            "last_transition": self.integration_status.get("last_transition"),
            "shared_artifacts_count": len(self.integration_status.get("shared_artifacts", []))
        }

    async def _activate_vybe_agent(self, agent_name: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Activate a specific Vybe agent with context from BMAD transition"""
        if not self.mas_coordinator:
            self.mas_coordinator = RealMASCoordinator()
            await self.mas_coordinator.initialize()
        
        # Format context for Vybe agent
        vybe_context = {
            "source": "BMAD_transition",
            "bmad_context": context,
            "agent_specialization": agent_name,
            "execution_mode": "autonomous"
        }
        
        # Use the Vybe command system to activate the agent
        return await self.vybe_system.vybe_agent_direct(agent_name, vybe_context)

    async def _save_transition_data(self, data: Dict[str, Any]) -> None:
        """Save transition data to shared artifacts"""
        shared_dir = Path(__file__).parent.parent / "shared_artifacts"
        transition_file = shared_dir / f"transition_{data['timestamp'].replace(':', '-')}.json"
        
        with open(transition_file, 'w') as f:
            json.dump(data, f, indent=2)
        
        self.integration_status["shared_artifacts"].append(str(transition_file))

    async def _format_for_bmad_review(self, handoff_data: Dict[str, Any]) -> Dict[str, Any]:
        """Format Vybe deliverables for BMAD Method review"""
        return {
            "review_type": "autonomous_validation",
            "source_agent": handoff_data["source_agent"],
            "deliverables": handoff_data["deliverables"],
            "review_checklist": await self._generate_review_checklist(handoff_data),
            "recommended_bmad_agent": handoff_data["target_agent"]
        }

    async def _generate_review_checklist(self, handoff_data: Dict[str, Any]) -> List[str]:
        """Generate review checklist based on agent type and deliverables"""
        agent_type = handoff_data["target_agent"]
        
        checklists = {
            "analyst": [
                "Are research findings comprehensive?",
                "Is the analysis methodology sound?",
                "Are sources credible and current?",
                "Does the brief align with project goals?"
            ],
            "pm": [
                "Are requirements clearly defined?",
                "Is the PRD complete and actionable?",
                "Are acceptance criteria specific?",
                "Is scope appropriate for timeline?"
            ],
            "architect": [
                "Is the architecture scalable?",
                "Are design patterns appropriate?",
                "Is the technical approach sound?",
                "Are dependencies clearly identified?"
            ],
            "design-architect": [
                "Is the design user-centered?",
                "Are accessibility standards met?",
                "Is the visual hierarchy clear?",
                "Is the design system consistent?"
            ],
            "po": [
                "Do stories align with business value?",
                "Are priorities clearly established?",
                "Is the backlog properly groomed?",
                "Are stakeholder needs addressed?"
            ],
            "sm": [
                "Are stories properly sized?",
                "Is the sprint plan realistic?",
                "Are blockers identified?",
                "Is team capacity considered?"
            ],
            "dev-frontend": [
                "Is the code maintainable?",
                "Are performance standards met?",
                "Is accessibility implemented?",
                "Are tests comprehensive?"
            ],
            "dev-fullstack": [
                "Is the solution complete?",
                "Are security standards met?",
                "Is the architecture scalable?",
                "Is documentation adequate?"
            ]
        }
        
        return checklists.get(agent_type, ["General quality review required"])

    async def _execute_bmad_educational_phase(self, task_description: str) -> Dict[str, Any]:
        """Execute BMAD Method educational phase"""
        # Implementation would coordinate with BMAD orchestrator
        return {
            "phase": "bmad_educational",
            "status": "completed",
            "context": {"task": task_description, "method": "sequential_learning"},
            "deliverables": {"educational_artifacts": "Generated via BMAD Method"}
        }

    async def _execute_vybe_autonomous_phase(self, task_description: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Execute Vybe Method autonomous phase"""
        # Implementation would coordinate with Vybe MAS
        return {
            "phase": "vybe_autonomous",
            "status": "completed",
            "deliverables": {"autonomous_implementation": "Generated via Vybe MAS"},
            "context": context or {}
        }

    async def _execute_bmad_review_phase(self, vybe_deliverables: Dict[str, Any]) -> Dict[str, Any]:
        """Execute BMAD Method review of Vybe deliverables"""
        return {
            "phase": "bmad_review",
            "status": "completed",
            "review_results": {"validation": "BMAD review of Vybe deliverables"},
            "vybe_deliverables": vybe_deliverables
        }

    async def _execute_consensus_validation(self, bmad_phase: Dict[str, Any], vybe_phase: Dict[str, Any]) -> Dict[str, Any]:
        """Execute consensus validation between BMAD and Vybe approaches"""
        return {
            "phase": "consensus_validation",
            "status": "completed",
            "consensus_result": {"validation": "Consensus between BMAD and Vybe approaches"},
            "bmad_phase": bmad_phase,
            "vybe_phase": vybe_phase
        }


# Main integration function
async def main():
    """Main integration bridge function"""
    bridge = BMADVybeIntegrationBridge()
    
    # Initialize the bridge
    init_result = await bridge.initialize_bridge()
    print(json.dumps(init_result, indent=2))
    
    # Show integration status
    status = await bridge.get_integration_status()
    print(json.dumps(status, indent=2))


if __name__ == "__main__":
    asyncio.run(main())
