# BMAD Method Sequential Workflow Process

**Version**: 3.1
**Source**: https://github.com/bmadcode/BMAD-METHOD.git
**Purpose**: Define the official 7-agent sequential workflow for BMAD Method implementation

## 🎯 **SEQUENTIAL WORKFLOW OVERVIEW**

The BMAD Method follows a strict sequential process where each agent completes their phase before handing off to the next agent. This ensures proper artifact flow, quality gates, and comprehensive project development.

**Required Sequence:**

```
<PERSON> (Analyst) → <PERSON> (PM) → <PERSON><PERSON> (Architect) → <PERSON> (Designer) → <PERSON> (PO) → <PERSON><PERSON> (SM) → <PERSON> (Developer)
```

## 📋 **PHASE-BY-PHASE WORKFLOW**

### **Phase 1: <PERSON> (Analyst) - Business Analysis**

**Input**: Initial project concept or business idea
**Duration**: 1-3 sessions
**Mode Options**: Brainstorming → Research Prompt Generation → Project Briefing

**Key Activities:**

1. **Brainstorming Phase**: Generate and explore ideas creatively
2. **Research Prompt Generation**: Create detailed research directives
3. **Project Briefing**: Structure comprehensive project brief

**Required Deliverables:**

- Project Brief (`/docs/project-brief.md`) using `project-brief-tmpl.md`
- Market analysis and competitive research
- Business requirements documentation

**Quality Gates:**

- [ ] Project brief follows template structure
- [ ] Business requirements clearly defined
- [ ] Market analysis completed
- [ ] Stakeholder needs identified

**Handoff to Bill (PM)**: Completed project brief with business analysis

---

### **Phase 2: Bill (PM) - Product Requirements**

**Input**: Project brief from Wendy
**Duration**: 2-4 sessions
**Focus**: Product strategy and requirements definition

**Key Activities:**

1. Review and analyze project brief
2. Create/update Product Requirements Document
3. Define epics and initial story structure
4. Establish product roadmap and strategy

**Required Deliverables:**

- PRD (`/method/bmad/artifacts/requirements/prd.md`) using `prd-tmpl.md`
- Epic definitions and story outlines
- Product strategy and roadmap

**Quality Gates:**

- [ ] PRD follows template structure
- [ ] All requirements clearly defined
- [ ] Epics properly structured
- [ ] Success metrics established

**Handoff to Timmy (Architect)**: Completed PRD with epics and stories

---

### **Phase 3: Timmy (Architect) - Technical Architecture**

**Input**: PRD from Bill
**Duration**: 2-3 sessions
**Focus**: System design and technical specifications

**Key Activities:**

1. Analyze PRD requirements
2. Design system architecture
3. Define technology stack
4. Create technical specifications

**Required Deliverables:**

- Architecture documentation (`/method/bmad/artifacts/architecture/`) using `architecture-tmpl.md`
- System design specifications
- Technology stack decisions
- Technical constraints and preferences

**Quality Gates:**

- [ ] Architecture follows template structure
- [ ] All technical requirements addressed
- [ ] Technology choices justified
- [ ] Scalability considerations included

**Handoff to Karen (Designer)**: Architecture specifications and technical constraints

### **Phase 4: Karen (Designer) - UX/UI Design**

**Input**: Architecture specifications from Timmy
**Duration**: 2-4 sessions
**Focus**: User experience and interface design

**Key Activities:**

1. Review architecture and technical constraints
2. Create UX/UI specifications
3. Design component library
4. Define design system

**Required Deliverables:**

- UX/UI specifications (`/method/bmad/artifacts/designs/`) using `front-end-spec-tmpl.md`
- Design system documentation
- Component specifications
- User flow definitions

**Quality Gates:**

- [ ] Design specs follow template structure
- [ ] All user flows defined
- [ ] Component library established
- [ ] Accessibility considerations included

**Handoff to Jimmy (PO)**: Design specifications and component definitions

---

### **Phase 5: Jimmy (PO) - Product Validation**

**Input**: Design specifications from Karen
**Duration**: 1-2 sessions
**Focus**: Validation and prioritization

**Key Activities:**

1. Validate design alignment with business goals
2. Prioritize features and stories
3. Define acceptance criteria
4. Create product backlog

**Required Deliverables:**

- Prioritized product backlog
- Validated story priorities
- Acceptance criteria definitions
- Release planning documentation

**Quality Gates:**

- [ ] All artifacts validated for alignment
- [ ] Stories properly prioritized
- [ ] Acceptance criteria defined
- [ ] Release plan established

**Handoff to Fran (SM)**: Validated and prioritized backlog

---

### **Phase 6: Fran (SM) - Story Creation**

**Input**: Prioritized backlog from Jimmy
**Duration**: 1-2 sessions
**Focus**: Detailed story creation and sprint planning

**Key Activities:**

1. Create detailed user stories
2. Define story acceptance criteria
3. Plan sprint structure
4. Prepare stories for development

**Required Deliverables:**

- User stories (`/docs/stories/`) using `story-tmpl.md`
- Sprint planning documentation
- Story acceptance criteria
- Definition of done validation

**Story Format**: `{epic}.{story}.story.md` (e.g., `1.001.story.md`)

**Quality Gates:**

- [ ] Stories follow template structure
- [ ] All stories have acceptance criteria
- [ ] Stories are properly sized
- [ ] Sprint plan established

**Handoff to Rodney (Developer)**: Ready-to-develop user stories

---

### **Phase 7: Rodney (Developer) - Implementation**

**Input**: Ready stories from Fran
**Duration**: Ongoing development cycles
**Focus**: Feature implementation and testing

**Key Activities:**

1. Implement user stories
2. Write comprehensive tests
3. Update story status
4. Create implementation documentation

**Required Deliverables:**

- Working code implementation (`/src/`)
- Comprehensive test coverage (`/tests/`)
- Implementation documentation
- Updated story status

**Quality Gates:**

- [ ] All acceptance criteria met
- [ ] Tests passing with good coverage
- [ ] Code follows project standards
- [ ] Documentation updated

**Handoff**: Completed features ready for next sprint cycle

## 🔄 **WORKFLOW MANAGEMENT**

### **Quality Gates Between Phases**

Each phase transition requires validation that all deliverables are complete and meet quality standards before proceeding to the next agent.

### **Artifact Flow**

All artifacts must be properly handed off between agents with clear documentation of what was delivered and what the next agent should focus on.

### **Iterative Refinement**

While the workflow is sequential, agents may need to iterate on deliverables based on feedback from subsequent phases.

### **Documentation Standards**

All deliverables must follow BMAD Method templates and be stored in designated locations according to compliance rules.
