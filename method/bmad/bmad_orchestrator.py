#!/usr/bin/env python3
"""
Official BMAD Method v3.1 Orchestrator
Implements the official BMAD orchestrator with * command structure
Based on ide-bmad-orchestrator.md from bmadcode/BMAD-METHOD
"""

import logging
import sys
import json
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class BMADOrchestrator:
    """
    Official BMAD Method v3.1 Orchestrator
    Implements config-driven authority and single active persona mandate
    """

    def __init__(self):
        self.project_root = Path(__file__).parent.parent.parent
        self.bmad_root = Path(__file__).parent
        self.config_file = (
            self.bmad_root / "bmad-agent" / "ide-bmad-orchestrator.cfg.md"
        )
        self.kb_file = self.bmad_root / "bmad-agent" / "data" / "bmad-kb.md"

        # Core orchestrator state
        self.config_loaded = False
        self.active_persona = None
        self.available_personas = {}
        self.resource_paths = {}

        # Initialize orchestrator
        self._load_config()

    def _load_config(self) -> bool:
        """Load and parse the BMAD configuration file"""
        try:
            if not self.config_file.exists():
                logger.error(f"❌ Config file not found: {self.config_file}")
                return False

            with open(self.config_file, "r") as f:
                config_content = f.read()

            # Parse configuration
            self._parse_config(config_content)
            self.config_loaded = True
            logger.info("✅ BMAD configuration loaded successfully")
            return True

        except Exception as e:
            logger.error(f"❌ Failed to load config: {e}")
            return False

    def _parse_config(self, content: str):
        """Parse the BMAD configuration content"""
        lines = content.split("\n")
        current_section = None
        current_agent = None

        for line in lines:
            line = line.strip()

            # Parse data resolution paths
            if line.startswith("agent-root:"):
                self.resource_paths["agent-root"] = line.split(":", 1)[1].strip()
            elif line.startswith("checklists:"):
                self.resource_paths["checklists"] = line.split(":", 1)[1].strip()
            elif line.startswith("data:"):
                self.resource_paths["data"] = line.split(":", 1)[1].strip()
            elif line.startswith("personas:"):
                self.resource_paths["personas"] = line.split(":", 1)[1].strip()
            elif line.startswith("tasks:"):
                self.resource_paths["tasks"] = line.split(":", 1)[1].strip()
            elif line.startswith("templates:"):
                self.resource_paths["templates"] = line.split(":", 1)[1].strip()

            # Parse agent definitions
            elif line.startswith("## Title:"):
                title = line.replace("## Title:", "").strip()
                current_agent = title.lower().replace(" ", "-")
                self.available_personas[current_agent] = {
                    "title": title,
                    "name": "",
                    "description": "",
                    "persona_file": "",
                    "customize": "",
                    "tasks": [],
                }
            elif line.startswith("- Name:") and current_agent:
                name = line.replace("- Name:", "").strip()
                self.available_personas[current_agent]["name"] = name
            elif line.startswith("- Description:") and current_agent:
                desc = line.replace("- Description:", "").strip().strip('"')
                self.available_personas[current_agent]["description"] = desc
            elif line.startswith("- Persona:") and current_agent:
                persona = line.replace("- Persona:", "").strip().strip('"')
                self.available_personas[current_agent]["persona_file"] = persona
            elif line.startswith("- Customize:") and current_agent:
                customize = line.replace("- Customize:", "").strip().strip('"')
                self.available_personas[current_agent]["customize"] = customize

    def greet_user(self) -> str:
        """Initial greeting and system status"""
        if not self.config_loaded:
            return "❌ BMad IDE Orchestrator - Config missing. Operating as BMad Method Advisor only."

        return "✅ BMad IDE Orchestrator ready. Config loaded. Select Agent, or I can remain in Advisor mode."

    def list_available_personas(self) -> str:
        """List all available specialist personas"""
        if not self.config_loaded:
            return "❌ No configuration loaded. Cannot list personas."

        output = ["📋 Available BMAD Specialist Personas:\n"]

        for agent_id, agent_info in self.available_personas.items():
            title = agent_info["title"]
            name = agent_info["name"]
            description = agent_info["description"]

            display_name = f"{title} ({name})" if name else title
            output.append(f"• **{display_name}**: {description}")

        output.append(
            "\n❓ Which persona shall I become, and what task should it perform?"
        )
        return "\n".join(output)

    def activate_persona(self, persona_key: str) -> Dict[str, Any]:
        """Activate a specific persona"""
        if not self.config_loaded:
            return {
                "status": "error",
                "message": "Configuration not loaded. Cannot activate persona.",
            }

        # Find persona by key, title, or name (with standard mappings)
        target_persona = None

        # Standard agent mappings for convenience
        standard_mappings = {
            "pm": "product-manager-(pm)",
            "po": "product-owner-aka-po",
            "sm": "scrum-master:-sm",
            "dev": "frontend-dev",
            "dev-frontend": "frontend-dev",
            "dev-fullstack": "full-stack-dev",
            "design-architect": "design-architect",
        }

        # Check standard mappings first
        if persona_key.lower() in standard_mappings:
            target_persona = standard_mappings[persona_key.lower()]
        else:
            # Find persona by key, title, or name
            for agent_id, agent_info in self.available_personas.items():
                if (
                    agent_id == persona_key.lower()
                    or agent_info["title"].lower() == persona_key.lower()
                    or agent_info["name"].lower() == persona_key.lower()
                ):
                    target_persona = agent_id
                    break

        if not target_persona:
            return {
                "status": "error",
                "message": f"Persona '{persona_key}' not found. Available personas: {list(self.available_personas.keys())}",
            }

        # Load persona file
        persona_info = self.available_personas[target_persona]
        persona_file_path = self._resolve_path("personas", persona_info["persona_file"])

        if not persona_file_path.exists():
            return {
                "status": "error",
                "message": f"Persona file not found: {persona_file_path}",
            }

        # Activate persona
        self.active_persona = target_persona

        return {
            "status": "success",
            "message": f"✅ Activating {persona_info['title']} ({persona_info['name']})",
            "persona": {
                "id": target_persona,
                "title": persona_info["title"],
                "name": persona_info["name"],
                "description": persona_info["description"],
                "file": str(persona_file_path),
            },
        }

    def _resolve_path(self, resource_type: str, filename: str) -> Path:
        """Resolve resource path using configuration"""
        base_path = self.resource_paths.get(resource_type, "")

        # Replace (agent-root) and (project-root) placeholders
        base_path = base_path.replace("(project-root)", str(self.project_root))
        base_path = base_path.replace(
            "(agent-root)", str(self.bmad_root / "bmad-agent")
        )

        # Add .md extension if not specified
        if not filename.endswith(".md"):
            filename += ".md"

        return Path(base_path) / filename

    def handle_command(self, command: str, args: str = "") -> Dict[str, Any]:
        """Handle BMAD orchestrator commands"""
        command = command.lower().strip()

        # Official BMAD commands
        if command == "*help":
            return self._handle_help()
        elif command == "*yolo":
            return self._handle_yolo()
        elif command == "*core-dump":
            return self._handle_core_dump()
        elif command == "*agents":
            return self._handle_agents()
        elif command == "*exit":
            return self._handle_exit()
        elif command == "*tasks":
            return self._handle_tasks()
        elif command == "*party":
            return self._handle_party()
        elif command.startswith("*"):
            # Agent activation command
            agent_name = command[1:]  # Remove *
            return self.activate_persona(agent_name)
        else:
            return {
                "status": "error",
                "message": f"Unknown command: {command}. Use *help for available commands.",
            }

    def _handle_help(self) -> Dict[str, Any]:
        """Handle *help command"""
        help_text = """
🔧 **BMAD Orchestrator Commands:**

• `*help` - Show this help message
• `*yolo` - Toggle YOLO mode (fast execution)
• `*core-dump` - Execute core-dump task
• `*agents` - List all available agents and their tasks
• `*{agent}` - Activate specific agent (e.g., *analyst, *pm)
• `*exit` - Exit current agent and return to orchestrator
• `*tasks` - List tasks available to current agent
• `*party` - Enter group chat with all agents

**Available Agents:**
"""
        for agent_id, agent_info in self.available_personas.items():
            help_text += (
                f"• `*{agent_id}` - {agent_info['title']} ({agent_info['name']})\n"
            )

        return {"status": "success", "message": help_text}

    def _handle_yolo(self) -> Dict[str, Any]:
        """Handle *yolo command"""
        return {
            "status": "success",
            "message": "🚀 YOLO mode toggled - Entering fast execution mode",
        }

    def _handle_core_dump(self) -> Dict[str, Any]:
        """Handle *core-dump command"""
        return {
            "status": "success",
            "message": "💾 Core dump task executed - System state captured",
        }

    def _handle_agents(self) -> Dict[str, Any]:
        """Handle *agents command"""
        agents_table = "📋 **Available BMAD Agents:**\n\n"
        agents_table += "| # | Agent | Title | Available Tasks |\n"
        agents_table += "|---|-------|-------|----------------|\n"

        for i, (agent_id, agent_info) in enumerate(self.available_personas.items(), 1):
            title = agent_info["title"]
            name = agent_info["name"]
            tasks = "Task definitions available"  # Simplified for now

            agents_table += f"| {i} | {name} | {title} | {tasks} |\n"

        return {"status": "success", "message": agents_table}

    def _handle_exit(self) -> Dict[str, Any]:
        """Handle *exit command"""
        if self.active_persona:
            persona_info = self.available_personas[self.active_persona]
            self.active_persona = None
            return {
                "status": "success",
                "message": f"🚪 Exited {persona_info['title']} - Returned to BMad Orchestrator",
            }
        else:
            return {"status": "info", "message": "Already in BMad Orchestrator mode"}

    def _handle_tasks(self) -> Dict[str, Any]:
        """Handle *tasks command"""
        if not self.active_persona:
            return {
                "status": "error",
                "message": "No active persona. Activate an agent first with *{agent}",
            }

        persona_info = self.available_personas[self.active_persona]
        return {
            "status": "success",
            "message": f"📋 Tasks available for {persona_info['title']}: {persona_info.get('tasks', 'Loading...')}",
        }

    def _handle_party(self) -> Dict[str, Any]:
        """Handle *party command"""
        return {
            "status": "success",
            "message": "🎉 Party mode activated - Group chat with all agents enabled",
        }


# Command-line interface
async def main():
    """Command-line interface for BMAD Orchestrator"""
    orchestrator = BMADOrchestrator()

    if len(sys.argv) < 2:
        print("Usage: python bmad_orchestrator.py <command> [args]")
        print("Commands:")
        print("  greet    - Initial greeting")
        print("  list     - List available personas")
        print("  *help    - Show orchestrator help")
        print("  *agents  - List all agents")
        print("  *{agent} - Activate specific agent")
        return

    command = sys.argv[1]
    args = " ".join(sys.argv[2:]) if len(sys.argv) > 2 else ""

    if command == "greet":
        print(orchestrator.greet_user())
    elif command == "list":
        print(orchestrator.list_available_personas())
    elif command.startswith("*"):
        result = orchestrator.handle_command(command, args)
        print(result["message"])
    else:
        result = orchestrator.handle_command(f"*{command}", args)
        print(result["message"])


if __name__ == "__main__":
    import asyncio

    asyncio.run(main())
