#!/usr/bin/env python3
"""
BMAD Method Command System
Implements the core /bmad commands for Business Method Analysis and Development
"""

import asyncio
import json
import logging
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any

# Setup logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Integration bridge availability flag
bridge_available = False
integration_bridge = None

# Try to import bridge for Vybe Method integration
try:
    sys.path.append(str(Path(__file__).parent.parent))
    from bmad_vybe_bridge import BMADVybeIntegrationBridge
    bridge_available = True
    logger.info("BMAD-Vybe Integration Bridge available")
except ImportError:
    logger.warning("BMAD-Vybe Integration Bridge not available")
    BMADVybeIntegrationBridge = None


class BMADCommandSystem:
    """
    BMAD Method Command System
    Implements /bmad commands for traditional human-AI collaboration workflow
    """

    def __init__(self):
        self.initialized = False
        self.project_root = Path(__file__).parent.parent.parent
        self.bmad_root = Path(__file__).parent
        self.system_status = {
            "agents_available": 7,
            "last_health_check": None,
            "current_phase": None,
            "artifacts_created": 0,
        }

        # BMAD Agent definitions (Official BMAD Method v3.1 Compliant - IDE Version)
        self.bmad_agents = {
            "analyst": {
                "name": "Wendy - Analyst",
                "role": "Research, brainstorming, requirements gathering, project briefs",
                "avatar": "🔍",
                "file": "analyst.md",
                "expertise": [
                    "market research",
                    "business analysis",
                    "requirements gathering",
                    "project briefs",
                ],
            },
            "pm": {
                "name": "Bill - Product Manager",
                "role": "PRD creation and maintenance",
                "avatar": "📋",
                "file": "pm.md",
                "expertise": [
                    "product requirements",
                    "PRD maintenance",
                    "product strategy",
                ],
            },
            "architect": {
                "name": "Timmy - Architect",
                "role": "Architecture generation, story planning",
                "avatar": "🏗️",
                "file": "architect.md",
                "expertise": [
                    "system architecture",
                    "story planning",
                    "technical design",
                ],
            },
            "design-architect": {
                "name": "Karen - Design Architect",
                "role": "Frontend architecture and UI design",
                "avatar": "🎨",
                "file": "design-architect.md",
                "expertise": [
                    "frontend architecture",
                    "UI design",
                    "web application design",
                ],
            },
            "po": {
                "name": "Jimmy - Product Owner",
                "role": "PRD maintenance, course correction, story drafting",
                "avatar": "✅",
                "file": "po.md",
                "expertise": ["PRD maintenance", "course correction", "story creation"],
            },
            "sm": {
                "name": "Fran - Scrum Master",
                "role": "Story generation, sprint management",
                "avatar": "📊",
                "file": "sm.md",
                "expertise": [
                    "story generation",
                    "sprint management",
                    "agile processes",
                ],
            },
            "dev-frontend": {
                "name": "Rodney - Frontend Developer",
                "role": "Frontend development (NextJS, React, TypeScript)",
                "avatar": "⚡",
                "file": "dev.ide.md",
                "expertise": ["NextJS", "React", "TypeScript", "HTML", "Tailwind"],
            },
            "dev-fullstack": {
                "name": "James - Full Stack Developer",
                "role": "Full stack development",
                "avatar": "🔧",
                "file": "dev.ide.md",
                "expertise": [
                    "full stack development",
                    "backend",
                    "frontend",
                    "databases",
                ],
            },
            "platform-engineer": {
                "name": "Alex - Platform Engineer",
                "role": "DevOps, infrastructure, and platform engineering",
                "avatar": "🔧",
                "file": "devops-pe.ide.md",
                "expertise": [
                    "cloud platforms",
                    "infrastructure automation",
                    "CI/CD pipelines",
                    "container orchestration",
                ],
            },
        }

        logger.info("BMADCommandSystem created")

        # Initialize integration bridge if available
        self.integration_bridge = None
        if bridge_available and BMADVybeIntegrationBridge:
            try:
                self.integration_bridge = BMADVybeIntegrationBridge()
                logger.info("BMAD-Vybe Integration Bridge initialized")
            except Exception as e:
                logger.warning(f"Failed to initialize integration bridge: {e}")
                self.integration_bridge = None

    async def bmad_start(self) -> Dict[str, Any]:
        """
        bmad start → Initialize BMAD Method system
        """
        logger.info("🚀 Initializing BMAD Method system...")

        try:
            # Verify BMAD structure exists
            bmad_structure_valid = self._verify_bmad_structure()

            # Check agent files
            agents_available = self._check_agent_files()

            # Initialize artifacts directory
            artifacts_dir = self.bmad_root / "artifacts"
            artifacts_dir.mkdir(exist_ok=True)

            # Create subdirectories for different artifact types
            for subdir in [
                "analysis",
                "requirements",
                "architecture",
                "design",
                "stories",
                "implementation",
            ]:
                (artifacts_dir / subdir).mkdir(exist_ok=True)

            self.initialized = True
            self.system_status["last_health_check"] = datetime.now().isoformat()
            self.system_status["agents_available"] = len(agents_available)

            result = {
                "status": "success",
                "message": "BMAD Method system initialized successfully",
                "components": {
                    "bmad_structure_valid": bmad_structure_valid,
                    "agents_available": agents_available,
                    "artifacts_directory": str(artifacts_dir),
                    "workflow_ready": True,
                },
                "next_steps": [
                    'Run "bmad status" to check system health',
                    'Run "bmad analyze" to start project analysis',
                    'Use "bmad analyst" to begin with Wendy for project analysis',
                ],
                "timestamp": datetime.now().isoformat(),
            }

            logger.info("🎉 BMAD Method system ready!")
            return result

        except Exception as e:
            error_msg = f"Failed to initialize BMAD Method system: {e}"
            logger.error(f"❌ {error_msg}")
            return {
                "status": "error",
                "message": error_msg,
                "timestamp": datetime.now().isoformat(),
            }

    async def bmad_status(self) -> Dict[str, Any]:
        """
        bmad status → Check system health and agent availability
        """
        logger.info("🔍 Checking BMAD Method system status...")

        # Auto-initialize if not already initialized
        if not self.initialized:
            await self.bmad_start()

        try:
            # Check agent files
            agent_status = {}
            for agent_id, agent_info in self.bmad_agents.items():
                agent_file = (
                    self.bmad_root / "bmad-agent" / "personas" / agent_info["file"]
                )
                agent_status[agent_id] = {
                    "name": agent_info["name"],
                    "role": agent_info["role"],
                    "avatar": agent_info["avatar"],
                    "file_exists": agent_file.exists(),
                    "expertise": agent_info["expertise"],
                }

            # Check artifacts
            artifacts_dir = self.bmad_root / "artifacts"
            artifact_counts = {}
            if artifacts_dir.exists():
                for subdir in [
                    "analysis",
                    "requirements",
                    "architecture",
                    "design",
                    "stories",
                    "implementation",
                ]:
                    subdir_path = artifacts_dir / subdir
                    if subdir_path.exists():
                        artifact_counts[subdir] = len(list(subdir_path.glob("*.md")))
                    else:
                        artifact_counts[subdir] = 0

            # Check BMAD knowledge base
            kb_file = self.bmad_root / "bmad-agent" / "data" / "bmad-kb.md"
            kb_available = kb_file.exists()

            status = {
                "system_initialized": self.initialized,
                "timestamp": datetime.now().isoformat(),
                "agents": agent_status,
                "artifacts": {
                    "directory_exists": artifacts_dir.exists(),
                    "counts": artifact_counts,
                    "total_artifacts": (
                        sum(artifact_counts.values()) if artifact_counts else 0
                    ),
                },
                "knowledge_base": {
                    "available": kb_available,
                    "path": str(kb_file) if kb_available else None,
                },
                "workflow_status": {
                    "current_phase": self.system_status.get(
                        "current_phase", "Not started"
                    ),
                    "ready_for_analysis": True,
                },
            }

            # Determine overall health
            agents_ready = sum(
                1 for agent in agent_status.values() if agent["file_exists"]
            )
            if self.initialized and agents_ready >= 6 and kb_available:
                status["overall_health"] = "healthy"
                status["message"] = "All BMAD components operational"
            elif self.initialized and agents_ready >= 4:
                status["overall_health"] = "partial"
                status["message"] = "Core BMAD components available"
            else:
                status["overall_health"] = "unhealthy"
                status["message"] = "BMAD system needs initialization"

            logger.info(f"📊 BMAD System health: {status['overall_health']}")
            return status

        except Exception as e:
            error_msg = f"Failed to check BMAD system status: {e}"
            logger.error(f"❌ {error_msg}")
            return {
                "status": "error",
                "message": error_msg,
                "timestamp": datetime.now().isoformat(),
            }

    async def bmad_analyze(
        self, feature_proposals: List[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        bmad analyze → Analyze feature proposals using BMAD methodology
        """
        logger.info("🔍 Starting BMAD analysis of feature proposals...")

        # Auto-initialize if not already initialized
        if not self.initialized:
            await self.bmad_start()

        try:
            analysis_id = f"bmad_analysis_{int(time.time())}"

            # Default feature proposals if none provided
            if not feature_proposals:
                feature_proposals = [
                    {
                        "name": "Full Vybe Button",
                        "description": 'Rename from "VYBE" to "Vybe" - Accept URL or text input → run through autonomous Vybe Method',
                        "location": "Quick Collaboration container",
                        "functionality": [
                            "Generate news content for community page",
                            "Create non-plagiarized article/blog post",
                            "Generate course material (if content is suitable)",
                        ],
                        "priority": "high",
                        "complexity": "medium",
                    },
                    {
                        "name": "Vybe Qube Generator Button",
                        "description": "Accept English text idea → process through complete Vybe Method → create Vybe Qube output",
                        "functionality": [
                            "Text input processing",
                            "Complete Vybe Method workflow execution",
                            "Vybe Qube generation and deployment",
                        ],
                        "priority": "high",
                        "complexity": "high",
                    },
                ]

            # Perform BMAD analysis
            analysis_results = {
                "analysis_id": analysis_id,
                "timestamp": datetime.now().isoformat(),
                "feature_proposals": feature_proposals,
                "bmad_analysis": {},
            }

            # Analyze each feature through BMAD lens
            for i, proposal in enumerate(feature_proposals):
                feature_analysis = await self._analyze_feature_with_bmad(
                    proposal, i + 1
                )
                analysis_results["bmad_analysis"][f"feature_{i + 1}"] = feature_analysis

            # Generate recommendations
            recommendations = self._generate_bmad_recommendations(feature_proposals)
            analysis_results["recommendations"] = recommendations

            # Save analysis to artifacts
            await self._save_analysis_artifact(analysis_results)

            # Update system status
            self.system_status["current_phase"] = "Analysis Complete"
            self.system_status["artifacts_created"] += 1

            logger.info(
                f"✅ BMAD analysis completed for {len(feature_proposals)} features"
            )
            return {
                "status": "success",
                "analysis_id": analysis_id,
                "features_analyzed": len(feature_proposals),
                "analysis_results": analysis_results,
                "next_steps": [
                    "Review analysis results",
                    "Activate appropriate agents based on recommendations",
                    "Begin implementation planning",
                ],
                "timestamp": datetime.now().isoformat(),
            }

        except Exception as e:
            error_msg = f"BMAD analysis failed: {e}"
            logger.error(f"❌ {error_msg}")
            return {
                "status": "error",
                "message": error_msg,
                "timestamp": datetime.now().isoformat(),
            }

    def _verify_bmad_structure(self) -> bool:
        """Verify BMAD directory structure exists"""
        required_dirs = [
            self.bmad_root / "bmad-agent",
            self.bmad_root / "bmad-agent" / "personas",
            self.bmad_root / "bmad-agent" / "data",
            self.bmad_root / "bmad-agent" / "tasks",
            self.bmad_root / "bmad-agent" / "templates",
            self.bmad_root / "bmad-agent" / "checklists",
        ]

        return all(dir_path.exists() for dir_path in required_dirs)

    def _check_agent_files(self) -> List[str]:
        """Check which agent files are available"""
        available_agents = []
        personas_dir = self.bmad_root / "bmad-agent" / "personas"

        for agent_id, agent_info in self.bmad_agents.items():
            agent_file = personas_dir / agent_info["file"]
            if agent_file.exists():
                available_agents.append(agent_id)

        return available_agents

    async def _analyze_feature_with_bmad(
        self, proposal: Dict[str, Any], feature_num: int
    ) -> Dict[str, Any]:
        """Analyze a feature proposal using BMAD methodology"""

        analysis = {
            "feature_name": proposal["name"],
            "business_analysis": {
                "market_fit": "High - Aligns with AI education and autonomous development trends",
                "user_value": "High - Provides immediate value through content generation",
                "revenue_impact": "Medium-High - Supports subscription model and user engagement",
                "risk_assessment": "Medium - Technical complexity and integration challenges",
            },
            "technical_analysis": {
                "complexity": proposal.get("complexity", "medium"),
                "integration_points": [
                    "Existing Vybe Method infrastructure",
                    "Content generation pipeline",
                    "UI/UX integration",
                    "Database storage systems",
                ],
                "dependencies": [
                    "Vybe Method workflow engine",
                    "Web scraping capabilities",
                    "Content processing pipeline",
                    "Authentication system",
                ],
                "estimated_effort": (
                    "2-3 sprints"
                    if proposal.get("complexity") == "high"
                    else "1-2 sprints"
                ),
            },
            "design_considerations": {
                "user_experience": "Must be intuitive and provide clear feedback",
                "accessibility": "Follow WCAG guidelines for inclusive design",
                "responsive_design": "Mobile-first approach for broad accessibility",
                "integration_points": "Seamless integration with existing UI components",
            },
            "implementation_strategy": {
                "phase_1": "Core functionality and backend integration",
                "phase_2": "UI/UX implementation and testing",
                "phase_3": "Performance optimization and monitoring",
                "testing_approach": "Unit tests, integration tests, user acceptance testing",
            },
        }

        return analysis

    def _generate_bmad_recommendations(
        self, proposals: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Generate BMAD-based recommendations for implementation"""

        return {
            "agent_activation_sequence": [
                {
                    "agent": "analyst",
                    "priority": 1,
                    "tasks": [
                        "Detailed market research for Vybe Method features",
                        "User persona analysis and requirements gathering",
                        "Competitive analysis of similar AI tools",
                    ],
                },
                {
                    "agent": "architect",
                    "priority": 2,
                    "tasks": [
                        "Design system architecture for new button functionality",
                        "Plan integration with existing Vybe Method infrastructure",
                        "Define API specifications and data flow",
                    ],
                },
                {
                    "agent": "designer",
                    "priority": 3,
                    "tasks": [
                        "Create UI/UX specifications for new buttons",
                        "Design user interaction flows",
                        "Ensure accessibility and responsive design",
                    ],
                },
                {
                    "agent": "dev",
                    "priority": 4,
                    "tasks": [
                        "Implement backend functionality",
                        "Create frontend components",
                        "Integrate with existing systems",
                        "Write comprehensive tests",
                    ],
                },
            ],
            "implementation_approach": "Iterative development with continuous testing",
            "success_criteria": [
                "Functional buttons with real web scraping/processing capabilities",
                "Seamless integration with existing Vybe Method workflow",
                "Proper content generation and storage systems",
                "Complete testing and documentation",
            ],
            "risk_mitigation": [
                "Start with MVP implementation",
                "Implement comprehensive error handling",
                "Create fallback mechanisms for failed operations",
                "Monitor performance and user feedback",
            ],
        }

    async def _save_analysis_artifact(self, analysis_results: Dict[str, Any]) -> None:
        """Save analysis results as BMAD artifact"""
        artifacts_dir = self.bmad_root / "artifacts" / "analysis"
        artifacts_dir.mkdir(parents=True, exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"vybe_method_feature_analysis_{timestamp}.md"
        filepath = artifacts_dir / filename

        # Create markdown content
        content = f"""# Vybe Method Feature Analysis
**Analysis ID:** {analysis_results['analysis_id']}
**Date:** {analysis_results['timestamp']}
**Agent:** BMAD Analysis System

## Executive Summary

This analysis evaluates proposed Vybe Method functionality enhancements using the BMAD methodology. The analysis covers business impact, technical feasibility, design considerations, and implementation strategy.

## Feature Proposals Analyzed

"""

        for i, proposal in enumerate(analysis_results["feature_proposals"]):
            content += f"""
### {i + 1}. {proposal['name']}

**Description:** {proposal['description']}
**Priority:** {proposal.get('priority', 'medium')}
**Complexity:** {proposal.get('complexity', 'medium')}

**Functionality:**
"""
            for func in proposal.get("functionality", []):
                content += f"- {func}\n"

        content += f"""

## BMAD Analysis Results

{json.dumps(analysis_results['bmad_analysis'], indent=2)}

## Recommendations

{json.dumps(analysis_results['recommendations'], indent=2)}

## Next Steps

1. Review analysis with stakeholders
2. Activate recommended agents in sequence
3. Begin implementation planning
4. Create detailed user stories

---
*Generated by BMAD Analysis System*
"""

        filepath.write_text(content)
        logger.info(f"📄 Analysis artifact saved: {filename}")

    # BMAD-Vybe Integration Bridge Methods
    
    async def bmad_to_vybe_transition(self, agent_name: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Transition from BMAD Method to Vybe Method MAS
        
        Args:
            agent_name: The BMAD agent completing work (analyst, pm, architect, etc.)
            context: Context and artifacts from BMAD workflow
        """
        if not self.integration_bridge:
            return {
                "success": False,
                "error": "Integration bridge not available",
                "message": "BMAD-Vybe integration bridge is not initialized"
            }
        
        logger.info(f"🔄 Transitioning from BMAD {agent_name} to Vybe MAS...")
        
        try:
            # Ensure bridge is initialized
            if not hasattr(self.integration_bridge, 'integration_status'):
                await self.integration_bridge.initialize_bridge()
            
            # Execute transition
            transition_result = await self.integration_bridge.bmad_to_vybe_transition(
                agent_name, context or {}
            )
            
            if transition_result["success"]:
                self.system_status["last_vybe_transition"] = {
                    "agent": agent_name,
                    "timestamp": datetime.now().isoformat(),
                    "vybe_agent": transition_result.get("transition_data", {}).get("target_agent")
                }
                
                logger.info(f"✅ Successfully transitioned to Vybe agent: {transition_result.get('transition_data', {}).get('target_agent')}")
            
            return transition_result
            
        except Exception as e:
            logger.error(f"Transition error: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to transition from BMAD {agent_name} to Vybe MAS"
            }

    async def bmad_hybrid_workflow(self, task_description: str, mode: str = "bmad_first") -> Dict[str, Any]:
        """
        Execute a hybrid BMAD-Vybe workflow
        
        Args:
            task_description: Description of the task to be completed
            mode: "bmad_first", "vybe_first", or "parallel"
        """
        if not self.integration_bridge:
            return {
                "success": False,
                "error": "Integration bridge not available",
                "message": "BMAD-Vybe integration bridge is not initialized"
            }
        
        logger.info(f"🔄 Executing hybrid BMAD-Vybe workflow: {mode}")
        
        try:
            # Ensure bridge is initialized
            if not hasattr(self.integration_bridge, 'integration_status'):
                await self.integration_bridge.initialize_bridge()
            
            # Execute hybrid workflow
            workflow_result = await self.integration_bridge.execute_hybrid_workflow(
                task_description, mode
            )
            
            if workflow_result["success"]:
                self.system_status["last_hybrid_workflow"] = {
                    "task": task_description,
                    "mode": mode,
                    "timestamp": datetime.now().isoformat(),
                    "workflow_id": workflow_result.get("workflow_result", {}).get("workflow_id")
                }
                
                logger.info(f"✅ Hybrid workflow completed: {workflow_result.get('workflow_result', {}).get('workflow_id')}")
            
            return workflow_result
            
        except Exception as e:
            logger.error(f"Hybrid workflow error: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to execute hybrid workflow"
            }

    async def bmad_get_integration_status(self) -> Dict[str, Any]:
        """Get BMAD-Vybe integration status"""
        if not self.integration_bridge:
            return {
                "bridge_available": False,
                "message": "Integration bridge not initialized",
                "bmad_agents": list(self.bmad_agents.keys()),
                "integration_features": [
                    "Agent transitions (BMAD → Vybe)",
                    "Hybrid workflows",
                    "Shared artifacts",
                    "Educational progression"
                ]
            }
        
        try:
            # Get bridge status (sync method)
            bridge_status = self.integration_bridge.get_integration_status()

            # Add BMAD-specific status
            return {
                "bridge_available": True,
                "bmad_system_status": self.system_status,
                "integration_status": bridge_status,
                "available_transitions": [
                    f"{agent} → {self.integration_bridge.agent_mapping.get(agent, 'unknown')}"
                    for agent in self.bmad_agents.keys()
                    if agent in self.integration_bridge.agent_mapping
                ]
            }
            
        except Exception as e:
            logger.error(f"Integration status error: {e}")
            return {
                "bridge_available": True,
                "error": str(e),
                "message": "Failed to get integration status"
            }


# Command-line interface
async def main():
    """Command-line interface for BMAD commands"""
    if len(sys.argv) < 2:
        print("Usage: python bmad_commands.py <command> [args]")
        print("Commands:")
        print("  start    - Initialize BMAD Method system")
        print("  status   - Check system health and agent availability")
        print("  analyze  - Analyze feature proposals using BMAD methodology")
        print("  analyst  - Activate Wendy for project analysis")
        print("  pm       - Switch to Bill for requirements and PRD creation")
        print("  architect- Engage Timmy for technical architecture design")
        print("  design-architect - Work with Karen for frontend architecture")
        print("  po       - Validate with Jimmy for quality and alignment")
        print("  sm       - Generate stories with Fran for development planning")
        print("  dev-frontend - Implement with Rodney for frontend development")
        print("  dev-fullstack - Implement with James for full stack development")
        print("")
        print("Integration Commands:")
        print("  transition <agent> - Transition BMAD agent work to Vybe MAS")
        print("  hybrid <task>      - Execute hybrid BMAD-Vybe workflow")
        print("  integration-status - Get BMAD-Vybe integration status")
        return

    command = sys.argv[1].lower()
    bmad = BMADCommandSystem()

    # Core system commands
    if command == "start":
        result = await bmad.bmad_start()
    elif command == "status":
        result = await bmad.bmad_status()
    elif command == "analyze":
        result = await bmad.bmad_analyze()
    elif command == "transition":
        if len(sys.argv) < 3:
            print("Usage: python bmad_commands.py transition <agent_name>")
            print("Available agents:", list(bmad.bmad_agents.keys()))
            return
        agent_name = sys.argv[2]
        result = await bmad.bmad_to_vybe_transition(agent_name)
    elif command == "hybrid":
        if len(sys.argv) < 3:
            print("Usage: python bmad_commands.py hybrid '<task_description>' [mode]")
            print("Modes: bmad_first (default), vybe_first, parallel")
            return
        task_desc = sys.argv[2]
        mode = sys.argv[3] if len(sys.argv) > 3 else "bmad_first"
        result = await bmad.bmad_hybrid_workflow(task_desc, mode)
    elif command == "integration-status":
        result = await bmad.bmad_get_integration_status()
    else:
        print(f"Command '{command}' not yet implemented")
        return

    # Format and display result
    print(json.dumps(result, indent=2))


if __name__ == "__main__":
    asyncio.run(main())
