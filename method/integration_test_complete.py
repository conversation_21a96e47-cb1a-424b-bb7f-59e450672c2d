# BMAD-Vybe Integration Test Suite
# Complete test of the BMAD Method v3.1 → Vybe Method MAS integration

import sys
import os
from pathlib import Path

# Add method directory to path
method_dir = Path(__file__).parent
sys.path.insert(0, str(method_dir))
sys.path.insert(0, str(method_dir / "bmad"))
sys.path.insert(0, str(method_dir / "vybe"))

def test_bridge_integration():
    """Test the core bridge functionality"""
    print("🔗 Testing BMAD-Vybe Integration Bridge")
    
    try:
        from bmad_vybe_bridge import BMADVybeIntegrationBridge
        bridge = BMADVybeIntegrationBridge()
        
        # Test 1: Bridge Status
        status = bridge.get_integration_status()
        assert status["bridge_available"] == True
        print("✅ Bridge Status: Available")
        
        # Test 2: Agent Mapping
        assert len(bridge.agent_mapping) == 8
        assert bridge.agent_mapping["analyst"] == "vyba"
        assert bridge.agent_mapping["pm"] == "qubert"
        print("✅ Agent Mapping: Complete (8 mappings)")
        
        # Test 3: BMAD → Vybe Transition
        context = {
            "active_agents": ["analyst", "pm", "architect"],
            "project": "test_integration"
        }
        transition = bridge.bmad_to_vybe_transition(context)
        assert transition["success"] == True
        print("✅ BMAD → Vybe Transition: Success")
        
        # Test 4: Vybe → BMAD Handoff
        deliverables = {
            "agents": ["vyba", "qubert"],
            "vyba": {"analysis": "completed"},
            "qubert": {"plan": "ready"}
        }
        handoff = bridge.vybe_to_bmad_handoff(deliverables)
        assert handoff["success"] == True
        print("✅ Vybe → BMAD Handoff: Success")
        
        # Test 5: Hybrid Workflow
        task = {"description": "Test hybrid workflow"}
        hybrid = bridge.bmad_hybrid_workflow(task, "bmad_first")
        assert hybrid["success"] == True
        print("✅ Hybrid Workflow: Success")
        
        return True
        
    except Exception as e:
        print(f"❌ Bridge Integration Test Failed: {e}")
        return False

def test_vybe_commands():
    """Test Vybe command integration"""
    print("\n🤖 Testing Vybe Commands Integration")
    
    try:
        from vybe_commands import VybeMethodCommands
        vybe = VybeMethodCommands()
        
        # Test bridge status
        status = vybe.bridge_status()
        print("✅ Vybe Bridge Status: Available")
        
        # Test autonomous workflow
        result = vybe.autonomous_workflow("web", "medium")
        assert result["status"] == "success"
        print("✅ Autonomous Workflow: Success")
        
        # Test agent status
        agents = vybe.agent_status()
        assert len(agents["agents"]) == 7
        print("✅ Agent Status: 7 agents active")
        
        return True
        
    except Exception as e:
        print(f"❌ Vybe Commands Test Failed: {e}")
        return False

def test_bmad_commands():
    """Test BMAD command integration"""
    print("\n📋 Testing BMAD Commands Integration")
    
    try:
        from bmad_commands import BMADCommandSystem
        bmad = BMADCommandSystem()
        
        # Test status
        status = bmad.bmad_status()
        print("✅ BMAD Status: Available")
        
        # Test bridge detection
        if hasattr(bmad, 'bridge') and bmad.bridge:
            print("✅ BMAD Bridge Integration: Detected")
        else:
            print("⚠️  BMAD Bridge Integration: Not detected (expected)")
        
        return True
        
    except Exception as e:
        print(f"❌ BMAD Commands Test Failed: {e}")
        return False

def main():
    """Run complete integration test suite"""
    print("🧪 BMAD-Vybe Integration Test Suite")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 3
    
    # Test 1: Bridge Integration
    if test_bridge_integration():
        tests_passed += 1
    
    # Test 2: Vybe Commands
    if test_vybe_commands():
        tests_passed += 1
    
    # Test 3: BMAD Commands
    if test_bmad_commands():
        tests_passed += 1
    
    # Summary
    print("\n" + "=" * 50)
    print(f"🎯 Test Results: {tests_passed}/{total_tests} passed")
    
    if tests_passed == total_tests:
        print("🎉 ALL TESTS PASSED! Integration is ready.")
        print("\nNext Steps:")
        print("1. Test with SvelteKit frontend")
        print("2. Document usage examples")
        print("3. Create user guides")
        return True
    else:
        print("⚠️  Some tests failed. Check errors above.")
        return False

if __name__ == "__main__":
    main()
