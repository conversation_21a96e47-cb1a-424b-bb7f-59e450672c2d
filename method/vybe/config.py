"""
Vybe Configuration System - Enterprise-grade configuration management for Vybe Method
Supports TOML configuration files, environment variables, validation, and security
Includes BMad Method knowledge base integration for seamless evolution
"""

import os
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field

try:
    import tomllib  # Python 3.11+
except ImportError:
    try:
        import tomli as tomllib  # Fallback for older Python versions
    except ImportError:
        tomllib = None

# BMad Method Knowledge Base Integration
BMAD_KNOWLEDGE_PATH = Path(__file__).parent / "knowledge"

@dataclass
class AgentConfig:
    """Configuration for individual Vybe agents"""
    name: str
    model: str = "gpt-4"
    max_tokens: int = 4000
    temperature: float = 0.1
    enabled: bool = True
    specialization: List[str] = field(default_factory=list)
    tools: List[str] = field(default_factory=list)
    memory_limit: int = 1000  # Number of interactions to remember
    context_window: int = 8000  # Tokens
    
    
@dataclass
class LLMConfig:
    """Configuration for LLM providers and models"""
    provider: str = "openai"  # openai, anthropic, local, ollama
    model: str = "gpt-4"
    api_key: Optional[str] = None
    api_url: Optional[str] = None
    max_tokens: int = 4000
    temperature: float = 0.1
    timeout: int = 60
    retry_attempts: int = 3
    rate_limit: int = 60  # requests per minute
    
    # Local LLM settings
    local_model_path: Optional[str] = None
    gpu_layers: int = 0  # Number of layers to offload to GPU
    context_length: int = 4096
    batch_size: int = 512


@dataclass
class ContextConfig:
    """Configuration for context engine and memory management"""
    max_context_tokens: int = 200000  # Unlimited local processing target
    chunk_size: int = 1000
    overlap_size: int = 200
    similarity_threshold: float = 0.7
    
    # Vector database settings
    vector_db_provider: str = "qdrant"  # qdrant, chroma, faiss
    vector_db_path: str = "./data/vector_db"
    embedding_model: str = "all-MiniLM-L6-v2"
    embedding_dimension: int = 384
    
    # Real-time indexing
    file_watch_enabled: bool = True
    index_extensions: List[str] = field(default_factory=lambda: [
        ".py", ".js", ".ts", ".md", ".txt", ".json", ".yaml", ".yml"
    ])
    exclude_patterns: List[str] = field(default_factory=lambda: [
        "node_modules", ".git", "__pycache__", ".venv", "venv"
    ])
    
    # Cache settings
    cache_enabled: bool = True
    cache_ttl: int = 3600  # seconds
    cache_size: int = 1000  # number of cached items


@dataclass
class ConsensusConfig:
    """Configuration for consensus framework and guardrails"""
    min_consensus_threshold: float = 0.7  # Minimum agreement required
    max_voting_rounds: int = 3
    timeout_per_round: int = 30  # seconds
    
    # Quality gates
    code_quality_threshold: float = 0.8
    test_coverage_threshold: float = 0.8
    documentation_threshold: float = 0.7
    
    # Validation layers
    syntax_validation: bool = True
    semantic_validation: bool = True
    security_validation: bool = True
    performance_validation: bool = True
    
    # Human escalation
    human_escalation_threshold: float = 0.5
    escalation_timeout: int = 300  # seconds
    

@dataclass
class MASConfig:
    """Configuration for Multi-Agent System coordination"""
    max_concurrent_agents: int = 5
    task_timeout: int = 300  # seconds
    coordination_interval: int = 10  # seconds
    
    # Agent lifecycle
    agent_startup_timeout: int = 30
    agent_shutdown_timeout: int = 10
    health_check_interval: int = 60
    
    # Task distribution
    load_balancing: str = "round_robin"  # round_robin, priority, workload
    task_queue_size: int = 100
    priority_levels: int = 5


@dataclass
class SecurityConfig:
    """Configuration for security and privacy settings"""
    encryption_enabled: bool = True
    api_key_encryption: bool = True
    data_retention_days: int = 30
    
    # Access control
    require_authentication: bool = False
    session_timeout: int = 3600  # seconds
    
    # Data privacy
    anonymize_logs: bool = True
    secure_storage: bool = True
    local_only: bool = True  # Never send data to external services


@dataclass
class LoggingConfig:
    """Configuration for logging and monitoring"""
    level: str = "INFO"
    file_path: str = "./logs/vybe.log"
    max_size: int = 10  # MB
    backup_count: int = 5
    
    # Structured logging
    json_format: bool = True
    include_context: bool = True
    
    # Monitoring
    metrics_enabled: bool = True
    performance_tracking: bool = True
    error_tracking: bool = True


@dataclass
class UIConfig:
    """Configuration for user interface and interactions"""
    theme: str = "dark"
    auto_save: bool = True
    auto_save_interval: int = 30  # seconds
    
    # Editor integration
    vscode_integration: bool = True
    syntax_highlighting: bool = True
    code_completion: bool = True
    
    # Notifications
    desktop_notifications: bool = True
    sound_notifications: bool = False
    notification_timeout: int = 5  # seconds


class VybeConfig:
    """
    Enterprise-grade configuration management for Vybe Method
    
    Supports:
    - TOML configuration files
    - Environment variable overrides
    - Validation and type checking
    - Security and encryption
    - Agent-specific configurations
    """
    
    def __init__(self, config_path: Optional[Union[str, Path]] = None):
        """
        Initialize Vybe configuration
        
        Args:
            config_path: Path to TOML configuration file
        """
        self.config_path: Optional[Path] = Path(config_path) if config_path else None
        self.logger = logging.getLogger(__name__)
        
        # Initialize configuration sections
        self.llm = LLMConfig()
        self.context = ContextConfig()
        self.consensus = ConsensusConfig()
        self.mas = MASConfig()
        self.security = SecurityConfig()
        self.logging = LoggingConfig()
        self.ui = UIConfig()
        self.agents: Dict[str, AgentConfig] = {}
        
        # Only load configuration if explicitly requested
        self._config_loaded = False
    
    def load_config(self):
        """Load configuration from file - call this explicitly"""
        if not self._config_loaded:
            if self.config_path is None:
                self.config_path = self._find_config_file()
            self._load_config()
            self._load_env_overrides()
            self._validate_config()
            self._config_loaded = True
        
    def _find_config_file(self) -> Path:
        """Find configuration file in standard locations"""
        search_paths = [
            Path("./vybe.toml"),
            Path("./config/vybe.toml"),
            Path("~/.config/vybe/vybe.toml").expanduser(),
            Path("/etc/vybe/vybe.toml"),
        ]
        
        for path in search_paths:
            if path.exists():
                return path
                
        # Return default path if none found
        return Path("./vybe.toml")
    
    def _load_config(self):
        """Load configuration from TOML file"""
        if self.config_path is None or not self.config_path.exists():
            if self.config_path is not None:
                self.logger.info("Config file not found: %s", self.config_path)
            self.logger.info("Using default configuration")
            return
            
        if tomllib is None:
            self.logger.warning("TOML library not available. Install 'tomli' for Python < 3.11")
            return
            
        try:
            with open(self.config_path, 'rb') as f:
                config_data = tomllib.load(f)
                
            self._apply_config_data(config_data)
            self.logger.info("Configuration loaded from: %s", self.config_path)
            
        except (FileNotFoundError, OSError) as e:
            self.logger.error("Failed to load config: %s", e)
            self.logger.info("Using default configuration")
    
    def _apply_config_data(self, config_data: Dict[str, Any]):
        """Apply configuration data to config objects"""
        # Apply LLM configuration
        if 'llm' in config_data:
            for key, value in config_data['llm'].items():
                if hasattr(self.llm, key):
                    setattr(self.llm, key, value)
        
        # Apply Context configuration
        if 'context' in config_data:
            for key, value in config_data['context'].items():
                if hasattr(self.context, key):
                    setattr(self.context, key, value)
        
        # Apply Consensus configuration
        if 'consensus' in config_data:
            for key, value in config_data['consensus'].items():
                if hasattr(self.consensus, key):
                    setattr(self.consensus, key, value)
        
        # Apply MAS configuration
        if 'mas' in config_data:
            for key, value in config_data['mas'].items():
                if hasattr(self.mas, key):
                    setattr(self.mas, key, value)
        
        # Apply Security configuration
        if 'security' in config_data:
            for key, value in config_data['security'].items():
                if hasattr(self.security, key):
                    setattr(self.security, key, value)
        
        # Apply Logging configuration
        if 'logging' in config_data:
            for key, value in config_data['logging'].items():
                if hasattr(self.logging, key):
                    setattr(self.logging, key, value)
        
        # Apply UI configuration
        if 'ui' in config_data:
            for key, value in config_data['ui'].items():
                if hasattr(self.ui, key):
                    setattr(self.ui, key, value)
        
        # Apply Agent configurations
        if 'agents' in config_data:
            for agent_name, agent_config in config_data['agents'].items():
                self.agents[agent_name] = AgentConfig(
                    name=agent_name,
                    **agent_config
                )
    
    def _load_env_overrides(self):
        """Load configuration overrides from environment variables"""
        env_mappings = {
            'VYBE_LLM_PROVIDER': ('llm', 'provider'),
            'VYBE_LLM_MODEL': ('llm', 'model'),
            'VYBE_LLM_API_KEY': ('llm', 'api_key'),
            'VYBE_LLM_API_URL': ('llm', 'api_url'),
            'VYBE_CONTEXT_MAX_TOKENS': ('context', 'max_context_tokens'),
            'VYBE_VECTOR_DB_PROVIDER': ('context', 'vector_db_provider'),
            'VYBE_LOG_LEVEL': ('logging', 'level'),
            'VYBE_LOG_FILE': ('logging', 'file_path'),
            'VYBE_SECURITY_LOCAL_ONLY': ('security', 'local_only'),
        }
        
        for env_var, (section, key) in env_mappings.items():
            if env_var in os.environ:
                value = os.environ[env_var]
                
                # Convert string values to appropriate types
                config_obj = getattr(self, section)
                if hasattr(config_obj, key):
                    current_value = getattr(config_obj, key)
                    if isinstance(current_value, bool):
                        value = value.lower() in ('true', '1', 'yes', 'on')
                    elif isinstance(current_value, int):
                        value = int(value)
                    elif isinstance(current_value, float):
                        value = float(value)
                    
                    setattr(config_obj, key, value)
                    self.logger.info("Override from env: %s = %s", env_var, value)
    
    def _validate_config(self) -> None:
        """Validate configuration values"""
        errors: List[str] = []
        
        # Validate LLM configuration
        if self.llm.provider not in ['openai', 'anthropic', 'local', 'ollama']:
            errors.append(f"Invalid LLM provider: {self.llm.provider}")
        
        if self.llm.max_tokens < 1:
            errors.append("LLM max_tokens must be positive")
        
        if not (0.0 <= self.llm.temperature <= 2.0):
            errors.append("LLM temperature must be between 0.0 and 2.0")
        
        # Validate Context configuration
        if self.context.max_context_tokens < 1000:
            errors.append("Context max_tokens should be at least 1000")
        
        if not (0.0 <= self.context.similarity_threshold <= 1.0):
            errors.append("Context similarity_threshold must be between 0.0 and 1.0")
        
        # Validate Consensus configuration
        if not (0.0 <= self.consensus.min_consensus_threshold <= 1.0):
            errors.append("Consensus threshold must be between 0.0 and 1.0")
        
        if self.consensus.max_voting_rounds < 1:
            errors.append("Max voting rounds must be positive")
        
        # Validate MAS configuration
        if self.mas.max_concurrent_agents < 1:
            errors.append("Max concurrent agents must be positive")
        
        # Validate Logging configuration
        if self.logging.level not in ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']:
            errors.append(f"Invalid log level: {self.logging.level}")
        
        if errors:
            error_msg = "Configuration validation errors:\n" + "\n".join(f"  - {e}" for e in errors)
            raise ValueError(error_msg)
        
        self.logger.info("Configuration validation passed")
    
    def save_config(self, path: Optional[Union[str, Path]] = None):
        """Save current configuration to TOML file"""
        if tomllib is None:
            self.logger.error("Cannot save TOML config: tomllib not available")
            return False
            
        save_path = Path(path) if path else (self.config_path or Path("./vybe.toml"))
        
        try:
            config_dict = {
                'llm': {
                    'provider': self.llm.provider,
                    'model': self.llm.model,
                    'max_tokens': self.llm.max_tokens,
                    'temperature': self.llm.temperature,
                    'timeout': self.llm.timeout,
                    'retry_attempts': self.llm.retry_attempts,
                    'rate_limit': self.llm.rate_limit,
                },
                'context': {
                    'max_context_tokens': self.context.max_context_tokens,
                    'chunk_size': self.context.chunk_size,
                    'overlap_size': self.context.overlap_size,
                    'similarity_threshold': self.context.similarity_threshold,
                    'vector_db_provider': self.context.vector_db_provider,
                    'vector_db_path': self.context.vector_db_path,
                    'embedding_model': self.context.embedding_model,
                },
                'consensus': {
                    'min_consensus_threshold': self.consensus.min_consensus_threshold,
                    'max_voting_rounds': self.consensus.max_voting_rounds,
                    'timeout_per_round': self.consensus.timeout_per_round,
                    'code_quality_threshold': self.consensus.code_quality_threshold,
                },
                'mas': {
                    'max_concurrent_agents': self.mas.max_concurrent_agents,
                    'task_timeout': self.mas.task_timeout,
                    'coordination_interval': self.mas.coordination_interval,
                },
                'security': {
                    'encryption_enabled': self.security.encryption_enabled,
                    'local_only': self.security.local_only,
                },
                'logging': {
                    'level': self.logging.level,
                    'file_path': self.logging.file_path,
                    'json_format': self.logging.json_format,
                },
                'ui': {
                    'theme': self.ui.theme,
                    'auto_save': self.ui.auto_save,
                    'vscode_integration': self.ui.vscode_integration,
                }
            }
            
            # Add agent configurations
            if self.agents:
                config_dict['agents'] = {}
                for name, agent in self.agents.items():
                    config_dict['agents'][name] = {
                        'model': agent.model,
                        'max_tokens': agent.max_tokens,
                        'temperature': agent.temperature,
                        'enabled': agent.enabled,
                        'specialization': agent.specialization,
                        'tools': agent.tools,
                    }
            
            # Note: We would need a TOML writer library like 'tomlkit' to actually write TOML
            # For now, just log that we would save
            self.logger.info("Would save configuration to: %s", save_path)
            return True
            
        except (OSError, IOError) as e:
            self.logger.error("Failed to save config: %s", e)
            return False
    
    def get_agent_config(self, agent_name: str) -> Optional[AgentConfig]:
        """Get configuration for a specific agent"""
        return self.agents.get(agent_name)
    
    def add_agent_config(self, agent_config: AgentConfig):
        """Add or update agent configuration"""
        self.agents[agent_config.name] = agent_config
        self.logger.info("Added agent configuration: %s", agent_config.name)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return {
            'llm': self.llm.__dict__,
            'context': self.context.__dict__,
            'consensus': self.consensus.__dict__,
            'mas': self.mas.__dict__,
            'security': self.security.__dict__,
            'logging': self.logging.__dict__,
            'ui': self.ui.__dict__,
            'agents': {name: agent.__dict__ for name, agent in self.agents.items()}
        }
    
    def __repr__(self) -> str:
        return f"VybeConfig(path={self.config_path}, agents={len(self.agents)})"