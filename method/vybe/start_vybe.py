#!/usr/bin/env python3
"""
Vybe Method Startup Script for VybeCoding.ai
Initialize and run the complete Vybe System
"""

import sys
import os
import asyncio
import signal
from pathlib import Path

# Add method/vybe to path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

try:
    from vybe_system import VybeSystem
    print("✅ Vybe System modules loaded successfully")
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Installing required dependencies...")
    os.system("pip3 install chromadb watchdog crewai autogen-agentchat langgraph langchain guardrails-ai fastapi uvicorn pydantic toml numpy sentence-transformers httpx aiofiles python-multipart")
    
    try:
        from vybe_system import VybeSystem
        print("✅ Vybe System modules loaded after dependency installation")
    except ImportError as e2:
        print(f"❌ Failed to import after installation: {e2}")
        sys.exit(1)

class VybeRunner:
    """Vybe System Runner for VybeCoding.ai"""
    
    def __init__(self):
        self.vybe_system = None
        self.running = False
    
    async def start(self):
        """Start the Vybe system"""
        print("🚀 Starting Vybe Method System for VybeCoding.ai...")
        
        # Initialize Vybe System
        config_path = current_dir / "config" / "vybe.toml"
        
        try:
            self.vybe_system = VybeSystem(str(config_path))

            # Initialize the system
            await self.vybe_system.initialize()

            # Start a session
            session = await self.vybe_system.start_session(project_path=".")
            self.running = True
            
            print("\n" + "="*60)
            print("✅ Vybe Method System is ACTIVE!")
            print("="*60)
            print("📊 Project: VybeCoding.ai AI Education Platform")
            print("🎯 Mission: Generate profitable Vybe Qubes through MAS")
            print("🧠 Agents: 7 specialist AI agents with MAS capabilities")
            print("📡 API: http://localhost:8000")
            print("🔧 GitHub Copilot: Load ide-vybe-orchestrator.md")
            print("="*60)
            print("\n🎮 Quick Commands:")
            print("  /vybe status     - System health check")
            print("  /vybe analyze    - Deep codebase analysis") 
            print("  /vybe collaborate <task> - Multi-agent execution")
            print("  /vybe generate   - Create profitable Vybe Qubes")
            print("\n🛡️  Zero-hallucination development ACTIVE")
            print("🎯 Ready for enterprise-grade AI education platform!")
            
            # Keep running
            while self.running:
                # Event-driven startup timing instead of sleep
                startup_event = asyncio.Event()
                try:
                    await asyncio.wait_for(startup_event.wait(), timeout=1.0)
                    startup_event.clear()
                except asyncio.TimeoutError:
                    pass  # Startup timing completed
                
        except Exception as e:
            print(f"❌ Failed to start Vybe System: {e}")
            print("💡 Try running: pip3 install -r requirements.txt")
            return False
        
        return True
    
    def stop(self):
        """Stop the Vybe system"""
        print("\n🛑 Stopping Vybe Method System...")
        self.running = False
        if self.vybe_system:
            # Perform graceful shutdown
            self.vybe_system.shutdown()
        print("✅ Vybe Method System stopped")

def signal_handler(signum, frame):
    """Handle shutdown signals"""
    print("\n🔄 Received shutdown signal...")
    if hasattr(signal_handler, 'runner'):
        signal_handler.runner.stop()
    sys.exit(0)

async def main():
    """Main startup function"""
    # Setup signal handlers
    runner = VybeRunner()
    signal_handler.runner = runner
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Start the system
    try:
        await runner.start()
    except KeyboardInterrupt:
        runner.stop()
    except Exception as e:
        print(f"❌ Startup error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    # Check if we can run async
    try:
        asyncio.run(main())
    except Exception as e:
        print(f"❌ Could not start async system: {e}")
        print("🔄 Trying synchronous startup...")
        
        # Fallback to synchronous startup
        config_path = current_dir / "config" / "vybe.toml"
        try:
            vybe_system = VybeSystem(str(config_path))
            print("✅ Vybe Method System initialized (sync mode)")
            print("📡 Use GitHub Copilot Chat for interaction")
            print("🎯 Load ide-vybe-orchestrator.md to begin")
        except Exception as e2:
            print(f"❌ Sync startup also failed: {e2}")
            sys.exit(1)
