"""
Real Vector Context Engine - ChromaDB integration for Vybe Method
Implements actual vector database operations with semantic search
"""

import asyncio
import hashlib
import json
import logging
import time
from pathlib import Path
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, field
from datetime import datetime
import threading
from concurrent.futures import Thread<PERSON>oolExecutor

# Vector database imports
try:
    import chromadb
    from chromadb.config import Settings
    CHROMADB_AVAILABLE = True
except ImportError:
    CHROMADB_AVAILABLE = False
    logging.warning("ChromaDB not available, vector operations disabled")

# Embedding model imports
try:
    from sentence_transformers import SentenceTransformer
    EMBEDDINGS_AVAILABLE = True
except ImportError:
    EMBEDDINGS_AVAILABLE = False
    logging.warning("sentence-transformers not available, using ChromaDB default embeddings")

# Token counting
try:
    import tiktoken
    TIKTOKEN_AVAILABLE = True
except ImportError:
    TIKTOKEN_AVAILABLE = False
    logging.warning("tiktoken not available, using fallback token counting")


@dataclass
class VectorContextItem:
    """Vector context item with metadata"""
    content: str
    source: str
    timestamp: datetime
    item_type: str
    priority: int = 1
    tokens: int = 0
    hash_id: str = field(default="")
    tags: Set[str] = field(default_factory=set)
    references: Set[str] = field(default_factory=set)
    
    def __post_init__(self):
        if not self.hash_id:
            self.hash_id = hashlib.sha256(
                f"{self.content}:{self.source}:{self.item_type}".encode()
            ).hexdigest()[:16]


class VectorContextEngine:
    """
    Real vector context engine using ChromaDB for semantic search
    Implements actual vector operations with no placeholders
    """
    
    def __init__(self, config_path: Optional[Path] = None):
        self.logger = logging.getLogger(__name__)
        self.config_path = config_path
        
        # Core settings
        self.max_context_tokens = 200000
        self.chunk_size = 2000
        self.overlap_size = 200
        self.collection_name = "vybe_context"
        
        # Initialize components
        self.chroma_client = None
        self.collection = None
        self.token_encoder = None
        self.embedding_model = None
        
        # Thread safety
        self.lock = threading.RLock()
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # Performance metrics
        self.metrics = {
            'total_items': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'query_time_ms': [],
            'vector_searches': 0,
            'embeddings_generated': 0
        }
        
        self._initialize()
    
    def _initialize(self):
        """Initialize all engine components"""
        try:
            self._setup_chromadb()
            self._setup_token_encoder()
            self._setup_embedding_model()
            self._setup_collection()
            self.logger.info("VectorContextEngine initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize VectorContextEngine: {e}")
            raise
    
    def _setup_chromadb(self):
        """Setup ChromaDB client and configuration"""
        if not CHROMADB_AVAILABLE:
            raise RuntimeError("ChromaDB not available. Install with: pip install chromadb")
        
        # Create data directory
        data_dir = Path("method/vybe/data/vector_db")
        data_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize ChromaDB client with persistent storage
        self.chroma_client = chromadb.PersistentClient(
            path=str(data_dir),
            settings=Settings(
                anonymized_telemetry=False,
                allow_reset=True
            )
        )
        
        self.logger.info(f"ChromaDB initialized with persistent storage at {data_dir}")
    
    def _setup_token_encoder(self):
        """Setup token encoder for accurate token counting"""
        if TIKTOKEN_AVAILABLE:
            try:
                self.token_encoder = tiktoken.get_encoding("cl100k_base")
                self.logger.info("Using tiktoken for token counting")
            except Exception as e:
                self.logger.warning(f"Failed to load tiktoken: {e}")
                self.token_encoder = None
        else:
            self.token_encoder = None
    
    def _setup_embedding_model(self):
        """Setup embedding model for semantic search"""
        if EMBEDDINGS_AVAILABLE:
            try:
                # Use a lightweight model for faster initialization
                self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
                self.logger.info("Embedding model initialized: all-MiniLM-L6-v2")
            except Exception as e:
                self.logger.error(f"Failed to initialize embedding model: {e}")
                self.embedding_model = None
        else:
            self.embedding_model = None
            self.logger.info("Using ChromaDB default embeddings")
    
    def _setup_collection(self):
        """Setup ChromaDB collection for context storage"""
        try:
            # Create or get collection
            if self.embedding_model:
                # Use custom embedding function
                self.collection = self.chroma_client.get_or_create_collection(
                    name=self.collection_name,
                    metadata={"description": "Vybe Method context storage with semantic search"}
                )
            else:
                # Use ChromaDB default embeddings
                self.collection = self.chroma_client.get_or_create_collection(
                    name=self.collection_name,
                    metadata={"description": "Vybe Method context storage with default embeddings"}
                )
            
            # Get current item count
            count = self.collection.count()
            self.metrics['total_items'] = count
            
            self.logger.info(f"ChromaDB collection '{self.collection_name}' ready with {count} items")
            
        except Exception as e:
            self.logger.error(f"Failed to setup ChromaDB collection: {e}")
            raise
    
    def count_tokens(self, text: str) -> int:
        """Count tokens in text using tiktoken or fallback"""
        if self.token_encoder:
            return len(self.token_encoder.encode(text))
        else:
            # Fallback: rough estimate
            return int(len(text.split()) * 1.3)
    
    def add_context(self, content: str, source: str, item_type: str = "general", 
                   priority: int = 1, tags: Optional[Set[str]] = None) -> bool:
        """Add new context item with real vector storage"""
        start_time = time.time()
        
        try:
            with self.lock:
                # Create context item
                item = VectorContextItem(
                    content=content,
                    source=source,
                    timestamp=datetime.now(),
                    item_type=item_type,
                    priority=priority,
                    tags=tags or set()
                )
                
                # Count tokens
                item.tokens = self.count_tokens(content)
                
                # Check if item already exists
                if self._item_exists(item.hash_id):
                    self.logger.debug(f"Context item {item.hash_id} already exists")
                    return False
                
                # Prepare metadata
                metadata = {
                    "source": source,
                    "item_type": item_type,
                    "priority": priority,
                    "tokens": item.tokens,
                    "timestamp": item.timestamp.isoformat(),
                    "tags": json.dumps(list(tags or set())),
                    "references": json.dumps(list(item.references))
                }
                
                # Add to ChromaDB
                if self.embedding_model:
                    # Generate custom embedding
                    embedding = self.embedding_model.encode(content).tolist()
                    self.collection.add(
                        documents=[content],
                        metadatas=[metadata],
                        ids=[item.hash_id],
                        embeddings=[embedding]
                    )
                    self.metrics['embeddings_generated'] += 1
                else:
                    # Use ChromaDB default embeddings
                    self.collection.add(
                        documents=[content],
                        metadatas=[metadata],
                        ids=[item.hash_id]
                    )
                
                # Update metrics
                self.metrics['total_items'] += 1
                query_time = (time.time() - start_time) * 1000
                self.metrics['query_time_ms'].append(query_time)
                
                self.logger.debug(f"Added context item: {item.hash_id} ({item.tokens} tokens)")
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to add context: {e}")
            return False
    
    def _item_exists(self, hash_id: str) -> bool:
        """Check if context item already exists"""
        try:
            result = self.collection.get(ids=[hash_id])
            return len(result['ids']) > 0
        except Exception:
            return False
    
    def get_context(self, query: str = "", max_tokens: Optional[int] = None, 
                   item_types: Optional[List[str]] = None, 
                   n_results: int = 10) -> List[VectorContextItem]:
        """Get relevant context items with real semantic search"""
        start_time = time.time()
        max_tokens = max_tokens or self.max_context_tokens
        
        try:
            with self.lock:
                if query:
                    # Perform semantic search
                    where_filter = {}
                    if item_types:
                        where_filter["item_type"] = {"$in": item_types}
                    
                    if self.embedding_model:
                        # Use custom embedding for query
                        query_embedding = self.embedding_model.encode(query).tolist()
                        results = self.collection.query(
                            query_embeddings=[query_embedding],
                            n_results=n_results,
                            where=where_filter if where_filter else None
                        )
                    else:
                        # Use ChromaDB default embedding
                        results = self.collection.query(
                            query_texts=[query],
                            n_results=n_results,
                            where=where_filter if where_filter else None
                        )
                    
                    self.metrics['vector_searches'] += 1
                else:
                    # Get recent items without search
                    where_filter = {}
                    if item_types:
                        where_filter["item_type"] = {"$in": item_types}
                    
                    results = self.collection.get(
                        where=where_filter if where_filter else None,
                        limit=n_results
                    )
                
                # Convert results to VectorContextItem objects
                items = []
                current_tokens = 0
                
                if 'documents' in results and results['documents']:
                    for i, (doc, metadata) in enumerate(zip(results['documents'][0], results['metadatas'][0])):
                        if current_tokens >= max_tokens:
                            break
                        
                        item_tokens = metadata.get('tokens', 0)
                        if current_tokens + item_tokens <= max_tokens:
                            item = VectorContextItem(
                                content=doc,
                                source=metadata.get('source', ''),
                                timestamp=datetime.fromisoformat(metadata.get('timestamp', datetime.now().isoformat())),
                                item_type=metadata.get('item_type', 'general'),
                                priority=metadata.get('priority', 1),
                                tokens=item_tokens,
                                hash_id=results['ids'][0][i],
                                tags=set(json.loads(metadata.get('tags', '[]'))),
                                references=set(json.loads(metadata.get('references', '[]')))
                            )
                            items.append(item)
                            current_tokens += item_tokens
                
                # Update metrics
                query_time = (time.time() - start_time) * 1000
                self.metrics['query_time_ms'].append(query_time)
                
                self.logger.debug(f"Retrieved {len(items)} context items ({current_tokens} tokens)")
                return items
                
        except Exception as e:
            self.logger.error(f"Failed to get context: {e}")
            return []
    
    def clear_context(self):
        """Clear all context items"""
        try:
            with self.lock:
                # Delete and recreate collection
                self.chroma_client.delete_collection(self.collection_name)
                self._setup_collection()
                self.metrics['total_items'] = 0
                self.logger.info("Context cleared")
        except Exception as e:
            self.logger.error(f"Failed to clear context: {e}")
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get performance and usage metrics"""
        avg_query_time = (
            sum(self.metrics['query_time_ms']) / len(self.metrics['query_time_ms'])
            if self.metrics['query_time_ms'] else 0
        )
        
        return {
            'total_items': self.metrics['total_items'],
            'vector_searches': self.metrics['vector_searches'],
            'embeddings_generated': self.metrics['embeddings_generated'],
            'avg_query_time_ms': avg_query_time,
            'chromadb_enabled': CHROMADB_AVAILABLE,
            'custom_embeddings_enabled': self.embedding_model is not None,
            'tiktoken_enabled': self.token_encoder is not None
        }
    
    def shutdown(self):
        """Gracefully shutdown the context engine"""
        try:
            if self.executor:
                self.executor.shutdown(wait=True)
            
            self.logger.info("VectorContextEngine shutdown complete")
            
        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")


# Async wrapper for vector context engine operations
class AsyncVectorContextEngine:
    """Async wrapper for VectorContextEngine operations"""
    
    def __init__(self, engine: VectorContextEngine):
        self.engine = engine
    
    async def add_context_async(self, content: str, source: str, **kwargs) -> bool:
        """Async version of add_context"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self.engine.executor, 
            self.engine.add_context, 
            content, source, **kwargs
        )
    
    async def get_context_async(self, query: str = "", **kwargs) -> List[VectorContextItem]:
        """Async version of get_context"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self.engine.executor,
            self.engine.get_context,
            query, **kwargs
        )
