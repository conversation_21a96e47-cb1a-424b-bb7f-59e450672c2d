"""
Vybe Method Codebase Interface
Autonomous codebase modification capabilities for MAS agents
Differentiates from BMAD Method (human + IDE) by providing direct file system access
"""

import os
import json
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable
import logging
from dataclasses import dataclass, field
from datetime import datetime
from collections import deque
import asyncio
import threading


@dataclass
class CodebaseOperation:
    operation_type: str  # 'create', 'modify', 'delete', 'move'
    file_path: str
    content: Optional[str] = None
    old_content: Optional[str] = None
    agent_id: str = ""
    reasoning: str = ""


class VybeCodebaseInterface:
    """
    Vybe Method Codebase Interface
    Enables autonomous MAS agents to directly modify codebase

    Key Differences from BMAD Method:
    - BMAD: Human uses VS Code + Augment Code/GitHub Copilot
    - Vybe: Autonomous agents directly modify files via this interface
    """

    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.logger = logging.getLogger(__name__)
        self.operation_history: List[CodebaseOperation] = []

        # MAS Integration Components
        self.mas_coordinator = None  # Will be set by MAS system
        self.feedback_queue = deque()  # Agent-to-Codebase feedback loop
        self.context_cache = {}  # Codebase context cache
        self.active_agents = set()  # Currently active agents

        # Real-time monitoring
        self.operation_callbacks: List[Callable] = []
        self.file_watchers = {}

        # Performance metrics
        self.metrics = {
            "total_operations": 0,
            "successful_operations": 0,
            "failed_operations": 0,
            "files_created": 0,
            "files_modified": 0,
            "files_deleted": 0,
            "commands_executed": 0,
            "agent_interactions": 0,
            "context_retrievals": 0,
            "avg_operation_time": 0.0,
        }

        # Ensure project root exists
        if not self.project_root.exists():
            raise ValueError(f"Project root does not exist: {project_root}")

        self.logger.info(f"VybeCodebaseInterface initialized for: {project_root}")

    def create_file(
        self, file_path: str, content: str, agent_id: str, reasoning: str = ""
    ) -> bool:
        """
        Create a new file (Vybe Method autonomous file creation)
        Unlike BMAD Method where human creates files in VS Code
        """
        try:
            full_path = self.project_root / file_path

            # Ensure directory exists
            full_path.parent.mkdir(parents=True, exist_ok=True)

            # Write file content
            with open(full_path, "w", encoding="utf-8") as f:
                f.write(content)

            # Log operation
            operation = CodebaseOperation(
                operation_type="create",
                file_path=file_path,
                content=content,
                agent_id=agent_id,
                reasoning=reasoning,
            )
            self.operation_history.append(operation)

            self.logger.info(f"Vybe Agent {agent_id} created file: {file_path}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to create file {file_path}: {e}")
            return False

    def modify_file(
        self, file_path: str, new_content: str, agent_id: str, reasoning: str = ""
    ) -> bool:
        """
        Modify existing file (Vybe Method autonomous file modification)
        Unlike BMAD Method where human edits in VS Code with AI assistance
        """
        try:
            full_path = self.project_root / file_path

            if not full_path.exists():
                self.logger.error(f"File does not exist: {file_path}")
                return False

            # Read current content for history
            with open(full_path, "r", encoding="utf-8") as f:
                old_content = f.read()

            # Write new content
            with open(full_path, "w", encoding="utf-8") as f:
                f.write(new_content)

            # Log operation
            operation = CodebaseOperation(
                operation_type="modify",
                file_path=file_path,
                content=new_content,
                old_content=old_content,
                agent_id=agent_id,
                reasoning=reasoning,
            )
            self.operation_history.append(operation)

            self.logger.info(f"Vybe Agent {agent_id} modified file: {file_path}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to modify file {file_path}: {e}")
            return False

    def delete_file(self, file_path: str, agent_id: str, reasoning: str = "") -> bool:
        """
        Delete file (Vybe Method autonomous file deletion)
        """
        try:
            full_path = self.project_root / file_path

            if not full_path.exists():
                self.logger.warning(f"File does not exist: {file_path}")
                return True

            # Read content for history
            with open(full_path, "r", encoding="utf-8") as f:
                old_content = f.read()

            # Delete file
            full_path.unlink()

            # Log operation
            operation = CodebaseOperation(
                operation_type="delete",
                file_path=file_path,
                old_content=old_content,
                agent_id=agent_id,
                reasoning=reasoning,
            )
            self.operation_history.append(operation)

            self.logger.info(f"Vybe Agent {agent_id} deleted file: {file_path}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to delete file {file_path}: {e}")
            return False

    def read_file(self, file_path: str) -> Optional[str]:
        """
        Read file content for agent analysis
        """
        try:
            full_path = self.project_root / file_path

            if not full_path.exists():
                return None

            with open(full_path, "r", encoding="utf-8") as f:
                return f.read()

        except Exception as e:
            self.logger.error(f"Failed to read file {file_path}: {e}")
            return None

    def list_files(self, directory: str = "", pattern: str = "*") -> List[str]:
        """
        List files in directory for agent discovery
        """
        try:
            search_path = (
                self.project_root / directory if directory else self.project_root
            )

            if pattern == "*":
                files = [
                    str(p.relative_to(self.project_root))
                    for p in search_path.rglob("*")
                    if p.is_file()
                ]
            else:
                files = [
                    str(p.relative_to(self.project_root))
                    for p in search_path.rglob(pattern)
                    if p.is_file()
                ]

            return sorted(files)

        except Exception as e:
            self.logger.error(f"Failed to list files in {directory}: {e}")
            return []

    def run_command(
        self, command: str, agent_id: str, reasoning: str = ""
    ) -> Dict[str, Any]:
        """
        Execute shell command (Vybe Method autonomous command execution)
        Unlike BMAD Method where human runs commands in terminal
        """
        try:
            result = subprocess.run(
                command,
                shell=True,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=300,  # 5 minute timeout
            )

            operation = CodebaseOperation(
                operation_type="command",
                file_path=command,
                content=f"stdout: {result.stdout}\nstderr: {result.stderr}",
                agent_id=agent_id,
                reasoning=reasoning,
            )
            self.operation_history.append(operation)

            self.logger.info(f"Vybe Agent {agent_id} executed command: {command}")

            return {
                "success": result.returncode == 0,
                "returncode": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr,
            }

        except subprocess.TimeoutExpired:
            self.logger.error(f"Command timeout: {command}")
            return {"success": False, "error": "Command timeout"}
        except Exception as e:
            self.logger.error(f"Failed to execute command {command}: {e}")
            return {"success": False, "error": str(e)}

    def install_dependencies(
        self, agent_id: str, dependencies: List[str] = None
    ) -> bool:
        """
        Install project dependencies (Vybe Method autonomous package management)
        """
        try:
            # Check for package.json (Node.js)
            if (self.project_root / "package.json").exists():
                if dependencies:
                    for dep in dependencies:
                        result = self.run_command(
                            f"npm install {dep}",
                            agent_id,
                            f"Installing dependency: {dep}",
                        )
                        if not result["success"]:
                            return False
                else:
                    result = self.run_command(
                        "npm install", agent_id, "Installing all dependencies"
                    )
                    return result["success"]

            # Check for requirements.txt (Python)
            elif (self.project_root / "requirements.txt").exists():
                result = self.run_command(
                    "pip install -r requirements.txt",
                    agent_id,
                    "Installing Python dependencies",
                )
                return result["success"]

            # Check for Cargo.toml (Rust)
            elif (self.project_root / "Cargo.toml").exists():
                result = self.run_command(
                    "cargo build", agent_id, "Building Rust project"
                )
                return result["success"]

            return True

        except Exception as e:
            self.logger.error(f"Failed to install dependencies: {e}")
            return False

    def git_operations(self, agent_id: str, operation: str, message: str = "") -> bool:
        """
        Perform Git operations (Vybe Method autonomous version control)
        """
        try:
            if operation == "add":
                result = self.run_command(
                    "git add .", agent_id, "Adding all changes to Git"
                )
            elif operation == "commit":
                result = self.run_command(
                    f'git commit -m "{message}"',
                    agent_id,
                    f"Committing changes: {message}",
                )
            elif operation == "push":
                result = self.run_command(
                    "git push", agent_id, "Pushing changes to remote"
                )
            elif operation == "status":
                result = self.run_command("git status", agent_id, "Checking Git status")
            else:
                return False

            return result["success"]

        except Exception as e:
            self.logger.error(f"Git operation failed: {e}")
            return False

    def get_operation_history(self, agent_id: str = None) -> List[CodebaseOperation]:
        """
        Get history of codebase operations
        """
        if agent_id:
            return [op for op in self.operation_history if op.agent_id == agent_id]
        return self.operation_history

    def rollback_operation(self, operation_index: int) -> bool:
        """
        Rollback a specific operation
        """
        try:
            if operation_index >= len(self.operation_history):
                return False

            operation = self.operation_history[operation_index]

            if operation.operation_type == "create":
                # Delete the created file
                return self.delete_file(
                    operation.file_path, "system", "Rollback create operation"
                )

            elif operation.operation_type == "modify" and operation.old_content:
                # Restore old content
                return self.modify_file(
                    operation.file_path,
                    operation.old_content,
                    "system",
                    "Rollback modify operation",
                )

            elif operation.operation_type == "delete" and operation.old_content:
                # Recreate deleted file
                return self.create_file(
                    operation.file_path,
                    operation.old_content,
                    "system",
                    "Rollback delete operation",
                )

            return False

        except Exception as e:
            self.logger.error(f"Failed to rollback operation: {e}")
            return False

    # MAS Integration Methods
    def set_mas_coordinator(self, coordinator):
        """Set the MAS coordinator for real-time integration"""
        self.mas_coordinator = coordinator
        self.logger.info("MAS coordinator connected to codebase interface")

    def register_agent(self, agent_id: str):
        """Register an agent as active in the codebase"""
        self.active_agents.add(agent_id)
        self.metrics["agent_interactions"] += 1
        self.logger.info(f"Agent {agent_id} registered for codebase access")

    def unregister_agent(self, agent_id: str):
        """Unregister an agent from codebase access"""
        self.active_agents.discard(agent_id)
        self.logger.info(f"Agent {agent_id} unregistered from codebase access")

    def add_operation_callback(self, callback: Callable):
        """Add callback for real-time operation monitoring"""
        self.operation_callbacks.append(callback)

    def notify_operation(self, operation: CodebaseOperation):
        """Notify all callbacks of a codebase operation"""
        for callback in self.operation_callbacks:
            try:
                callback(operation)
            except Exception as e:
                self.logger.error(f"Operation callback failed: {e}")

    def get_codebase_context(self, query: str) -> Dict[str, Any]:
        """Get codebase context for agent decision making"""
        self.metrics["context_retrievals"] += 1

        # Check cache first
        if query in self.context_cache:
            return self.context_cache[query]

        # Generate context
        context = {
            "query": query,
            "relevant_files": self._find_relevant_files(query),
            "recent_operations": self.operation_history[-10:],
            "active_agents": list(self.active_agents),
            "project_structure": self._get_project_structure(),
        }

        # Cache context
        self.context_cache[query] = context
        return context

    def _find_relevant_files(self, query: str) -> List[str]:
        """Find files relevant to the query"""
        # Simple implementation - could be enhanced with semantic search
        query_lower = query.lower()
        relevant_files = []

        for file_path in self.list_files():
            if any(term in file_path.lower() for term in query_lower.split()):
                relevant_files.append(file_path)

        return relevant_files[:10]  # Limit to 10 most relevant

    def _get_project_structure(self) -> Dict[str, Any]:
        """Get high-level project structure"""
        structure = {"directories": [], "key_files": [], "technologies": []}

        # Get directories
        for item in self.project_root.iterdir():
            if item.is_dir() and not item.name.startswith("."):
                structure["directories"].append(item.name)

        # Get key files
        key_patterns = ["package.json", "requirements.txt", "Cargo.toml", "README.md"]
        for pattern in key_patterns:
            if (self.project_root / pattern).exists():
                structure["key_files"].append(pattern)

        # Detect technologies
        if (self.project_root / "package.json").exists():
            structure["technologies"].append("Node.js")
        if (self.project_root / "requirements.txt").exists():
            structure["technologies"].append("Python")
        if (self.project_root / "Cargo.toml").exists():
            structure["technologies"].append("Rust")

        return structure

    def get_interface_metrics(self) -> Dict[str, Any]:
        """Get codebase interface metrics"""
        return {
            **self.metrics,
            "active_agents_count": len(self.active_agents),
            "operation_history_size": len(self.operation_history),
            "context_cache_size": len(self.context_cache),
        }


# Integration with Real MAS Coordinator
class VybeMASCodebaseIntegration:
    """
    Integration layer between Vybe MAS and Codebase Interface
    Enables VYBRO (Developer Agent) to autonomously modify codebase
    """

    def __init__(self, project_root: str):
        self.codebase = VybeCodebaseInterface(project_root)
        self.project_root = Path(project_root)  # Add project_root for file operations
        self.logger = logging.getLogger(__name__)

    def execute_vybro_development_task(
        self, task_result: Any, agent_id: str = "vybro"
    ) -> Dict[str, Any]:
        """
        Execute VYBRO's development task with real codebase modifications
        This is where Vybe Method differs from BMAD Method
        """
        try:
            # Parse VYBRO's output for file operations
            if hasattr(task_result, "result"):
                development_plan = str(task_result.result)
            else:
                development_plan = str(task_result)

            # Extract file operations from VYBRO's plan
            # This would need sophisticated parsing of agent output
            operations = self._parse_development_plan(development_plan)

            results = []
            for operation in operations:
                if operation["type"] == "create_file":
                    success = self.codebase.create_file(
                        operation["path"],
                        operation["content"],
                        agent_id,
                        operation.get("reasoning", ""),
                    )
                    results.append(
                        {
                            "operation": "create_file",
                            "path": operation["path"],
                            "success": success,
                        }
                    )

                elif operation["type"] == "modify_file":
                    success = self.codebase.modify_file(
                        operation["path"],
                        operation["content"],
                        agent_id,
                        operation.get("reasoning", ""),
                    )
                    results.append(
                        {
                            "operation": "modify_file",
                            "path": operation["path"],
                            "success": success,
                        }
                    )

            return {"success": True, "operations": results}

        except Exception as e:
            self.logger.error(f"Failed to execute VYBRO development task: {e}")
            return {"success": False, "error": str(e)}

    def _parse_development_plan(self, plan: str) -> List[Dict[str, Any]]:
        """
        Parse VYBRO's development plan into actionable file operations
        Enhanced parser for real agent output
        """
        operations = []

        # Look for common file operation patterns in agent output
        import re
        import time

        # Pattern for file creation: "create file", "new file", etc.
        create_patterns = [
            r"create\s+(?:file|component)\s+['\"]?([^'\"]+)['\"]?",
            r"new\s+(?:file|component)\s+['\"]?([^'\"]+)['\"]?",
            r"add\s+(?:file|component)\s+['\"]?([^'\"]+)['\"]?",
        ]

        # Pattern for file modification: "update", "modify", "edit"
        modify_patterns = [
            r"(?:update|modify|edit)\s+(?:file|component)?\s*['\"]?([^'\"]+)['\"]?",
            r"change\s+(?:in\s+)?['\"]?([^'\"]+)['\"]?",
        ]

        # Extract file operations from the plan
        for pattern in create_patterns:
            matches = re.finditer(pattern, plan, re.IGNORECASE)
            for match in matches:
                file_path = match.group(1).strip()
                if file_path:
                    operations.append(
                        {
                            "type": "create_file",
                            "path": file_path,
                            "content": self._generate_file_content(file_path, plan),
                            "reasoning": f"Creating {file_path} as specified in development plan",
                        }
                    )

        for pattern in modify_patterns:
            matches = re.finditer(pattern, plan, re.IGNORECASE)
            for match in matches:
                file_path = match.group(1).strip()
                if file_path and self._file_exists(file_path):
                    operations.append(
                        {
                            "type": "modify_file",
                            "path": file_path,
                            "content": self._generate_modification_content(
                                file_path, plan
                            ),
                            "reasoning": f"Modifying {file_path} as specified in development plan",
                        }
                    )

        # If no specific operations found, create a demo operation
        if not operations:
            operations.append(
                {
                    "type": "create_file",
                    "path": f"src/components/VybroGenerated_{int(time.time())}.svelte",
                    "content": self._generate_demo_component(plan),
                    "reasoning": "Creating demo component based on VYBRO's development plan",
                }
            )

        return operations

    def _generate_file_content(self, file_path: str, plan: str) -> str:
        """Generate appropriate content based on file type and plan"""
        import time

        if file_path.endswith(".svelte"):
            component_name = file_path.split("/")[-1].replace(".svelte", "")
            return f"""<!-- Generated by VYBRO Agent -->
<!-- Plan: {plan[:100]}... -->
<script>
  // {component_name} component
  export let title = '{component_name}';
</script>

<div class="vybro-component">
  <h2>{{title}}</h2>
  <p>Generated by Vybe Method MAS at {time.strftime('%Y-%m-%d %H:%M:%S')}</p>
</div>

<style>
  .vybro-component {{
    padding: 1rem;
    border: 2px solid #00ffff;
    border-radius: 8px;
    background: rgba(0, 255, 255, 0.1);
  }}
</style>"""

        elif file_path.endswith(".js") or file_path.endswith(".ts"):
            return f"""// Generated by VYBRO Agent
// Plan: {plan[:100]}...

export default {{
  name: '{file_path.split('/')[-1].replace('.js', '').replace('.ts', '')}',
  generated: '{time.strftime('%Y-%m-%d %H:%M:%S')}',

  init() {{
    console.log('VYBRO generated module initialized');
  }}
}};"""

        elif file_path.endswith(".md"):
            return f"""# Generated by VYBRO Agent

**Generated:** {time.strftime('%Y-%m-%d %H:%M:%S')}

**Plan:** {plan[:200]}...

## Implementation Notes

This file was automatically generated by the Vybe Method MAS system.
"""

        else:
            return f"# Generated by VYBRO Agent\n# Plan: {plan[:100]}...\n# Generated at: {time.strftime('%Y-%m-%d %H:%M:%S')}\n"

    def _generate_modification_content(self, file_path: str, plan: str) -> str:
        """Generate modification content for existing files"""
        try:
            with open(self.project_root / file_path, "r") as f:
                existing_content = f.read()

            # Add a comment about the modification
            modification_comment = (
                f"\n<!-- Modified by VYBRO Agent: {plan[:100]}... -->\n"
            )

            if file_path.endswith(".svelte"):
                # Add modification comment before closing tag
                if "</div>" in existing_content:
                    return existing_content.replace(
                        "</div>", f"{modification_comment}</div>", 1
                    )

            return existing_content + modification_comment

        except Exception:
            return self._generate_file_content(file_path, plan)

    def _generate_demo_component(self, plan: str) -> str:
        """Generate a demo component when no specific operations are found"""
        import time

        return f"""<!-- Demo Component Generated by VYBRO Agent -->
<script>
  import {{ onMount }} from 'svelte';

  let message = 'VYBRO Agent Active';
  let timestamp = '{time.strftime('%Y-%m-%d %H:%M:%S')}';

  onMount(() => {{
    console.log('VYBRO demo component mounted');
  }});
</script>

<div class="vybro-demo">
  <h3>🤖 Vybe Method MAS Demo</h3>
  <p><strong>Message:</strong> {{message}}</p>
  <p><strong>Generated:</strong> {{timestamp}}</p>
  <p><strong>Plan:</strong> {plan[:150]}...</p>

  <div class="status">
    <span class="indicator"></span>
    Agent-Codebase Integration Active
  </div>
</div>

<style>
  .vybro-demo {{
    padding: 1.5rem;
    border: 2px solid #ff00ff;
    border-radius: 12px;
    background: linear-gradient(135deg, rgba(255, 0, 255, 0.1), rgba(0, 255, 255, 0.1));
    margin: 1rem 0;
  }}

  .status {{
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1rem;
    font-weight: bold;
    color: #00ffff;
  }}

  .indicator {{
    width: 8px;
    height: 8px;
    background: #00ffff;
    border-radius: 50%;
    animation: pulse 2s infinite;
  }}

  @keyframes pulse {{
    0%, 100% {{ opacity: 1; }}
    50% {{ opacity: 0.3; }}
  }}
</style>"""

    def _file_exists(self, file_path: str) -> bool:
        """Check if a file exists in the project"""
        return (self.project_root / file_path).exists()
