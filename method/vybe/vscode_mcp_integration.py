"""
VS Code MCP Integration for Vybe Method
Implements Model Context Protocol servers for VS Code integration
Based on analysis of competitive developer stack
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any
from pathlib import Path
import subprocess
import os

# MCP Protocol imports
try:
    from mcp import Server, Tool, Resource
    from mcp.server.stdio import stdio_server
    MCP_AVAILABLE = True
except ImportError:
    MCP_AVAILABLE = False
    logging.warning("MCP not available - install with: pip install mcp")

class VybeDesktopCommanderMCP:
    """
    Desktop Commander MCP Server for Vybe Method
    Provides terminal and filesystem access for VS Code
    """
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.logger = logging.getLogger(__name__)
        self.server = None
        
        if MCP_AVAILABLE:
            self.server = Server("vybe-desktop-commander")
            self._register_tools()
    
    def _register_tools(self):
        """Register MCP tools for desktop operations"""
        
        @self.server.tool("execute_command")
        async def execute_command(command: str, cwd: str = None) -> Dict[str, Any]:
            """Execute shell command with safety checks"""
            try:
                # Safety checks for dangerous commands
                dangerous_patterns = ['rm -rf', 'sudo', 'chmod 777', 'dd if=']
                if any(pattern in command.lower() for pattern in dangerous_patterns):
                    return {
                        "success": False,
                        "error": "Dangerous command blocked for safety",
                        "output": ""
                    }
                
                # Set working directory
                work_dir = Path(cwd) if cwd else self.project_root
                
                # Execute command
                result = subprocess.run(
                    command,
                    shell=True,
                    cwd=work_dir,
                    capture_output=True,
                    text=True,
                    timeout=30
                )
                
                return {
                    "success": result.returncode == 0,
                    "returncode": result.returncode,
                    "output": result.stdout,
                    "error": result.stderr
                }
                
            except subprocess.TimeoutExpired:
                return {
                    "success": False,
                    "error": "Command timed out after 30 seconds",
                    "output": ""
                }
            except Exception as e:
                return {
                    "success": False,
                    "error": str(e),
                    "output": ""
                }
        
        @self.server.tool("list_directory")
        async def list_directory(path: str = ".") -> Dict[str, Any]:
            """List directory contents"""
            try:
                dir_path = self.project_root / path
                if not dir_path.exists():
                    return {"success": False, "error": "Directory not found"}
                
                items = []
                for item in dir_path.iterdir():
                    items.append({
                        "name": item.name,
                        "type": "directory" if item.is_dir() else "file",
                        "size": item.stat().st_size if item.is_file() else None,
                        "modified": item.stat().st_mtime
                    })
                
                return {
                    "success": True,
                    "items": items,
                    "path": str(dir_path)
                }
                
            except Exception as e:
                return {"success": False, "error": str(e)}
        
        @self.server.tool("read_file")
        async def read_file(file_path: str) -> Dict[str, Any]:
            """Read file contents"""
            try:
                full_path = self.project_root / file_path
                if not full_path.exists():
                    return {"success": False, "error": "File not found"}
                
                content = full_path.read_text(encoding='utf-8')
                return {
                    "success": True,
                    "content": content,
                    "path": str(full_path),
                    "size": len(content)
                }
                
            except Exception as e:
                return {"success": False, "error": str(e)}
        
        @self.server.tool("write_file")
        async def write_file(file_path: str, content: str) -> Dict[str, Any]:
            """Write content to file"""
            try:
                full_path = self.project_root / file_path
                full_path.parent.mkdir(parents=True, exist_ok=True)
                full_path.write_text(content, encoding='utf-8')
                
                return {
                    "success": True,
                    "path": str(full_path),
                    "size": len(content)
                }
                
            except Exception as e:
                return {"success": False, "error": str(e)}

class VybeAppwriteMCP:
    """
    Appwrite MCP Server for Vybe Method
    Provides Appwrite database and storage operations
    """
    
    def __init__(self, appwrite_config: Dict[str, str]):
        self.config = appwrite_config
        self.logger = logging.getLogger(__name__)
        self.server = None
        
        if MCP_AVAILABLE:
            self.server = Server("vybe-appwrite")
            self._register_tools()
    
    def _register_tools(self):
        """Register Appwrite MCP tools"""
        
        @self.server.tool("create_collection")
        async def create_collection(database_id: str, collection_id: str, name: str, attributes: List[Dict]) -> Dict[str, Any]:
            """Create Appwrite collection"""
            try:
                # This would integrate with actual Appwrite SDK
                return {
                    "success": True,
                    "collection_id": collection_id,
                    "message": f"Collection '{name}' created successfully"
                }
            except Exception as e:
                return {"success": False, "error": str(e)}
        
        @self.server.tool("create_document")
        async def create_document(database_id: str, collection_id: str, document_id: str, data: Dict) -> Dict[str, Any]:
            """Create Appwrite document"""
            try:
                # This would integrate with actual Appwrite SDK
                return {
                    "success": True,
                    "document_id": document_id,
                    "data": data
                }
            except Exception as e:
                return {"success": False, "error": str(e)}
        
        @self.server.tool("query_documents")
        async def query_documents(database_id: str, collection_id: str, queries: List[str] = None) -> Dict[str, Any]:
            """Query Appwrite documents"""
            try:
                # This would integrate with actual Appwrite SDK
                return {
                    "success": True,
                    "documents": [],
                    "total": 0
                }
            except Exception as e:
                return {"success": False, "error": str(e)}

class VybeSvelteMCP:
    """
    Svelte MCP Server for Vybe Method
    Provides SvelteKit component and route generation
    """
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.logger = logging.getLogger(__name__)
        self.server = None
        
        if MCP_AVAILABLE:
            self.server = Server("vybe-svelte")
            self._register_tools()
    
    def _register_tools(self):
        """Register Svelte MCP tools"""
        
        @self.server.tool("create_component")
        async def create_component(name: str, props: List[str] = None, template: str = "basic") -> Dict[str, Any]:
            """Create Svelte component"""
            try:
                component_path = self.project_root / "src" / "lib" / "components" / f"{name}.svelte"
                
                # Generate component template
                props_str = ""
                if props:
                    props_str = "\n".join([f"  export let {prop};" for prop in props])
                
                component_content = f"""<script lang="ts">
{props_str}
</script>

<div class="vybe-{name.lower()}">
  <h2>{name} Component</h2>
  <!-- Component content here -->
</div>

<style>
  .vybe-{name.lower()} {{
    /* Component styles here */
  }}
</style>
"""
                
                component_path.parent.mkdir(parents=True, exist_ok=True)
                component_path.write_text(component_content)
                
                return {
                    "success": True,
                    "component_path": str(component_path),
                    "name": name
                }
                
            except Exception as e:
                return {"success": False, "error": str(e)}
        
        @self.server.tool("create_route")
        async def create_route(path: str, layout: bool = False) -> Dict[str, Any]:
            """Create SvelteKit route"""
            try:
                route_dir = self.project_root / "src" / "routes" / path.lstrip('/')
                route_dir.mkdir(parents=True, exist_ok=True)
                
                if layout:
                    layout_file = route_dir / "+layout.svelte"
                    layout_content = """<script lang="ts">
  // Layout script
</script>

<main>
  <slot />
</main>

<style>
  main {
    /* Layout styles */
  }
</style>
"""
                    layout_file.write_text(layout_content)
                
                page_file = route_dir / "+page.svelte"
                page_content = f"""<script lang="ts">
  // Page script
</script>

<svelte:head>
  <title>{path.replace('/', ' ').title()}</title>
</svelte:head>

<div class="page-{path.replace('/', '-')}">
  <h1>{path.replace('/', ' ').title()}</h1>
  <!-- Page content here -->
</div>

<style>
  .page-{path.replace('/', '-')} {{
    /* Page styles here */
  }}
</style>
"""
                page_file.write_text(page_content)
                
                return {
                    "success": True,
                    "route_path": str(route_dir),
                    "files_created": ["page", "layout"] if layout else ["page"]
                }
                
            except Exception as e:
                return {"success": False, "error": str(e)}

class VybeLLMMCP:
    """
    LLM MCP Server for Vybe Method
    Provides local LLM management and switching
    """
    
    def __init__(self, ollama_host: str = "localhost:11434"):
        self.ollama_host = ollama_host
        self.logger = logging.getLogger(__name__)
        self.server = None
        self.current_model = "qwen3:30b-a3b"
        
        if MCP_AVAILABLE:
            self.server = Server("vybe-llm")
            self._register_tools()
    
    def _register_tools(self):
        """Register LLM MCP tools"""
        
        @self.server.tool("list_models")
        async def list_models() -> Dict[str, Any]:
            """List available Ollama models"""
            try:
                result = subprocess.run(
                    ["ollama", "list"],
                    capture_output=True,
                    text=True
                )
                
                if result.returncode == 0:
                    models = []
                    for line in result.stdout.strip().split('\n')[1:]:  # Skip header
                        if line.strip():
                            parts = line.split()
                            if parts:
                                models.append({
                                    "name": parts[0],
                                    "size": parts[1] if len(parts) > 1 else "unknown"
                                })
                    
                    return {
                        "success": True,
                        "models": models,
                        "current_model": self.current_model
                    }
                else:
                    return {"success": False, "error": result.stderr}
                    
            except Exception as e:
                return {"success": False, "error": str(e)}
        
        @self.server.tool("switch_model")
        async def switch_model(model_name: str) -> Dict[str, Any]:
            """Switch active LLM model"""
            try:
                # Validate model exists
                result = subprocess.run(
                    ["ollama", "show", model_name],
                    capture_output=True,
                    text=True
                )
                
                if result.returncode == 0:
                    self.current_model = model_name
                    return {
                        "success": True,
                        "current_model": self.current_model,
                        "message": f"Switched to model: {model_name}"
                    }
                else:
                    return {"success": False, "error": f"Model {model_name} not found"}
                    
            except Exception as e:
                return {"success": False, "error": str(e)}
        
        @self.server.tool("generate_response")
        async def generate_response(prompt: str, model: str = None) -> Dict[str, Any]:
            """Generate LLM response"""
            try:
                use_model = model or self.current_model
                
                # This would integrate with actual Ollama API
                return {
                    "success": True,
                    "response": f"Generated response using {use_model}",
                    "model": use_model,
                    "tokens": 100
                }
                
            except Exception as e:
                return {"success": False, "error": str(e)}

class VybeMCPOrchestrator:
    """
    Orchestrator for all Vybe MCP servers
    Manages VS Code integration and server lifecycle
    """
    
    def __init__(self, project_root: str, appwrite_config: Dict[str, str]):
        self.project_root = project_root
        self.appwrite_config = appwrite_config
        self.logger = logging.getLogger(__name__)
        
        # Initialize MCP servers
        self.desktop_commander = VybeDesktopCommanderMCP(project_root)
        self.appwrite_mcp = VybeAppwriteMCP(appwrite_config)
        self.svelte_mcp = VybeSvelteMCP(project_root)
        self.llm_mcp = VybeLLMMCP()
        
        self.servers = [
            self.desktop_commander,
            self.appwrite_mcp,
            self.svelte_mcp,
            self.llm_mcp
        ]
    
    async def start_all_servers(self):
        """Start all MCP servers"""
        if not MCP_AVAILABLE:
            self.logger.error("MCP not available - cannot start servers")
            return False
        
        try:
            for server_wrapper in self.servers:
                if server_wrapper.server:
                    # Start server (implementation depends on MCP library)
                    self.logger.info(f"Starting MCP server: {server_wrapper.server.name}")
            
            self.logger.info("All Vybe MCP servers started successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start MCP servers: {e}")
            return False
    
    async def stop_all_servers(self):
        """Stop all MCP servers"""
        try:
            for server_wrapper in self.servers:
                if server_wrapper.server:
                    # Stop server (implementation depends on MCP library)
                    self.logger.info(f"Stopping MCP server: {server_wrapper.server.name}")
            
            self.logger.info("All Vybe MCP servers stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping MCP servers: {e}")
    
    def get_server_status(self) -> Dict[str, Any]:
        """Get status of all MCP servers"""
        return {
            "mcp_available": MCP_AVAILABLE,
            "servers": [
                {
                    "name": server.server.name if server.server else "unknown",
                    "status": "running" if server.server else "not_available"
                }
                for server in self.servers
            ]
        }


# VS Code settings.json configuration for MCP integration
VSCODE_MCP_CONFIG = {
    "mcp.servers": {
        "vybe-desktop-commander": {
            "command": "python",
            "args": ["-m", "method.vybe.vscode_mcp_integration", "desktop-commander"],
            "env": {
                "VYBE_PROJECT_ROOT": "${workspaceFolder}"
            }
        },
        "vybe-appwrite": {
            "command": "python", 
            "args": ["-m", "method.vybe.vscode_mcp_integration", "appwrite"],
            "env": {
                "APPWRITE_ENDPOINT": "https://cloud.appwrite.io/v1",
                "APPWRITE_PROJECT_ID": "${env:APPWRITE_PROJECT_ID}",
                "APPWRITE_API_KEY": "${env:APPWRITE_API_KEY}"
            }
        },
        "vybe-svelte": {
            "command": "python",
            "args": ["-m", "method.vybe.vscode_mcp_integration", "svelte"],
            "env": {
                "VYBE_PROJECT_ROOT": "${workspaceFolder}"
            }
        },
        "vybe-llm": {
            "command": "python",
            "args": ["-m", "method.vybe.vscode_mcp_integration", "llm"],
            "env": {
                "OLLAMA_HOST": "localhost:11434"
            }
        }
    }
}
