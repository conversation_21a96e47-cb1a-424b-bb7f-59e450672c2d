#!/usr/bin/env python3
"""
Vybe Method WebSocket Server
Broadcasts real agent activities to Observatory
STORY-MAS-002: Observatory Real-Time Integration
"""

import asyncio
import websockets
import json
import logging
import time
from datetime import datetime
from typing import Set, Dict, Any
import threading
import queue

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VybeWebSocketServer:
    def __init__(self, host='localhost', port=8007):
        self.host = host
        self.port = port
        self.clients: Set[websockets.WebSocketServerProtocol] = set()
        self.message_queue = queue.Queue()
        self.is_running = False
        self._activity_monitor_event = asyncio.Event()
        self._error_recovery_event = asyncio.Event()
        self._monitoring_event = asyncio.Event()
        
    async def register_client(self, websocket):
        """Register a new WebSocket client"""
        self.clients.add(websocket)
        logger.info(f"Client connected. Total clients: {len(self.clients)}")
        
        # Send welcome message
        await self.send_to_client(websocket, {
            "type": "welcome",
            "message": "Connected to Vybe Method MAS",
            "timestamp": datetime.now().isoformat(),
            "server": "vybe-websocket-server"
        })
    
    async def unregister_client(self, websocket):
        """Unregister a WebSocket client"""
        self.clients.discard(websocket)
        logger.info(f"Client disconnected. Total clients: {len(self.clients)}")
    
    async def send_to_client(self, websocket, message):
        """Send message to a specific client"""
        try:
            await websocket.send(json.dumps(message))
        except websockets.exceptions.ConnectionClosed:
            await self.unregister_client(websocket)
        except Exception as e:
            logger.error(f"Error sending message to client: {e}")
    
    async def broadcast_message(self, message):
        """Broadcast message to all connected clients"""
        if not self.clients:
            return
            
        # Add timestamp if not present
        if 'timestamp' not in message:
            message['timestamp'] = datetime.now().isoformat()
        
        logger.info(f"Broadcasting message: {message.get('type', 'unknown')} from {message.get('agent', 'unknown')}")
        
        # Send to all clients
        disconnected_clients = set()
        for client in self.clients:
            try:
                await self.send_to_client(client, message)
            except Exception as e:
                logger.error(f"Failed to send to client: {e}")
                disconnected_clients.add(client)
        
        # Remove disconnected clients
        for client in disconnected_clients:
            await self.unregister_client(client)
    
    async def handle_client_message(self, websocket, message_data):
        """Handle incoming messages from clients"""
        try:
            message = json.loads(message_data)
            message_type = message.get('type', 'unknown')
            
            if message_type == 'handshake':
                logger.info(f"Handshake received from {message.get('client', 'unknown')}")
                await self.send_to_client(websocket, {
                    "type": "handshake_ack",
                    "status": "connected",
                    "timestamp": datetime.now().isoformat()
                })
            elif message_type == 'ping':
                await self.send_to_client(websocket, {
                    "type": "pong",
                    "timestamp": datetime.now().isoformat()
                })
            else:
                logger.info(f"Received message type: {message_type}")
                
        except json.JSONDecodeError:
            logger.error("Invalid JSON received from client")
        except Exception as e:
            logger.error(f"Error handling client message: {e}")
    
    async def client_handler(self, websocket, path):
        """Handle WebSocket client connections"""
        await self.register_client(websocket)
        
        try:
            async for message in websocket:
                await self.handle_client_message(websocket, message)
        except websockets.exceptions.ConnectionClosed:
            pass
        except Exception as e:
            logger.error(f"Client handler error: {e}")
        finally:
            await self.unregister_client(websocket)
    
    async def message_processor(self):
        """Process messages from the queue and broadcast them"""
        while self.is_running:
            try:
                # Check for messages in queue (non-blocking)
                try:
                    message = self.message_queue.get_nowait()
                    await self.broadcast_message(message)
                except queue.Empty:
                    pass
                
                # Event-driven waiting to prevent busy waiting
                try:
                    await asyncio.wait_for(self._monitoring_event.wait(), timeout=0.1)
                    self._monitoring_event.clear()
                except asyncio.TimeoutError:
                    pass  # Continue normal processing
                
            except Exception as e:
                logger.error(f"Message processor error: {e}")
                # Event-driven waiting instead of sleep
                try:
                    await asyncio.wait_for(self._monitoring_event.wait(), timeout=1.0)
                    self._monitoring_event.clear()
                except asyncio.TimeoutError:
                    pass  # Continue normal monitoring cycle
    
    def add_message(self, message: Dict[str, Any]):
        """Add message to broadcast queue (thread-safe)"""
        try:
            self.message_queue.put_nowait(message)
        except queue.Full:
            logger.warning("Message queue is full, dropping message")
    
    async def capture_real_agent_communication(self):
        """Capture REAL agent-to-agent communication from Vybe Method"""
        import subprocess
        import json

        while self.is_running:
            try:
                # Execute real Vybe Method agent coordination
                result = subprocess.run([
                    'python3', 'method/vybe/vybe_commands.py', 'agent-status'
                ], capture_output=True, text=True, timeout=10)

                if result.returncode == 0 and result.stdout:
                    # Parse real agent status
                    try:
                        agent_data = json.loads(result.stdout)

                        # Broadcast real agent conversations
                        for conversation in agent_data.get('conversations', []):
                            await self.broadcast_message({
                                "type": "agent_conversation",
                                "agent": conversation.get('from_agent'),
                                "data": {
                                    "conversation": {
                                        "from": conversation.get('from_agent'),
                                        "to": conversation.get('to_agent'),
                                        "message": conversation.get('message'),
                                        "context": conversation.get('context', 'Real agent coordination')
                                    }
                                }
                            })

                        # Broadcast real agent activities
                        for activity in agent_data.get('activities', []):
                            await self.broadcast_message({
                                "type": "agent_activity",
                                "agent": activity.get('agent'),
                                "data": {
                                    "activity": {
                                        "action": activity.get('action'),
                                        "target": activity.get('target'),
                                        "details": activity.get('details')
                                    }
                                }
                            })

                    except json.JSONDecodeError:
                        logger.warning("Failed to parse agent status JSON")

                # Check for real file changes
                await self.capture_real_file_changes()

                # Event-driven waiting before next check
                try:
                    await asyncio.wait_for(self._monitoring_event.wait(), timeout=5.0)
                    self._monitoring_event.clear()
                except asyncio.TimeoutError:
                    pass  # Continue normal monitoring cycle

            except subprocess.TimeoutExpired:
                logger.warning("Agent status check timed out")
                # Event-driven error recovery
                try:
                    await asyncio.wait_for(self._error_recovery_event.wait(), timeout=10.0)
                    self._error_recovery_event.clear()
                except asyncio.TimeoutError:
                    pass  # Continue after timeout
            except Exception as e:
                logger.error(f"Real agent communication error: {e}")
                # NO SIMULATION FALLBACK - System operates with real implementations only
                logger.info("Waiting for real agent communication to be available...")
                # Event-driven error recovery
                try:
                    await asyncio.wait_for(self._error_recovery_event.wait(), timeout=30.0)
                    self._error_recovery_event.clear()
                except asyncio.TimeoutError:
                    pass  # Continue after timeout

    async def capture_real_file_changes(self):
        """Capture actual file modifications from agent operations"""
        import os
        import time

        try:
            # Monitor key directories for real changes
            watch_dirs = [
                'src/routes',
                'src/lib',
                'method/vybe',
                'docs'
            ]

            current_time = time.time()

            for watch_dir in watch_dirs:
                if os.path.exists(watch_dir):
                    for root, dirs, files in os.walk(watch_dir):
                        for file in files:
                            file_path = os.path.join(root, file)
                            try:
                                mtime = os.path.getmtime(file_path)

                                # File modified in last 10 seconds
                                if current_time - mtime < 10:
                                    file_size = os.path.getsize(file_path)

                                    await self.broadcast_message({
                                        "type": "file_change",
                                        "agent": "VYBE_SYSTEM",
                                        "data": {
                                            "file_change": {
                                                "file_path": file_path,
                                                "action": "modified",
                                                "size": file_size
                                            }
                                        }
                                    })

                            except (OSError, IOError):
                                continue

        except Exception as e:
            logger.error(f"File change monitoring error: {e}")

    async def capture_real_agent_activity(self):
        """Capture REAL agent activity from actual MAS operations - NO SIMULATION"""
        logger.info("🔍 Starting REAL agent activity monitoring - NO SIMULATIONS")

        while self.is_running:
            try:
                # Monitor real agent communication logs
                await self._monitor_agent_logs()

                # Monitor real file changes from agents
                await self._monitor_real_file_changes()

                # Monitor real MAS task execution
                await self._monitor_mas_tasks()

                # Wait before next monitoring cycle using event-driven approach
                try:
                    await asyncio.wait_for(self._activity_monitor_event.wait(), timeout=5.0)
                    self._activity_monitor_event.clear()
                except asyncio.TimeoutError:
                    pass  # Continue normal monitoring cycle

            except Exception as e:
                logger.error(f"Real agent monitoring error: {e}")
                # Use event-driven error recovery
                try:
                    await asyncio.wait_for(self._error_recovery_event.wait(), timeout=10.0)
                    self._error_recovery_event.clear()
                except asyncio.TimeoutError:
                    pass  # Continue after timeout

    async def _monitor_agent_logs(self):
        """Monitor real agent communication logs"""
        try:
            # Check for real agent communication files
            log_paths = [
                'logs/agent_communication.log',
                'method/vybe/logs/agent_activity.log',
                'logs/mas_activity.log'
            ]

            for log_path in log_paths:
                if os.path.exists(log_path):
                    # Read recent log entries for real agent activity
                    with open(log_path, 'r') as f:
                        lines = f.readlines()
                        # Process only new lines since last check
                        for line in lines[-10:]:  # Last 10 lines
                            if '[AGENT_COMM]' in line:
                                try:
                                    # Parse real agent communication
                                    comm_data = json.loads(line.split('[AGENT_COMM]')[1])
                                    await self.broadcast_message({
                                        "type": "agent_conversation",
                                        "agent": comm_data.get("from_agent", "UNKNOWN"),
                                        "data": {"conversation": comm_data}
                                    })
                                except json.JSONDecodeError:
                                    continue
        except Exception as e:
            logger.debug(f"Agent log monitoring: {e}")

    async def _monitor_real_file_changes(self):
        """Monitor real file changes made by agents"""
        try:
            # Monitor git status for real changes
            import subprocess
            result = subprocess.run(['git', 'status', '--porcelain'],
                                  capture_output=True, text=True, timeout=5)

            if result.returncode == 0 and result.stdout.strip():
                changes = result.stdout.strip().split('\n')
                for change in changes:
                    if change.strip():
                        status = change[:2].strip()
                        file_path = change[3:].strip()

                        # Broadcast real file change
                        await self.broadcast_message({
                            "type": "file_change",
                            "agent": "MAS",
                            "data": {
                                "file_change": {
                                    "file_path": file_path,
                                    "action": "modified" if status == "M" else "created" if status == "A" else "unknown",
                                    "timestamp": datetime.now().isoformat()
                                }
                            }
                        })
        except Exception as e:
            logger.debug(f"File change monitoring: {e}")

    async def _monitor_mas_tasks(self):
        """Monitor real MAS task execution"""
        try:
            # Check for active MAS processes
            import psutil
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] and 'python' in proc.info['name'].lower():
                        cmdline = ' '.join(proc.info['cmdline'] or [])
                        if any(keyword in cmdline.lower() for keyword in ['vybe', 'mas', 'agent', 'bmad']):
                            # Real MAS process detected
                            await self.broadcast_message({
                                "type": "agent_activity",
                                "agent": "MAS",
                                "data": {
                                    "activity": {
                                        "action": "Real MAS process active",
                                        "target": "System",
                                        "details": f"Process: {proc.info['name']} (PID: {proc.info['pid']})"
                                    }
                                }
                            })
                            break
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        except Exception as e:
            logger.debug(f"MAS task monitoring: {e}")
    
    async def start_server(self):
        """Start the WebSocket server"""
        self.is_running = True
        logger.info(f"Starting Vybe WebSocket server on {self.host}:{self.port}")
        
        # Start message processor
        asyncio.create_task(self.message_processor())

        # Start REAL agent activity capture - NO SIMULATIONS
        asyncio.create_task(self.capture_real_agent_activity())
        
        # Start WebSocket server
        async with websockets.serve(self.client_handler, self.host, self.port):
            logger.info(f"✅ Vybe WebSocket server running on ws://{self.host}:{self.port}")
            await asyncio.Future()  # Run forever
    
    def stop_server(self):
        """Stop the WebSocket server"""
        self.is_running = False
        logger.info("Stopping Vybe WebSocket server")

# Global server instance
vybe_ws_server = VybeWebSocketServer()

def broadcast_agent_message(message: Dict[str, Any]):
    """Thread-safe function to broadcast agent messages"""
    vybe_ws_server.add_message(message)

async def main():
    """Main entry point"""
    try:
        await vybe_ws_server.start_server()
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {e}")
    finally:
        vybe_ws_server.stop_server()

if __name__ == "__main__":
    asyncio.run(main())
