"""
Web Search Agent for Real-time Information Retrieval
Provides autonomous agents with current web information
"""

import asyncio
import httpx
import json
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from dataclasses import dataclass
from urllib.parse import quote_plus


@dataclass
class SearchResult:
    """Web search result"""
    title: str
    url: str
    snippet: str
    source: str
    timestamp: datetime


@dataclass
class MarketResearchData:
    """Market research compilation"""
    business_idea: str
    market_size: List[SearchResult]
    competitors: List[SearchResult]
    revenue_models: List[SearchResult]
    target_audience: List[SearchResult]
    trends: List[SearchResult]
    timestamp: datetime


class WebSearchAgent:
    """
    Web search agent for autonomous information retrieval
    Uses multiple search providers for comprehensive results
    """
    
    def __init__(self, api_key: Optional[str] = None):
        self.logger = logging.getLogger(__name__)
        self.api_key = api_key
        self.client = httpx.AsyncClient(timeout=30.0)
        
        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 1.0  # seconds
        
        # Search providers
        self.providers = {
            'duckduckgo': self._search_duckduckgo,
            'serper': self._search_serper,  # If API key provided
            'brave': self._search_brave     # Alternative provider
        }
        
        self.logger.info("WebSearchAgent initialized")
    
    async def search(self, query: str, num_results: int = 5, provider: str = 'duckduckgo') -> List[SearchResult]:
        """Search web for information"""
        try:
            # Rate limiting
            await self._rate_limit()
            
            # Use specified provider or fallback
            search_func = self.providers.get(provider, self._search_duckduckgo)
            results = await search_func(query, num_results)
            
            self.logger.info(f"Search completed: '{query}' - {len(results)} results")
            return results
            
        except Exception as e:
            self.logger.error(f"Search failed for '{query}': {e}")
            return []
    
    async def research_market(self, business_idea: str) -> MarketResearchData:
        """Comprehensive market research for business idea"""
        try:
            self.logger.info(f"Starting market research for: {business_idea}")
            
            # Define research queries
            research_queries = {
                'market_size': [
                    f"{business_idea} market size 2025",
                    f"{business_idea} industry revenue statistics",
                    f"{business_idea} market growth trends"
                ],
                'competitors': [
                    f"{business_idea} top companies competitors",
                    f"{business_idea} leading brands market share",
                    f"best {business_idea} platforms services"
                ],
                'revenue_models': [
                    f"{business_idea} business model revenue streams",
                    f"how to monetize {business_idea}",
                    f"{business_idea} pricing strategies"
                ],
                'target_audience': [
                    f"{business_idea} target customers demographics",
                    f"who uses {business_idea} services",
                    f"{business_idea} customer personas"
                ],
                'trends': [
                    f"{business_idea} trends 2025",
                    f"future of {business_idea} industry",
                    f"{business_idea} innovation opportunities"
                ]
            }
            
            # Conduct research
            research_data = MarketResearchData(
                business_idea=business_idea,
                market_size=[],
                competitors=[],
                revenue_models=[],
                target_audience=[],
                trends=[],
                timestamp=datetime.now()
            )
            
            # Execute searches for each category
            for category, queries in research_queries.items():
                category_results = []
                
                for query in queries:
                    results = await self.search(query, num_results=3)
                    category_results.extend(results)
                    
                    # Rate limiting between queries
                    # Event-driven rate limiting instead of sleep
                rate_limit_event = asyncio.Event()
                try:
                    await asyncio.wait_for(rate_limit_event.wait(), timeout=1.0)
                    rate_limit_event.clear()
                except asyncio.TimeoutError:
                    pass  # Rate limiting completed
                
                # Store results in appropriate category
                setattr(research_data, category, category_results)
            
            self.logger.info(f"Market research completed for: {business_idea}")
            return research_data
            
        except Exception as e:
            self.logger.error(f"Market research failed for '{business_idea}': {e}")
            return MarketResearchData(
                business_idea=business_idea,
                market_size=[], competitors=[], revenue_models=[],
                target_audience=[], trends=[],
                timestamp=datetime.now()
            )
    
    async def search_technical_info(self, technology: str, context: str = "") -> List[SearchResult]:
        """Search for technical information and best practices"""
        try:
            queries = [
                f"{technology} best practices 2025",
                f"{technology} tutorial guide {context}",
                f"{technology} documentation examples",
                f"how to use {technology} {context}"
            ]
            
            all_results = []
            for query in queries:
                results = await self.search(query, num_results=3)
                all_results.extend(results)
                # Event-driven rate limiting instead of sleep
                rate_limit_event = asyncio.Event()
                try:
                    await asyncio.wait_for(rate_limit_event.wait(), timeout=1.0)
                    rate_limit_event.clear()
                except asyncio.TimeoutError:
                    pass  # Rate limiting completed
            
            return all_results
            
        except Exception as e:
            self.logger.error(f"Technical search failed for '{technology}': {e}")
            return []
    
    async def search_business_opportunities(self, industry: str) -> List[SearchResult]:
        """Search for business opportunities in an industry"""
        try:
            queries = [
                f"{industry} business opportunities 2025",
                f"profitable {industry} business ideas",
                f"{industry} startup opportunities",
                f"emerging {industry} markets"
            ]
            
            all_results = []
            for query in queries:
                results = await self.search(query, num_results=4)
                all_results.extend(results)
                # Event-driven rate limiting instead of sleep
                rate_limit_event = asyncio.Event()
                try:
                    await asyncio.wait_for(rate_limit_event.wait(), timeout=1.0)
                    rate_limit_event.clear()
                except asyncio.TimeoutError:
                    pass  # Rate limiting completed
            
            return all_results
            
        except Exception as e:
            self.logger.error(f"Business opportunity search failed for '{industry}': {e}")
            return []
    
    async def _search_duckduckgo(self, query: str, num_results: int) -> List[SearchResult]:
        """Search using DuckDuckGo Instant Answer API"""
        try:
            # DuckDuckGo Instant Answer API
            url = "https://api.duckduckgo.com/"
            params = {
                "q": query,
                "format": "json",
                "no_html": "1",
                "skip_disambig": "1"
            }
            
            response = await self.client.get(url, params=params)
            data = response.json()
            
            results = []
            
            # Process RelatedTopics
            for item in data.get("RelatedTopics", [])[:num_results]:
                if isinstance(item, dict) and "Text" in item and "FirstURL" in item:
                    results.append(SearchResult(
                        title=item.get("Text", "")[:100] + "..." if len(item.get("Text", "")) > 100 else item.get("Text", ""),
                        url=item.get("FirstURL", ""),
                        snippet=item.get("Text", ""),
                        source="DuckDuckGo",
                        timestamp=datetime.now()
                    ))
            
            # If no RelatedTopics, try Abstract
            if not results and data.get("Abstract"):
                results.append(SearchResult(
                    title=data.get("Heading", query),
                    url=data.get("AbstractURL", ""),
                    snippet=data.get("Abstract", ""),
                    source="DuckDuckGo",
                    timestamp=datetime.now()
                ))
            
            return results[:num_results]
            
        except Exception as e:
            self.logger.error(f"DuckDuckGo search failed: {e}")
            return []
    
    async def _search_serper(self, query: str, num_results: int) -> List[SearchResult]:
        """Search using Serper API (requires API key)"""
        try:
            if not self.api_key:
                return await self._search_duckduckgo(query, num_results)
            
            url = "https://google.serper.dev/search"
            headers = {
                "X-API-KEY": self.api_key,
                "Content-Type": "application/json"
            }
            data = {
                "q": query,
                "num": num_results
            }
            
            response = await self.client.post(url, headers=headers, json=data)
            search_data = response.json()
            
            results = []
            for item in search_data.get("organic", [])[:num_results]:
                results.append(SearchResult(
                    title=item.get("title", ""),
                    url=item.get("link", ""),
                    snippet=item.get("snippet", ""),
                    source="Google",
                    timestamp=datetime.now()
                ))
            
            return results
            
        except Exception as e:
            self.logger.error(f"Serper search failed: {e}")
            return await self._search_duckduckgo(query, num_results)
    
    async def _search_brave(self, query: str, num_results: int) -> List[SearchResult]:
        """Search using Brave Search API (alternative)"""
        try:
            # This would require Brave Search API key
            # For now, fallback to DuckDuckGo
            return await self._search_duckduckgo(query, num_results)
            
        except Exception as e:
            self.logger.error(f"Brave search failed: {e}")
            return []
    
    async def _rate_limit(self):
        """Implement rate limiting"""
        current_time = asyncio.get_event_loop().time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last
            await asyncio.sleep(sleep_time)
        
        self.last_request_time = asyncio.get_event_loop().time()
    
    async def close(self):
        """Close HTTP client"""
        await self.client.aclose()
    
    def __del__(self):
        """Cleanup on deletion"""
        try:
            asyncio.create_task(self.close())
        except:
            pass
