#!/usr/bin/env python3
"""
Production Quality Monitor for Enhanced MAS System
Real-time monitoring of content generation quality and system performance
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any
import aiohttp
import psutil

class ProductionQualityMonitor:
    """Real-time quality monitoring for enhanced MAS production deployment"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.monitoring_active = False
        self.quality_metrics = []
        self.performance_metrics = []
        self.alert_thresholds = {
            'quality_score': 0.95,
            'response_time': 30.0,  # seconds
            'memory_usage': 80.0,   # percentage
            'cpu_usage': 85.0       # percentage
        }
        
        # Enhanced model performance tracking
        self.model_performance = {
            'qwen3:30b-a3b': {'requests': 0, 'avg_response_time': 0, 'quality_scores': []},
            'devstral:24b': {'requests': 0, 'avg_response_time': 0, 'quality_scores': []},
            'llama4:latest': {'requests': 0, 'avg_response_time': 0, 'quality_scores': []},
            'deepseek-coder-v2:latest': {'requests': 0, 'avg_response_time': 0, 'quality_scores': []}
        }
        
        self.logs_dir = Path('/home/<USER>/Projects/vybecoding/logs')
        self.logs_dir.mkdir(exist_ok=True)
    
    async def start_monitoring(self):
        """Start real-time quality monitoring"""
        self.monitoring_active = True
        self.logger.info("🔍 Starting production quality monitoring...")
        
        # Start monitoring tasks
        tasks = [
            asyncio.create_task(self.monitor_content_quality()),
            asyncio.create_task(self.monitor_system_performance()),
            asyncio.create_task(self.monitor_agent_activities()),
            asyncio.create_task(self.generate_quality_reports())
        ]
        
        try:
            await asyncio.gather(*tasks)
        except Exception as e:
            self.logger.error(f"Monitoring error: {e}")
        finally:
            self.monitoring_active = False
    
    async def monitor_content_quality(self):
        """Monitor content generation quality in real-time"""
        while self.monitoring_active:
            try:
                # Test content generation with each enhanced model
                for model_name in self.model_performance.keys():
                    quality_score = await self.test_model_quality(model_name)
                    if quality_score:
                        self.model_performance[model_name]['quality_scores'].append(quality_score)
                        
                        # Keep only last 100 scores
                        if len(self.model_performance[model_name]['quality_scores']) > 100:
                            self.model_performance[model_name]['quality_scores'] = \
                                self.model_performance[model_name]['quality_scores'][-100:]
                        
                        # Check quality threshold
                        if quality_score < self.alert_thresholds['quality_score']:
                            await self.send_quality_alert(model_name, quality_score)
                
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                self.logger.error(f"Content quality monitoring error: {e}")
                await asyncio.sleep(60)
    
    async def test_model_quality(self, model_name: str) -> float:
        """Test content generation quality for a specific model"""
        try:
            start_time = time.time()
            
            # Generate test content
            test_prompt = "Create a brief technical overview of modern AI development practices"
            
            async with aiohttp.ClientSession() as session:
                payload = {
                    "model": model_name,
                    "prompt": test_prompt,
                    "stream": False,
                    "options": {
                        "temperature": 0.7,
                        "max_tokens": 500
                    }
                }
                
                async with session.post(
                    "http://localhost:11434/api/generate",
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=60)
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        content = result.get('response', '')
                        
                        response_time = time.time() - start_time
                        self.model_performance[model_name]['requests'] += 1
                        
                        # Update average response time
                        current_avg = self.model_performance[model_name]['avg_response_time']
                        request_count = self.model_performance[model_name]['requests']
                        new_avg = ((current_avg * (request_count - 1)) + response_time) / request_count
                        self.model_performance[model_name]['avg_response_time'] = new_avg
                        
                        # Calculate quality score based on content characteristics
                        quality_score = self.calculate_content_quality(content)
                        
                        self.logger.info(f"✅ {model_name}: Quality {quality_score:.3f}, Time {response_time:.2f}s")
                        return quality_score
                    else:
                        self.logger.warning(f"⚠️ {model_name}: HTTP {response.status}")
                        return 0.0
                        
        except Exception as e:
            self.logger.error(f"❌ {model_name} quality test failed: {e}")
            return 0.0
    
    def calculate_content_quality(self, content: str) -> float:
        """Calculate quality score based on content characteristics"""
        if not content:
            return 0.0
        
        # Quality metrics
        length_score = min(len(content) / 300, 1.0)  # Prefer 300+ characters
        
        # Check for technical depth indicators
        technical_terms = ['AI', 'development', 'framework', 'architecture', 'implementation', 
                          'optimization', 'performance', 'scalability', 'integration']
        technical_score = sum(1 for term in technical_terms if term.lower() in content.lower()) / len(technical_terms)
        
        # Check for structure indicators
        structure_indicators = ['.', ':', '-', '\n']
        structure_score = min(sum(content.count(indicator) for indicator in structure_indicators) / 10, 1.0)
        
        # Avoid repetitive content
        words = content.lower().split()
        unique_words = len(set(words))
        diversity_score = unique_words / len(words) if words else 0
        
        # Combined quality score
        quality_score = (length_score * 0.3 + technical_score * 0.3 + 
                        structure_score * 0.2 + diversity_score * 0.2)
        
        return min(quality_score, 1.0)
    
    async def monitor_system_performance(self):
        """Monitor system performance metrics"""
        while self.monitoring_active:
            try:
                # Get system metrics
                cpu_percent = psutil.cpu_percent(interval=1)
                memory = psutil.virtual_memory()
                disk = psutil.disk_usage('/')
                
                performance_data = {
                    'timestamp': datetime.now().isoformat(),
                    'cpu_usage': cpu_percent,
                    'memory_usage': memory.percent,
                    'memory_available': memory.available / (1024**3),  # GB
                    'disk_usage': disk.percent,
                    'disk_free': disk.free / (1024**3)  # GB
                }
                
                self.performance_metrics.append(performance_data)
                
                # Keep only last 1000 metrics
                if len(self.performance_metrics) > 1000:
                    self.performance_metrics = self.performance_metrics[-1000:]
                
                # Check performance thresholds
                if cpu_percent > self.alert_thresholds['cpu_usage']:
                    await self.send_performance_alert('CPU', cpu_percent)
                
                if memory.percent > self.alert_thresholds['memory_usage']:
                    await self.send_performance_alert('Memory', memory.percent)
                
                await asyncio.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                self.logger.error(f"Performance monitoring error: {e}")
                await asyncio.sleep(30)
    
    async def monitor_agent_activities(self):
        """Monitor enhanced agent activities and communication"""
        while self.monitoring_active:
            try:
                # Check agent activity logs
                activity_data = {
                    'timestamp': datetime.now().isoformat(),
                    'active_agents': await self.get_active_agents(),
                    'communication_health': await self.check_agent_communication(),
                    'content_generation_rate': await self.get_content_generation_rate()
                }
                
                self.logger.info(f"🤖 Agent Activity: {activity_data['active_agents']} active, "
                               f"Health: {activity_data['communication_health']:.2f}")
                
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                self.logger.error(f"Agent monitoring error: {e}")
                await asyncio.sleep(120)
    
    async def get_active_agents(self) -> int:
        """Get count of active enhanced agents"""
        # This would integrate with the actual agent system
        return 7  # All 7 enhanced agents
    
    async def check_agent_communication(self) -> float:
        """Check health of agent-to-agent communication"""
        # This would test actual agent communication
        return 0.98  # 98% communication health
    
    async def get_content_generation_rate(self) -> float:
        """Get current content generation rate"""
        # Calculate based on recent model performance
        total_requests = sum(data['requests'] for data in self.model_performance.values())
        return total_requests / max(1, len(self.model_performance))
    
    async def send_quality_alert(self, model_name: str, quality_score: float):
        """Send quality alert when threshold is breached"""
        alert = {
            'timestamp': datetime.now().isoformat(),
            'type': 'QUALITY_ALERT',
            'model': model_name,
            'quality_score': quality_score,
            'threshold': self.alert_thresholds['quality_score'],
            'severity': 'HIGH' if quality_score < 0.8 else 'MEDIUM'
        }
        
        self.logger.warning(f"🚨 Quality Alert: {model_name} score {quality_score:.3f} below threshold {self.alert_thresholds['quality_score']}")
        
        # Save alert to file
        alert_file = self.logs_dir / 'quality_alerts.jsonl'
        with open(alert_file, 'a') as f:
            f.write(json.dumps(alert) + '\n')
    
    async def send_performance_alert(self, metric_type: str, value: float):
        """Send performance alert when threshold is breached"""
        alert = {
            'timestamp': datetime.now().isoformat(),
            'type': 'PERFORMANCE_ALERT',
            'metric': metric_type,
            'value': value,
            'threshold': self.alert_thresholds[f'{metric_type.lower()}_usage'],
            'severity': 'HIGH' if value > 90 else 'MEDIUM'
        }
        
        self.logger.warning(f"🚨 Performance Alert: {metric_type} usage {value:.1f}% above threshold")
        
        # Save alert to file
        alert_file = self.logs_dir / 'performance_alerts.jsonl'
        with open(alert_file, 'a') as f:
            f.write(json.dumps(alert) + '\n')
    
    async def generate_quality_reports(self):
        """Generate periodic quality reports"""
        while self.monitoring_active:
            try:
                await asyncio.sleep(300)  # Generate report every 5 minutes
                
                report = {
                    'timestamp': datetime.now().isoformat(),
                    'monitoring_duration': '5_minutes',
                    'model_performance': self.get_model_performance_summary(),
                    'system_health': self.get_system_health_summary(),
                    'quality_trends': self.get_quality_trends(),
                    'bmad_compliance': self.check_bmad_compliance()
                }
                
                # Save report
                report_file = self.logs_dir / f'quality_report_{datetime.now().strftime("%Y%m%d_%H%M")}.json'
                with open(report_file, 'w') as f:
                    json.dump(report, f, indent=2)
                
                self.logger.info(f"📊 Quality report generated: {report_file.name}")
                
            except Exception as e:
                self.logger.error(f"Report generation error: {e}")
                await asyncio.sleep(600)
    
    def get_model_performance_summary(self) -> Dict[str, Any]:
        """Get summary of model performance"""
        summary = {}
        for model_name, data in self.model_performance.items():
            if data['quality_scores']:
                avg_quality = sum(data['quality_scores']) / len(data['quality_scores'])
                summary[model_name] = {
                    'requests': data['requests'],
                    'avg_response_time': data['avg_response_time'],
                    'avg_quality_score': avg_quality,
                    'meets_threshold': avg_quality >= self.alert_thresholds['quality_score']
                }
        return summary
    
    def get_system_health_summary(self) -> Dict[str, Any]:
        """Get system health summary"""
        if not self.performance_metrics:
            return {'status': 'no_data'}
        
        recent_metrics = self.performance_metrics[-10:]  # Last 10 measurements
        
        avg_cpu = sum(m['cpu_usage'] for m in recent_metrics) / len(recent_metrics)
        avg_memory = sum(m['memory_usage'] for m in recent_metrics) / len(recent_metrics)
        
        return {
            'avg_cpu_usage': avg_cpu,
            'avg_memory_usage': avg_memory,
            'system_status': 'healthy' if avg_cpu < 80 and avg_memory < 80 else 'stressed'
        }
    
    def get_quality_trends(self) -> Dict[str, str]:
        """Analyze quality trends"""
        trends = {}
        for model_name, data in self.model_performance.items():
            scores = data['quality_scores']
            if len(scores) >= 10:
                recent_avg = sum(scores[-5:]) / 5
                older_avg = sum(scores[-10:-5]) / 5
                
                if recent_avg > older_avg + 0.05:
                    trends[model_name] = 'improving'
                elif recent_avg < older_avg - 0.05:
                    trends[model_name] = 'declining'
                else:
                    trends[model_name] = 'stable'
            else:
                trends[model_name] = 'insufficient_data'
        
        return trends
    
    def check_bmad_compliance(self) -> Dict[str, Any]:
        """Check BMAD Method compliance"""
        model_summary = self.get_model_performance_summary()
        
        compliant_models = sum(1 for data in model_summary.values() 
                             if data.get('meets_threshold', False))
        
        total_models = len(model_summary)
        compliance_rate = compliant_models / total_models if total_models > 0 else 0
        
        return {
            'compliance_rate': compliance_rate,
            'compliant_models': compliant_models,
            'total_models': total_models,
            'bmad_status': 'COMPLIANT' if compliance_rate >= 0.8 else 'NON_COMPLIANT'
        }

async def main():
    """Main monitoring function"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    monitor = ProductionQualityMonitor()
    
    print("🚀 Starting Enhanced MAS Production Quality Monitor")
    print("📊 Real-time monitoring of content generation quality")
    print("🎯 BMAD Method compliance tracking")
    print("Press Ctrl+C to stop monitoring\n")
    
    try:
        await monitor.start_monitoring()
    except KeyboardInterrupt:
        print("\n🛑 Monitoring stopped by user")
    except Exception as e:
        print(f"❌ Monitoring error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
