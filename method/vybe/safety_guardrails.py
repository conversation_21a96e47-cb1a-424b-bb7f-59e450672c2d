"""
Safety Guardrails System for Autonomous Agents
Provides security validation and safety checks for agent actions
"""

import re
import ast
import logging
from typing import Dict, Any, List, Optional, Set
from datetime import datetime
from dataclasses import dataclass
from enum import Enum
from pathlib import Path


class SecurityLevel(Enum):
    """Security risk levels"""
    SAFE = "safe"
    LOW_RISK = "low_risk"
    MEDIUM_RISK = "medium_risk"
    HIGH_RISK = "high_risk"
    CRITICAL = "critical"


class ActionType(Enum):
    """Types of agent actions"""
    FILE_CREATE = "file_create"
    FILE_EDIT = "file_edit"
    FILE_DELETE = "file_delete"
    CODE_EXECUTION = "code_execution"
    NETWORK_REQUEST = "network_request"
    SYSTEM_COMMAND = "system_command"
    DATABASE_OPERATION = "database_operation"


@dataclass
class SecurityReport:
    """Security validation report"""
    is_safe: bool
    security_level: SecurityLevel
    action_type: ActionType
    violations: List[str]
    warnings: List[str]
    recommendations: List[str]
    timestamp: datetime


@dataclass
class ValidationRule:
    """Security validation rule"""
    name: str
    description: str
    pattern: Optional[str] = None
    forbidden_patterns: List[str] = None
    required_patterns: List[str] = None
    action_types: List[ActionType] = None


class SafetyGuardrails:
    """
    Comprehensive safety and security validation system
    Validates agent actions before execution
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Security rules
        self.validation_rules = self._initialize_security_rules()
        
        # Forbidden patterns
        self.forbidden_code_patterns = [
            r'eval\s*\(',
            r'exec\s*\(',
            r'__import__\s*\(',
            r'subprocess\.',
            r'os\.system',
            r'os\.popen',
            r'shell=True',
            r'rm\s+-rf',
            r'sudo\s+',
            r'chmod\s+777',
            r'\.env',
            r'password\s*=',
            r'secret\s*=',
            r'api_key\s*=',
            r'token\s*='
        ]
        
        # Safe file extensions
        self.safe_extensions = {
            '.py', '.js', '.ts', '.jsx', '.tsx', '.md', '.txt', '.json',
            '.yaml', '.yml', '.toml', '.css', '.scss', '.html', '.svelte'
        }
        
        # Forbidden paths
        self.forbidden_paths = {
            '.git', 'node_modules', '__pycache__', '.venv', 'venv',
            '.env', '.env.local', '.env.production', '/etc', '/usr',
            '/var', '/root', '/home', 'C:\\Windows', 'C:\\Program Files'
        }
        
        # Network restrictions
        self.allowed_domains = {
            'api.duckduckgo.com',
            'github.com',
            'api.github.com',
            'npmjs.com',
            'pypi.org',
            'stackoverflow.com',
            'developer.mozilla.org'
        }
        
        self.logger.info("SafetyGuardrails initialized")
    
    async def validate_file_operation(self, action_type: ActionType, 
                                    file_path: str, content: str = "") -> SecurityReport:
        """Validate file operation for safety"""
        try:
            violations = []
            warnings = []
            recommendations = []
            security_level = SecurityLevel.SAFE
            
            # Validate file path
            path_validation = self._validate_file_path(file_path)
            if path_validation['violations']:
                violations.extend(path_validation['violations'])
                security_level = SecurityLevel.HIGH_RISK
            
            # Validate file extension
            path_obj = Path(file_path)
            if path_obj.suffix and path_obj.suffix not in self.safe_extensions:
                violations.append(f"File extension {path_obj.suffix} not allowed")
                security_level = SecurityLevel.MEDIUM_RISK
            
            # Validate content if provided
            if content:
                content_validation = await self._validate_code_content(content)
                violations.extend(content_validation['violations'])
                warnings.extend(content_validation['warnings'])
                
                if content_validation['violations']:
                    security_level = SecurityLevel.HIGH_RISK
                elif content_validation['warnings']:
                    security_level = max(security_level, SecurityLevel.LOW_RISK)
            
            # Check file size
            if len(content.encode('utf-8')) > 10 * 1024 * 1024:  # 10MB
                violations.append("File size exceeds maximum allowed (10MB)")
                security_level = SecurityLevel.MEDIUM_RISK
            
            # Generate recommendations
            if violations:
                recommendations.append("Review and fix security violations before proceeding")
            if warnings:
                recommendations.append("Consider addressing security warnings")
            
            is_safe = len(violations) == 0 and security_level in [SecurityLevel.SAFE, SecurityLevel.LOW_RISK]
            
            return SecurityReport(
                is_safe=is_safe,
                security_level=security_level,
                action_type=action_type,
                violations=violations,
                warnings=warnings,
                recommendations=recommendations,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"File operation validation failed: {e}")
            return SecurityReport(
                is_safe=False,
                security_level=SecurityLevel.CRITICAL,
                action_type=action_type,
                violations=[f"Validation error: {e}"],
                warnings=[],
                recommendations=["Manual review required"],
                timestamp=datetime.now()
            )
    
    async def validate_network_request(self, url: str, method: str = "GET", 
                                     data: Dict[str, Any] = None) -> SecurityReport:
        """Validate network request for safety"""
        try:
            violations = []
            warnings = []
            recommendations = []
            security_level = SecurityLevel.SAFE
            
            # Parse URL
            from urllib.parse import urlparse
            parsed_url = urlparse(url)
            
            # Check domain whitelist
            if parsed_url.netloc not in self.allowed_domains:
                violations.append(f"Domain {parsed_url.netloc} not in allowed list")
                security_level = SecurityLevel.HIGH_RISK
            
            # Check for suspicious patterns in URL
            suspicious_patterns = [
                r'localhost',
                r'127\.0\.0\.1',
                r'192\.168\.',
                r'10\.',
                r'172\.(1[6-9]|2[0-9]|3[01])\.',
                r'file://',
                r'ftp://'
            ]
            
            for pattern in suspicious_patterns:
                if re.search(pattern, url, re.IGNORECASE):
                    violations.append(f"Suspicious URL pattern detected: {pattern}")
                    security_level = SecurityLevel.HIGH_RISK
            
            # Validate request data
            if data:
                data_str = str(data)
                for pattern in self.forbidden_code_patterns:
                    if re.search(pattern, data_str, re.IGNORECASE):
                        violations.append(f"Forbidden pattern in request data: {pattern}")
                        security_level = SecurityLevel.HIGH_RISK
            
            # Check HTTP method
            if method.upper() not in ['GET', 'POST', 'PUT', 'PATCH', 'DELETE']:
                warnings.append(f"Unusual HTTP method: {method}")
                security_level = max(security_level, SecurityLevel.LOW_RISK)
            
            is_safe = len(violations) == 0
            
            return SecurityReport(
                is_safe=is_safe,
                security_level=security_level,
                action_type=ActionType.NETWORK_REQUEST,
                violations=violations,
                warnings=warnings,
                recommendations=recommendations,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"Network request validation failed: {e}")
            return SecurityReport(
                is_safe=False,
                security_level=SecurityLevel.CRITICAL,
                action_type=ActionType.NETWORK_REQUEST,
                violations=[f"Validation error: {e}"],
                warnings=[],
                recommendations=["Manual review required"],
                timestamp=datetime.now()
            )
    
    async def validate_task(self, task: Dict[str, Any]) -> SecurityReport:
        """Validate agent task for safety"""
        try:
            violations = []
            warnings = []
            recommendations = []
            security_level = SecurityLevel.SAFE
            
            # Check task description for suspicious content
            description = task.get('description', '')
            for pattern in self.forbidden_code_patterns:
                if re.search(pattern, description, re.IGNORECASE):
                    violations.append(f"Forbidden pattern in task description: {pattern}")
                    security_level = SecurityLevel.HIGH_RISK
            
            # Check task parameters
            params = task.get('params', {})
            if params:
                params_str = str(params)
                for pattern in self.forbidden_code_patterns:
                    if re.search(pattern, params_str, re.IGNORECASE):
                        violations.append(f"Forbidden pattern in task parameters: {pattern}")
                        security_level = SecurityLevel.HIGH_RISK
            
            # Check required capabilities
            required_capabilities = task.get('required_capabilities', [])
            dangerous_capabilities = ['system_access', 'network_admin', 'file_system_root']
            
            for capability in required_capabilities:
                if capability in dangerous_capabilities:
                    violations.append(f"Dangerous capability required: {capability}")
                    security_level = SecurityLevel.HIGH_RISK
            
            is_safe = len(violations) == 0
            
            return SecurityReport(
                is_safe=is_safe,
                security_level=security_level,
                action_type=ActionType.CODE_EXECUTION,
                violations=violations,
                warnings=warnings,
                recommendations=recommendations,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"Task validation failed: {e}")
            return SecurityReport(
                is_safe=False,
                security_level=SecurityLevel.CRITICAL,
                action_type=ActionType.CODE_EXECUTION,
                violations=[f"Validation error: {e}"],
                warnings=[],
                recommendations=["Manual review required"],
                timestamp=datetime.now()
            )
    
    def _validate_file_path(self, file_path: str) -> Dict[str, List[str]]:
        """Validate file path for security"""
        violations = []
        warnings = []
        
        # Check for path traversal
        if '..' in file_path:
            violations.append("Path traversal attempt detected")
        
        # Check for absolute paths
        if file_path.startswith('/') or (len(file_path) > 1 and file_path[1] == ':'):
            violations.append("Absolute path not allowed")
        
        # Check for forbidden paths
        path_parts = Path(file_path).parts
        for part in path_parts:
            if part in self.forbidden_paths:
                violations.append(f"Access to forbidden path: {part}")
        
        # Check for hidden files/directories
        for part in path_parts:
            if part.startswith('.') and part not in ['.', '..']:
                warnings.append(f"Hidden file/directory: {part}")
        
        return {'violations': violations, 'warnings': warnings}
    
    async def _validate_code_content(self, content: str) -> Dict[str, List[str]]:
        """Validate code content for security"""
        violations = []
        warnings = []
        
        # Check for forbidden patterns
        for pattern in self.forbidden_code_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                violations.append(f"Forbidden code pattern: {pattern}")
        
        # Try to parse as Python code for additional validation
        try:
            tree = ast.parse(content)
            
            # Check for dangerous AST nodes
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        if alias.name in ['os', 'subprocess', 'sys']:
                            warnings.append(f"Potentially dangerous import: {alias.name}")
                
                elif isinstance(node, ast.Call):
                    if hasattr(node.func, 'id') and node.func.id in ['eval', 'exec']:
                        violations.append(f"Dangerous function call: {node.func.id}")
        
        except SyntaxError:
            # Not valid Python, skip AST validation
            pass
        except Exception as e:
            warnings.append(f"Code analysis warning: {e}")
        
        return {'violations': violations, 'warnings': warnings}
    
    def _initialize_security_rules(self) -> List[ValidationRule]:
        """Initialize security validation rules"""
        return [
            ValidationRule(
                name="no_eval_exec",
                description="Prevent eval() and exec() usage",
                forbidden_patterns=[r'eval\s*\(', r'exec\s*\('],
                action_types=[ActionType.FILE_CREATE, ActionType.FILE_EDIT]
            ),
            ValidationRule(
                name="no_system_commands",
                description="Prevent system command execution",
                forbidden_patterns=[r'os\.system', r'subprocess\.', r'shell=True'],
                action_types=[ActionType.FILE_CREATE, ActionType.FILE_EDIT]
            ),
            ValidationRule(
                name="no_secrets",
                description="Prevent hardcoded secrets",
                forbidden_patterns=[r'password\s*=', r'secret\s*=', r'api_key\s*='],
                action_types=[ActionType.FILE_CREATE, ActionType.FILE_EDIT]
            )
        ]
    
    def get_security_summary(self) -> Dict[str, Any]:
        """Get security configuration summary"""
        return {
            'validation_rules': len(self.validation_rules),
            'forbidden_patterns': len(self.forbidden_code_patterns),
            'safe_extensions': list(self.safe_extensions),
            'forbidden_paths': list(self.forbidden_paths),
            'allowed_domains': list(self.allowed_domains)
        }
