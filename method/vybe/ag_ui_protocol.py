"""
AG-UI (Agent-User Interface) Protocol Implementation for Vybe Method
Real-time agent-to-UI communication protocol for seamless user interaction
Based on 2025 AG-UI standards for agentic user interfaces
"""

import asyncio
import json
import logging
import uuid
from typing import Dict, List, Optional, Any, Callable, Union
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import websockets
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.responses import HTMLResponse
import uvicorn

# AG-UI Message Types
class AGUIMessageType(Enum):
    """AG-UI protocol message types"""
    AGENT_STATUS = "agent_status"
    AGENT_THINKING = "agent_thinking"
    AGENT_ACTION = "agent_action"
    AGENT_RESULT = "agent_result"
    AGENT_ERROR = "agent_error"
    USER_INPUT_REQUEST = "user_input_request"
    USER_CONFIRMATION = "user_confirmation"
    PROGRESS_UPDATE = "progress_update"
    TOOL_EXECUTION = "tool_execution"
    CONTEXT_UPDATE = "context_update"
    STREAMING_RESPONSE = "streaming_response"
    UI_COMPONENT_UPDATE = "ui_component_update"

class AGUIComponentType(Enum):
    """UI component types for dynamic updates"""
    TEXT = "text"
    CODE = "code"
    IMAGE = "image"
    CHART = "chart"
    TABLE = "table"
    FORM = "form"
    BUTTON = "button"
    PROGRESS_BAR = "progress_bar"
    NOTIFICATION = "notification"
    MODAL = "modal"

@dataclass
class AGUIMessage:
    """AG-UI protocol message"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    type: AGUIMessageType = AGUIMessageType.AGENT_STATUS
    agent_id: str = ""
    timestamp: datetime = field(default_factory=datetime.now)
    payload: Dict[str, Any] = field(default_factory=dict)
    component_id: Optional[str] = None
    component_type: Optional[AGUIComponentType] = None
    streaming: bool = False
    requires_user_action: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for transmission"""
        return {
            "id": self.id,
            "type": self.type.value,
            "agent_id": self.agent_id,
            "timestamp": self.timestamp.isoformat(),
            "payload": self.payload,
            "component_id": self.component_id,
            "component_type": self.component_type.value if self.component_type else None,
            "streaming": self.streaming,
            "requires_user_action": self.requires_user_action
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AGUIMessage':
        """Create from dictionary"""
        return cls(
            id=data.get("id", str(uuid.uuid4())),
            type=AGUIMessageType(data.get("type", "agent_status")),
            agent_id=data.get("agent_id", ""),
            timestamp=datetime.fromisoformat(data.get("timestamp", datetime.now().isoformat())),
            payload=data.get("payload", {}),
            component_id=data.get("component_id"),
            component_type=AGUIComponentType(data["component_type"]) if data.get("component_type") else None,
            streaming=data.get("streaming", False),
            requires_user_action=data.get("requires_user_action", False)
        )

class AGUIProtocolHandler:
    """
    AG-UI Protocol Handler for real-time agent-UI communication
    Enables seamless interaction between Vybe agents and user interface
    """
    
    def __init__(self, port: int = 8766):
        self.port = port
        self.logger = logging.getLogger("AGUI")
        
        # WebSocket connections
        self.connections: Dict[str, WebSocket] = {}
        self.agent_connections: Dict[str, WebSocket] = {}
        
        # Message routing
        self.message_handlers: Dict[AGUIMessageType, Callable] = {}
        self.component_handlers: Dict[str, Callable] = {}
        
        # State management
        self.agent_states: Dict[str, Dict[str, Any]] = {}
        self.ui_components: Dict[str, Dict[str, Any]] = {}
        self.pending_user_actions: Dict[str, asyncio.Future] = {}
        
        # Performance metrics
        self.metrics = {
            "messages_sent": 0,
            "messages_received": 0,
            "active_connections": 0,
            "agent_connections": 0,
            "ui_updates": 0,
            "user_interactions": 0,
            "streaming_sessions": 0
        }
        
        # FastAPI app for WebSocket server
        self.app = FastAPI(title="AG-UI Protocol Server")
        self._setup_routes()
    
    def _setup_routes(self):
        """Setup FastAPI routes for AG-UI protocol"""
        
        @self.app.websocket("/agui/user/{user_id}")
        async def user_websocket(websocket: WebSocket, user_id: str):
            """WebSocket endpoint for user connections"""
            await self._handle_user_connection(websocket, user_id)
        
        @self.app.websocket("/agui/agent/{agent_id}")
        async def agent_websocket(websocket: WebSocket, agent_id: str):
            """WebSocket endpoint for agent connections"""
            await self._handle_agent_connection(websocket, agent_id)
        
        @self.app.get("/agui/status")
        async def get_status():
            """Get AG-UI protocol status"""
            return {
                "status": "running",
                "metrics": self.metrics,
                "active_agents": list(self.agent_states.keys()),
                "active_connections": len(self.connections)
            }
        
        @self.app.get("/agui/demo")
        async def demo_page():
            """Demo page for AG-UI protocol"""
            return HTMLResponse(self._get_demo_html())
    
    async def _handle_user_connection(self, websocket: WebSocket, user_id: str):
        """Handle user WebSocket connection"""
        await websocket.accept()
        self.connections[user_id] = websocket
        self.metrics["active_connections"] += 1
        
        try:
            self.logger.info(f"User connected: {user_id}")
            
            # Send initial state
            await self._send_initial_state(websocket, user_id)
            
            async for message_data in websocket.iter_text():
                try:
                    data = json.loads(message_data)
                    await self._process_user_message(data, user_id)
                    
                except json.JSONDecodeError:
                    await self._send_error(websocket, "Invalid JSON format")
                except Exception as e:
                    await self._send_error(websocket, f"Message processing error: {e}")
                    
        except WebSocketDisconnect:
            self.logger.info(f"User disconnected: {user_id}")
        except Exception as e:
            self.logger.error(f"User connection error: {e}")
        finally:
            self.connections.pop(user_id, None)
            self.metrics["active_connections"] -= 1
    
    async def _handle_agent_connection(self, websocket: WebSocket, agent_id: str):
        """Handle agent WebSocket connection"""
        await websocket.accept()
        self.agent_connections[agent_id] = websocket
        self.metrics["agent_connections"] += 1
        
        # Initialize agent state
        self.agent_states[agent_id] = {
            "status": "connected",
            "last_activity": datetime.now(),
            "current_task": None,
            "progress": 0
        }
        
        try:
            self.logger.info(f"Agent connected: {agent_id}")
            
            # Notify all users of agent connection
            await self._broadcast_agent_status(agent_id, "connected")
            
            async for message_data in websocket.iter_text():
                try:
                    data = json.loads(message_data)
                    message = AGUIMessage.from_dict(data)
                    await self._process_agent_message(message)
                    
                except json.JSONDecodeError:
                    await self._send_error(websocket, "Invalid JSON format")
                except Exception as e:
                    await self._send_error(websocket, f"Message processing error: {e}")
                    
        except WebSocketDisconnect:
            self.logger.info(f"Agent disconnected: {agent_id}")
        except Exception as e:
            self.logger.error(f"Agent connection error: {e}")
        finally:
            self.agent_connections.pop(agent_id, None)
            self.agent_states.pop(agent_id, None)
            self.metrics["agent_connections"] -= 1
            
            # Notify users of agent disconnection
            await self._broadcast_agent_status(agent_id, "disconnected")
    
    async def _process_user_message(self, data: Dict[str, Any], user_id: str):
        """Process message from user"""
        self.metrics["user_interactions"] += 1
        
        message_type = data.get("type")
        
        if message_type == "user_input":
            # Handle user input for pending requests
            request_id = data.get("request_id")
            if request_id in self.pending_user_actions:
                future = self.pending_user_actions.pop(request_id)
                future.set_result(data.get("input"))
        
        elif message_type == "component_interaction":
            # Handle UI component interactions
            await self._handle_component_interaction(data, user_id)
        
        elif message_type == "agent_command":
            # Handle direct agent commands
            await self._handle_agent_command(data, user_id)
    
    async def _process_agent_message(self, message: AGUIMessage):
        """Process message from agent"""
        self.metrics["messages_received"] += 1
        
        # Update agent state
        if message.agent_id in self.agent_states:
            self.agent_states[message.agent_id]["last_activity"] = datetime.now()
        
        # Route message based on type
        if message.type == AGUIMessageType.AGENT_STATUS:
            await self._handle_agent_status(message)
        elif message.type == AGUIMessageType.AGENT_THINKING:
            await self._handle_agent_thinking(message)
        elif message.type == AGUIMessageType.AGENT_ACTION:
            await self._handle_agent_action(message)
        elif message.type == AGUIMessageType.AGENT_RESULT:
            await self._handle_agent_result(message)
        elif message.type == AGUIMessageType.PROGRESS_UPDATE:
            await self._handle_progress_update(message)
        elif message.type == AGUIMessageType.STREAMING_RESPONSE:
            await self._handle_streaming_response(message)
        elif message.type == AGUIMessageType.UI_COMPONENT_UPDATE:
            await self._handle_ui_component_update(message)
        elif message.type == AGUIMessageType.USER_INPUT_REQUEST:
            await self._handle_user_input_request(message)
    
    async def _handle_agent_status(self, message: AGUIMessage):
        """Handle agent status update"""
        status = message.payload.get("status", "unknown")
        
        if message.agent_id in self.agent_states:
            self.agent_states[message.agent_id]["status"] = status
        
        await self._broadcast_to_users(message)
    
    async def _handle_agent_thinking(self, message: AGUIMessage):
        """Handle agent thinking process"""
        thinking_text = message.payload.get("thinking", "")
        
        # Update UI with thinking indicator
        ui_message = {
            "type": "agent_thinking",
            "agent_id": message.agent_id,
            "thinking": thinking_text,
            "timestamp": message.timestamp.isoformat()
        }
        
        await self._broadcast_to_users(ui_message)
    
    async def _handle_agent_action(self, message: AGUIMessage):
        """Handle agent action notification"""
        action = message.payload.get("action", "")
        
        # Update UI with action indicator
        ui_message = {
            "type": "agent_action",
            "agent_id": message.agent_id,
            "action": action,
            "timestamp": message.timestamp.isoformat()
        }
        
        await self._broadcast_to_users(ui_message)
    
    async def _handle_agent_result(self, message: AGUIMessage):
        """Handle agent result"""
        result = message.payload.get("result", {})
        
        # Update UI with result
        ui_message = {
            "type": "agent_result",
            "agent_id": message.agent_id,
            "result": result,
            "timestamp": message.timestamp.isoformat()
        }
        
        await self._broadcast_to_users(ui_message)
    
    async def _handle_progress_update(self, message: AGUIMessage):
        """Handle progress update"""
        progress = message.payload.get("progress", 0)
        task = message.payload.get("task", "")
        
        # Update agent state
        if message.agent_id in self.agent_states:
            self.agent_states[message.agent_id]["progress"] = progress
            self.agent_states[message.agent_id]["current_task"] = task
        
        # Update UI progress
        ui_message = {
            "type": "progress_update",
            "agent_id": message.agent_id,
            "progress": progress,
            "task": task,
            "timestamp": message.timestamp.isoformat()
        }
        
        await self._broadcast_to_users(ui_message)
    
    async def _handle_streaming_response(self, message: AGUIMessage):
        """Handle streaming response"""
        self.metrics["streaming_sessions"] += 1
        
        chunk = message.payload.get("chunk", "")
        is_complete = message.payload.get("complete", False)
        
        # Stream to UI
        ui_message = {
            "type": "streaming_response",
            "agent_id": message.agent_id,
            "chunk": chunk,
            "complete": is_complete,
            "timestamp": message.timestamp.isoformat()
        }
        
        await self._broadcast_to_users(ui_message)
    
    async def _handle_ui_component_update(self, message: AGUIMessage):
        """Handle UI component update"""
        self.metrics["ui_updates"] += 1
        
        component_id = message.component_id
        component_data = message.payload.get("component_data", {})
        
        # Update component state
        if component_id:
            self.ui_components[component_id] = {
                "type": message.component_type.value if message.component_type else "text",
                "data": component_data,
                "agent_id": message.agent_id,
                "updated": datetime.now()
            }
        
        # Update UI
        ui_message = {
            "type": "ui_component_update",
            "component_id": component_id,
            "component_type": message.component_type.value if message.component_type else "text",
            "component_data": component_data,
            "agent_id": message.agent_id,
            "timestamp": message.timestamp.isoformat()
        }
        
        await self._broadcast_to_users(ui_message)
    
    async def _handle_user_input_request(self, message: AGUIMessage):
        """Handle user input request"""
        request_id = message.id
        prompt = message.payload.get("prompt", "Input required")
        input_type = message.payload.get("input_type", "text")
        
        # Create future for user response
        future = asyncio.Future()
        self.pending_user_actions[request_id] = future
        
        # Send input request to UI
        ui_message = {
            "type": "user_input_request",
            "request_id": request_id,
            "prompt": prompt,
            "input_type": input_type,
            "agent_id": message.agent_id,
            "timestamp": message.timestamp.isoformat()
        }
        
        await self._broadcast_to_users(ui_message)
        
        # Wait for user response (with timeout)
        try:
            user_input = await asyncio.wait_for(future, timeout=300)  # 5 minutes
            
            # Send response back to agent
            if message.agent_id in self.agent_connections:
                response = AGUIMessage(
                    type=AGUIMessageType.USER_CONFIRMATION,
                    agent_id=message.agent_id,
                    payload={"user_input": user_input, "request_id": request_id}
                )
                await self._send_to_agent(message.agent_id, response)
                
        except asyncio.TimeoutError:
            self.pending_user_actions.pop(request_id, None)
            self.logger.warning(f"User input request {request_id} timed out")
    
    async def _broadcast_to_users(self, message: Union[Dict[str, Any], AGUIMessage]):
        """Broadcast message to all connected users"""
        if isinstance(message, AGUIMessage):
            message_data = message.to_dict()
        else:
            message_data = message
        
        message_json = json.dumps(message_data)
        
        for websocket in self.connections.values():
            try:
                await websocket.send_text(message_json)
                self.metrics["messages_sent"] += 1
            except Exception as e:
                self.logger.error(f"Failed to send message to user: {e}")
    
    async def _send_to_agent(self, agent_id: str, message: AGUIMessage):
        """Send message to specific agent"""
        if agent_id in self.agent_connections:
            try:
                websocket = self.agent_connections[agent_id]
                await websocket.send_text(json.dumps(message.to_dict()))
                self.metrics["messages_sent"] += 1
            except Exception as e:
                self.logger.error(f"Failed to send message to agent {agent_id}: {e}")
    
    async def _broadcast_agent_status(self, agent_id: str, status: str):
        """Broadcast agent status to all users"""
        message = {
            "type": "agent_status",
            "agent_id": agent_id,
            "status": status,
            "timestamp": datetime.now().isoformat()
        }
        await self._broadcast_to_users(message)
    
    async def _send_initial_state(self, websocket: WebSocket, user_id: str):
        """Send initial state to newly connected user"""
        initial_state = {
            "type": "initial_state",
            "agents": self.agent_states,
            "components": self.ui_components,
            "timestamp": datetime.now().isoformat()
        }
        
        await websocket.send_text(json.dumps(initial_state))
    
    async def _send_error(self, websocket: WebSocket, error_message: str):
        """Send error message"""
        error_msg = {
            "type": "error",
            "error": error_message,
            "timestamp": datetime.now().isoformat()
        }
        await websocket.send_text(json.dumps(error_msg))
    
    def _get_demo_html(self) -> str:
        """Get demo HTML page for AG-UI protocol"""
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <title>AG-UI Protocol Demo</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .agent { border: 1px solid #ccc; margin: 10px; padding: 10px; }
                .status { font-weight: bold; }
                .thinking { font-style: italic; color: #666; }
                .progress { width: 100%; height: 20px; background: #f0f0f0; }
                .progress-bar { height: 100%; background: #4CAF50; transition: width 0.3s; }
            </style>
        </head>
        <body>
            <h1>AG-UI Protocol Demo</h1>
            <div id="agents"></div>
            <div id="messages"></div>
            
            <script>
                const ws = new WebSocket('ws://localhost:8766/agui/user/demo_user');
                
                ws.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    console.log('Received:', data);
                    
                    // Handle different message types
                    if (data.type === 'agent_status') {
                        updateAgentStatus(data);
                    } else if (data.type === 'progress_update') {
                        updateProgress(data);
                    }
                };
                
                function updateAgentStatus(data) {
                    // Update agent status in UI
                    console.log('Agent status:', data);
                }
                
                function updateProgress(data) {
                    // Update progress bar
                    console.log('Progress:', data);
                }
            </script>
        </body>
        </html>
        """
    
    async def start_server(self):
        """Start AG-UI protocol server"""
        config = uvicorn.Config(
            self.app,
            host="0.0.0.0",
            port=self.port,
            log_level="info"
        )
        server = uvicorn.Server(config)
        await server.serve()
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get AG-UI protocol metrics"""
        return {
            **self.metrics,
            "active_agents": len(self.agent_states),
            "ui_components": len(self.ui_components),
            "pending_actions": len(self.pending_user_actions)
        }
