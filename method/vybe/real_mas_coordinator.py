"""
Real Multi-Agent System Coordinator using CrewAI
Implements actual agent coordination with no placeholders
"""

import asyncio
import json
import logging
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable, Set
from dataclasses import dataclass, field

# Real MAS framework imports
try:
    from crewai import Agent, Task, Crew, Process
    from crewai.tools import BaseTool
    from langchain.llms import Ollama
    from langchain.tools import Tool

    CREWAI_AVAILABLE = True
except ImportError:
    CREWAI_AVAILABLE = False
    logging.error("CrewAI not available. Install with: pip install crewai langchain")

# WebSocket imports for real-time communication
try:
    import websockets
    from websockets.server import WebSocketServerProtocol

    WEBSOCKETS_AVAILABLE = True
except ImportError:
    WEBSOCKETS_AVAILABLE = False
    logging.warning("WebSockets not available - install with: pip install websockets")

# Vector context engine
try:
    from .vector_context_engine import VectorContextEngine, VectorContextItem
except ImportError:
    from vector_context_engine import VectorContextEngine, VectorContextItem

# Enhanced 2025 Protocol Imports
try:
    from .agentic_retrieval_engine import AgenticRetrievalEngine, VybeAgenticRetrieval
    from .a2a_protocol import A2AProtocolHandler, VybeA2AIntegration
    from .ag_ui_protocol import AGUIProtocolHandler, AGUIMessage, AGUIMessageType
    from .enhanced_safety_guardrails import EnhancedSafetyGuardrails, SafetyLevel

    ENHANCED_PROTOCOLS_AVAILABLE = True
except ImportError:
    ENHANCED_PROTOCOLS_AVAILABLE = False
    logging.warning("Enhanced 2025 protocols not available - using basic functionality")


@dataclass
class AgentResult:
    """Result from agent execution"""

    agent_id: str
    task_id: str
    result: Any
    execution_time: float
    success: bool
    error: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class MASTask:
    """Multi-agent system task definition"""

    task_id: str
    description: str
    agent_type: str
    priority: int = 1
    dependencies: List[str] = field(default_factory=list)
    context: Dict[str, Any] = field(default_factory=dict)
    deadline: Optional[datetime] = None


class RealMASCoordinator:
    """
    Real Multi-Agent System Coordinator using CrewAI
    Implements actual agent coordination with working agents
    """

    def __init__(
        self,
        config_path: Optional[Path] = None,
        project_root: str = None,
        enable_vector_context: bool = False,
    ):
        if not CREWAI_AVAILABLE:
            raise RuntimeError(
                "CrewAI not available. Cannot initialize real MAS coordinator."
            )

        self.logger = logging.getLogger(__name__)
        self.config_path = config_path
        self.project_root = project_root or str(Path.cwd())

        # Core components
        self.context_engine = None
        if enable_vector_context:
            try:
                self.context_engine = VectorContextEngine()
                self.logger.info("Vector context engine enabled")
            except Exception as e:
                self.logger.warning(f"Vector context engine disabled due to error: {e}")
                self.context_engine = None
        else:
            self.logger.info("Vector context engine disabled for faster initialization")

        self.agents: Dict[str, Agent] = {}
        self.crews: Dict[str, Crew] = {}
        self.active_tasks: Dict[str, MASTask] = {}
        self.completed_tasks: Dict[str, AgentResult] = {}

        # Enhanced 2025 Protocol Components
        self.agentic_retrieval = None
        self.a2a_integration = None
        self.agui_handler = None
        self.safety_guardrails = None

        # WebSocket server for real-time communication
        self.websocket_server = None
        self.websocket_clients: Set[WebSocketServerProtocol] = set()
        self.websocket_port = 8765
        self.is_running = False

        # 🔗 PHASE 0B: Agent-Codebase Integration
        self.codebase_interface = None
        self._initialize_codebase_integration()

        # Performance metrics
        self.metrics = {
            "total_tasks": 0,
            "successful_tasks": 0,
            "failed_tasks": 0,
            "avg_execution_time": 0.0,
            "active_agents": 0,
            "total_agents_created": 0,
            "safety_validations": 0,
            "retrieval_queries": 0,
            "a2a_messages": 0,
            "agui_updates": 0,
            "websocket_connections": 0,
        }

        self._initialize_agents()
        self._initialize_enhanced_protocols()
        self.logger.info(
            "RealMASCoordinator initialized with CrewAI and 2025 protocols"
        )

    def _initialize_agents(self):
        """Initialize real CrewAI agents with Vybe Method personalities"""

        # Initialize Ollama LLM for all agents (disabled for now due to connection issues)
        ollama_llm = None
        self.logger.info("🔄 Agents will be created without LLM (basic mode)")

        # VYBA - Vybe Business Analyst
        self.agents["vyba"] = Agent(
            role="Vybe Business Analyst",
            goal="Analyze market patterns and create AI-enhanced business insights",
            backstory="""You are VYBA, a visionary AI business analyst with crystal ball-like
            pattern recognition abilities. You see data patterns that humans miss and provide
            prophetic insights backed by analytics. You speak in patterns and probabilities,
            using crystal ball metaphors to reveal hidden market truths.""",
            verbose=True,
            allow_delegation=False,
            max_iter=3,
        )

        # QUBERT - Vybe Product Manager
        self.agents["qubert"] = Agent(
            role="Vybe Product Manager",
            goal="Create quantum-enhanced product requirements with multi-dimensional thinking",
            backstory="""You are QUBERT, a quantum-thinking product manager who operates
            in multiple dimensions simultaneously. You create comprehensive PRDs that
            consider all possible product states and user journeys. Your cube-like thinking
            allows you to see products from every angle.""",
            verbose=True,
            allow_delegation=False,
            max_iter=3,
        )

        # CODEX - Vybe Technical Architect
        self.agents["codex"] = Agent(
            role="Vybe Technical Architect",
            goal="Design ancient-wisdom-meets-modern-tech architectures",
            backstory="""You are CODEX, an ancient code sage who bridges timeless
            architectural wisdom with cutting-edge technology. You design systems that
            are both robust and elegant, drawing from the accumulated knowledge of
            software architecture across the ages.""",
            verbose=True,
            allow_delegation=False,
            max_iter=3,
        )

        # PIXY - Vybe UI/UX Designer
        self.agents["pixy"] = Agent(
            role="Vybe UI/UX Designer",
            goal="Create magical, intuitive user experiences with design enchantment",
            backstory="""You are PIXY, a magical design fairy who sprinkles user experience
            enchantment into every interface. You create designs that feel magical and
            intuitive, making complex functionality feel effortless and delightful for users.""",
            verbose=True,
            allow_delegation=False,
            max_iter=3,
        )

        # DUCKY - Vybe Quality Guardian
        self.agents["ducky"] = Agent(
            role="Vybe Quality Guardian",
            goal="Ensure quality excellence with vigilant testing and validation",
            backstory="""You are DUCKY, the vigilant quality guardian who ensures every
            piece of code meets the highest standards. You're like a protective duck
            watching over the codebase, catching bugs before they swim into production.
            Your keen eye for detail and systematic testing approach keeps quality high.""",
            verbose=True,
            allow_delegation=False,
            max_iter=3,
        )

        # HAPPY - Vybe Harmony Coordinator
        self.agents["happy"] = Agent(
            role="Vybe Harmony Coordinator",
            goal="Coordinate team harmony and ensure smooth deployment workflows",
            backstory="""You are HAPPY, the harmony coordinator who brings joy and
            coordination to the development process. You ensure all agents work together
            smoothly, manage deployments with care, and maintain team morale. Your
            positive energy keeps the entire MAS system running harmoniously.""",
            verbose=True,
            allow_delegation=False,
            max_iter=3,
        )

        # VYBRO - Vybe Developer
        self.agents["vybro"] = Agent(
            role="Vybe Developer",
            goal="Code with brotherhood spirit and collaborative excellence",
            backstory="""You are VYBRO, the ultimate coding brother who brings team spirit
            and collaborative excellence to every line of code. You write code that's not
            just functional but also brings the team together, with clean, tested, and
            maintainable implementations.""",
            verbose=True,
            allow_delegation=False,
            max_iter=5,
        )

        self.metrics["total_agents_created"] = len(self.agents)
        self.metrics["active_agents"] = len(self.agents)

        self.logger.info(f"Initialized {len(self.agents)} real CrewAI agents")

    def _initialize_enhanced_protocols(self):
        """Initialize enhanced 2025 protocols if available"""
        if not ENHANCED_PROTOCOLS_AVAILABLE:
            self.logger.warning(
                "Enhanced 2025 protocols not available - using basic functionality"
            )
            return

        try:
            # Initialize Agentic Retrieval Engine
            from .agentic_retrieval_engine import (
                AgenticRetrievalEngine,
                VybeAgenticRetrieval,
            )

            retrieval_engine = AgenticRetrievalEngine(
                project_root=self.project_root, project_name="VybeCoding"
            )
            self.agentic_retrieval = VybeAgenticRetrieval(retrieval_engine)
            self.logger.info("Agentic Retrieval Engine initialized")

            # Initialize A2A Protocol Integration
            from .a2a_protocol import VybeA2AIntegration

            self.a2a_integration = VybeA2AIntegration()

            # Register all Vybe agents for A2A communication
            for agent_id in self.agents.keys():
                port = 8765 + len(
                    self.a2a_integration.agents
                )  # Dynamic port assignment
                self.a2a_integration.register_vybe_agent(
                    agent_id=agent_id, agent_type=f"vybe_{agent_id}", port=port
                )
            self.logger.info("A2A Protocol Integration initialized")

            # Initialize AG-UI Protocol Handler
            from .ag_ui_protocol import AGUIProtocolHandler

            self.agui_handler = AGUIProtocolHandler(port=8766)
            self.logger.info("AG-UI Protocol Handler initialized")

            # Initialize Enhanced Safety Guardrails
            from .enhanced_safety_guardrails import (
                EnhancedSafetyGuardrails,
                SafetyLevel,
            )

            self.safety_guardrails = EnhancedSafetyGuardrails(
                safety_level=SafetyLevel.HIGH
            )
            self.logger.info("Enhanced Safety Guardrails initialized")

            self.logger.info("All enhanced 2025 protocols initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize enhanced protocols: {e}")
            # Continue with basic functionality

    def create_task(
        self,
        task_id: str,
        description: str,
        agent_type: str,
        priority: int = 1,
        context: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """Create a new MAS task"""
        try:
            task = MASTask(
                task_id=task_id,
                description=description,
                agent_type=agent_type,
                priority=priority,
                context=context or {},
            )

            self.active_tasks[task_id] = task
            self.metrics["total_tasks"] += 1

            # Add task context to vector database (if available)
            if self.context_engine:
                self.context_engine.add_context(
                    content=f"Task: {description}",
                    source=f"mas_task_{task_id}",
                    item_type="task",
                    priority=priority,
                    tags={agent_type, "task", "active"},
                )

            self.logger.info(f"Created task {task_id} for agent {agent_type}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to create task {task_id}: {e}")
            return False

    async def execute_task(self, task_id: str) -> Optional[AgentResult]:
        """Execute a task using the appropriate agent"""
        if task_id not in self.active_tasks:
            self.logger.error(f"Task {task_id} not found")
            return None

        task = self.active_tasks[task_id]
        agent_type = task.agent_type

        if agent_type not in self.agents:
            self.logger.error(f"Agent type {agent_type} not available")
            return None

        start_time = time.time()

        try:
            # Get relevant context for the task (if available)
            context_items = []
            if self.context_engine:
                context_items = self.context_engine.get_context(
                    query=task.description,
                    item_types=["task", "requirement", "specification"],
                    n_results=5,
                )

            # Prepare context for agent
            context_text = "\n".join([item.content for item in context_items])

            # Create CrewAI task
            crew_task = Task(
                description=f"""
                {task.description}
                
                Relevant Context:
                {context_text}
                
                Additional Context: {json.dumps(task.context, indent=2)}
                """,
                agent=self.agents[agent_type],
                expected_output="A detailed response addressing the task requirements",
            )

            # Create crew with single agent for this task
            crew = Crew(
                agents=[self.agents[agent_type]],
                tasks=[crew_task],
                process=Process.sequential,
                verbose=True,
            )

            # Execute the task
            self.logger.info(f"Executing task {task_id} with agent {agent_type}")
            result = crew.kickoff()

            execution_time = time.time() - start_time

            # Create result object
            agent_result = AgentResult(
                agent_id=agent_type,
                task_id=task_id,
                result=result,
                execution_time=execution_time,
                success=True,
                metadata={
                    "context_items_used": len(context_items),
                    "task_priority": task.priority,
                },
            )

            # Store result
            self.completed_tasks[task_id] = agent_result
            del self.active_tasks[task_id]

            # Update metrics
            self.metrics["successful_tasks"] += 1
            self._update_avg_execution_time(execution_time)

            # Add result to context (if available)
            if self.context_engine:
                self.context_engine.add_context(
                    content=f"Task Result: {str(result)}",
                    source=f"mas_result_{task_id}",
                    item_type="result",
                    priority=task.priority,
                    tags={agent_type, "result", "completed"},
                )

            self.logger.info(
                f"Task {task_id} completed successfully in {execution_time:.2f}s"
            )
            return agent_result

        except Exception as e:
            execution_time = time.time() - start_time

            # Create error result
            agent_result = AgentResult(
                agent_id=agent_type,
                task_id=task_id,
                result=None,
                execution_time=execution_time,
                success=False,
                error=str(e),
            )

            self.completed_tasks[task_id] = agent_result
            del self.active_tasks[task_id]

            # Update metrics
            self.metrics["failed_tasks"] += 1
            self._update_avg_execution_time(execution_time)

            self.logger.error(f"Task {task_id} failed: {e}")
            return agent_result

    def _update_avg_execution_time(self, execution_time: float):
        """Update average execution time metric"""
        total_completed = (
            self.metrics["successful_tasks"] + self.metrics["failed_tasks"]
        )
        if total_completed > 0:
            current_avg = self.metrics["avg_execution_time"]
            self.metrics["avg_execution_time"] = (
                current_avg * (total_completed - 1) + execution_time
            ) / total_completed

    async def execute_vybe_workflow(
        self, project_description: str
    ) -> Dict[str, Any]:
        """Execute the complete BMAD workflow for Vybe Qube generation with real CrewAI agents"""
        try:
            workflow_id = f"vybe_workflow_{int(time.time())}"
            self.logger.info(f"🚀 Starting Real MAS Vybe workflow: {workflow_id}")

            # Create workflow tracking with enhanced structure
            workflow_results = {
                "workflow_id": workflow_id,
                "status": "running",
                "phases": {},
                "agent_outputs": {},
                "collaboration_log": [],
                "start_time": time.time(),
                "project_description": project_description,
                "mas_type": "CrewAI Real Agents"
            }

            # Initialize real agent collaboration
            await self._initialize_workflow_collaboration(workflow_id, project_description)

            # Phase 1: Business Analysis (VYBA) - Enhanced with real agent execution
            self.logger.info("📊 Phase 1: Business Analysis (VYBA)")
            vyba_result = await self._execute_real_business_analysis(
                project_description, workflow_id
            )
            workflow_results["phases"]["business_analysis"] = vyba_result
            workflow_results["agent_outputs"]["vyba"] = vyba_result

            # Phase 2: Product Management (QUBERT) - Real agent with context
            self.logger.info("📋 Phase 2: Product Management (QUBERT)")
            qubert_result = await self._execute_real_product_management(
                project_description, vyba_result, workflow_id
            )
            workflow_results["phases"]["product_management"] = qubert_result
            workflow_results["agent_outputs"]["qubert"] = qubert_result

            # Phase 3: Technical Architecture (CODEX) - Real agent with full context
            self.logger.info("🏗️ Phase 3: Technical Architecture (CODEX)")
            codex_result = await self._execute_real_technical_architecture(
                project_description, vyba_result, qubert_result, workflow_id
            )
            workflow_results["phases"]["technical_architecture"] = codex_result
            workflow_results["agent_outputs"]["codex"] = codex_result

            # Phase 4: UI/UX Design (PIXY) - Real agent with complete context
            self.logger.info("🎨 Phase 4: UI/UX Design (PIXY)")
            pixy_result = await self._execute_real_ui_ux_design(
                project_description, vyba_result, qubert_result, codex_result, workflow_id
            )
            workflow_results["phases"]["ui_ux_design"] = pixy_result
            workflow_results["agent_outputs"]["pixy"] = pixy_result

            # Phase 5: Agent Collaboration & Consensus
            self.logger.info("🤝 Phase 5: Agent Collaboration & Consensus")
            consensus_result = await self._execute_agent_consensus(
                workflow_results["agent_outputs"], workflow_id
            )
            workflow_results["phases"]["consensus"] = consensus_result

            # Mark workflow as completed
            workflow_results["status"] = "completed"
            workflow_results["completion_time"] = time.time()
            workflow_results["total_duration"] = (
                workflow_results["completion_time"] - workflow_results["start_time"]
            )

            # Generate final specifications for code generation
            workflow_results["final_specifications"] = await self._generate_final_specifications(
                workflow_results["agent_outputs"], consensus_result
            )

            self.logger.info(
                f"✅ Real MAS Vybe workflow {workflow_id} completed in {workflow_results['total_duration']:.2f} seconds"
            )

            # Update metrics
            self.metrics["successful_tasks"] += 1
            self.metrics["total_agents_created"] += 4  # VYBA, QUBERT, CODEX, PIXY

            return workflow_results

        except Exception as e:
            self.logger.error(f"❌ Real MAS Vybe workflow failed: {e}")
            workflow_results["status"] = "failed"
            workflow_results["error"] = str(e)
            workflow_results["completion_time"] = time.time()
            self.metrics["failed_tasks"] += 1
            return workflow_results

    async def _initialize_workflow_collaboration(self, workflow_id: str, project_description: str):
        """Initialize collaboration context for the workflow"""
        try:
            # Add workflow context to vector database if available
            if self.context_engine:
                self.context_engine.add_context(
                    content=f"Workflow: {project_description}",
                    source=f"workflow_{workflow_id}",
                    item_type="workflow",
                    priority=5,
                    tags={"workflow", "vybe_qube", "active"}
                )

            # Log collaboration initialization
            self.logger.info(f"🤝 Initialized collaboration context for workflow {workflow_id}")

        except Exception as e:
            self.logger.warning(f"Failed to initialize workflow collaboration: {e}")

    async def _execute_real_business_analysis(self, project_description: str, workflow_id: str) -> Dict[str, Any]:
        """Execute real business analysis using VYBA agent"""
        try:
            vyba_task_id = f"{workflow_id}_vyba"

            # Enhanced business analysis prompt for real agent
            analysis_prompt = f"""
            🔮 VYBA Business Analysis Mission:

            Project: {project_description}

            As VYBA, the mystical business analyst with crystal ball pattern recognition, perform a comprehensive business analysis:

            1. MARKET OPPORTUNITY ANALYSIS:
               - Market size and growth potential
               - Target audience demographics and psychographics
               - Competitive landscape and positioning
               - Revenue potential and monetization strategies

            2. BUSINESS MODEL VALIDATION:
               - Value proposition clarity
               - Revenue streams identification
               - Cost structure analysis
               - Scalability assessment

            3. RISK ASSESSMENT:
               - Market risks and mitigation strategies
               - Technical feasibility concerns
               - Financial viability analysis
               - Regulatory and compliance considerations

            4. SUCCESS METRICS:
               - Key performance indicators (KPIs)
               - Success milestones and timelines
               - Growth trajectory projections
               - Exit strategy considerations

            Provide detailed, actionable insights that will guide the entire project development.
            """

            # Create and execute VYBA task
            self.create_task(
                task_id=vyba_task_id,
                description=analysis_prompt,
                agent_type="vyba",
                priority=5,
                context={
                    "workflow_id": workflow_id,
                    "phase": "business_analysis",
                    "project_description": project_description
                }
            )

            vyba_result = await self.execute_task(vyba_task_id)

            if vyba_result and vyba_result.success:
                # Structure the business analysis result
                structured_result = {
                    "agent": "VYBA",
                    "phase": "business_analysis",
                    "success": True,
                    "analysis": vyba_result.result,
                    "execution_time": vyba_result.execution_time,
                    "market_opportunity": self._extract_market_opportunity(vyba_result.result),
                    "business_model": self._extract_business_model(vyba_result.result),
                    "risk_assessment": self._extract_risk_assessment(vyba_result.result),
                    "success_metrics": self._extract_success_metrics(vyba_result.result),
                    "timestamp": time.time()
                }

                self.logger.info(f"✅ VYBA business analysis completed for {workflow_id}")
                return structured_result
            else:
                raise Exception("VYBA business analysis failed")

        except Exception as e:
            self.logger.error(f"❌ VYBA business analysis failed: {e}")
            return {
                "agent": "VYBA",
                "phase": "business_analysis",
                "success": False,
                "error": str(e),
                "timestamp": time.time()
            }

    async def _execute_real_product_management(self, project_description: str, vyba_result: Dict[str, Any], workflow_id: str) -> Dict[str, Any]:
        """Execute real product management using QUBERT agent"""
        try:
            qubert_task_id = f"{workflow_id}_qubert"

            # Enhanced product management prompt with VYBA context
            pm_prompt = f"""
            🎲 QUBERT Product Management Mission:

            Project: {project_description}

            Business Analysis Context: {vyba_result.get('analysis', 'No previous analysis available')}

            As QUBERT, the quantum multi-dimensional product manager, create comprehensive product requirements:

            1. PRODUCT VISION & STRATEGY:
               - Clear product vision statement
               - Strategic objectives and goals
               - Product positioning and differentiation
               - Success criteria and metrics

            2. USER EXPERIENCE REQUIREMENTS:
               - User personas and journey mapping
               - Core user stories and use cases
               - Feature prioritization matrix
               - Accessibility and usability requirements

            3. FUNCTIONAL REQUIREMENTS:
               - Core feature specifications
               - API and integration requirements
               - Performance and scalability needs
               - Security and compliance requirements

            4. TECHNICAL CONSTRAINTS:
               - Platform and technology preferences
               - Third-party service integrations
               - Data management requirements
               - Deployment and hosting considerations

            Consider all quantum states of user interaction and product evolution.
            """

            # Create and execute QUBERT task
            self.create_task(
                task_id=qubert_task_id,
                description=pm_prompt,
                agent_type="qubert",
                priority=5,
                context={
                    "workflow_id": workflow_id,
                    "phase": "product_management",
                    "business_analysis": vyba_result,
                    "project_description": project_description
                }
            )

            qubert_result = await self.execute_task(qubert_task_id)

            if qubert_result and qubert_result.success:
                # Structure the product management result
                structured_result = {
                    "agent": "QUBERT",
                    "phase": "product_management",
                    "success": True,
                    "requirements": qubert_result.result,
                    "execution_time": qubert_result.execution_time,
                    "product_vision": self._extract_product_vision(qubert_result.result),
                    "user_requirements": self._extract_user_requirements(qubert_result.result),
                    "functional_requirements": self._extract_functional_requirements(qubert_result.result),
                    "technical_constraints": self._extract_technical_constraints(qubert_result.result),
                    "timestamp": time.time()
                }

                self.logger.info(f"✅ QUBERT product management completed for {workflow_id}")
                return structured_result
            else:
                raise Exception("QUBERT product management failed")

        except Exception as e:
            self.logger.error(f"❌ QUBERT product management failed: {e}")
            return {
                "agent": "QUBERT",
                "phase": "product_management",
                "success": False,
                "error": str(e),
                "timestamp": time.time()
            }

    def get_metrics(self) -> Dict[str, Any]:
        """Get MAS performance metrics"""
        metrics = {
            **self.metrics,
            "active_tasks": len(self.active_tasks),
            "completed_tasks": len(self.completed_tasks),
        }

        # Add context engine metrics if available
        if self.context_engine:
            metrics["context_engine_metrics"] = self.context_engine.get_metrics()
        else:
            metrics["context_engine_metrics"] = {"status": "disabled"}

        return metrics

    def get_agent_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all agents"""
        status = {}
        for agent_id, agent in self.agents.items():
            status[agent_id] = {
                "role": agent.role,
                "goal": agent.goal,
                "available": True,  # CrewAI agents are always available
                "tasks_completed": len(
                    [r for r in self.completed_tasks.values() if r.agent_id == agent_id]
                ),
            }
        return status

    async def start_websocket_server(self):
        """Start WebSocket server for real-time communication"""
        if not WEBSOCKETS_AVAILABLE:
            self.logger.warning("WebSockets not available - cannot start server")
            return False

        try:
            self.websocket_server = await websockets.serve(
                self._handle_websocket_connection, "0.0.0.0", self.websocket_port
            )
            self.is_running = True
            self.logger.info(f"WebSocket server started on port {self.websocket_port}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to start WebSocket server: {e}")
            return False

    async def _handle_websocket_connection(self, websocket, path):
        """Handle new WebSocket connection"""
        self.websocket_clients.add(websocket)
        self.metrics["websocket_connections"] += 1
        client_id = f"client_{len(self.websocket_clients)}"

        try:
            self.logger.info(f"New WebSocket connection: {client_id}")

            # Send initial agent status
            await self._send_agent_status_update(websocket)

            async for message in websocket:
                try:
                    data = json.loads(message)
                    await self._process_websocket_message(data, websocket)

                except json.JSONDecodeError:
                    await self._send_websocket_error(websocket, "Invalid JSON format")
                except Exception as e:
                    await self._send_websocket_error(
                        websocket, f"Message processing error: {e}"
                    )

        except websockets.exceptions.ConnectionClosed:
            self.logger.info(f"WebSocket connection closed: {client_id}")
        except Exception as e:
            self.logger.error(f"WebSocket connection error: {e}")
        finally:
            self.websocket_clients.discard(websocket)

    async def _process_websocket_message(self, data: Dict[str, Any], websocket):
        """Process incoming WebSocket message"""
        message_type = data.get("type")

        if message_type == "get_agent_status":
            await self._send_agent_status_update(websocket)
        elif message_type == "execute_task":
            await self._handle_websocket_task_execution(data, websocket)
        elif message_type == "get_metrics":
            await self._send_metrics_update(websocket)
        elif message_type == "execute_vybe_workflow":
            await self._handle_websocket_vybe_workflow(data, websocket)
        else:
            await self._send_websocket_error(
                websocket, f"Unknown message type: {message_type}"
            )

    async def _send_agent_status_update(self, websocket=None):
        """Send agent status update to WebSocket clients"""
        agent_status = {}
        for agent_id, agent in self.agents.items():
            agent_status[agent_id] = {
                "id": agent_id,
                "name": agent.role,
                "status": "idle",  # This would be dynamic based on actual agent state
                "current_task": None,
                "progress": 0,
                "last_activity": datetime.now().isoformat(),
            }

        message = {
            "type": "agent_status_update",
            "data": agent_status,
            "timestamp": datetime.now().isoformat(),
        }

        if websocket:
            await websocket.send(json.dumps(message))
        else:
            await self._broadcast_to_all_clients(message)

    async def _send_websocket_error(self, websocket, error_message: str):
        """Send error message to WebSocket client"""
        message = {
            "type": "error",
            "data": {"error": error_message, "timestamp": datetime.now().isoformat()},
        }
        await websocket.send(json.dumps(message))

    async def _broadcast_to_all_clients(self, message: Dict[str, Any]):
        """Broadcast message to all connected WebSocket clients"""
        if self.websocket_clients:
            message_json = json.dumps(message)
            disconnected_clients = set()

            for client in self.websocket_clients:
                try:
                    await client.send(message_json)
                except websockets.exceptions.ConnectionClosed:
                    disconnected_clients.add(client)
                except Exception as e:
                    self.logger.error(f"Error broadcasting to client: {e}")
                    disconnected_clients.add(client)

            # Remove disconnected clients
            self.websocket_clients -= disconnected_clients

    # 🔗 PHASE 0B: Agent-Codebase Integration Methods
    def _initialize_codebase_integration(self):
        """Initialize the codebase interface for agent file operations"""
        try:
            from .vybe_codebase_interface import VybeMASCodebaseIntegration

            self.codebase_interface = VybeMASCodebaseIntegration(self.project_root)
            self.logger.info(
                "✅ Codebase interface initialized for agent file operations"
            )
        except ImportError as e:
            self.logger.warning(f"⚠️ Codebase interface not available: {e}")
            self.codebase_interface = None

    async def _handle_websocket_task_execution(self, data: Dict[str, Any], websocket):
        """Handle task execution request from WebSocket"""
        try:
            task_data = data.get("task", {})
            agent_id = task_data.get("agent_id", "vybro")
            description = task_data.get("description", "Execute development task")

            # Create and execute task
            task_id = f"ws_task_{int(time.time())}"

            # First create the task, then execute it
            task_created = self.create_task(
                task_id=task_id,
                description=description,
                agent_type=agent_id,
                priority=task_data.get("priority", 1),
                context=task_data.get("context", {}),
            )
            if not task_created:
                await self._send_websocket_error(
                    websocket, f"Failed to create task {task_id}"
                )
                return

            # Execute task using task_id
            result = await self.execute_task(task_id)

            # If this is a development task and we have codebase interface, execute file operations
            if (
                agent_id == "vybro"
                and self.codebase_interface
                and result
                and result.success
            ):
                codebase_result = (
                    self.codebase_interface.execute_vybro_development_task(
                        result, agent_id
                    )
                )

                # Send codebase operation results
                await self._send_codebase_operation_result(
                    websocket, task_id, codebase_result
                )

            # Send task completion result
            if result:
                await self._send_task_execution_result(websocket, task_id, result)
            else:
                await self._send_websocket_error(
                    websocket, f"Task execution failed for {task_id}"
                )

        except Exception as e:
            self.logger.error(f"WebSocket task execution failed: {e}")
            await self._send_websocket_error(websocket, f"Task execution failed: {e}")

    async def _handle_websocket_vybe_workflow(self, data: Dict[str, Any], websocket):
        """Handle Vybe workflow execution request from WebSocket"""
        try:
            workflow_data = data.get("workflow", {})
            content_input = workflow_data.get("content", "")

            # Execute Vybe workflow
            workflow_id = f"ws_workflow_{int(time.time())}"
            results = await self.execute_vybe_workflow(content_input)

            # Send workflow results
            await self._send_vybe_workflow_result(websocket, workflow_id, results)

        except Exception as e:
            self.logger.error(f"WebSocket Vybe workflow failed: {e}")
            await self._send_websocket_error(websocket, f"Vybe workflow failed: {e}")

    async def _send_metrics_update(self, websocket):
        """Send metrics update to WebSocket client"""
        metrics = self.get_metrics()
        message = {
            "type": "metrics_update",
            "data": metrics,
            "timestamp": datetime.now().isoformat(),
        }
        await websocket.send(json.dumps(message))

    async def _send_task_execution_result(
        self, websocket, task_id: str, result: AgentResult
    ):
        """Send task execution result to WebSocket client"""
        message = {
            "type": "task_execution_result",
            "data": {
                "task_id": task_id,
                "agent_id": result.agent_id,
                "success": result.success,
                "result": result.result,
                "execution_time": result.execution_time,
                "error": result.error,
                "timestamp": datetime.now().isoformat(),
            },
        }
        await websocket.send(json.dumps(message))

    async def _send_codebase_operation_result(
        self, websocket, task_id: str, codebase_result: Dict[str, Any]
    ):
        """Send codebase operation result to WebSocket client"""
        message = {
            "type": "codebase_operation_result",
            "data": {
                "task_id": task_id,
                "codebase_result": codebase_result,
                "timestamp": datetime.now().isoformat(),
            },
        }
        await websocket.send(json.dumps(message))

    async def _send_vybe_workflow_result(
        self, websocket, workflow_id: str, results: Dict[str, Any]
    ):
        """Send Vybe workflow result to WebSocket client"""
        message = {
            "type": "vybe_workflow_result",
            "data": {
                "workflow_id": workflow_id,
                "results": results,
                "timestamp": datetime.now().isoformat(),
            },
        }
        await websocket.send(json.dumps(message))

    def shutdown(self):
        """Gracefully shutdown the MAS coordinator"""
        try:
            # Clear active tasks
            self.active_tasks.clear()

            # Shutdown context engine (if available)
            if self.context_engine:
                self.context_engine.shutdown()

            # Shutdown codebase interface
            if self.codebase_interface:
                # VybeCodebaseInterface doesn't have a shutdown method, just clear reference
                self.codebase_interface = None

            self.logger.info("RealMASCoordinator shutdown complete")

        except Exception as e:
            self.logger.error(f"Error during MAS shutdown: {e}")


# Factory function for creating real MAS coordinator
def create_real_mas_coordinator(
    config_path: Optional[Path] = None,
) -> RealMASCoordinator:
    """Create a real MAS coordinator with CrewAI"""
    if not CREWAI_AVAILABLE:
        raise RuntimeError("CrewAI not available. Install with: pip install crewai")

    return RealMASCoordinator(config_path)


async def main():
    """Main function to run MAS coordinator as standalone service"""
    import logging
    import signal
    import sys
    from fastapi import FastAPI
    import uvicorn

    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)

    # Create MAS coordinator
    coordinator = create_real_mas_coordinator()

    # Create FastAPI app for REST endpoints
    app = FastAPI(title="MAS Coordinator API", version="1.0.0")

    @app.get("/health")
    async def health_check():
        return {"status": "healthy", "timestamp": datetime.now().isoformat()}

    @app.get("/agents")
    async def get_agents():
        """Get all registered agents"""
        return {
            "agents": [
                {
                    "id": agent_id,
                    "name": agent.role,
                    "status": "idle",
                    "capabilities": list(agent.tools) if hasattr(agent, 'tools') else []
                }
                for agent_id, agent in coordinator.agents.items()
            ]
        }

    @app.post("/tasks")
    async def create_task(task_data: dict):
        """Create a new task"""
        task_id = f"api_task_{int(time.time())}"
        success = coordinator.create_task(
            task_id=task_id,
            description=task_data.get("description", ""),
            agent_type=task_data.get("agent_type", "vybro"),
            priority=task_data.get("priority", 1),
            context=task_data.get("context", {})
        )

        if success:
            return {"task_id": task_id, "status": "created"}
        else:
            return {"error": "Failed to create task"}, 400

    @app.post("/tasks/{task_id}/execute")
    async def execute_task(task_id: str):
        """Execute a task"""
        result = await coordinator.execute_task(task_id)
        if result:
            return {
                "task_id": task_id,
                "success": result.success,
                "result": result.result,
                "execution_time": result.execution_time,
                "error": result.error
            }
        else:
            return {"error": "Task execution failed"}, 400

    @app.get("/metrics")
    async def get_metrics():
        """Get coordinator metrics"""
        return coordinator.get_metrics()

    # Graceful shutdown handler
    def signal_handler(signum, frame):
        logger.info("Received shutdown signal, shutting down gracefully...")
        coordinator.shutdown()
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        logger.info("🚀 MAS Coordinator starting...")

        # Start WebSocket server
        await coordinator.start_websocket_server()
        logger.info("🌐 WebSocket server started on port 8765")

        # Start FastAPI server
        logger.info("🚀 Starting MAS Coordinator API server on port 8766...")
        config = uvicorn.Config(app, host="0.0.0.0", port=8766, log_level="info")
        server = uvicorn.Server(config)
        await server.serve()

    except Exception as e:
        logger.error(f"Failed to start MAS coordinator: {e}")
        coordinator.shutdown()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
