"""
Vybe Method Workflow Engine
Core orchestration system that implements Vybe Method rules for autonomous MAS
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional, Set
from datetime import datetime
from dataclasses import dataclass, asdict
from enum import Enum

from .autonomous_vybe_qube_generator import AutonomousVybeQubeGenerator
from .real_agent_communication import (
    RealAgentCommunication,
    MessageType,
    MessagePriority,
)
from .safety_guardrails import SafetyGuardrails

# Import consensus framework if available, otherwise create minimal implementation
try:
    from .consensus_framework import ConsensusFramework, ConsensusProposal
except ImportError:
    # Minimal consensus implementation for now
    class ConsensusProposal:
        def __init__(
            self, proposal_id, title, description, proposal_type, proposed_by, content
        ):
            self.proposal_id = proposal_id
            self.title = title
            self.description = description
            self.proposal_type = proposal_type
            self.proposed_by = proposed_by
            self.content = content

    class ConsensusFramework:
        async def register_agent(self, agent_id, role, capabilities):
            pass

        async def submit_proposal(self, proposal):
            return proposal.proposal_id


class VybePhase(Enum):
    """Vybe Method phases"""

    ANALYSIS = "analysis"
    REQUIREMENTS = "requirements"
    ARCHITECTURE = "architecture"
    DESIGN = "design"
    IMPLEMENTATION = "implementation"
    QUALITY = "quality"
    DEPLOYMENT = "deployment"
    COMPLETE = "complete"


class ExecutionPattern(Enum):
    """Execution patterns for phases"""

    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"
    CONSENSUS = "consensus"


@dataclass
class VybeAgent:
    """Vybe Method agent definition"""

    id: str
    name: str
    role: str
    capabilities: List[str]
    primary_phases: List[VybePhase]
    collaboration_phases: List[VybePhase]
    catchphrase: str
    educational_focus: str


@dataclass
class PhaseDefinition:
    """Definition of a Vybe Method phase"""

    phase: VybePhase
    name: str
    description: str
    primary_agent: str
    supporting_agents: List[str]
    execution_pattern: ExecutionPattern
    requires_consensus: bool
    quality_gates: List[str]
    educational_objectives: List[str]
    input_artifacts: List[str]
    output_artifacts: List[str]


@dataclass
class PhaseResult:
    """Result of executing a Vybe Method phase"""

    phase: VybePhase
    status: str
    primary_agent: str
    supporting_agents: List[str]
    artifacts: Dict[str, Any]
    quality_validation: Dict[str, Any]
    consensus_result: Optional[Dict[str, Any]]
    educational_content: Dict[str, Any]
    execution_time: float
    timestamp: datetime


class VybeMethodWorkflowEngine:
    """
    Core Vybe Method Workflow Engine
    Orchestrates autonomous agents following BMAD + MAS methodology
    """

    def __init__(self, workspace_root: str):
        self.logger = logging.getLogger(__name__)
        self.workspace_root = workspace_root

        # Initialize core components
        self.generator = AutonomousVybeQubeGenerator(workspace_root)
        self.agent_comm = RealAgentCommunication()
        self.consensus_framework = ConsensusFramework()
        self.safety_guardrails = SafetyGuardrails()

        # Workflow state
        self.current_workflow: Optional[str] = None
        self.current_phase: Optional[VybePhase] = None
        self.workflow_history: List[Dict] = []
        self.educational_feed: List[Dict] = []

        # Initialize Vybe agents and phases
        self.vybe_agents = self._initialize_vybe_agents()
        self.phase_definitions = self._initialize_phase_definitions()

        self.logger.info("VybeMethodWorkflowEngine initialized")

    async def initialize(self) -> bool:
        """Initialize the workflow engine"""
        try:
            # Initialize generator
            await self.generator.initialize()

            # Register Vybe agents
            for agent in self.vybe_agents.values():
                await self.agent_comm.register_agent(agent.id, agent.capabilities)
                await self.consensus_framework.register_agent(
                    agent.id, agent.role, agent.capabilities
                )

            self.logger.info("Vybe Method Workflow Engine initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize workflow engine: {e}")
            return False

    async def execute_vybe_workflow(
        self, project_brief: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute complete Vybe Method workflow"""
        try:
            workflow_id = f"vybe_{int(datetime.now().timestamp())}"
            self.current_workflow = workflow_id

            self.logger.info(f"Starting Vybe Method workflow: {workflow_id}")

            # Initialize workflow tracking
            workflow_context = {
                "workflow_id": workflow_id,
                "project_brief": project_brief,
                "start_time": datetime.now(),
                "phases_completed": [],
                "current_phase": None,
                "status": "running",
            }

            # Execute phases according to Vybe Method
            results = {}

            # Phase 1: Analysis (VYBA) - Sequential
            results["analysis"] = await self._execute_phase(
                VybePhase.ANALYSIS, project_brief, workflow_context
            )

            # Phase 2: Requirements (QUBERT) - Sequential with handoff
            results["requirements"] = await self._execute_phase(
                VybePhase.REQUIREMENTS, results["analysis"], workflow_context
            )

            # Phase 3: Architecture + Design (CODEX + PIXY) - Parallel
            arch_design_results = await self._execute_parallel_phases(
                [
                    (VybePhase.ARCHITECTURE, results["requirements"]),
                    (VybePhase.DESIGN, results["requirements"]),
                ],
                workflow_context,
            )

            results["architecture"] = arch_design_results["architecture"]
            results["design"] = arch_design_results["design"]

            # Phase 4: Implementation (VYBRO) - Sequential
            implementation_input = {
                "requirements": results["requirements"],
                "architecture": results["architecture"],
                "design": results["design"],
            }
            results["implementation"] = await self._execute_phase(
                VybePhase.IMPLEMENTATION, implementation_input, workflow_context
            )

            # Phase 5: Quality + Deployment (DUCKY + HAPPY) - Parallel
            final_results = await self._execute_parallel_phases(
                [
                    (VybePhase.QUALITY, results["implementation"]),
                    (VybePhase.DEPLOYMENT, results["implementation"]),
                ],
                workflow_context,
            )

            results["quality"] = final_results["quality"]
            results["deployment"] = final_results["deployment"]

            # Complete workflow
            workflow_context["status"] = "completed"
            workflow_context["end_time"] = datetime.now()
            workflow_context["results"] = results

            self.workflow_history.append(workflow_context)

            self.logger.info(f"Vybe Method workflow completed: {workflow_id}")
            return workflow_context

        except Exception as e:
            self.logger.error(f"Vybe Method workflow failed: {e}")
            if self.current_workflow:
                workflow_context["status"] = "failed"
                workflow_context["error"] = str(e)
                self.workflow_history.append(workflow_context)
            raise

    async def _execute_phase(
        self, phase: VybePhase, input_data: Any, workflow_context: Dict
    ) -> PhaseResult:
        """Execute a single Vybe Method phase"""
        try:
            phase_def = self.phase_definitions[phase]
            self.current_phase = phase

            self.logger.info(f"Executing phase: {phase.value}")

            # Update workflow context
            workflow_context["current_phase"] = phase.value

            # Phase initialization
            await self._initialize_phase(phase, phase_def, workflow_context)

            # Execute primary agent work
            primary_result = await self._execute_agent_work(
                phase_def.primary_agent, phase, input_data, workflow_context
            )

            # Execute supporting agents if needed
            supporting_results = {}
            if phase_def.supporting_agents:
                for agent_id in phase_def.supporting_agents:
                    supporting_results[agent_id] = await self._execute_agent_work(
                        agent_id, phase, input_data, workflow_context
                    )

            # Quality gate validation
            quality_validation = await self._validate_phase_output(
                phase, primary_result, supporting_results
            )

            # Consensus decision making (if required)
            consensus_result = None
            if phase_def.requires_consensus:
                consensus_result = await self._evaluate_consensus(
                    phase, primary_result, supporting_results
                )

                if not consensus_result["approved"]:
                    return await self._handle_consensus_failure(
                        phase, consensus_result, workflow_context
                    )

            # Educational documentation
            educational_content = await self._document_educational_outcomes(
                phase, primary_result, supporting_results, workflow_context
            )

            # Create phase result
            phase_result = PhaseResult(
                phase=phase,
                status="completed",
                primary_agent=phase_def.primary_agent,
                supporting_agents=phase_def.supporting_agents,
                artifacts={"primary": primary_result, "supporting": supporting_results},
                quality_validation=quality_validation,
                consensus_result=consensus_result,
                educational_content=educational_content,
                execution_time=(
                    datetime.now() - workflow_context["start_time"]
                ).total_seconds(),
                timestamp=datetime.now(),
            )

            # Update workflow tracking
            workflow_context["phases_completed"].append(phase.value)

            self.logger.info(f"Phase completed: {phase.value}")
            return phase_result

        except Exception as e:
            self.logger.error(f"Phase execution failed: {phase.value} - {e}")
            raise

    async def _execute_parallel_phases(
        self, phases: List[tuple], workflow_context: Dict
    ) -> Dict[str, PhaseResult]:
        """Execute multiple phases in parallel"""
        try:
            self.logger.info(
                f"Executing parallel phases: {[p[0].value for p in phases]}"
            )

            # Create tasks for parallel execution
            tasks = []
            for phase, input_data in phases:
                task = asyncio.create_task(
                    self._execute_phase(phase, input_data, workflow_context)
                )
                tasks.append((phase, task))

            # Wait for all phases to complete
            results = {}
            for phase, task in tasks:
                result = await task
                results[phase.value] = result

            self.logger.info(f"Parallel phases completed: {list(results.keys())}")
            return results

        except Exception as e:
            self.logger.error(f"Parallel phase execution failed: {e}")
            raise

    async def _execute_agent_work(
        self, agent_id: str, phase: VybePhase, input_data: Any, workflow_context: Dict
    ) -> Dict[str, Any]:
        """Execute work for a specific agent"""
        try:
            agent = self.vybe_agents[agent_id]

            # Create agent task based on phase and agent role
            task_description = self._generate_agent_task(agent, phase, input_data)

            # Execute task using the autonomous generator
            if phase == VybePhase.ANALYSIS:
                result = await self.generator._execute_market_research(
                    input_data.get("business_idea", "AI-powered application")
                )
            elif phase == VybePhase.REQUIREMENTS:
                result = await self.generator._execute_product_requirements(
                    input_data.get("business_idea", ""), input_data
                )
            elif phase == VybePhase.ARCHITECTURE:
                result = await self.generator._execute_technical_architecture(
                    input_data
                )
            elif phase == VybePhase.DESIGN:
                result = await self.generator._execute_ui_ux_design(input_data, {})
            elif phase == VybePhase.IMPLEMENTATION:
                result = await self.generator._execute_code_generation(
                    input_data.get("architecture", {}), input_data.get("design", {})
                )
            elif phase == VybePhase.QUALITY:
                result = await self.generator._execute_quality_validation(input_data)
            elif phase == VybePhase.DEPLOYMENT:
                result = await self.generator._execute_deployment(
                    workflow_context["workflow_id"], input_data
                )
            else:
                result = {
                    "status": "completed",
                    "agent": agent_id,
                    "phase": phase.value,
                }

            # Document agent reasoning for educational purposes
            await self._document_agent_reasoning(
                agent_id, phase, task_description, result, workflow_context
            )

            return result

        except Exception as e:
            self.logger.error(f"Agent work failed: {agent_id} - {phase.value} - {e}")
            raise

    def _initialize_vybe_agents(self) -> Dict[str, VybeAgent]:
        """Initialize Vybe Method agents"""
        return {
            "vyba": VybeAgent(
                id="vyba",
                name="VYBA",
                role="Business Analyst",
                capabilities=[
                    "market_research",
                    "business_analysis",
                    "competitive_analysis",
                ],
                primary_phases=[VybePhase.ANALYSIS],
                collaboration_phases=[VybePhase.REQUIREMENTS, VybePhase.ARCHITECTURE],
                catchphrase="Data drives decisions, insights inspire innovation!",
                educational_focus="Market research and business opportunity analysis",
            ),
            "qubert": VybeAgent(
                id="qubert",
                name="QUBERT",
                role="Product Manager",
                capabilities=[
                    "requirements_gathering",
                    "product_management",
                    "user_stories",
                ],
                primary_phases=[VybePhase.REQUIREMENTS],
                collaboration_phases=[
                    VybePhase.ANALYSIS,
                    VybePhase.ARCHITECTURE,
                    VybePhase.DESIGN,
                ],
                catchphrase="Requirements are the foundation of great products!",
                educational_focus="Product requirements and feature specification",
            ),
            "codex": VybeAgent(
                id="codex",
                name="CODEX",
                role="Technical Architect",
                capabilities=[
                    "system_design",
                    "technical_architecture",
                    "technology_selection",
                ],
                primary_phases=[VybePhase.ARCHITECTURE],
                collaboration_phases=[
                    VybePhase.REQUIREMENTS,
                    VybePhase.DESIGN,
                    VybePhase.IMPLEMENTATION,
                ],
                catchphrase="Architecture is the art of building digital foundations!",
                educational_focus="System design and technical architecture",
            ),
            "pixy": VybeAgent(
                id="pixy",
                name="PIXY",
                role="UI/UX Designer",
                capabilities=[
                    "ui_design",
                    "ux_design",
                    "design_systems",
                    "accessibility",
                ],
                primary_phases=[VybePhase.DESIGN],
                collaboration_phases=[
                    VybePhase.REQUIREMENTS,
                    VybePhase.ARCHITECTURE,
                    VybePhase.IMPLEMENTATION,
                ],
                catchphrase="Design is not just how it looks, but how it works!",
                educational_focus="User experience and design systems",
            ),
            "ducky": VybeAgent(
                id="ducky",
                name="DUCKY",
                role="Quality Guardian",
                capabilities=[
                    "quality_assurance",
                    "testing",
                    "validation",
                    "security_review",
                ],
                primary_phases=[VybePhase.QUALITY],
                collaboration_phases=[VybePhase.IMPLEMENTATION, VybePhase.DEPLOYMENT],
                catchphrase="Quality is not an accident, it's a habit!",
                educational_focus="Quality assurance and security validation",
            ),
            "happy": VybeAgent(
                id="happy",
                name="HAPPY",
                role="Harmony Coordinator",
                capabilities=["deployment", "devops", "monitoring", "coordination"],
                primary_phases=[VybePhase.DEPLOYMENT],
                collaboration_phases=[VybePhase.QUALITY, VybePhase.IMPLEMENTATION],
                catchphrase="Harmony in deployment brings joy to users!",
                educational_focus="Deployment and system coordination",
            ),
            "vybro": VybeAgent(
                id="vybro",
                name="VYBRO",
                role="Full-Stack Developer",
                capabilities=[
                    "full_stack_development",
                    "frontend",
                    "backend",
                    "integration",
                ],
                primary_phases=[VybePhase.IMPLEMENTATION],
                collaboration_phases=[
                    VybePhase.ARCHITECTURE,
                    VybePhase.DESIGN,
                    VybePhase.QUALITY,
                ],
                catchphrase="Code is poetry in motion, let's make it beautiful!",
                educational_focus="Full-stack development and implementation",
            ),
        }

    def _initialize_phase_definitions(self) -> Dict[VybePhase, PhaseDefinition]:
        """Initialize Vybe Method phase definitions"""
        return {
            VybePhase.ANALYSIS: PhaseDefinition(
                phase=VybePhase.ANALYSIS,
                name="Market Analysis",
                description="Comprehensive market research and business opportunity analysis",
                primary_agent="vyba",
                supporting_agents=[],
                execution_pattern=ExecutionPattern.SEQUENTIAL,
                requires_consensus=False,
                quality_gates=[
                    "market_data_validation",
                    "competitive_analysis_completeness",
                ],
                educational_objectives=[
                    "Understanding market research",
                    "Competitive analysis techniques",
                ],
                input_artifacts=["project_brief"],
                output_artifacts=[
                    "market_analysis",
                    "competitive_landscape",
                    "opportunity_assessment",
                ],
            ),
            VybePhase.REQUIREMENTS: PhaseDefinition(
                phase=VybePhase.REQUIREMENTS,
                name="Requirements Gathering",
                description="Product requirements and feature specification",
                primary_agent="qubert",
                supporting_agents=["vyba"],
                execution_pattern=ExecutionPattern.SEQUENTIAL,
                requires_consensus=True,
                quality_gates=["requirements_completeness", "user_story_validation"],
                educational_objectives=[
                    "Requirements gathering",
                    "User story creation",
                ],
                input_artifacts=["market_analysis"],
                output_artifacts=[
                    "product_requirements",
                    "user_stories",
                    "acceptance_criteria",
                ],
            ),
            VybePhase.ARCHITECTURE: PhaseDefinition(
                phase=VybePhase.ARCHITECTURE,
                name="Technical Architecture",
                description="System design and technical architecture",
                primary_agent="codex",
                supporting_agents=["qubert"],
                execution_pattern=ExecutionPattern.PARALLEL,
                requires_consensus=True,
                quality_gates=["architecture_feasibility", "scalability_assessment"],
                educational_objectives=[
                    "System design principles",
                    "Architecture patterns",
                ],
                input_artifacts=["product_requirements"],
                output_artifacts=[
                    "technical_architecture",
                    "system_design",
                    "technology_stack",
                ],
            ),
            VybePhase.DESIGN: PhaseDefinition(
                phase=VybePhase.DESIGN,
                name="UI/UX Design",
                description="User experience and design system creation",
                primary_agent="pixy",
                supporting_agents=["qubert"],
                execution_pattern=ExecutionPattern.PARALLEL,
                requires_consensus=False,
                quality_gates=["design_consistency", "accessibility_compliance"],
                educational_objectives=["Design systems", "User experience principles"],
                input_artifacts=["product_requirements"],
                output_artifacts=["design_system", "ui_specifications", "user_flows"],
            ),
            VybePhase.IMPLEMENTATION: PhaseDefinition(
                phase=VybePhase.IMPLEMENTATION,
                name="Development",
                description="Full-stack development and implementation",
                primary_agent="vybro",
                supporting_agents=["codex", "pixy"],
                execution_pattern=ExecutionPattern.SEQUENTIAL,
                requires_consensus=False,
                quality_gates=["code_quality", "functionality_validation"],
                educational_objectives=[
                    "Development practices",
                    "Code quality standards",
                ],
                input_artifacts=["technical_architecture", "design_system"],
                output_artifacts=["source_code", "application_build", "documentation"],
            ),
            VybePhase.QUALITY: PhaseDefinition(
                phase=VybePhase.QUALITY,
                name="Quality Assurance",
                description="Quality validation and security review",
                primary_agent="ducky",
                supporting_agents=["vybro"],
                execution_pattern=ExecutionPattern.PARALLEL,
                requires_consensus=False,
                quality_gates=["security_validation", "performance_testing"],
                educational_objectives=["Quality assurance", "Security best practices"],
                input_artifacts=["source_code"],
                output_artifacts=[
                    "quality_report",
                    "security_assessment",
                    "test_results",
                ],
            ),
            VybePhase.DEPLOYMENT: PhaseDefinition(
                phase=VybePhase.DEPLOYMENT,
                name="Deployment",
                description="Production deployment and monitoring",
                primary_agent="happy",
                supporting_agents=["ducky"],
                execution_pattern=ExecutionPattern.PARALLEL,
                requires_consensus=False,
                quality_gates=["deployment_readiness", "monitoring_setup"],
                educational_objectives=[
                    "Deployment strategies",
                    "Production monitoring",
                ],
                input_artifacts=["application_build", "quality_report"],
                output_artifacts=[
                    "deployed_application",
                    "monitoring_dashboard",
                    "deployment_report",
                ],
            ),
        }

    async def _initialize_phase(
        self, phase: VybePhase, phase_def: PhaseDefinition, workflow_context: Dict
    ):
        """Initialize a phase for execution"""
        # Add educational content about phase start
        await self._add_educational_content(
            {
                "type": "phase_start",
                "phase": phase.value,
                "description": phase_def.description,
                "primary_agent": phase_def.primary_agent,
                "educational_objectives": phase_def.educational_objectives,
                "workflow_id": workflow_context["workflow_id"],
            }
        )

    async def _validate_phase_output(
        self, phase: VybePhase, primary_result: Dict, supporting_results: Dict
    ) -> Dict[str, Any]:
        """Validate phase output against quality gates"""
        phase_def = self.phase_definitions[phase]
        validation_results = {}

        for gate in phase_def.quality_gates:
            # Implement specific validation logic for each quality gate
            validation_results[gate] = await self._validate_quality_gate(
                gate, primary_result, supporting_results
            )

        return validation_results

    async def _validate_quality_gate(
        self, gate: str, primary_result: Dict, supporting_results: Dict
    ) -> Dict[str, Any]:
        """Validate a specific quality gate"""
        # Implement quality gate validation logic
        return {
            "gate": gate,
            "status": "passed",
            "details": f"Quality gate {gate} validation completed",
            "timestamp": datetime.now().isoformat(),
        }

    async def _evaluate_consensus(
        self, phase: VybePhase, primary_result: Dict, supporting_results: Dict
    ) -> Dict[str, Any]:
        """Evaluate consensus for phase decisions"""
        # Create consensus proposal
        proposal = ConsensusProposal(
            proposal_id=f"{phase.value}_{int(datetime.now().timestamp())}",
            title=f"{phase.value.title()} Phase Consensus",
            description=f"Consensus evaluation for {phase.value} phase results",
            proposal_type="phase_validation",
            proposed_by="system",
            content={
                "phase": phase.value,
                "primary_result": primary_result,
                "supporting_results": supporting_results,
            },
        )

        # Submit to consensus framework
        proposal_id = await self.consensus_framework.submit_proposal(proposal)

        # For now, auto-approve (in real implementation, agents would vote)
        return {
            "proposal_id": proposal_id,
            "approved": True,
            "consensus_level": "unanimous",
            "timestamp": datetime.now().isoformat(),
        }

    async def _handle_consensus_failure(
        self, phase: VybePhase, consensus_result: Dict, workflow_context: Dict
    ) -> PhaseResult:
        """Handle consensus failure"""
        # Implement consensus failure handling
        self.logger.warning(f"Consensus failed for phase: {phase.value}")

        # For now, escalate to human review
        return PhaseResult(
            phase=phase,
            status="consensus_failed",
            primary_agent=self.phase_definitions[phase].primary_agent,
            supporting_agents=self.phase_definitions[phase].supporting_agents,
            artifacts={"consensus_failure": consensus_result},
            quality_validation={},
            consensus_result=consensus_result,
            educational_content={"type": "consensus_failure", "phase": phase.value},
            execution_time=0.0,
            timestamp=datetime.now(),
        )

    async def _document_educational_outcomes(
        self,
        phase: VybePhase,
        primary_result: Dict,
        supporting_results: Dict,
        workflow_context: Dict,
    ) -> Dict[str, Any]:
        """Document educational outcomes for the phase"""
        phase_def = self.phase_definitions[phase]

        educational_content = {
            "phase": phase.value,
            "educational_objectives": phase_def.educational_objectives,
            "primary_agent": phase_def.primary_agent,
            "supporting_agents": phase_def.supporting_agents,
            "key_learnings": self._extract_key_learnings(phase, primary_result),
            "student_takeaways": self._generate_student_takeaways(
                phase, primary_result
            ),
            "workflow_id": workflow_context["workflow_id"],
            "timestamp": datetime.now().isoformat(),
        }

        await self._add_educational_content(educational_content)
        return educational_content

    async def _document_agent_reasoning(
        self,
        agent_id: str,
        phase: VybePhase,
        task_description: str,
        result: Dict,
        workflow_context: Dict,
    ):
        """Document agent reasoning for educational purposes"""
        agent = self.vybe_agents[agent_id]

        reasoning_content = {
            "type": "agent_reasoning",
            "agent_id": agent_id,
            "agent_name": agent.name,
            "phase": phase.value,
            "task": task_description,
            "reasoning": f"{agent.name} applied {agent.educational_focus} to complete {phase.value}",
            "result_summary": self._summarize_result(result),
            "vybe_method_principle": self._get_relevant_vybe_principle(phase),
            "catchphrase": agent.catchphrase,
            "workflow_id": workflow_context["workflow_id"],
            "timestamp": datetime.now().isoformat(),
        }

        await self._add_educational_content(reasoning_content)

    def _generate_agent_task(
        self, agent: VybeAgent, phase: VybePhase, input_data: Any
    ) -> str:
        """Generate task description for agent"""
        phase_def = self.phase_definitions[phase]
        return f"{agent.name} ({agent.role}): {phase_def.description}"

    def _extract_key_learnings(self, phase: VybePhase, result: Dict) -> List[str]:
        """Extract key learnings from phase result"""
        return [
            f"Completed {phase.value} phase successfully",
            f"Applied Vybe Method principles for {phase.value}",
            f"Generated artifacts for next phase",
        ]

    def _generate_student_takeaways(self, phase: VybePhase, result: Dict) -> List[str]:
        """Generate student takeaways from phase"""
        return [
            f"Understanding {phase.value} phase importance",
            f"Learning agent collaboration patterns",
            f"Seeing quality gates in action",
        ]

    def _summarize_result(self, result: Dict) -> str:
        """Summarize agent result for educational content"""
        return f"Successfully completed task with {len(result)} key outputs"

    def _get_relevant_vybe_principle(self, phase: VybePhase) -> str:
        """Get relevant Vybe Method principle for phase"""
        principles = {
            VybePhase.ANALYSIS: "Data-driven decision making",
            VybePhase.REQUIREMENTS: "User-centered design thinking",
            VybePhase.ARCHITECTURE: "Scalable system design",
            VybePhase.DESIGN: "Accessibility-first design",
            VybePhase.IMPLEMENTATION: "Quality-focused development",
            VybePhase.QUALITY: "Comprehensive validation",
            VybePhase.DEPLOYMENT: "Reliable production deployment",
        }
        return principles.get(phase, "Collaborative problem solving")

    async def _add_educational_content(self, content: Dict[str, Any]):
        """Add content to educational feed"""
        self.educational_feed.append(content)

        # Keep only last 1000 items
        if len(self.educational_feed) > 1000:
            self.educational_feed = self.educational_feed[-1000:]

    def get_educational_feed(self, limit: int = 50) -> List[Dict]:
        """Get recent educational content"""
        return self.educational_feed[-limit:]

    def get_workflow_status(self, workflow_id: str) -> Optional[Dict]:
        """Get status of a specific workflow"""
        for workflow in self.workflow_history:
            if workflow["workflow_id"] == workflow_id:
                return workflow
        return None

    def get_all_workflows(self) -> List[Dict]:
        """Get all workflow history"""
        return self.workflow_history.copy()

    async def shutdown(self):
        """Shutdown the workflow engine"""
        try:
            await self.generator.shutdown()
            await self.agent_comm.shutdown()

            self.logger.info("VybeMethodWorkflowEngine shutdown complete")

        except Exception as e:
            self.logger.error(f"Error during workflow engine shutdown: {e}")
