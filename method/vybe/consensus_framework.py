"""
Consensus Framework - Multi-Agent consensus system with guardrails for Vybe Method
Implements enterprise-grade validation, conflict resolution, and human escalation
"""

import asyncio
import json
import logging
import time
from pathlib import Path
from typing import Dict, List, Optional, Any, Set, Tuple, Union, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from collections import defaultdict, deque
import hashlib
import threading
from concurrent.futures import ThreadPoolExecutor


class ConsensusLevel(Enum):
    """Consensus requirement levels"""
    SIMPLE_MAJORITY = "simple_majority"    # 51%
    SUPER_MAJORITY = "super_majority"      # 67%
    UNANIMOUS = "unanimous"                # 100%
    EXPERT_APPROVAL = "expert_approval"    # Specific expert must approve
    HUMAN_REQUIRED = "human_required"      # Human approval required


class ConflictType(Enum):
    """Types of conflicts that can occur"""
    CODE_QUALITY = "code_quality"
    SECURITY_CONCERN = "security_concern"
    PERFORMANCE_ISSUE = "performance_issue"
    DESIGN_PATTERN = "design_pattern"
    BUSINESS_LOGIC = "business_logic"
    TESTING_COVERAGE = "testing_coverage"
    DOCUMENTATION = "documentation"
    COMPATIBILITY = "compatibility"


class ValidationStatus(Enum):
    """Validation status types"""
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    REQUIRES_REVISION = "requires_revision"
    ESCALATED = "escalated"
    TIMEOUT = "timeout"


@dataclass
class AgentVote:
    """Individual agent vote with reasoning"""
    agent_id: str
    agent_type: str
    vote: bool  # True = approve, False = reject
    confidence: float  # 0.0 to 1.0
    reasoning: str
    timestamp: datetime
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ValidationRule:
    """Individual validation rule"""
    rule_id: str
    name: str
    description: str
    rule_type: str
    consensus_level: ConsensusLevel
    required_agents: Set[str]
    weight: float = 1.0
    enabled: bool = True
    timeout_seconds: int = 300
    escalation_threshold: float = 0.3


@dataclass
class ConsensusProposal:
    """Proposal requiring consensus validation"""
    proposal_id: str
    title: str
    description: str
    proposer: str
    proposal_type: str
    content: Dict[str, Any]
    timestamp: datetime
    deadline: Optional[datetime] = None
    required_rules: List[str] = field(default_factory=list)
    votes: List[AgentVote] = field(default_factory=list)
    status: ValidationStatus = ValidationStatus.PENDING
    conflicts: List[ConflictType] = field(default_factory=list)
    escalation_reason: Optional[str] = None
    human_reviewers: Set[str] = field(default_factory=set)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class GuardrailViolation:
    """Guardrail violation detection"""
    violation_id: str
    rule_id: str
    severity: str  # "low", "medium", "high", "critical"
    description: str
    proposal_id: str
    detected_by: str
    timestamp: datetime
    auto_fix_suggested: bool = False
    fix_suggestion: Optional[str] = None


class ConsensusFramework:
    """
    Enterprise-grade consensus framework with multi-agent validation and guardrails
    """
    
    def __init__(self, config_path: Optional[Path] = None):
        self.logger = logging.getLogger(__name__)
        self.config_path = config_path
        
        # Core components
        self.validation_rules: Dict[str, ValidationRule] = {}
        self.active_proposals: Dict[str, ConsensusProposal] = {}
        self.proposal_history: List[ConsensusProposal] = []
        self.guardrail_violations: List[GuardrailViolation] = []
        
        # Agent registry
        self.registered_agents: Dict[str, Dict[str, Any]] = {}
        self.agent_capabilities: Dict[str, Set[str]] = {}
        self.agent_weights: Dict[str, float] = {}
        
        # Callbacks and hooks
        self.validation_callbacks: Dict[str, Callable] = {}
        self.escalation_callbacks: Dict[str, Callable] = {}
        self.notification_callbacks: List[Callable] = []
        
        # Threading and async
        self.lock = threading.RLock()
        self.executor = ThreadPoolExecutor(max_workers=8)
        
        # Performance metrics
        self.metrics = {
            'total_proposals': 0,
            'approved_proposals': 0,
            'rejected_proposals': 0,
            'escalated_proposals': 0,
            'avg_validation_time': deque(maxlen=100),
            'guardrail_violations': 0,
            'agent_participation': defaultdict(int),
            'consensus_accuracy': deque(maxlen=100)
        }
        
        self._initialize()
    
    def _initialize(self):
        """Initialize the consensus framework"""
        try:
            self._setup_default_rules()
            self._setup_default_callbacks()
            self.logger.info("ConsensusFramework initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize ConsensusFramework: {e}")
            raise
    
    def _setup_default_rules(self):
        """Setup default validation rules"""
        default_rules = [
            ValidationRule(
                rule_id="code_quality_check",
                name="Code Quality Validation",
                description="Validates code quality, style, and best practices",
                rule_type="code_quality",
                consensus_level=ConsensusLevel.SIMPLE_MAJORITY,
                required_agents={"developer", "architect", "qa"},
                weight=1.0,
                timeout_seconds=300
            ),
            ValidationRule(
                rule_id="security_review",
                name="Security Review",
                description="Security vulnerability and compliance check",
                rule_type="security",
                consensus_level=ConsensusLevel.EXPERT_APPROVAL,
                required_agents={"security", "architect"},
                weight=2.0,
                timeout_seconds=600
            ),
            ValidationRule(
                rule_id="performance_analysis",
                name="Performance Analysis",
                description="Performance impact and optimization review",
                rule_type="performance",
                consensus_level=ConsensusLevel.SIMPLE_MAJORITY,
                required_agents={"developer", "architect"},
                weight=1.5,
                timeout_seconds=300
            ),
            ValidationRule(
                rule_id="testing_coverage",
                name="Testing Coverage",
                description="Test coverage and quality validation",
                rule_type="testing",
                consensus_level=ConsensusLevel.SIMPLE_MAJORITY,
                required_agents={"qa", "developer"},
                weight=1.2,
                timeout_seconds=300
            ),
            ValidationRule(
                rule_id="business_logic_review",
                name="Business Logic Review",
                description="Business requirements and logic validation",
                rule_type="business",
                consensus_level=ConsensusLevel.SUPER_MAJORITY,
                required_agents={"analyst", "architect"},
                weight=1.8,
                timeout_seconds=600
            )
        ]
        
        for rule in default_rules:
            self.validation_rules[rule.rule_id] = rule
    
    def _setup_default_callbacks(self):
        """Setup default validation callbacks"""
        self.validation_callbacks["default"] = self._default_validation
        self.escalation_callbacks["human_review"] = self._escalate_to_human
    
    def register_agent(self, agent_id: str, agent_type: str, 
                      capabilities: Set[str], weight: float = 1.0,
                      metadata: Optional[Dict[str, Any]] = None):
        """Register an agent with the consensus framework"""
        with self.lock:
            self.registered_agents[agent_id] = {
                'type': agent_type,
                'capabilities': capabilities,
                'weight': weight,
                'metadata': metadata or {},
                'registered_at': datetime.now(),
                'active': True
            }
            
            self.agent_capabilities[agent_id] = capabilities
            self.agent_weights[agent_id] = weight
            
            self.logger.info(f"Registered agent: {agent_id} ({agent_type})")
    
    def create_proposal(self, title: str, description: str, proposer: str,
                       proposal_type: str, content: Dict[str, Any],
                       required_rules: Optional[List[str]] = None,
                       deadline: Optional[datetime] = None) -> str:
        """Create a new consensus proposal"""
        proposal_id = hashlib.sha256(
            f"{title}:{proposer}:{time.time()}".encode()
        ).hexdigest()[:16]
        
        proposal = ConsensusProposal(
            proposal_id=proposal_id,
            title=title,
            description=description,
            proposer=proposer,
            proposal_type=proposal_type,
            content=content,
            timestamp=datetime.now(),
            deadline=deadline,
            required_rules=required_rules or ["code_quality_check"]
        )
        
        with self.lock:
            self.active_proposals[proposal_id] = proposal
            self.metrics['total_proposals'] += 1
        
        self.logger.info(f"Created proposal: {proposal_id} - {title}")
        return proposal_id
    
    async def submit_vote(self, proposal_id: str, agent_id: str, 
                         vote: bool, confidence: float, reasoning: str,
                         metadata: Optional[Dict[str, Any]] = None) -> bool:
        """Submit an agent vote for a proposal"""
        try:
            if proposal_id not in self.active_proposals:
                self.logger.error(f"Proposal {proposal_id} not found")
                return False
            
            if agent_id not in self.registered_agents:
                self.logger.error(f"Agent {agent_id} not registered")
                return False
            
            proposal = self.active_proposals[proposal_id]
            
            # Check if agent already voted
            existing_vote = next(
                (v for v in proposal.votes if v.agent_id == agent_id), None
            )
            
            if existing_vote:
                self.logger.warning(f"Agent {agent_id} already voted on {proposal_id}")
                return False
            
            # Create vote
            vote_obj = AgentVote(
                agent_id=agent_id,
                agent_type=self.registered_agents[agent_id]['type'],
                vote=vote,
                confidence=confidence,
                reasoning=reasoning,
                timestamp=datetime.now(),
                metadata=metadata or {}
            )
            
            with self.lock:
                proposal.votes.append(vote_obj)
                self.metrics['agent_participation'][agent_id] += 1
            
            # Check for guardrail violations
            await self._check_guardrails(proposal_id, vote_obj)
            
            # Evaluate consensus if all required votes are in
            await self._evaluate_consensus(proposal_id)
            
            self.logger.info(f"Vote submitted: {agent_id} -> {proposal_id} ({vote})")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to submit vote: {e}")
            return False
    
    async def _check_guardrails(self, proposal_id: str, vote: AgentVote):
        """Check for guardrail violations in the vote"""
        try:
            proposal = self.active_proposals[proposal_id]
            
            # Low confidence with approval
            if vote.vote and vote.confidence < 0.3:
                violation = GuardrailViolation(
                    violation_id=f"low_conf_{proposal_id}_{vote.agent_id}",
                    rule_id="confidence_threshold",
                    severity="medium",
                    description=f"Agent {vote.agent_id} approved with low confidence ({vote.confidence})",
                    proposal_id=proposal_id,
                    detected_by="guardrail_system",
                    timestamp=datetime.now(),
                    auto_fix_suggested=True,
                    fix_suggestion="Request detailed reasoning or escalate for review"
                )
                
                self.guardrail_violations.append(violation)
                self.metrics['guardrail_violations'] += 1
            
            # Unanimous rejection (potential issue)
            rejections = [v for v in proposal.votes if not v.vote]
            if len(rejections) >= 3:
                violation = GuardrailViolation(
                    violation_id=f"unanimous_reject_{proposal_id}",
                    rule_id="unanimous_rejection",
                    severity="high",
                    description=f"Proposal receiving multiple rejections ({len(rejections)})",
                    proposal_id=proposal_id,
                    detected_by="guardrail_system",
                    timestamp=datetime.now(),
                    auto_fix_suggested=True,
                    fix_suggestion="Review proposal requirements and consider revision"
                )
                
                self.guardrail_violations.append(violation)
                proposal.conflicts.append(ConflictType.DESIGN_PATTERN)
            
        except Exception as e:
            self.logger.error(f"Guardrail check failed: {e}")
    
    async def _evaluate_consensus(self, proposal_id: str) -> bool:
        """Evaluate if consensus has been reached for a proposal"""
        try:
            proposal = self.active_proposals[proposal_id]
            
            # Check if all required agents have voted
            all_voted = await self._check_all_required_votes(proposal)
            
            if not all_voted:
                # Check for timeout
                if proposal.deadline and datetime.now() > proposal.deadline:
                    await self._handle_timeout(proposal_id)
                return False
            
            # Evaluate each required rule
            consensus_results = {}
            for rule_id in proposal.required_rules:
                if rule_id in self.validation_rules:
                    rule = self.validation_rules[rule_id]
                    consensus_results[rule_id] = await self._evaluate_rule_consensus(proposal, rule)
            
            # Determine overall consensus
            overall_consensus = all(consensus_results.values())
            
            if overall_consensus:
                await self._approve_proposal(proposal_id)
            else:
                # Check if escalation is needed
                rejection_rate = sum(1 for r in consensus_results.values() if not r) / len(consensus_results)
                if rejection_rate >= 0.5:  # More than 50% rules failed
                    await self._escalate_proposal(proposal_id, "Multiple validation rules failed")
                else:
                    await self._reject_proposal(proposal_id, "Consensus not reached")
            
            return overall_consensus
            
        except Exception as e:
            self.logger.error(f"Consensus evaluation failed: {e}")
            return False
    
    async def _check_all_required_votes(self, proposal: ConsensusProposal) -> bool:
        """Check if all required agents have voted"""
        voted_agents = {vote.agent_id for vote in proposal.votes}
        
        for rule_id in proposal.required_rules:
            if rule_id in self.validation_rules:
                rule = self.validation_rules[rule_id]
                required_agent_types = rule.required_agents
                
                # Find agents of required types
                required_agents = set()
                for agent_id, agent_info in self.registered_agents.items():
                    if agent_info['type'] in required_agent_types and agent_info['active']:
                        required_agents.add(agent_id)
                
                # Check if at least one agent of each required type has voted
                for agent_type in required_agent_types:
                    type_agents = {
                        aid for aid, info in self.registered_agents.items() 
                        if info['type'] == agent_type and info['active']
                    }
                    
                    if not type_agents.intersection(voted_agents):
                        return False
        
        return True
    
    async def _evaluate_rule_consensus(self, proposal: ConsensusProposal, 
                                     rule: ValidationRule) -> bool:
        """Evaluate consensus for a specific rule"""
        # Get relevant votes for this rule
        relevant_votes = []
        for vote in proposal.votes:
            agent_type = self.registered_agents[vote.agent_id]['type']
            if agent_type in rule.required_agents:
                relevant_votes.append(vote)
        
        if not relevant_votes:
            return False
        
        # Calculate weighted consensus based on rule level
        if rule.consensus_level == ConsensusLevel.UNANIMOUS:
            return all(vote.vote for vote in relevant_votes)
        
        elif rule.consensus_level == ConsensusLevel.SUPER_MAJORITY:
            approval_rate = sum(1 for vote in relevant_votes if vote.vote) / len(relevant_votes)
            return approval_rate >= 0.67
        
        elif rule.consensus_level == ConsensusLevel.SIMPLE_MAJORITY:
            approval_rate = sum(1 for vote in relevant_votes if vote.vote) / len(relevant_votes)
            return approval_rate > 0.5
        
        elif rule.consensus_level == ConsensusLevel.EXPERT_APPROVAL:
            # Find expert agents (higher weight or specific capabilities)
            expert_votes = [
                vote for vote in relevant_votes 
                if self.agent_weights.get(vote.agent_id, 1.0) >= 1.5
            ]
            return any(vote.vote for vote in expert_votes) if expert_votes else False
        
        elif rule.consensus_level == ConsensusLevel.HUMAN_REQUIRED:
            # Always escalate to human for this level
            return False
        
        return False
    
    async def _approve_proposal(self, proposal_id: str):
        """Approve a consensus proposal"""
        with self.lock:
            proposal = self.active_proposals[proposal_id]
            proposal.status = ValidationStatus.APPROVED
            
            # Move to history
            self.proposal_history.append(proposal)
            del self.active_proposals[proposal_id]
            
            self.metrics['approved_proposals'] += 1
        
        # Notify callbacks
        await self._notify_callbacks("approval", proposal)
        
        self.logger.info(f"Proposal approved: {proposal_id}")
    
    async def _reject_proposal(self, proposal_id: str, reason: str):
        """Reject a consensus proposal"""
        with self.lock:
            proposal = self.active_proposals[proposal_id]
            proposal.status = ValidationStatus.REJECTED
            proposal.escalation_reason = reason
            
            # Move to history
            self.proposal_history.append(proposal)
            del self.active_proposals[proposal_id]
            
            self.metrics['rejected_proposals'] += 1
        
        # Notify callbacks
        await self._notify_callbacks("rejection", proposal)
        
        self.logger.info(f"Proposal rejected: {proposal_id} - {reason}")
    
    async def _escalate_proposal(self, proposal_id: str, reason: str):
        """Escalate a proposal for human review"""
        with self.lock:
            proposal = self.active_proposals[proposal_id]
            proposal.status = ValidationStatus.ESCALATED
            proposal.escalation_reason = reason
            
            self.metrics['escalated_proposals'] += 1
        
        # Execute escalation callbacks
        for callback in self.escalation_callbacks.values():
            try:
                await callback(proposal, reason)
            except Exception as e:
                self.logger.error(f"Escalation callback failed: {e}")
        
        self.logger.warning(f"Proposal escalated: {proposal_id} - {reason}")
    
    async def _handle_timeout(self, proposal_id: str):
        """Handle proposal timeout"""
        with self.lock:
            proposal = self.active_proposals[proposal_id]
            proposal.status = ValidationStatus.TIMEOUT
            
            # Move to history
            self.proposal_history.append(proposal)
            del self.active_proposals[proposal_id]
        
        self.logger.warning(f"Proposal timed out: {proposal_id}")
    
    async def _notify_callbacks(self, event_type: str, proposal: ConsensusProposal):
        """Notify registered callbacks"""
        for callback in self.notification_callbacks:
            try:
                await callback(event_type, proposal)
            except Exception as e:
                self.logger.error(f"Notification callback failed: {e}")
    
    def _default_validation(self, proposal: ConsensusProposal) -> bool:
        """Default validation logic"""
        return True
    
    async def _escalate_to_human(self, proposal: ConsensusProposal, reason: str):
        """Default human escalation handler"""
        self.logger.info(f"Human review required for {proposal.proposal_id}: {reason}")
        # In a real implementation, this would integrate with a human review system
    
    def get_proposal_status(self, proposal_id: str) -> Optional[Dict[str, Any]]:
        """Get the current status of a proposal"""
        if proposal_id in self.active_proposals:
            proposal = self.active_proposals[proposal_id]
        else:
            proposal = next(
                (p for p in self.proposal_history if p.proposal_id == proposal_id),
                None
            )
        
        if not proposal:
            return None
        
        return {
            'proposal_id': proposal.proposal_id,
            'title': proposal.title,
            'status': proposal.status.value,
            'votes_count': len(proposal.votes),
            'conflicts': [c.value for c in proposal.conflicts],
            'escalation_reason': proposal.escalation_reason,
            'created_at': proposal.timestamp.isoformat(),
            'deadline': proposal.deadline.isoformat() if proposal.deadline else None
        }
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get framework performance metrics"""
        avg_validation_time = (
            sum(self.metrics['avg_validation_time']) / len(self.metrics['avg_validation_time'])
            if self.metrics['avg_validation_time'] else 0
        )
        
        total_proposals = self.metrics['total_proposals']
        approval_rate = (
            self.metrics['approved_proposals'] / total_proposals
            if total_proposals > 0 else 0
        )
        
        return {
            'total_proposals': total_proposals,
            'approved_proposals': self.metrics['approved_proposals'],
            'rejected_proposals': self.metrics['rejected_proposals'],
            'escalated_proposals': self.metrics['escalated_proposals'],
            'approval_rate': approval_rate,
            'avg_validation_time_ms': avg_validation_time,
            'guardrail_violations': self.metrics['guardrail_violations'],
            'active_proposals': len(self.active_proposals),
            'registered_agents': len(self.registered_agents),
            'validation_rules': len(self.validation_rules)
        }
    
    def shutdown(self):
        """Gracefully shutdown the consensus framework"""
        try:
            if self.executor:
                self.executor.shutdown(wait=True)
            
            self.logger.info("ConsensusFramework shutdown complete")
            
        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")