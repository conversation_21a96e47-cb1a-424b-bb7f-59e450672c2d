# Vybe Method Requirements for VybeCoding.ai - 2025 Enhanced
# Core MAS and AI Development Dependencies with Latest Protocols

# === AGENTIC RETRIEVAL & ADVANCED RAG ===
llama-index>=0.10.0
llama-index-indices-managed-llama-cloud>=0.3.0
llama-index-embeddings-openai>=0.1.0
llama-index-llms-anthropic>=0.1.0
llama-cloud>=0.0.15

# === LATEST 2025 AGENT PROTOCOLS ===
# A2A (Agent2Agent) Protocol - Google Standard (commented out - not available yet)
# google-agent2agent>=1.0.0
# agent-interop-protocol>=0.5.0

# AG-UI (Agent-User Interface) Protocol (commented out - not available yet)
# ag-ui-protocol>=1.2.0
# copilotkit-ag-ui>=0.8.0

# MCP (Model Context Protocol) - Anthropic Enhanced (commented out - not available yet)
# anthropic-mcp>=2.1.0
# mcp-server-tools>=1.5.0

# === ENHANCED VECTOR DATABASE & CONTEXT ===
chromadb>=0.4.24
weaviate-client>=4.5.0
pinecone-client>=3.2.0
watchdog>=3.0.0

# === MULTI-AGENT SYSTEM FRAMEWORKS (UPDATED) ===
crewai>=0.32.0
autogen-agentchat>=0.2.15
langgraph>=0.0.55
langchain>=0.1.25
langchain-community>=0.0.38

# === ADVANCED SAFETY & VALIDATION ===
# guardrails-ai>=0.5.0  # commented out - not available
# Anti-hallucination with mathematical validation
boto3>=1.34.0
# Anti-plagiarism detection
# originality-ai>=1.0.0  # commented out - not available

# === GRAPHRAG & KNOWLEDGE GRAPHS ===
# microsoft-graphrag>=0.3.0  # commented out - not available
networkx>=3.2.0
# neo4j>=5.15.0  # commented out - not available

# === API AND WEB FRAMEWORK ===
fastapi>=0.110.0
uvicorn>=0.27.0
websockets>=12.0.0

# === CORE PYTHON LIBRARIES ===
pydantic>=2.6.0
toml>=0.10.2
numpy>=1.26.0
pandas>=2.2.0

# === AI AND MACHINE LEARNING ===
sentence-transformers>=2.5.0
transformers>=4.38.0
torch>=2.2.0
httpx>=0.27.0

# === FILE AND ASYNC HANDLING ===
aiofiles>=23.2.1
python-multipart>=0.0.6
asyncio-mqtt>=0.16.0

# === DEVELOPMENT AND TESTING ===
pytest>=8.0.0
pytest-asyncio>=0.23.0
pytest-mock>=3.12.0

# === REAL-TIME COMMUNICATION ===
redis>=5.0.0
celery>=5.3.0

# === MONITORING & OBSERVABILITY ===
prometheus-client>=0.20.0
opentelemetry-api>=1.22.0

# === VS CODE MCP INTEGRATION ===
# Model Context Protocol for VS Code integration (commented out - not available)
# mcp>=1.0.0
# mcp-server>=1.0.0
# mcp-client>=1.0.0

# === TYPESCRIPT INTEGRATION ===
# Node.js integration for TypeScript agents (commented out - not available)
# nodejs>=18.0.0
# npm>=9.0.0

# === ENHANCED DEVELOPMENT TOOLS ===
# VS Code extension support (commented out - not available)
# code-server>=4.20.0
jupyter>=1.0.0

# === ADDITIONAL SAFETY & VALIDATION ===
# Enhanced plagiarism detection (commented out - not available)
# difflib2>=0.1.0
textdistance>=4.6.0

# === PERFORMANCE MONITORING ===
# System monitoring and metrics
psutil>=5.9.0
memory-profiler>=0.61.0

# === OPTIONAL: CLOUD AI SERVICES ===
# openai>=1.12.0
# anthropic>=0.18.0
# azure-cognitiveservices>=0.2.0

# === COMPETITIVE ANALYSIS ADDITIONS ===
# Based on comparison with other developer stack
websockets>=12.0.0
sse-starlette>=1.8.0
jsonrpc-base>=2.2.0
jsonrpc-websocket>=3.1.5
jsonrpc-async>=2.1.2
