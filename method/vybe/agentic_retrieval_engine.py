"""
Agentic Retrieval Engine for Vybe Method
Advanced RAG implementation with multi-modal, multi-index intelligent routing
Based on LlamaIndex 2025 best practices: "RAG is dead, long live agentic retrieval"
"""

import os
import asyncio
import logging
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from pathlib import Path
from datetime import datetime
import json

# Core LlamaIndex imports for agentic retrieval
try:
    from llama_index.core import VectorStoreIndex, Document, Settings
    from llama_index.core.retrievers import BaseRetriever
    from llama_index.core.query_engine import BaseQueryEngine
    from llama_index.indices.managed.llama_cloud import LlamaCloudIndex, LlamaCloudCompositeRetriever
    from llama_cloud import CompositeRetrievalMode
    from llama_index.embeddings.openai import OpenAIEmbedding
    from llama_index.llms.anthropic import Anthropic
except ImportError:
    # Fallback for development without LlamaIndex
    print("LlamaIndex not installed - using mock implementations")
    BaseRetriever = object
    BaseQueryEngine = object

# Vector database imports
try:
    import chromadb
    from chromadb.config import Settings as ChromaSettings
except ImportError:
    chromadb = None

# GraphRAG imports
try:
    import networkx as nx
    from microsoft_graphrag import GraphRAG
except ImportError:
    nx = None
    GraphRAG = None

@dataclass
class RetrievalContext:
    """Context for agentic retrieval operations"""
    query: str
    agent_id: str
    retrieval_mode: str = "auto_routed"
    max_results: int = 5
    rerank: bool = True
    include_metadata: bool = True
    timestamp: datetime = field(default_factory=datetime.now)

@dataclass
class RetrievalResult:
    """Result from agentic retrieval"""
    content: str
    source: str
    score: float
    metadata: Dict[str, Any]
    retrieval_mode: str
    agent_id: str
    timestamp: datetime = field(default_factory=datetime.now)

class AgenticRetrievalEngine:
    """
    Advanced Agentic Retrieval Engine implementing 2025 best practices
    Features:
    - Multi-modal retrieval (text, code, images, documents)
    - Intelligent routing between retrieval modes
    - GraphRAG for complex reasoning
    - Real-time agent context integration
    - Composite index management
    """
    
    def __init__(self, project_root: str, project_name: str = "VybeCoding"):
        self.project_root = Path(project_root)
        self.project_name = project_name
        self.logger = logging.getLogger(__name__)
        
        # Initialize components
        self.indices: Dict[str, Any] = {}
        self.composite_retriever = None
        self.graph_rag = None
        self.chroma_client = None
        
        # Performance metrics
        self.metrics = {
            "total_queries": 0,
            "successful_retrievals": 0,
            "failed_retrievals": 0,
            "avg_retrieval_time": 0.0,
            "cache_hits": 0,
            "agent_queries": {},
            "retrieval_modes_used": {}
        }
        
        # Cache for frequent queries
        self.query_cache: Dict[str, List[RetrievalResult]] = {}
        self.cache_ttl = 3600  # 1 hour
        
        self._initialize_retrieval_engine()
    
    def _initialize_retrieval_engine(self):
        """Initialize the agentic retrieval engine"""
        try:
            # Set up LlamaIndex settings
            Settings.embed_model = OpenAIEmbedding(model="text-embedding-3-large")
            Settings.llm = Anthropic(model="claude-3-sonnet-20240229")
            
            # Initialize ChromaDB for local vector storage
            if chromadb:
                self.chroma_client = chromadb.PersistentClient(
                    path=str(self.project_root / "data" / "chroma_db"),
                    settings=ChromaSettings(anonymized_telemetry=False)
                )
            
            # Initialize GraphRAG for complex reasoning
            if GraphRAG:
                self.graph_rag = GraphRAG(
                    data_dir=str(self.project_root / "data" / "graph_rag"),
                    embedding_model="text-embedding-3-large"
                )
            
            self.logger.info("Agentic Retrieval Engine initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize retrieval engine: {e}")
            # Continue with limited functionality
    
    async def create_knowledge_index(self, 
                                   name: str, 
                                   description: str, 
                                   documents: List[str] = None,
                                   file_paths: List[str] = None) -> str:
        """
        Create a new knowledge index with agentic retrieval capabilities
        
        Args:
            name: Index name
            description: Description for routing decisions
            documents: List of document texts
            file_paths: List of file paths to index
            
        Returns:
            Index ID
        """
        try:
            # Create LlamaCloud index for production-grade retrieval
            index = LlamaCloudIndex.from_documents(
                documents=[],  # Will upload files separately
                name=name,
                project_name=self.project_name,
            )
            
            # Upload files if provided
            if file_paths:
                for file_path in file_paths:
                    if Path(file_path).exists():
                        index.upload_file(file_path, wait_for_ingestion=False)
                
                # Wait for all files to be processed
                index.wait_for_completion()
            
            # Store index with metadata
            self.indices[name] = {
                "index": index,
                "description": description,
                "created": datetime.now(),
                "document_count": len(file_paths) if file_paths else 0
            }
            
            self.logger.info(f"Created knowledge index: {name}")
            return name
            
        except Exception as e:
            self.logger.error(f"Failed to create index {name}: {e}")
            return None
    
    async def setup_composite_retriever(self):
        """Set up composite retriever for multi-index agentic routing"""
        try:
            self.composite_retriever = LlamaCloudCompositeRetriever(
                name="Vybe Knowledge Agent",
                project_name=self.project_name,
                create_if_not_exists=True,
                mode=CompositeRetrievalMode.ROUTED,  # Intelligent routing
                rerank_top_n=5,  # Rerank top results
            )
            
            # Add all indices to composite retriever
            for name, index_data in self.indices.items():
                self.composite_retriever.add_index(
                    index_data["index"],
                    description=index_data["description"]
                )
            
            self.logger.info("Composite retriever configured with agentic routing")
            
        except Exception as e:
            self.logger.error(f"Failed to setup composite retriever: {e}")
    
    async def agentic_retrieve(self, 
                             context: RetrievalContext) -> List[RetrievalResult]:
        """
        Perform agentic retrieval with intelligent routing
        
        Args:
            context: Retrieval context with query and agent info
            
        Returns:
            List of retrieval results
        """
        start_time = datetime.now()
        self.metrics["total_queries"] += 1
        
        # Track agent usage
        if context.agent_id not in self.metrics["agent_queries"]:
            self.metrics["agent_queries"][context.agent_id] = 0
        self.metrics["agent_queries"][context.agent_id] += 1
        
        try:
            # Check cache first
            cache_key = f"{context.query}:{context.retrieval_mode}:{context.max_results}"
            if cache_key in self.query_cache:
                self.metrics["cache_hits"] += 1
                return self.query_cache[cache_key]
            
            results = []
            
            # Route to appropriate retrieval method
            if context.retrieval_mode == "auto_routed" and self.composite_retriever:
                # Use composite retriever for intelligent routing
                nodes = self.composite_retriever.retrieve(context.query)
                
                for node in nodes[:context.max_results]:
                    result = RetrievalResult(
                        content=node.text,
                        source=node.metadata.get("file_name", "unknown"),
                        score=node.score if hasattr(node, 'score') else 1.0,
                        metadata=node.metadata,
                        retrieval_mode=node.metadata.get("retrieval_mode", "auto_routed"),
                        agent_id=context.agent_id
                    )
                    results.append(result)
            
            elif context.retrieval_mode == "graph_rag" and self.graph_rag:
                # Use GraphRAG for complex reasoning
                graph_results = await self._graph_rag_retrieve(context)
                results.extend(graph_results)
            
            else:
                # Fallback to single index retrieval
                results = await self._single_index_retrieve(context)
            
            # Cache results
            self.query_cache[cache_key] = results
            
            # Update metrics
            self.metrics["successful_retrievals"] += 1
            retrieval_time = (datetime.now() - start_time).total_seconds()
            self._update_avg_time(retrieval_time)
            
            # Track retrieval mode usage
            mode = context.retrieval_mode
            if mode not in self.metrics["retrieval_modes_used"]:
                self.metrics["retrieval_modes_used"][mode] = 0
            self.metrics["retrieval_modes_used"][mode] += 1
            
            self.logger.info(f"Agentic retrieval completed: {len(results)} results in {retrieval_time:.2f}s")
            return results
            
        except Exception as e:
            self.logger.error(f"Agentic retrieval failed: {e}")
            self.metrics["failed_retrievals"] += 1
            return []
    
    async def _graph_rag_retrieve(self, context: RetrievalContext) -> List[RetrievalResult]:
        """Perform GraphRAG retrieval for complex reasoning"""
        try:
            # Use GraphRAG for complex multi-hop reasoning
            graph_results = self.graph_rag.query(
                query=context.query,
                max_results=context.max_results
            )
            
            results = []
            for result in graph_results:
                retrieval_result = RetrievalResult(
                    content=result.content,
                    source=result.source,
                    score=result.score,
                    metadata={"reasoning_path": result.reasoning_path},
                    retrieval_mode="graph_rag",
                    agent_id=context.agent_id
                )
                results.append(retrieval_result)
            
            return results
            
        except Exception as e:
            self.logger.error(f"GraphRAG retrieval failed: {e}")
            return []
    
    async def _single_index_retrieve(self, context: RetrievalContext) -> List[RetrievalResult]:
        """Fallback single index retrieval"""
        results = []
        
        # Try each index until we get results
        for name, index_data in self.indices.items():
            try:
                retriever = index_data["index"].as_retriever(
                    retrieval_mode=context.retrieval_mode,
                    similarity_top_k=context.max_results
                )
                
                nodes = retriever.retrieve(context.query)
                
                for node in nodes:
                    result = RetrievalResult(
                        content=node.text,
                        source=name,
                        score=node.score if hasattr(node, 'score') else 1.0,
                        metadata=node.metadata,
                        retrieval_mode=context.retrieval_mode,
                        agent_id=context.agent_id
                    )
                    results.append(result)
                
                if results:
                    break  # Found results, stop searching
                    
            except Exception as e:
                self.logger.warning(f"Failed to retrieve from index {name}: {e}")
                continue
        
        return results
    
    def _update_avg_time(self, new_time: float):
        """Update average retrieval time"""
        current_avg = self.metrics["avg_retrieval_time"]
        total_queries = self.metrics["total_queries"]
        
        if total_queries == 1:
            self.metrics["avg_retrieval_time"] = new_time
        else:
            self.metrics["avg_retrieval_time"] = (
                (current_avg * (total_queries - 1) + new_time) / total_queries
            )
    
    def get_retrieval_metrics(self) -> Dict[str, Any]:
        """Get comprehensive retrieval metrics"""
        return {
            **self.metrics,
            "indices_count": len(self.indices),
            "cache_size": len(self.query_cache),
            "cache_hit_rate": (
                self.metrics["cache_hits"] / max(self.metrics["total_queries"], 1)
            ) * 100
        }
    
    async def clear_cache(self):
        """Clear query cache"""
        self.query_cache.clear()
        self.logger.info("Query cache cleared")
    
    async def optimize_indices(self):
        """Optimize indices for better performance"""
        try:
            # Rebuild indices if needed
            for name, index_data in self.indices.items():
                # Check if index needs optimization
                if index_data["document_count"] > 1000:
                    self.logger.info(f"Optimizing large index: {name}")
                    # Implement optimization logic here
            
            self.logger.info("Index optimization completed")
            
        except Exception as e:
            self.logger.error(f"Index optimization failed: {e}")


# Integration with Vybe Method MAS
class VybeAgenticRetrieval:
    """Integration layer for Vybe Method agents"""
    
    def __init__(self, retrieval_engine: AgenticRetrievalEngine):
        self.engine = retrieval_engine
        self.logger = logging.getLogger(__name__)
    
    async def agent_query(self, 
                         agent_id: str, 
                         query: str, 
                         context_type: str = "auto") -> List[RetrievalResult]:
        """
        Agent-specific retrieval with context awareness
        
        Args:
            agent_id: ID of requesting agent (VYBA, QUBERT, etc.)
            query: Search query
            context_type: Type of context needed
            
        Returns:
            Contextual retrieval results
        """
        # Map agent to optimal retrieval strategy
        retrieval_mode = self._get_agent_retrieval_mode(agent_id, context_type)
        
        context = RetrievalContext(
            query=query,
            agent_id=agent_id,
            retrieval_mode=retrieval_mode,
            max_results=self._get_agent_max_results(agent_id)
        )
        
        return await self.engine.agentic_retrieve(context)
    
    def _get_agent_retrieval_mode(self, agent_id: str, context_type: str) -> str:
        """Get optimal retrieval mode for specific agent"""
        agent_modes = {
            "vyba": "auto_routed",  # Business analyst needs comprehensive search
            "qubert": "files_via_content",  # PM needs document-level context
            "codex": "graph_rag",  # Architect needs complex reasoning
            "pixy": "chunks",  # Designer needs specific details
            "ducky": "auto_routed",  # QA needs thorough search
            "happy": "files_via_metadata",  # Coordinator needs file organization
            "vybro": "graph_rag"  # Developer needs complex code relationships
        }
        
        return agent_modes.get(agent_id.lower(), "auto_routed")
    
    def _get_agent_max_results(self, agent_id: str) -> int:
        """Get optimal result count for specific agent"""
        agent_limits = {
            "vyba": 10,  # Business analyst needs comprehensive results
            "qubert": 5,  # PM needs focused results
            "codex": 8,  # Architect needs detailed context
            "pixy": 3,  # Designer needs specific examples
            "ducky": 15,  # QA needs thorough coverage
            "happy": 5,  # Coordinator needs organized results
            "vybro": 12  # Developer needs extensive code context
        }
        
        return agent_limits.get(agent_id.lower(), 5)
