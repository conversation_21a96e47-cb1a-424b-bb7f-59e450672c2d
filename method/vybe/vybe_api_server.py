#!/usr/bin/env python3
"""
Vybe API Server
HTTP server to bridge Vybe commands with SvelteKit frontend
"""

import asyncio
import json
import logging
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import sys
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

try:
    from vybe_commands import VybeCommandSystem
    VYBE_AVAILABLE = True
except ImportError as e:
    VYBE_AVAILABLE = False
    logging.error(f"Vybe commands not available: {e}")

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class VybeAPIHandler(BaseHTTPRequestHandler):
    """HTTP request handler for Vybe API"""
    
    def __init__(self, *args, vybe_system=None, **kwargs):
        self.vybe_system = vybe_system
        super().__init__(*args, **kwargs)
    
    def do_OPTIONS(self):
        """Handle CORS preflight requests"""
        self.send_response(200)
        self.send_cors_headers()
        self.end_headers()
    
    def do_GET(self):
        """Handle GET requests"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        try:
            if path == '/vybe/status':
                self.handle_status()
            elif path == '/health':
                self.handle_health()
            else:
                self.send_error(404, "Endpoint not found")
        except Exception as e:
            logger.error(f"GET request failed: {e}")
            self.send_json_response({'status': 'error', 'message': str(e)}, 500)
    
    def do_POST(self):
        """Handle POST requests"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        try:
            # Read request body
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = self.rfile.read(content_length)
            
            try:
                request_data = json.loads(post_data.decode('utf-8')) if post_data else {}
            except json.JSONDecodeError:
                request_data = {}
            
            if path == '/vybe/start':
                self.handle_start(request_data)
            elif path == '/vybe/analyze':
                self.handle_analyze(request_data)
            else:
                self.send_error(404, "Endpoint not found")
                
        except Exception as e:
            logger.error(f"POST request failed: {e}")
            self.send_json_response({'status': 'error', 'message': str(e)}, 500)
    
    def handle_health(self):
        """Handle health check"""
        response = {
            'status': 'healthy',
            'service': 'vybe-api-server',
            'timestamp': datetime.now().isoformat(),
            'vybe_available': VYBE_AVAILABLE
        }
        self.send_json_response(response)
    
    def handle_start(self, request_data):
        """Handle /vybe/start command"""
        if not VYBE_AVAILABLE:
            self.send_json_response({
                'status': 'error',
                'message': 'Vybe system not available'
            }, 503)
            return
        
        async def run_start():
            return await self.vybe_system.vybe_start()
        
        result = asyncio.run(run_start())
        self.send_json_response(result)
    
    def handle_status(self):
        """Handle /vybe/status command"""
        if not VYBE_AVAILABLE:
            self.send_json_response({
                'status': 'error',
                'message': 'Vybe system not available'
            }, 503)
            return
        
        async def run_status():
            return await self.vybe_system.vybe_status()
        
        result = asyncio.run(run_status())
        self.send_json_response(result)
    
    def handle_analyze(self, request_data):
        """Handle /vybe/analyze command"""
        if not VYBE_AVAILABLE:
            self.send_json_response({
                'status': 'error',
                'message': 'Vybe system not available'
            }, 503)
            return
        
        target = request_data.get('target', None)
        
        async def run_analyze():
            return await self.vybe_system.vybe_analyze(target)
        
        result = asyncio.run(run_analyze())
        self.send_json_response(result)
    
    def send_cors_headers(self):
        """Send CORS headers"""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
    
    def send_json_response(self, data, status_code=200):
        """Send JSON response"""
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json')
        self.send_cors_headers()
        self.end_headers()
        
        response_json = json.dumps(data, indent=2)
        self.wfile.write(response_json.encode('utf-8'))
    
    def log_message(self, format, *args):
        """Override to use our logger"""
        logger.info(f"{self.address_string()} - {format % args}")


class VybeAPIServer:
    """Vybe API Server"""
    
    def __init__(self, host='localhost', port=8000):
        self.host = host
        self.port = port
        self.vybe_system = VybeCommandSystem() if VYBE_AVAILABLE else None
        
    def create_handler(self):
        """Create request handler with vybe_system"""
        def handler(*args, **kwargs):
            return VybeAPIHandler(*args, vybe_system=self.vybe_system, **kwargs)
        return handler
    
    def start(self):
        """Start the API server"""
        if not VYBE_AVAILABLE:
            logger.error("❌ Vybe system not available. Cannot start API server.")
            return False
        
        try:
            handler_class = self.create_handler()
            httpd = HTTPServer((self.host, self.port), handler_class)
            
            logger.info(f"🚀 Vybe API Server starting on http://{self.host}:{self.port}")
            logger.info("📋 Available endpoints:")
            logger.info("  GET  /health        - Health check")
            logger.info("  POST /vybe/start    - Initialize Vybe system")
            logger.info("  GET  /vybe/status   - Check system status")
            logger.info("  POST /vybe/analyze  - Run codebase analysis")
            logger.info("\n🔗 Integration with SvelteKit:")
            logger.info(f"  Add proxy in vite.config.js:")
            logger.info(f"  '/api/vybe': 'http://{self.host}:{self.port}/vybe'")
            
            httpd.serve_forever()
            
        except KeyboardInterrupt:
            logger.info("\n🛑 Server stopped by user")
            return True
        except Exception as e:
            logger.error(f"❌ Server failed to start: {e}")
            return False


def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Vybe API Server')
    parser.add_argument('--host', default='localhost', help='Host to bind to')
    parser.add_argument('--port', type=int, default=8000, help='Port to bind to')
    parser.add_argument('--test', action='store_true', help='Run quick test')
    
    args = parser.parse_args()
    
    if args.test:
        # Quick test
        print("🧪 Testing Vybe API Server...")
        
        if not VYBE_AVAILABLE:
            print("❌ Vybe system not available")
            return False
        
        try:
            vybe = VybeCommandSystem()
            print("✅ VybeCommandSystem created")
            
            # Test basic functionality
            async def test():
                result = await vybe.vybe_status()
                print(f"✅ Status check: {result.get('status', 'unknown')}")
                return True
            
            success = asyncio.run(test())
            print("✅ Basic test passed" if success else "❌ Basic test failed")
            return success
            
        except Exception as e:
            print(f"❌ Test failed: {e}")
            return False
    else:
        # Start server
        server = VybeAPIServer(host=args.host, port=args.port)
        return server.start()


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
