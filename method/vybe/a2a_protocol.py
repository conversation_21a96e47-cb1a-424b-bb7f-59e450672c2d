"""
A2A (Agent2Agent) Protocol Implementation for Vybe Method
Based on Google's 2025 A2A standard for agent interoperability
Enables seamless communication between Vybe agents and external agent systems
"""

import asyncio
import json
import logging
import uuid
from typing import Dict, List, Optional, Any, Callable, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import websockets
import aiohttp

# A2A Protocol Message Types
class A2AMessageType(Enum):
    """Standard A2A message types"""
    HANDSHAKE = "handshake"
    REQUEST = "request"
    RESPONSE = "response"
    NOTIFICATION = "notification"
    ERROR = "error"
    HEARTBEAT = "heartbeat"
    CAPABILITY_QUERY = "capability_query"
    CAPABILITY_RESPONSE = "capability_response"
    TASK_DELEGATION = "task_delegation"
    TASK_RESULT = "task_result"

class A2AProtocolVersion(Enum):
    """Supported A2A protocol versions"""
    V1_0 = "1.0"
    V1_1 = "1.1"
    V2_0 = "2.0"  # Latest 2025 standard

@dataclass
class A2AMessage:
    """Standard A2A protocol message"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    type: A2AMessageType = A2AMessageType.REQUEST
    version: A2AProtocolVersion = A2AProtocolVersion.V2_0
    sender_id: str = ""
    receiver_id: str = ""
    timestamp: datetime = field(default_factory=datetime.now)
    payload: Dict[str, Any] = field(default_factory=dict)
    correlation_id: Optional[str] = None
    ttl: Optional[int] = 300  # Time to live in seconds
    priority: int = 5  # 1-10, 10 being highest
    requires_response: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert message to dictionary for transmission"""
        return {
            "id": self.id,
            "type": self.type.value,
            "version": self.version.value,
            "sender_id": self.sender_id,
            "receiver_id": self.receiver_id,
            "timestamp": self.timestamp.isoformat(),
            "payload": self.payload,
            "correlation_id": self.correlation_id,
            "ttl": self.ttl,
            "priority": self.priority,
            "requires_response": self.requires_response
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'A2AMessage':
        """Create message from dictionary"""
        return cls(
            id=data.get("id", str(uuid.uuid4())),
            type=A2AMessageType(data.get("type", "request")),
            version=A2AProtocolVersion(data.get("version", "2.0")),
            sender_id=data.get("sender_id", ""),
            receiver_id=data.get("receiver_id", ""),
            timestamp=datetime.fromisoformat(data.get("timestamp", datetime.now().isoformat())),
            payload=data.get("payload", {}),
            correlation_id=data.get("correlation_id"),
            ttl=data.get("ttl", 300),
            priority=data.get("priority", 5),
            requires_response=data.get("requires_response", False)
        )

@dataclass
class AgentCapability:
    """Agent capability description for A2A discovery"""
    name: str
    description: str
    input_schema: Dict[str, Any]
    output_schema: Dict[str, Any]
    version: str = "1.0"
    cost: float = 0.0  # Computational cost estimate
    latency_ms: int = 1000  # Expected latency
    reliability: float = 0.99  # Reliability score 0-1

class A2AProtocolHandler:
    """
    A2A Protocol Handler for Vybe Method agents
    Implements Google's 2025 A2A standard for agent interoperability
    """
    
    def __init__(self, agent_id: str, agent_type: str, port: int = 8765):
        self.agent_id = agent_id
        self.agent_type = agent_type
        self.port = port
        self.logger = logging.getLogger(f"A2A.{agent_id}")
        
        # Protocol state
        self.is_running = False
        self.connections: Dict[str, websockets.WebSocketServerProtocol] = {}
        self.pending_requests: Dict[str, asyncio.Future] = {}
        self.message_handlers: Dict[A2AMessageType, Callable] = {}
        self.capabilities: Dict[str, AgentCapability] = {}
        
        # Performance metrics
        self.metrics = {
            "messages_sent": 0,
            "messages_received": 0,
            "successful_handshakes": 0,
            "failed_handshakes": 0,
            "active_connections": 0,
            "avg_response_time": 0.0,
            "error_count": 0
        }
        
        # Register default handlers
        self._register_default_handlers()
    
    def _register_default_handlers(self):
        """Register default A2A message handlers"""
        self.message_handlers[A2AMessageType.HANDSHAKE] = self._handle_handshake
        self.message_handlers[A2AMessageType.HEARTBEAT] = self._handle_heartbeat
        self.message_handlers[A2AMessageType.CAPABILITY_QUERY] = self._handle_capability_query
        self.message_handlers[A2AMessageType.ERROR] = self._handle_error
    
    async def start_server(self):
        """Start A2A protocol server"""
        try:
            self.is_running = True
            server = await websockets.serve(
                self._handle_connection,
                "localhost",
                self.port
            )
            self.logger.info(f"A2A server started on port {self.port}")
            await server.wait_closed()
            
        except Exception as e:
            self.logger.error(f"Failed to start A2A server: {e}")
            self.is_running = False
    
    async def _handle_connection(self, websocket, path):
        """Handle new A2A connection"""
        connection_id = str(uuid.uuid4())
        self.connections[connection_id] = websocket
        self.metrics["active_connections"] += 1
        
        try:
            self.logger.info(f"New A2A connection: {connection_id}")
            
            async for message_data in websocket:
                try:
                    # Parse A2A message
                    data = json.loads(message_data)
                    message = A2AMessage.from_dict(data)
                    
                    # Handle message
                    await self._process_message(message, websocket)
                    
                except json.JSONDecodeError:
                    await self._send_error(websocket, "Invalid JSON format")
                except Exception as e:
                    await self._send_error(websocket, f"Message processing error: {e}")
                    
        except websockets.exceptions.ConnectionClosed:
            self.logger.info(f"A2A connection closed: {connection_id}")
        except Exception as e:
            self.logger.error(f"A2A connection error: {e}")
        finally:
            self.connections.pop(connection_id, None)
            self.metrics["active_connections"] -= 1
    
    async def _process_message(self, message: A2AMessage, websocket):
        """Process incoming A2A message"""
        self.metrics["messages_received"] += 1
        
        # Check message TTL
        if message.ttl and (datetime.now() - message.timestamp).total_seconds() > message.ttl:
            await self._send_error(websocket, "Message TTL expired", message.id)
            return
        
        # Route to appropriate handler
        handler = self.message_handlers.get(message.type)
        if handler:
            try:
                await handler(message, websocket)
            except Exception as e:
                self.logger.error(f"Handler error for {message.type}: {e}")
                await self._send_error(websocket, f"Handler error: {e}", message.id)
        else:
            await self._send_error(websocket, f"Unsupported message type: {message.type}", message.id)
    
    async def _handle_handshake(self, message: A2AMessage, websocket):
        """Handle A2A handshake"""
        try:
            # Validate handshake payload
            required_fields = ["agent_id", "agent_type", "protocol_version"]
            if not all(field in message.payload for field in required_fields):
                await self._send_error(websocket, "Invalid handshake payload", message.id)
                return
            
            # Send handshake response
            response = A2AMessage(
                type=A2AMessageType.RESPONSE,
                sender_id=self.agent_id,
                receiver_id=message.sender_id,
                correlation_id=message.id,
                payload={
                    "agent_id": self.agent_id,
                    "agent_type": self.agent_type,
                    "protocol_version": A2AProtocolVersion.V2_0.value,
                    "capabilities": list(self.capabilities.keys()),
                    "status": "connected"
                }
            )
            
            await self._send_message(response, websocket)
            self.metrics["successful_handshakes"] += 1
            self.logger.info(f"Handshake completed with {message.sender_id}")
            
        except Exception as e:
            self.metrics["failed_handshakes"] += 1
            await self._send_error(websocket, f"Handshake failed: {e}", message.id)
    
    async def _handle_heartbeat(self, message: A2AMessage, websocket):
        """Handle heartbeat message"""
        response = A2AMessage(
            type=A2AMessageType.HEARTBEAT,
            sender_id=self.agent_id,
            receiver_id=message.sender_id,
            correlation_id=message.id,
            payload={"status": "alive", "timestamp": datetime.now().isoformat()}
        )
        await self._send_message(response, websocket)
    
    async def _handle_capability_query(self, message: A2AMessage, websocket):
        """Handle capability query"""
        capabilities_data = {}
        for name, capability in self.capabilities.items():
            capabilities_data[name] = {
                "description": capability.description,
                "input_schema": capability.input_schema,
                "output_schema": capability.output_schema,
                "version": capability.version,
                "cost": capability.cost,
                "latency_ms": capability.latency_ms,
                "reliability": capability.reliability
            }
        
        response = A2AMessage(
            type=A2AMessageType.CAPABILITY_RESPONSE,
            sender_id=self.agent_id,
            receiver_id=message.sender_id,
            correlation_id=message.id,
            payload={"capabilities": capabilities_data}
        )
        await self._send_message(response, websocket)
    
    async def _handle_error(self, message: A2AMessage, websocket):
        """Handle error message"""
        self.metrics["error_count"] += 1
        self.logger.error(f"Received error from {message.sender_id}: {message.payload}")
    
    async def _send_message(self, message: A2AMessage, websocket):
        """Send A2A message"""
        try:
            message_json = json.dumps(message.to_dict())
            await websocket.send(message_json)
            self.metrics["messages_sent"] += 1
            
        except Exception as e:
            self.logger.error(f"Failed to send message: {e}")
    
    async def _send_error(self, websocket, error_message: str, correlation_id: str = None):
        """Send error message"""
        error_msg = A2AMessage(
            type=A2AMessageType.ERROR,
            sender_id=self.agent_id,
            correlation_id=correlation_id,
            payload={"error": error_message, "timestamp": datetime.now().isoformat()}
        )
        await self._send_message(error_msg, websocket)
    
    def register_capability(self, capability: AgentCapability):
        """Register agent capability for A2A discovery"""
        self.capabilities[capability.name] = capability
        self.logger.info(f"Registered capability: {capability.name}")
    
    def register_message_handler(self, message_type: A2AMessageType, handler: Callable):
        """Register custom message handler"""
        self.message_handlers[message_type] = handler
        self.logger.info(f"Registered handler for {message_type}")
    
    async def connect_to_agent(self, agent_url: str, agent_id: str) -> bool:
        """Connect to another agent via A2A protocol"""
        try:
            async with websockets.connect(agent_url) as websocket:
                # Send handshake
                handshake = A2AMessage(
                    type=A2AMessageType.HANDSHAKE,
                    sender_id=self.agent_id,
                    receiver_id=agent_id,
                    payload={
                        "agent_id": self.agent_id,
                        "agent_type": self.agent_type,
                        "protocol_version": A2AProtocolVersion.V2_0.value
                    }
                )
                
                await self._send_message(handshake, websocket)
                
                # Wait for response
                response_data = await websocket.recv()
                response = A2AMessage.from_dict(json.loads(response_data))
                
                if response.type == A2AMessageType.RESPONSE:
                    self.logger.info(f"Successfully connected to agent {agent_id}")
                    return True
                else:
                    self.logger.error(f"Handshake failed with agent {agent_id}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"Failed to connect to agent {agent_id}: {e}")
            return False
    
    async def send_request(self, 
                          agent_url: str, 
                          receiver_id: str, 
                          payload: Dict[str, Any],
                          timeout: int = 30) -> Optional[A2AMessage]:
        """Send request to another agent and wait for response"""
        try:
            async with websockets.connect(agent_url) as websocket:
                request = A2AMessage(
                    type=A2AMessageType.REQUEST,
                    sender_id=self.agent_id,
                    receiver_id=receiver_id,
                    payload=payload,
                    requires_response=True
                )
                
                await self._send_message(request, websocket)
                
                # Wait for response with timeout
                response_data = await asyncio.wait_for(websocket.recv(), timeout=timeout)
                response = A2AMessage.from_dict(json.loads(response_data))
                
                return response
                
        except asyncio.TimeoutError:
            self.logger.error(f"Request timeout to agent {receiver_id}")
            return None
        except Exception as e:
            self.logger.error(f"Request failed to agent {receiver_id}: {e}")
            return None
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get A2A protocol metrics"""
        return {
            **self.metrics,
            "capabilities_count": len(self.capabilities),
            "handlers_count": len(self.message_handlers),
            "is_running": self.is_running
        }
    
    async def stop(self):
        """Stop A2A protocol handler"""
        self.is_running = False
        for websocket in self.connections.values():
            await websocket.close()
        self.connections.clear()
        self.logger.info("A2A protocol handler stopped")


# Vybe Method A2A Integration
class VybeA2AIntegration:
    """Integration layer for Vybe Method agents with A2A protocol"""
    
    def __init__(self):
        self.agents: Dict[str, A2AProtocolHandler] = {}
        self.logger = logging.getLogger("VybeA2A")
    
    def register_vybe_agent(self, agent_id: str, agent_type: str, port: int):
        """Register Vybe agent for A2A communication"""
        handler = A2AProtocolHandler(agent_id, agent_type, port)
        
        # Register Vybe-specific capabilities
        self._register_vybe_capabilities(handler, agent_id)
        
        self.agents[agent_id] = handler
        self.logger.info(f"Registered Vybe agent {agent_id} for A2A")
    
    def _register_vybe_capabilities(self, handler: A2AProtocolHandler, agent_id: str):
        """Register capabilities specific to Vybe agents"""
        vybe_capabilities = {
            "vyba": [
                AgentCapability(
                    name="business_analysis",
                    description="Analyze business requirements and create project briefs",
                    input_schema={"requirements": "string", "context": "object"},
                    output_schema={"analysis": "object", "recommendations": "array"}
                )
            ],
            "qubert": [
                AgentCapability(
                    name="product_management",
                    description="Create PRDs and manage product requirements",
                    input_schema={"brief": "object", "stakeholders": "array"},
                    output_schema={"prd": "object", "timeline": "object"}
                )
            ],
            "codex": [
                AgentCapability(
                    name="technical_architecture",
                    description="Design technical architecture and system design",
                    input_schema={"requirements": "object", "constraints": "array"},
                    output_schema={"architecture": "object", "diagrams": "array"}
                )
            ],
            "pixy": [
                AgentCapability(
                    name="ui_ux_design",
                    description="Create UI/UX designs and prototypes",
                    input_schema={"requirements": "object", "brand": "object"},
                    output_schema={"designs": "array", "prototypes": "array"}
                )
            ],
            "ducky": [
                AgentCapability(
                    name="quality_assurance",
                    description="Perform quality testing and validation",
                    input_schema={"code": "string", "requirements": "object"},
                    output_schema={"test_results": "object", "issues": "array"}
                )
            ],
            "happy": [
                AgentCapability(
                    name="deployment_coordination",
                    description="Coordinate deployments and team harmony",
                    input_schema={"deployment_plan": "object", "team_status": "object"},
                    output_schema={"deployment_status": "object", "coordination_report": "object"}
                )
            ],
            "vybro": [
                AgentCapability(
                    name="code_development",
                    description="Develop code and implement features",
                    input_schema={"specifications": "object", "architecture": "object"},
                    output_schema={"code": "string", "tests": "array", "documentation": "string"}
                )
            ]
        }
        
        capabilities = vybe_capabilities.get(agent_id.lower(), [])
        for capability in capabilities:
            handler.register_capability(capability)
    
    async def start_all_agents(self):
        """Start A2A servers for all registered agents"""
        tasks = []
        for agent_id, handler in self.agents.items():
            task = asyncio.create_task(handler.start_server())
            tasks.append(task)
            self.logger.info(f"Starting A2A server for {agent_id}")
        
        await asyncio.gather(*tasks, return_exceptions=True)
    
    async def stop_all_agents(self):
        """Stop all A2A agents"""
        for handler in self.agents.values():
            await handler.stop()
        self.logger.info("All Vybe A2A agents stopped")
