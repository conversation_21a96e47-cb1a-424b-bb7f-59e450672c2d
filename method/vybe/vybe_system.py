"""
Vybe System - Main orchestrator for the Vybe Method with Multi-Agent System capabilities
Enterprise-grade AI development platform with context awareness and consensus-driven development
"""

import asyncio
import logging
import time
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from datetime import datetime

from config import VybeConfig
from context_engine import ContextEngine
from consensus_framework import ConsensusFramework
from mas_coordinator import MASCoordinator


@dataclass
class VybeSession:
    """Represents an active Vybe development session"""
    session_id: str
    project_path: Path
    started_at: datetime = field(default_factory=datetime.now)
    active_agents: List[str] = field(default_factory=list)
    context_state: Dict[str, Any] = field(default_factory=dict)
    consensus_decisions: List[Dict[str, Any]] = field(default_factory=list)
    quality_metrics: Dict[str, float] = field(default_factory=dict)


class VybeSystem:
    """
    Main orchestrator for the Vybe Method Multi-Agent System
    
    This class integrates all Vybe components to provide:
    - Context-aware development with unlimited local processing
    - Multi-agent consensus-driven development
    - Real-time codebase indexing and analysis
    - Hallucination-free, bug-free development protocols
    - Enterprise-grade guardrails and quality gates
    """
    
    def __init__(self, config_path: Optional[Union[str, Path]] = None):
        """Initialize the Vybe System with configuration"""
        self.logger = logging.getLogger(__name__)
        self.config = VybeConfig(config_path)
        
        # Core components
        self.context_engine: Optional[ContextEngine] = None
        self.consensus_framework: Optional[ConsensusFramework] = None
        self.mas_coordinator: Optional[MASCoordinator] = None
        
        # Session management
        self.current_session: Optional[VybeSession] = None
        self.session_history: List[VybeSession] = []
        
        # System state
        self.is_initialized = False
        self.is_running = False
        self.performance_metrics: Dict[str, Any] = {}
        
        # Setup logging
        self._setup_logging()
        
    def _setup_logging(self):
        """Configure logging for the Vybe System"""
        log_level = getattr(logging, self.config.logging.level.upper())
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.config.logging.file_path),
                logging.StreamHandler()
            ]
        )
        self.logger.info("Vybe System logging initialized")
        
    async def initialize(self, project_path: Optional[Union[str, Path]] = None) -> bool:
        """
        Initialize all Vybe system components
        
        Args:
            project_path: Optional path to the project to analyze
            
        Returns:
            bool: True if initialization successful
        """
        try:
            self.logger.info("Initializing Vybe System...")
            
            # Initialize core components
            self.logger.info("Initializing Context Engine...")
            self.context_engine = ContextEngine(self.config)
            await self.context_engine.initialize()
            
            self.logger.info("Initializing Consensus Framework...")
            self.consensus_framework = ConsensusFramework(self.config)
            await self.consensus_framework.initialize()
            
            self.logger.info("Initializing MAS Coordinator...")
            self.mas_coordinator = MASCoordinator(self.config)
            await self.mas_coordinator.initialize()
            
            # Connect components
            await self._connect_components()
            
            # Load project if specified
            if project_path:
                await self.load_project(project_path)
                
            self.is_initialized = True
            self.logger.info("Vybe System initialization complete")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Vybe System: {e}")
            return False
            
    async def _connect_components(self):
        """Connect all system components for seamless integration"""
        # Connect context engine to MAS coordinator
        self.mas_coordinator.set_context_engine(self.context_engine)
        
        # Connect consensus framework to MAS coordinator
        self.mas_coordinator.set_consensus_framework(self.consensus_framework)
        
        # Enable real-time communication between components
        await self.context_engine.register_observer(self.mas_coordinator)
        await self.consensus_framework.register_observer(self.mas_coordinator)
        
    async def load_project(self, project_path: Union[str, Path]) -> bool:
        """
        Load and analyze a project for Vybe development
        
        Args:
            project_path: Path to the project directory
            
        Returns:
            bool: True if project loaded successfully
        """
        try:
            project_path = Path(project_path)
            if not project_path.exists():
                self.logger.error(f"Project path does not exist: {project_path}")
                return False
                
            self.logger.info(f"Loading project: {project_path}")
            
            # Index the project with context engine
            await self.context_engine.index_project(project_path)
            
            # Analyze project structure and dependencies
            project_analysis = await self.context_engine.analyze_project_structure(project_path)
            
            # Initialize agents based on project type
            await self.mas_coordinator.initialize_project_agents(project_analysis)
            
            self.logger.info(f"Project loaded successfully: {project_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to load project: {e}")
            return False
            
    async def start_session(self, session_id: Optional[str] = None, project_path: Optional[Union[str, Path]] = None) -> VybeSession:
        """
        Start a new Vybe development session
        
        Args:
            session_id: Optional custom session ID
            project_path: Optional project path for the session
            
        Returns:
            VybeSession: The created session
        """
        if not self.is_initialized:
            await self.initialize(project_path)
            
        session_id = session_id or f"vybe_session_{int(time.time())}"
        
        # Create new session
        self.current_session = VybeSession(
            session_id=session_id,
            project_path=Path(project_path) if project_path else Path.cwd()
        )
        
        # Start MAS coordinator
        await self.mas_coordinator.start_session(session_id)
        
        # Activate core agents
        core_agents = ["orchestrator", "analyst", "architect", "developer", "qa"]
        for agent in core_agents:
            await self.mas_coordinator.activate_agent(agent)
            self.current_session.active_agents.append(agent)
            
        self.is_running = True
        self.logger.info(f"Vybe session started: {session_id}")
        
        return self.current_session
        
    async def execute_task(self, task_description: str, **kwargs) -> Dict[str, Any]:
        """
        Execute a development task using the Vybe Method
        
        Args:
            task_description: Description of the task to execute
            **kwargs: Additional task parameters
            
        Returns:
            Dict containing task results and metrics
        """
        if not self.current_session:
            raise RuntimeError("No active Vybe session. Call start_session() first.")
            
        self.logger.info(f"Executing task: {task_description}")
        
        try:
            # Analyze task with context engine
            task_context = await self.context_engine.analyze_task(task_description, **kwargs)
            
            # Route task to appropriate agents via MAS coordinator
            task_result = await self.mas_coordinator.execute_task(
                task_description, 
                task_context, 
                **kwargs
            )
            
            # Apply consensus framework for validation
            consensus_result = await self.consensus_framework.validate_result(task_result)
            
            # Update session state
            self.current_session.context_state.update(task_context)
            self.current_session.consensus_decisions.append(consensus_result)
            
            # Calculate quality metrics
            quality_metrics = await self._calculate_quality_metrics(task_result, consensus_result)
            self.current_session.quality_metrics.update(quality_metrics)
            
            self.logger.info(f"Task completed successfully: {task_description}")
            
            return {
                "status": "success",
                "result": task_result,
                "consensus": consensus_result,
                "metrics": quality_metrics,
                "session_id": self.current_session.session_id
            }
            
        except Exception as e:
            self.logger.error(f"Task execution failed: {e}")
            return {
                "status": "error",
                "error": str(e),
                "session_id": self.current_session.session_id if self.current_session else None
            }
            
    async def get_project_insights(self) -> Dict[str, Any]:
        """Get comprehensive insights about the current project"""
        if not self.context_engine:
            return {"error": "Context engine not initialized"}
            
        insights = await self.context_engine.get_project_insights()
        return insights
        
    async def get_agent_status(self) -> Dict[str, Any]:
        """Get status of all active agents"""
        if not self.mas_coordinator:
            return {"error": "MAS coordinator not initialized"}
            
        return await self.mas_coordinator.get_agent_status()
        
    async def get_system_health(self) -> Dict[str, Any]:
        """Get comprehensive system health metrics"""
        health = {
            "system_status": "healthy" if self.is_running else "stopped",
            "initialized": self.is_initialized,
            "active_session": self.current_session.session_id if self.current_session else None,
            "uptime": time.time() - (self.current_session.started_at.timestamp() if self.current_session else time.time()),
            "components": {}
        }
        
        # Check component health
        if self.context_engine:
            health["components"]["context_engine"] = await self.context_engine.health_check()
            
        if self.consensus_framework:
            health["components"]["consensus_framework"] = await self.consensus_framework.health_check()
            
        if self.mas_coordinator:
            health["components"]["mas_coordinator"] = await self.mas_coordinator.health_check()
            
        return health
        
    async def _calculate_quality_metrics(self, task_result: Dict[str, Any], consensus_result: Dict[str, Any]) -> Dict[str, float]:
        """Calculate quality metrics for completed tasks"""
        metrics = {
            "consensus_score": consensus_result.get("score", 0.0),
            "completion_time": task_result.get("execution_time", 0.0),
            "agent_agreement": consensus_result.get("agreement_level", 0.0),
            "code_quality": task_result.get("quality_score", 0.0),
            "test_coverage": task_result.get("test_coverage", 0.0)
        }
        
        # Overall quality score
        metrics["overall_quality"] = sum(metrics.values()) / len(metrics)
        
        return metrics
        
    async def stop_session(self):
        """Stop the current Vybe session"""
        if self.current_session:
            self.logger.info(f"Stopping Vybe session: {self.current_session.session_id}")
            
            # Deactivate agents
            await self.mas_coordinator.stop_session()
            
            # Archive session
            self.session_history.append(self.current_session)
            self.current_session = None
            
            self.is_running = False
            self.logger.info("Vybe session stopped")
            
    async def shutdown(self):
        """Shutdown the entire Vybe system"""
        self.logger.info("Shutting down Vybe System...")
        
        if self.current_session:
            await self.stop_session()
            
        # Shutdown components
        if self.mas_coordinator:
            await self.mas_coordinator.shutdown()
            
        if self.consensus_framework:
            await self.consensus_framework.shutdown()
            
        if self.context_engine:
            await self.context_engine.shutdown()
            
        self.is_initialized = False
        self.logger.info("Vybe System shutdown complete")
        
    def __repr__(self) -> str:
        return f"VybeSystem(initialized={self.is_initialized}, running={self.is_running})"


# Convenience functions for easy usage
async def create_vybe_system(config_path: Optional[Union[str, Path]] = None) -> VybeSystem:
    """Create and initialize a new Vybe System"""
    system = VybeSystem(config_path)
    await system.initialize()
    return system


async def quick_start(project_path: Union[str, Path], task_description: str, config_path: Optional[Union[str, Path]] = None) -> Dict[str, Any]:
    """Quick start function for simple Vybe tasks"""
    system = await create_vybe_system(config_path)
    session = await system.start_session(project_path=project_path)
    result = await system.execute_task(task_description)
    await system.stop_session()
    return result


if __name__ == "__main__":
    # Example usage
    async def main():
        system = VybeSystem()
        await system.initialize()
        
        session = await system.start_session(project_path=".")
        result = await system.execute_task("Analyze the current project structure")
        
        print(f"Task result: {result}")
        
        health = await system.get_system_health()
        print(f"System health: {health}")
        
        await system.shutdown()
        
    asyncio.run(main())