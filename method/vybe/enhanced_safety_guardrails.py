"""
Enhanced Safety Guardrails for Vybe Method
Advanced anti-hallucination, anti-plagiarism, and safety validation
Based on 2025 best practices for AI safety and reliability
"""

import asyncio
import logging
import hashlib
import re
from typing import Dict, List, Optional, Any, Tuple, Union
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import json

# Safety validation imports
try:
    import guardrails as gd
    from guardrails.validators import Valid<PERSON>ength, ValidChoices, RegexMatch
    from guardrails.hub import CompetitorCheck, ToxicLanguage, ProfanityFree
except ImportError:
    gd = None

# Anti-plagiarism imports
try:
    import difflib
    from sentence_transformers import SentenceTransformer
    import numpy as np
except ImportError:
    SentenceTransformer = None
    np = None

# Mathematical validation imports
try:
    import sympy
    from sympy import sympify, simplify
except ImportError:
    sympy = None

class SafetyLevel(Enum):
    """Safety validation levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class ValidationResult(Enum):
    """Validation result types"""
    PASS = "pass"
    WARNING = "warning"
    FAIL = "fail"
    BLOCKED = "blocked"

@dataclass
class SafetyViolation:
    """Safety violation details"""
    type: str
    severity: SafetyLevel
    message: str
    confidence: float
    evidence: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)

@dataclass
class ValidationReport:
    """Comprehensive validation report"""
    content_id: str
    agent_id: str
    overall_result: ValidationResult
    safety_score: float  # 0-1, 1 being safest
    violations: List[SafetyViolation] = field(default_factory=list)
    checks_performed: List[str] = field(default_factory=list)
    processing_time: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)

class EnhancedSafetyGuardrails:
    """
    Enhanced Safety Guardrails System for Vybe Method
    Features:
    - Advanced anti-hallucination with mathematical validation
    - Semantic plagiarism detection
    - Educational content safety
    - Real-time fact checking
    - Multi-layer consensus validation
    """
    
    def __init__(self, safety_level: SafetyLevel = SafetyLevel.HIGH):
        self.safety_level = safety_level
        self.logger = logging.getLogger(__name__)
        
        # Initialize components
        self.sentence_model = None
        self.known_facts_db = {}
        self.plagiarism_cache = {}
        self.math_validator = None
        
        # Safety metrics
        self.metrics = {
            "total_validations": 0,
            "passed_validations": 0,
            "failed_validations": 0,
            "blocked_content": 0,
            "hallucination_detected": 0,
            "plagiarism_detected": 0,
            "safety_violations": 0,
            "avg_processing_time": 0.0
        }
        
        # Educational safety rules
        self.educational_rules = {
            "age_appropriate": True,
            "curriculum_aligned": True,
            "factual_accuracy": True,
            "no_harmful_content": True,
            "privacy_compliant": True
        }
        
        self._initialize_safety_systems()
    
    def _initialize_safety_systems(self):
        """Initialize safety validation systems"""
        try:
            # Initialize sentence transformer for semantic analysis
            if SentenceTransformer:
                self.sentence_model = SentenceTransformer('all-MiniLM-L6-v2')
            
            # Initialize mathematical validator
            if sympy:
                self.math_validator = MathematicalValidator()
            
            # Load known facts database
            self._load_known_facts()
            
            self.logger.info("Enhanced safety guardrails initialized")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize safety systems: {e}")
    
    def _load_known_facts(self):
        """Load known facts database for fact checking"""
        # This would typically load from a comprehensive fact database
        self.known_facts_db = {
            "python": {
                "creator": "Guido van Rossum",
                "first_release": "1991",
                "paradigm": "multi-paradigm"
            },
            "javascript": {
                "creator": "Brendan Eich",
                "first_release": "1995",
                "paradigm": "multi-paradigm"
            },
            "ai": {
                "turing_test": "1950",
                "deep_learning": "neural networks with multiple layers",
                "machine_learning": "subset of artificial intelligence"
            }
        }
    
    async def validate_content(self, 
                             content: str, 
                             agent_id: str, 
                             content_type: str = "text",
                             context: Dict[str, Any] = None) -> ValidationReport:
        """
        Comprehensive content validation
        
        Args:
            content: Content to validate
            agent_id: ID of agent generating content
            content_type: Type of content (text, code, educational, etc.)
            context: Additional context for validation
            
        Returns:
            Comprehensive validation report
        """
        start_time = datetime.now()
        content_id = hashlib.md5(content.encode()).hexdigest()[:12]
        
        self.metrics["total_validations"] += 1
        
        report = ValidationReport(
            content_id=content_id,
            agent_id=agent_id,
            overall_result=ValidationResult.PASS,
            safety_score=1.0
        )
        
        try:
            # Perform validation checks
            checks = [
                self._check_hallucination(content, context),
                self._check_plagiarism(content),
                self._check_toxicity(content),
                self._check_educational_safety(content, content_type),
                self._check_factual_accuracy(content),
                self._check_mathematical_validity(content)
            ]
            
            # Execute all checks concurrently
            results = await asyncio.gather(*checks, return_exceptions=True)
            
            # Process results
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    self.logger.error(f"Validation check {i} failed: {result}")
                    continue
                
                if isinstance(result, list):
                    report.violations.extend(result)
                elif isinstance(result, SafetyViolation):
                    report.violations.append(result)
            
            # Calculate overall safety score and result
            report.safety_score = self._calculate_safety_score(report.violations)
            report.overall_result = self._determine_overall_result(report.violations)
            
            # Update metrics
            if report.overall_result == ValidationResult.PASS:
                self.metrics["passed_validations"] += 1
            else:
                self.metrics["failed_validations"] += 1
                
                if report.overall_result == ValidationResult.BLOCKED:
                    self.metrics["blocked_content"] += 1
            
            # Count specific violation types
            for violation in report.violations:
                if "hallucination" in violation.type.lower():
                    self.metrics["hallucination_detected"] += 1
                elif "plagiarism" in violation.type.lower():
                    self.metrics["plagiarism_detected"] += 1
                else:
                    self.metrics["safety_violations"] += 1
            
            # Record processing time
            processing_time = (datetime.now() - start_time).total_seconds()
            report.processing_time = processing_time
            self._update_avg_processing_time(processing_time)
            
            self.logger.info(f"Content validation completed: {report.overall_result.value} "
                           f"(score: {report.safety_score:.2f}, time: {processing_time:.2f}s)")
            
            return report
            
        except Exception as e:
            self.logger.error(f"Content validation failed: {e}")
            report.overall_result = ValidationResult.FAIL
            report.violations.append(SafetyViolation(
                type="validation_error",
                severity=SafetyLevel.HIGH,
                message=f"Validation system error: {e}",
                confidence=1.0
            ))
            return report
    
    async def _check_hallucination(self, content: str, context: Dict[str, Any] = None) -> List[SafetyViolation]:
        """Check for hallucination using multiple methods"""
        violations = []
        
        try:
            # Method 1: Consistency checking
            consistency_violations = await self._check_internal_consistency(content)
            violations.extend(consistency_violations)
            
            # Method 2: Fact verification
            fact_violations = await self._verify_facts(content)
            violations.extend(fact_violations)
            
            # Method 3: Confidence analysis
            confidence_violations = await self._analyze_confidence_markers(content)
            violations.extend(confidence_violations)
            
            # Method 4: Context coherence
            if context:
                coherence_violations = await self._check_context_coherence(content, context)
                violations.extend(coherence_violations)
            
        except Exception as e:
            self.logger.error(f"Hallucination check failed: {e}")
        
        return violations
    
    async def _check_internal_consistency(self, content: str) -> List[SafetyViolation]:
        """Check for internal consistency in content"""
        violations = []
        
        # Look for contradictory statements
        sentences = content.split('.')
        contradictions = []
        
        for i, sent1 in enumerate(sentences):
            for j, sent2 in enumerate(sentences[i+1:], i+1):
                if self._are_contradictory(sent1.strip(), sent2.strip()):
                    contradictions.append((i, j, sent1.strip(), sent2.strip()))
        
        if contradictions:
            for contradiction in contradictions:
                violations.append(SafetyViolation(
                    type="hallucination_contradiction",
                    severity=SafetyLevel.HIGH,
                    message=f"Contradictory statements detected: '{contradiction[2]}' vs '{contradiction[3]}'",
                    confidence=0.8,
                    evidence={"sentences": contradiction}
                ))
        
        return violations
    
    def _are_contradictory(self, sent1: str, sent2: str) -> bool:
        """Check if two sentences are contradictory"""
        # Simple contradiction detection
        negation_words = ["not", "no", "never", "none", "nothing", "cannot", "can't", "won't", "don't", "doesn't"]
        
        # Remove negations and compare
        sent1_clean = sent1.lower()
        sent2_clean = sent2.lower()
        
        for neg in negation_words:
            if neg in sent1_clean and neg not in sent2_clean:
                # Check if the rest is similar
                sent1_no_neg = sent1_clean.replace(neg, "").strip()
                if self._semantic_similarity(sent1_no_neg, sent2_clean) > 0.7:
                    return True
            elif neg in sent2_clean and neg not in sent1_clean:
                sent2_no_neg = sent2_clean.replace(neg, "").strip()
                if self._semantic_similarity(sent1_clean, sent2_no_neg) > 0.7:
                    return True
        
        return False
    
    async def _verify_facts(self, content: str) -> List[SafetyViolation]:
        """Verify factual claims against known facts database"""
        violations = []
        
        # Extract potential factual claims
        fact_patterns = [
            r"(\w+) was created by (\w+(?:\s+\w+)*)",
            r"(\w+) was first released in (\d{4})",
            r"(\w+) is a (\w+(?:\s+\w+)*)"
        ]
        
        for pattern in fact_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                subject = match.group(1).lower()
                claim = match.group(2).lower()
                
                if subject in self.known_facts_db:
                    known_facts = self.known_facts_db[subject]
                    
                    # Check if claim contradicts known facts
                    if self._contradicts_known_facts(claim, known_facts):
                        violations.append(SafetyViolation(
                            type="hallucination_factual_error",
                            severity=SafetyLevel.HIGH,
                            message=f"Factual error detected: {match.group(0)}",
                            confidence=0.9,
                            evidence={"claim": match.group(0), "known_facts": known_facts}
                        ))
        
        return violations
    
    def _contradicts_known_facts(self, claim: str, known_facts: Dict[str, str]) -> bool:
        """Check if claim contradicts known facts"""
        for fact_value in known_facts.values():
            if isinstance(fact_value, str) and fact_value.lower() != claim:
                # Simple string matching - could be enhanced with semantic similarity
                if self._semantic_similarity(claim, fact_value.lower()) < 0.3:
                    return True
        return False
    
    async def _analyze_confidence_markers(self, content: str) -> List[SafetyViolation]:
        """Analyze confidence markers that might indicate hallucination"""
        violations = []
        
        # Uncertain language patterns
        uncertain_patterns = [
            r"i think",
            r"i believe",
            r"probably",
            r"might be",
            r"could be",
            r"seems like",
            r"appears to",
            r"i'm not sure"
        ]
        
        uncertain_count = 0
        for pattern in uncertain_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            uncertain_count += len(list(matches))
        
        # High uncertainty might indicate hallucination
        if uncertain_count > 3:
            violations.append(SafetyViolation(
                type="hallucination_high_uncertainty",
                severity=SafetyLevel.MEDIUM,
                message=f"High uncertainty detected ({uncertain_count} uncertain phrases)",
                confidence=0.6,
                evidence={"uncertain_count": uncertain_count}
            ))
        
        return violations
    
    async def _check_context_coherence(self, content: str, context: Dict[str, Any]) -> List[SafetyViolation]:
        """Check if content is coherent with provided context"""
        violations = []
        
        # This would implement sophisticated context coherence checking
        # For now, simple keyword matching
        if "topic" in context:
            topic = context["topic"].lower()
            if topic not in content.lower():
                violations.append(SafetyViolation(
                    type="hallucination_context_drift",
                    severity=SafetyLevel.MEDIUM,
                    message=f"Content appears to drift from topic: {topic}",
                    confidence=0.5,
                    evidence={"expected_topic": topic}
                ))
        
        return violations
    
    async def _check_plagiarism(self, content: str) -> List[SafetyViolation]:
        """Check for plagiarism using semantic similarity"""
        violations = []
        
        try:
            # Check against cache of known content
            content_hash = hashlib.md5(content.encode()).hexdigest()
            
            if content_hash in self.plagiarism_cache:
                violations.append(SafetyViolation(
                    type="plagiarism_exact_match",
                    severity=SafetyLevel.CRITICAL,
                    message="Exact content match detected",
                    confidence=1.0,
                    evidence={"hash": content_hash}
                ))
            
            # Semantic similarity check
            if self.sentence_model:
                for cached_content, cached_hash in self.plagiarism_cache.items():
                    if cached_hash != content_hash:
                        similarity = self._semantic_similarity(content, cached_content)
                        if similarity > 0.85:
                            violations.append(SafetyViolation(
                                type="plagiarism_semantic_similarity",
                                severity=SafetyLevel.HIGH,
                                message=f"High semantic similarity detected (score: {similarity:.2f})",
                                confidence=similarity,
                                evidence={"similarity_score": similarity}
                            ))
            
            # Add to cache
            self.plagiarism_cache[content] = content_hash
            
        except Exception as e:
            self.logger.error(f"Plagiarism check failed: {e}")
        
        return violations
    
    def _semantic_similarity(self, text1: str, text2: str) -> float:
        """Calculate semantic similarity between two texts"""
        if not self.sentence_model or not text1.strip() or not text2.strip():
            return 0.0
        
        try:
            embeddings = self.sentence_model.encode([text1, text2])
            similarity = np.dot(embeddings[0], embeddings[1]) / (
                np.linalg.norm(embeddings[0]) * np.linalg.norm(embeddings[1])
            )
            return float(similarity)
        except Exception:
            return 0.0
    
    async def _check_toxicity(self, content: str) -> List[SafetyViolation]:
        """Check for toxic or harmful content"""
        violations = []
        
        # Simple toxicity patterns (would use advanced models in production)
        toxic_patterns = [
            r"\b(hate|kill|die|stupid|idiot)\b",
            r"\b(offensive|inappropriate|harmful)\b"
        ]
        
        for pattern in toxic_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                violations.append(SafetyViolation(
                    type="toxicity_harmful_language",
                    severity=SafetyLevel.HIGH,
                    message=f"Potentially harmful language detected: {match.group(0)}",
                    confidence=0.7,
                    evidence={"match": match.group(0)}
                ))
        
        return violations
    
    async def _check_educational_safety(self, content: str, content_type: str) -> List[SafetyViolation]:
        """Check educational content safety"""
        violations = []
        
        if content_type == "educational":
            # Age-appropriate content check
            inappropriate_topics = ["violence", "adult content", "dangerous activities"]
            for topic in inappropriate_topics:
                if topic.lower() in content.lower():
                    violations.append(SafetyViolation(
                        type="educational_inappropriate_content",
                        severity=SafetyLevel.CRITICAL,
                        message=f"Inappropriate educational content: {topic}",
                        confidence=0.8,
                        evidence={"topic": topic}
                    ))
        
        return violations
    
    async def _check_factual_accuracy(self, content: str) -> List[SafetyViolation]:
        """Check factual accuracy of claims"""
        violations = []
        
        # This would integrate with fact-checking APIs
        # For now, simple pattern matching
        definitive_claims = re.finditer(r"(\w+) is (definitely|certainly|absolutely) (\w+)", content, re.IGNORECASE)
        
        for match in definitive_claims:
            # Flag overly confident claims that might be hallucinations
            violations.append(SafetyViolation(
                type="hallucination_overconfident_claim",
                severity=SafetyLevel.MEDIUM,
                message=f"Overly confident claim detected: {match.group(0)}",
                confidence=0.6,
                evidence={"claim": match.group(0)}
            ))
        
        return violations
    
    async def _check_mathematical_validity(self, content: str) -> List[SafetyViolation]:
        """Check mathematical expressions for validity"""
        violations = []
        
        if not self.math_validator:
            return violations
        
        # Extract mathematical expressions
        math_patterns = [
            r"(\d+\s*[+\-*/]\s*\d+\s*=\s*\d+)",
            r"(\w+\s*=\s*\d+)",
            r"(\d+\s*[<>]=?\s*\d+)"
        ]
        
        for pattern in math_patterns:
            matches = re.finditer(pattern, content)
            for match in matches:
                expression = match.group(1)
                if not self.math_validator.validate_expression(expression):
                    violations.append(SafetyViolation(
                        type="hallucination_mathematical_error",
                        severity=SafetyLevel.HIGH,
                        message=f"Mathematical error detected: {expression}",
                        confidence=0.9,
                        evidence={"expression": expression}
                    ))
        
        return violations
    
    def _calculate_safety_score(self, violations: List[SafetyViolation]) -> float:
        """Calculate overall safety score based on violations"""
        if not violations:
            return 1.0
        
        total_penalty = 0.0
        for violation in violations:
            severity_penalty = {
                SafetyLevel.LOW: 0.1,
                SafetyLevel.MEDIUM: 0.2,
                SafetyLevel.HIGH: 0.4,
                SafetyLevel.CRITICAL: 0.8
            }
            penalty = severity_penalty.get(violation.severity, 0.2) * violation.confidence
            total_penalty += penalty
        
        # Ensure score doesn't go below 0
        safety_score = max(0.0, 1.0 - total_penalty)
        return safety_score
    
    def _determine_overall_result(self, violations: List[SafetyViolation]) -> ValidationResult:
        """Determine overall validation result"""
        if not violations:
            return ValidationResult.PASS
        
        # Check for critical violations
        critical_violations = [v for v in violations if v.severity == SafetyLevel.CRITICAL]
        if critical_violations:
            return ValidationResult.BLOCKED
        
        # Check for high severity violations
        high_violations = [v for v in violations if v.severity == SafetyLevel.HIGH]
        if len(high_violations) >= 2:
            return ValidationResult.FAIL
        
        # Check for medium violations
        medium_violations = [v for v in violations if v.severity == SafetyLevel.MEDIUM]
        if len(medium_violations) >= 3:
            return ValidationResult.WARNING
        
        return ValidationResult.WARNING if violations else ValidationResult.PASS
    
    def _update_avg_processing_time(self, new_time: float):
        """Update average processing time"""
        current_avg = self.metrics["avg_processing_time"]
        total_validations = self.metrics["total_validations"]
        
        if total_validations == 1:
            self.metrics["avg_processing_time"] = new_time
        else:
            self.metrics["avg_processing_time"] = (
                (current_avg * (total_validations - 1) + new_time) / total_validations
            )
    
    def get_safety_metrics(self) -> Dict[str, Any]:
        """Get comprehensive safety metrics"""
        total = max(self.metrics["total_validations"], 1)
        return {
            **self.metrics,
            "pass_rate": (self.metrics["passed_validations"] / total) * 100,
            "fail_rate": (self.metrics["failed_validations"] / total) * 100,
            "block_rate": (self.metrics["blocked_content"] / total) * 100,
            "safety_level": self.safety_level.value
        }


class MathematicalValidator:
    """Mathematical expression validator"""
    
    def __init__(self):
        self.logger = logging.getLogger("MathValidator")
    
    def validate_expression(self, expression: str) -> bool:
        """Validate mathematical expression"""
        try:
            if not sympy:
                return True  # Skip validation if sympy not available
            
            # Simple arithmetic validation
            if "=" in expression:
                left, right = expression.split("=", 1)
                left_val = sympify(left.strip())
                right_val = sympify(right.strip())
                return simplify(left_val - right_val) == 0
            
            # Try to parse as valid mathematical expression
            sympify(expression)
            return True
            
        except Exception as e:
            self.logger.debug(f"Mathematical validation failed for '{expression}': {e}")
            return False
