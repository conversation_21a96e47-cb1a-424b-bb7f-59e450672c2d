[project]
name = "vybecoding"
description = "AI education platform transformed to Vybe Method"
root_path = "/home/<USER>/Projects/vybecoding"
version = "1.0.0"

# Enhanced FOSS LLM Configuration - Production Ready
[llm]
provider = "ollama"
model = "qwen3:30b-a3b"
api_url = "http://localhost:11434"
max_tokens = 32000
temperature = 0.7
timeout = 300
retry_attempts = 5
rate_limit = 50
context_length = 200000
gpu_layers = -1
production_mode = true
quality_threshold = 0.95

# Enhanced models for specialized tasks - Production Configuration
[llm.models]
primary_reasoning = "qwen3:30b-a3b"
advanced_coding = "devstral:24b"
content_creation = "llama-3.1-70b"
design_systems = "qwen3:30b-a3b"
quality_assurance = "llama-3.1-70b"
fast_responses = "deepseek-coder-v2:latest"

# Production model configurations
[llm.model_configs]
[llm.model_configs.qwen3_30b_a3b]
context_window = 200000
temperature = 0.7
max_tokens = 32000
specialization = "advanced_reasoning_creativity"

[llm.model_configs.devstral_24b]
context_window = 100000
temperature = 0.3
max_tokens = 16000
specialization = "enterprise_development"

[llm.model_configs.llama_31_70b]
context_window = 128000
temperature = 0.8
max_tokens = 24000
specialization = "premium_content_creation"

[agents]
max_agents = 10
coordination_framework = "crewai"
fallback_frameworks = ["autogen", "langgraph"]

[agents.specialist_agents]
vybe_orchestrator = { role = "coordination", priority = 1, model = "qwen3:30b-a3b" }
vybe_analyst = { role = "analysis", priority = 2, model = "gemma3:latest" }
vybe_architect = { role = "architecture", priority = 3, model = "qwen3:30b-a3b" }
vybe_designer = { role = "design", priority = 4, model = "gemma3:latest" }
vybe_developer = { role = "development", priority = 5, model = "devstral:24b" }
vybe_qa = { role = "quality_assurance", priority = 6, model = "qwen3:30b-a3b" }
vybe_security = { role = "security", priority = 7, model = "deepseek-coder-v2:latest" }

[context_engine]
database_path = "./method/vybe/data/context.db"
max_context_tokens = 1000000
embedding_model = "all-MiniLM-L6-v2"
supported_extensions = [".py", ".js", ".ts", ".jsx", ".tsx", ".md", ".txt", ".json", ".toml", ".yaml", ".yml"]
watch_directories = ["./src", "./docs", "./tests", "./method/vybe", "./story-drafts"]
vector_db_provider = "chroma"
vector_db_path = "./method/vybe/data/vector_db"
file_watch_enabled = true
cache_enabled = true
cache_ttl = 3600

[consensus]
validation_layers = 4
consensus_threshold = 0.75
max_iterations = 3
human_escalation_threshold = 0.5
guardrails_enabled = true
code_quality_threshold = 0.8
test_coverage_threshold = 0.8
syntax_validation = true
semantic_validation = true
security_validation = true
performance_validation = true

[mas_coordinator]
task_queue_size = 100
max_concurrent_tasks = 5
health_check_interval = 30
performance_monitoring = true
load_balancing = "priority"
agent_startup_timeout = 30
coordination_interval = 10

# Security Configuration for Enterprise-Grade Protection
[security]
encryption_enabled = true
local_only = true
anonymize_logs = true
secure_storage = true
require_authentication = false
data_retention_days = 30

# Logging Configuration for Development and Monitoring
[logging]
level = "INFO"
file_path = "./method/vybe/logs/vybe.log"
json_format = true
include_context = true
metrics_enabled = true
performance_tracking = true
error_tracking = true

# API Configuration for Local Development
[api]
host = "localhost"
port = 8000
enable_cors = true
log_level = "INFO"
