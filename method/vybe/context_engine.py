"""
Context Engine - Advanced context management system for Vybe Method
Provides 200K+ token context windows, real-time codebase indexing, and intelligent context sampling
"""

import asyncio
import hashlib
import json
import logging
import pickle
import time
from pathlib import Path
from typing import Dict, List, Optional, Any, Set, Tuple, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import defaultdict, deque
import sqlite3
import threading
from concurrent.futures import ThreadPoolExecutor

# Third-party imports for enhanced functionality
try:
    import tiktoken
    TIKTOKEN_AVAILABLE = True
except ImportError:
    TIKTOKEN_AVAILABLE = False
    logging.warning("tiktoken not available, using fallback token counting")

try:
    from sentence_transformers import SentenceTransformer
    EMBEDDINGS_AVAILABLE = True
except ImportError:
    EMBEDDINGS_AVAILABLE = False
    logging.warning("sentence-transformers not available, semantic search disabled")


@dataclass
class ContextItem:
    """Individual context item with metadata"""
    content: str
    source: str
    timestamp: datetime
    item_type: str
    priority: int = 1
    tokens: int = 0
    embedding: Optional[List[float]] = None
    hash_id: str = field(default="")
    tags: Set[str] = field(default_factory=set)
    references: Set[str] = field(default_factory=set)
    
    def __post_init__(self):
        if not self.hash_id:
            self.hash_id = hashlib.sha256(
                f"{self.content}:{self.source}:{self.item_type}".encode()
            ).hexdigest()[:16]


@dataclass
class ContextWindow:
    """Context window with intelligent management"""
    items: List[ContextItem] = field(default_factory=list)
    max_tokens: int = 200000
    current_tokens: int = 0
    priority_threshold: int = 3
    recency_weight: float = 0.3
    relevance_weight: float = 0.7


class ContextEngine:
    """
    Enterprise-grade context engine with unlimited local context processing
    Implements Augment Code-inspired features with 200K+ token windows
    """
    
    def __init__(self, config_path: Optional[Path] = None):
        self.logger = logging.getLogger(__name__)
        self.config_path = config_path
        self.db_path = Path("data/context_engine.db")
        self.cache_path = Path("temp/context_cache")
        
        # Core settings
        self.max_context_tokens = 200000
        self.chunk_size = 2000
        self.overlap_size = 200
        self.max_cache_items = 50000
        
        # Initialize components
        self.db_connection = None
        self.token_encoder = None
        self.embedding_model = None
        self.context_window = ContextWindow(max_tokens=self.max_context_tokens)
        
        # Thread-safe operations
        self.lock = threading.RLock()
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # Real-time indexing
        self.file_watchers: Dict[str, Any] = {}
        self.index_queue = deque()
        self.last_index_update = time.time()
        
        # Performance metrics
        self.metrics = {
            'total_items': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'index_updates': 0,
            'query_time_ms': deque(maxlen=100)
        }
        
        self._initialize()
    
    def _initialize(self):
        """Initialize all engine components"""
        try:
            self._setup_database()
            self._setup_token_encoder()
            self._setup_embedding_model()
            self._setup_directories()
            self._load_existing_context()
            self.logger.info("ContextEngine initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize ContextEngine: {e}")
            raise
    
    def _setup_database(self):
        """Setup SQLite database for persistent context storage"""
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self.db_connection = sqlite3.connect(str(self.db_path), check_same_thread=False)
        
        cursor = self.db_connection.cursor()
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS context_items (
                hash_id TEXT PRIMARY KEY,
                content TEXT NOT NULL,
                source TEXT NOT NULL,
                timestamp REAL NOT NULL,
                item_type TEXT NOT NULL,
                priority INTEGER DEFAULT 1,
                tokens INTEGER DEFAULT 0,
                embedding BLOB,
                tags TEXT,
                item_references TEXT
            )
        """)
        
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_timestamp ON context_items(timestamp);
        """)
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_source ON context_items(source);
        """)
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_type ON context_items(item_type);
        """)
        
        self.db_connection.commit()
    
    def _setup_token_encoder(self):
        """Setup token encoder for accurate token counting"""
        if TIKTOKEN_AVAILABLE:
            try:
                self.token_encoder = tiktoken.get_encoding("cl100k_base")
                self.logger.info("Using tiktoken for token counting")
            except Exception as e:
                self.logger.warning(f"Failed to load tiktoken: {e}")
                self.token_encoder = None
        else:
            self.token_encoder = None
    
    def _setup_embedding_model(self):
        """Setup embedding model for semantic search"""
        if EMBEDDINGS_AVAILABLE:
            try:
                # Use a lightweight model for faster initialization
                self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
                self.logger.info("Embedding model initialized: all-MiniLM-L6-v2")
            except Exception as e:
                self.logger.error(f"Failed to initialize embedding model: {e}")
                self.embedding_model = None
        else:
            self.embedding_model = None
            self.logger.warning("Sentence transformers not available, semantic search disabled")
    
    def _setup_directories(self):
        """Setup required directories"""
        self.cache_path.mkdir(parents=True, exist_ok=True)
        Path("data").mkdir(exist_ok=True)
        Path("logs").mkdir(exist_ok=True)
    
    def _load_existing_context(self):
        """Load existing context items from database"""
        try:
            cursor = self.db_connection.cursor()
            cursor.execute("""
                SELECT * FROM context_items 
                ORDER BY timestamp DESC 
                LIMIT 1000
            """)
            
            for row in cursor.fetchall():
                item = self._row_to_context_item(row)
                self.context_window.items.append(item)
            
            self._update_context_tokens()
            self.logger.info(f"Loaded {len(self.context_window.items)} context items")
            
        except Exception as e:
            self.logger.error(f"Failed to load existing context: {e}")
    
    def _row_to_context_item(self, row) -> ContextItem:
        """Convert database row to ContextItem"""
        hash_id, content, source, timestamp, item_type, priority, tokens, embedding_blob, tags_str, refs_str = row
        
        # Deserialize embedding
        embedding = None
        if embedding_blob:
            try:
                embedding = pickle.loads(embedding_blob)
            except Exception:
                pass
        
        # Parse tags and references
        tags = set(json.loads(tags_str)) if tags_str else set()
        references = set(json.loads(refs_str)) if refs_str else set()
        
        return ContextItem(
            content=content,
            source=source,
            timestamp=datetime.fromtimestamp(timestamp),
            item_type=item_type,
            priority=priority,
            tokens=tokens,
            embedding=embedding,
            hash_id=hash_id,
            tags=tags,
            references=references
        )
    
    def count_tokens(self, text: str) -> int:
        """Count tokens in text using tiktoken or fallback"""
        if self.token_encoder:
            return len(self.token_encoder.encode(text))
        else:
            # Fallback: rough estimate
            return len(text.split()) * 1.3
    
    def add_context(self, content: str, source: str, item_type: str = "general", 
                   priority: int = 1, tags: Optional[Set[str]] = None) -> bool:
        """Add new context item with real-time processing"""
        start_time = time.time()
        
        try:
            with self.lock:
                # Create context item
                item = ContextItem(
                    content=content,
                    source=source,
                    timestamp=datetime.now(),
                    item_type=item_type,
                    priority=priority,
                    tags=tags or set()
                )
                
                # Count tokens
                item.tokens = int(self.count_tokens(content))
                
                # Generate embedding if available
                if self.embedding_model:
                    try:
                        item.embedding = self.embedding_model.encode(content).tolist()
                    except Exception as e:
                        self.logger.warning(f"Failed to generate embedding: {e}")
                
                # Check if item already exists
                if self._item_exists(item.hash_id):
                    self.logger.debug(f"Context item {item.hash_id} already exists")
                    return False
                
                # Add to context window
                self.context_window.items.append(item)
                self.context_window.current_tokens += item.tokens
                
                # Persist to database
                self._persist_item(item)
                
                # Manage context window size
                self._manage_context_window()
                
                # Update metrics
                self.metrics['total_items'] += 1
                query_time = (time.time() - start_time) * 1000
                self.metrics['query_time_ms'].append(query_time)
                
                self.logger.debug(f"Added context item: {item.hash_id} ({item.tokens} tokens)")
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to add context: {e}")
            return False
    
    def _item_exists(self, hash_id: str) -> bool:
        """Check if context item already exists"""
        cursor = self.db_connection.cursor()
        cursor.execute("SELECT 1 FROM context_items WHERE hash_id = ?", (hash_id,))
        return cursor.fetchone() is not None
    
    def _persist_item(self, item: ContextItem):
        """Persist context item to database"""
        cursor = self.db_connection.cursor()
        
        # Serialize embedding
        embedding_blob = None
        if item.embedding:
            embedding_blob = pickle.dumps(item.embedding)
        
        cursor.execute("""
            INSERT OR REPLACE INTO context_items
            (hash_id, content, source, timestamp, item_type, priority, tokens, embedding, tags, item_references)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            item.hash_id,
            item.content,
            item.source,
            item.timestamp.timestamp(),
            item.item_type,
            item.priority,
            item.tokens,
            embedding_blob,
            json.dumps(list(item.tags)),
            json.dumps(list(item.references))
        ))
        
        self.db_connection.commit()
    
    def _manage_context_window(self):
        """Manage context window size using intelligent pruning"""
        if self.context_window.current_tokens <= self.context_window.max_tokens:
            return
        
        # Sort by priority and recency
        self.context_window.items.sort(key=lambda x: (
            -x.priority,
            -x.timestamp.timestamp()
        ))
        
        # Remove items until under token limit
        while (self.context_window.current_tokens > self.context_window.max_tokens and 
               self.context_window.items):
            removed_item = self.context_window.items.pop()
            self.context_window.current_tokens -= removed_item.tokens
        
        self.logger.debug(f"Context window pruned to {len(self.context_window.items)} items")
    
    def _update_context_tokens(self):
        """Update current token count"""
        self.context_window.current_tokens = sum(
            item.tokens for item in self.context_window.items
        )
    
    def get_context(self, query: str = "", max_tokens: Optional[int] = None, 
                   item_types: Optional[List[str]] = None) -> List[ContextItem]:
        """Get relevant context items with semantic search"""
        start_time = time.time()
        max_tokens = max_tokens or self.max_context_tokens
        
        try:
            with self.lock:
                items = self.context_window.items.copy()
                
                # Filter by item types
                if item_types:
                    items = [item for item in items if item.item_type in item_types]
                
                # Semantic search if query provided and embeddings available
                if query and self.embedding_model:
                    items = self._semantic_search(query, items)
                
                # Select items within token limit
                selected_items = []
                current_tokens = 0
                
                for item in items:
                    if current_tokens + item.tokens <= max_tokens:
                        selected_items.append(item)
                        current_tokens += item.tokens
                    else:
                        break
                
                # Update metrics
                query_time = (time.time() - start_time) * 1000
                self.metrics['query_time_ms'].append(query_time)
                
                self.logger.debug(f"Retrieved {len(selected_items)} context items ({current_tokens} tokens)")
                return selected_items
                
        except Exception as e:
            self.logger.error(f"Failed to get context: {e}")
            return []
    
    def _semantic_search(self, query: str, items: List[ContextItem]) -> List[ContextItem]:
        """Perform semantic search on context items"""
        try:
            if not self.embedding_model:
                return items
            
            # Generate query embedding
            query_embedding = self.embedding_model.encode(query)
            
            # Calculate similarities
            similarities = []
            for item in items:
                if item.embedding:
                    similarity = self._cosine_similarity(query_embedding, item.embedding)
                    similarities.append((similarity, item))
                else:
                    similarities.append((0.0, item))
            
            # Sort by similarity
            similarities.sort(key=lambda x: x[0], reverse=True)
            
            return [item for _, item in similarities]
            
        except Exception as e:
            self.logger.error(f"Semantic search failed: {e}")
            return items
    
    def _cosine_similarity(self, a: List[float], b: List[float]) -> float:
        """Calculate cosine similarity between two vectors"""
        try:
            import numpy as np
            a, b = np.array(a), np.array(b)
            return np.dot(a, b) / (np.linalg.norm(a) * np.linalg.norm(b))
        except ImportError:
            # Fallback without numpy
            dot_product = sum(x * y for x, y in zip(a, b))
            norm_a = sum(x * x for x in a) ** 0.5
            norm_b = sum(x * x for x in b) ** 0.5
            return dot_product / (norm_a * norm_b) if norm_a * norm_b > 0 else 0.0
    
    def clear_context(self):
        """Clear all context items"""
        with self.lock:
            self.context_window.items.clear()
            self.context_window.current_tokens = 0
            
            # Clear database
            cursor = self.db_connection.cursor()
            cursor.execute("DELETE FROM context_items")
            self.db_connection.commit()
            
            self.logger.info("Context cleared")
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get performance and usage metrics"""
        avg_query_time = (
            sum(self.metrics['query_time_ms']) / len(self.metrics['query_time_ms'])
            if self.metrics['query_time_ms'] else 0
        )
        
        return {
            'total_items': self.metrics['total_items'],
            'active_items': len(self.context_window.items),
            'current_tokens': self.context_window.current_tokens,
            'max_tokens': self.context_window.max_tokens,
            'cache_hit_rate': (
                self.metrics['cache_hits'] / 
                (self.metrics['cache_hits'] + self.metrics['cache_misses'])
                if (self.metrics['cache_hits'] + self.metrics['cache_misses']) > 0 else 0
            ),
            'avg_query_time_ms': avg_query_time,
            'index_updates': self.metrics['index_updates'],
            'embeddings_enabled': self.embedding_model is not None,
            'tiktoken_enabled': self.token_encoder is not None
        }
    
    def shutdown(self):
        """Gracefully shutdown the context engine"""
        try:
            if self.db_connection:
                self.db_connection.close()
            
            if self.executor:
                self.executor.shutdown(wait=True)
            
            self.logger.info("ContextEngine shutdown complete")
            
        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")


# Async wrapper for context engine operations
class AsyncContextEngine:
    """Async wrapper for ContextEngine operations"""
    
    def __init__(self, engine: ContextEngine):
        self.engine = engine
    
    async def add_context_async(self, content: str, source: str, **kwargs) -> bool:
        """Async version of add_context"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self.engine.executor, 
            self.engine.add_context, 
            content, source, **kwargs
        )
    
    async def get_context_async(self, query: str = "", **kwargs) -> List[ContextItem]:
        """Async version of get_context"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self.engine.executor,
            self.engine.get_context,
            query, **kwargs
        )