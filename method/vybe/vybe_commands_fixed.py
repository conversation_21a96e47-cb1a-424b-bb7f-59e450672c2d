#!/usr/bin/env python3
"""
Vybe Method Command System
Implements the core /vybe commands with real MAS integration
"""

import asyncio
import json
import logging
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

try:
    from real_mas_coordinator import RealMASCoordinator
    from vector_context_engine import VectorContextEngine
    # Check ChromaDB availability
    try:
        import chromadb
        CHROMADB_AVAILABLE = True
    except ImportError:
        CHROMADB_AVAILABLE = False
    MAS_AVAILABLE = True
except ImportError as e:
    MAS_AVAILABLE = False
    CHROMADB_AVAILABLE = False
    logging.error(f"MAS components not available: {e}")

# Add BMAD-Vybe Bridge import
try:
    sys.path.insert(0, str(Path(__file__).parent.parent))
    from bmad_vybe_bridge import BMADVybeIntegrationBridge
    BRIDGE_AVAILABLE = True
except ImportError as e:
    BRIDGE_AVAILABLE = False
    logging.warning(f"BMAD-Vybe Bridge not available: {e}")

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class VybeCommandSystem:
    """
    Real Vybe Method Command System
    Implements /vybe commands with actual MAS integration
    """
    
    def __init__(self):
        self.initialized = False
        self.mas_coordinator = None
        self.context_engine = None
        self.bridge = None
        self.system_status = {
            'mas_available': MAS_AVAILABLE,
            'vector_db_available': CHROMADB_AVAILABLE,
            'agents_active': 0,
            'last_health_check': None,
            'bridge_available': BRIDGE_AVAILABLE
        }
        
        logger.info("VybeCommandSystem created")
    
    async def vybe_start(self) -> Dict[str, Any]:
        """
        /vybe start → Initialize Vybe Method system
        """
        logger.info("🚀 Initializing Vybe Method system...")
        
        try:
            # Initialize MAS Coordinator
            if MAS_AVAILABLE:
                self.mas_coordinator = RealMASCoordinator()
                self.system_status['agents_active'] = len(self.mas_coordinator.agents)
                logger.info(f"✅ MAS Coordinator initialized with {self.system_status['agents_active']} agents")
            else:
                logger.warning("⚠️ MAS Coordinator not available")
            
            # Initialize Vector Context Engine
            if CHROMADB_AVAILABLE:
                self.context_engine = VectorContextEngine()
                logger.info("✅ Vector Context Engine initialized")
            else:
                logger.warning("⚠️ Vector Context Engine not available")
            
            # Add initial context about the project
            if self.context_engine:
                await self._add_initial_context()
            
            self.initialized = True
            self.system_status['last_health_check'] = datetime.now().isoformat()
            
            result = {
                'status': 'success',
                'message': 'Vybe Method system initialized successfully',
                'components': {
                    'mas_coordinator': self.mas_coordinator is not None,
                    'context_engine': self.context_engine is not None,
                    'agents_available': list(self.mas_coordinator.agents.keys()) if self.mas_coordinator else [],
                    'vector_db_ready': CHROMADB_AVAILABLE
                },
                'timestamp': datetime.now().isoformat()
            }
            
            logger.info("🎉 Vybe Method system ready!")
            return result
            
        except Exception as e:
            error_msg = f"Failed to initialize Vybe Method system: {e}"
            logger.error(f"❌ {error_msg}")
            return {
                'status': 'error',
                'message': error_msg,
                'timestamp': datetime.now().isoformat()
            }
    
    async def vybe_status(self) -> Dict[str, Any]:
        """
        /vybe status → Check system health and agent availability
        """
        logger.info("🔍 Checking Vybe Method system status...")
        
        try:
            status = {
                'system_initialized': self.initialized,
                'timestamp': datetime.now().isoformat(),
                'components': {
                    'mas_coordinator': {
                        'available': self.mas_coordinator is not None,
                        'agents_count': len(self.mas_coordinator.agents) if self.mas_coordinator else 0,
                        'agents_list': list(self.mas_coordinator.agents.keys()) if self.mas_coordinator else [],
                        'metrics': self.mas_coordinator.get_metrics() if self.mas_coordinator else {}
                    },
                    'context_engine': {
                        'available': self.context_engine is not None,
                        'vector_db_ready': CHROMADB_AVAILABLE,
                        'metrics': self.context_engine.get_metrics() if self.context_engine else {}
                    }
                },
                'dependencies': {
                    'chromadb': CHROMADB_AVAILABLE,
                    'mas_framework': MAS_AVAILABLE
                }
            }
            
            # Health check for each agent
            if self.mas_coordinator:
                agent_status = {}
                for agent_id, agent in self.mas_coordinator.agents.items():
                    agent_status[agent_id] = {
                        'role': agent.role,
                        'expertise': agent.expertise,
                        'tasks_completed': agent.tasks_completed,
                        'available': True
                    }
                status['components']['mas_coordinator']['agent_details'] = agent_status
            
            # Determine overall health
            if self.initialized and self.mas_coordinator and self.context_engine:
                status['overall_health'] = 'healthy'
                status['message'] = 'All systems operational'
            elif self.initialized:
                status['overall_health'] = 'partial'
                status['message'] = 'Some components unavailable'
            else:
                status['overall_health'] = 'unhealthy'
                status['message'] = 'System not initialized'
            
            self.system_status['last_health_check'] = datetime.now().isoformat()
            
            logger.info(f"📊 System health: {status['overall_health']}")
            return status
            
        except Exception as e:
            error_msg = f"Failed to check system status: {e}"
            logger.error(f"❌ {error_msg}")
            return {
                'status': 'error',
                'message': error_msg,
                'timestamp': datetime.now().isoformat()
            }
    
    async def vybe_analyze(self, target: Optional[str] = None) -> Dict[str, Any]:
        """
        /vybe analyze → Deep codebase analysis with unlimited context
        """
        logger.info("🔍 Starting deep codebase analysis...")
        
        if not self.initialized:
            return {
                'status': 'error',
                'message': 'Vybe system not initialized. Run /vybe start first.',
                'timestamp': datetime.now().isoformat()
            }
        
        try:
            analysis_id = f"analysis_{int(time.time())}"
            
            # Determine analysis target
            if not target:
                target = "Complete VybeCoding.ai project analysis"
            
            # Use analyst agent for deep analysis
            if self.mas_coordinator:
                logger.info(f"🤖 Executing analysis with specialist agent...")
                
                result = await self.mas_coordinator.execute_task(
                    task_id=analysis_id,
                    description=f"""
                    Perform a comprehensive analysis of the VybeCoding.ai project:
                    
                    Target: {target}
                    
                    Please analyze:
                    1. Current implementation status
                    2. Architecture and design patterns
                    3. Code quality and best practices
                    4. Performance considerations
                    5. Security implications
                    6. Scalability potential
                    7. Educational value and clarity
                    8. Recommendations for improvement
                    
                    Provide specific, actionable insights based on the codebase.
                    """,
                    agent_type="analyst"
                )
                
                if result and result.success:
                    # Store analysis in context engine
                    if self.context_engine:
                        self.context_engine.add_context(
                            content=result.result,
                            source=f"vybe_analysis_{analysis_id}",
                            item_type="analysis",
                            priority=5,
                            tags={"analysis", "codebase", "vybe"}
                        )
                    
                    analysis_result = {
                        'status': 'success',
                        'analysis_id': analysis_id,
                        'target': target,
                        'agent_used': 'analyst',
                        'execution_time': result.execution_time,
                        'analysis': result.result,
                        'timestamp': datetime.now().isoformat(),
                        'context_stored': self.context_engine is not None
                    }
                    
                    logger.info(f"✅ Analysis completed in {result.execution_time:.2f}s")
                    return analysis_result
                else:
                    error_msg = f"Analysis failed: {result.error if result else 'Unknown error'}"
                    logger.error(f"❌ {error_msg}")
                    return {
                        'status': 'error',
                        'message': error_msg,
                        'timestamp': datetime.now().isoformat()
                    }
            else:
                return {
                    'status': 'error',
                    'message': 'MAS Coordinator not available for analysis',
                    'timestamp': datetime.now().isoformat()
                }
                
        except Exception as e:
            error_msg = f"Analysis failed: {e}"
            logger.error(f"❌ {error_msg}")
            return {
                'status': 'error',
                'message': error_msg,
                'timestamp': datetime.now().isoformat()
            }
    
    async def _add_initial_context(self):
        """Add initial context about VybeCoding.ai project"""
        if not self.context_engine:
            return
        
        initial_contexts = [
            {
                'content': 'VybeCoding.ai is an AI-powered education platform teaching the Vybe Method for building profitable applications using AI tools.',
                'source': 'project_overview',
                'item_type': 'documentation',
                'tags': {'project', 'overview', 'education'}
            },
            {
                'content': 'The Vybe Method combines BMAD methodology (Business Analysis, Product Management, Architecture, Design) with Multi-Agent Systems (MAS).',
                'source': 'methodology',
                'item_type': 'methodology',
                'tags': {'vybe', 'bmad', 'mas', 'methodology'}
            },
            {
                'content': 'The platform features autonomous Multi-Agent Systems that generate live revenue-producing websites (Vybe Qubes) as proof of concept demonstrations.',
                'source': 'features',
                'item_type': 'features',
                'tags': {'features', 'vybe-qubes', 'autonomous', 'revenue'}
            },
            {
                'content': 'Technology stack includes SvelteKit frontend, Appwrite backend, ChromaDB vector database, and CrewAI multi-agent framework.',
                'source': 'tech_stack',
                'item_type': 'technical',
                'tags': {'technology', 'sveltekit', 'appwrite', 'chromadb', 'crewai'}
            }
        ]
        
        for context in initial_contexts:
            self.context_engine.add_context(
                content=context['content'],
                source=context['source'],
                item_type=context['item_type'],
                priority=3,
                tags=context['tags']
            )
        
        logger.info("📚 Initial project context added to vector database")

    async def handle_vybe_agent_command(self, agent_name: str, task: str = "") -> Dict[str, Any]:
        """Handle Vybe Method agent commands"""
        # Vybe Method agents (autonomous MAS)
        vybe_agent_map = {
            'vyba': {
                'name': 'VYBA - Vybe Business Analyst',
                'role': 'Business Analysis & Strategy',
                'avatar': '🔮',
                'catchphrase': 'I see patterns in the data that humans miss',
                'type': 'analyst'
            },
            'qubert': {
                'name': 'QUBERT - Qube Product Manager',
                'role': 'Product Requirements & Vision',
                'avatar': '📦',
                'catchphrase': 'Every feature must serve the user\'s vybe',
                'type': 'product_manager'
            },
            'codex': {
                'name': 'CODEX - Code Architect',
                'role': 'Technical Architecture & Design',
                'avatar': '🏗️',
                'catchphrase': 'Architecture is poetry written in logic',
                'type': 'architect'
            },
            'pixy': {
                'name': 'PIXY - Pixel Designer',
                'role': 'UI/UX Design & Experience',
                'avatar': '🎨',
                'catchphrase': 'Beauty and function dance together in perfect harmony',
                'type': 'designer'
            },
            'ducky': {
                'name': 'DUCKY - Quality Guardian',
                'role': 'Quality Assurance & Validation',
                'avatar': '🦆',
                'catchphrase': 'If it\'s not perfect, it\'s not ready for the pond',
                'type': 'quality_assurance'
            },
            'happy': {
                'name': 'HAPPY - Harmony Coordinator',
                'role': 'Scrum Master & Team Coordination',
                'avatar': '😊',
                'catchphrase': 'When the team vibes together, magic happens',
                'type': 'scrum_master'
            },
            'vybro': {
                'name': 'VYBRO - Vybe Developer',
                'role': 'Code Implementation & Execution',
                'avatar': '⚡',
                'catchphrase': 'Code is my canvas, solutions are my art',
                'type': 'developer'
            }
        }

        if agent_name not in vybe_agent_map:
            return {
                'status': 'error',
                'message': f'Unknown Vybe agent: {agent_name}. Available agents: {", ".join(vybe_agent_map.keys())}',
                'timestamp': datetime.now().isoformat()
            }

        if not self.initialized:
            return {
                'status': 'error',
                'message': 'Vybe system not initialized. Run /vybe start first.',
                'timestamp': datetime.now().isoformat()
            }

        agent_info = vybe_agent_map[agent_name]
        logger.info(f"{agent_info['avatar']} Activating {agent_info['name']}...")

        try:
            if self.mas_coordinator:
                task_id = f"{agent_name}_task_{int(time.time())}"

                # Create agent-specific task description
                if not task:
                    task = f"Execute {agent_info['role']} responsibilities for VybeCoding.ai project"

                result = await self.mas_coordinator.execute_task(
                    task_id=task_id,
                    description=f"""
                    {agent_info['catchphrase']}

                    Agent: {agent_info['name']}
                    Role: {agent_info['role']}
                    Task: {task}

                    Please execute this task according to your specialized expertise and the Vybe Method principles.
                    """,
                    agent_type=agent_info['type']
                )

                if result and result.success:
                    return {
                        'status': 'success',
                        'agent': agent_info['name'],
                        'avatar': agent_info['avatar'],
                        'catchphrase': agent_info['catchphrase'],
                        'task_id': task_id,
                        'execution_time': result.execution_time,
                        'result': result.result,
                        'timestamp': datetime.now().isoformat()
                    }
                else:
                    error_msg = f"Agent {agent_name} task failed: {result.error if result else 'Unknown error'}"
                    return {
                        'status': 'error',
                        'message': error_msg,
                        'timestamp': datetime.now().isoformat()
                    }
            else:
                return {
                    'status': 'error',
                    'message': 'MAS Coordinator not available',
                    'timestamp': datetime.now().isoformat()
                }

        except Exception as e:
            error_msg = f"Failed to execute {agent_name} command: {e}"
            logger.error(f"❌ {error_msg}")
            return {
                'status': 'error',
                'message': error_msg,
                'timestamp': datetime.now().isoformat()
            }

    async def vybe_assemble(self) -> Dict[str, Any]:
        """
        /vybe assemble → Activate all 7 Vybe agents for full MAS collaboration
        """
        logger.info("🤖 Assembling all Vybe Method agents...")

        if not self.initialized:
            return {
                'status': 'error',
                'message': 'Vybe system not initialized. Run /vybe start first.',
                'timestamp': datetime.now().isoformat()
            }

        try:
            agents = ['vyba', 'qubert', 'codex', 'pixy', 'ducky', 'happy', 'vybro']
            assembly_results = {}

            for agent in agents:
                result = await self.handle_vybe_agent_command(agent, "Participate in full MAS collaboration")
                assembly_results[agent] = result

            # Check if all agents assembled successfully
            successful_agents = [agent for agent, result in assembly_results.items() if result.get('status') == 'success']

            return {
                'status': 'success' if len(successful_agents) == len(agents) else 'partial',
                'message': f'Assembled {len(successful_agents)}/{len(agents)} Vybe agents',
                'agents_assembled': successful_agents,
                'assembly_results': assembly_results,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            error_msg = f"Failed to assemble agents: {e}"
            logger.error(f"❌ {error_msg}")
            return {
                'status': 'error',
                'message': error_msg,
                'timestamp': datetime.now().isoformat()
            }
    
    async def vybe_from_bmad(self, bmad_data: Optional[Dict] = None) -> Dict[str, Any]:
        """
        /vybe from-bmad → Receive handoff from BMAD Method
        """
        logger.info("🔄 Receiving handoff from BMAD Method...")
        
        if not BRIDGE_AVAILABLE:
            return {
                'status': 'error',
                'message': 'BMAD-Vybe Bridge not available',
                'timestamp': datetime.now().isoformat()
            }
        
        try:
            # Initialize bridge if not already done
            if not self.bridge:
                self.bridge = BMADVybeIntegrationBridge()
            
            # Process BMAD transition data
            if bmad_data:
                transition_result = await self.bridge.bmad_to_vybe_transition(bmad_data)
            else:
                # Load from saved transition data
                transition_result = await self.bridge.load_transition_data()
            
            if not transition_result:
                return {
                    'status': 'error',
                    'message': 'No BMAD transition data found',
                    'timestamp': datetime.now().isoformat()
                }
            
            # Initialize Vybe system if not already done
            if not self.initialized:
                await self.vybe_start()
            
            # Activate mapped agents based on BMAD data
            activated_agents = []
            if 'agent_mapping' in transition_result:
                for bmad_agent, vybe_agent in transition_result['agent_mapping'].items():
                    if self.mas_coordinator and vybe_agent in self.mas_coordinator.agents:
                        activated_agents.append(vybe_agent)
            
            result = {
                'status': 'success',
                'message': 'Successfully received handoff from BMAD Method',
                'transition_data': transition_result,
                'activated_agents': activated_agents,
                'recommendations': [
                    'Review BMAD artifacts and context',
                    'Validate agent mappings',
                    'Begin autonomous workflow execution'
                ],
                'timestamp': datetime.now().isoformat()
            }
            
            logger.info("✅ BMAD handoff received successfully")
            return result
            
        except Exception as e:
            logger.error(f"Error receiving BMAD handoff: {e}")
            return {
                'status': 'error',
                'message': f'Failed to receive BMAD handoff: {str(e)}',
                'timestamp': datetime.now().isoformat()
            }
    
    async def vybe_to_bmad(self, context: Optional[str] = None) -> Dict[str, Any]:
        """
        /vybe to-bmad → Hand off to BMAD Method
        """
        logger.info("🔄 Handing off to BMAD Method...")
        
        if not BRIDGE_AVAILABLE:
            return {
                'status': 'error',
                'message': 'BMAD-Vybe Bridge not available',
                'timestamp': datetime.now().isoformat()
            }
        
        try:
            # Initialize bridge if not already done
            if not self.bridge:
                self.bridge = BMADVybeIntegrationBridge()
            
            # Gather Vybe system state and artifacts
            vybe_data = {
                'system_status': self.system_status,
                'active_agents': list(self.mas_coordinator.agents.keys()) if self.mas_coordinator else [],
                'context': context or "Vybe Method autonomous workflow completion",
                'artifacts': await self._gather_vybe_artifacts(),
                'recommendations': [
                    'Review autonomous workflow results',
                    'Validate agent outputs',
                    'Consider human oversight and refinement'
                ],
                'timestamp': datetime.now().isoformat()
            }
            
            # Execute transition
            transition_result = await self.bridge.vybe_to_bmad_handoff(vybe_data)
            
            result = {
                'status': 'success',
                'message': 'Successfully handed off to BMAD Method',
                'transition_data': transition_result,
                'bmad_agents_mapped': list(transition_result.get('agent_mapping', {}).keys()),
                'next_steps': [
                    'BMAD Method will take over with human-AI collaboration',
                    'Review and refine Vybe autonomous outputs',
                    'Apply human expertise and oversight'
                ],
                'timestamp': datetime.now().isoformat()
            }
            
            logger.info("✅ BMAD handoff completed successfully")
            return result
            
        except Exception as e:
            logger.error(f"Error handing off to BMAD: {e}")
            return {
                'status': 'error',
                'message': f'Failed to hand off to BMAD: {str(e)}',
                'timestamp': datetime.now().isoformat()
            }
    
    async def vybe_hybrid_mode(self, mode: str = "parallel") -> Dict[str, Any]:
        """
        /vybe hybrid → Execute hybrid BMAD-Vybe workflow
        """
        logger.info(f"🔀 Starting hybrid workflow in {mode} mode...")
        
        if not BRIDGE_AVAILABLE:
            return {
                'status': 'error',
                'message': 'BMAD-Vybe Bridge not available',
                'timestamp': datetime.now().isoformat()
            }
        
        try:
            # Initialize bridge if not already done
            if not self.bridge:
                self.bridge = BMADVybeIntegrationBridge()
            
            # Initialize Vybe system if not already done
            if not self.initialized:
                await self.vybe_start()
            
            # Execute hybrid workflow
            hybrid_result = await self.bridge.execute_hybrid_workflow(
                mode=mode,
                vybe_coordinator=self.mas_coordinator
            )
            
            result = {
                'status': 'success',
                'message': f'Hybrid workflow initiated in {mode} mode',
                'workflow_data': hybrid_result,
                'active_mode': mode,
                'integration_status': await self.bridge.get_integration_status(),
                'timestamp': datetime.now().isoformat()
            }
            
            logger.info(f"✅ Hybrid workflow started in {mode} mode")
            return result
            
        except Exception as e:
            logger.error(f"Error starting hybrid workflow: {e}")
            return {
                'status': 'error',
                'message': f'Failed to start hybrid workflow: {str(e)}',
                'timestamp': datetime.now().isoformat()
            }
    
    async def vybe_bridge_status(self) -> Dict[str, Any]:
        """
        /vybe bridge-status → Get BMAD-Vybe integration status
        """
        logger.info("📊 Checking BMAD-Vybe bridge status...")
        
        if not BRIDGE_AVAILABLE:
            return {
                'status': 'error',
                'message': 'BMAD-Vybe Bridge not available',
                'bridge_available': False,
                'timestamp': datetime.now().isoformat()
            }
        
        try:
            # Initialize bridge if not already done
            if not self.bridge:
                self.bridge = BMADVybeIntegrationBridge()
            
            # Get comprehensive status
            bridge_status = await self.bridge.get_integration_status()
            
            result = {
                'status': 'success',
                'bridge_available': True,
                'integration_status': bridge_status,
                'vybe_system_status': self.system_status,
                'bridge_capabilities': [
                    'BMAD to Vybe transitions',
                    'Vybe to BMAD handoffs',
                    'Hybrid workflow execution',
                    'Agent mapping and coordination',
                    'Shared artifact management'
                ],
                'timestamp': datetime.now().isoformat()
            }
            
            logger.info("✅ Bridge status retrieved successfully")
            return result
            
        except Exception as e:
            logger.error(f"Error getting bridge status: {e}")
            return {
                'status': 'error',
                'message': f'Failed to get bridge status: {str(e)}',
                'bridge_available': True,
                'timestamp': datetime.now().isoformat()
            }
    
    async def _gather_vybe_artifacts(self) -> Dict[str, Any]:
        """Helper method to gather Vybe system artifacts for handoff"""
        artifacts = {
            'agent_outputs': {},
            'system_logs': [],
            'context_data': {},
            'generated_content': {}
        }
        
        try:
            # Gather agent outputs
            if self.mas_coordinator:
                for agent_name, agent in self.mas_coordinator.agents.items():
                    if hasattr(agent, 'get_outputs'):
                        artifacts['agent_outputs'][agent_name] = await agent.get_outputs()
            
            # Gather context data
            if self.context_engine:
                artifacts['context_data'] = await self.context_engine.get_all_contexts()
            
            # Add system status
            artifacts['system_status'] = self.system_status
            
        except Exception as e:
            logger.error(f"Error gathering artifacts: {e}")
            artifacts['error'] = str(e)
        
        return artifacts


# Command-line interface
async def main():
    """Command-line interface for Vybe commands"""
    if len(sys.argv) < 2:
        print("Usage: python vybe_commands.py <command> [args]")
        print("Commands:")
        print("  start    - Initialize Vybe Method system")
        print("  status   - Check system health and agent availability")
        print("  analyze  - Deep codebase analysis with unlimited context")
        print("  assemble - Activate all 7 Vybe agents for full MAS collaboration")
        print("  vyba     - Activate VYBA for business analysis")
        print("  qubert   - Switch to QUBERT for product management")
        print("  codex    - Engage CODEX for technical architecture")
        print("  pixy     - Work with PIXY for UI/UX design")
        print("  ducky    - Validate with DUCKY for quality assurance")
        print("  happy    - Coordinate with HAPPY for team harmony")
        print("  vybro    - Implement with VYBRO for development")
        print("")
        print("BMAD-Vybe Bridge:")
        print("  from-bmad     - Receive handoff from BMAD Method")
        print("  to-bmad       - Hand off to BMAD Method")
        print("  hybrid        - Execute hybrid BMAD-Vybe workflow")
        print("  bridge-status - Check bridge integration status")
        return

    command = sys.argv[1].lower()
    vybe = VybeCommandSystem()

    # Core system commands
    if command == "start":
        result = await vybe.vybe_start()
    elif command == "status":
        result = await vybe.vybe_status()
    elif command == "analyze":
        target = sys.argv[2] if len(sys.argv) > 2 else None
        result = await vybe.vybe_analyze(target)
    elif command == "assemble":
        result = await vybe.vybe_assemble()
    # Vybe agent commands
    elif command in ["vyba", "qubert", "codex", "pixy", "ducky", "happy", "vybro"]:
        task = " ".join(sys.argv[2:]) if len(sys.argv) > 2 else ""
        result = await vybe.handle_vybe_agent_command(command, task)
    # Bridge integration commands
    elif command == "from-bmad":
        result = await vybe.vybe_from_bmad()
    elif command == "to-bmad":
        context = " ".join(sys.argv[2:]) if len(sys.argv) > 2 else None
        result = await vybe.vybe_to_bmad(context)
    elif command == "hybrid":
        mode = sys.argv[2] if len(sys.argv) > 2 else "parallel"
        result = await vybe.vybe_hybrid_mode(mode)
    elif command == "bridge-status":
        result = await vybe.vybe_bridge_status()
    else:
        print(f"Unknown command: {command}")
        return

    print(json.dumps(result, indent=2))


if __name__ == "__main__":
    asyncio.run(main())
