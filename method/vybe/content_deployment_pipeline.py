#!/usr/bin/env python3
"""
Content Deployment Pipeline for Vybe Method MAS
Automatically deploys generated content to the correct website locations
"""

import asyncio
import json
import logging
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import uuid
import os


@dataclass
class DeploymentResult:
    """Result of content deployment"""
    content_id: str
    content_type: str
    deployment_path: str
    url_path: str
    status: str  # success, failed, partial
    files_created: List[str]
    timestamp: datetime
    metadata: Dict[str, Any]


class ContentDeploymentPipeline:
    """
    Real content deployment pipeline for Vybe Method MAS
    
    Deploys generated content to:
    - Courses → /courses directory and pages
    - News articles → /community/news directory and pages  
    - Vybe Qubes → /vybeqube directory and live subdomains
    - Documentation → /docs directory and pages
    """
    
    def __init__(self, project_root: Path = None):
        self.logger = logging.getLogger(__name__)
        self.project_root = project_root or Path.cwd()
        
        # Deployment paths
        self.deployment_paths = {
            'course': {
                'content_dir': 'src/routes/courses',
                'static_dir': 'static/courses',
                'url_prefix': '/courses'
            },
            'news_article': {
                'content_dir': 'src/routes/news',
                'static_dir': 'static/news',
                'url_prefix': '/news'
            },
            'vybe_qube': {
                'content_dir': 'src/routes/vybeqube',
                'static_dir': 'static/vybequbes',
                'url_prefix': '/vybeqube'
            },
            'documentation': {
                'content_dir': 'src/routes/docs',
                'static_dir': 'static/docs',
                'url_prefix': '/docs'
            }
        }
        
        # Deployment statistics
        self.stats = {
            'total_deployments': 0,
            'successful_deployments': 0,
            'failed_deployments': 0,
            'deployments_by_type': {},
            'files_created': 0,
            'last_deployment': None
        }
        
        self.logger.info("Content Deployment Pipeline initialized")
    
    async def deploy_content(self, content_data: Dict[str, Any], 
                           content_type: str, agent_contributions: Dict[str, Any] = None) -> DeploymentResult:
        """Deploy generated content to the website"""
        try:
            content_id = str(uuid.uuid4())[:8]
            timestamp = datetime.now()
            
            self.logger.info(f"🚀 Deploying {content_type}: {content_id}")
            
            # Get deployment configuration
            if content_type not in self.deployment_paths:
                raise ValueError(f"Unknown content type: {content_type}")
            
            config = self.deployment_paths[content_type]
            
            # Create deployment directories
            content_dir = self.project_root / config['content_dir']
            static_dir = self.project_root / config['static_dir']
            
            content_dir.mkdir(parents=True, exist_ok=True)
            static_dir.mkdir(parents=True, exist_ok=True)
            
            # Deploy based on content type
            files_created = []
            
            if content_type == 'course':
                files_created = await self._deploy_course(content_data, content_id, content_dir, static_dir)
            elif content_type == 'news_article':
                files_created = await self._deploy_news_article(content_data, content_id, content_dir, static_dir)
            elif content_type == 'vybe_qube':
                files_created = await self._deploy_vybe_qube(content_data, content_id, content_dir, static_dir)
            elif content_type == 'documentation':
                files_created = await self._deploy_documentation(content_data, content_id, content_dir, static_dir)
            
            # Create deployment metadata
            metadata = {
                'content_id': content_id,
                'content_type': content_type,
                'deployed_at': timestamp.isoformat(),
                'agent_contributions': agent_contributions or {},
                'files_created': files_created,
                'deployment_config': config
            }
            
            # Save deployment metadata
            metadata_file = content_dir / f"{content_id}_metadata.json"
            with open(metadata_file, 'w') as f:
                json.dump(metadata, f, indent=2)
            files_created.append(str(metadata_file))
            
            # Update statistics
            self._update_stats(content_type, len(files_created), True)
            
            # Create deployment result
            result = DeploymentResult(
                content_id=content_id,
                content_type=content_type,
                deployment_path=str(content_dir),
                url_path=f"{config['url_prefix']}/{content_id}",
                status='success',
                files_created=files_created,
                timestamp=timestamp,
                metadata=metadata
            )
            
            self.logger.info(f"✅ Successfully deployed {content_type}: {content_id}")
            self.logger.info(f"   Files created: {len(files_created)}")
            self.logger.info(f"   URL: {result.url_path}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"❌ Failed to deploy {content_type}: {e}")
            self._update_stats(content_type, 0, False)
            
            return DeploymentResult(
                content_id=content_id if 'content_id' in locals() else 'unknown',
                content_type=content_type,
                deployment_path='',
                url_path='',
                status='failed',
                files_created=[],
                timestamp=datetime.now(),
                metadata={'error': str(e)}
            )
    
    async def _deploy_course(self, content_data: Dict[str, Any], content_id: str, 
                           content_dir: Path, static_dir: Path) -> List[str]:
        """Deploy course content"""
        files_created = []
        
        # Extract course data
        title = content_data.get('title', f'Course {content_id}')
        description = content_data.get('description', '')
        lessons = content_data.get('lessons', [])
        duration = content_data.get('duration', 60)
        difficulty = content_data.get('difficulty', 'beginner')
        
        # Create course directory
        course_dir = content_dir / content_id
        course_dir.mkdir(exist_ok=True)
        
        # Create main course page
        course_page = f"""<script>
  import {{ onMount }} from 'svelte';
  
  let course = {{
    id: '{content_id}',
    title: '{title}',
    description: '{description}',
    duration: {duration},
    difficulty: '{difficulty}',
    lessons: {json.dumps(lessons, indent=2)},
    createdAt: '{datetime.now().isoformat()}'
  }};
</script>

<svelte:head>
  <title>{{course.title}} - VybeCoding.ai</title>
  <meta name="description" content="{{course.description}}" />
</svelte:head>

<div class="course-container">
  <header class="course-header">
    <h1>{{course.title}}</h1>
    <p class="course-description">{{course.description}}</p>
    <div class="course-meta">
      <span class="duration">Duration: {{course.duration}} minutes</span>
      <span class="difficulty">Difficulty: {{course.difficulty}}</span>
    </div>
  </header>
  
  <div class="lessons-container">
    <h2>Course Lessons</h2>
    {{#each course.lessons as lesson, index}}
      <div class="lesson-card">
        <h3>Lesson {{index + 1}}: {{lesson.title}}</h3>
        <p>{{lesson.description}}</p>
        <div class="lesson-content">
          {{@html lesson.content}}
        </div>
      </div>
    {{/each}}
  </div>
</div>

<style>
  .course-container {{
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
  }}
  
  .course-header {{
    margin-bottom: 2rem;
    text-align: center;
  }}
  
  .course-meta {{
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 1rem;
  }}
  
  .lesson-card {{
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
  }}
  
  .lesson-card h3 {{
    color: #2d3748;
    margin-bottom: 0.5rem;
  }}
</style>"""
        
        course_file = course_dir / '+page.svelte'
        with open(course_file, 'w') as f:
            f.write(course_page)
        files_created.append(str(course_file))
        
        # Create course data file
        course_data_file = static_dir / f"{content_id}.json"
        with open(course_data_file, 'w') as f:
            json.dump(content_data, f, indent=2)
        files_created.append(str(course_data_file))
        
        return files_created
    
    async def _deploy_news_article(self, content_data: Dict[str, Any], content_id: str,
                                 content_dir: Path, static_dir: Path) -> List[str]:
        """Deploy news article content"""
        files_created = []
        
        # Extract article data
        title = content_data.get('title', f'News Article {content_id}')
        content = content_data.get('content', '')
        author = content_data.get('author', 'VybeCoding AI')
        tags = content_data.get('tags', [])
        
        # Create article directory
        article_dir = content_dir / content_id
        article_dir.mkdir(exist_ok=True)
        
        # Create article page
        article_page = f"""<script>
  let article = {{
    id: '{content_id}',
    title: '{title}',
    content: `{content}`,
    author: '{author}',
    tags: {json.dumps(tags)},
    publishedAt: '{datetime.now().isoformat()}'
  }};
</script>

<svelte:head>
  <title>{{article.title}} - VybeCoding.ai News</title>
  <meta name="description" content="{{article.content.substring(0, 160)}}..." />
</svelte:head>

<article class="news-article">
  <header class="article-header">
    <h1>{{article.title}}</h1>
    <div class="article-meta">
      <span class="author">By {{article.author}}</span>
      <time class="published-date">{{new Date(article.publishedAt).toLocaleDateString()}}</time>
    </div>
    {{#if article.tags.length > 0}}
      <div class="tags">
        {{#each article.tags as tag}}
          <span class="tag">{{tag}}</span>
        {{/each}}
      </div>
    {{/if}}
  </header>
  
  <div class="article-content">
    {{@html article.content}}
  </div>
</article>

<style>
  .news-article {{
    max-width: 700px;
    margin: 0 auto;
    padding: 2rem;
  }}
  
  .article-header {{
    margin-bottom: 2rem;
    border-bottom: 1px solid #e2e8f0;
    padding-bottom: 1rem;
  }}
  
  .article-meta {{
    display: flex;
    gap: 1rem;
    margin: 1rem 0;
    color: #718096;
  }}
  
  .tags {{
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
  }}
  
  .tag {{
    background: #edf2f7;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
  }}
  
  .article-content {{
    line-height: 1.6;
  }}
</style>"""
        
        article_file = article_dir / '+page.svelte'
        with open(article_file, 'w') as f:
            f.write(article_page)
        files_created.append(str(article_file))
        
        # Create article data file
        article_data_file = static_dir / f"{content_id}.json"
        with open(article_data_file, 'w') as f:
            json.dump(content_data, f, indent=2)
        files_created.append(str(article_data_file))
        
        return files_created

    async def _deploy_vybe_qube(self, content_data: Dict[str, Any], content_id: str,
                              content_dir: Path, static_dir: Path) -> List[str]:
        """Deploy Vybe Qube content"""
        files_created = []

        # Extract Vybe Qube data
        title = content_data.get('title', f'Vybe Qube {content_id}')
        description = content_data.get('description', '')
        website_content = content_data.get('website_content', {})
        payment_config = content_data.get('payment_config', {})

        # Create Vybe Qube directory
        qube_dir = content_dir / content_id
        qube_dir.mkdir(exist_ok=True)

        # Create main Vybe Qube page
        qube_page = f"""<script>
  let qube = {{
    id: '{content_id}',
    title: '{title}',
    description: '{description}',
    websiteContent: {json.dumps(website_content, indent=2)},
    paymentConfig: {json.dumps(payment_config, indent=2)},
    createdAt: '{datetime.now().isoformat()}'
  }};

  function handlePurchase() {{
    console.log('Processing payment for Vybe Qube:', qube.id);
  }}
</script>

<svelte:head>
  <title>{{qube.title}} - VybeCoding.ai Vybe Qube</title>
  <meta name="description" content="{{qube.description}}" />
</svelte:head>

<div class="vybe-qube">
  <header class="qube-header">
    <h1>{{qube.title}}</h1>
    <p class="qube-description">{{qube.description}}</p>
  </header>

  <div class="qube-content">
    {{#if qube.websiteContent.html}}
      <div class="website-preview">
        <h2>Website Preview</h2>
        <iframe srcdoc="{{qube.websiteContent.html}}" class="preview-frame"></iframe>
      </div>
    {{/if}}

    {{#if qube.paymentConfig.price}}
      <div class="purchase-section">
        <h3>Purchase This Vybe Qube</h3>
        <p class="price">${{qube.paymentConfig.price}}</p>
        <button on:click={{handlePurchase}} class="purchase-btn">
          Buy Now
        </button>
      </div>
    {{/if}}
  </div>
</div>

<style>
  .vybe-qube {{
    max-width: 900px;
    margin: 0 auto;
    padding: 2rem;
  }}

  .qube-header {{
    text-align: center;
    margin-bottom: 2rem;
  }}

  .preview-frame {{
    width: 100%;
    height: 400px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
  }}

  .purchase-section {{
    background: #f7fafc;
    padding: 2rem;
    border-radius: 8px;
    text-align: center;
    margin-top: 2rem;
  }}

  .price {{
    font-size: 2rem;
    font-weight: bold;
    color: #2d3748;
    margin: 1rem 0;
  }}

  .purchase-btn {{
    background: #4299e1;
    color: white;
    padding: 1rem 2rem;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    cursor: pointer;
  }}

  .purchase-btn:hover {{
    background: #3182ce;
  }}
</style>"""

        qube_file = qube_dir / '+page.svelte'
        with open(qube_file, 'w') as f:
            f.write(qube_page)
        files_created.append(str(qube_file))

        # Create Vybe Qube data file
        qube_data_file = static_dir / f"{content_id}.json"
        with open(qube_data_file, 'w') as f:
            json.dump(content_data, f, indent=2)
        files_created.append(str(qube_data_file))

        # Create website files if provided
        if website_content.get('html'):
            website_dir = static_dir / content_id
            website_dir.mkdir(exist_ok=True)

            html_file = website_dir / 'index.html'
            with open(html_file, 'w') as f:
                f.write(website_content['html'])
            files_created.append(str(html_file))

            if website_content.get('css'):
                css_file = website_dir / 'styles.css'
                with open(css_file, 'w') as f:
                    f.write(website_content['css'])
                files_created.append(str(css_file))

            if website_content.get('js'):
                js_file = website_dir / 'script.js'
                with open(js_file, 'w') as f:
                    f.write(website_content['js'])
                files_created.append(str(js_file))

        return files_created

    async def _deploy_documentation(self, content_data: Dict[str, Any], content_id: str,
                                  content_dir: Path, static_dir: Path) -> List[str]:
        """Deploy documentation content"""
        files_created = []

        # Extract documentation data
        title = content_data.get('title', f'Documentation {content_id}')
        content = content_data.get('content', '')
        sections = content_data.get('sections', [])

        # Create documentation directory
        docs_dir = content_dir / content_id
        docs_dir.mkdir(exist_ok=True)

        # Create documentation page
        docs_page = f"""<script>
  let documentation = {{
    id: '{content_id}',
    title: '{title}',
    content: `{content}`,
    sections: {json.dumps(sections, indent=2)},
    updatedAt: '{datetime.now().isoformat()}'
  }};
</script>

<svelte:head>
  <title>{{documentation.title}} - VybeCoding.ai Docs</title>
</svelte:head>

<div class="documentation">
  <main class="docs-content">
    <header class="docs-header">
      <h1>{{documentation.title}}</h1>
    </header>

    <div class="content-body">
      {{@html documentation.content}}
    </div>

    {{#each documentation.sections as section}}
      <section class="docs-section">
        <h2>{{section.title}}</h2>
        <div class="section-content">
          {{@html section.content}}
        </div>
      </section>
    {{/each}}
  </main>
</div>

<style>
  .documentation {{
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
  }}

  .docs-header {{
    margin-bottom: 2rem;
    border-bottom: 1px solid #e2e8f0;
    padding-bottom: 1rem;
  }}

  .docs-section {{
    margin: 2rem 0;
  }}
</style>"""

        docs_file = docs_dir / '+page.svelte'
        with open(docs_file, 'w', encoding='utf-8') as f:
            f.write(docs_page)
        files_created.append(str(docs_file))

        # Create documentation data file
        docs_data_file = static_dir / f"{content_id}.json"
        with open(docs_data_file, 'w', encoding='utf-8') as f:
            json.dump(content_data, f, indent=2)
        files_created.append(str(docs_data_file))

        return files_created

    def _update_stats(self, content_type: str, files_count: int, success: bool):
        """Update deployment statistics"""
        self.stats['total_deployments'] += 1

        if success:
            self.stats['successful_deployments'] += 1
        else:
            self.stats['failed_deployments'] += 1

        if content_type not in self.stats['deployments_by_type']:
            self.stats['deployments_by_type'][content_type] = 0
        self.stats['deployments_by_type'][content_type] += 1

        self.stats['files_created'] += files_count
        self.stats['last_deployment'] = datetime.now().isoformat()

    def get_deployment_stats(self) -> Dict[str, Any]:
        """Get deployment statistics"""
        return self.stats.copy()
