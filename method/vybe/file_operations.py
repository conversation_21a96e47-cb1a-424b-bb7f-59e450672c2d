"""
Agent File Operations Service
Provides safe file system access for autonomous agents
"""

import os
import git
import asyncio
import logging
import shutil
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
from dataclasses import dataclass
from enum import Enum


class FileOperationStatus(Enum):
    """File operation status"""
    SUCCESS = "success"
    ERROR = "error"
    PERMISSION_DENIED = "permission_denied"
    FILE_NOT_FOUND = "file_not_found"
    INVALID_PATH = "invalid_path"


@dataclass
class FileChange:
    """File change definition"""
    type: str  # 'insert', 'replace', 'delete'
    startLine: int
    endLine: Optional[int] = None
    content: Optional[str] = None


@dataclass
class FileOperationResult:
    """Result of file operation"""
    status: FileOperationStatus
    message: str
    path: Optional[str] = None
    backup_path: Optional[str] = None
    timestamp: Optional[datetime] = None


class AgentFileOperations:
    """
    Safe file operations for autonomous agents
    Includes backup, validation, and git integration
    """
    
    def __init__(self, workspace_root: str):
        self.workspace_root = Path(workspace_root).resolve()
        self.logger = logging.getLogger(__name__)
        
        # Initialize git repo
        try:
            self.repo = git.Repo(workspace_root)
        except git.InvalidGitRepositoryError:
            self.logger.warning("Not a git repository, git operations will be disabled")
            self.repo = None
        
        # Create backup directory
        self.backup_dir = self.workspace_root / ".vybe_backups"
        self.backup_dir.mkdir(exist_ok=True)
        
        # Safety settings
        self.max_file_size = 10 * 1024 * 1024  # 10MB
        self.allowed_extensions = {
            '.py', '.js', '.ts', '.jsx', '.tsx', '.md', '.txt', '.json', 
            '.yaml', '.yml', '.toml', '.css', '.scss', '.html', '.svelte'
        }
        self.forbidden_paths = {
            '.git', 'node_modules', '__pycache__', '.venv', 'venv',
            '.env', '.env.local', '.env.production'
        }
        
        self.logger.info(f"AgentFileOperations initialized for {workspace_root}")
    
    async def create_file(self, path: str, content: str) -> FileOperationResult:
        """Create a new file with content"""
        try:
            # Validate path
            validation_result = self._validate_path(path)
            if validation_result.status != FileOperationStatus.SUCCESS:
                return validation_result
            
            full_path = self.workspace_root / path
            
            # Check if file already exists
            if full_path.exists():
                return FileOperationResult(
                    status=FileOperationStatus.ERROR,
                    message=f"File {path} already exists",
                    path=path,
                    timestamp=datetime.now()
                )
            
            # Validate content size
            if len(content.encode('utf-8')) > self.max_file_size:
                return FileOperationResult(
                    status=FileOperationStatus.ERROR,
                    message=f"File content exceeds maximum size ({self.max_file_size} bytes)",
                    path=path,
                    timestamp=datetime.now()
                )
            
            # Create parent directories
            full_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Write file
            full_path.write_text(content, encoding='utf-8')
            
            self.logger.info(f"Created file: {path}")
            
            return FileOperationResult(
                status=FileOperationStatus.SUCCESS,
                message=f"File {path} created successfully",
                path=path,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"Failed to create file {path}: {e}")
            return FileOperationResult(
                status=FileOperationStatus.ERROR,
                message=str(e),
                path=path,
                timestamp=datetime.now()
            )
    
    async def edit_file(self, path: str, changes: List[Dict]) -> FileOperationResult:
        """Apply changes to an existing file"""
        try:
            # Validate path
            validation_result = self._validate_path(path)
            if validation_result.status != FileOperationStatus.SUCCESS:
                return validation_result
            
            full_path = self.workspace_root / path
            
            # Check if file exists
            if not full_path.exists():
                return FileOperationResult(
                    status=FileOperationStatus.FILE_NOT_FOUND,
                    message=f"File {path} not found",
                    path=path,
                    timestamp=datetime.now()
                )
            
            # Create backup
            backup_path = await self._create_backup(full_path)
            
            # Read current content
            original_content = full_path.read_text(encoding='utf-8')
            lines = original_content.splitlines()
            
            # Apply changes in reverse order to maintain line numbers
            sorted_changes = sorted(changes, key=lambda x: x.get('startLine', 0), reverse=True)
            
            for change in sorted_changes:
                change_type = change.get('type')
                start_line = change.get('startLine', 1) - 1  # Convert to 0-based
                end_line = change.get('endLine', start_line + 1) - 1
                content = change.get('content', '')
                
                if change_type == 'replace':
                    lines[start_line:end_line] = content.splitlines()
                elif change_type == 'insert':
                    lines.insert(start_line, content)
                elif change_type == 'delete':
                    del lines[start_line:end_line]
            
            # Write modified content
            new_content = '\n'.join(lines)
            
            # Validate new content size
            if len(new_content.encode('utf-8')) > self.max_file_size:
                # Restore from backup
                await self._restore_from_backup(full_path, backup_path)
                return FileOperationResult(
                    status=FileOperationStatus.ERROR,
                    message=f"Modified file exceeds maximum size ({self.max_file_size} bytes)",
                    path=path,
                    timestamp=datetime.now()
                )
            
            full_path.write_text(new_content, encoding='utf-8')
            
            self.logger.info(f"Edited file: {path}")
            
            return FileOperationResult(
                status=FileOperationStatus.SUCCESS,
                message=f"File {path} edited successfully",
                path=path,
                backup_path=str(backup_path),
                timestamp=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"Failed to edit file {path}: {e}")
            return FileOperationResult(
                status=FileOperationStatus.ERROR,
                message=str(e),
                path=path,
                timestamp=datetime.now()
            )
    
    async def delete_file(self, path: str) -> FileOperationResult:
        """Delete a file (with backup)"""
        try:
            # Validate path
            validation_result = self._validate_path(path)
            if validation_result.status != FileOperationStatus.SUCCESS:
                return validation_result
            
            full_path = self.workspace_root / path
            
            # Check if file exists
            if not full_path.exists():
                return FileOperationResult(
                    status=FileOperationStatus.FILE_NOT_FOUND,
                    message=f"File {path} not found",
                    path=path,
                    timestamp=datetime.now()
                )
            
            # Create backup before deletion
            backup_path = await self._create_backup(full_path)
            
            # Delete file
            full_path.unlink()
            
            self.logger.info(f"Deleted file: {path}")
            
            return FileOperationResult(
                status=FileOperationStatus.SUCCESS,
                message=f"File {path} deleted successfully",
                path=path,
                backup_path=str(backup_path),
                timestamp=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"Failed to delete file {path}: {e}")
            return FileOperationResult(
                status=FileOperationStatus.ERROR,
                message=str(e),
                path=path,
                timestamp=datetime.now()
            )
    
    async def move_file(self, from_path: str, to_path: str) -> FileOperationResult:
        """Move/rename a file"""
        try:
            # Validate both paths
            from_validation = self._validate_path(from_path)
            if from_validation.status != FileOperationStatus.SUCCESS:
                return from_validation
            
            to_validation = self._validate_path(to_path)
            if to_validation.status != FileOperationStatus.SUCCESS:
                return to_validation
            
            full_from_path = self.workspace_root / from_path
            full_to_path = self.workspace_root / to_path
            
            # Check if source file exists
            if not full_from_path.exists():
                return FileOperationResult(
                    status=FileOperationStatus.FILE_NOT_FOUND,
                    message=f"Source file {from_path} not found",
                    path=from_path,
                    timestamp=datetime.now()
                )
            
            # Check if destination already exists
            if full_to_path.exists():
                return FileOperationResult(
                    status=FileOperationStatus.ERROR,
                    message=f"Destination file {to_path} already exists",
                    path=to_path,
                    timestamp=datetime.now()
                )
            
            # Create destination directory if needed
            full_to_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Move file
            shutil.move(str(full_from_path), str(full_to_path))
            
            self.logger.info(f"Moved file: {from_path} -> {to_path}")
            
            return FileOperationResult(
                status=FileOperationStatus.SUCCESS,
                message=f"File moved from {from_path} to {to_path}",
                path=to_path,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"Failed to move file {from_path} -> {to_path}: {e}")
            return FileOperationResult(
                status=FileOperationStatus.ERROR,
                message=str(e),
                path=from_path,
                timestamp=datetime.now()
            )
    
    async def read_directory(self, path: str = "") -> List[str]:
        """Read directory contents"""
        try:
            full_path = self.workspace_root / path if path else self.workspace_root
            
            if not full_path.exists() or not full_path.is_dir():
                return []
            
            contents = []
            for item in full_path.iterdir():
                # Skip forbidden paths
                if item.name in self.forbidden_paths:
                    continue
                
                relative_path = item.relative_to(self.workspace_root)
                contents.append(str(relative_path))
            
            return sorted(contents)
            
        except Exception as e:
            self.logger.error(f"Failed to read directory {path}: {e}")
            return []
    
    async def git_commit(self, message: str, files: Optional[List[str]] = None) -> FileOperationResult:
        """Commit changes to git"""
        try:
            if not self.repo:
                return FileOperationResult(
                    status=FileOperationStatus.ERROR,
                    message="Git repository not available",
                    timestamp=datetime.now()
                )
            
            # Add files to staging
            if files:
                for file_path in files:
                    self.repo.index.add([file_path])
            else:
                self.repo.git.add(A=True)
            
            # Check if there are changes to commit
            if not self.repo.index.diff("HEAD"):
                return FileOperationResult(
                    status=FileOperationStatus.SUCCESS,
                    message="No changes to commit",
                    timestamp=datetime.now()
                )
            
            # Commit changes
            commit = self.repo.index.commit(message)
            
            self.logger.info(f"Git commit: {commit.hexsha[:8]} - {message}")
            
            return FileOperationResult(
                status=FileOperationStatus.SUCCESS,
                message=f"Committed changes: {commit.hexsha[:8]}",
                timestamp=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"Git commit failed: {e}")
            return FileOperationResult(
                status=FileOperationStatus.ERROR,
                message=str(e),
                timestamp=datetime.now()
            )
    
    def _validate_path(self, path: str) -> FileOperationResult:
        """Validate file path for safety"""
        try:
            # Convert to Path object
            path_obj = Path(path)
            
            # Check for path traversal attempts
            if '..' in path_obj.parts:
                return FileOperationResult(
                    status=FileOperationStatus.INVALID_PATH,
                    message="Path traversal not allowed",
                    path=path,
                    timestamp=datetime.now()
                )
            
            # Check for forbidden paths
            for part in path_obj.parts:
                if part in self.forbidden_paths:
                    return FileOperationResult(
                        status=FileOperationStatus.PERMISSION_DENIED,
                        message=f"Access to {part} is forbidden",
                        path=path,
                        timestamp=datetime.now()
                    )
            
            # Check file extension
            if path_obj.suffix and path_obj.suffix not in self.allowed_extensions:
                return FileOperationResult(
                    status=FileOperationStatus.PERMISSION_DENIED,
                    message=f"File extension {path_obj.suffix} not allowed",
                    path=path,
                    timestamp=datetime.now()
                )
            
            return FileOperationResult(
                status=FileOperationStatus.SUCCESS,
                message="Path validation passed",
                path=path,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            return FileOperationResult(
                status=FileOperationStatus.INVALID_PATH,
                message=str(e),
                path=path,
                timestamp=datetime.now()
            )
    
    async def _create_backup(self, file_path: Path) -> Path:
        """Create backup of file"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"{file_path.name}_{timestamp}.backup"
        backup_path = self.backup_dir / backup_name
        
        shutil.copy2(file_path, backup_path)
        return backup_path
    
    async def _restore_from_backup(self, file_path: Path, backup_path: Path):
        """Restore file from backup"""
        shutil.copy2(backup_path, file_path)
