"""
MAS Coordinator - Multi-Agent System coordination and orchestration for Vybe Method
Enterprise-grade agent lifecycle management, task distribution, and collaboration
"""

import asyncio
import json
import logging
import time
from pathlib import Path
from typing import Dict, List, Optional, Any, Set, Tuple, Union, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from collections import defaultdict, deque
import hashlib
import threading
from concurrent.futures import ThreadPoolExecutor
import uuid


class AgentState(Enum):
    """Agent operational states"""
    IDLE = "idle"
    WORKING = "working"
    WAITING = "waiting"
    ERROR = "error"
    OFFLINE = "offline"
    MAINTENANCE = "maintenance"


class TaskPriority(Enum):
    """Task priority levels"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4
    EMERGENCY = 5


class TaskStatus(Enum):
    """Task execution status"""
    PENDING = "pending"
    ASSIGNED = "assigned"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    ESCALATED = "escalated"


class MessageType(Enum):
    """Inter-agent message types"""
    TASK_ASSIGNMENT = "task_assignment"
    STATUS_UPDATE = "status_update"
    COLLABORATION_REQUEST = "collaboration_request"
    RESOURCE_REQUEST = "resource_request"
    ERROR_REPORT = "error_report"
    CONSENSUS_VOTE = "consensus_vote"
    HEARTBEAT = "heartbeat"


@dataclass
class AgentCapability:
    """Agent capability definition"""
    name: str
    description: str
    proficiency: float  # 0.0 to 1.0
    resource_cost: float = 1.0
    dependencies: Set[str] = field(default_factory=set)


@dataclass
class AgentProfile:
    """Comprehensive agent profile"""
    agent_id: str
    agent_type: str
    name: str
    description: str
    capabilities: Dict[str, AgentCapability] = field(default_factory=dict)
    state: AgentState = AgentState.IDLE
    current_load: float = 0.0
    max_load: float = 100.0
    priority: int = 1
    last_heartbeat: Optional[datetime] = None
    created_at: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)
    performance_metrics: Dict[str, Any] = field(default_factory=dict)


@dataclass
class Task:
    """Task definition for agent execution"""
    task_id: str
    title: str
    description: str
    task_type: str
    priority: TaskPriority
    required_capabilities: Set[str]
    estimated_effort: float
    deadline: Optional[datetime] = None
    assigned_agent: Optional[str] = None
    status: TaskStatus = TaskStatus.PENDING
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    result: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    dependencies: Set[str] = field(default_factory=set)
    collaborators: Set[str] = field(default_factory=set)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class Message:
    """Inter-agent communication message"""
    message_id: str
    sender_id: str
    recipient_id: str
    message_type: MessageType
    content: Dict[str, Any]
    timestamp: datetime = field(default_factory=datetime.now)
    priority: int = 1
    requires_response: bool = False
    correlation_id: Optional[str] = None


@dataclass
class ResourceRequest:
    """Resource allocation request"""
    request_id: str
    requesting_agent: str
    resource_type: str
    resource_amount: float
    duration_seconds: int
    priority: int = 1
    approved: bool = False
    allocated_at: Optional[datetime] = None


class MASCoordinator:
    """
    Multi-Agent System Coordinator with enterprise-grade orchestration capabilities
    """
    
    def __init__(self, config_path: Optional[Path] = None):
        self.logger = logging.getLogger(__name__)
        self.config_path = config_path
        
        # Agent management
        self.agents: Dict[str, AgentProfile] = {}
        self.agent_connections: Dict[str, Any] = {}
        self.agent_workloads: Dict[str, float] = {}
        
        # Task management
        self.tasks: Dict[str, Task] = {}
        self.task_queue: deque = deque()
        self.completed_tasks: List[Task] = []
        self.failed_tasks: List[Task] = []
        
        # Communication
        self.message_queue: deque = deque()
        self.message_history: List[Message] = []
        self.active_conversations: Dict[str, List[Message]] = defaultdict(list)
        
        # Resource management
        self.resource_pools: Dict[str, float] = {
            'cpu': 100.0,
            'memory': 100.0,
            'network': 100.0,
            'storage': 100.0
        }
        self.resource_allocations: Dict[str, ResourceRequest] = {}
        
        # Collaboration tracking
        self.active_collaborations: Dict[str, Set[str]] = {}
        self.collaboration_history: List[Dict[str, Any]] = []
        
        # Thread safety and async
        self.lock = threading.RLock()
        self.executor = ThreadPoolExecutor(max_workers=16)
        self.running = False
        self._task_event = asyncio.Event()
        self._message_event = asyncio.Event()
        self._health_event = asyncio.Event()
        self._load_balancer_event = asyncio.Event()
        
        # Performance metrics
        self.metrics = {
            'total_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'avg_task_completion_time': deque(maxlen=100),
            'agent_utilization': defaultdict(float),
            'messages_sent': 0,
            'collaborations_initiated': 0,
            'resource_conflicts': 0,
            'escalations': 0
        }
        
        # Callbacks and hooks
        self.task_callbacks: Dict[str, Callable] = {}
        self.agent_callbacks: Dict[str, Callable] = {}
        self.collaboration_callbacks: List[Callable] = []
        
        self._initialize()
    
    def _initialize(self):
        """Initialize the MAS coordinator"""
        try:
            self._setup_default_callbacks()
            self._start_background_tasks()
            self.logger.info("MASCoordinator initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize MASCoordinator: {e}")
            raise
    
    def _setup_default_callbacks(self):
        """Setup default callbacks"""
        self.task_callbacks["default"] = self._default_task_handler
        self.agent_callbacks["heartbeat"] = self._handle_heartbeat
    
    def _start_background_tasks(self):
        """Start background monitoring and coordination tasks"""
        self.running = True
        
        # Start async background tasks
        asyncio.create_task(self._task_scheduler())
        asyncio.create_task(self._message_processor())
        asyncio.create_task(self._health_monitor())
        asyncio.create_task(self._load_balancer())
    
    def register_agent(self, agent_id: str, agent_type: str, name: str,
                      description: str, capabilities: Dict[str, AgentCapability],
                      max_load: float = 100.0, priority: int = 1,
                      metadata: Optional[Dict[str, Any]] = None) -> bool:
        """Register a new agent with the MAS"""
        try:
            with self.lock:
                if agent_id in self.agents:
                    self.logger.warning(f"Agent {agent_id} already registered")
                    return False
                
                profile = AgentProfile(
                    agent_id=agent_id,
                    agent_type=agent_type,
                    name=name,
                    description=description,
                    capabilities=capabilities,
                    max_load=max_load,
                    priority=priority,
                    metadata=metadata or {},
                    last_heartbeat=datetime.now()
                )
                
                self.agents[agent_id] = profile
                self.agent_workloads[agent_id] = 0.0
            
            self.logger.info(f"Registered agent: {agent_id} ({agent_type})")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to register agent {agent_id}: {e}")
            return False
    
    async def unregister_agent(self, agent_id: str) -> bool:
        """Unregister an agent from the MAS"""
        try:
            with self.lock:
                if agent_id not in self.agents:
                    self.logger.warning(f"Agent {agent_id} not found")
                    return False

                # Update agent state
                self.agents[agent_id].state = AgentState.OFFLINE

                # Clean up resources first
                del self.agents[agent_id]
                if agent_id in self.agent_workloads:
                    del self.agent_workloads[agent_id]
                if agent_id in self.agent_connections:
                    del self.agent_connections[agent_id]

            # Reassign active tasks (outside the lock)
            await self._reassign_agent_tasks(agent_id)

            self.logger.info(f"Unregistered agent: {agent_id}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to unregister agent {agent_id}: {e}")
            return False
    
    async def create_task(self, title: str, description: str, task_type: str,
                         priority: TaskPriority, required_capabilities: Set[str],
                         estimated_effort: float, deadline: Optional[datetime] = None,
                         dependencies: Optional[Set[str]] = None,
                         metadata: Optional[Dict[str, Any]] = None) -> str:
        """Create a new task for agent execution"""
        task_id = str(uuid.uuid4())
        
        task = Task(
            task_id=task_id,
            title=title,
            description=description,
            task_type=task_type,
            priority=priority,
            required_capabilities=required_capabilities,
            estimated_effort=estimated_effort,
            deadline=deadline,
            dependencies=dependencies or set(),
            metadata=metadata or {}
        )
        
        with self.lock:
            self.tasks[task_id] = task
            self.task_queue.append(task_id)
            self.metrics['total_tasks'] += 1
        
        # Trigger task assignment
        await self._schedule_task(task_id)
        
        self.logger.info(f"Created task: {task_id} - {title}")
        return task_id
    
    async def _schedule_task(self, task_id: str) -> bool:
        """Schedule a task for execution"""
        try:
            task = self.tasks[task_id]
            
            # Check dependencies
            if not await self._check_task_dependencies(task):
                self.logger.debug(f"Task {task_id} waiting for dependencies")
                return False
            
            # Find suitable agent
            best_agent = await self._find_best_agent_for_task(task)
            
            if not best_agent:
                self.logger.warning(f"No suitable agent found for task {task_id}")
                return False
            
            # Assign task
            return await self._assign_task_to_agent(task_id, best_agent)
            
        except Exception as e:
            self.logger.error(f"Failed to schedule task {task_id}: {e}")
            return False
    
    async def _check_task_dependencies(self, task: Task) -> bool:
        """Check if all task dependencies are completed"""
        for dep_id in task.dependencies:
            if dep_id in self.tasks:
                dep_task = self.tasks[dep_id]
                if dep_task.status != TaskStatus.COMPLETED:
                    return False
            else:
                # Dependency not found - might be external
                self.logger.warning(f"Dependency {dep_id} not found for task {task.task_id}")
        
        return True
    
    async def _find_best_agent_for_task(self, task: Task) -> Optional[str]:
        """Find the best available agent for a task"""
        suitable_agents = []
        
        with self.lock:
            for agent_id, agent in self.agents.items():
                if agent.state not in [AgentState.IDLE, AgentState.WORKING]:
                    continue
                
                # Check capabilities
                agent_caps = set(agent.capabilities.keys())
                if not task.required_capabilities.issubset(agent_caps):
                    continue
                
                # Check workload
                if self.agent_workloads[agent_id] + task.estimated_effort > agent.max_load:
                    continue
                
                # Calculate suitability score
                capability_score = sum(
                    agent.capabilities[cap].proficiency 
                    for cap in task.required_capabilities
                ) / len(task.required_capabilities)
                
                load_score = 1.0 - (self.agent_workloads[agent_id] / agent.max_load)
                priority_score = agent.priority / 10.0
                
                total_score = capability_score * 0.5 + load_score * 0.3 + priority_score * 0.2
                
                suitable_agents.append((agent_id, total_score))
        
        if not suitable_agents:
            return None
        
        # Sort by score and return best agent
        suitable_agents.sort(key=lambda x: x[1], reverse=True)
        return suitable_agents[0][0]
    
    async def _assign_task_to_agent(self, task_id: str, agent_id: str) -> bool:
        """Assign a task to a specific agent"""
        try:
            with self.lock:
                task = self.tasks[task_id]
                agent = self.agents[agent_id]
                
                # Update task
                task.assigned_agent = agent_id
                task.status = TaskStatus.ASSIGNED
                task.started_at = datetime.now()
                
                # Update agent workload
                self.agent_workloads[agent_id] += task.estimated_effort
                agent.current_load = self.agent_workloads[agent_id]
                
                if agent.state == AgentState.IDLE:
                    agent.state = AgentState.WORKING
            
            # Send assignment message
            await self._send_message(
                sender_id="coordinator",
                recipient_id=agent_id,
                message_type=MessageType.TASK_ASSIGNMENT,
                content={
                    'task_id': task_id,
                    'task': {
                        'title': task.title,
                        'description': task.description,
                        'type': task.task_type,
                        'priority': task.priority.value,
                        'estimated_effort': task.estimated_effort,
                        'deadline': task.deadline.isoformat() if task.deadline else None,
                        'metadata': task.metadata
                    }
                },
                requires_response=True
            )
            
            self.logger.info(f"Assigned task {task_id} to agent {agent_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to assign task {task_id} to agent {agent_id}: {e}")
            return False
    
    async def _send_message(self, sender_id: str, recipient_id: str,
                          message_type: MessageType, content: Dict[str, Any],
                          priority: int = 1, requires_response: bool = False,
                          correlation_id: Optional[str] = None) -> str:
        """Send a message between agents"""
        message_id = str(uuid.uuid4())
        
        message = Message(
            message_id=message_id,
            sender_id=sender_id,
            recipient_id=recipient_id,
            message_type=message_type,
            content=content,
            priority=priority,
            requires_response=requires_response,
            correlation_id=correlation_id
        )
        
        with self.lock:
            self.message_queue.append(message)
            self.message_history.append(message)
            self.metrics['messages_sent'] += 1
        
        self.logger.debug(f"Sent message: {sender_id} -> {recipient_id} ({message_type.value})")
        return message_id
    
    async def update_task_status(self, task_id: str, status: TaskStatus,
                               result: Optional[Dict[str, Any]] = None,
                               error_message: Optional[str] = None) -> bool:
        """Update task status from an agent"""
        try:
            with self.lock:
                if task_id not in self.tasks:
                    self.logger.error(f"Task {task_id} not found")
                    return False
                
                task = self.tasks[task_id]
                old_status = task.status
                task.status = status
                
                if status == TaskStatus.COMPLETED:
                    task.completed_at = datetime.now()
                    task.result = result
                    
                    # Update metrics
                    completion_time = (task.completed_at - task.started_at).total_seconds()
                    self.metrics['avg_task_completion_time'].append(completion_time)
                    self.metrics['completed_tasks'] += 1
                    
                    # Move to completed tasks
                    self.completed_tasks.append(task)
                    
                elif status == TaskStatus.FAILED:
                    task.error_message = error_message
                    self.metrics['failed_tasks'] += 1
                    
                    # Move to failed tasks
                    self.failed_tasks.append(task)
                
                # Update agent workload
                if task.assigned_agent and status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                    agent_id = task.assigned_agent
                    self.agent_workloads[agent_id] -= task.estimated_effort
                    self.agents[agent_id].current_load = self.agent_workloads[agent_id]
                    
                    # Update agent state if no more tasks
                    if self.agent_workloads[agent_id] <= 0:
                        self.agents[agent_id].state = AgentState.IDLE
            
            self.logger.info(f"Task {task_id} status updated: {old_status.value} -> {status.value}")
            
            # Trigger dependent tasks if completed
            if status == TaskStatus.COMPLETED:
                await self._trigger_dependent_tasks(task_id)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to update task status: {e}")
            return False
    
    async def _trigger_dependent_tasks(self, completed_task_id: str):
        """Trigger tasks that depend on the completed task"""
        dependent_tasks = [
            task for task in self.tasks.values()
            if completed_task_id in task.dependencies and task.status == TaskStatus.PENDING
        ]
        
        for task in dependent_tasks:
            await self._schedule_task(task.task_id)
    
    async def request_collaboration(self, requesting_agent: str, target_agents: Set[str],
                                  collaboration_type: str, context: Dict[str, Any]) -> str:
        """Request collaboration between agents"""
        collaboration_id = str(uuid.uuid4())
        
        with self.lock:
            self.active_collaborations[collaboration_id] = {requesting_agent} | target_agents
            self.metrics['collaborations_initiated'] += 1
        
        # Send collaboration requests
        for target_agent in target_agents:
            await self._send_message(
                sender_id=requesting_agent,
                recipient_id=target_agent,
                message_type=MessageType.COLLABORATION_REQUEST,
                content={
                    'collaboration_id': collaboration_id,
                    'type': collaboration_type,
                    'context': context
                },
                requires_response=True
            )
        
        self.logger.info(f"Collaboration {collaboration_id} requested by {requesting_agent}")
        return collaboration_id
    
    async def _task_scheduler(self):
        """Background task scheduler"""
        while self.running:
            try:
                # Process pending tasks
                with self.lock:
                    pending_tasks = [
                        task_id for task_id, task in self.tasks.items()
                        if task.status == TaskStatus.PENDING
                    ]
                
                for task_id in pending_tasks:
                    await self._schedule_task(task_id)
                
                # Use event-driven waiting instead of sleep
                try:
                    await asyncio.wait_for(self._task_event.wait(), timeout=1.0)
                    self._task_event.clear()
                except asyncio.TimeoutError:
                    pass  # Continue normal processing after timeout
                
            except Exception as e:
                self.logger.error(f"Task scheduler error: {e}")
    
    async def _message_processor(self):
        """Background message processor"""
        while self.running:
            try:
                if self.message_queue:
                    with self.lock:
                        message = self.message_queue.popleft()
                    
                    await self._process_message(message)
                
                # Use event-driven waiting for messages
                try:
                    await asyncio.wait_for(self._message_event.wait(), timeout=0.1)
                    self._message_event.clear()
                except asyncio.TimeoutError:
                    pass  # Continue normal processing
                
            except Exception as e:
                self.logger.error(f"Message processor error: {e}")
    
    async def _process_message(self, message: Message):
        """Process an individual message"""
        try:
            # Handle different message types
            if message.message_type == MessageType.STATUS_UPDATE:
                await self._handle_status_update(message)
            elif message.message_type == MessageType.HEARTBEAT:
                await self._handle_heartbeat(message)
            elif message.message_type == MessageType.ERROR_REPORT:
                await self._handle_error_report(message)
            # Add more message type handlers as needed
            
        except Exception as e:
            self.logger.error(f"Failed to process message {message.message_id}: {e}")
    
    async def _handle_status_update(self, message: Message):
        """Handle agent status update messages"""
        agent_id = message.sender_id
        status_data = message.content
        
        if agent_id in self.agents:
            # Update agent metrics
            self.agents[agent_id].performance_metrics.update(status_data.get('metrics', {}))
            self.agents[agent_id].last_heartbeat = datetime.now()
    
    async def _handle_heartbeat(self, message: Message):
        """Handle agent heartbeat messages"""
        agent_id = message.sender_id
        
        if agent_id in self.agents:
            self.agents[agent_id].last_heartbeat = datetime.now()
            
            # Update state if agent was offline
            if self.agents[agent_id].state == AgentState.OFFLINE:
                self.agents[agent_id].state = AgentState.IDLE
    
    async def _handle_error_report(self, message: Message):
        """Handle agent error reports"""
        agent_id = message.sender_id
        error_data = message.content
        
        self.logger.warning(f"Error reported by agent {agent_id}: {error_data}")
        
        # Update agent state
        if agent_id in self.agents:
            self.agents[agent_id].state = AgentState.ERROR
    
    async def _health_monitor(self):
        """Background health monitoring"""
        while self.running:
            try:
                current_time = datetime.now()
                stale_threshold = timedelta(minutes=5)
                
                with self.lock:
                    for agent_id, agent in self.agents.items():
                        if agent.last_heartbeat:
                            time_since_heartbeat = current_time - agent.last_heartbeat
                            
                            if time_since_heartbeat > stale_threshold:
                                if agent.state != AgentState.OFFLINE:
                                    self.logger.warning(f"Agent {agent_id} appears offline")
                                    agent.state = AgentState.OFFLINE
                                    
                                    # Reassign tasks if needed
                                    await self._reassign_agent_tasks(agent_id)
                
                # Use event-driven waiting for health monitoring
                try:
                    await asyncio.wait_for(self._health_event.wait(), timeout=30.0)
                    self._health_event.clear()
                except asyncio.TimeoutError:
                    pass  # Continue normal health monitoring
                
            except Exception as e:
                self.logger.error(f"Health monitor error: {e}")
    
    async def _load_balancer(self):
        """Background load balancing"""
        while self.running:
            try:
                # Check for overloaded agents and redistribute tasks
                with self.lock:
                    overloaded_agents = [
                        agent_id for agent_id, load in self.agent_workloads.items()
                        if load > self.agents[agent_id].max_load * 0.9
                    ]
                
                for agent_id in overloaded_agents:
                    await self._redistribute_agent_load(agent_id)
                
                # Use event-driven waiting for load balancing
                try:
                    await asyncio.wait_for(self._load_balancer_event.wait(), timeout=60.0)
                    self._load_balancer_event.clear()
                except asyncio.TimeoutError:
                    pass  # Continue normal load balancing
                
            except Exception as e:
                self.logger.error(f"Load balancer error: {e}")
    
    async def _reassign_agent_tasks(self, agent_id: str):
        """Reassign tasks from an unavailable agent"""
        tasks_to_reassign = [
            task for task in self.tasks.values()
            if task.assigned_agent == agent_id and task.status in [TaskStatus.ASSIGNED, TaskStatus.IN_PROGRESS]
        ]
        
        for task in tasks_to_reassign:
            task.assigned_agent = None
            task.status = TaskStatus.PENDING
            await self._schedule_task(task.task_id)
        
        self.logger.info(f"Reassigned {len(tasks_to_reassign)} tasks from agent {agent_id}")
    
    async def _redistribute_agent_load(self, overloaded_agent_id: str):
        """Redistribute load from an overloaded agent"""
        # Find tasks that can be moved
        movable_tasks = [
            task for task in self.tasks.values()
            if (task.assigned_agent == overloaded_agent_id and 
                task.status == TaskStatus.ASSIGNED and
                task.priority.value <= TaskPriority.NORMAL.value)
        ]
        
        for task in movable_tasks[:2]:  # Move up to 2 tasks
            task.assigned_agent = None
            task.status = TaskStatus.PENDING
            await self._schedule_task(task.task_id)
        
        if movable_tasks:
            self.logger.info(f"Redistributed {min(2, len(movable_tasks))} tasks from {overloaded_agent_id}")
    
    def _default_task_handler(self, task: Task) -> bool:
        """Default task completion handler"""
        return True
    
    def get_agent_status(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """Get comprehensive agent status"""
        if agent_id not in self.agents:
            return None
        
        agent = self.agents[agent_id]
        
        return {
            'agent_id': agent.agent_id,
            'name': agent.name,
            'type': agent.agent_type,
            'state': agent.state.value,
            'current_load': agent.current_load,
            'max_load': agent.max_load,
            'utilization': agent.current_load / agent.max_load if agent.max_load > 0 else 0,
            'capabilities': list(agent.capabilities.keys()),
            'last_heartbeat': agent.last_heartbeat.isoformat() if agent.last_heartbeat else None,
            'performance_metrics': agent.performance_metrics
        }
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get comprehensive task status"""
        if task_id not in self.tasks:
            return None
        
        task = self.tasks[task_id]
        
        return {
            'task_id': task.task_id,
            'title': task.title,
            'type': task.task_type,
            'status': task.status.value,
            'priority': task.priority.value,
            'assigned_agent': task.assigned_agent,
            'estimated_effort': task.estimated_effort,
            'created_at': task.created_at.isoformat(),
            'started_at': task.started_at.isoformat() if task.started_at else None,
            'completed_at': task.completed_at.isoformat() if task.completed_at else None,
            'dependencies': list(task.dependencies),
            'collaborators': list(task.collaborators)
        }
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get comprehensive system metrics"""
        avg_completion_time = (
            sum(self.metrics['avg_task_completion_time']) / len(self.metrics['avg_task_completion_time'])
            if self.metrics['avg_task_completion_time'] else 0
        )
        
        total_tasks = self.metrics['total_tasks']
        success_rate = (
            self.metrics['completed_tasks'] / total_tasks
            if total_tasks > 0 else 0
        )
        
        return {
            'total_agents': len(self.agents),
            'active_agents': len([a for a in self.agents.values() if a.state not in [AgentState.OFFLINE, AgentState.ERROR]]),
            'total_tasks': total_tasks,
            'pending_tasks': len([t for t in self.tasks.values() if t.status == TaskStatus.PENDING]),
            'active_tasks': len([t for t in self.tasks.values() if t.status in [TaskStatus.ASSIGNED, TaskStatus.IN_PROGRESS]]),
            'completed_tasks': self.metrics['completed_tasks'],
            'failed_tasks': self.metrics['failed_tasks'],
            'success_rate': success_rate,
            'avg_completion_time_seconds': avg_completion_time,
            'messages_sent': self.metrics['messages_sent'],
            'active_collaborations': len(self.active_collaborations),
            'resource_utilization': {
                resource: (100.0 - available) for resource, available in self.resource_pools.items()
            }
        }
    
    def shutdown(self):
        """Gracefully shutdown the MAS coordinator"""
        try:
            self.running = False
            
            if self.executor:
                self.executor.shutdown(wait=True)
            
            self.logger.info("MASCoordinator shutdown complete")
            
        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")