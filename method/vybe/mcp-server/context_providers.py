"""
Context Providers for MCP Server
Handles context retrieval and management for agents
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional, Protocol
from datetime import datetime
from abc import ABC, abstractmethod


class ContextProvider(ABC):
    """Abstract base class for context providers"""
    
    @abstractmethod
    async def get_context(self, query: str, agent_id: str) -> Dict[str, Any]:
        """Get context for a query"""
        pass
    
    @abstractmethod
    async def add_context(self, content: str, metadata: Dict[str, Any]) -> bool:
        """Add new context"""
        pass
    
    @abstractmethod
    async def update_context(self, context_id: str, content: str, metadata: Dict[str, Any]) -> bool:
        """Update existing context"""
        pass
    
    @abstractmethod
    async def delete_context(self, context_id: str) -> bool:
        """Delete context"""
        pass


class VectorContextProvider(ContextProvider):
    """
    Vector-based context provider using ChromaDB
    Integrates with existing VectorContextEngine
    """
    
    def __init__(self, vector_engine=None):
        self.logger = logging.getLogger(__name__)
        self.vector_engine = vector_engine
        
        if not self.vector_engine:
            # Import and initialize vector engine
            try:
                from ..vector_context_engine import VectorContextEngine
                self.vector_engine = VectorContextEngine()
            except ImportError:
                self.logger.error("VectorContextEngine not available")
                self.vector_engine = None
    
    async def get_context(self, query: str, agent_id: str) -> Dict[str, Any]:
        """Get context using vector search"""
        try:
            if not self.vector_engine:
                return {"error": "Vector engine not available"}
            
            # Use existing vector context engine
            context_items = self.vector_engine.get_context(
                query=query,
                max_items=10,
                min_similarity=0.7
            )
            
            # Format for MCP response
            context_data = {
                "query": query,
                "agent_id": agent_id,
                "timestamp": datetime.now().isoformat(),
                "items": []
            }
            
            for item in context_items:
                context_data["items"].append({
                    "content": item.content,
                    "source": item.source,
                    "item_type": item.item_type,
                    "priority": item.priority,
                    "tokens": item.tokens,
                    "tags": list(item.tags),
                    "timestamp": item.timestamp.isoformat()
                })
            
            return context_data
            
        except Exception as e:
            self.logger.error(f"Context retrieval failed: {e}")
            return {"error": str(e)}
    
    async def add_context(self, content: str, metadata: Dict[str, Any]) -> bool:
        """Add new context to vector database"""
        try:
            if not self.vector_engine:
                return False
            
            source = metadata.get('source', 'unknown')
            item_type = metadata.get('item_type', 'general')
            priority = metadata.get('priority', 1)
            tags = set(metadata.get('tags', []))
            
            return self.vector_engine.add_context(
                content=content,
                source=source,
                item_type=item_type,
                priority=priority,
                tags=tags
            )
            
        except Exception as e:
            self.logger.error(f"Failed to add context: {e}")
            return False
    
    async def update_context(self, context_id: str, content: str, metadata: Dict[str, Any]) -> bool:
        """Update existing context (not implemented in base vector engine)"""
        # This would require extending the VectorContextEngine
        self.logger.warning("Context update not implemented for vector provider")
        return False
    
    async def delete_context(self, context_id: str) -> bool:
        """Delete context (not implemented in base vector engine)"""
        # This would require extending the VectorContextEngine
        self.logger.warning("Context deletion not implemented for vector provider")
        return False


class FileContextProvider(ContextProvider):
    """
    File-based context provider
    Provides context from project files
    """
    
    def __init__(self, workspace_root: str):
        self.logger = logging.getLogger(__name__)
        self.workspace_root = workspace_root
        self.file_cache: Dict[str, Dict] = {}
    
    async def get_context(self, query: str, agent_id: str) -> Dict[str, Any]:
        """Get context from project files"""
        try:
            from pathlib import Path
            import os
            
            workspace = Path(self.workspace_root)
            context_data = {
                "query": query,
                "agent_id": agent_id,
                "timestamp": datetime.now().isoformat(),
                "files": []
            }
            
            # Search for relevant files based on query
            relevant_files = await self._find_relevant_files(query, workspace)
            
            for file_path in relevant_files:
                try:
                    content = file_path.read_text(encoding='utf-8')
                    context_data["files"].append({
                        "path": str(file_path.relative_to(workspace)),
                        "content": content[:2000],  # Limit content size
                        "size": len(content),
                        "modified": datetime.fromtimestamp(file_path.stat().st_mtime).isoformat()
                    })
                except Exception as e:
                    self.logger.warning(f"Could not read file {file_path}: {e}")
            
            return context_data
            
        except Exception as e:
            self.logger.error(f"File context retrieval failed: {e}")
            return {"error": str(e)}
    
    async def add_context(self, content: str, metadata: Dict[str, Any]) -> bool:
        """Add context by creating a file"""
        try:
            from pathlib import Path
            
            file_path = metadata.get('file_path')
            if not file_path:
                return False
            
            full_path = Path(self.workspace_root) / file_path
            full_path.parent.mkdir(parents=True, exist_ok=True)
            full_path.write_text(content)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to add file context: {e}")
            return False
    
    async def update_context(self, context_id: str, content: str, metadata: Dict[str, Any]) -> bool:
        """Update file content"""
        try:
            from pathlib import Path
            
            file_path = Path(self.workspace_root) / context_id
            if file_path.exists():
                file_path.write_text(content)
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to update file context: {e}")
            return False
    
    async def delete_context(self, context_id: str) -> bool:
        """Delete file"""
        try:
            from pathlib import Path
            
            file_path = Path(self.workspace_root) / context_id
            if file_path.exists():
                file_path.unlink()
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to delete file context: {e}")
            return False
    
    async def _find_relevant_files(self, query: str, workspace: Path) -> List[Path]:
        """Find files relevant to the query"""
        relevant_files = []
        query_lower = query.lower()
        
        # Define file extensions to search
        search_extensions = {'.py', '.js', '.ts', '.md', '.txt', '.json', '.yaml', '.yml', '.toml'}
        
        try:
            for file_path in workspace.rglob('*'):
                if (file_path.is_file() and 
                    file_path.suffix in search_extensions and
                    not any(part.startswith('.') for part in file_path.parts)):
                    
                    # Check if query matches file name or path
                    if (query_lower in file_path.name.lower() or
                        query_lower in str(file_path).lower()):
                        relevant_files.append(file_path)
                        
                        # Limit number of files
                        if len(relevant_files) >= 10:
                            break
            
        except Exception as e:
            self.logger.error(f"Error searching files: {e}")
        
        return relevant_files


class MemoryContextProvider(ContextProvider):
    """
    In-memory context provider for temporary context
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.memory_store: Dict[str, Dict] = {}
        self.context_counter = 0
    
    async def get_context(self, query: str, agent_id: str) -> Dict[str, Any]:
        """Get context from memory store"""
        try:
            query_lower = query.lower()
            matching_contexts = []
            
            for context_id, context_data in self.memory_store.items():
                content = context_data.get('content', '').lower()
                if query_lower in content or query_lower in context_data.get('tags', []):
                    matching_contexts.append({
                        "id": context_id,
                        "content": context_data['content'],
                        "metadata": context_data.get('metadata', {}),
                        "timestamp": context_data.get('timestamp')
                    })
            
            return {
                "query": query,
                "agent_id": agent_id,
                "timestamp": datetime.now().isoformat(),
                "contexts": matching_contexts
            }
            
        except Exception as e:
            self.logger.error(f"Memory context retrieval failed: {e}")
            return {"error": str(e)}
    
    async def add_context(self, content: str, metadata: Dict[str, Any]) -> bool:
        """Add context to memory store"""
        try:
            self.context_counter += 1
            context_id = f"mem_{self.context_counter}"
            
            self.memory_store[context_id] = {
                "content": content,
                "metadata": metadata,
                "timestamp": datetime.now().isoformat()
            }
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to add memory context: {e}")
            return False
    
    async def update_context(self, context_id: str, content: str, metadata: Dict[str, Any]) -> bool:
        """Update memory context"""
        try:
            if context_id in self.memory_store:
                self.memory_store[context_id].update({
                    "content": content,
                    "metadata": metadata,
                    "timestamp": datetime.now().isoformat()
                })
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to update memory context: {e}")
            return False
    
    async def delete_context(self, context_id: str) -> bool:
        """Delete memory context"""
        try:
            if context_id in self.memory_store:
                del self.memory_store[context_id]
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to delete memory context: {e}")
            return False
