"""
Tool Registry and Management for MCP Server
Handles tool registration, validation, and execution
"""

import asyncio
import logging
from typing import Dict, Any, Callable, Optional, List, Union
from datetime import datetime
from dataclasses import dataclass
from enum import Enum


class ToolStatus(Enum):
    """Tool execution status"""
    SUCCESS = "success"
    ERROR = "error"
    TIMEOUT = "timeout"
    PERMISSION_DENIED = "permission_denied"


@dataclass
class ToolResult:
    """Result of tool execution"""
    status: ToolStatus
    result: Optional[Any] = None
    error: Optional[str] = None
    execution_time: Optional[float] = None
    timestamp: Optional[datetime] = None


@dataclass
class AgentTool:
    """Agent tool definition"""
    name: str
    description: str
    parameters: Dict[str, Any]
    handler: Callable
    timeout: float = 30.0
    requires_permission: bool = True
    category: str = "general"


class ToolRegistry:
    """
    Registry for managing agent tools
    Handles tool registration, validation, and execution
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.tools: Dict[str, AgentTool] = {}
        self.execution_history: List[Dict] = []
        self.permissions: Dict[str, List[str]] = {}  # agent_id -> tool_names
        
        # Metrics
        self.metrics = {
            'total_executions': 0,
            'successful_executions': 0,
            'failed_executions': 0,
            'avg_execution_time': 0.0
        }
    
    async def register_tool(self, tool: AgentTool) -> bool:
        """Register a new tool"""
        try:
            # Validate tool
            if not await self._validate_tool(tool):
                return False
            
            self.tools[tool.name] = tool
            self.logger.info(f"Registered tool: {tool.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to register tool {tool.name}: {e}")
            return False
    
    async def execute_tool(self, tool_name: str, agent_id: str, params: Dict[str, Any]) -> ToolResult:
        """Execute a tool for an agent"""
        start_time = datetime.now()
        
        try:
            # Check if tool exists
            if tool_name not in self.tools:
                return ToolResult(
                    status=ToolStatus.ERROR,
                    error=f"Tool {tool_name} not found",
                    timestamp=start_time
                )
            
            tool = self.tools[tool_name]
            
            # Check permissions
            if tool.requires_permission and not self._has_permission(agent_id, tool_name):
                return ToolResult(
                    status=ToolStatus.PERMISSION_DENIED,
                    error=f"Agent {agent_id} does not have permission to use {tool_name}",
                    timestamp=start_time
                )
            
            # Validate parameters
            validation_error = self._validate_parameters(tool, params)
            if validation_error:
                return ToolResult(
                    status=ToolStatus.ERROR,
                    error=validation_error,
                    timestamp=start_time
                )
            
            # Execute tool with timeout
            try:
                result = await asyncio.wait_for(
                    tool.handler(**params),
                    timeout=tool.timeout
                )
                
                execution_time = (datetime.now() - start_time).total_seconds()
                
                # Update metrics
                self.metrics['total_executions'] += 1
                self.metrics['successful_executions'] += 1
                self._update_avg_execution_time(execution_time)
                
                # Log execution
                self.execution_history.append({
                    'tool_name': tool_name,
                    'agent_id': agent_id,
                    'params': params,
                    'result': result,
                    'execution_time': execution_time,
                    'timestamp': start_time.isoformat(),
                    'status': 'success'
                })
                
                return ToolResult(
                    status=ToolStatus.SUCCESS,
                    result=result,
                    execution_time=execution_time,
                    timestamp=start_time
                )
                
            except asyncio.TimeoutError:
                self.metrics['total_executions'] += 1
                self.metrics['failed_executions'] += 1
                
                return ToolResult(
                    status=ToolStatus.TIMEOUT,
                    error=f"Tool execution timed out after {tool.timeout}s",
                    timestamp=start_time
                )
            
        except Exception as e:
            self.metrics['total_executions'] += 1
            self.metrics['failed_executions'] += 1
            
            self.logger.error(f"Tool execution failed: {e}")
            
            return ToolResult(
                status=ToolStatus.ERROR,
                error=str(e),
                timestamp=start_time
            )
    
    async def grant_permission(self, agent_id: str, tool_names: Union[str, List[str]]):
        """Grant tool permissions to an agent"""
        if isinstance(tool_names, str):
            tool_names = [tool_names]
        
        if agent_id not in self.permissions:
            self.permissions[agent_id] = []
        
        for tool_name in tool_names:
            if tool_name in self.tools and tool_name not in self.permissions[agent_id]:
                self.permissions[agent_id].append(tool_name)
        
        self.logger.info(f"Granted permissions to {agent_id}: {tool_names}")
    
    async def revoke_permission(self, agent_id: str, tool_names: Union[str, List[str]]):
        """Revoke tool permissions from an agent"""
        if isinstance(tool_names, str):
            tool_names = [tool_names]
        
        if agent_id in self.permissions:
            for tool_name in tool_names:
                if tool_name in self.permissions[agent_id]:
                    self.permissions[agent_id].remove(tool_name)
        
        self.logger.info(f"Revoked permissions from {agent_id}: {tool_names}")
    
    def _has_permission(self, agent_id: str, tool_name: str) -> bool:
        """Check if agent has permission to use tool"""
        if agent_id not in self.permissions:
            return False
        return tool_name in self.permissions[agent_id]
    
    async def _validate_tool(self, tool: AgentTool) -> bool:
        """Validate tool definition"""
        if not tool.name or not tool.description:
            self.logger.error("Tool name and description are required")
            return False
        
        if not callable(tool.handler):
            self.logger.error("Tool handler must be callable")
            return False
        
        if tool.name in self.tools:
            self.logger.warning(f"Tool {tool.name} already exists, will be overwritten")
        
        return True
    
    def _validate_parameters(self, tool: AgentTool, params: Dict[str, Any]) -> Optional[str]:
        """Validate tool parameters"""
        try:
            # Check required parameters
            for param_name, param_def in tool.parameters.items():
                if param_def.get('required', True) and param_name not in params:
                    return f"Required parameter '{param_name}' is missing"
            
            # Check parameter types (basic validation)
            for param_name, value in params.items():
                if param_name in tool.parameters:
                    param_def = tool.parameters[param_name]
                    expected_type = param_def.get('type')
                    
                    if expected_type == 'string' and not isinstance(value, str):
                        return f"Parameter '{param_name}' must be a string"
                    elif expected_type == 'integer' and not isinstance(value, int):
                        return f"Parameter '{param_name}' must be an integer"
                    elif expected_type == 'boolean' and not isinstance(value, bool):
                        return f"Parameter '{param_name}' must be a boolean"
                    elif expected_type == 'array' and not isinstance(value, list):
                        return f"Parameter '{param_name}' must be an array"
            
            return None
            
        except Exception as e:
            return f"Parameter validation error: {e}"
    
    def _update_avg_execution_time(self, execution_time: float):
        """Update average execution time metric"""
        current_avg = self.metrics['avg_execution_time']
        total_executions = self.metrics['total_executions']
        
        if total_executions == 1:
            self.metrics['avg_execution_time'] = execution_time
        else:
            # Calculate running average
            self.metrics['avg_execution_time'] = (
                (current_avg * (total_executions - 1) + execution_time) / total_executions
            )
    
    def get_tool_list(self) -> List[Dict[str, Any]]:
        """Get list of available tools"""
        return [
            {
                'name': tool.name,
                'description': tool.description,
                'parameters': tool.parameters,
                'category': tool.category,
                'timeout': tool.timeout
            }
            for tool in self.tools.values()
        ]
    
    def get_agent_permissions(self, agent_id: str) -> List[str]:
        """Get tool permissions for an agent"""
        return self.permissions.get(agent_id, [])
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get tool registry metrics"""
        return self.metrics.copy()
    
    def get_execution_history(self, limit: int = 100) -> List[Dict]:
        """Get recent tool execution history"""
        return self.execution_history[-limit:]
    
    async def shutdown(self):
        """Shutdown tool registry"""
        try:
            # Clear execution history
            self.execution_history.clear()
            
            # Clear permissions
            self.permissions.clear()
            
            self.logger.info("Tool registry shutdown complete")
            
        except Exception as e:
            self.logger.error(f"Error during tool registry shutdown: {e}")
