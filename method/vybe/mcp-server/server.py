"""
MCP Server Implementation for Vybe Method MAS
Handles tool registration, execution, and context management
"""

import asyncio
import json
import logging
from typing import Dict, Any, Callable, Optional, List
from datetime import datetime
from dataclasses import dataclass, asdict
from pathlib import Path

from .tools import Too<PERSON><PERSON>eg<PERSON>ry, AgentTool, Tool<PERSON><PERSON>ult
from .context_providers import Context<PERSON>rovider


@dataclass
class MCPRequest:
    """MCP request structure"""
    id: str
    method: str
    params: Dict[str, Any]
    agent_id: str
    timestamp: datetime


@dataclass
class MCPResponse:
    """MCP response structure"""
    id: str
    result: Optional[Any] = None
    error: Optional[str] = None
    timestamp: Optional[datetime] = None


class MCPServer:
    """
    Model Context Protocol Server for Vybe Method MAS
    Provides standardized interface for agent tool calling and context sharing
    """
    
    def __init__(self, config_path: Optional[Path] = None):
        self.logger = logging.getLogger(__name__)
        self.tool_registry = ToolRegistry()
        self.context_providers: Dict[str, ContextProvider] = {}
        self.active_sessions: Dict[str, Dict] = {}
        self.request_history: List[MCPRequest] = []
        
        # Performance metrics
        self.metrics = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'avg_response_time': 0.0,
            'active_agents': 0,
            'tools_registered': 0
        }
        
        self.logger.info("MCP Server initialized")
    
    async def start(self, host: str = "localhost", port: int = 8765):
        """Start the MCP server"""
        try:
            # Register default tools
            await self._register_default_tools()
            
            self.logger.info(f"MCP Server started on {host}:{port}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start MCP server: {e}")
            return False
    
    async def register_tool(self, tool: AgentTool) -> bool:
        """Register a new tool for agent use"""
        try:
            success = await self.tool_registry.register_tool(tool)
            if success:
                self.metrics['tools_registered'] += 1
                self.logger.info(f"Registered tool: {tool.name}")
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to register tool {tool.name}: {e}")
            return False
    
    async def register_context_provider(self, name: str, provider: ContextProvider) -> bool:
        """Register a context provider"""
        try:
            self.context_providers[name] = provider
            self.logger.info(f"Registered context provider: {name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to register context provider {name}: {e}")
            return False
    
    async def handle_request(self, request: MCPRequest) -> MCPResponse:
        """Handle incoming MCP request"""
        start_time = datetime.now()
        
        try:
            self.metrics['total_requests'] += 1
            self.request_history.append(request)
            
            # Route request based on method
            if request.method == "tools/call":
                result = await self._handle_tool_call(request)
            elif request.method == "context/get":
                result = await self._handle_context_request(request)
            elif request.method == "session/create":
                result = await self._handle_session_create(request)
            elif request.method == "session/destroy":
                result = await self._handle_session_destroy(request)
            else:
                raise ValueError(f"Unknown method: {request.method}")
            
            self.metrics['successful_requests'] += 1
            
            # Calculate response time
            response_time = (datetime.now() - start_time).total_seconds()
            self._update_avg_response_time(response_time)
            
            return MCPResponse(
                id=request.id,
                result=result,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            self.metrics['failed_requests'] += 1
            self.logger.error(f"Request {request.id} failed: {e}")
            
            return MCPResponse(
                id=request.id,
                error=str(e),
                timestamp=datetime.now()
            )
    
    async def _handle_tool_call(self, request: MCPRequest) -> Any:
        """Handle tool call request"""
        params = request.params
        tool_name = params.get('tool_name')
        tool_params = params.get('params', {})
        
        if not tool_name:
            raise ValueError("tool_name is required for tool calls")
        
        # Execute tool
        result = await self.tool_registry.execute_tool(
            tool_name, 
            request.agent_id, 
            tool_params
        )
        
        return asdict(result)
    
    async def _handle_context_request(self, request: MCPRequest) -> Any:
        """Handle context request"""
        params = request.params
        provider_name = params.get('provider')
        query = params.get('query', '')
        
        if provider_name not in self.context_providers:
            raise ValueError(f"Context provider {provider_name} not found")
        
        provider = self.context_providers[provider_name]
        context = await provider.get_context(query, request.agent_id)
        
        return context
    
    async def _handle_session_create(self, request: MCPRequest) -> Any:
        """Handle session creation"""
        agent_id = request.agent_id
        session_config = request.params.get('config', {})
        
        self.active_sessions[agent_id] = {
            'created_at': datetime.now(),
            'config': session_config,
            'tool_calls': 0,
            'context_requests': 0
        }
        
        self.metrics['active_agents'] += 1
        
        return {'session_id': agent_id, 'status': 'created'}
    
    async def _handle_session_destroy(self, request: MCPRequest) -> Any:
        """Handle session destruction"""
        agent_id = request.agent_id
        
        if agent_id in self.active_sessions:
            del self.active_sessions[agent_id]
            self.metrics['active_agents'] -= 1
        
        return {'session_id': agent_id, 'status': 'destroyed'}
    
    async def _register_default_tools(self):
        """Register default tools for all agents"""
        from ..file_operations import AgentFileOperations
        from ..web_search import WebSearchAgent
        
        # File operations tools
        file_ops = AgentFileOperations("/home/<USER>/Projects/vybecoding")
        
        create_file_tool = AgentTool(
            name="create_file",
            description="Create a new file with content",
            parameters={
                "path": {"type": "string", "description": "File path relative to workspace"},
                "content": {"type": "string", "description": "File content"}
            },
            handler=file_ops.create_file
        )
        
        edit_file_tool = AgentTool(
            name="edit_file", 
            description="Edit an existing file",
            parameters={
                "path": {"type": "string", "description": "File path relative to workspace"},
                "changes": {"type": "array", "description": "List of changes to apply"}
            },
            handler=file_ops.edit_file
        )
        
        # Web search tools
        web_search = WebSearchAgent()
        
        search_tool = AgentTool(
            name="web_search",
            description="Search the web for current information",
            parameters={
                "query": {"type": "string", "description": "Search query"},
                "num_results": {"type": "integer", "description": "Number of results", "default": 5}
            },
            handler=web_search.search
        )
        
        # Register tools
        await self.register_tool(create_file_tool)
        await self.register_tool(edit_file_tool)
        await self.register_tool(search_tool)
    
    def _update_avg_response_time(self, response_time: float):
        """Update average response time metric"""
        current_avg = self.metrics['avg_response_time']
        total_requests = self.metrics['total_requests']
        
        if total_requests == 1:
            self.metrics['avg_response_time'] = response_time
        else:
            # Calculate running average
            self.metrics['avg_response_time'] = (
                (current_avg * (total_requests - 1) + response_time) / total_requests
            )
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get server metrics"""
        return self.metrics.copy()
    
    def get_active_tools(self) -> List[str]:
        """Get list of active tools"""
        return list(self.tool_registry.tools.keys())
    
    def get_active_sessions(self) -> Dict[str, Any]:
        """Get active agent sessions"""
        return self.active_sessions.copy()
    
    async def shutdown(self):
        """Gracefully shutdown the MCP server"""
        try:
            # Clear active sessions
            self.active_sessions.clear()
            
            # Shutdown tool registry
            await self.tool_registry.shutdown()
            
            self.logger.info("MCP Server shutdown complete")
            
        except Exception as e:
            self.logger.error(f"Error during MCP server shutdown: {e}")
