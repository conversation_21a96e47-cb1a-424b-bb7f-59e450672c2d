"""
Real Agent Communication System
Enables true inter-agent messaging with context persistence
"""

import asyncio
import json
import logging
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime
from dataclasses import dataclass, asdict
from enum import Enum
import uuid


class MessageType(Enum):
    """Types of inter-agent messages"""
    TASK_REQUEST = "task_request"
    TASK_RESPONSE = "task_response"
    COLLABORATION_REQUEST = "collaboration_request"
    COLLABORATION_RESPONSE = "collaboration_response"
    CONTEXT_SHARE = "context_share"
    STATUS_UPDATE = "status_update"
    ERROR_REPORT = "error_report"
    BROADCAST = "broadcast"


class MessagePriority(Enum):
    """Message priority levels"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


@dataclass
class AgentMessage:
    """Inter-agent message structure"""
    id: str
    from_agent: str
    to_agent: str
    message_type: MessageType
    content: Dict[str, Any]
    priority: MessagePriority
    timestamp: datetime
    requires_response: bool = False
    correlation_id: Optional[str] = None
    response_timeout: Optional[float] = None


@dataclass
class AgentContext:
    """Agent context for communication"""
    agent_id: str
    current_task: Optional[str] = None
    status: str = "ready"
    capabilities: List[str] = None
    last_activity: Optional[datetime] = None
    context_data: Dict[str, Any] = None


class RealAgentCommunication:
    """
    Real agent-to-agent communication system
    Provides persistent messaging, context sharing, and collaboration
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Message queues for each agent
        self.message_queues: Dict[str, asyncio.Queue] = {}
        
        # Agent contexts
        self.agent_contexts: Dict[str, AgentContext] = {}
        
        # Message history and persistence
        self.message_history: List[AgentMessage] = []
        self.pending_responses: Dict[str, AgentMessage] = {}
        
        # Event handlers
        self.message_handlers: Dict[str, List[Callable]] = {}
        
        # Collaboration tracking
        self.active_collaborations: Dict[str, Dict] = {}
        
        # Performance metrics
        self.metrics = {
            'total_messages': 0,
            'successful_deliveries': 0,
            'failed_deliveries': 0,
            'active_agents': 0,
            'active_collaborations': 0,
            'avg_response_time': 0.0
        }
        
        self.logger.info("RealAgentCommunication initialized")
    
    async def register_agent(self, agent_id: str, capabilities: List[str] = None) -> bool:
        """Register an agent for communication"""
        try:
            # Create message queue
            self.message_queues[agent_id] = asyncio.Queue()
            
            # Initialize context
            self.agent_contexts[agent_id] = AgentContext(
                agent_id=agent_id,
                capabilities=capabilities or [],
                last_activity=datetime.now(),
                context_data={}
            )
            
            self.metrics['active_agents'] += 1
            self.logger.info(f"Registered agent: {agent_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to register agent {agent_id}: {e}")
            return False
    
    async def unregister_agent(self, agent_id: str) -> bool:
        """Unregister an agent"""
        try:
            # Remove message queue
            if agent_id in self.message_queues:
                del self.message_queues[agent_id]
            
            # Remove context
            if agent_id in self.agent_contexts:
                del self.agent_contexts[agent_id]
            
            self.metrics['active_agents'] -= 1
            self.logger.info(f"Unregistered agent: {agent_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to unregister agent {agent_id}: {e}")
            return False
    
    async def send_message(self, from_agent: str, to_agent: str, 
                          message_type: MessageType, content: Dict[str, Any],
                          priority: MessagePriority = MessagePriority.NORMAL,
                          requires_response: bool = False,
                          response_timeout: Optional[float] = None,
                          correlation_id: Optional[str] = None) -> str:
        """Send message between agents"""
        try:
            # Generate message ID
            message_id = str(uuid.uuid4())
            
            # Create message
            message = AgentMessage(
                id=message_id,
                from_agent=from_agent,
                to_agent=to_agent,
                message_type=message_type,
                content=content,
                priority=priority,
                timestamp=datetime.now(),
                requires_response=requires_response,
                correlation_id=correlation_id,
                response_timeout=response_timeout
            )
            
            # Validate agents exist
            if to_agent not in self.message_queues:
                self.logger.error(f"Target agent {to_agent} not registered")
                self.metrics['failed_deliveries'] += 1
                return ""
            
            # Add to target agent's queue
            await self.message_queues[to_agent].put(message)
            
            # Store in history
            self.message_history.append(message)
            
            # Track pending response if required
            if requires_response:
                self.pending_responses[message_id] = message
            
            # Update metrics
            self.metrics['total_messages'] += 1
            self.metrics['successful_deliveries'] += 1
            
            # Update agent activity
            if from_agent in self.agent_contexts:
                self.agent_contexts[from_agent].last_activity = datetime.now()

            # Broadcast to WebSocket clients for real-time monitoring
            try:
                from .websocket_server import broadcast_agent_message
                broadcast_agent_message({
                    "type": "agent_conversation",
                    "agent": from_agent,
                    "data": {
                        "conversation": {
                            "from": from_agent,
                            "to": to_agent,
                            "message_type": message_type.value,
                            "content": content,
                            "timestamp": message.timestamp.isoformat()
                        }
                    }
                })
            except Exception as e:
                self.logger.debug(f"Failed to broadcast message: {e}")

            self.logger.debug(f"Message sent: {from_agent} -> {to_agent} ({message_type.value})")
            return message_id
            
        except Exception as e:
            self.logger.error(f"Failed to send message: {e}")
            self.metrics['failed_deliveries'] += 1
            return ""
    
    async def receive_message(self, agent_id: str, timeout: float = 1.0) -> Optional[AgentMessage]:
        """Receive message for an agent"""
        try:
            if agent_id not in self.message_queues:
                return None
            
            # Get message from queue with timeout
            message = await asyncio.wait_for(
                self.message_queues[agent_id].get(),
                timeout=timeout
            )
            
            # Update agent activity
            if agent_id in self.agent_contexts:
                self.agent_contexts[agent_id].last_activity = datetime.now()
            
            # Trigger message handlers
            await self._trigger_message_handlers(agent_id, message)
            
            return message
            
        except asyncio.TimeoutError:
            return None
        except Exception as e:
            self.logger.error(f"Failed to receive message for {agent_id}: {e}")
            return None
    
    async def broadcast_to_all(self, from_agent: str, content: Dict[str, Any],
                              exclude_agents: List[str] = None) -> List[str]:
        """Broadcast message to all registered agents"""
        exclude_agents = exclude_agents or []
        message_ids = []
        
        for agent_id in self.message_queues:
            if agent_id != from_agent and agent_id not in exclude_agents:
                message_id = await self.send_message(
                    from_agent=from_agent,
                    to_agent=agent_id,
                    message_type=MessageType.BROADCAST,
                    content=content,
                    priority=MessagePriority.NORMAL
                )
                if message_id:
                    message_ids.append(message_id)
        
        return message_ids
    
    async def broadcast_to_group(self, from_agent: str, group: List[str], 
                               content: Dict[str, Any]) -> List[str]:
        """Broadcast message to specific group of agents"""
        message_ids = []
        
        for agent_id in group:
            if agent_id != from_agent and agent_id in self.message_queues:
                message_id = await self.send_message(
                    from_agent=from_agent,
                    to_agent=agent_id,
                    message_type=MessageType.BROADCAST,
                    content=content,
                    priority=MessagePriority.NORMAL
                )
                if message_id:
                    message_ids.append(message_id)
        
        return message_ids
    
    async def request_collaboration(self, requesting_agent: str, target_agents: List[str],
                                  collaboration_type: str, context: Dict[str, Any]) -> str:
        """Request collaboration between agents"""
        try:
            collaboration_id = str(uuid.uuid4())
            
            # Track collaboration
            self.active_collaborations[collaboration_id] = {
                'id': collaboration_id,
                'requesting_agent': requesting_agent,
                'target_agents': target_agents,
                'type': collaboration_type,
                'context': context,
                'status': 'pending',
                'responses': {},
                'created_at': datetime.now()
            }
            
            # Send collaboration requests
            for target_agent in target_agents:
                await self.send_message(
                    from_agent=requesting_agent,
                    to_agent=target_agent,
                    message_type=MessageType.COLLABORATION_REQUEST,
                    content={
                        'collaboration_id': collaboration_id,
                        'type': collaboration_type,
                        'context': context,
                        'requesting_agent': requesting_agent
                    },
                    requires_response=True,
                    response_timeout=30.0
                )
            
            self.metrics['active_collaborations'] += 1
            self.logger.info(f"Collaboration requested: {collaboration_id}")
            return collaboration_id
            
        except Exception as e:
            self.logger.error(f"Failed to request collaboration: {e}")
            return ""
    
    async def respond_to_collaboration(self, agent_id: str, collaboration_id: str,
                                     response: Dict[str, Any]) -> bool:
        """Respond to collaboration request"""
        try:
            if collaboration_id not in self.active_collaborations:
                return False
            
            collaboration = self.active_collaborations[collaboration_id]
            collaboration['responses'][agent_id] = {
                'response': response,
                'timestamp': datetime.now()
            }
            
            # Check if all responses received
            if len(collaboration['responses']) == len(collaboration['target_agents']):
                collaboration['status'] = 'complete'
                self.metrics['active_collaborations'] -= 1
            
            # Send response to requesting agent
            await self.send_message(
                from_agent=agent_id,
                to_agent=collaboration['requesting_agent'],
                message_type=MessageType.COLLABORATION_RESPONSE,
                content={
                    'collaboration_id': collaboration_id,
                    'response': response,
                    'responding_agent': agent_id
                }
            )
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to respond to collaboration: {e}")
            return False
    
    async def update_agent_context(self, agent_id: str, context_updates: Dict[str, Any]) -> bool:
        """Update agent context"""
        try:
            if agent_id not in self.agent_contexts:
                return False
            
            context = self.agent_contexts[agent_id]
            
            # Update context data
            if context.context_data is None:
                context.context_data = {}
            
            context.context_data.update(context_updates)
            context.last_activity = datetime.now()
            
            # Update specific fields if provided
            if 'current_task' in context_updates:
                context.current_task = context_updates['current_task']
            if 'status' in context_updates:
                context.status = context_updates['status']
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to update agent context: {e}")
            return False
    
    async def share_context(self, from_agent: str, to_agent: str, 
                           context_data: Dict[str, Any]) -> bool:
        """Share context between agents"""
        try:
            message_id = await self.send_message(
                from_agent=from_agent,
                to_agent=to_agent,
                message_type=MessageType.CONTEXT_SHARE,
                content={
                    'context_data': context_data,
                    'shared_at': datetime.now().isoformat()
                }
            )
            
            return bool(message_id)
            
        except Exception as e:
            self.logger.error(f"Failed to share context: {e}")
            return False
    
    def register_message_handler(self, agent_id: str, handler: Callable):
        """Register message handler for an agent"""
        if agent_id not in self.message_handlers:
            self.message_handlers[agent_id] = []
        
        self.message_handlers[agent_id].append(handler)
    
    async def _trigger_message_handlers(self, agent_id: str, message: AgentMessage):
        """Trigger registered message handlers"""
        try:
            if agent_id in self.message_handlers:
                for handler in self.message_handlers[agent_id]:
                    try:
                        await handler(message)
                    except Exception as e:
                        self.logger.error(f"Message handler failed: {e}")
        except Exception as e:
            self.logger.error(f"Failed to trigger message handlers: {e}")
    
    def get_agent_status(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """Get agent status and context"""
        if agent_id not in self.agent_contexts:
            return None
        
        context = self.agent_contexts[agent_id]
        return {
            'agent_id': agent_id,
            'status': context.status,
            'current_task': context.current_task,
            'capabilities': context.capabilities,
            'last_activity': context.last_activity.isoformat() if context.last_activity else None,
            'queue_size': self.message_queues[agent_id].qsize() if agent_id in self.message_queues else 0
        }
    
    def get_all_agents_status(self) -> Dict[str, Any]:
        """Get status of all registered agents"""
        return {
            agent_id: self.get_agent_status(agent_id)
            for agent_id in self.agent_contexts
        }
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get communication metrics"""
        return self.metrics.copy()
    
    def get_message_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get recent message history"""
        recent_messages = self.message_history[-limit:]
        return [asdict(msg) for msg in recent_messages]
    
    async def shutdown(self):
        """Shutdown communication system"""
        try:
            # Clear all queues
            for queue in self.message_queues.values():
                while not queue.empty():
                    try:
                        queue.get_nowait()
                    except asyncio.QueueEmpty:
                        break
            
            # Clear data structures
            self.message_queues.clear()
            self.agent_contexts.clear()
            self.message_history.clear()
            self.pending_responses.clear()
            self.active_collaborations.clear()
            
            self.logger.info("RealAgentCommunication shutdown complete")
            
        except Exception as e:
            self.logger.error(f"Error during communication shutdown: {e}")
