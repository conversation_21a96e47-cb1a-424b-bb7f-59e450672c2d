#!/usr/bin/env python3
"""
BMAD-Vybe Method Integration Bridge (Production)
Provides seamless transition between BMAD Method v3.1 and Vybe Method MAS
"""

import json
import sys
import asyncio
from datetime import datetime
from pathlib import Path
from typing import Dict, Any

class BMADVybeIntegrationBridge:
    """
    Production integration bridge between BMAD Method v3.1 and Vybe Method MAS
    Features:
    - Agent mapping between methods
    - Transition workflows
    - Status tracking
    - Shared artifacts management
    - Async support for real workflows
    """

    def __init__(self):
        """Initialize the integration bridge"""
        self.agent_mapping = {
            # BMAD Method v3.1 agents → Vybe Method MAS agents (IDE Version)
            "analyst": "vyba",        # <PERSON> → VYBA (analytics)
            "pm": "qubert",           # <PERSON> → QUBE<PERSON> (project management)
            "architect": "codex",     # <PERSON><PERSON> → CODE<PERSON> (technical architecture)
            "design-architect": "pixy", # <PERSON> → <PERSON>IX<PERSON> (design & UX)
            "po": "happy",            # <PERSON> → <PERSON> (product owner)
            "sm": "ducky",            # <PERSON><PERSON> → <PERSON><PERSON><PERSON><PERSON> (scrum master)
            "dev-frontend": "vybro",  # <PERSON> → <PERSON><PERSON>BR<PERSON> (development)
            "dev-fullstack": "vybro", # <PERSON> → V<PERSON>BRO (full-stack)
            "platform-engineer": "codex"  # <PERSON> → CODEX (infrastructure)
        }

        self.integration_status = {
            "bridge_active": True,
            "last_transition": None,
            "transitions_count": 0,
            "status": "ready",
            "message": "BMAD-Vybe Integration Bridge ready",
            "timestamp": datetime.now().isoformat()
        }

        # Initialize shared directory
        self.shared_dir = Path(__file__).parent / "shared_artifacts"
        self.shared_dir.mkdir(exist_ok=True)

    async def initialize_bridge(self):
        """Initialize bridge for async operations"""
        self.integration_status["status"] = "initialized"
        self.integration_status["timestamp"] = datetime.now().isoformat()

    def get_integration_status(self) -> Dict[str, Any]:
        """Get current integration bridge status"""
        return {
            "bridge_available": True,
            "status": self.integration_status["status"],
            "message": self.integration_status["message"],
            "agent_mappings": len(self.agent_mapping),
            "last_transition": self.integration_status.get("last_transition"),
            "transitions_count": self.integration_status.get("transitions_count", 0),
            "timestamp": datetime.now().isoformat()
        }

    async def bmad_to_vybe_transition(self, agent_name: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Transition from BMAD Method agent to Vybe Method MAS

        Args:
            agent_name: The BMAD agent completing work (analyst, pm, architect, etc.)
            context: Context and artifacts from BMAD workflow

        Returns:
            Dict containing transition results and agent assignments
        """
        try:
            if agent_name not in self.agent_mapping:
                return {
                    "success": False,
                    "error": f"Unknown BMAD agent: {agent_name}",
                    "available_agents": list(self.agent_mapping.keys())
                }

            target_agent = self.agent_mapping[agent_name]

            transition_data = {
                "transition_type": "bmad_to_vybe",
                "timestamp": datetime.now().isoformat(),
                "source_agent": agent_name,
                "target_agent": target_agent,
                "context": context or {},
                "workflow_id": f"transition_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            }

            # Save transition data
            await self._save_transition_data(transition_data)

            # Update status
            self.integration_status.update({
                "last_transition": "bmad_to_vybe",
                "transitions_count": self.integration_status.get("transitions_count", 0) + 1,
                "status": "transition_complete"
            })

            return {
                "success": True,
                "transition_type": "bmad_to_vybe",
                "source_agent": agent_name,
                "target_agent": target_agent,
                "transition_data": transition_data,
                "context_preserved": True,
                "message": f"Successfully transitioned from BMAD {agent_name} to Vybe {target_agent}"
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to transition from BMAD {agent_name} to Vybe Method"
            }

    async def vybe_to_bmad_handoff(self, deliverables: Dict[str, Any]) -> Dict[str, Any]:
        """
        Hand off Vybe Method autonomous results to BMAD Method for review

        Args:
            deliverables: Autonomous deliverables from Vybe MAS

        Returns:
            Dict containing handoff results and review structure
        """
        try:
            handoff_data = {
                "handoff_type": "vybe_to_bmad",
                "timestamp": datetime.now().isoformat(),
                "deliverables": deliverables,
                "review_structure": {},
                "workflow_id": f"handoff_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            }

            # Create review structure for BMAD agents
            for vybe_agent, bmad_agent in [(v, k) for k, v in self.agent_mapping.items()]:
                if vybe_agent in deliverables.get("agents", []):
                    handoff_data["review_structure"][bmad_agent] = {
                        "vybe_agent": vybe_agent,
                        "deliverables": deliverables.get(vybe_agent, {}),
                        "review_status": "pending"
                    }

            # Save handoff data
            await self._save_transition_data(handoff_data)

            # Update status
            self.integration_status.update({
                "last_transition": "vybe_to_bmad",
                "transitions_count": self.integration_status.get("transitions_count", 0) + 1,
                "status": "handoff_complete"
            })

            return {
                "success": True,
                "handoff_type": "vybe_to_bmad",
                "review_structure": handoff_data["review_structure"],
                "workflow_id": handoff_data["workflow_id"],
                "message": "Successfully handed off to BMAD Method for review"
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to handoff to BMAD Method"
            }

    async def execute_hybrid_workflow(self, task_description: str, mode: str = "bmad_first") -> Dict[str, Any]:
        """
        Execute a hybrid BMAD-Vybe workflow

        Args:
            task_description: Description of the task to be completed
            mode: "bmad_first", "vybe_first", or "parallel"

        Returns:
            Dict containing workflow execution results
        """
        try:
            workflow_data = {
                "workflow_type": "hybrid",
                "mode": mode,
                "timestamp": datetime.now().isoformat(),
                "task_description": task_description,
                "workflow_id": f"hybrid_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            }

            # Execute workflow based on mode
            if mode == "bmad_first":
                workflow_data["phases"] = [
                    {"phase": "bmad_education", "status": "planned"},
                    {"phase": "vybe_automation", "status": "planned"}
                ]
            elif mode == "vybe_first":
                workflow_data["phases"] = [
                    {"phase": "vybe_automation", "status": "planned"},
                    {"phase": "bmad_review", "status": "planned"}
                ]
            elif mode == "parallel":
                workflow_data["phases"] = [
                    {"phase": "bmad_vybe_parallel", "status": "planned"}
                ]

            # Save workflow data
            await self._save_transition_data(workflow_data)

            # Update status
            self.integration_status.update({
                "last_transition": "hybrid_workflow",
                "transitions_count": self.integration_status.get("transitions_count", 0) + 1,
                "status": "hybrid_active"
            })

            return {
                "success": True,
                "workflow_type": "hybrid",
                "mode": mode,
                "workflow_result": workflow_data,
                "message": f"Hybrid workflow initiated: {mode}"
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to execute hybrid workflow: {mode}"
            }

    async def _save_transition_data(self, data: Dict[str, Any]) -> None:
        """Save transition data to shared artifacts directory"""
        try:
            timestamp = data.get("timestamp", datetime.now().isoformat()).replace(":", "-")
            filename = f"transition_{timestamp}.json"
            filepath = self.shared_dir / filename

            with open(filepath, 'w') as f:
                json.dump(data, f, indent=2)

        except Exception as e:
            print(f"Warning: Could not save transition data: {e}")

# Test function for async operations
async def test_bridge_async():
    """Test the integration bridge functionality with async support"""
    print("🔗 Testing BMAD-Vybe Integration Bridge (Async)")

    try:
        bridge = BMADVybeIntegrationBridge()
        await bridge.initialize_bridge()

        # Test status
        status = bridge.get_integration_status()
        print(f"✅ Bridge Status: {status['status']}")
        print(f"   Agent Mappings: {status['agent_mappings']}")

        # Test BMAD to Vybe transition
        transition_result = await bridge.bmad_to_vybe_transition("analyst", {"project": "test"})
        print(f"✅ BMAD→Vybe Transition: {transition_result['success']}")

        # Test Vybe to BMAD handoff
        deliverables = {
            "agents": ["vyba", "qubert"],
            "vyba": {"analysis": "completed"},
            "qubert": {"project_plan": "ready"}
        }
        handoff_result = await bridge.vybe_to_bmad_handoff(deliverables)
        print(f"✅ Vybe→BMAD Handoff: {handoff_result['success']}")

        # Test hybrid workflow
        hybrid_result = await bridge.execute_hybrid_workflow("Test hybrid workflow", "bmad_first")
        print(f"✅ Hybrid Workflow: {hybrid_result['success']}")

        print("🎉 Async bridge integration test completed successfully!")
        return True

    except Exception as e:
        print(f"❌ Async bridge test failed: {e}")
        return False

# Sync wrapper for compatibility
def test_bridge():
    """Synchronous wrapper for bridge testing"""
    return asyncio.run(test_bridge_async())

if __name__ == "__main__":
    test_bridge()