#!/usr/bin/env python3

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from bmad_vybe_bridge import BMADVybeIntegrationBridge
    print("✅ Bridge import successful!")
    
    bridge = BMADVybeIntegrationBridge()
    print("✅ Bridge initialized!")
    
    status = bridge.get_integration_status()
    print("✅ Bridge status retrieved!")
    print(f"Status: {status}")
    
    # Test transition
    context = {"active_agents": ["analyst", "pm"], "project": "test"}
    result = bridge.bmad_to_vybe_transition(context)
    print(f"✅ Transition test: {result['success']}")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
