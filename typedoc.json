{"entryPoints": ["src/lib"], "out": "docs/api/typescript", "theme": "default", "name": "VybeCoding.ai TypeScript Documentation", "includeVersion": true, "excludePrivate": true, "excludeProtected": false, "excludeExternals": true, "readme": "docs/typescript-readme.md", "categorizeByGroup": true, "defaultCategory": "Other", "categoryOrder": ["Components", "Stores", "Utils", "Types", "Config", "*"], "sort": ["source-order"], "kindSortOrder": ["Document", "Project", "<PERSON><PERSON><PERSON>", "Namespace", "Enum", "EnumMember", "Class", "Interface", "TypeAlias", "<PERSON><PERSON><PERSON><PERSON>", "Property", "Variable", "Function", "Accessor", "Method", "Parameter", "TypeParameter", "TypeLiteral", "CallSignature", "ConstructorSignature", "IndexSignature", "GetSignature", "SetSignature"], "visibilityFilters": {"protected": false, "private": false, "inherited": true, "external": false}, "searchInComments": true, "cleanOutputDir": true, "titleLink": "https://vybecoding.ai", "navigationLinks": {"GitHub": "https://github.com/Hiram-<PERSON>y/VybeCoding.ai", "Documentation": "https://docs.vybecoding.ai"}, "sidebarLinks": {"API Documentation": "https://docs.vybecoding.ai/api/openapi.yaml", "Component Library": "https://docs.vybecoding.ai/components/"}, "hostedBaseUrl": "https://docs.vybecoding.ai/api/typescript/", "hideGenerator": false, "customCss": "docs/typedoc-custom.css", "markedOptions": {"mangle": false}, "blockTags": ["@alpha", "@beta", "@defaultValue", "@decorator", "@deprecated", "@eventProperty", "@example", "@experimental", "@inheritDoc", "@internal", "@label", "@override", "@packageDocumentation", "@param", "@privateRemarks", "@public", "@readonly", "@remarks", "@returns", "@sealed", "@see", "@since", "@throws", "@typeParam", "@virtual"], "inlineTags": ["@inheritDoc", "@label", "@link", "@linkcode", "@linkplain"], "modifierTags": ["@alpha", "@beta", "@deprecated", "@eventProperty", "@experimental", "@internal", "@override", "@packageDocumentation", "@public", "@readonly", "@sealed", "@virtual"]}