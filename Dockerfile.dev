# VybeCoding.ai Development Dockerfile
# Optimized for development with hot reload

FROM node:20-alpine

# Install development tools and security updates
RUN apk update && apk add --no-cache \
    curl \
    git \
    bash \
    vim \
    && rm -rf /var/cache/apk/*

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S developer -u 1001 -G nodejs

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install all dependencies (including dev dependencies)
RUN npm install && npm cache clean --force

# Create necessary directories
RUN mkdir -p /app/logs /app/node_modules/.cache && \
    chown -R developer:nodejs /app

# Switch to non-root user
USER developer

# Expose Vite HMR port for hot module replacement
EXPOSE 24678

# Expose port
EXPOSE 5173

# Set environment variables
ENV NODE_ENV=development
ENV VITE_HOST=0.0.0.0
ENV VITE_PORT=5173

# Health check for development
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:5173 || exit 1

# Labels
LABEL maintainer="VybeCoding.ai Team"
LABEL version="1.0.0-dev"
LABEL description="VybeCoding.ai Development Environment"

# Start development server
CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0"]
