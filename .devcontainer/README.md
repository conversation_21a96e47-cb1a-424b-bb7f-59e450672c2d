# VybeCoding.ai VS Code Dev Container

This directory contains the VS Code development container configuration for VybeCoding.ai, enabling a consistent, fully-configured development environment that can be set up in under 5 minutes.

## 🚀 Quick Start

### Prerequisites

- [Docker Desktop](https://www.docker.com/products/docker-desktop) installed and running
- [VS Code](https://code.visualstudio.com/) with the [Remote-Containers extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.remote-containers)
- Git repository cloned locally

### One-Click Setup

1. Open the project folder in VS Code
2. When prompted, click "Reopen in Container" (or use Command Palette: `Remote-Containers: Reopen in Container`)
3. Wait for the container to build and start (first time: ~3-5 minutes, subsequent: ~30 seconds)
4. Start coding! 🎉

## 📦 What's Included

### Development Tools

- **Node.js 18** with npm and global development packages
- **SvelteKit** with TypeScript support
- **Docker-in-Docker** for container management
- **GitHub CLI** for repository operations
- **Python 3** with pip for development scripts

### VS Code Extensions (Auto-installed)

- **Svelte for VS Code** - Full SvelteKit support
- **Tailwind CSS IntelliSense** - CSS framework support
- **Prettier** - Code formatting
- **ESLint** - Code linting
- **TypeScript** - Enhanced TypeScript support
- **Docker** - Container management
- **GitLens** - Enhanced Git integration
- **Auto Rename Tag** - HTML/XML tag management
- **Path Intellisense** - File path autocompletion

### Pre-configured Settings

- **Format on Save** enabled with Prettier
- **ESLint** validation for JavaScript, TypeScript, and Svelte
- **Tailwind CSS** IntelliSense for Svelte files
- **TypeScript** auto-imports and suggestions
- **Git** smart commit and auto-fetch
- **Terminal** configured with useful aliases

## 🔧 Container Features

### Port Forwarding

- **5173** - SvelteKit development server
- **3000** - Production server
- **8080** - Appwrite console (when using local instance)
- **6379** - Redis (when using local instance)

### Volume Mounts

- **Source Code** - Live reload for development
- **Node Modules** - Cached for faster rebuilds
- **Docker Socket** - Docker-in-Docker capabilities
- **VS Code Extensions** - Persistent extension storage
- **Logs** - Application logs directory

### Environment Variables

```bash
NODE_ENV=development
VITE_HOST=0.0.0.0
VITE_PORT=5173
VITE_ENVIRONMENT=development
VITE_ENABLE_DEBUG_MODE=true
VITE_ENABLE_HOT_RELOAD=true
```

## 🛠️ Available Commands

### Development Commands

```bash
# Start development server
npm run dev

# Build for production
npm run build

# Run tests
npm run test

# Type checking
npm run check

# Linting and formatting
npm run lint
npm run format
```

### Container Commands

```bash
# Start dev container (outside VS Code)
npm run dev:container

# Build dev container
npm run dev:container:build

# Stop dev container
npm run dev:container:down
```

### Useful Aliases (in container terminal)

```bash
ll          # ls -la
gs          # git status
gp          # git pull
gc          # git commit
gd          # git diff
npm-dev     # npm run dev
npm-build   # npm run build
npm-test    # npm run test
```

## 🔍 Troubleshooting

### Container Won't Start

1. Ensure Docker Desktop is running
2. Check Docker has sufficient resources (4GB+ RAM recommended)
3. Try rebuilding the container: `Remote-Containers: Rebuild Container`

### Extensions Not Loading

1. Wait for container to fully initialize
2. Check VS Code output panel for errors
3. Try reloading the window: `Developer: Reload Window`

### Hot Reload Not Working

1. Ensure source code is properly mounted
2. Check file permissions in container
3. Restart the development server: `npm run dev`

### Port Already in Use

1. Check if another instance is running: `docker ps`
2. Stop conflicting containers: `docker stop <container-id>`
3. Use different ports in docker-compose.development.yml

### Performance Issues

1. Increase Docker Desktop memory allocation
2. Enable Docker Desktop's "Use the WSL 2 based engine" (Windows)
3. Close unnecessary applications

## 🎯 Educational Integration

This dev container is designed to teach container concepts while providing a productive development environment:

### Learning Opportunities

- **Container Basics** - Understanding Docker concepts through daily use
- **Development Workflows** - Professional development practices
- **Tool Integration** - How modern development tools work together
- **Environment Consistency** - Benefits of containerized development

### Progressive Disclosure

- **Beginner** - Use the container without understanding internals
- **Intermediate** - Explore container configuration and customization
- **Advanced** - Modify and extend the container setup

## 🔒 Security Features

### Non-Root User

- Container runs as `vscode` user (UID 1001)
- Proper file permissions and ownership
- Reduced security attack surface

### Docker-in-Docker

- Secure Docker socket mounting
- Isolated container operations
- Proper privilege management

### Secrets Management

- Environment variables for configuration
- No secrets stored in container images
- Secure credential handling

## 📊 Performance Metrics

### Target Performance

- **Container Startup** - < 30 seconds (after initial build)
- **Initial Build** - < 5 minutes
- **Hot Reload** - < 1 second
- **Extension Loading** - < 30 seconds

### Resource Usage

- **Memory** - ~512MB base usage
- **CPU** - < 50% under normal development load
- **Disk** - ~2GB for full container with dependencies

## 🚀 Next Steps

After setting up the dev container:

1. **Explore the Codebase** - Familiarize yourself with the project structure
2. **Run Tests** - Ensure everything works: `npm run test`
3. **Start Development** - Begin working on your assigned tasks
4. **Learn Container Concepts** - Understand how the setup works
5. **Customize** - Modify the container to fit your workflow

## 📚 Additional Resources

- [VS Code Remote-Containers Documentation](https://code.visualstudio.com/docs/remote/containers)
- [Docker Documentation](https://docs.docker.com/)
- [SvelteKit Documentation](https://kit.svelte.dev/)
- [VybeCoding.ai Development Guide](../docs/setup-guide.md)

---

**Happy Coding!** 🎉

This dev container setup represents the foundation of modern, containerized development workflows that you'll use throughout your career in software development.
