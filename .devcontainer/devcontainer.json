{
  "name": "VybeCoding.ai Development",
  "build": {
    "dockerfile": "Dockerfile",
    "args": {
      "USERNAME": "vscode",
      "USER_UID": "1000",
      "USER_GID": "1000"
    }
  },
  "workspaceFolder": "/workspaces/vybecoding",

  // Network configuration for external access
  "runArgs": ["--publish=5173:5173"],

  // Features to install in the container
  "features": {
    "ghcr.io/devcontainers/features/docker-in-docker:2": {
      "version": "latest",
      "enableNonRootDocker": "true"
    },
    "ghcr.io/devcontainers/features/github-cli:1": {
      "version": "latest"
    }
  },

  // VS Code customizations
  "customizations": {
    "vscode": {
      "extensions": [
        // SvelteKit and Svelte support
        "svelte.svelte-vscode",

        // CSS and Styling
        "bradlc.vscode-tailwindcss",

        // Code formatting and linting
        "esbenp.prettier-vscode",
        "dbaeumer.vscode-eslint",

        // TypeScript support
        "ms-vscode.vscode-typescript-next",

        // Docker tools
        "ms-azuretools.vscode-docker",

        // Git integration
        "eamodio.gitlens",

        // Additional helpful extensions
        "ms-vscode.vscode-json",
        "redhat.vscode-yaml",
        "ms-vscode-remote.remote-containers",
        "ms-vscode.live-server",
        "formulahendry.auto-rename-tag",
        "christian-kohler.path-intellisense",
        "ms-vscode.vscode-typescript-next",

        // AI and code augmentation
        "augment.vscode-augment"
      ],

      "settings": {
        // TypeScript settings
        "typescript.preferences.includePackageJsonAutoImports": "on",
        "typescript.suggest.autoImports": true,

        // Svelte settings
        "svelte.enable-ts-plugin": true,
        "svelte.plugin.typescript.enable": true,
        "svelte.plugin.typescript.diagnostics.enable": true,

        // Prettier settings
        "editor.defaultFormatter": "esbenp.prettier-vscode",
        "editor.formatOnSave": true,
        "editor.formatOnPaste": true,

        // ESLint settings
        "eslint.enable": true,
        "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "svelte"],

        // Tailwind CSS settings
        "tailwindCSS.includeLanguages": {
          "svelte": "html"
        },
        "tailwindCSS.experimental.classRegex": ["class:([^=]+)=['\"]([^'\"]*)['\"]"],

        // File associations
        "files.associations": {
          "*.svelte": "svelte"
        },

        // Editor settings
        "editor.tabSize": 2,
        "editor.insertSpaces": true,
        "editor.detectIndentation": false,
        "editor.wordWrap": "on",
        "editor.minimap.enabled": false,
        "editor.bracketPairColorization.enabled": true,
        "editor.guides.bracketPairs": true,

        // Terminal settings
        "terminal.integrated.defaultProfile.linux": "bash",
        "terminal.integrated.shell.linux": "/bin/bash",

        // Git settings
        "git.enableSmartCommit": true,
        "git.confirmSync": false,
        "git.autofetch": true,

        // Docker settings
        "docker.showStartPage": false,

        // Live Server settings
        "liveServer.settings.donotShowInfoMsg": true,

        // Search settings
        "search.exclude": {
          "**/node_modules": true,
          "**/build": true,
          "**/.svelte-kit": true,
          "**/dist": true
        },

        // File watcher settings
        "files.watcherExclude": {
          "**/node_modules/**": true,
          "**/.git/objects/**": true,
          "**/.git/subtree-cache/**": true,
          "**/build/**": true,
          "**/.svelte-kit/**": true
        }
      }
    }
  },

  // Ports to forward (enable network access)
  "forwardPorts": [5173],
  "portsAttributes": {
    "5173": {
      "label": "VybeCoding.ai Dev Server",
      "onAutoForward": "notify",
      "visibility": "public",
      "protocol": "http"
    }
  },

  // Commands to run after container creation
  "postCreateCommand": "echo 'Container created! Workspace will be mounted shortly...'",

  // Commands to run when attaching to container
  "postAttachCommand": "echo 'Dev container ready! Run: npm install && npm run dev'",

  // Mount the Docker socket for Docker-in-Docker
  "mounts": ["source=/var/run/docker.sock,target=/var/run/docker.sock,type=bind"],

  // Environment variables
  "containerEnv": {
    "NODE_ENV": "development",
    "VITE_HOST": "0.0.0.0",
    "VITE_PORT": "5173"
  },

  // User to run as (non-root for security)
  "remoteUser": "vscode",

  // Override the command to keep container running
  "overrideCommand": false,

  // Lifecycle scripts
  "initializeCommand": "echo 'Initializing VybeCoding.ai dev container...'",
  "onCreateCommand": "echo 'Dev container created successfully!'",

  // Additional container features
  "capAdd": ["SYS_PTRACE"],
  "securityOpt": ["seccomp=unconfined"],

  // Shutdown action
  "shutdownAction": "stopContainer"
}
