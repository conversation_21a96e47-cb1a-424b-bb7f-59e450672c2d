# VybeCoding.ai VS Code Dev Container
# Enhanced development environment with all tools and extensions

FROM node:20-bullseye

# Install system dependencies and development tools
RUN apt-get update && apt-get install -y \
    # Basic tools
    curl \
    git \
    bash \
    openssh-client \
    # Build tools
    build-essential \
    python3 \
    python3-pip \
    # Additional utilities
    nano \
    vim \
    htop \
    tree \
    jq \
    # Security tools
    gnupg \
    ca-certificates \
    # Clean up
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user for VS Code with matching host user ID
ARG USERNAME=vscode
ARG USER_UID=1000
ARG USER_GID=$USER_UID

RUN if ! getent group $USER_GID >/dev/null 2>&1; then groupadd --gid $USER_GID vscode; fi && \
    if ! getent passwd $USER_UID >/dev/null 2>&1; then useradd --uid $USER_UID --gid $USER_GID -s /bin/bash -m vscode; fi && \
    if ! id vscode >/dev/null 2>&1; then usermod -l vscode $(getent passwd $USER_UID | cut -d: -f1); fi

# Install global Node.js development tools
RUN npm install -g \
    @sveltejs/kit \
    @playwright/test \
    typescript \
    ts-node \
    nodemon \
    concurrently \
    npm-check-updates \
    serve \
    http-server

# Install Python tools for development scripts
RUN pip3 install --no-cache-dir \
    requests \
    python-dotenv \
    pyyaml

# Set up workspace directory
WORKDIR /workspaces/vybecoding

# Create necessary directories with proper permissions
RUN mkdir -p \
    /workspaces \
    /workspaces/vybecoding \
    /home/<USER>/.npm \
    /home/<USER>/.cache \
    /home/<USER>/.config && \
    chown -R $USER_UID:$USER_GID /workspaces && \
    if [ -d /home/<USER>/home/<USER>

# Note: Project dependencies will be installed via postCreateCommand
# This allows for proper workspace mounting and avoids build context issues

# Set up Git configuration for the container
RUN git config --global --add safe.directory /workspaces/vybecoding && \
    git config --global init.defaultBranch main

# Configure npm for the user and create .bashrc
RUN npm config set prefix /home/<USER>/.npm && \
    touch /home/<USER>/.bashrc && \
    echo 'export PATH="/home/<USER>/.npm/bin:$PATH"' >> /home/<USER>/.bashrc && \
    echo 'alias ll="ls -la"' >> /home/<USER>/.bashrc && \
    echo 'alias la="ls -la"' >> /home/<USER>/.bashrc && \
    echo 'alias ..="cd .."' >> /home/<USER>/.bashrc && \
    echo 'alias ...="cd ../.."' >> /home/<USER>/.bashrc && \
    echo 'alias gs="git status"' >> /home/<USER>/.bashrc && \
    echo 'alias gp="git pull"' >> /home/<USER>/.bashrc && \
    echo 'alias gc="git commit"' >> /home/<USER>/.bashrc && \
    echo 'alias gd="git diff"' >> /home/<USER>/.bashrc && \
    echo 'alias npm-dev="npm run dev"' >> /home/<USER>/.bashrc && \
    echo 'alias npm-build="npm run build"' >> /home/<USER>/.bashrc && \
    echo 'alias npm-test="npm run test"' >> /home/<USER>/.bashrc && \
    if [ -f /home/<USER>/.bashrc ]; then chown $USER_UID:$USER_GID /home/<USER>/.bashrc; fi

# Switch to non-root user
USER vscode

# Set environment variables for development
ENV NODE_ENV=development
ENV VITE_HOST=0.0.0.0
ENV VITE_PORT=5173
ENV VITE_ENVIRONMENT=development
ENV VITE_ENABLE_DEBUG_MODE=true
ENV VITE_ENABLE_HOT_RELOAD=true

# Expose development ports
EXPOSE **************

# Note: Health check removed for dev container compatibility

# Labels for container metadata
LABEL maintainer="VybeCoding.ai Team"
LABEL version="1.0.0-devcontainer"
LABEL description="VybeCoding.ai VS Code Development Container"
LABEL org.opencontainers.image.title="VybeCoding.ai Dev Container"
LABEL org.opencontainers.image.description="Complete development environment for VybeCoding.ai"
LABEL org.opencontainers.image.vendor="VybeCoding.ai"
LABEL org.opencontainers.image.licenses="MIT"

# Default command (will be overridden by VS Code)
CMD ["bash", "-c", "while sleep 1000; do :; done"]
