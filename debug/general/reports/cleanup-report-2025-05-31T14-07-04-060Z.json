{"timestamp": "2025-05-31T14:07:04.060Z", "removedFolders": [{"name": "debug-artifacts", "size": 362399, "reason": "Replaced by organized debug/ structure"}, {"name": "debug-output", "size": 21443, "reason": "Replaced by organized debug/ structure"}, {"name": "debug-screenshots", "size": 100304, "reason": "Replaced by organized debug/ structure"}, {"name": "debug-traces", "size": 124151, "reason": "Replaced by organized debug/ structure"}, {"name": "debug-videos", "size": 42331, "reason": "Replaced by organized debug/ structure"}, {"name": "firefox-debug-har", "size": 2235, "reason": "Replaced by organized debug/ structure"}, {"name": "firefox-debug-output", "size": 0, "reason": "Replaced by organized debug/ structure"}, {"name": "firefox-debug-screenshots", "size": 0, "reason": "Replaced by organized debug/ structure"}, {"name": "firefox-debug-traces", "size": 0, "reason": "Replaced by organized debug/ structure"}, {"name": "firefox-debug-videos", "size": 7933, "reason": "Replaced by organized debug/ structure"}, {"name": "__pycache__", "size": 7664, "reason": "Python cache files (auto-generated)"}, {"name": "src/src", "size": 3925, "reason": "Incorrect nested src structure"}, {"name": ".vite", "size": 169, "reason": "Build artifact (auto-generated)"}], "removedFiles": [{"name": "app.html", "size": 2573, "reason": "Duplicate or temporary file"}, {"name": "github-copilot-instructions.md", "size": 12340, "reason": "Duplicate or temporary file"}, {"name": "fix-github-setup.sh", "size": 3082, "reason": "Duplicate or temporary file"}, {"name": "git-push.sh", "size": 1244, "reason": "Duplicate or temporary file"}], "keptFolders": [], "recommendations": ["Use \"debug/\" folder for all debugging artifacts going forward", "Run \"npm run build\" to regenerate clean build artifacts", "Add __pycache__/ to .gitignore if not already present", "Use organized debug structure: debug/[tool]/[artifact-type]/", "Run automated cleanup weekly: node scripts/debug-artifact-manager.js maintenance"], "totalSizeFreed": 691793}