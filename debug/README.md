# VybeCoding.ai Debug Output

This directory contains organized debugging artifacts from autonomous debugging sessions.
Follows industry best practices with tool-based primary organization.

## Structure

```
debug/
├── general/          # General debugging tools output
│   ├── reports/      # Debug session reports and summaries
│   ├── screenshots/  # Visual debugging artifacts
│   ├── videos/       # Video recordings
│   ├── traces/       # Performance and debugging traces
│   ├── logs/         # Application and system logs
│   └── temp/         # Temporary processing files
├── firefox/          # Firefox Developer Edition specific output
│   ├── reports/      # Firefox debugging reports
│   ├── screenshots/  # Firefox screenshots and captures
│   ├── videos/       # Firefox test recordings
│   ├── traces/       # Firefox performance traces
│   ├── har/          # HTTP Archive files
│   ├── logs/         # Firefox-specific logs
│   └── temp/         # Firefox temporary files
├── playwright/       # Playwright automation output
│   ├── reports/      # Playwright test reports
│   ├── screenshots/  # Playwright screenshots
│   ├── videos/       # Playwright test recordings
│   ├── traces/       # Playwright traces
│   ├── logs/         # Playwright logs
│   └── temp/         # Playwright temporary files
├── exports/          # Formatted exports for external systems
│   ├── ci-cd/        # CI/CD pipeline artifacts
│   ├── reports/      # Formatted reports for sharing
│   └── dashboards/   # Dashboard data exports
└── archived/         # Compressed old artifacts
    ├── general/      # Archived general artifacts
    ├── firefox/      # Archived Firefox artifacts
    └── playwright/   # Archived Playwright artifacts
```

## Retention Policy

- **Reports**: 30 days (compressed after 7 days)
- **Screenshots**: 7 days (compressed after 3 days)
- **Videos**: 3 days (auto-deleted, large files)
- **Traces**: 7 days (compressed after 3 days)
- **Logs**: 14 days (compressed after 7 days)

## Management

Use `scripts/debug-artifact-manager.js` to:

- Clean up old artifacts
- Compress large files
- Generate usage reports
- Export artifacts for CI/CD

## Auto-cleanup

Cleanup runs automatically:

- Daily: Remove expired files
- Weekly: Compress old files
- Monthly: Generate usage reports
