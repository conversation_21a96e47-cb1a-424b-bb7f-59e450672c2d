# VybeCoding.ai Testing Docker Compose
# Isolated environment for running tests

version: '3.8'

services:
  vybecoding-test:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: vybecoding-test
    environment:
      - NODE_ENV=test
      - CI=true
      - VITE_APPWRITE_ENDPOINT=http://appwrite-test:80/v1
      - VITE_APPWRITE_PROJECT_ID=vybecoding-test
      - VITE_APPWRITE_DATABASE_ID=test
    volumes:
      - .:/app
      - /app/node_modules
      - test-cache:/app/.vite
      - test-coverage:/app/coverage
    networks:
      - vybecoding-test
    command: npm run test
    depends_on:
      - appwrite-test

  # Test database for integration tests
  appwrite-test:
    image: appwrite/appwrite:1.4
    container_name: appwrite-test
    ports:
      - '8080:80'
    environment:
      - _APP_ENV=development
      - _APP_WORKER_PER_CORE=6
      - _APP_LOCALE=en
      - _APP_CONSOLE_WHITELIST_ROOT=enabled
      - _APP_CONSOLE_WHITELIST_EMAILS=<EMAIL>
      - _APP_CONSOLE_WHITELIST_IPS=
      - _APP_SYSTEM_EMAIL_NAME=VybeCoding.ai Test
      - _APP_SYSTEM_EMAIL_ADDRESS=<EMAIL>
      - _APP_SYSTEM_SECURITY_EMAIL_ADDRESS=<EMAIL>
      - _APP_SYSTEM_RESPONSE_FORMAT=
      - _APP_OPTIONS_ABUSE=enabled
      - _APP_OPTIONS_FORCE_HTTPS=disabled
      - _APP_OPENSSL_KEY_V1=test-key-v1
      - _APP_DOMAIN=localhost
      - _APP_DOMAIN_TARGET=localhost
      - _APP_REDIS_HOST=redis-test
      - _APP_REDIS_PORT=6379
      - _APP_DB_HOST=mariadb-test
      - _APP_DB_PORT=3306
      - _APP_DB_SCHEMA=appwrite
      - _APP_DB_USER=user
      - _APP_DB_PASS=password
      - _APP_INFLUXDB_HOST=influxdb-test
      - _APP_INFLUXDB_PORT=8086
    volumes:
      - appwrite-test-uploads:/storage/uploads:rw
      - appwrite-test-cache:/storage/cache:rw
      - appwrite-test-config:/storage/config:rw
      - appwrite-test-certificates:/storage/certificates:rw
      - appwrite-test-functions:/storage/functions:rw
    depends_on:
      - mariadb-test
      - redis-test
    networks:
      - vybecoding-test

  # Test database
  mariadb-test:
    image: mariadb:10.7
    container_name: mariadb-test
    environment:
      - MYSQL_ROOT_PASSWORD=rootpassword
      - MYSQL_DATABASE=appwrite
      - MYSQL_USER=user
      - MYSQL_PASSWORD=password
    volumes:
      - mariadb-test-data:/var/lib/mysql:rw
    networks:
      - vybecoding-test

  # Test Redis
  redis-test:
    image: redis:7-alpine
    container_name: redis-test
    networks:
      - vybecoding-test

  # Test InfluxDB for metrics
  influxdb-test:
    image: appwrite/influxdb:1.5.0
    container_name: influxdb-test
    networks:
      - vybecoding-test

  # Browser testing service
  playwright:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: vybecoding-playwright
    environment:
      - NODE_ENV=test
      - CI=true
      - PLAYWRIGHT_BROWSERS_PATH=/app/.cache/playwright
    volumes:
      - .:/app
      - /app/node_modules
      - playwright-cache:/app/.cache/playwright
    networks:
      - vybecoding-test
    command: npx playwright test
    depends_on:
      - vybecoding-test
    profiles:
      - e2e

volumes:
  test-cache:
    driver: local
  test-coverage:
    driver: local
  playwright-cache:
    driver: local
  appwrite-test-uploads:
    driver: local
  appwrite-test-cache:
    driver: local
  appwrite-test-config:
    driver: local
  appwrite-test-certificates:
    driver: local
  appwrite-test-functions:
    driver: local
  mariadb-test-data:
    driver: local

networks:
  vybecoding-test:
    driver: bridge
