# VybeCoding.ai Docker Compose - Base Configuration
version: '3.8'

services:
  vybecoding:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - '${PORT:-3000}:3000'
    environment:
      - NODE_ENV=${NODE_ENV:-production}
      - VITE_ENVIRONMENT=${VITE_ENVIRONMENT:-production}
      - VITE_APPWRITE_ENDPOINT=${VITE_APPWRITE_ENDPOINT}
      - VITE_APPWRITE_PROJECT_ID=${VITE_APPWRITE_PROJECT_ID}
      - VITE_APPWRITE_DATABASE_ID=${VITE_APPWRITE_DATABASE_ID}
    volumes:
      - ./logs:/app/logs
      - vybecoding_data:/app/data
    restart: unless-stopped
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3000/api/health']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - vybecoding_network

  # Vybe Qube Generator Service
  vybe-qube-generator:
    build:
      context: ./services/vybe-qube-generator
      dockerfile: Dockerfile
    ports:
      - '${VYBE_QUBE_PORT:-8001}:8001'
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
      - PORT=8001
      - MAS_ENDPOINT=http://mas-coordinator:8002
      - DEPLOYER_ENDPOINT=http://vybe-qube-deployer:8002
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
      - STRIPE_PUBLIC_KEY=${STRIPE_PUBLIC_KEY}
      - REDIS_URL=redis://redis:6379
    volumes:
      - ./services/vybe-qube-generator:/app
      - /tmp/deployments:/tmp/deployments
      - ./method/vybe:/app/method/vybe
      - vybe_qube_data:/app/data
    depends_on:
      - redis
      - vybe-qube-deployer
    restart: unless-stopped
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:8001/health']
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - vybecoding_network

  # Vybe Qube Deployer Service
  vybe-qube-deployer:
    build:
      context: ./services/vybe-qube-deployer
      dockerfile: Dockerfile
    ports:
      - '${VYBE_DEPLOYER_PORT:-8002}:8002'
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
      - PORT=8002
      - DOCKER_HOST=unix:///var/run/docker.sock
      - CLOUDFLARE_API_TOKEN=${CLOUDFLARE_API_TOKEN}
      - CLOUDFLARE_ZONE_ID=${CLOUDFLARE_ZONE_ID}
    volumes:
      - ./services/vybe-qube-deployer:/app
      - /var/run/docker.sock:/var/run/docker.sock
      - /tmp/deployments:/tmp/deployments
      - vybe_deployment_data:/app/data
    restart: unless-stopped
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:8002/health']
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - vybecoding_network

  # Redis for caching and session storage
  redis:
    image: redis:7-alpine
    ports:
      - '${REDIS_PORT:-6379}:6379'
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ['CMD', 'redis-cli', 'ping']
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - vybecoding_network
    command: redis-server --appendonly yes

  # Nginx reverse proxy (optional)
  nginx:
    image: nginx:alpine
    ports:
      - '${NGINX_PORT:-80}:80'
      - '${NGINX_SSL_PORT:-443}:443'
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - vybecoding
    restart: unless-stopped
    networks:
      - vybecoding_network
    profiles:
      - with-nginx

volumes:
  vybecoding_data:
    driver: local
  redis_data:
    driver: local
  nginx_logs:
    driver: local
  vybe_qube_data:
    driver: local
  vybe_deployment_data:
    driver: local

networks:
  vybecoding_network:
    driver: bridge
