# VybeCoding.ai Enterprise Configuration
NODE_ENV=production
VITE_ENVIRONMENT=enterprise

# Database Configuration
POSTGRES_DB=vybecoding_enterprise
POSTGRES_USER=vybecoding
POSTGRES_PASSWORD=ypC7b/8FTmfT+jlWSBdDCzvy4j2gD0yXcCGeACaFYu4=

# Redis Configuration
REDIS_PASSWORD=QwKSBGh9ShXM8yhQsuGwjKNKE4oeljjqUlZzfUUalEg=

# Grafana Configuration
GRAFANA_PASSWORD=kdeyVMXSfDmu/G4Hm/G34A==

# SSL Configuration
SSL_DOMAIN=vybecoding.ai
SSL_EMAIL=<EMAIL>

# Scaling Configuration
MIN_INSTANCES=3
MAX_INSTANCES=10
SCALE_UP_THRESHOLD=80
SCALE_DOWN_THRESHOLD=20

# Enterprise Features
ENABLE_ENTERPRISE_FEATURES=true
ENABLE_AUTO_SCALING=true
ENABLE_ADVANCED_MONITORING=true
ENABLE_CDN=true
ENABLE_LOAD_BALANCING=true
