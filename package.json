{"name": "vybecoding-platform", "version": "1.0.0", "description": "VybeCoding.ai - AI-powered education platform teaching the Vybe Method", "type": "module", "scripts": {"dev": "npm run validate:no-simulations && npm run kill:port5173 && concurrently \"npm run portainer:safe\" \"npx vite dev --host --port 5173 --strictPort\" --names \"🐳,⚡\" --prefix-colors \"cyan,green\"", "dev:simple": "npm run kill:port5173 && npx vite dev --host --port 5173 --strictPort", "dev:devcontainer": "echo '🚀 Starting VybeCoding.ai in dev container mode...' && echo '📍 Server will be available at: http://localhost:5173' && npm run kill:port5173 && npx vite dev --host 0.0.0.0 --port 5173 --strictPort", "dev:container-safe": "echo '🚀 VybeCoding.ai Development Server' && echo '📍 URL: http://localhost:5173' && echo '🐳 Container Mode: Docker features disabled' && npm run kill:port5173 && npx vite dev --host 0.0.0.0 --port 5173 --strictPort", "dev:host": "./scripts/start-comprehensive-protected.sh", "dev:protected": "./scripts/start-with-protection.sh", "vybe": "npm run dev:host", "mas:start": "python3 mas-observatory-control.py start-observatory", "mas:stop": "python3 mas-observatory-control.py stop-observatory", "mas:status": "python3 mas-observatory-control.py status", "mas:kill-ports": "./scripts/kill-observatory-ports.sh", "mas:clean": "./scripts/kill-observatory-ports.sh && python3 mas-observatory-control.py stop-observatory", "kill:vite": "echo 'Killing processes on port 5173...' && lsof -ti:5173 2>/dev/null | xargs kill -9 2>/dev/null || true && pkill -f 'vite dev' 2>/dev/null || true && sleep 1 && echo 'Cleared processes'", "kill:port5173": "echo 'Force-killing all processes on port 5173...' && lsof -ti:5173 2>/dev/null | xargs kill -9 2>/dev/null || true && sleep 1 && echo 'Port 5173 is now free'", "clean:vite": "rm -rf node_modules/.vite && echo 'Cleared Vite cache'", "test:network": "./scripts/test-network.sh", "network:ip": "echo '🔍 Network access URLs:' && echo '📍 Local: http://localhost:5173' && echo '🌐 Network: Check your router settings for your computer IP' && echo '💡 Common ranges: 192.168.1.x or 192.168.0.x or 10.0.0.x' && echo '🔧 Container IP: http://**********:5173 - internal only'", "dev:debug": "npm run kill:port5173 && concurrently \"vite dev --host --port 5173 --strictPort\" \"echo 'Opening debug monitor...' && sleep 3 && echo 'Debug monitor available at: http://localhost:5173/debug-monitor.html'\" --names \"⚡,🔍\" --prefix-colors \"green,yellow\"", "firefox:debug": "./scripts/firefox-debug.sh", "firefox:monitor": "./scripts/firefox-debug.sh http://localhost:5173/debug-monitor.html", "build": "vite build", "preview": "npm run kill:port5173 && vite preview --host --port 5173 --strictPort", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:components": "vitest run --config vitest.config.basic.ts src/lib/tests/component-*.test.ts", "test:integration": "vitest run --config vitest.config.basic.ts src/lib/tests/integration-*.test.ts", "test:deploy": "vitest run --config vitest.config.basic.ts src/lib/tests/deployment-*.test.ts", "test:basic": "vitest run --config vitest.config.basic.ts src/lib/tests/basic-*.test.ts", "test:service": "vitest run --config vitest.config.basic.ts src/lib/tests/service-*.test.ts", "test:e2e": "vitest run --config vitest.config.basic.ts src/lib/tests/e2e-*.test.ts", "test:performance": "vitest run --config vitest.config.basic.ts src/lib/tests/performance-*.test.ts", "test:visual": "vitest run --config vitest.config.basic.ts src/lib/tests/visual-*.test.ts", "test:cicd": "vitest run --config vitest.config.basic.ts src/lib/tests/cicd-*.test.ts", "test:all-expanded": "vitest run --config vitest.config.basic.ts", "test:summary": "vitest run --config vitest.config.basic.ts src/lib/tests/testing-infrastructure-*.test.ts", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "lint": "prettier --check . && eslint .", "format": "prettier --write .", "type-check": "tsc --noEmit", "milestone:create": "./scripts/auto-milestone.sh create", "milestone:list": "./scripts/auto-milestone.sh list", "milestone:rollback": "./scripts/auto-milestone.sh rollback", "deploy:config": "node scripts/deploy-config.js", "deploy:staging": "npm run deploy:config staging && npm run build", "deploy:production": "npm run deploy:config production && npm run build", "monitor": "node scripts/monitor-deployment.js", "monitor:staging": "node scripts/monitor-deployment.js staging", "monitor:production": "node scripts/monitor-deployment.js production", "monitor:performance": "node scripts/performance-monitor.js start", "monitor:check": "node scripts/performance-monitor.js check", "health": "node scripts/monitor-deployment.js health", "analyze": "npm run build && npx vite-bundle-analyzer dist", "lighthouse": "npx lighthouse http://localhost:4173 --output=json --output=html --output-path=./reports/lighthouse", "lighthouse:ci": "npx lhci autorun", "perf:audit": "npm run build && npm run preview & sleep 5 && npm run lighthouse && kill %1", "security:headers": "node scripts/check-security-headers.js", "bundlesize": "npx bundlesize", "security:scan": "npm audit --audit-level=moderate", "security:fix": "npm audit fix", "rollback": "./scripts/rollback-deployment.sh", "docker:setup": "./scripts/docker-setup.sh setup", "docker:build": "./scripts/docker-build.sh prod", "docker:build:dev": "./scripts/docker-build.sh dev", "docker:build:all": "./scripts/docker-build.sh all", "docker:dev": "./scripts/docker-dev.sh start", "docker:dev:stop": "./scripts/docker-dev.sh stop", "docker:dev:logs": "./scripts/docker-dev.sh logs", "docker:dev:shell": "./scripts/docker-dev.sh shell", "docker:dev:rebuild": "./scripts/docker-dev.sh rebuild", "docker:test": "docker-compose -f docker-compose.test.yml up --abort-on-container-exit", "docker:test:e2e": "docker-compose -f docker-compose.test.yml --profile e2e up --abort-on-container-exit", "docker:scan": "./scripts/docker-build.sh scan", "docker:release": "./scripts/docker-build.sh release", "docker:clean": "./scripts/docker-dev.sh clean", "env:setup": "./scripts/setup-environment.sh", "env:dev": "./scripts/setup-environment.sh development", "env:staging": "./scripts/setup-environment.sh staging", "env:prod": "./scripts/setup-environment.sh production", "build:css": "tailwindcss -i ./src/app.css -o ./static/app.css --watch", "dev:css": "tailwindcss -i ./src/app.css -o ./static/app.css --watch", "dev:setup": "echo 'Setting up development environment...' && echo 'Checking Node.js version:' && node --version && echo 'Checking npm version:' && npm --version && echo 'Development environment ready!'", "dev:container": "docker compose -f docker-compose.development.yml up vybecoding-dev", "dev:container:build": "docker compose -f docker-compose.development.yml build vybecoding-dev", "dev:container:down": "docker compose -f docker-compose.development.yml down", "portainer:setup": "./scripts/setup-portainer.sh", "portainer:start": "docker compose -f docker-compose.development.yml up -d portainer", "portainer:stop": "docker compose -f docker-compose.development.yml stop portainer", "portainer:logs": "docker logs portainer", "portainer:open": "node scripts/auto-portainer.js", "portainer:safe": "echo '🐳 <PERSON><PERSON><PERSON> disabled in container environment - Docker socket not accessible' && sleep 1", "portainer:reset": "docker stop portainer && docker rm portainer && docker volume rm portainer_data || true && npm run portainer:open", "dev:with-portainer": "npm run kill:port5173 && npm run portainer:safe && npm run dev:simple", "dev:full": "npm run kill:port5173 && concurrently \"npm run portainer:safe\" \"vite dev --host --port 5173 --strictPort\" --names \"🐳,⚡\" --prefix-colors \"cyan,green\"", "ports:cleanup": "./scripts/cleanup-ports.sh", "ports:status": "./scripts/cleanup-ports.sh --show", "docs:api": "swagger-jsdoc -d docs/api/swagger-config.js -o docs/api/openapi.yaml src/routes/api/**/*.ts", "docs:swagger": "swagger-ui-serve docs/api/openapi.yaml", "docs:swagger-ui": "swagger-ui-dist-cli -f docs/api/openapi.yaml -d docs/api/swagger-ui", "docs:redoc": "redoc-cli build docs/api/openapi.yaml --output docs/api/redoc.html", "docs:typedoc": "typedoc --out docs/api/typescript src/lib", "docs:components": "sveld --glob \"src/lib/components/**/*.svelte\" --output docs/components", "docs:storybook": "storybook dev -p 6006", "docs:storybook:build": "storybook build -o docs/storybook", "docs:build": "npm run docs:api && npm run docs:swagger-ui && npm run docs:redoc && npm run docs:typedoc && npm run docs:components && npm run docs:storybook:build", "docs:deploy": "./scripts/deploy-docs.sh", "docs:serve": "cd docs && npm install && npm run dev", "docs:site": "vitepress dev docs", "docs:site:build": "vitepress build docs", "docs:test-links": "markdown-link-check docs/**/*.md", "docs:validate": "swagger-codegen validate -i docs/api/openapi.yaml", "docs:validate-all": "npm run docs:validate && npm run docs:test-links", "backup": "./scripts/backup-system.sh", "backup:test": "./scripts/backup-system.sh --test", "backup:cleanup": "./scripts/backup-system.sh --cleanup", "backup:security": "./scripts/backup-security-validator.sh", "backup:validate": "./scripts/backup-security-validator.sh", "recovery": "./scripts/disaster-recovery.sh", "recovery:dry-run": "DRY_RUN=true ./scripts/disaster-recovery.sh", "recovery:database": "./scripts/disaster-recovery.sh database-only", "recovery:files": "./scripts/disaster-recovery.sh files-only", "recovery:config": "./scripts/disaster-recovery.sh config-only", "perf:test": "./scripts/performance-testing.sh", "perf:load": "./scripts/performance-testing.sh load", "perf:stress": "./scripts/performance-testing.sh stress", "perf:spike": "./scripts/performance-testing.sh spike", "perf:endurance": "./scripts/performance-testing.sh endurance", "validate:no-simulations": "python3 scripts/anti-simulation-validator.py", "validate:pre-commit": "python3 scripts/anti-simulation-validator.py"}, "devDependencies": {"@axe-core/playwright": "^4.10.2", "@lhci/cli": "^0.12.0", "@melt-ui/pp": "^0.3.2", "@storybook/addon-essentials": "^7.6.6", "@storybook/addon-interactions": "^7.6.6", "@storybook/addon-links": "^7.6.6", "@storybook/blocks": "^7.6.6", "@storybook/svelte": "^7.6.6", "@storybook/testing-library": "^0.2.2", "@sveltejs/adapter-auto": "^6.0.1", "@sveltejs/kit": "^2.21.2", "@sveltejs/vite-plugin-svelte": "^5.0.3", "@types/node": "^22.15.29", "@typescript-eslint/eslint-plugin": "^8.33.0", "@typescript-eslint/parser": "^8.33.0", "@vitest/coverage-v8": "^3.1.4", "@vitest/ui": "^3.1.4", "autoprefixer": "^10.4.21", "bits-ui": "^2.4.1", "bundlesize": "^0.18.1", "clsx": "^2.1.1", "concurrently": "^9.1.2", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-svelte": "^3.9.0", "globals": "^16.2.0", "jsdom": "^26.1.0", "markdown-link-check": "^3.12.2", "postcss": "^8.5.4", "prettier": "^3.1.1", "prettier-plugin-svelte": "^3.4.0", "redoc-cli": "^0.13.21", "storybook": "^7.6.6", "sveld": "^0.19.1", "svelte": "^5.33.10", "svelte-check": "^4.2.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-dist": "^5.10.5", "swagger-ui-express": "^5.0.1", "tailwind-merge": "^3.3.0", "tailwind-variants": "^1.0.0", "tailwindcss": "^3.4.17", "tslib": "^2.4.1", "typedoc": "^0.25.13", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-bundle-analyzer": "^0.7.0", "vitepress": "^1.0.0-rc.31", "vitest": "^3.1.4"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/openai": "^1.3.22", "@melt-ui/svelte": "^0.86.6", "@modelcontextprotocol/sdk": "^1.12.1", "@monaco-editor/loader": "^1.5.0", "@opentelemetry/auto-instrumentations-node": "^0.59.0", "@opentelemetry/exporter-jaeger": "^2.0.1", "@opentelemetry/exporter-prometheus": "^0.201.1", "@opentelemetry/instrumentation-express": "^0.50.0", "@opentelemetry/instrumentation-http": "^0.201.1", "@opentelemetry/semantic-conventions": "^1.34.0", "@stripe/stripe-js": "^7.3.1", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/postcss": "^4.1.8", "@tailwindcss/typography": "^0.5.10", "@testing-library/jest-dom": "^6.6.3", "@testing-library/svelte": "^5.2.8", "@wdio/cli": "^9.15.0", "@wdio/mocha-framework": "^9.15.0", "@wdio/spec-reporter": "^9.15.0", "ai": "^4.3.16", "appwrite": "^18.1.1", "class-variance-authority": "^0.7.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "lodash-es": "^4.17.21", "lucide-svelte": "^0.511.0", "monaco-editor": "^0.52.2", "nanoid": "^5.0.7", "node-appwrite": "^17.0.0", "playwright": "^1.52.0", "pyodide": "^0.27.6", "quickjs-emscripten": "^0.31.0", "stripe": "^18.2.0", "svelte-portal": "^2.2.1", "uuid": "^11.1.0", "vite-plugin-monaco-editor": "^1.1.0", "webdriverio": "^9.15.0", "zod": "^3.23.0"}, "engines": {"node": ">=18.0.0"}, "keywords": ["ai-education", "vybe-method", "sveltekit", "appwrite", "multi-agent-system", "educational-platform"], "author": "VybeCoding.ai Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/Hiram-<PERSON>y/VybeCoding.ai.git"}}