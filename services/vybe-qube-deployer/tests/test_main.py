"""
Test suite for Vybe Qube Deployer Service
STORY-3-001: Vybe Qube Deployment Infrastructure
"""

import pytest
import asyncio
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch, AsyncMock
import json
import time

from main import app, active_deployments, DeploymentRequest, DeploymentResponse


@pytest.fixture
def client():
    """Test client for FastAPI app"""
    return TestClient(app)


@pytest.fixture
def sample_deployment_request():
    """Sample deployment request for testing"""
    return {
        "qube_id": "test-qube-001",
        "generated_files": {
            "package.json": json.dumps({
                "name": "test-vybe-qube",
                "version": "1.0.0",
                "scripts": {"build": "echo 'build complete'"}
            }),
            "index.html": "<html><body><h1>Test Vybe Qube</h1></body></html>",
            "style.css": "body { font-family: Arial; }"
        },
        "business_idea": "Test business for deployment testing",
        "subdomain": "test-deployment",
        "environment": "development"
    }


@pytest.fixture(autouse=True)
def cleanup_deployments():
    """Clean up active deployments before each test"""
    active_deployments.clear()
    yield
    active_deployments.clear()


class TestHealthEndpoint:
    """Test health check endpoint"""
    
    def test_health_check(self, client):
        """Test health endpoint returns success"""
        response = client.get("/health")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert "service" in data
        assert data["service"] == "vybe-qube-deployer"


class TestDeploymentEndpoints:
    """Test deployment-related endpoints"""
    
    def test_deploy_vybe_qube_success(self, client, sample_deployment_request):
        """Test successful deployment initiation"""
        with patch('main.deploy_qube_async') as mock_deploy:
            response = client.post("/deploy", json=sample_deployment_request)
            
            assert response.status_code == 200
            data = response.json()
            
            # Verify response structure
            assert "deployment_id" in data
            assert data["qube_id"] == sample_deployment_request["qube_id"]
            assert data["status"] == "initializing"
            assert data["progress"] == 0
            assert data["current_phase"] == "preparing"
            assert data["subdomain"] == "test-qube-001.vybequbes.com"
            assert data["url"] == "https://test-qube-001.vybequbes.com"
            assert data["ssl_status"] == "pending"
            
            # Verify deployment is tracked
            deployment_id = data["deployment_id"]
            assert deployment_id in active_deployments
            
            # Verify background task was started
            mock_deploy.assert_called_once()
    
    def test_deploy_invalid_request(self, client):
        """Test deployment with invalid request data"""
        invalid_request = {
            "qube_id": "",  # Invalid empty qube_id
            "generated_files": {},
            "business_idea": "Test"
        }
        
        response = client.post("/deploy", json=invalid_request)
        assert response.status_code == 422  # Validation error
    
    def test_get_deployment_status_success(self, client, sample_deployment_request):
        """Test getting deployment status"""
        # First create a deployment
        with patch('main.deploy_qube_async'):
            deploy_response = client.post("/deploy", json=sample_deployment_request)
            deployment_id = deploy_response.json()["deployment_id"]
        
        # Get deployment status
        response = client.get(f"/deployments/{deployment_id}")
        assert response.status_code == 200
        
        data = response.json()
        assert data["deployment_id"] == deployment_id
        assert data["qube_id"] == sample_deployment_request["qube_id"]
        assert data["status"] == "initializing"
    
    def test_get_deployment_status_not_found(self, client):
        """Test getting status for non-existent deployment"""
        response = client.get("/deployments/non-existent-id")
        assert response.status_code == 404
        
        data = response.json()
        assert "not found" in data["detail"].lower()
    
    def test_list_deployments_empty(self, client):
        """Test listing deployments when none exist"""
        response = client.get("/deployments")
        assert response.status_code == 200
        
        data = response.json()
        assert isinstance(data, list)
        assert len(data) == 0
    
    def test_list_deployments_with_data(self, client, sample_deployment_request):
        """Test listing deployments with existing data"""
        # Create multiple deployments
        with patch('main.deploy_qube_async'):
            for i in range(3):
                request = sample_deployment_request.copy()
                request["qube_id"] = f"test-qube-{i:03d}"
                client.post("/deploy", json=request)
        
        # List deployments
        response = client.get("/deployments")
        assert response.status_code == 200
        
        data = response.json()
        assert isinstance(data, list)
        assert len(data) == 3
        
        # Verify all deployments are present
        qube_ids = [d["qube_id"] for d in data]
        for i in range(3):
            assert f"test-qube-{i:03d}" in qube_ids
    
    def test_delete_deployment_success(self, client, sample_deployment_request):
        """Test successful deployment deletion"""
        # Create a deployment
        with patch('main.deploy_qube_async'):
            deploy_response = client.post("/deploy", json=sample_deployment_request)
            deployment_id = deploy_response.json()["deployment_id"]
        
        # Mock Docker and DNS cleanup
        with patch('main.cleanup_deployment_resources') as mock_cleanup:
            mock_cleanup.return_value = True
            
            # Delete deployment
            response = client.delete(f"/deployments/{deployment_id}")
            assert response.status_code == 200
            
            data = response.json()
            assert data["message"] == "Deployment deleted successfully"
            assert data["deployment_id"] == deployment_id
            
            # Verify deployment is removed from active deployments
            assert deployment_id not in active_deployments
            
            # Verify cleanup was called
            mock_cleanup.assert_called_once_with(deployment_id)
    
    def test_delete_deployment_not_found(self, client):
        """Test deleting non-existent deployment"""
        response = client.delete("/deployments/non-existent-id")
        assert response.status_code == 404
        
        data = response.json()
        assert "not found" in data["detail"].lower()


class TestDeploymentProcess:
    """Test the deployment process functions"""
    
    @pytest.mark.asyncio
    async def test_create_deployment_files(self, sample_deployment_request):
        """Test deployment file creation"""
        from main import create_deployment_files
        
        deployment_id = "test-deploy-001"
        request = DeploymentRequest(**sample_deployment_request)
        
        with patch('pathlib.Path.mkdir') as mock_mkdir, \
             patch('pathlib.Path.write_text') as mock_write:
            
            deployment_path = await create_deployment_files(deployment_id, request)
            
            # Verify directory creation
            mock_mkdir.assert_called()
            
            # Verify files were written
            assert mock_write.call_count >= 3  # At least package.json, Dockerfile, nginx.conf
    
    @pytest.mark.asyncio
    async def test_update_deployment_status(self):
        """Test deployment status updates"""
        from main import update_deployment_status
        
        deployment_id = "test-deploy-001"
        active_deployments[deployment_id] = {
            "deployment_id": deployment_id,
            "status": "initializing",
            "progress": 0,
            "logs": []
        }
        
        await update_deployment_status(deployment_id, "building", 25, "Building Docker image")
        
        deployment = active_deployments[deployment_id]
        assert deployment["status"] == "building"
        assert deployment["progress"] == 25
        assert deployment["current_phase"] == "building"
        assert len(deployment["logs"]) == 1
        assert "Building Docker image" in deployment["logs"][0]


class TestDockerIntegration:
    """Test Docker-related functionality"""
    
    def test_generate_dockerfile(self):
        """Test Dockerfile generation"""
        from main import generate_dockerfile

        dockerfile = generate_dockerfile("sveltekit")

        assert "FROM node:" in dockerfile
        assert "COPY package*.json" in dockerfile
        assert "npm ci" in dockerfile
        assert "npm run build" in dockerfile
        assert "EXPOSE" in dockerfile
    
    def test_generate_nginx_config(self):
        """Test nginx configuration generation"""
        from main import generate_nginx_config

        qube_id = "test-qube-001"
        nginx_config = generate_nginx_config(qube_id)

        assert "server {" in nginx_config
        assert "listen 80;" in nginx_config
        assert "location /" in nginx_config
        assert "root /usr/share/nginx/html" in nginx_config


class TestErrorHandling:
    """Test error handling scenarios"""
    
    def test_deployment_with_missing_files(self, client):
        """Test deployment with missing required files"""
        invalid_request = {
            "qube_id": "test-qube-001",
            "generated_files": {},  # Empty files
            "business_idea": "Test business"
        }
        
        response = client.post("/deploy", json=invalid_request)
        # Should still accept but may fail during deployment
        assert response.status_code in [200, 422]
    
    @pytest.mark.asyncio
    async def test_deployment_failure_handling(self, sample_deployment_request):
        """Test handling of deployment failures"""
        from main import deploy_qube_async, active_deployments
        
        deployment_id = "test-deploy-fail"
        request = DeploymentRequest(**sample_deployment_request)
        
        # Add deployment to active deployments
        active_deployments[deployment_id] = {
            "deployment_id": deployment_id,
            "status": "initializing",
            "progress": 0,
            "logs": []
        }
        
        # Mock a failure in Docker build
        with patch('main.build_docker_image') as mock_build:
            mock_build.side_effect = Exception("Docker build failed")
            
            # Run deployment (should handle the exception)
            await deploy_qube_async(deployment_id, request)
            
            # Verify failure was recorded
            deployment = active_deployments[deployment_id]
            assert deployment["status"] == "failed"
            assert any("failed" in log.lower() for log in deployment["logs"])


class TestPerformanceAndLimits:
    """Test performance and resource limits"""
    
    def test_concurrent_deployment_limit(self, client, sample_deployment_request):
        """Test that concurrent deployments are properly limited"""
        # This would require more complex setup to test actual concurrency limits
        # For now, just verify the endpoint accepts multiple requests
        
        with patch('main.deploy_qube_async'):
            responses = []
            for i in range(3):
                request = sample_deployment_request.copy()
                request["qube_id"] = f"concurrent-test-{i}"
                response = client.post("/deploy", json=request)
                responses.append(response)
            
            # All should succeed (actual limiting would be in the background process)
            for response in responses:
                assert response.status_code == 200
    
    def test_deployment_timeout_handling(self):
        """Test deployment timeout scenarios"""
        # This would require async testing with actual timeouts
        # For now, verify the timeout configuration exists
        from main import DEPLOYMENT_TIMEOUT
        assert DEPLOYMENT_TIMEOUT > 0


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
