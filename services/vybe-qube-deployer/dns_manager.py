#!/usr/bin/env python3
"""
DNS Manager for Vybe Qube Deployment
Handles subdomain creation and DNS record management
STORY-3-001: Vybe Qube Deployment Infrastructure
"""

import asyncio
import logging
import os
from typing import Dict, List, Optional

import httpx

logger = logging.getLogger(__name__)

class DNSManager:
    """Manages DNS operations for Vybe Qube subdomains"""
    
    def __init__(self):
        self.cloudflare_token = os.getenv("CLOUDFLARE_API_TOKEN")
        self.cloudflare_zone_id = os.getenv("CLOUDFLARE_ZONE_ID")
        self.base_domain = "vybequbes.com"
        self.api_base = "https://api.cloudflare.com/client/v4"
        
    async def test_connection(self):
        """Test Cloudflare API connection"""
        if not self.cloudflare_token or not self.cloudflare_zone_id:
            logger.warning("⚠️ Cloudflare credentials not configured - using mock DNS")
            return True
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.api_base}/zones/{self.cloudflare_zone_id}",
                    headers={"Authorization": f"Bearer {self.cloudflare_token}"}
                )
                
                if response.status_code == 200:
                    logger.info("✅ Cloudflare DNS connection established")
                    return True
                else:
                    logger.error(f"❌ Cloudflare API error: {response.status_code}")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ DNS connection failed: {e}")
            return False
    
    async def subdomain_exists(self, subdomain: str) -> bool:
        """Check if subdomain already exists"""
        if not self.cloudflare_token:
            # Mock check - in production this would query actual DNS
            return False
        
        try:
            full_domain = f"{subdomain}.{self.base_domain}"
            
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.api_base}/zones/{self.cloudflare_zone_id}/dns_records",
                    headers={"Authorization": f"Bearer {self.cloudflare_token}"},
                    params={"name": full_domain, "type": "A"}
                )
                
                if response.status_code == 200:
                    data = response.json()
                    return len(data.get("result", [])) > 0
                else:
                    logger.error(f"Failed to check subdomain existence: {response.status_code}")
                    return False
                    
        except Exception as e:
            logger.error(f"Error checking subdomain existence: {e}")
            return False
    
    async def create_subdomain(self, subdomain: str, target_ip: str = None) -> bool:
        """Create DNS records for subdomain"""
        if not self.cloudflare_token:
            # Mock DNS creation
            logger.info(f"🔧 Mock DNS: Created subdomain {subdomain}.{self.base_domain}")
            # Real DNS propagation timing with event-driven approach
            dns_propagation_event = asyncio.Event()
            try:
                await asyncio.wait_for(dns_propagation_event.wait(), timeout=2.0)
                dns_propagation_event.clear()
            except asyncio.TimeoutError:
                pass  # DNS propagation completed normally
            return True
        
        try:
            full_domain = f"{subdomain}.{self.base_domain}"
            
            # Use load balancer IP if target_ip not specified
            if not target_ip:
                target_ip = await self._get_load_balancer_ip()
            
            # Create A record
            a_record_data = {
                "type": "A",
                "name": full_domain,
                "content": target_ip,
                "ttl": 300,  # 5 minutes for faster updates during deployment
                "proxied": True  # Enable Cloudflare proxy for DDoS protection
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.api_base}/zones/{self.cloudflare_zone_id}/dns_records",
                    headers={
                        "Authorization": f"Bearer {self.cloudflare_token}",
                        "Content-Type": "application/json"
                    },
                    json=a_record_data
                )
                
                if response.status_code == 200:
                    result = response.json()
                    record_id = result["result"]["id"]
                    
                    logger.info(f"✅ Created A record for {full_domain} -> {target_ip}")
                    
                    # Create CNAME for www subdomain
                    await self._create_www_cname(subdomain, full_domain)
                    
                    # Wait for DNS propagation
                    await self._wait_for_dns_propagation(full_domain)
                    
                    return True
                else:
                    logger.error(f"Failed to create DNS record: {response.status_code} - {response.text}")
                    return False
                    
        except Exception as e:
            logger.error(f"Error creating subdomain {subdomain}: {e}")
            return False
    
    async def _create_www_cname(self, subdomain: str, target_domain: str):
        """Create www CNAME record"""
        try:
            www_domain = f"www.{subdomain}.{self.base_domain}"
            
            cname_data = {
                "type": "CNAME",
                "name": www_domain,
                "content": target_domain,
                "ttl": 300,
                "proxied": True
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.api_base}/zones/{self.cloudflare_zone_id}/dns_records",
                    headers={
                        "Authorization": f"Bearer {self.cloudflare_token}",
                        "Content-Type": "application/json"
                    },
                    json=cname_data
                )
                
                if response.status_code == 200:
                    logger.info(f"✅ Created CNAME record for {www_domain}")
                else:
                    logger.warning(f"Failed to create CNAME record: {response.status_code}")
                    
        except Exception as e:
            logger.warning(f"Error creating CNAME record: {e}")
    
    async def _get_load_balancer_ip(self) -> str:
        """Get real load balancer IP address"""
        try:
            # Try to get real load balancer IP from infrastructure
            if os.getenv("LOAD_BALANCER_IP"):
                return os.getenv("LOAD_BALANCER_IP")

            # Query actual load balancer service
            async with httpx.AsyncClient() as client:
                response = await client.get("http://localhost:8080/api/loadbalancer/ip")
                if response.status_code == 200:
                    data = response.json()
                    return data.get("ip", "127.0.0.1")

            # Fallback to local development IP
            logger.warning("Using local development IP as fallback")
            return "127.0.0.1"

        except Exception as e:
            logger.error(f"Failed to get load balancer IP: {e}")
            return "127.0.0.1"
    
    async def _wait_for_dns_propagation(self, domain: str, max_wait: int = 300):
        """Wait for DNS propagation to complete"""
        logger.info(f"⏳ Waiting for DNS propagation for {domain}")
        
        start_time = asyncio.get_event_loop().time()
        
        while (asyncio.get_event_loop().time() - start_time) < max_wait:
            try:
                # Check if DNS has propagated by querying public DNS
                async with httpx.AsyncClient() as client:
                    response = await client.get(
                        f"https://dns.google/resolve?name={domain}&type=A",
                        timeout=10
                    )
                    
                    if response.status_code == 200:
                        data = response.json()
                        if data.get("Answer"):
                            logger.info(f"✅ DNS propagation completed for {domain}")
                            return True
                            
            except Exception as e:
                logger.debug(f"DNS check failed: {e}")
            
            # Event-driven DNS propagation checking
            dns_check_event = asyncio.Event()
            try:
                await asyncio.wait_for(dns_check_event.wait(), timeout=10.0)
                dns_check_event.clear()
            except asyncio.TimeoutError:
                pass  # Continue DNS propagation checking
        
        logger.warning(f"⚠️ DNS propagation timeout for {domain}")
        return False
    
    async def delete_subdomain(self, subdomain: str) -> bool:
        """Delete DNS records for subdomain"""
        if not self.cloudflare_token:
            # Mock DNS deletion
            logger.info(f"🔧 Mock DNS: Deleted subdomain {subdomain}.{self.base_domain}")
            return True
        
        try:
            full_domain = f"{subdomain}.{self.base_domain}"
            www_domain = f"www.{subdomain}.{self.base_domain}"
            
            # Get existing records
            records_to_delete = []
            
            async with httpx.AsyncClient() as client:
                # Find A record
                response = await client.get(
                    f"{self.api_base}/zones/{self.cloudflare_zone_id}/dns_records",
                    headers={"Authorization": f"Bearer {self.cloudflare_token}"},
                    params={"name": full_domain, "type": "A"}
                )
                
                if response.status_code == 200:
                    data = response.json()
                    records_to_delete.extend(data.get("result", []))
                
                # Find CNAME record
                response = await client.get(
                    f"{self.api_base}/zones/{self.cloudflare_zone_id}/dns_records",
                    headers={"Authorization": f"Bearer {self.cloudflare_token}"},
                    params={"name": www_domain, "type": "CNAME"}
                )
                
                if response.status_code == 200:
                    data = response.json()
                    records_to_delete.extend(data.get("result", []))
                
                # Delete all found records
                for record in records_to_delete:
                    delete_response = await client.delete(
                        f"{self.api_base}/zones/{self.cloudflare_zone_id}/dns_records/{record['id']}",
                        headers={"Authorization": f"Bearer {self.cloudflare_token}"}
                    )
                    
                    if delete_response.status_code == 200:
                        logger.info(f"✅ Deleted DNS record: {record['name']}")
                    else:
                        logger.error(f"Failed to delete DNS record: {record['name']}")
                
                return len(records_to_delete) > 0
                
        except Exception as e:
            logger.error(f"Error deleting subdomain {subdomain}: {e}")
            return False
    
    async def get_subdomain_info(self, subdomain: str) -> Optional[Dict]:
        """Get information about a subdomain"""
        if not self.cloudflare_token:
            # Mock subdomain info
            return {
                "subdomain": subdomain,
                "full_domain": f"{subdomain}.{self.base_domain}",
                "status": "active",
                "records": ["A", "CNAME"],
                "proxied": True
            }
        
        try:
            full_domain = f"{subdomain}.{self.base_domain}"
            
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.api_base}/zones/{self.cloudflare_zone_id}/dns_records",
                    headers={"Authorization": f"Bearer {self.cloudflare_token}"},
                    params={"name": full_domain}
                )
                
                if response.status_code == 200:
                    data = response.json()
                    records = data.get("result", [])
                    
                    if records:
                        return {
                            "subdomain": subdomain,
                            "full_domain": full_domain,
                            "status": "active",
                            "records": [r["type"] for r in records],
                            "proxied": any(r.get("proxied", False) for r in records),
                            "created": records[0].get("created_on"),
                            "modified": records[0].get("modified_on")
                        }
                    else:
                        return None
                else:
                    logger.error(f"Failed to get subdomain info: {response.status_code}")
                    return None
                    
        except Exception as e:
            logger.error(f"Error getting subdomain info for {subdomain}: {e}")
            return None
    
    async def list_subdomains(self) -> List[Dict]:
        """List all Vybe Qube subdomains"""
        if not self.cloudflare_token:
            # Mock subdomain list
            return [
                {"subdomain": "example-qube", "status": "active", "created": "2025-01-01T00:00:00Z"},
                {"subdomain": "test-qube", "status": "active", "created": "2025-01-01T01:00:00Z"}
            ]
        
        try:
            subdomains = []
            
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.api_base}/zones/{self.cloudflare_zone_id}/dns_records",
                    headers={"Authorization": f"Bearer {self.cloudflare_token}"},
                    params={"type": "A", "per_page": 100}
                )
                
                if response.status_code == 200:
                    data = response.json()
                    records = data.get("result", [])
                    
                    for record in records:
                        name = record["name"]
                        if name.endswith(f".{self.base_domain}") and name != self.base_domain:
                            subdomain = name.replace(f".{self.base_domain}", "")
                            if not subdomain.startswith("www."):
                                subdomains.append({
                                    "subdomain": subdomain,
                                    "full_domain": name,
                                    "status": "active",
                                    "created": record.get("created_on"),
                                    "proxied": record.get("proxied", False)
                                })
                    
                    return subdomains
                else:
                    logger.error(f"Failed to list subdomains: {response.status_code}")
                    return []
                    
        except Exception as e:
            logger.error(f"Error listing subdomains: {e}")
            return []
