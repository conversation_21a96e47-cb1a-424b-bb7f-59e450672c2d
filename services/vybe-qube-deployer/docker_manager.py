#!/usr/bin/env python3
"""
Docker Manager for Vybe Qube Deployment
Handles Docker container creation, management, and deployment
STORY-3-001: Vybe Qube Deployment Infrastructure
"""

import asyncio
import json
import logging
import os
import tempfile
from pathlib import Path
from typing import Dict, List, Optional

import docker
from docker.errors import APIError, NotFound

logger = logging.getLogger(__name__)

class DockerManager:
    """Manages Docker operations for Vybe Qube deployment"""
    
    def __init__(self):
        self.client = None
        self.network_name = "vybe-deployment"
        
    async def test_connection(self):
        """Test Docker connection and initialize client"""
        try:
            self.client = docker.from_env()
            self.client.ping()
            
            # Ensure deployment network exists
            await self._ensure_network_exists()
            
            logger.info("✅ Docker connection established")
            return True
            
        except Exception as e:
            logger.error(f"❌ Docker connection failed: {e}")
            self.client = None
            return False
    
    async def _ensure_network_exists(self):
        """Ensure the deployment network exists"""
        try:
            self.client.networks.get(self.network_name)
            logger.info(f"✅ Network {self.network_name} exists")
        except NotFound:
            self.client.networks.create(
                self.network_name,
                driver="bridge",
                labels={"vybe-qube": "deployment-network"}
            )
            logger.info(f"✅ Created network {self.network_name}")
    
    async def prepare_container_config(self, qube_id: str, generated_files: Dict[str, str], business_idea: str) -> Dict:
        """Prepare container configuration for deployment"""
        
        # Create temporary directory for build context
        temp_dir = Path(tempfile.mkdtemp(prefix=f"vybe-qube-{qube_id}-"))
        
        try:
            # Write generated files to temp directory
            for file_path, content in generated_files.items():
                full_path = temp_dir / file_path
                full_path.parent.mkdir(parents=True, exist_ok=True)
                full_path.write_text(content, encoding='utf-8')
            
            # Generate optimized Dockerfile
            dockerfile_content = self._generate_dockerfile(qube_id, business_idea)
            (temp_dir / "Dockerfile").write_text(dockerfile_content)
            
            # Generate nginx configuration
            nginx_config = self._generate_nginx_config(qube_id)
            (temp_dir / "nginx.conf").write_text(nginx_config)
            
            # Generate package.json if not exists
            if not (temp_dir / "package.json").exists():
                package_json = self._generate_package_json(qube_id, business_idea)
                (temp_dir / "package.json").write_text(package_json)
            
            # Generate docker-compose override for production
            compose_override = self._generate_compose_override(qube_id)
            (temp_dir / "docker-compose.override.yml").write_text(compose_override)
            
            return {
                "build_context": str(temp_dir),
                "image_tag": f"vybe-qube-{qube_id}:latest",
                "container_name": f"vybe-qube-{qube_id}",
                "temp_dir": temp_dir
            }
            
        except Exception as e:
            # Clean up temp directory on error
            import shutil
            shutil.rmtree(temp_dir, ignore_errors=True)
            raise Exception(f"Failed to prepare container config: {e}")
    
    def _generate_dockerfile(self, qube_id: str, business_idea: str) -> str:
        """Generate optimized Dockerfile for Vybe Qube"""
        return f"""# Vybe Qube Dockerfile - {qube_id}
# Generated for: {business_idea[:50]}...

# Build stage
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production --silent

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production stage
FROM nginx:alpine

# Copy built application
COPY --from=builder /app/build /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Add health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \\
    CMD wget --no-verbose --tries=1 --spider http://localhost/health || exit 1

# Expose port
EXPOSE 80

# Add labels for identification
LABEL vybe-qube-id="{qube_id}"
LABEL vybe-qube-business="{business_idea[:100]}"
LABEL vybe-qube-created="{asyncio.get_event_loop().time()}"

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
"""
    
    def _generate_nginx_config(self, qube_id: str) -> str:
        """Generate nginx configuration for the Vybe Qube"""
        return f"""# Nginx configuration for Vybe Qube {qube_id}

events {{
    worker_connections 1024;
    use epoll;
    multi_accept on;
}}

http {{
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # Performance optimizations
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    server {{
        listen 80;
        server_name {qube_id}.vybequbes.com;
        root /usr/share/nginx/html;
        index index.html;
        
        # Main application
        location / {{
            try_files $uri $uri/ /index.html;
            
            # Cache static assets
            location ~* \\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {{
                expires 1y;
                add_header Cache-Control "public, immutable";
            }}
        }}
        
        # Health check endpoint
        location /health {{
            access_log off;
            return 200 "healthy\\n";
            add_header Content-Type text/plain;
        }}
        
        # API proxy (if needed)
        location /api/ {{
            proxy_pass http://localhost:3001/;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
        }}
        
        # Error pages
        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;
        
        location = /50x.html {{
            root /usr/share/nginx/html;
        }}
    }}
}}
"""
    
    def _generate_package_json(self, qube_id: str, business_idea: str) -> str:
        """Generate package.json for the Vybe Qube"""
        package_data = {
            "name": f"vybe-qube-{qube_id}",
            "version": "1.0.0",
            "description": f"AI-generated website: {business_idea[:100]}",
            "private": True,
            "scripts": {
                "dev": "vite dev",
                "build": "vite build",
                "preview": "vite preview",
                "start": "node build"
            },
            "devDependencies": {
                "@sveltejs/adapter-auto": "^3.0.0",
                "@sveltejs/kit": "^2.0.0",
                "@sveltejs/vite-plugin-svelte": "^5.0.0",
                "svelte": "^5.0.0",
                "vite": "^6.0.0",
                "tailwindcss": "^4.0.0",
                "autoprefixer": "^10.4.0",
                "postcss": "^8.4.0"
            },
            "dependencies": {
                "stripe": "^14.0.0",
                "@stripe/stripe-js": "^2.0.0"
            },
            "type": "module",
            "engines": {
                "node": ">=18.0.0"
            }
        }
        
        return json.dumps(package_data, indent=2)
    
    def _generate_compose_override(self, qube_id: str) -> str:
        """Generate docker-compose override for production deployment"""
        return f"""# Docker Compose override for Vybe Qube {qube_id}
version: '3.8'

services:
  vybe-qube-{qube_id}:
    restart: unless-stopped
    networks:
      - {self.network_name}
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.{qube_id}.rule=Host(`{qube_id}.vybequbes.com`)"
      - "traefik.http.routers.{qube_id}.tls=true"
      - "traefik.http.routers.{qube_id}.tls.certresolver=letsencrypt"
      - "vybe-qube.id={qube_id}"
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  {self.network_name}:
    external: true
"""
    
    async def build_image(self, qube_id: str, container_config: Dict) -> str:
        """Build Docker image for the Vybe Qube"""
        if not self.client:
            raise Exception("Docker client not initialized")
        
        build_context = container_config["build_context"]
        image_tag = container_config["image_tag"]
        
        try:
            logger.info(f"Building Docker image {image_tag} from {build_context}")
            
            # Build the image
            image, build_logs = self.client.images.build(
                path=build_context,
                tag=image_tag,
                rm=True,
                forcerm=True,
                pull=True,
                labels={
                    "vybe-qube-id": qube_id,
                    "vybe-qube-created": str(asyncio.get_event_loop().time())
                }
            )
            
            # Log build output
            for log in build_logs:
                if 'stream' in log:
                    logger.debug(f"Build: {log['stream'].strip()}")
            
            logger.info(f"✅ Successfully built image {image_tag}")
            return image_tag
            
        except Exception as e:
            logger.error(f"❌ Failed to build image {image_tag}: {e}")
            raise Exception(f"Docker build failed: {e}")
        finally:
            # Clean up temp directory
            import shutil
            temp_dir = container_config.get("temp_dir")
            if temp_dir and temp_dir.exists():
                shutil.rmtree(temp_dir, ignore_errors=True)
    
    async def deploy_container(self, qube_id: str, image_tag: str, subdomain: str, ssl_cert: Optional[Dict] = None) -> str:
        """Deploy the Docker container"""
        if not self.client:
            raise Exception("Docker client not initialized")
        
        container_name = f"vybe-qube-{qube_id}"
        
        try:
            # Remove existing container if it exists
            await self._remove_existing_container(container_name)
            
            # Container configuration
            container_config = {
                "image": image_tag,
                "name": container_name,
                "ports": {'80/tcp': None},  # Let Docker assign port
                "network": self.network_name,
                "detach": True,
                "restart_policy": {"Name": "unless-stopped"},
                "labels": {
                    "vybe-qube-id": qube_id,
                    "vybe-qube-subdomain": subdomain,
                    "vybe-qube-deployed": str(asyncio.get_event_loop().time()),
                    "traefik.enable": "true",
                    f"traefik.http.routers.{qube_id}.rule": f"Host(`{subdomain}`)",
                    f"traefik.http.routers.{qube_id}.tls": "true",
                    f"traefik.http.routers.{qube_id}.tls.certresolver": "letsencrypt"
                },
                "environment": {
                    "VYBE_QUBE_ID": qube_id,
                    "VYBE_SUBDOMAIN": subdomain,
                    "NODE_ENV": "production"
                }
            }
            
            # Create and start container
            container = self.client.containers.run(**container_config)
            
            logger.info(f"✅ Container deployed: {container.id[:12]} for {subdomain}")
            return container.id
            
        except Exception as e:
            logger.error(f"❌ Failed to deploy container for {qube_id}: {e}")
            raise Exception(f"Container deployment failed: {e}")
    
    async def _remove_existing_container(self, container_name: str):
        """Remove existing container if it exists"""
        try:
            existing = self.client.containers.get(container_name)
            existing.stop(timeout=10)
            existing.remove()
            logger.info(f"Removed existing container: {container_name}")
        except NotFound:
            pass  # Container doesn't exist, which is fine
        except Exception as e:
            logger.warning(f"Failed to remove existing container {container_name}: {e}")
    
    async def remove_container(self, qube_id: str) -> bool:
        """Remove a deployed container"""
        if not self.client:
            return False
        
        container_name = f"vybe-qube-{qube_id}"
        
        try:
            await self._remove_existing_container(container_name)
            
            # Also remove the image
            image_tag = f"vybe-qube-{qube_id}:latest"
            try:
                self.client.images.remove(image_tag, force=True)
                logger.info(f"Removed image: {image_tag}")
            except Exception as e:
                logger.warning(f"Failed to remove image {image_tag}: {e}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to remove container for {qube_id}: {e}")
            return False
    
    async def get_container_status(self, qube_id: str) -> Optional[Dict]:
        """Get status of a deployed container"""
        if not self.client:
            return None
        
        container_name = f"vybe-qube-{qube_id}"
        
        try:
            container = self.client.containers.get(container_name)
            
            return {
                "id": container.id,
                "name": container.name,
                "status": container.status,
                "created": container.attrs["Created"],
                "ports": container.ports,
                "labels": container.labels
            }
            
        except NotFound:
            return None
        except Exception as e:
            logger.error(f"Failed to get container status for {qube_id}: {e}")
            return None
