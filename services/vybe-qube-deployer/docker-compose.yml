# Docker Compose for Vybe Qube Deployer Service
# STORY-3-001: Vybe Qube Deployment Infrastructure

version: '3.8'

services:
  vybe-qube-deployer:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: vybe-qube-deployer
    restart: unless-stopped
    ports:
      - '8002:8002'
    environment:
      # Service configuration
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1

      # Cloudflare DNS configuration
      - CLOUDFLARE_API_TOKEN=${CLOUDFLARE_API_TOKEN}
      - CLOUDFLARE_ZONE_ID=${CLOUDFLARE_ZONE_ID}

      # SSL configuration
      - SSL_EMAIL=${SSL_EMAIL:-<EMAIL>}
      - SSL_STAGING=${SSL_STAGING:-false}

      # Load balancer configuration
      - LOAD_BALANCER_IP=${LOAD_BALANCER_IP:-*************}

      # Deployment configuration
      - DEPLOYMENT_DOMAIN=${DEPLOYMENT_DOMAIN:-vybequbes.com}
      - MAX_CONCURRENT_DEPLOYMENTS=${MAX_CONCURRENT_DEPLOYMENTS:-5}

      # Monitoring
      - PROMETHEUS_ENABLED=${PROMETHEUS_ENABLED:-true}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}

    volumes:
      # Docker socket for container management
      - /var/run/docker.sock:/var/run/docker.sock

      # SSL certificates storage
      - ssl_certificates:/etc/letsencrypt

      # Deployment logs
      - deployment_logs:/app/logs

      # Temporary build contexts
      - build_contexts:/tmp/vybe-builds

    networks:
      - vybe-deployment
      - vybe-internal

    depends_on:
      - redis
      - prometheus

    labels:
      - 'traefik.enable=true'
      - 'traefik.http.routers.deployer.rule=Host(`deployer.vybecoding.ai`)'
      - 'traefik.http.routers.deployer.tls=true'
      - 'traefik.http.routers.deployer.tls.certresolver=letsencrypt'
      - 'traefik.http.services.deployer.loadbalancer.server.port=8002'

    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:8002/health']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis for deployment state management
  redis:
    image: redis:7-alpine
    container_name: vybe-deployer-redis
    restart: unless-stopped
    ports:
      - '6379:6379'
    volumes:
      - redis_data:/data
    networks:
      - vybe-internal
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru

    healthcheck:
      test: ['CMD', 'redis-cli', 'ping']
      interval: 30s
      timeout: 5s
      retries: 3

  # Prometheus for metrics collection
  prometheus:
    image: prom/prometheus:latest
    container_name: vybe-deployer-prometheus
    restart: unless-stopped
    ports:
      - '9090:9090'
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - vybe-internal
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

  # Grafana for deployment monitoring
  grafana:
    image: grafana/grafana:latest
    container_name: vybe-deployer-grafana
    restart: unless-stopped
    ports:
      - '3000:3000'
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - vybe-internal
    depends_on:
      - prometheus

  # Traefik reverse proxy for deployed sites
  traefik:
    image: traefik:v3.0
    container_name: vybe-deployer-traefik
    restart: unless-stopped
    ports:
      - '80:80'
      - '443:443'
      - '8080:8080' # Traefik dashboard
    environment:
      - CLOUDFLARE_EMAIL=${CLOUDFLARE_EMAIL}
      - CLOUDFLARE_API_KEY=${CLOUDFLARE_API_KEY}
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./traefik/traefik.yml:/etc/traefik/traefik.yml
      - ./traefik/dynamic:/etc/traefik/dynamic
      - traefik_certificates:/certificates
    networks:
      - vybe-deployment
      - vybe-internal
    labels:
      - 'traefik.enable=true'
      - 'traefik.http.routers.traefik.rule=Host(`traefik.vybecoding.ai`)'
      - 'traefik.http.routers.traefik.tls=true'
      - 'traefik.http.routers.traefik.service=api@internal'

  # Watchtower for automatic updates
  watchtower:
    image: containrrr/watchtower
    container_name: vybe-deployer-watchtower
    restart: unless-stopped
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - WATCHTOWER_CLEANUP=true
      - WATCHTOWER_POLL_INTERVAL=3600 # Check every hour
      - WATCHTOWER_INCLUDE_STOPPED=true
    networks:
      - vybe-internal

volumes:
  ssl_certificates:
    driver: local
  deployment_logs:
    driver: local
  build_contexts:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  traefik_certificates:
    driver: local

networks:
  vybe-deployment:
    driver: bridge
    name: vybe-deployment
    labels:
      - 'vybe-qube.network=deployment'

  vybe-internal:
    driver: bridge
    name: vybe-internal
    internal: true
    labels:
      - 'vybe-qube.network=internal'
