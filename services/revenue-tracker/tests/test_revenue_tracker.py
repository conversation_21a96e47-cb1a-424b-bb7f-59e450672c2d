#!/usr/bin/env python3
"""
Tests for VybeCoding.ai Revenue Tracking Service
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from decimal import Decimal
from unittest.mock import Mock, AsyncMock, patch
import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from main import RevenueTracker, RevenueTransaction
from stripe_integration import StripeIntegration
from analytics_engine import AnalyticsEngine
from payout_manager import PayoutManager

class TestRevenueTracker:
    """Test cases for RevenueTracker class"""
    
    @pytest.fixture
    def revenue_tracker(self):
        """Create a RevenueTracker instance for testing"""
        return RevenueTracker()
    
    @pytest.fixture
    def sample_transaction(self):
        """Create a sample transaction for testing"""
        return RevenueTransaction(
            transaction_id="test_txn_123",
            qube_id="qube_456",
            user_id="user_789",
            amount=100.0,
            currency="usd",
            stripe_payment_intent_id="pi_test_123",
            status="completed",
            created_at=datetime.now(),
            metadata={"test": "data"}
        )
    
    @pytest.mark.asyncio
    async def test_record_transaction(self, revenue_tracker, sample_transaction):
        """Test recording a revenue transaction"""
        with patch.object(revenue_tracker, 'databases') as mock_db:
            mock_db.create_document = AsyncMock(return_value={'$id': 'test_doc_id'})
            mock_db.update_analytics = AsyncMock()
            
            result = await revenue_tracker.record_transaction(sample_transaction)
            
            assert result == 'test_doc_id'
            mock_db.create_document.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_user_revenue(self, revenue_tracker):
        """Test getting user revenue summary"""
        mock_transactions = {
            'documents': [
                {
                    'userRevenue': 50.0,
                    'qubeId': 'qube_1',
                    'createdAt': datetime.now().isoformat()
                },
                {
                    'userRevenue': 75.0,
                    'qubeId': 'qube_2',
                    'createdAt': datetime.now().isoformat()
                }
            ]
        }
        
        with patch.object(revenue_tracker, 'databases') as mock_db:
            mock_db.list_documents = AsyncMock(return_value=mock_transactions)
            
            result = await revenue_tracker.get_user_revenue('test_user', 30)
            
            assert result['totalRevenue'] == 125.0
            assert result['transactionCount'] == 2
            assert result['averageTransaction'] == 62.5
            assert len(result['topQubes']) == 2

class TestStripeIntegration:
    """Test cases for StripeIntegration class"""
    
    @pytest.fixture
    def stripe_integration(self):
        """Create a StripeIntegration instance for testing"""
        return StripeIntegration()
    
    @pytest.mark.asyncio
    async def test_create_connect_account(self, stripe_integration):
        """Test creating a Stripe Connect account"""
        with patch('stripe.Account.create') as mock_create:
            mock_create.return_value = Mock(id='acct_test_123')
            
            with patch.object(stripe_integration, 'databases') as mock_db:
                mock_db.create_document = AsyncMock(return_value={'$id': 'test_doc'})
                
                result = await stripe_integration.create_connect_account(
                    'user_123', '<EMAIL>', 'US'
                )
                
                assert result['account_id'] == 'acct_test_123'
                assert result['status'] == 'created'
                assert result['onboarding_required'] is True
    
    @pytest.mark.asyncio
    async def test_create_payment_intent_for_qube(self, stripe_integration):
        """Test creating payment intent for Vybe Qube"""
        mock_account = {'stripeAccountId': 'acct_test_123'}
        
        with patch.object(stripe_integration, 'databases') as mock_db:
            mock_db.get_document = AsyncMock(return_value=mock_account)
            
            with patch('stripe.PaymentIntent.create') as mock_create:
                mock_create.return_value = Mock(
                    client_secret='pi_test_123_secret',
                    id='pi_test_123'
                )
                
                result = await stripe_integration.create_payment_intent_for_qube(
                    'qube_123', 'user_456', 10000, 'usd'
                )
                
                assert result['client_secret'] == 'pi_test_123_secret'
                assert result['payment_intent_id'] == 'pi_test_123'
                assert result['amount'] == 10000
                assert result['platform_fee'] == 500  # 5% of 10000
                assert result['user_revenue'] == 9500
    
    @pytest.mark.asyncio
    async def test_handle_payment_success(self, stripe_integration):
        """Test handling successful payment webhook"""
        payment_intent = {
            'id': 'pi_test_123',
            'amount': 10000,
            'currency': 'usd',
            'metadata': {
                'qube_id': 'qube_123',
                'user_id': 'user_456',
                'platform_fee': '500',
                'user_revenue': '9500'
            }
        }
        
        with patch.object(stripe_integration, 'databases') as mock_db:
            mock_db.create_document = AsyncMock(return_value={'$id': 'test_doc'})
            
            result = await stripe_integration._handle_payment_success(payment_intent)
            
            assert result['status'] == 'success'
            assert result['transaction_id'] == 'pi_test_123'
            mock_db.create_document.assert_called_once()

class TestAnalyticsEngine:
    """Test cases for AnalyticsEngine class"""
    
    @pytest.fixture
    def analytics_engine(self):
        """Create an AnalyticsEngine instance for testing"""
        return AnalyticsEngine()
    
    @pytest.mark.asyncio
    async def test_calculate_user_metrics(self, analytics_engine):
        """Test calculating user revenue metrics"""
        mock_transactions = [
            {'userRevenue': 100.0, 'qubeId': 'qube_1'},
            {'userRevenue': 150.0, 'qubeId': 'qube_2'},
            {'userRevenue': 75.0, 'qubeId': 'qube_1'}
        ]
        
        with patch.object(analytics_engine, '_get_user_transactions') as mock_get:
            mock_get.return_value = mock_transactions
            
            with patch.object(analytics_engine, '_calculate_conversion_rate') as mock_conv:
                mock_conv.return_value = 3.5
                
                with patch.object(analytics_engine, '_calculate_growth_rate') as mock_growth:
                    mock_growth.return_value = 15.0
                    
                    with patch.object(analytics_engine, '_get_top_performing_qubes') as mock_top:
                        mock_top.return_value = [
                            {'qubeId': 'qube_1', 'revenue': 175.0, 'transactions': 2},
                            {'qubeId': 'qube_2', 'revenue': 150.0, 'transactions': 1}
                        ]
                        
                        result = await analytics_engine.calculate_user_metrics('user_123', 30)
                        
                        assert result.total_revenue == 325.0
                        assert result.transaction_count == 3
                        assert result.average_transaction == 108.33333333333333
                        assert result.conversion_rate == 3.5
                        assert result.growth_rate == 15.0
                        assert len(result.top_performing_qubes) == 2
    
    def test_prepare_daily_revenue_series(self, analytics_engine):
        """Test preparing daily revenue time series"""
        start_date = datetime(2024, 1, 1)
        end_date = datetime(2024, 1, 5)
        
        transactions = [
            {'userRevenue': 100.0, 'createdAt': '2024-01-01T12:00:00Z'},
            {'userRevenue': 150.0, 'createdAt': '2024-01-02T15:00:00Z'},
            {'userRevenue': 75.0, 'createdAt': '2024-01-01T18:00:00Z'}
        ]
        
        result = analytics_engine._prepare_daily_revenue_series(
            transactions, start_date, end_date
        )
        
        assert len(result) == 4  # 4 days
        assert result[0] == 175.0  # Day 1: 100 + 75
        assert result[1] == 150.0  # Day 2: 150
        assert result[2] == 0.0    # Day 3: 0
        assert result[3] == 0.0    # Day 4: 0
    
    def test_categorize_performance(self, analytics_engine):
        """Test performance categorization"""
        assert analytics_engine._categorize_performance(1500, 60) == "excellent"
        assert analytics_engine._categorize_performance(750, 30) == "good"
        assert analytics_engine._categorize_performance(200, 10) == "average"
        assert analytics_engine._categorize_performance(50, 3) == "poor"
        assert analytics_engine._categorize_performance(0, 0) == "no_revenue"

class TestPayoutManager:
    """Test cases for PayoutManager class"""
    
    @pytest.fixture
    def payout_manager(self):
        """Create a PayoutManager instance for testing"""
        return PayoutManager()
    
    @pytest.mark.asyncio
    async def test_calculate_pending_payout(self, payout_manager):
        """Test calculating pending payout amount"""
        mock_transactions = {
            'documents': [
                {'userRevenue': 100.0},
                {'userRevenue': 75.0},
                {'userRevenue': 50.0}
            ]
        }
        
        with patch.object(payout_manager, '_get_last_payout') as mock_last:
            mock_last.return_value = None
            
            with patch.object(payout_manager, 'databases') as mock_db:
                mock_db.list_documents = AsyncMock(return_value=mock_transactions)
                
                result = await payout_manager.calculate_pending_payout('user_123')
                
                assert result['grossRevenue'] == 225.0
                assert result['processingFees'] == 0.75  # 3 transactions * $0.25
                assert result['netPayout'] == 224.25
                assert result['transactionCount'] == 3
                assert result['isEligible'] is True  # Above $50 minimum
    
    @pytest.mark.asyncio
    async def test_process_payout_eligible(self, payout_manager):
        """Test processing payout for eligible user"""
        payout_info = {
            'netPayout': 100.0,
            'grossRevenue': 105.0,
            'processingFees': 5.0,
            'transactionCount': 20,
            'isEligible': True
        }
        
        mock_account = {
            'stripeAccountId': 'acct_test_123',
            'chargesEnabled': True
        }
        
        with patch.object(payout_manager, 'calculate_pending_payout') as mock_calc:
            mock_calc.return_value = payout_info
            
            with patch.object(payout_manager, 'databases') as mock_db:
                mock_db.get_document = AsyncMock(return_value=mock_account)
                mock_db.create_document = AsyncMock(return_value={'$id': 'payout_doc'})
                
                with patch('stripe.Transfer.create') as mock_transfer:
                    mock_transfer.return_value = Mock(id='tr_test_123')
                    
                    result = await payout_manager.process_payout('user_123')
                    
                    assert result['status'] == 'success'
                    assert result['payoutId'] == 'tr_test_123'
                    assert result['amount'] == 100.0
    
    @pytest.mark.asyncio
    async def test_process_payout_ineligible(self, payout_manager):
        """Test processing payout for ineligible user"""
        payout_info = {
            'netPayout': 25.0,
            'isEligible': False
        }
        
        with patch.object(payout_manager, 'calculate_pending_payout') as mock_calc:
            mock_calc.return_value = payout_info
            
            result = await payout_manager.process_payout('user_123')
            
            assert result['status'] == 'ineligible'
            assert 'Minimum payout amount not reached' in result['message']
            assert result['pendingAmount'] == 25.0

# Test fixtures and utilities
@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
