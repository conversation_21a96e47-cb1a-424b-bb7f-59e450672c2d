#!/usr/bin/env python3
"""
VybeCoding.ai Revenue Tracking Service
Real-time revenue tracking for Vybe Qubes with Stripe integration
"""

import os
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from fastapi import FastAPI, HTTPException, Request, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import stripe
import uvicorn
from appwrite.client import Client
from appwrite.services.databases import Databases
from appwrite.services.users import Users
from appwrite.query import Query

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="VybeCoding.ai Revenue Tracker",
    description="Real-time revenue tracking and analytics for Vybe Qubes",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Environment configuration
STRIPE_SECRET_KEY = os.getenv("STRIPE_SECRET_KEY")
STRIPE_WEBHOOK_SECRET = os.getenv("STRIPE_WEBHOOK_SECRET")
APPWRITE_ENDPOINT = os.getenv("APPWRITE_ENDPOINT", "https://cloud.appwrite.io/v1")
APPWRITE_PROJECT_ID = os.getenv("APPWRITE_PROJECT_ID")
APPWRITE_API_KEY = os.getenv("APPWRITE_API_KEY")
DATABASE_ID = os.getenv("APPWRITE_DATABASE_ID", "vybecoding")

# Initialize Stripe
stripe.api_key = STRIPE_SECRET_KEY

# Initialize Appwrite
client = Client()
client.set_endpoint(APPWRITE_ENDPOINT)
client.set_project(APPWRITE_PROJECT_ID)
client.set_key(APPWRITE_API_KEY)

databases = Databases(client)
users = Users(client)

# Pydantic models
class RevenueTransaction(BaseModel):
    transaction_id: str
    qube_id: str
    user_id: str
    amount: float
    currency: str
    stripe_payment_intent_id: str
    status: str
    created_at: datetime
    metadata: Dict = {}

class RevenueAnalytics(BaseModel):
    user_id: str
    qube_id: str
    total_revenue: float
    transaction_count: int
    average_transaction: float
    conversion_rate: float
    period_start: datetime
    period_end: datetime

class PayoutRequest(BaseModel):
    user_id: str
    amount: float
    currency: str = "usd"

# Revenue tracking service
class RevenueTracker:
    def __init__(self):
        self.collections = {
            'transactions': 'revenueTransactions',
            'analytics': 'revenueAnalytics',
            'payouts': 'userPayouts'
        }
    
    async def record_transaction(self, transaction: RevenueTransaction) -> str:
        """Record a new revenue transaction"""
        try:
            doc_data = {
                'transactionId': transaction.transaction_id,
                'qubeId': transaction.qube_id,
                'userId': transaction.user_id,
                'amount': transaction.amount,
                'currency': transaction.currency,
                'stripePaymentIntentId': transaction.stripe_payment_intent_id,
                'status': transaction.status,
                'createdAt': transaction.created_at.isoformat(),
                'metadata': transaction.metadata,
                'platformFee': transaction.amount * 0.05,  # 5% platform fee
                'userRevenue': transaction.amount * 0.95
            }
            
            result = await databases.create_document(
                database_id=DATABASE_ID,
                collection_id=self.collections['transactions'],
                document_id=transaction.transaction_id,
                data=doc_data
            )
            
            # Update analytics in background
            await self.update_analytics(transaction.user_id, transaction.qube_id)
            
            logger.info(f"Transaction recorded: {transaction.transaction_id}")
            return result['$id']
            
        except Exception as e:
            logger.error(f"Error recording transaction: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def get_user_revenue(self, user_id: str, period_days: int = 30) -> Dict:
        """Get user revenue summary for specified period"""
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=period_days)
            
            # Query transactions for user in period
            transactions = await databases.list_documents(
                database_id=DATABASE_ID,
                collection_id=self.collections['transactions'],
                queries=[
                    Query.equal('userId', user_id),
                    Query.greater_than('createdAt', start_date.isoformat()),
                    Query.less_than('createdAt', end_date.isoformat()),
                    Query.equal('status', 'completed')
                ]
            )
            
            total_revenue = sum(doc['userRevenue'] for doc in transactions['documents'])
            transaction_count = len(transactions['documents'])
            
            # Get top performing Qubes
            qube_revenue = {}
            for doc in transactions['documents']:
                qube_id = doc['qubeId']
                qube_revenue[qube_id] = qube_revenue.get(qube_id, 0) + doc['userRevenue']
            
            top_qubes = sorted(qube_revenue.items(), key=lambda x: x[1], reverse=True)[:5]
            
            return {
                'userId': user_id,
                'period': f"{period_days} days",
                'totalRevenue': total_revenue,
                'transactionCount': transaction_count,
                'averageTransaction': total_revenue / transaction_count if transaction_count > 0 else 0,
                'topQubes': [{'qubeId': qube_id, 'revenue': revenue} for qube_id, revenue in top_qubes],
                'periodStart': start_date.isoformat(),
                'periodEnd': end_date.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting user revenue: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def update_analytics(self, user_id: str, qube_id: str):
        """Update analytics for user and qube"""
        try:
            # This would typically be done in a background task
            # For now, we'll update basic metrics
            pass
        except Exception as e:
            logger.error(f"Error updating analytics: {e}")

# Initialize revenue tracker
revenue_tracker = RevenueTracker()

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "revenue-tracker", "timestamp": datetime.now().isoformat()}

@app.post("/webhook/stripe")
async def stripe_webhook(request: Request, background_tasks: BackgroundTasks):
    """Handle Stripe webhooks for payment events"""
    try:
        payload = await request.body()
        sig_header = request.headers.get('stripe-signature')
        
        # Verify webhook signature
        event = stripe.Webhook.construct_event(
            payload, sig_header, STRIPE_WEBHOOK_SECRET
        )
        
        # Handle payment success
        if event['type'] == 'payment_intent.succeeded':
            payment_intent = event['data']['object']
            
            # Extract metadata
            metadata = payment_intent.get('metadata', {})
            qube_id = metadata.get('qube_id')
            user_id = metadata.get('user_id')
            
            if qube_id and user_id:
                transaction = RevenueTransaction(
                    transaction_id=payment_intent['id'],
                    qube_id=qube_id,
                    user_id=user_id,
                    amount=payment_intent['amount'] / 100,  # Convert from cents
                    currency=payment_intent['currency'],
                    stripe_payment_intent_id=payment_intent['id'],
                    status='completed',
                    created_at=datetime.now(),
                    metadata=metadata
                )
                
                background_tasks.add_task(revenue_tracker.record_transaction, transaction)
        
        return {"status": "success"}
        
    except Exception as e:
        logger.error(f"Webhook error: {e}")
        raise HTTPException(status_code=400, detail=str(e))

@app.get("/revenue/{user_id}")
async def get_revenue_summary(user_id: str, period_days: int = 30):
    """Get revenue summary for a user"""
    return await revenue_tracker.get_user_revenue(user_id, period_days)

@app.get("/analytics/{user_id}")
async def get_revenue_analytics(user_id: str, qube_id: Optional[str] = None):
    """Get detailed revenue analytics"""
    try:
        # Implementation for detailed analytics
        # This would include conversion rates, trends, forecasting
        return {
            "message": "Analytics endpoint - implementation in progress",
            "userId": user_id,
            "qubeId": qube_id
        }
    except Exception as e:
        logger.error(f"Error getting analytics: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8003,
        reload=True,
        log_level="info"
    )
