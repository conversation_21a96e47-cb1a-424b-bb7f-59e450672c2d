"""
Payout Manager for VybeCoding.ai Revenue Tracking
Automated payout processing and management
"""

import os
import logging
from typing import Dict, List, Optional
from datetime import datetime, timedelta
from decimal import Decimal
import asyncio
import stripe
from appwrite.client import Client
from appwrite.services.databases import Databases
from appwrite.query import Query

logger = logging.getLogger(__name__)

class PayoutManager:
    def __init__(self):
        # Stripe setup
        stripe.api_key = os.getenv("STRIPE_SECRET_KEY")
        
        # Appwrite setup
        self.client = Client()
        self.client.set_endpoint(os.getenv("APPWRITE_ENDPOINT", "https://cloud.appwrite.io/v1"))
        self.client.set_project(os.getenv("APPWRITE_PROJECT_ID"))
        self.client.set_key(os.getenv("APPWRITE_API_KEY"))
        self.databases = Databases(self.client)
        self.database_id = os.getenv("APPWRITE_DATABASE_ID", "vybecoding")
        
        # Payout configuration
        self.minimum_payout = Decimal('50.00')  # Minimum $50 for payout
        self.payout_schedule = 'weekly'  # weekly, monthly
        self.processing_fee = Decimal('0.25')  # $0.25 processing fee
        
        self.collections = {
            'transactions': 'revenueTransactions',
            'payouts': 'userPayouts',
            'accounts': 'stripeAccounts'
        }
    
    async def calculate_pending_payout(self, user_id: str) -> Dict:
        """Calculate pending payout amount for user"""
        try:
            # Get all completed transactions since last payout
            last_payout = await self._get_last_payout(user_id)
            since_date = last_payout['createdAt'] if last_payout else '2024-01-01T00:00:00.000Z'
            
            transactions = await self.databases.list_documents(
                database_id=self.database_id,
                collection_id=self.collections['transactions'],
                queries=[
                    Query.equal('userId', user_id),
                    Query.equal('status', 'completed'),
                    Query.greater_than('createdAt', since_date)
                ]
            )
            
            total_revenue = sum(Decimal(str(doc['userRevenue'])) for doc in transactions['documents'])
            transaction_count = len(transactions['documents'])
            
            # Calculate net payout (after processing fee)
            processing_fees = self.processing_fee * transaction_count
            net_payout = total_revenue - processing_fees
            
            is_eligible = net_payout >= self.minimum_payout
            
            return {
                'userId': user_id,
                'grossRevenue': float(total_revenue),
                'processingFees': float(processing_fees),
                'netPayout': float(net_payout),
                'transactionCount': transaction_count,
                'minimumPayout': float(self.minimum_payout),
                'isEligible': is_eligible,
                'lastPayoutDate': last_payout['createdAt'] if last_payout else None
            }
            
        except Exception as e:
            logger.error(f"Error calculating pending payout: {e}")
            raise
    
    async def process_payout(self, user_id: str, force: bool = False) -> Dict:
        """Process payout for user"""
        try:
            # Calculate pending amount
            payout_info = await self.calculate_pending_payout(user_id)
            
            if not payout_info['isEligible'] and not force:
                return {
                    'status': 'ineligible',
                    'message': f"Minimum payout amount not reached. Current: ${payout_info['netPayout']:.2f}, Required: ${self.minimum_payout}",
                    'pendingAmount': payout_info['netPayout']
                }
            
            # Get user's Stripe account
            user_account = await self.databases.get_document(
                database_id=self.database_id,
                collection_id=self.collections['accounts'],
                document_id=user_id
            )
            
            if not user_account.get('chargesEnabled'):
                return {
                    'status': 'account_not_ready',
                    'message': 'Stripe account not fully set up for payouts',
                    'pendingAmount': payout_info['netPayout']
                }
            
            # Create Stripe transfer
            transfer_amount = int(payout_info['netPayout'] * 100)  # Convert to cents
            
            transfer = stripe.Transfer.create(
                amount=transfer_amount,
                currency='usd',
                destination=user_account['stripeAccountId'],
                metadata={
                    'user_id': user_id,
                    'payout_type': 'automated',
                    'transaction_count': payout_info['transactionCount'],
                    'gross_revenue': payout_info['grossRevenue'],
                    'processing_fees': payout_info['processingFees']
                }
            )
            
            # Record payout in database
            payout_record = {
                'payoutId': transfer.id,
                'userId': user_id,
                'amount': payout_info['netPayout'],
                'grossRevenue': payout_info['grossRevenue'],
                'processingFees': payout_info['processingFees'],
                'transactionCount': payout_info['transactionCount'],
                'stripeTransferId': transfer.id,
                'status': 'pending',
                'createdAt': datetime.now().isoformat(),
                'expectedArrival': (datetime.now() + timedelta(days=2)).isoformat(),
                'metadata': {
                    'forced': force,
                    'payout_schedule': self.payout_schedule
                }
            }
            
            await self.databases.create_document(
                database_id=self.database_id,
                collection_id=self.collections['payouts'],
                document_id=transfer.id,
                data=payout_record
            )
            
            logger.info(f"Payout processed for user {user_id}: ${payout_info['netPayout']:.2f}")
            
            return {
                'status': 'success',
                'payoutId': transfer.id,
                'amount': payout_info['netPayout'],
                'expectedArrival': payout_record['expectedArrival'],
                'message': f"Payout of ${payout_info['netPayout']:.2f} initiated successfully"
            }
            
        except Exception as e:
            logger.error(f"Error processing payout: {e}")
            raise
    
    async def process_scheduled_payouts(self) -> Dict:
        """Process all eligible scheduled payouts"""
        try:
            # Get all users with Stripe accounts
            accounts = await self.databases.list_documents(
                database_id=self.database_id,
                collection_id=self.collections['accounts'],
                queries=[Query.equal('chargesEnabled', True)]
            )
            
            results = {
                'processed': 0,
                'failed': 0,
                'ineligible': 0,
                'total_amount': 0.0,
                'details': []
            }
            
            for account in accounts['documents']:
                user_id = account['userId']
                
                try:
                    # Check if user is due for payout
                    if await self._is_payout_due(user_id):
                        payout_result = await self.process_payout(user_id)
                        
                        if payout_result['status'] == 'success':
                            results['processed'] += 1
                            results['total_amount'] += payout_result['amount']
                        elif payout_result['status'] == 'ineligible':
                            results['ineligible'] += 1
                        else:
                            results['failed'] += 1
                        
                        results['details'].append({
                            'userId': user_id,
                            'status': payout_result['status'],
                            'amount': payout_result.get('amount', 0)
                        })
                        
                except Exception as e:
                    logger.error(f"Error processing payout for user {user_id}: {e}")
                    results['failed'] += 1
                    results['details'].append({
                        'userId': user_id,
                        'status': 'error',
                        'error': str(e)
                    })
            
            logger.info(f"Scheduled payouts completed: {results['processed']} processed, {results['failed']} failed, {results['ineligible']} ineligible")
            return results
            
        except Exception as e:
            logger.error(f"Error processing scheduled payouts: {e}")
            raise
    
    async def get_payout_history(self, user_id: str, limit: int = 50) -> List[Dict]:
        """Get payout history for user"""
        try:
            payouts = await self.databases.list_documents(
                database_id=self.database_id,
                collection_id=self.collections['payouts'],
                queries=[
                    Query.equal('userId', user_id),
                    Query.order_desc('createdAt'),
                    Query.limit(limit)
                ]
            )
            
            return [
                {
                    'payoutId': doc['payoutId'],
                    'amount': doc['amount'],
                    'status': doc['status'],
                    'createdAt': doc['createdAt'],
                    'expectedArrival': doc.get('expectedArrival'),
                    'actualArrival': doc.get('actualArrival'),
                    'transactionCount': doc['transactionCount']
                }
                for doc in payouts['documents']
            ]
            
        except Exception as e:
            logger.error(f"Error getting payout history: {e}")
            return []
    
    async def update_payout_status(self, transfer_id: str, status: str, arrival_date: Optional[str] = None) -> bool:
        """Update payout status from Stripe webhook"""
        try:
            update_data = {'status': status}
            
            if arrival_date:
                update_data['actualArrival'] = arrival_date
            
            await self.databases.update_document(
                database_id=self.database_id,
                collection_id=self.collections['payouts'],
                document_id=transfer_id,
                data=update_data
            )
            
            logger.info(f"Updated payout status: {transfer_id} -> {status}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating payout status: {e}")
            return False
    
    async def generate_tax_document(self, user_id: str, year: int) -> Dict:
        """Generate tax document (1099) for user"""
        try:
            start_date = f"{year}-01-01T00:00:00.000Z"
            end_date = f"{year}-12-31T23:59:59.999Z"
            
            # Get all payouts for the year
            payouts = await self.databases.list_documents(
                database_id=self.database_id,
                collection_id=self.collections['payouts'],
                queries=[
                    Query.equal('userId', user_id),
                    Query.greater_than('createdAt', start_date),
                    Query.less_than('createdAt', end_date),
                    Query.equal('status', 'paid')
                ]
            )
            
            total_earnings = sum(Decimal(str(doc['amount'])) for doc in payouts['documents'])
            total_transactions = sum(doc['transactionCount'] for doc in payouts['documents'])
            
            # Get user info
            user_account = await self.databases.get_document(
                database_id=self.database_id,
                collection_id=self.collections['accounts'],
                document_id=user_id
            )
            
            tax_document = {
                'userId': user_id,
                'year': year,
                'totalEarnings': float(total_earnings),
                'totalTransactions': total_transactions,
                'payoutCount': len(payouts['documents']),
                'userEmail': user_account.get('email'),
                'generatedAt': datetime.now().isoformat(),
                'requires1099': total_earnings >= Decimal('600.00')  # IRS threshold
            }
            
            # Store tax document
            await self.databases.create_document(
                database_id=self.database_id,
                collection_id='taxDocuments',
                document_id=f"{user_id}_{year}",
                data=tax_document
            )
            
            return tax_document
            
        except Exception as e:
            logger.error(f"Error generating tax document: {e}")
            raise
    
    async def _get_last_payout(self, user_id: str) -> Optional[Dict]:
        """Get user's last payout"""
        try:
            payouts = await self.databases.list_documents(
                database_id=self.database_id,
                collection_id=self.collections['payouts'],
                queries=[
                    Query.equal('userId', user_id),
                    Query.order_desc('createdAt'),
                    Query.limit(1)
                ]
            )
            
            return payouts['documents'][0] if payouts['documents'] else None
            
        except Exception as e:
            logger.error(f"Error getting last payout: {e}")
            return None
    
    async def _is_payout_due(self, user_id: str) -> bool:
        """Check if user is due for a payout based on schedule"""
        try:
            last_payout = await self._get_last_payout(user_id)
            
            if not last_payout:
                return True  # First payout
            
            last_payout_date = datetime.fromisoformat(last_payout['createdAt'].replace('Z', '+00:00'))
            
            if self.payout_schedule == 'weekly':
                return datetime.now() >= last_payout_date + timedelta(weeks=1)
            elif self.payout_schedule == 'monthly':
                return datetime.now() >= last_payout_date + timedelta(days=30)
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking payout due: {e}")
            return False
