version: '3.8'

services:
  revenue-tracker:
    build: .
    container_name: vybe-revenue-tracker
    ports:
      - '8003:8003'
    environment:
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
      - STRIPE_WEBHOOK_SECRET=${STRIPE_WEBHOOK_SECRET}
      - STRIPE_CONNECT_CLIENT_ID=${STRIPE_CONNECT_CLIENT_ID}
      - APPWRITE_ENDPOINT=${APPWRITE_ENDPOINT}
      - APPWRITE_PROJECT_ID=${APPWRITE_PROJECT_ID}
      - APPWRITE_API_KEY=${APPWRITE_API_KEY}
      - APPWRITE_DATABASE_ID=${APPWRITE_DATABASE_ID}
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:8003/health']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - vybe-network

  # Redis for caching and background tasks
  redis:
    image: redis:7-alpine
    container_name: vybe-revenue-redis
    ports:
      - '6379:6379'
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - vybe-network

  # Celery worker for background tasks
  celery-worker:
    build: .
    container_name: vybe-revenue-celery
    command: celery -A main.celery worker --loglevel=info
    environment:
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
      - STRIPE_WEBHOOK_SECRET=${STRIPE_WEBHOOK_SECRET}
      - APPWRITE_ENDPOINT=${APPWRITE_ENDPOINT}
      - APPWRITE_PROJECT_ID=${APPWRITE_PROJECT_ID}
      - APPWRITE_API_KEY=${APPWRITE_API_KEY}
      - APPWRITE_DATABASE_ID=${APPWRITE_DATABASE_ID}
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - vybe-network

  # Celery beat for scheduled tasks
  celery-beat:
    build: .
    container_name: vybe-revenue-beat
    command: celery -A main.celery beat --loglevel=info
    environment:
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
      - APPWRITE_ENDPOINT=${APPWRITE_ENDPOINT}
      - APPWRITE_PROJECT_ID=${APPWRITE_PROJECT_ID}
      - APPWRITE_API_KEY=${APPWRITE_API_KEY}
      - APPWRITE_DATABASE_ID=${APPWRITE_DATABASE_ID}
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - vybe-network

volumes:
  redis_data:

networks:
  vybe-network:
    external: true
