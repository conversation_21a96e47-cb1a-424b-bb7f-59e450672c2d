"""
Stripe Integration for VybeCoding.ai Revenue Tracking
Handles Stripe Connect, payments, and marketplace functionality
"""

import os
import logging
from typing import Dict, List, Optional
from datetime import datetime, timedelta
import stripe
from appwrite.client import Client
from appwrite.services.databases import Databases
from appwrite.query import Query

logger = logging.getLogger(__name__)

class StripeIntegration:
    def __init__(self):
        self.stripe_secret_key = os.getenv("STRIPE_SECRET_KEY")
        self.stripe_connect_client_id = os.getenv("STRIPE_CONNECT_CLIENT_ID")
        stripe.api_key = self.stripe_secret_key
        
        # Appwrite setup
        self.client = Client()
        self.client.set_endpoint(os.getenv("APPWRITE_ENDPOINT", "https://cloud.appwrite.io/v1"))
        self.client.set_project(os.getenv("APPWRITE_PROJECT_ID"))
        self.client.set_key(os.getenv("APPWRITE_API_KEY"))
        self.databases = Databases(self.client)
        self.database_id = os.getenv("APPWRITE_DATABASE_ID", "vybecoding")
    
    async def create_connect_account(self, user_id: str, email: str, country: str = "US") -> Dict:
        """Create Stripe Connect account for user"""
        try:
            account = stripe.Account.create(
                type="express",
                country=country,
                email=email,
                capabilities={
                    "card_payments": {"requested": True},
                    "transfers": {"requested": True},
                },
                business_type="individual",
                metadata={"user_id": user_id}
            )
            
            # Store account info in database
            await self.databases.create_document(
                database_id=self.database_id,
                collection_id="stripeAccounts",
                document_id=user_id,
                data={
                    "userId": user_id,
                    "stripeAccountId": account.id,
                    "email": email,
                    "country": country,
                    "status": "pending",
                    "createdAt": datetime.now().isoformat()
                }
            )
            
            logger.info(f"Created Stripe Connect account for user {user_id}: {account.id}")
            return {
                "account_id": account.id,
                "status": "created",
                "onboarding_required": True
            }
            
        except Exception as e:
            logger.error(f"Error creating Stripe Connect account: {e}")
            raise
    
    async def create_account_link(self, user_id: str, return_url: str, refresh_url: str) -> str:
        """Create account link for Stripe Connect onboarding"""
        try:
            # Get user's Stripe account ID
            user_account = await self.databases.get_document(
                database_id=self.database_id,
                collection_id="stripeAccounts",
                document_id=user_id
            )
            
            account_link = stripe.AccountLink.create(
                account=user_account["stripeAccountId"],
                return_url=return_url,
                refresh_url=refresh_url,
                type="account_onboarding",
            )
            
            return account_link.url
            
        except Exception as e:
            logger.error(f"Error creating account link: {e}")
            raise
    
    async def create_payment_intent_for_qube(
        self, 
        qube_id: str, 
        user_id: str, 
        amount: int, 
        currency: str = "usd",
        customer_email: Optional[str] = None
    ) -> Dict:
        """Create payment intent for Vybe Qube purchase"""
        try:
            # Get user's Stripe account
            user_account = await self.databases.get_document(
                database_id=self.database_id,
                collection_id="stripeAccounts",
                document_id=user_id
            )
            
            # Calculate platform fee (5%)
            platform_fee = int(amount * 0.05)
            
            payment_intent = stripe.PaymentIntent.create(
                amount=amount,
                currency=currency,
                application_fee_amount=platform_fee,
                transfer_data={
                    "destination": user_account["stripeAccountId"],
                },
                metadata={
                    "qube_id": qube_id,
                    "user_id": user_id,
                    "platform_fee": platform_fee,
                    "user_revenue": amount - platform_fee
                },
                receipt_email=customer_email,
                description=f"Purchase from Vybe Qube {qube_id}"
            )
            
            return {
                "client_secret": payment_intent.client_secret,
                "payment_intent_id": payment_intent.id,
                "amount": amount,
                "platform_fee": platform_fee,
                "user_revenue": amount - platform_fee
            }
            
        except Exception as e:
            logger.error(f"Error creating payment intent: {e}")
            raise
    
    async def process_webhook_event(self, event: Dict) -> Dict:
        """Process Stripe webhook events"""
        try:
            event_type = event["type"]
            
            if event_type == "payment_intent.succeeded":
                return await self._handle_payment_success(event["data"]["object"])
            
            elif event_type == "account.updated":
                return await self._handle_account_update(event["data"]["object"])
            
            elif event_type == "payment_intent.payment_failed":
                return await self._handle_payment_failure(event["data"]["object"])
            
            elif event_type == "transfer.created":
                return await self._handle_transfer_created(event["data"]["object"])
            
            else:
                logger.info(f"Unhandled webhook event: {event_type}")
                return {"status": "ignored", "event_type": event_type}
                
        except Exception as e:
            logger.error(f"Error processing webhook event: {e}")
            raise
    
    async def _handle_payment_success(self, payment_intent: Dict) -> Dict:
        """Handle successful payment"""
        try:
            metadata = payment_intent.get("metadata", {})
            qube_id = metadata.get("qube_id")
            user_id = metadata.get("user_id")
            
            if not qube_id or not user_id:
                logger.warning("Payment success but missing qube_id or user_id in metadata")
                return {"status": "warning", "message": "Missing metadata"}
            
            # Record transaction
            transaction_data = {
                "transactionId": payment_intent["id"],
                "qubeId": qube_id,
                "userId": user_id,
                "amount": payment_intent["amount"] / 100,
                "currency": payment_intent["currency"],
                "stripePaymentIntentId": payment_intent["id"],
                "status": "completed",
                "createdAt": datetime.now().isoformat(),
                "platformFee": float(metadata.get("platform_fee", 0)) / 100,
                "userRevenue": float(metadata.get("user_revenue", 0)) / 100,
                "metadata": metadata
            }
            
            await self.databases.create_document(
                database_id=self.database_id,
                collection_id="revenueTransactions",
                document_id=payment_intent["id"],
                data=transaction_data
            )
            
            logger.info(f"Payment processed successfully: {payment_intent['id']}")
            return {"status": "success", "transaction_id": payment_intent["id"]}
            
        except Exception as e:
            logger.error(f"Error handling payment success: {e}")
            raise
    
    async def _handle_account_update(self, account: Dict) -> Dict:
        """Handle Stripe account updates"""
        try:
            user_id = account.get("metadata", {}).get("user_id")
            if not user_id:
                return {"status": "ignored", "message": "No user_id in account metadata"}
            
            # Update account status
            await self.databases.update_document(
                database_id=self.database_id,
                collection_id="stripeAccounts",
                document_id=user_id,
                data={
                    "status": "active" if account["charges_enabled"] else "pending",
                    "chargesEnabled": account["charges_enabled"],
                    "payoutsEnabled": account["payouts_enabled"],
                    "updatedAt": datetime.now().isoformat()
                }
            )
            
            return {"status": "updated", "user_id": user_id}
            
        except Exception as e:
            logger.error(f"Error handling account update: {e}")
            raise
    
    async def _handle_payment_failure(self, payment_intent: Dict) -> Dict:
        """Handle failed payment"""
        try:
            metadata = payment_intent.get("metadata", {})
            
            # Log failed payment
            failure_data = {
                "paymentIntentId": payment_intent["id"],
                "qubeId": metadata.get("qube_id"),
                "userId": metadata.get("user_id"),
                "amount": payment_intent["amount"] / 100,
                "currency": payment_intent["currency"],
                "failureReason": payment_intent.get("last_payment_error", {}).get("message", "Unknown"),
                "createdAt": datetime.now().isoformat(),
                "metadata": metadata
            }
            
            await self.databases.create_document(
                database_id=self.database_id,
                collection_id="paymentFailures",
                document_id=payment_intent["id"],
                data=failure_data
            )
            
            logger.warning(f"Payment failed: {payment_intent['id']}")
            return {"status": "failed", "payment_intent_id": payment_intent["id"]}
            
        except Exception as e:
            logger.error(f"Error handling payment failure: {e}")
            raise
    
    async def _handle_transfer_created(self, transfer: Dict) -> Dict:
        """Handle transfer creation (payout to user)"""
        try:
            # Log transfer for tracking
            logger.info(f"Transfer created: {transfer['id']} for {transfer['amount']/100} {transfer['currency']}")
            return {"status": "logged", "transfer_id": transfer["id"]}
            
        except Exception as e:
            logger.error(f"Error handling transfer: {e}")
            raise
    
    async def get_account_status(self, user_id: str) -> Dict:
        """Get Stripe Connect account status for user"""
        try:
            user_account = await self.databases.get_document(
                database_id=self.database_id,
                collection_id="stripeAccounts",
                document_id=user_id
            )
            
            # Get latest account info from Stripe
            account = stripe.Account.retrieve(user_account["stripeAccountId"])
            
            return {
                "account_id": account.id,
                "charges_enabled": account.charges_enabled,
                "payouts_enabled": account.payouts_enabled,
                "details_submitted": account.details_submitted,
                "requirements": account.requirements,
                "status": "active" if account.charges_enabled else "pending"
            }
            
        except Exception as e:
            logger.error(f"Error getting account status: {e}")
            raise
    
    async def create_express_dashboard_link(self, user_id: str) -> str:
        """Create link to Stripe Express dashboard"""
        try:
            user_account = await self.databases.get_document(
                database_id=self.database_id,
                collection_id="stripeAccounts",
                document_id=user_id
            )
            
            link = stripe.Account.create_login_link(user_account["stripeAccountId"])
            return link.url
            
        except Exception as e:
            logger.error(f"Error creating dashboard link: {e}")
            raise
