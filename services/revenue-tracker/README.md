# VybeCoding.ai Revenue Tracking Service

Real-time revenue tracking and analytics for Vybe Qubes with Stripe integration.

## 🎯 Overview

The Revenue Tracking Service provides comprehensive revenue analytics, payment processing, and automated payout management for the VybeCoding.ai platform. It enables users to track earnings from their Vybe Qubes in real-time and receive automated payouts.

## ✨ Features

### 🔄 Real-time Revenue Tracking

- Live transaction monitoring
- Revenue attribution per Vybe Qube
- Platform fee calculation (5%)
- Multi-currency support

### 📊 Advanced Analytics

- Revenue trends and forecasting
- Conversion rate analysis
- Top-performing Qube identification
- Growth rate calculations
- Actionable insights generation

### 💳 Stripe Integration

- Stripe Connect marketplace functionality
- Automated payment processing
- Webhook handling for real-time updates
- Express dashboard access

### 💰 Automated Payouts

- Weekly payout processing
- Minimum threshold enforcement ($50)
- Processing fee management ($0.25/transaction)
- Tax document generation (1099)

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   API Gateway   │    │ Revenue Service │
│   Dashboard     │◄──►│   (SvelteKit)   │◄──►│   (FastAPI)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                       ┌─────────────────┐             │
                       │     Stripe      │◄────────────┤
                       │   Webhooks      │             │
                       └─────────────────┘             │
                                                        │
                       ┌─────────────────┐             │
                       │   Appwrite      │◄────────────┘
                       │   Database      │
                       └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites

- Python 3.11+
- Docker & Docker Compose
- Stripe account with Connect enabled
- Appwrite instance

### Environment Variables

```bash
# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
STRIPE_CONNECT_CLIENT_ID=ca_...

# Appwrite Configuration
APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
APPWRITE_PROJECT_ID=your_project_id
APPWRITE_API_KEY=your_api_key
APPWRITE_DATABASE_ID=vybecoding

# Service Configuration
REVENUE_SERVICE_URL=http://localhost:8003
```

### Installation

1. **Clone and Setup**

```bash
cd services/revenue-tracker
cp .env.example .env
# Edit .env with your configuration
```

2. **Docker Deployment**

```bash
docker-compose up -d
```

3. **Local Development**

```bash
pip install -r requirements.txt
uvicorn main:app --reload --port 8003
```

## 📡 API Endpoints

### Revenue Tracking

- `GET /revenue/{user_id}` - Get revenue summary
- `GET /analytics/{user_id}` - Get detailed analytics
- `GET /insights/{user_id}` - Get actionable insights

### Payout Management

- `GET /payout/{user_id}` - Get payout information
- `POST /payout/{user_id}/request` - Request payout
- `GET /payout/{user_id}/history` - Get payout history

### Stripe Integration

- `POST /stripe/create-account` - Create Connect account
- `GET /stripe/account-status/{user_id}` - Get account status
- `GET /stripe/dashboard-link/{user_id}` - Get dashboard link
- `POST /webhook/stripe` - Handle Stripe webhooks

### Health & Monitoring

- `GET /health` - Service health check

## 🗄️ Database Schema

### Collections

#### revenueTransactions

```json
{
  "transactionId": "string",
  "qubeId": "string",
  "userId": "string",
  "amount": "number",
  "currency": "string",
  "stripePaymentIntentId": "string",
  "status": "string",
  "createdAt": "datetime",
  "platformFee": "number",
  "userRevenue": "number",
  "metadata": "object"
}
```

#### userPayouts

```json
{
  "payoutId": "string",
  "userId": "string",
  "amount": "number",
  "grossRevenue": "number",
  "processingFees": "number",
  "transactionCount": "number",
  "stripeTransferId": "string",
  "status": "string",
  "createdAt": "datetime",
  "expectedArrival": "datetime",
  "actualArrival": "datetime"
}
```

#### stripeAccounts

```json
{
  "userId": "string",
  "stripeAccountId": "string",
  "email": "string",
  "country": "string",
  "status": "string",
  "chargesEnabled": "boolean",
  "payoutsEnabled": "boolean",
  "createdAt": "datetime"
}
```

## 🧪 Testing

### Run Tests

```bash
# Unit tests
python -m pytest tests/ -v

# Coverage report
python -m pytest tests/ --cov=. --cov-report=html

# Integration tests
python -m pytest tests/integration/ -v
```

### Test Coverage

- Revenue tracking: 95%+
- Stripe integration: 90%+
- Analytics engine: 85%+
- Payout manager: 90%+

## 📊 Monitoring & Observability

### Health Checks

- Service health endpoint
- Database connectivity
- Stripe API status
- Background task monitoring

### Metrics

- Transaction volume
- Revenue trends
- Payout success rates
- API response times
- Error rates

### Logging

- Structured JSON logging
- Transaction audit trails
- Error tracking
- Performance monitoring

## 🔒 Security

### Payment Security

- PCI DSS compliance through Stripe
- Webhook signature verification
- Encrypted data transmission
- Secure API key management

### Data Protection

- User data encryption
- Access control
- Audit logging
- GDPR compliance

## 🚀 Deployment

### Production Deployment

```bash
# Build and deploy
docker-compose -f docker-compose.prod.yml up -d

# Scale services
docker-compose up -d --scale celery-worker=3
```

### Environment Configuration

- Production: Stripe live keys
- Staging: Stripe test keys
- Development: Local configuration

## 📈 Performance

### Benchmarks

- API Response Time: <200ms P95
- Transaction Processing: <500ms
- Analytics Generation: <2s
- Payout Processing: <1s

### Scaling

- Horizontal scaling with load balancer
- Background task processing with Celery
- Redis caching for performance
- Database connection pooling

## 🔧 Configuration

### Payout Settings

```python
MINIMUM_PAYOUT = 50.00  # USD
PROCESSING_FEE = 0.25   # Per transaction
PLATFORM_FEE = 0.05     # 5% of revenue
PAYOUT_SCHEDULE = 'weekly'
```

### Analytics Settings

```python
FORECAST_DAYS = 30
CONFIDENCE_INTERVAL = 0.8
TREND_THRESHOLD = 0.1
```

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit pull request

## 📄 License

MIT License - see LICENSE file for details

## 🆘 Support

- Documentation: `/docs`
- Issues: GitHub Issues
- Email: <EMAIL>

---

**VybeCoding.ai Revenue Tracking Service** - Powering transparent, real-time revenue analytics for the future of AI-generated websites.
