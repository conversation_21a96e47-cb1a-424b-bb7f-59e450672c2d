"""
Test suite for Vybe Qube Generator Service
"""

import pytest
import asyncio
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch, AsyncMock
import json
import time

from main import app, VybeQubeRequest, active_generations, completed_qubes

client = TestClient(app)

@pytest.fixture
def sample_request():
    """Sample Vybe Qube generation request"""
    return {
        "template_type": "ecommerce",
        "business_concept": "Online store for handmade crafts",
        "target_audience": "Art enthusiasts and craft lovers",
        "revenue_model": "Direct product sales with 40% markup",
        "customization": {
            "branding": {
                "primary_color": "#3B82F6",
                "secondary_color": "#10B981",
                "company_name": "CraftHub"
            }
        }
    }

@pytest.fixture
def mock_mas_coordinator():
    """Mock MAS coordinator for testing"""
    mock = Mock()
    mock.execute_bmad_workflow = AsyncMock(return_value={
        "analyst": {"result": "Business analysis complete"},
        "product_manager": {"result": "Product requirements defined"},
        "architect": {"result": "Technical architecture designed"},
        "designer": {"result": "UI/UX specifications created"}
    })
    return mock

class TestVybeQubeGeneration:
    """Test Vybe Qube generation endpoints"""
    
    def test_generate_vybe_qube_success(self, sample_request):
        """Test successful Vybe Qube generation request"""
        response = client.post("/generate", json=sample_request)
        
        assert response.status_code == 200
        data = response.json()
        
        assert "qube_id" in data
        assert data["status"] == "generating"
        assert data["preview_url"].startswith("https://")
        assert data["estimated_completion"] == "15-20 minutes"
        
        # Verify qube is tracked in active generations
        qube_id = data["qube_id"]
        assert qube_id in active_generations
        assert active_generations[qube_id]["status"] == "generating"
    
    def test_generate_vybe_qube_invalid_template(self):
        """Test generation with invalid template type"""
        invalid_request = {
            "template_type": "invalid_template",
            "business_concept": "Test business",
            "target_audience": "Test audience",
            "revenue_model": "Test revenue model"
        }
        
        response = client.post("/generate", json=invalid_request)
        
        # Should still accept but may fail during generation
        assert response.status_code == 200
    
    def test_generate_vybe_qube_missing_fields(self):
        """Test generation with missing required fields"""
        incomplete_request = {
            "template_type": "ecommerce"
            # Missing other required fields
        }
        
        response = client.post("/generate", json=incomplete_request)
        assert response.status_code == 422  # Validation error

class TestVybeQubeStatus:
    """Test Vybe Qube status endpoints"""
    
    def test_get_qube_status_active(self, sample_request):
        """Test getting status of active generation"""
        # First create a generation
        response = client.post("/generate", json=sample_request)
        qube_id = response.json()["qube_id"]
        
        # Get status
        status_response = client.get(f"/status/{qube_id}")
        assert status_response.status_code == 200
        
        status_data = status_response.json()
        assert status_data["status"] in ["generating", "completed", "failed"]
        assert "progress" in status_data
        assert "current_phase" in status_data
    
    def test_get_qube_status_not_found(self):
        """Test getting status of non-existent qube"""
        response = client.get("/status/nonexistent_qube")
        assert response.status_code == 404
        assert "not found" in response.json()["detail"].lower()

class TestVybeQubeRevenue:
    """Test revenue tracking endpoints"""
    
    def test_get_qube_revenue(self):
        """Test getting revenue data for a qube"""
        # Use a mock qube ID
        qube_id = "test_qube_123"
        
        response = client.get(f"/revenue/{qube_id}")
        assert response.status_code == 200
        
        revenue_data = response.json()
        assert "total_revenue" in revenue_data
        assert "monthly_revenue" in revenue_data
        assert "weekly_revenue" in revenue_data
        assert "daily_revenue" in revenue_data
        assert "transactions" in revenue_data
        
        # Verify all values are numeric
        for key in ["total_revenue", "monthly_revenue", "weekly_revenue", "daily_revenue"]:
            assert isinstance(revenue_data[key], (int, float))
    
    def test_get_total_revenue(self):
        """Test getting total revenue across all qubes"""
        response = client.get("/revenue/total")
        assert response.status_code == 200
        
        total_data = response.json()
        assert "total" in total_data
        assert "monthly" in total_data
        assert "weekly" in total_data
        assert "daily" in total_data
        assert "qube_count" in total_data
        assert "average_per_qube" in total_data

class TestVybeQubeList:
    """Test Vybe Qube listing endpoints"""
    
    def test_get_all_qubes_empty(self):
        """Test getting all qubes when none exist"""
        # Clear existing data
        active_generations.clear()
        completed_qubes.clear()
        
        response = client.get("/qubes")
        assert response.status_code == 200
        
        qubes = response.json()
        assert isinstance(qubes, list)
        assert len(qubes) == 0
    
    def test_get_all_qubes_with_data(self, sample_request):
        """Test getting all qubes with existing data"""
        # Create a generation first
        gen_response = client.post("/generate", json=sample_request)
        qube_id = gen_response.json()["qube_id"]
        
        response = client.get("/qubes")
        assert response.status_code == 200
        
        qubes = response.json()
        assert isinstance(qubes, list)
        assert len(qubes) >= 1
        
        # Check structure of qube data
        qube = qubes[0]
        assert "id" in qube
        assert "name" in qube
        assert "template" in qube
        assert "status" in qube
        assert "url" in qube
        assert "revenue" in qube
        assert "created_at" in qube

class TestMASIntegration:
    """Test MAS coordinator integration"""
    
    def test_start_mas_coordinator(self):
        """Test starting MAS coordinator"""
        response = client.post("/mas/start")
        assert response.status_code == 200
        
        result = response.json()
        assert "success" in result
        assert "message" in result
    
    def test_get_mas_status(self):
        """Test getting MAS coordinator status"""
        response = client.get("/mas/status")
        assert response.status_code == 200
        
        status = response.json()
        assert "status" in status
        assert status["status"] in ["running", "stopped", "error"]
        assert "agents" in status
        assert "active_generations" in status
        assert "total_generated" in status
        
        # Check agent structure
        if status["agents"]:
            agent = status["agents"][0]
            assert "name" in agent
            assert "status" in agent
            assert "last_activity" in agent

class TestWebsiteGeneration:
    """Test website file generation functions"""
    
    @patch('main.generate_website_files')
    async def test_generate_website_files(self, mock_generate):
        """Test website file generation"""
        mock_results = {
            "analyst": {"result": "Business analysis"},
            "product_manager": {"result": "Product requirements"},
            "architect": {"result": "Technical architecture"},
            "designer": {"result": "Design specifications"}
        }
        
        mock_generate.return_value = {
            "package.json": '{"name": "test-qube"}',
            "src/app.html": "<!DOCTYPE html>...",
            "src/routes/+page.svelte": "<h1>Test</h1>"
        }
        
        files = await mock_generate(mock_results, "ecommerce")
        
        assert isinstance(files, dict)
        assert "package.json" in files
        assert "src/app.html" in files
        assert "src/routes/+page.svelte" in files

class TestErrorHandling:
    """Test error handling and edge cases"""
    
    def test_invalid_json_request(self):
        """Test handling of invalid JSON in request"""
        response = client.post(
            "/generate",
            data="invalid json",
            headers={"Content-Type": "application/json"}
        )
        assert response.status_code == 422
    
    def test_empty_request_body(self):
        """Test handling of empty request body"""
        response = client.post("/generate", json={})
        assert response.status_code == 422
    
    @patch('main.mas_coordinator', None)
    def test_mas_coordinator_unavailable(self, sample_request):
        """Test generation when MAS coordinator is unavailable"""
        response = client.post("/generate", json=sample_request)
        
        # Should still accept the request and use mock generation
        assert response.status_code == 200
        
        qube_id = response.json()["qube_id"]
        
        # Wait a moment for mock generation to start
        time.sleep(1)
        
        status_response = client.get(f"/status/{qube_id}")
        assert status_response.status_code == 200

class TestPerformance:
    """Test performance and load handling"""
    
    def test_concurrent_generations(self, sample_request):
        """Test handling multiple concurrent generation requests"""
        responses = []
        
        # Submit multiple requests
        for i in range(3):
            modified_request = sample_request.copy()
            modified_request["business_concept"] = f"Business concept {i}"
            
            response = client.post("/generate", json=modified_request)
            responses.append(response)
        
        # All should succeed
        for response in responses:
            assert response.status_code == 200
            assert "qube_id" in response.json()
        
        # All should have unique IDs
        qube_ids = [r.json()["qube_id"] for r in responses]
        assert len(set(qube_ids)) == len(qube_ids)
    
    def test_status_check_performance(self, sample_request):
        """Test performance of status checking"""
        # Create a generation
        response = client.post("/generate", json=sample_request)
        qube_id = response.json()["qube_id"]
        
        # Check status multiple times quickly
        start_time = time.time()
        for _ in range(10):
            status_response = client.get(f"/status/{qube_id}")
            assert status_response.status_code == 200
        
        end_time = time.time()
        
        # Should complete quickly (less than 1 second for 10 requests)
        assert end_time - start_time < 1.0

@pytest.mark.asyncio
class TestAsyncOperations:
    """Test asynchronous operations"""
    
    async def test_async_generation_workflow(self, mock_mas_coordinator):
        """Test the async generation workflow"""
        from main import generate_qube_async, VybeQubeRequest
        
        request = VybeQubeRequest(
            template_type="ecommerce",
            business_concept="Test business",
            target_audience="Test audience",
            revenue_model="Test revenue"
        )
        
        qube_id = f"test_{int(time.time())}"
        
        # Initialize the generation tracking
        active_generations[qube_id] = {
            "status": "generating",
            "progress": 0,
            "current_phase": "initializing",
            "request": request.dict(),
            "start_time": time.time(),
            "steps": []
        }
        
        # Run the async generation
        with patch('main.mas_coordinator', mock_mas_coordinator):
            await generate_qube_async(qube_id, request)
        
        # Verify completion
        assert qube_id in completed_qubes
        assert completed_qubes[qube_id]["status"] == "completed"

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
