# VybeCoding.ai Environment Template
# Copy this file to .env.development, .env.staging, or .env.production
# and fill in the appropriate values for your environment

# Environment Configuration
NODE_ENV=development
VITE_ENVIRONMENT=development

# Application Configuration
VITE_APP_NAME=VybeCoding.ai
VITE_APP_VERSION=1.0.0
PORT=3000

# Appwrite Configuration
# Get these values from your Appwrite console
VITE_APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
VITE_APPWRITE_PROJECT_ID=your-project-id
VITE_APPWRITE_DATABASE_ID=your-database-id

# API Configuration
VITE_API_BASE_URL=http://localhost:3000/api
VITE_API_TIMEOUT=30000

# Feature Flags
VITE_ENABLE_DEBUG_MODE=true
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_ERROR_REPORTING=true
VITE_ENABLE_PERFORMANCE_MONITORING=true

# Security Settings
VITE_ENABLE_CSP=true
VITE_ENABLE_HTTPS_ONLY=false
VITE_ENABLE_SECURE_COOKIES=false

# Logging Configuration
VITE_LOG_LEVEL=debug
VITE_ENABLE_CONSOLE_LOGS=true

# Development Tools
VITE_ENABLE_DEVTOOLS=true
VITE_ENABLE_HOT_RELOAD=true
VITE_ENABLE_SOURCE_MAPS=true

# External Services
VITE_ENABLE_THIRD_PARTY_SCRIPTS=false
VITE_GOOGLE_ANALYTICS_ID=
VITE_SENTRY_DSN=

# Build Configuration
VITE_BUILD_TARGET=development
VITE_OPTIMIZE_BUNDLE=false
VITE_GENERATE_SOURCEMAP=true

# Cache Configuration
VITE_CACHE_STRATEGY=network-first
VITE_CACHE_TTL=300

# Docker Configuration
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password
NGINX_PORT=80
NGINX_SSL_PORT=443
NGINX_HOST=localhost

# Monitoring Configuration (Production)
GRAFANA_PASSWORD=your-grafana-password
PROMETHEUS_PORT=9090
GRAFANA_PORT=3001

# Deployment Information (Auto-generated)
VITE_DEPLOY_TIMESTAMP=
VITE_COMMIT_HASH=
VITE_BRANCH_NAME=

# Database Configuration (if using local database)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=vybecoding
DB_USER=vybecoding
DB_PASSWORD=your-db-password

# Email Configuration (if needed)
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASSWORD=
SMTP_FROM=<EMAIL>

# Storage Configuration (if using local storage)
STORAGE_DRIVER=local
STORAGE_PATH=./storage
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=

# AI/LLM Configuration (if using local models)
OPENAI_API_KEY=
ANTHROPIC_API_KEY=
OLLAMA_HOST=http://localhost:11434
LOCAL_LLM_ENDPOINT=

# Webhook Configuration
WEBHOOK_SECRET=your-webhook-secret
GITHUB_WEBHOOK_SECRET=your-github-webhook-secret

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Session Configuration
SESSION_SECRET=your-session-secret
SESSION_TIMEOUT=3600000

# CORS Configuration
CORS_ORIGIN=http://localhost:5173
CORS_CREDENTIALS=true

# SSL Configuration (Production)
SSL_CERT_PATH=
SSL_KEY_PATH=
SSL_CA_PATH=

# Backup Configuration
BACKUP_ENABLED=false
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH=./backups

# Monitoring and Alerting
HEALTH_CHECK_INTERVAL=30000
ALERT_EMAIL=<EMAIL>
SLACK_WEBHOOK_URL=
DISCORD_WEBHOOK_URL=

# Performance Configuration
MAX_REQUEST_SIZE=10mb
REQUEST_TIMEOUT=30000
WORKER_PROCESSES=auto
WORKER_CONNECTIONS=1024

# Security Headers
SECURITY_HSTS_MAX_AGE=31536000
SECURITY_CONTENT_TYPE_NOSNIFF=true
SECURITY_FRAME_OPTIONS=DENY
SECURITY_XSS_PROTECTION=true
