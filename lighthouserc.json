{"ci": {"collect": {"url": ["http://localhost:4173", "http://localhost:4173/about", "http://localhost:4173/features", "http://localhost:4173/pricing"], "startServerCommand": "npm run preview", "startServerReadyPattern": "Local:", "numberOfRuns": 3}, "assert": {"assertions": {"categories:performance": ["error", {"minScore": 0.85}], "categories:accessibility": ["error", {"minScore": 0.9}], "categories:best-practices": ["error", {"minScore": 0.9}], "categories:seo": ["error", {"minScore": 0.8}], "categories:pwa": ["warn", {"minScore": 0.7}]}}, "upload": {"target": "temporary-public-storage"}}}