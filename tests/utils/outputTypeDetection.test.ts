/**
 * Unit Tests for Output Type Detection Utility
 * Sprint 1 Deliverable - Comprehensive Testing
 */

import { describe, it, expect, vi } from 'vitest';
import {
  detectOutputType,
  createDetectionDebouncer,
  validateDetectionAccuracy,
  getDetectionStats
} from '$lib/utils/outputTypeDetection';

describe('Output Type Detection Utilities', () => {
  describe('detectOutputType', () => {
    it('should detect course type correctly', () => {
      const coursePrompts = [
        'Create a comprehensive course about web development',
        'Build a tutorial series for beginners',
        'I want to learn JavaScript step by step',
        'Teach me how to code',
        'Create a beginner guide to React',
        'Educational content about Python programming',
        'Training course for advanced developers',
        'Learning path for full stack development'
      ];

      coursePrompts.forEach(prompt => {
        const result = detectOutputType(prompt);
        expect(result.type).toBe('course');
        expect(result.confidence).toBeGreaterThan(60);
        expect(result.keywords.length).toBeGreaterThan(0);
      });
    });

    it('should detect article type correctly', () => {
      const articlePrompts = [
        'Write an article about React hooks',
        'Blog post explaining JavaScript closures',
        'Article discussing the benefits of TypeScript',
        'Explain how CSS Grid works',
        'What are the pros and cons of Vue.js',
        'Analysis of modern web frameworks',
        'Review of the latest JavaScript features',
        'Opinion piece on software development trends'
      ];

      articlePrompts.forEach(prompt => {
        const result = detectOutputType(prompt);
        expect(result.type).toBe('article');
        expect(result.confidence).toBeGreaterThan(60);
        expect(result.keywords.length).toBeGreaterThan(0);
      });
    });

    it('should detect website type correctly', () => {
      const websitePrompts = [
        'Build a professional business website',
        'Create a portfolio site for developers',
        'Design a landing page for startups',
        'Build an ecommerce store',
        'Create a company website with dashboard',
        'Web application for project management',
        'Responsive website for mobile users',
        'Online platform for course delivery'
      ];

      websitePrompts.forEach(prompt => {
        const result = detectOutputType(prompt);
        expect(result.type).toBe('website');
        expect(result.confidence).toBeGreaterThan(60);
        expect(result.keywords.length).toBeGreaterThan(0);
      });
    });

    it('should handle empty or invalid prompts', () => {
      const invalidPrompts = ['', '   ', null, undefined];

      invalidPrompts.forEach(prompt => {
        const result = detectOutputType(prompt as string);
        expect(result.type).toBe('article'); // Default fallback
        expect(result.confidence).toBe(0);
        expect(result.keywords).toEqual([]);
      });
    });

    it('should handle ambiguous prompts with low confidence', () => {
      const ambiguousPrompts = [
        'Something unclear',
        'Random text without keywords',
        'Generic content request',
        'Help me with stuff'
      ];

      ambiguousPrompts.forEach(prompt => {
        const result = detectOutputType(prompt);
        expect(result.confidence).toBeLessThan(70);
        expect(['course', 'article', 'website']).toContain(result.type);
      });
    });

    it('should return appropriate confidence scores', () => {
      // High confidence test
      const highConfidencePrompt = 'Create a comprehensive course tutorial for beginners to learn web development step by step';
      const highResult = detectOutputType(highConfidencePrompt);
      expect(highResult.confidence).toBeGreaterThan(80);

      // Medium confidence test
      const mediumConfidencePrompt = 'Create something about programming';
      const mediumResult = detectOutputType(mediumConfidencePrompt);
      expect(mediumResult.confidence).toBeLessThan(80);

      // Low confidence test
      const lowConfidencePrompt = 'Random text';
      const lowResult = detectOutputType(lowConfidencePrompt);
      expect(lowResult.confidence).toBeLessThan(50);
    });

    it('should extract relevant keywords', () => {
      const prompt = 'Create a comprehensive course tutorial for beginners';
      const result = detectOutputType(prompt);
      
      expect(result.keywords).toContain('course');
      expect(result.keywords).toContain('tutorial');
      expect(result.keywords).toContain('beginner');
      expect(result.keywords.length).toBeLessThanOrEqual(5); // Limited to 5 keywords
    });

    it('should handle case insensitive detection', () => {
      const prompts = [
        'CREATE A COURSE ABOUT WEB DEVELOPMENT',
        'create a course about web development',
        'Create A Course About Web Development',
        'CrEaTe A cOuRsE aBoUt WeB dEvElOpMeNt'
      ];

      prompts.forEach(prompt => {
        const result = detectOutputType(prompt);
        expect(result.type).toBe('course');
        expect(result.confidence).toBeGreaterThan(60);
      });
    });
  });

  describe('createDetectionDebouncer', () => {
    it('should create a debounced function', () => {
      const mockCallback = vi.fn();
      const debouncedFn = createDetectionDebouncer(mockCallback, 100);

      expect(typeof debouncedFn).toBe('function');
    });

    it('should debounce function calls', async () => {
      const mockCallback = vi.fn();
      const debouncedFn = createDetectionDebouncer(mockCallback, 50);

      // Call multiple times quickly
      debouncedFn('test 1');
      debouncedFn('test 2');
      debouncedFn('test 3');

      // Should not have been called yet
      expect(mockCallback).not.toHaveBeenCalled();

      // Wait for debounce delay
      await new Promise(resolve => setTimeout(resolve, 60));

      // Should have been called once with the last value
      expect(mockCallback).toHaveBeenCalledTimes(1);
    });

    it('should use default delay when not specified', () => {
      const mockCallback = vi.fn();
      const debouncedFn = createDetectionDebouncer(mockCallback);

      expect(typeof debouncedFn).toBe('function');
    });
  });

  describe('validateDetectionAccuracy', () => {
    it('should validate accurate detections', () => {
      const testCases = [
        { prompt: 'Create a course about programming', expected: 'course' as const },
        { prompt: 'Write an article about JavaScript', expected: 'article' as const },
        { prompt: 'Build a business website', expected: 'website' as const }
      ];

      testCases.forEach(({ prompt, expected }) => {
        const isAccurate = validateDetectionAccuracy(prompt, expected);
        expect(typeof isAccurate).toBe('boolean');
      });
    });

    it('should return false for inaccurate detections', () => {
      // This test assumes the detection might not be perfect for edge cases
      const edgeCases = [
        { prompt: 'Random text', expected: 'course' as const },
        { prompt: 'Unclear request', expected: 'website' as const }
      ];

      edgeCases.forEach(({ prompt, expected }) => {
        const isAccurate = validateDetectionAccuracy(prompt, expected);
        // We don't assert the result since it depends on the actual detection
        expect(typeof isAccurate).toBe('boolean');
      });
    });
  });

  describe('getDetectionStats', () => {
    it('should calculate detection statistics', () => {
      const testPrompts = [
        { text: 'Create a course about web development', expected: 'course' as const },
        { text: 'Write an article about React', expected: 'article' as const },
        { text: 'Build a business website', expected: 'website' as const },
        { text: 'Tutorial for beginners', expected: 'course' as const },
        { text: 'Blog post about JavaScript', expected: 'article' as const }
      ];

      const stats = getDetectionStats(testPrompts);

      expect(stats.totalTests).toBe(5);
      expect(stats.correctPredictions).toBeGreaterThanOrEqual(0);
      expect(stats.correctPredictions).toBeLessThanOrEqual(5);
      expect(stats.accuracy).toBeGreaterThanOrEqual(0);
      expect(stats.accuracy).toBeLessThanOrEqual(100);
      expect(stats.averageConfidence).toBeGreaterThanOrEqual(0);
      expect(stats.averageConfidence).toBeLessThanOrEqual(100);
    });

    it('should handle empty test set', () => {
      const stats = getDetectionStats([]);

      expect(stats.totalTests).toBe(0);
      expect(stats.correctPredictions).toBe(0);
      expect(stats.accuracy).toBeNaN(); // 0/0 = NaN
      expect(stats.averageConfidence).toBeNaN(); // 0/0 = NaN
    });

    it('should calculate 100% accuracy for perfect detection', () => {
      // Create test cases that should be easily detectable
      const perfectTestCases = [
        { text: 'Create a comprehensive course tutorial for beginners', expected: 'course' as const },
        { text: 'Write a detailed article explaining the topic', expected: 'article' as const },
        { text: 'Build a professional business website', expected: 'website' as const }
      ];

      const stats = getDetectionStats(perfectTestCases);

      expect(stats.totalTests).toBe(3);
      // We expect high accuracy but don't assert 100% since detection might vary
      expect(stats.accuracy).toBeGreaterThan(50);
    });
  });

  describe('Performance Requirements', () => {
    it('should meet detection speed requirements', () => {
      const startTime = performance.now();
      
      // Test detection speed with various prompts
      const testPrompts = [
        'Create a course',
        'Write an article',
        'Build a website',
        'Tutorial for beginners',
        'Blog post about technology'
      ];

      testPrompts.forEach(prompt => {
        detectOutputType(prompt);
      });

      const endTime = performance.now();
      const totalTime = endTime - startTime;
      const averageTime = totalTime / testPrompts.length;

      // Should be much faster than 100ms per detection
      expect(averageTime).toBeLessThan(100);
    });

    it('should handle long prompts efficiently', () => {
      const longPrompt = 'Create a comprehensive course about web development that covers HTML, CSS, JavaScript, React, Node.js, databases, deployment, testing, and best practices for modern web development including responsive design, accessibility, performance optimization, and security considerations for building scalable web applications'.repeat(3);

      const startTime = performance.now();
      const result = detectOutputType(longPrompt);
      const endTime = performance.now();

      expect(endTime - startTime).toBeLessThan(100); // Should still be fast
      expect(result.type).toBe('course'); // Should still detect correctly
    });
  });
});
