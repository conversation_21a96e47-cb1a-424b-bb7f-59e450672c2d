/**
 * Unit Tests for Validation Utilities
 * Sprint 1 Deliverable - Comprehensive Testing
 */

import { describe, it, expect } from 'vitest';
import {
  validateUrl,
  validatePrompt,
  validateAutonomousInput,
  createValidator
} from '$lib/utils/validation';

describe('Validation Utilities', () => {
  describe('validateUrl', () => {
    it('should validate correct URLs', () => {
      const validUrls = [
        'https://example.com',
        'http://example.com',
        'https://www.example.com',
        'https://example.com/path',
        'https://example.com/path?query=value',
        'https://subdomain.example.com',
        'https://example.com:8080',
        'https://example.co.uk'
      ];

      validUrls.forEach(url => {
        const result = validateUrl(url);
        expect(result.isValid).toBe(true);
        expect(result.errors).toEqual([]);
      });
    });

    it('should reject invalid URLs', () => {
      const invalidUrls = [
        '',
        '   ',
        'not-a-url',
        'ftp://example.com',
        'example.com',
        'www.example.com',
        'https://',
        'https://.',
        'https://.com',
        'javascript:alert(1)'
      ];

      invalidUrls.forEach(url => {
        const result = validateUrl(url);
        expect(result.isValid).toBe(false);
        expect(result.errors.length).toBeGreaterThan(0);
      });
    });

    it('should provide specific error messages', () => {
      const result = validateUrl('');
      expect(result.errors).toContain('URL is required');

      const result2 = validateUrl('example.com');
      expect(result2.errors).toContain('URL must start with http:// or https://');

      const result3 = validateUrl('https://invalid..url');
      expect(result3.errors.length).toBeGreaterThan(0);
    });
  });

  describe('validatePrompt', () => {
    it('should validate correct prompts', () => {
      const validPrompts = [
        'Create a course about web development',
        'Write an article explaining React hooks',
        'Build a professional business website',
        'A'.repeat(10), // Minimum length
        'A'.repeat(500), // Maximum length
        'This is a meaningful prompt with various characters 123!'
      ];

      validPrompts.forEach(prompt => {
        const result = validatePrompt(prompt);
        expect(result.isValid).toBe(true);
        expect(result.errors).toEqual([]);
      });
    });

    it('should reject invalid prompts', () => {
      const invalidPrompts = [
        '',
        '   ',
        'Short', // Less than 10 characters
        'A'.repeat(501), // More than 500 characters
        'aaa', // Not meaningful (too few unique characters)
        '   a   ' // Mostly spaces
      ];

      invalidPrompts.forEach(prompt => {
        const result = validatePrompt(prompt);
        expect(result.isValid).toBe(false);
        expect(result.errors.length).toBeGreaterThan(0);
      });
    });

    it('should provide specific error messages', () => {
      const result1 = validatePrompt('');
      expect(result1.errors).toContain('Prompt is required');

      const result2 = validatePrompt('Short');
      expect(result2.errors).toContain('Prompt must be at least 10 characters long');

      const result3 = validatePrompt('A'.repeat(501));
      expect(result3.errors).toContain('Prompt must be less than 500 characters');

      const result4 = validatePrompt('aaa');
      expect(result4.errors).toContain('Prompt must contain meaningful content');
    });
  });

  describe('validateAutonomousInput', () => {
    it('should validate correct input objects', () => {
      const validInputs = [
        {
          url: 'https://example.com',
          prompt: 'Create a course about web development'
        },
        {
          url: 'http://test.com',
          prompt: 'Write an article about JavaScript'
        }
      ];

      validInputs.forEach(input => {
        const result = validateAutonomousInput(input);
        expect(result.isValid).toBe(true);
        expect(result.errors).toEqual([]);
      });
    });

    it('should reject invalid input objects', () => {
      const invalidInputs = [
        null,
        undefined,
        'not an object',
        123,
        [],
        {},
        { url: 'invalid-url', prompt: 'Valid prompt here' },
        { url: 'https://example.com', prompt: 'Short' },
        { url: '', prompt: '' }
      ];

      invalidInputs.forEach(input => {
        const result = validateAutonomousInput(input);
        expect(result.isValid).toBe(false);
        expect(result.errors.length).toBeGreaterThan(0);
      });
    });

    it('should combine URL and prompt validation errors', () => {
      const input = {
        url: 'invalid-url',
        prompt: 'Short'
      };

      const result = validateAutonomousInput(input);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(1); // Should have errors from both validations
    });
  });

  describe('createValidator', () => {
    it('should create validator with required rule', () => {
      const validator = createValidator({ required: true });

      expect(validator('').isValid).toBe(false);
      expect(validator('   ').isValid).toBe(false);
      expect(validator(null).isValid).toBe(false);
      expect(validator(undefined).isValid).toBe(false);
      expect(validator('valid value').isValid).toBe(true);
    });

    it('should create validator with length rules', () => {
      const validator = createValidator({
        minLength: 5,
        maxLength: 10
      });

      expect(validator('1234').isValid).toBe(false); // Too short
      expect(validator('12345').isValid).toBe(true); // Minimum
      expect(validator('1234567890').isValid).toBe(true); // Maximum
      expect(validator('12345678901').isValid).toBe(false); // Too long
    });

    it('should create validator with pattern rule', () => {
      const emailValidator = createValidator({
        pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      });

      expect(emailValidator('invalid-email').isValid).toBe(false);
      expect(emailValidator('<EMAIL>').isValid).toBe(true);
      expect(emailValidator('<EMAIL>').isValid).toBe(true);
    });

    it('should create validator with custom rule', () => {
      const validator = createValidator({
        custom: (value: string) => {
          if (value && value.includes('forbidden')) {
            return 'Value contains forbidden word';
          }
          return null;
        }
      });

      expect(validator('normal text').isValid).toBe(true);
      expect(validator('text with forbidden word').isValid).toBe(false);
      expect(validator('text with forbidden word').errors).toContain('Value contains forbidden word');
    });

    it('should combine multiple validation rules', () => {
      const validator = createValidator({
        required: true,
        minLength: 5,
        maxLength: 20,
        pattern: /^[a-zA-Z\s]+$/,
        custom: (value: string) => {
          if (value && value.toLowerCase().includes('test')) {
            return 'Test values not allowed';
          }
          return null;
        }
      });

      expect(validator('').isValid).toBe(false); // Required
      expect(validator('1234').isValid).toBe(false); // Too short
      expect(validator('Valid Name').isValid).toBe(true); // All rules pass
      expect(validator('Valid Name With Numbers 123').isValid).toBe(false); // Pattern fails
      expect(validator('Test Name').isValid).toBe(false); // Custom rule fails
    });
  });
});
