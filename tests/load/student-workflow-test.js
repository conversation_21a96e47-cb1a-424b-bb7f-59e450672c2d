// VybeCoding.ai Student Workflow Load Test
// Simulates realistic student learning journeys under load

import http from 'k6/http';
import { check, sleep, group } from 'k6';
import { Rate, Trend, Counter } from 'k6/metrics';
import { htmlReport } from 'https://raw.githubusercontent.com/benc-uk/k6-reporter/main/dist/bundle.js';
import { textSummary } from 'https://jslib.k6.io/k6-summary/0.0.1/index.js';

// Custom metrics for student workflow
const studentLoginRate = new Rate('student_login_success');
const courseEnrollmentRate = new Rate('course_enrollment_success');
const lessonCompletionRate = new Rate('lesson_completion_success');
const quizSubmissionRate = new Rate('quiz_submission_success');
const progressTrackingRate = new Rate('progress_tracking_success');
const learningPathTime = new Trend('learning_path_duration');
const sessionDuration = new Trend('student_session_duration');
const contentLoadTime = new Trend('content_load_time');
const interactionCounter = new Counter('student_interactions');

// Test configuration for student workflow
export const options = {
  scenarios: {
    student_learning_journey: {
      executor: 'ramping-vus',
      startVUs: 0,
      stages: [
        { duration: '3m', target: 20 }, // Gradual ramp up
        { duration: '10m', target: 50 }, // Sustained learning activity
        { duration: '5m', target: 80 }, // Peak learning hours
        { duration: '10m', target: 80 }, // Sustained peak
        { duration: '5m', target: 20 }, // Wind down
        { duration: '2m', target: 0 }, // Complete
      ],
    },
    concurrent_quiz_taking: {
      executor: 'constant-vus',
      vus: 30,
      duration: '15m',
      startTime: '5m', // Start after initial ramp up
    },
  },
  thresholds: {
    // Student-specific performance thresholds
    student_login_success: ['rate>0.98'], // 98% login success
    course_enrollment_success: ['rate>0.95'], // 95% enrollment success
    lesson_completion_success: ['rate>0.97'], // 97% lesson completion
    quiz_submission_success: ['rate>0.95'], // 95% quiz submission success
    progress_tracking_success: ['rate>0.99'], // 99% progress tracking
    learning_path_duration: ['p(95)<300000'], // 95% complete path under 5 minutes
    content_load_time: ['p(95)<3000'], // 95% content loads under 3s
    http_req_duration: ['p(95)<2000'], // Overall response time
    http_req_failed: ['rate<0.05'], // Error rate under 5%
  },
};

// Student test data
const studentProfiles = [
  {
    email: '<EMAIL>',
    password: 'StudentPass123!',
    learningStyle: 'visual',
    preferredPace: 'fast',
  },
  {
    email: '<EMAIL>',
    password: 'StudentPass123!',
    learningStyle: 'hands-on',
    preferredPace: 'medium',
  },
  {
    email: '<EMAIL>',
    password: 'StudentPass123!',
    learningStyle: 'reading',
    preferredPace: 'slow',
  },
  {
    email: '<EMAIL>',
    password: 'StudentPass123!',
    learningStyle: 'mixed',
    preferredPace: 'fast',
  },
  {
    email: '<EMAIL>',
    password: 'StudentPass123!',
    learningStyle: 'visual',
    preferredPace: 'medium',
  },
];

const courses = [
  {
    id: 1,
    name: 'Introduction to AI',
    difficulty: 'beginner',
    estimatedTime: 120,
  },
  {
    id: 2,
    name: 'Web Development Fundamentals',
    difficulty: 'beginner',
    estimatedTime: 180,
  },
  {
    id: 3,
    name: 'Advanced JavaScript',
    difficulty: 'intermediate',
    estimatedTime: 240,
  },
  {
    id: 4,
    name: 'Machine Learning Basics',
    difficulty: 'intermediate',
    estimatedTime: 300,
  },
  {
    id: 5,
    name: 'Full Stack Development',
    difficulty: 'advanced',
    estimatedTime: 480,
  },
];

const baseUrl = __ENV.BASE_URL || 'http://localhost:3000';

// Main student workflow test
export default function () {
  const student = studentProfiles[__VU % studentProfiles.length];
  const sessionStart = Date.now();

  group('Student Learning Session', () => {
    const authToken = authenticateStudent(student);

    if (authToken) {
      simulateStudentLearningJourney(authToken, student);
    }
  });

  const sessionEnd = Date.now();
  sessionDuration.add(sessionEnd - sessionStart);

  // Simulate break between learning sessions
  sleep(Math.random() * 5 + 2);
}

// Authenticate student
function authenticateStudent(student) {
  const loginPayload = {
    email: student.email,
    password: student.password,
  };

  const response = http.post(
    `${baseUrl}/api/auth/login`,
    JSON.stringify(loginPayload),
    {
      headers: { 'Content-Type': 'application/json' },
    }
  );

  interactionCounter.add(1);

  const loginSuccess = check(response, {
    'student login successful': r => r.status === 200 || r.status === 201,
    'login response time acceptable': r => r.timings.duration < 2000,
    'login returns valid token': r => {
      try {
        const body = JSON.parse(r.body);
        return !!(body.token || body.access_token || body.jwt);
      } catch (e) {
        return false;
      }
    },
  });

  studentLoginRate.add(loginSuccess);

  if (loginSuccess && response.status < 400) {
    try {
      const responseBody = JSON.parse(response.body);
      return (
        responseBody.token || responseBody.access_token || responseBody.jwt
      );
    } catch (e) {
      return null;
    }
  }

  return null;
}

// Simulate complete student learning journey
function simulateStudentLearningJourney(token, student) {
  const headers = {
    Authorization: `Bearer ${token}`,
    'Content-Type': 'application/json',
  };

  const journeyStart = Date.now();

  // 1. Browse available courses
  group('Course Discovery', () => {
    browseCourses(headers, student);
  });

  // 2. Enroll in a course
  const selectedCourse = selectCourseBasedOnProfile(student);
  let enrollmentSuccess = false;

  group('Course Enrollment', () => {
    enrollmentSuccess = enrollInCourse(headers, selectedCourse);
  });

  if (enrollmentSuccess) {
    // 3. Access course content and complete lessons
    group('Learning Activities', () => {
      completeLearningActivities(headers, selectedCourse, student);
    });

    // 4. Take quizzes and assessments
    group('Assessments', () => {
      takeQuizzes(headers, selectedCourse);
    });

    // 5. Track progress
    group('Progress Tracking', () => {
      checkProgress(headers, selectedCourse);
    });
  }

  const journeyEnd = Date.now();
  learningPathTime.add(journeyEnd - journeyStart);
}

// Browse available courses
function browseCourses(headers, student) {
  const response = http.get(`${baseUrl}/api/courses`, { headers });

  interactionCounter.add(1);
  contentLoadTime.add(response.timings.duration);

  check(response, {
    'courses list loads': r => r.status === 200,
    'courses load time acceptable': r => r.timings.duration < 2000,
    'courses data is valid': r => {
      try {
        const body = JSON.parse(r.body);
        return Array.isArray(body) || Array.isArray(body.courses);
      } catch (e) {
        return false;
      }
    },
  });

  // Simulate browsing time based on learning style
  const browsingTime =
    student.learningStyle === 'fast'
      ? 2
      : student.learningStyle === 'medium'
        ? 4
        : 6;
  sleep(browsingTime);
}

// Select course based on student profile
function selectCourseBasedOnProfile(student) {
  // Beginners prefer easier courses, advanced students prefer challenging ones
  const availableCourses =
    student.preferredPace === 'slow'
      ? courses.filter(c => c.difficulty === 'beginner')
      : student.preferredPace === 'fast'
        ? courses.filter(
            c => c.difficulty === 'advanced' || c.difficulty === 'intermediate'
          )
        : courses;

  return availableCourses[Math.floor(Math.random() * availableCourses.length)];
}

// Enroll in selected course
function enrollInCourse(headers, course) {
  const response = http.post(
    `${baseUrl}/api/courses/${course.id}/enroll`,
    '{}',
    { headers }
  );

  interactionCounter.add(1);

  const enrollmentSuccess = check(response, {
    'course enrollment successful': r =>
      r.status === 200 || r.status === 201 || r.status === 409,
    'enrollment response time OK': r => r.timings.duration < 1500,
  });

  courseEnrollmentRate.add(enrollmentSuccess);

  if (enrollmentSuccess) {
    sleep(1); // Brief pause after enrollment
  }

  return enrollmentSuccess;
}

// Complete learning activities
function completeLearningActivities(headers, course, student) {
  // Get course lessons
  const lessonsResponse = http.get(
    `${baseUrl}/api/courses/${course.id}/lessons`,
    { headers }
  );

  interactionCounter.add(1);
  contentLoadTime.add(lessonsResponse.timings.duration);

  if (lessonsResponse.status === 200) {
    try {
      const lessons = JSON.parse(lessonsResponse.body);
      const lessonArray = Array.isArray(lessons)
        ? lessons
        : lessons.lessons || [];

      // Complete 2-4 lessons based on student pace
      const lessonsToComplete =
        student.preferredPace === 'fast'
          ? 4
          : student.preferredPace === 'medium'
            ? 3
            : 2;

      for (
        let i = 0;
        i < Math.min(lessonsToComplete, lessonArray.length);
        i++
      ) {
        const lesson = lessonArray[i];
        completeLesson(headers, lesson, student);
      }
    } catch (e) {
      console.error('Failed to parse lessons response:', e);
    }
  }
}

// Complete individual lesson
function completeLesson(headers, lesson, student) {
  // Access lesson content
  const contentResponse = http.get(`${baseUrl}/api/lessons/${lesson.id}`, {
    headers,
  });

  interactionCounter.add(1);
  contentLoadTime.add(contentResponse.timings.duration);

  const contentLoaded = check(contentResponse, {
    'lesson content loads': r => r.status === 200,
    'lesson load time acceptable': r => r.timings.duration < 3000,
  });

  if (contentLoaded) {
    // Simulate reading/studying time based on learning style and pace
    const studyTime = calculateStudyTime(student, lesson);
    sleep(studyTime);

    // Mark lesson as complete
    const completionResponse = http.post(
      `${baseUrl}/api/lessons/${lesson.id}/complete`,
      '{}',
      { headers }
    );

    interactionCounter.add(1);

    const completionSuccess = check(completionResponse, {
      'lesson completion recorded': r => r.status === 200 || r.status === 201,
      'completion response time OK': r => r.timings.duration < 1000,
    });

    lessonCompletionRate.add(completionSuccess);

    sleep(1); // Brief pause between lessons
  }
}

// Calculate study time based on student profile
function calculateStudyTime(student, lesson) {
  let baseTime = 10; // Base 10 seconds for simulation

  // Adjust for learning style
  if (student.learningStyle === 'reading') baseTime *= 1.5;
  if (student.learningStyle === 'visual') baseTime *= 1.2;
  if (student.learningStyle === 'hands-on') baseTime *= 1.8;

  // Adjust for pace
  if (student.preferredPace === 'slow') baseTime *= 1.5;
  if (student.preferredPace === 'fast') baseTime *= 0.7;

  return Math.max(2, Math.random() * baseTime + 5);
}

// Take quizzes and assessments
function takeQuizzes(headers, course) {
  const quizResponse = http.get(`${baseUrl}/api/courses/${course.id}/quiz`, {
    headers,
  });

  interactionCounter.add(1);
  contentLoadTime.add(quizResponse.timings.duration);

  if (quizResponse.status === 200) {
    try {
      const quiz = JSON.parse(quizResponse.body);

      if (quiz && quiz.questions) {
        // Simulate thinking time
        sleep(Math.random() * 10 + 5);

        // Generate quiz answers (simulate student responses)
        const answers = quiz.questions.map(question => ({
          questionId: question.id,
          answer: generateQuizAnswer(question),
        }));

        // Submit quiz
        const submissionResponse = http.post(
          `${baseUrl}/api/quizzes/${quiz.id}/submit`,
          JSON.stringify({ answers }),
          { headers }
        );

        interactionCounter.add(1);

        const submissionSuccess = check(submissionResponse, {
          'quiz submission successful': r =>
            r.status === 200 || r.status === 201,
          'quiz submission time OK': r => r.timings.duration < 2000,
        });

        quizSubmissionRate.add(submissionSuccess);
      }
    } catch (e) {
      console.error('Failed to parse quiz response:', e);
    }
  }
}

// Generate realistic quiz answer
function generateQuizAnswer(question) {
  if (question.type === 'multiple_choice' && question.options) {
    // 80% chance of correct answer, 20% chance of random answer
    if (Math.random() < 0.8 && question.correctAnswer) {
      return question.correctAnswer;
    } else {
      return question.options[
        Math.floor(Math.random() * question.options.length)
      ];
    }
  } else if (question.type === 'true_false') {
    return Math.random() < 0.7
      ? question.correctAnswer
      : !question.correctAnswer;
  } else {
    // For text answers, return a simple response
    return 'Student response for load testing';
  }
}

// Check progress tracking
function checkProgress(headers, course) {
  const progressResponse = http.get(
    `${baseUrl}/api/courses/${course.id}/progress`,
    { headers }
  );

  interactionCounter.add(1);

  const progressSuccess = check(progressResponse, {
    'progress tracking works': r => r.status === 200,
    'progress response time OK': r => r.timings.duration < 1000,
    'progress data is valid': r => {
      try {
        const body = JSON.parse(r.body);
        return body && typeof body.completionPercentage !== 'undefined';
      } catch (e) {
        return false;
      }
    },
  });

  progressTrackingRate.add(progressSuccess);

  // Also check overall student dashboard
  const dashboardResponse = http.get(`${baseUrl}/api/student/dashboard`, {
    headers,
  });

  interactionCounter.add(1);

  check(dashboardResponse, {
    'student dashboard loads': r => r.status === 200,
    'dashboard response time OK': r => r.timings.duration < 2000,
  });
}

// Custom summary report for student workflow
export function handleSummary(data) {
  return {
    'results/student-workflow-summary.html': htmlReport(data),
    'results/student-workflow-summary.json': JSON.stringify(data, null, 2),
    stdout: textSummary(data, { indent: ' ', enableColors: true }),
  };
}
