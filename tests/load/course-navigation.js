// VybeCoding.ai Load Test - Course Navigation & Learning
// K6 load testing script for course browsing and learning activities

import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate, Trend, Counter } from 'k6/metrics';

// Custom metrics
export const errorRate = new Rate('errors');
export const courseLoadTime = new Trend('course_load_time');
export const lessonCompletions = new Counter('lesson_completions');
export const codeExecutions = new Counter('code_executions');

// Test configuration
export const options = {
  stages: [
    { duration: '3m', target: 20 }, // Ramp up to 20 users
    { duration: '10m', target: 100 }, // Stay at 100 users
    { duration: '5m', target: 200 }, // Ramp up to 200 users
    { duration: '10m', target: 200 }, // Stay at 200 users
    { duration: '3m', target: 0 }, // Ramp down
  ],
  thresholds: {
    http_req_duration: ['p(95)<1000'], // 95% of requests under 1s
    http_req_failed: ['rate<0.01'], // Error rate under 1%
    errors: ['rate<0.01'],
    course_load_time: ['p(95)<2000'], // Course loading under 2s
  },
};

const BASE_URL = __ENV.BASE_URL || 'http://localhost:3000';

// Test user credentials (pre-created test accounts)
const TEST_USERS = [
  { email: '<EMAIL>', password: 'TestPassword123!' },
  { email: '<EMAIL>', password: 'TestPassword123!' },
  { email: '<EMAIL>', password: 'TestPassword123!' },
  { email: '<EMAIL>', password: 'TestPassword123!' },
  { email: '<EMAIL>', password: 'TestPassword123!' },
];

// Sample course and lesson data
const COURSES = [
  { id: 'javascript-fundamentals', name: 'JavaScript Fundamentals' },
  { id: 'python-basics', name: 'Python Basics' },
  { id: 'web-development', name: 'Web Development' },
  { id: 'data-structures', name: 'Data Structures' },
];

const CODE_SAMPLES = [
  'console.log("Hello, World!");',
  'function add(a, b) { return a + b; }',
  'const numbers = [1, 2, 3, 4, 5];',
  'for (let i = 0; i < 10; i++) { console.log(i); }',
  'const user = { name: "John", age: 30 };',
];

// Main test function
export default function () {
  const user = TEST_USERS[Math.floor(Math.random() * TEST_USERS.length)];

  // Authenticate user
  const authToken = authenticateUser(user);
  if (!authToken) {
    errorRate.add(1);
    return;
  }

  // Simulate course navigation and learning
  testCourseNavigation(authToken);

  sleep(2); // Pause between iterations
}

function authenticateUser(user) {
  const loginPayload = {
    email: user.email,
    password: user.password,
  };

  const loginResponse = http.post(
    `${BASE_URL}/api/auth/login`,
    JSON.stringify(loginPayload),
    {
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );

  const loginSuccess = check(loginResponse, {
    'login successful': r => r.status === 200,
  });

  if (!loginSuccess) {
    console.error(`Login failed for ${user.email}: ${loginResponse.status}`);
    return null;
  }

  try {
    const loginBody = JSON.parse(loginResponse.body);
    return loginBody.token || loginBody.accessToken || loginBody.jwt;
  } catch (e) {
    console.error('Failed to parse login response');
    return null;
  }
}

function testCourseNavigation(authToken) {
  const headers = {
    Authorization: `Bearer ${authToken}`,
    'Content-Type': 'application/json',
  };

  // Step 1: Browse available courses
  const coursesResponse = http.get(`${BASE_URL}/api/courses`, { headers });

  check(coursesResponse, {
    'courses list loaded': r => r.status === 200,
    'courses contain data': r => {
      try {
        const body = JSON.parse(r.body);
        return Array.isArray(body) && body.length > 0;
      } catch (e) {
        return false;
      }
    },
  }) || errorRate.add(1);

  sleep(1); // User browsing time

  // Step 2: Select and load a specific course
  const selectedCourse = COURSES[Math.floor(Math.random() * COURSES.length)];
  const courseLoadStart = Date.now();

  const courseResponse = http.get(
    `${BASE_URL}/api/courses/${selectedCourse.id}`,
    { headers }
  );

  const courseLoadSuccess = check(courseResponse, {
    'course loaded successfully': r => r.status === 200,
    'course has lessons': r => {
      try {
        const body = JSON.parse(r.body);
        return body.lessons && body.lessons.length > 0;
      } catch (e) {
        return false;
      }
    },
  });

  if (!courseLoadSuccess) {
    errorRate.add(1);
    return;
  }

  courseLoadTime.add(Date.now() - courseLoadStart);

  // Parse course data
  let courseData;
  try {
    courseData = JSON.parse(courseResponse.body);
  } catch (e) {
    console.error('Failed to parse course response');
    errorRate.add(1);
    return;
  }

  sleep(2); // User reading course overview

  // Step 3: Navigate through lessons
  const lessons = courseData.lessons || [];
  const numLessonsToVisit = Math.min(3, lessons.length); // Visit up to 3 lessons

  for (let i = 0; i < numLessonsToVisit; i++) {
    const lesson = lessons[i];
    testLessonInteraction(lesson, authToken, headers);
    sleep(1); // Pause between lessons
  }

  // Step 4: Check progress
  const progressResponse = http.get(
    `${BASE_URL}/api/courses/${selectedCourse.id}/progress`,
    { headers }
  );

  check(progressResponse, {
    'progress loaded': r => r.status === 200,
    'progress contains data': r => {
      try {
        const body = JSON.parse(r.body);
        return typeof body.completion === 'number';
      } catch (e) {
        return false;
      }
    },
  }) || errorRate.add(1);
}

function testLessonInteraction(lesson, authToken, headers) {
  // Load lesson content
  const lessonResponse = http.get(`${BASE_URL}/api/lessons/${lesson.id}`, {
    headers,
  });

  const lessonLoadSuccess = check(lessonResponse, {
    'lesson loaded': r => r.status === 200,
    'lesson has content': r => {
      try {
        const body = JSON.parse(r.body);
        return body.content || body.description;
      } catch (e) {
        return false;
      }
    },
  });

  if (!lessonLoadSuccess) {
    errorRate.add(1);
    return;
  }

  sleep(3); // User reading lesson content

  // Simulate code execution if lesson has coding component
  if (Math.random() > 0.3) {
    // 70% chance of code execution
    testCodeExecution(lesson, authToken, headers);
  }

  // Mark lesson as completed
  const completionPayload = {
    lessonId: lesson.id,
    completed: true,
    timeSpent: Math.floor(Math.random() * 300) + 60, // 1-5 minutes
  };

  const completionResponse = http.post(
    `${BASE_URL}/api/lessons/${lesson.id}/complete`,
    JSON.stringify(completionPayload),
    { headers }
  );

  const completionSuccess = check(completionResponse, {
    'lesson completion recorded': r => r.status === 200 || r.status === 201,
  });

  if (completionSuccess) {
    lessonCompletions.add(1);
  } else {
    errorRate.add(1);
  }
}

function testCodeExecution(lesson, authToken, headers) {
  const codeToExecute =
    CODE_SAMPLES[Math.floor(Math.random() * CODE_SAMPLES.length)];

  const executionPayload = {
    code: codeToExecute,
    language: 'javascript',
    lessonId: lesson.id,
  };

  const executionResponse = http.post(
    `${BASE_URL}/api/code/execute`,
    JSON.stringify(executionPayload),
    { headers }
  );

  const executionSuccess = check(executionResponse, {
    'code execution successful': r => r.status === 200,
    'execution returns result': r => {
      try {
        const body = JSON.parse(r.body);
        return body.output !== undefined || body.result !== undefined;
      } catch (e) {
        return false;
      }
    },
  });

  if (executionSuccess) {
    codeExecutions.add(1);
  } else {
    errorRate.add(1);
  }

  sleep(1); // User reviewing execution results
}

// Setup function
export function setup() {
  console.log('Starting course navigation load test');
  console.log(`Target URL: ${BASE_URL}`);

  // Verify application accessibility
  const healthCheck = http.get(`${BASE_URL}/api/health`);
  if (healthCheck.status !== 200) {
    throw new Error(`Application not accessible: ${healthCheck.status}`);
  }

  // Verify test users can authenticate
  const testUser = TEST_USERS[0];
  const loginResponse = http.post(
    `${BASE_URL}/api/auth/login`,
    JSON.stringify({ email: testUser.email, password: testUser.password }),
    { headers: { 'Content-Type': 'application/json' } }
  );

  if (loginResponse.status !== 200) {
    console.warn('Test user authentication failed - some tests may fail');
  }

  return { baseUrl: BASE_URL };
}

// Teardown function
export function teardown(data) {
  console.log('Course navigation load test completed');
  console.log(`Total lesson completions: ${lessonCompletions.count}`);
  console.log(`Total code executions: ${codeExecutions.count}`);
}
