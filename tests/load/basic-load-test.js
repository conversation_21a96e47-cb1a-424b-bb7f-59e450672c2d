// VybeCoding.ai Basic Load Test
// Comprehensive load testing for core platform functionality

import http from 'k6/http';
import { check, sleep, group } from 'k6';
import { Rate, Trend, Counter } from 'k6/metrics';
import { htmlReport } from 'https://raw.githubusercontent.com/benc-uk/k6-reporter/main/dist/bundle.js';
import { textSummary } from 'https://jslib.k6.io/k6-summary/0.0.1/index.js';

// Custom metrics
const errorRate = new Rate('errors');
const responseTime = new Trend('response_time');
const pageLoadTime = new Trend('page_load_time');
const apiCallTime = new Trend('api_call_time');
const authSuccessRate = new Rate('auth_success');
const courseAccessRate = new Rate('course_access');
const requestCounter = new Counter('total_requests');

// Test configuration
export const options = {
  stages: [
    { duration: '2m', target: 10 }, // Ramp up to 10 users
    { duration: '5m', target: 50 }, // Normal load - 50 concurrent users
    { duration: '2m', target: 100 }, // Peak load - 100 concurrent users
    { duration: '5m', target: 100 }, // Sustained peak load
    { duration: '3m', target: 200 }, // Stress test - 200 concurrent users
    { duration: '2m', target: 0 }, // Ramp down
  ],
  thresholds: {
    // Performance thresholds
    http_req_duration: ['p(95)<2000'], // 95% of requests under 2s
    http_req_failed: ['rate<0.05'], // Error rate under 5%
    errors: ['rate<0.05'], // Custom error rate under 5%
    response_time: ['p(95)<2000'], // 95% response time under 2s
    page_load_time: ['p(95)<3000'], // 95% page loads under 3s
    api_call_time: ['p(95)<1000'], // 95% API calls under 1s
    auth_success: ['rate>0.95'], // 95% auth success rate
    course_access: ['rate>0.98'], // 98% course access success

    // Throughput thresholds
    http_reqs: ['rate>100'], // At least 100 requests/second
  },
  ext: {
    loadimpact: {
      projectID: 3596395,
      name: 'VybeCoding.ai Basic Load Test',
    },
  },
};

// Test data
const testUsers = [
  {
    email: '<EMAIL>',
    password: 'TestPass123!',
    role: 'student',
  },
  {
    email: '<EMAIL>',
    password: 'TestPass123!',
    role: 'student',
  },
  {
    email: '<EMAIL>',
    password: 'TestPass123!',
    role: 'student',
  },
  {
    email: '<EMAIL>',
    password: 'TestPass123!',
    role: 'instructor',
  },
  {
    email: '<EMAIL>',
    password: 'TestPass123!',
    role: 'instructor',
  },
];

const courseIds = [1, 2, 3, 4, 5];
const lessonIds = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];

// Base URL configuration
const baseUrl = __ENV.BASE_URL || 'http://localhost:3000';

// Main test function
export default function () {
  const user = testUsers[Math.floor(Math.random() * testUsers.length)];

  group('Homepage Load Test', () => {
    testHomepage();
  });

  group('Authentication Test', () => {
    const authToken = testAuthentication(user);
    if (authToken) {
      group('Authenticated User Journey', () => {
        testAuthenticatedJourney(authToken, user);
      });
    }
  });

  // Random sleep between 1-3 seconds to simulate real user behavior
  sleep(Math.random() * 2 + 1);
}

// Test homepage performance
function testHomepage() {
  const startTime = Date.now();

  const response = http.get(`${baseUrl}/`);

  const loadTime = Date.now() - startTime;
  pageLoadTime.add(loadTime);
  responseTime.add(response.timings.duration);
  requestCounter.add(1);

  const success = check(response, {
    'homepage status is 200': r => r.status === 200,
    'homepage loads in reasonable time': r => r.timings.duration < 2000,
    'homepage contains expected content': r => r.body.includes('VybeCoding'),
    'homepage has proper headers': r =>
      r.headers['Content-Type'] &&
      r.headers['Content-Type'].includes('text/html'),
  });

  errorRate.add(!success);

  // Test static assets
  if (response.status === 200) {
    group('Static Assets', () => {
      // Test CSS loading
      const cssResponse = http.get(`${baseUrl}/app.css`);
      check(cssResponse, {
        'CSS loads successfully': r => r.status === 200,
      });

      // Test favicon
      const faviconResponse = http.get(`${baseUrl}/favicon.ico`);
      check(faviconResponse, {
        'Favicon loads': r => r.status === 200 || r.status === 404, // 404 is acceptable
      });
    });
  }
}

// Test user authentication
function testAuthentication(user) {
  const startTime = Date.now();

  const loginPayload = {
    email: user.email,
    password: user.password,
  };

  const response = http.post(
    `${baseUrl}/api/auth/login`,
    JSON.stringify(loginPayload),
    {
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );

  const authTime = Date.now() - startTime;
  apiCallTime.add(authTime);
  responseTime.add(response.timings.duration);
  requestCounter.add(1);

  const authSuccess = check(response, {
    'login status is 200 or 201': r => r.status === 200 || r.status === 201,
    'login response time OK': r => r.timings.duration < 1000,
    'login returns token': r => {
      try {
        const body = JSON.parse(r.body);
        return body.token || body.access_token || body.jwt;
      } catch (e) {
        return false;
      }
    },
  });

  authSuccessRate.add(authSuccess);
  errorRate.add(!authSuccess);

  if (authSuccess && response.status < 400) {
    try {
      const responseBody = JSON.parse(response.body);
      return (
        responseBody.token || responseBody.access_token || responseBody.jwt
      );
    } catch (e) {
      console.error('Failed to parse auth response:', e);
      return null;
    }
  }

  return null;
}

// Test authenticated user journey
function testAuthenticatedJourney(token, user) {
  const headers = {
    Authorization: `Bearer ${token}`,
    'Content-Type': 'application/json',
  };

  // Test dashboard access
  group('Dashboard Access', () => {
    const response = http.get(`${baseUrl}/dashboard`, { headers });

    responseTime.add(response.timings.duration);
    requestCounter.add(1);

    check(response, {
      'dashboard loads': r => r.status === 200,
      'dashboard response time OK': r => r.timings.duration < 2000,
    });
  });

  // Test course listing
  group('Course Listing', () => {
    const response = http.get(`${baseUrl}/api/courses`, { headers });

    apiCallTime.add(response.timings.duration);
    requestCounter.add(1);

    const success = check(response, {
      'courses API responds': r => r.status === 200,
      'courses API response time OK': r => r.timings.duration < 1000,
      'courses API returns array': r => {
        try {
          const body = JSON.parse(r.body);
          return Array.isArray(body) || Array.isArray(body.courses);
        } catch (e) {
          return false;
        }
      },
    });

    courseAccessRate.add(success);
  });

  // Test course enrollment (for students)
  if (user.role === 'student') {
    group('Course Enrollment', () => {
      const courseId = courseIds[Math.floor(Math.random() * courseIds.length)];
      const response = http.post(
        `${baseUrl}/api/courses/${courseId}/enroll`,
        '{}',
        { headers }
      );

      apiCallTime.add(response.timings.duration);
      requestCounter.add(1);

      check(response, {
        'enrollment request processed': r =>
          r.status === 200 || r.status === 201 || r.status === 409, // 409 = already enrolled
        'enrollment response time OK': r => r.timings.duration < 1500,
      });
    });

    // Test lesson access
    group('Lesson Access', () => {
      const lessonId = lessonIds[Math.floor(Math.random() * lessonIds.length)];
      const response = http.get(`${baseUrl}/api/lessons/${lessonId}`, {
        headers,
      });

      apiCallTime.add(response.timings.duration);
      requestCounter.add(1);

      check(response, {
        'lesson loads': r => r.status === 200 || r.status === 404, // 404 acceptable if lesson doesn't exist
        'lesson response time OK': r => r.timings.duration < 1500,
      });
    });
  }

  // Test instructor features
  if (user.role === 'instructor') {
    group('Instructor Features', () => {
      // Test student progress view
      const response = http.get(`${baseUrl}/api/instructor/students`, {
        headers,
      });

      apiCallTime.add(response.timings.duration);
      requestCounter.add(1);

      check(response, {
        'instructor dashboard loads': r => r.status === 200 || r.status === 403, // 403 acceptable if not authorized
        'instructor response time OK': r => r.timings.duration < 2000,
      });
    });
  }

  // Test Vybe Qube access
  group('Vybe Qube Features', () => {
    const response = http.get(`${baseUrl}/vybe-qubes`, { headers });

    responseTime.add(response.timings.duration);
    requestCounter.add(1);

    check(response, {
      'vybe qubes page loads': r => r.status === 200,
      'vybe qubes response time OK': r => r.timings.duration < 2000,
    });
  });

  // Test API health endpoint
  group('Health Check', () => {
    const response = http.get(`${baseUrl}/api/health`, { headers });

    apiCallTime.add(response.timings.duration);
    requestCounter.add(1);

    check(response, {
      'health endpoint responds': r => r.status === 200,
      'health check is fast': r => r.timings.duration < 500,
    });
  });
}

// Custom summary report
export function handleSummary(data) {
  return {
    'results/basic-load-test-summary.html': htmlReport(data),
    'results/basic-load-test-summary.json': JSON.stringify(data, null, 2),
    stdout: textSummary(data, { indent: ' ', enableColors: true }),
  };
}
