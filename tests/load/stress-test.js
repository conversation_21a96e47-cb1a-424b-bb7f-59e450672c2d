// VybeCoding.ai Stress Test
// Finds breaking points and system limits

import http from 'k6/http';
import { check, sleep, group } from 'k6';
import { Rate, Trend, Counter, Gauge } from 'k6/metrics';
import { htmlReport } from 'https://raw.githubusercontent.com/benc-uk/k6-reporter/main/dist/bundle.js';
import { textSummary } from 'https://jslib.k6.io/k6-summary/0.0.1/index.js';

// Stress test specific metrics
const systemStability = new Rate('system_stability');
const breakingPointReached = new Rate('breaking_point_reached');
const recoveryTime = new Trend('system_recovery_time');
const maxConcurrentUsers = new Gauge('max_concurrent_users');
const resourceExhaustion = new Rate('resource_exhaustion');
const cascadingFailures = new Rate('cascading_failures');
const errorSpike = new Rate('error_spike');

// Aggressive stress test configuration
export const options = {
  scenarios: {
    // Gradual stress increase to find breaking point
    breaking_point_test: {
      executor: 'ramping-vus',
      startVUs: 0,
      stages: [
        { duration: '2m', target: 50 }, // Warm up
        { duration: '3m', target: 100 }, // Normal load
        { duration: '3m', target: 200 }, // High load
        { duration: '3m', target: 400 }, // Very high load
        { duration: '3m', target: 600 }, // Extreme load
        { duration: '3m', target: 800 }, // Breaking point search
        { duration: '3m', target: 1000 }, // Maximum stress
        { duration: '2m', target: 0 }, // Recovery test
      ],
    },
    // Spike test - sudden load increase
    spike_test: {
      executor: 'ramping-vus',
      startVUs: 10,
      stages: [
        { duration: '1m', target: 10 }, // Baseline
        { duration: '30s', target: 500 }, // Sudden spike
        { duration: '2m', target: 500 }, // Sustained spike
        { duration: '1m', target: 10 }, // Recovery
      ],
      startTime: '15m', // Start after breaking point test
    },
    // Volume test - sustained high load
    volume_test: {
      executor: 'constant-vus',
      vus: 300,
      duration: '10m',
      startTime: '25m', // Start after spike test
    },
  },
  thresholds: {
    // Relaxed thresholds for stress testing
    http_req_duration: ['p(95)<10000'], // Allow up to 10s under stress
    http_req_failed: ['rate<0.5'], // Allow 50% failure under extreme stress
    system_stability: ['rate>0.3'], // System should remain 30% stable
    breaking_point_reached: ['rate<1'], // Should not always reach breaking point
    recovery_time: ['p(95)<30000'], // Recovery under 30s
  },
};

const baseUrl = __ENV.BASE_URL || 'http://localhost:3000';

// Test users for stress testing
const stressTestUsers = Array.from({ length: 100 }, (_, i) => ({
  email: `stress_user_${i}@vybecoding.ai`,
  password: 'StressTest123!',
  id: i,
}));

// Main stress test function
export default function () {
  const user = stressTestUsers[__VU % stressTestUsers.length];
  const testStart = Date.now();

  group('Stress Test Session', () => {
    try {
      executeStressScenario(user);
      systemStability.add(1); // Successful execution
    } catch (error) {
      systemStability.add(0); // Failed execution
      console.error(`Stress test error for VU ${__VU}:`, error);
    }
  });

  // Minimal sleep to maximize stress
  sleep(Math.random() * 0.5);
}

// Execute stress test scenario
function executeStressScenario(user) {
  const scenario = Math.floor(Math.random() * 4);

  switch (scenario) {
    case 0:
      heavyPageLoadScenario(user);
      break;
    case 1:
      intensiveAPIScenario(user);
      break;
    case 2:
      concurrentGenerationScenario(user);
      break;
    case 3:
      mixedWorkloadScenario(user);
      break;
  }
}

// Heavy page load scenario
function heavyPageLoadScenario(user) {
  group('Heavy Page Load Stress', () => {
    // Rapid page requests
    const pages = ['/', '/courses', '/dashboard', '/vybe-qubes', '/community'];

    for (let i = 0; i < 5; i++) {
      const page = pages[Math.floor(Math.random() * pages.length)];
      const response = http.get(`${baseUrl}${page}`);

      const success = check(response, {
        'page loads under stress': r => r.status === 200,
        'page response time acceptable': r => r.timings.duration < 15000,
      });

      if (!success && response.status >= 500) {
        errorSpike.add(1);
        if (response.status === 503 || response.status === 504) {
          resourceExhaustion.add(1);
        }
      }

      // No sleep - maximum stress
    }
  });
}

// Intensive API scenario
function intensiveAPIScenario(user) {
  group('Intensive API Stress', () => {
    // Authenticate first
    const authToken = attemptAuthentication(user);

    if (authToken) {
      const headers = { Authorization: `Bearer ${authToken}` };

      // Rapid API calls
      const apiEndpoints = [
        '/api/courses',
        '/api/user/profile',
        '/api/dashboard/stats',
        '/api/vybe-qubes',
        '/api/progress',
      ];

      for (let i = 0; i < 10; i++) {
        const endpoint =
          apiEndpoints[Math.floor(Math.random() * apiEndpoints.length)];
        const response = http.get(`${baseUrl}${endpoint}`, { headers });

        const success = check(response, {
          'API responds under stress': r => r.status < 500,
          'API response time under stress': r => r.timings.duration < 10000,
        });

        if (!success) {
          cascadingFailures.add(1);
        }
      }
    }
  });
}

// Concurrent generation scenario
function concurrentGenerationScenario(user) {
  group('Concurrent Generation Stress', () => {
    const authToken = attemptAuthentication(user);

    if (authToken) {
      const headers = { Authorization: `Bearer ${authToken}` };

      // Attempt multiple concurrent generations
      const generationRequests = [];

      for (let i = 0; i < 3; i++) {
        const payload = {
          type: 'ecommerce',
          description: `Stress test generation ${__VU}_${i}`,
          features: ['payment', 'inventory'],
          style: 'modern',
        };

        const response = http.post(
          `${baseUrl}/api/vybe-qubes/generate`,
          JSON.stringify(payload),
          { headers, timeout: '30s' }
        );

        generationRequests.push(response);

        check(response, {
          'generation request accepted under stress': r =>
            r.status === 200 || r.status === 202,
        });

        if (response.status === 429) {
          resourceExhaustion.add(1);
        }
      }
    }
  });
}

// Mixed workload scenario
function mixedWorkloadScenario(user) {
  group('Mixed Workload Stress', () => {
    // Simulate realistic but intense user behavior

    // 1. Quick homepage check
    const homeResponse = http.get(`${baseUrl}/`);
    check(homeResponse, {
      'homepage available under stress': r => r.status === 200,
    });

    // 2. Authentication
    const authToken = attemptAuthentication(user);

    if (authToken) {
      const headers = { Authorization: `Bearer ${authToken}` };

      // 3. Dashboard access
      const dashResponse = http.get(`${baseUrl}/dashboard`, { headers });

      // 4. Course browsing
      const coursesResponse = http.get(`${baseUrl}/api/courses`, { headers });

      // 5. Quick enrollment attempt
      if (coursesResponse.status === 200) {
        const enrollResponse = http.post(
          `${baseUrl}/api/courses/1/enroll`,
          '{}',
          { headers }
        );
        check(enrollResponse, {
          'enrollment works under stress': r => r.status < 500,
        });
      }

      // 6. Vybe Qube access
      const vybeResponse = http.get(`${baseUrl}/vybe-qubes`, { headers });

      // Check overall scenario success
      const scenarioSuccess =
        homeResponse.status === 200 &&
        dashResponse.status === 200 &&
        coursesResponse.status === 200;

      if (!scenarioSuccess) {
        cascadingFailures.add(1);
      }
    }
  });
}

// Attempt authentication with error handling
function attemptAuthentication(user) {
  try {
    const loginPayload = {
      email: user.email,
      password: user.password,
    };

    const response = http.post(
      `${baseUrl}/api/auth/login`,
      JSON.stringify(loginPayload),
      {
        headers: { 'Content-Type': 'application/json' },
        timeout: '10s',
      }
    );

    const authSuccess = check(response, {
      'auth succeeds under stress': r => r.status === 200 || r.status === 201,
      'auth response time under stress': r => r.timings.duration < 8000,
    });

    if (authSuccess && response.status < 400) {
      try {
        const responseBody = JSON.parse(response.body);
        return (
          responseBody.token || responseBody.access_token || responseBody.jwt
        );
      } catch (e) {
        return null;
      }
    }

    if (response.status === 429) {
      resourceExhaustion.add(1);
    } else if (response.status >= 500) {
      errorSpike.add(1);
    }
  } catch (error) {
    console.error(`Auth error for user ${user.id}:`, error);
    cascadingFailures.add(1);
  }

  return null;
}

// Monitor system breaking points
export function setup() {
  console.log('Starting stress test - monitoring for breaking points...');

  // Initial system health check
  const healthResponse = http.get(`${baseUrl}/api/health`);

  if (healthResponse.status !== 200) {
    console.error('System not healthy before stress test!');
    return { systemHealthy: false };
  }

  return { systemHealthy: true };
}

// Analyze stress test results
export function teardown(data) {
  console.log('Stress test completed - analyzing results...');

  // Check if system recovered
  sleep(10); // Wait for system to stabilize

  const recoveryStart = Date.now();
  let recovered = false;
  let attempts = 0;

  while (!recovered && attempts < 10) {
    const healthResponse = http.get(`${baseUrl}/api/health`);

    if (healthResponse.status === 200) {
      recovered = true;
      const recoveryDuration = Date.now() - recoveryStart;
      recoveryTime.add(recoveryDuration);
      console.log(`System recovered in ${recoveryDuration}ms`);
    } else {
      sleep(3);
      attempts++;
    }
  }

  if (!recovered) {
    console.error('System did not recover after stress test!');
    breakingPointReached.add(1);
  }
}

// Custom summary for stress test
export function handleSummary(data) {
  // Calculate stress test insights
  const insights = {
    maxVUs: data.metrics.vus_max?.values?.max || 0,
    peakRPS: data.metrics.http_reqs?.values?.rate || 0,
    errorRate: data.metrics.http_req_failed?.values?.rate || 0,
    avgResponseTime: data.metrics.http_req_duration?.values?.avg || 0,
    systemStability: data.metrics.system_stability?.values?.rate || 0,
  };

  const stressReport = {
    summary: data,
    insights: insights,
    recommendations: generateStressRecommendations(insights),
  };

  return {
    'results/stress-test-summary.html': htmlReport(data),
    'results/stress-test-analysis.json': JSON.stringify(stressReport, null, 2),
    stdout: textSummary(data, { indent: ' ', enableColors: true }),
  };
}

// Generate recommendations based on stress test results
function generateStressRecommendations(insights) {
  const recommendations = [];

  if (insights.errorRate > 0.3) {
    recommendations.push(
      'High error rate detected - consider implementing circuit breakers'
    );
  }

  if (insights.avgResponseTime > 5000) {
    recommendations.push(
      'High response times - consider caching and database optimization'
    );
  }

  if (insights.systemStability < 0.5) {
    recommendations.push(
      'Low system stability - implement graceful degradation'
    );
  }

  if (insights.maxVUs < 500) {
    recommendations.push(
      'Low concurrent user capacity - consider horizontal scaling'
    );
  }

  if (insights.peakRPS < 100) {
    recommendations.push('Low throughput - optimize application performance');
  }

  return recommendations;
}
