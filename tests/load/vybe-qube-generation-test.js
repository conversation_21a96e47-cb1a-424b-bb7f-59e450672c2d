// VybeCoding.ai Vybe Qube Generation Load Test
// Tests AI-powered website generation under load

import http from 'k6/http';
import { check, sleep, group } from 'k6';
import { Rate, Trend, Counter } from 'k6/metrics';
import { htmlReport } from 'https://raw.githubusercontent.com/benc-uk/k6-reporter/main/dist/bundle.js';
import { textSummary } from 'https://jslib.k6.io/k6-summary/0.0.1/index.js';

// Custom metrics for Vybe Qube generation
const generationSuccessRate = new Rate('qube_generation_success');
const generationTime = new Trend('qube_generation_duration');
const queueWaitTime = new Trend('generation_queue_wait_time');
const aiProcessingTime = new Trend('ai_processing_time');
const generationCounter = new Counter('total_generations');
const concurrentGenerations = new Counter('concurrent_generations');
const generationErrors = new Rate('generation_errors');

// Test configuration for AI generation load
export const options = {
  scenarios: {
    // Steady generation load
    steady_generation: {
      executor: 'constant-arrival-rate',
      rate: 3, // 3 generations per second
      timeUnit: '1s',
      duration: '15m',
      preAllocatedVUs: 10,
      maxVUs: 30,
    },
    // Burst generation testing
    burst_generation: {
      executor: 'ramping-arrival-rate',
      startRate: 1,
      timeUnit: '1s',
      stages: [
        { duration: '2m', target: 5 }, // Ramp to 5/sec
        { duration: '3m', target: 10 }, // Burst to 10/sec
        { duration: '2m', target: 2 }, // Cool down
      ],
      preAllocatedVUs: 15,
      maxVUs: 50,
      startTime: '10m', // Start after steady load
    },
  },
  thresholds: {
    // AI generation specific thresholds
    qube_generation_success: ['rate>0.85'], // 85% success rate (AI can fail)
    qube_generation_duration: ['p(95)<45000'], // 95% under 45 seconds
    generation_queue_wait_time: ['p(95)<10000'], // 95% queue wait under 10s
    ai_processing_time: ['p(95)<35000'], // 95% AI processing under 35s
    generation_errors: ['rate<0.15'], // Error rate under 15%
    http_req_duration: ['p(95)<5000'], // Allow longer for AI endpoints
    http_req_failed: ['rate<0.1'], // 10% failure acceptable for AI
  },
};

// Vybe Qube generation templates and test data
const qubeTemplates = [
  {
    type: 'ecommerce',
    name: 'Online Store',
    description: 'Modern e-commerce platform for selling products online',
    features: [
      'payment_processing',
      'inventory_management',
      'customer_reviews',
      'shopping_cart',
    ],
    style: 'modern',
    complexity: 'medium',
  },
  {
    type: 'saas',
    name: 'SaaS Dashboard',
    description: 'Software as a Service platform with user management',
    features: [
      'user_authentication',
      'dashboard',
      'analytics',
      'subscription_billing',
    ],
    style: 'professional',
    complexity: 'high',
  },
  {
    type: 'portfolio',
    name: 'Creative Portfolio',
    description: 'Showcase website for creative professionals',
    features: ['gallery', 'contact_form', 'blog', 'responsive_design'],
    style: 'creative',
    complexity: 'low',
  },
  {
    type: 'blog',
    name: 'Content Blog',
    description: 'Content management system for bloggers',
    features: [
      'content_management',
      'comments',
      'social_sharing',
      'seo_optimization',
    ],
    style: 'minimal',
    complexity: 'medium',
  },
  {
    type: 'marketplace',
    name: 'Digital Marketplace',
    description: 'Platform for buying and selling digital products',
    features: [
      'multi_vendor',
      'payment_gateway',
      'rating_system',
      'search_filters',
    ],
    style: 'modern',
    complexity: 'high',
  },
];

const instructorProfiles = [
  {
    email: '<EMAIL>',
    password: 'InstructorPass123!',
    experience: 'senior',
  },
  {
    email: '<EMAIL>',
    password: 'InstructorPass123!',
    experience: 'intermediate',
  },
  {
    email: '<EMAIL>',
    password: 'InstructorPass123!',
    experience: 'expert',
  },
  {
    email: '<EMAIL>',
    password: 'InstructorPass123!',
    experience: 'senior',
  },
  {
    email: '<EMAIL>',
    password: 'InstructorPass123!',
    experience: 'intermediate',
  },
];

const baseUrl = __ENV.BASE_URL || 'http://localhost:3000';

// Main Vybe Qube generation test
export default function () {
  const instructor = instructorProfiles[__VU % instructorProfiles.length];
  const template =
    qubeTemplates[Math.floor(Math.random() * qubeTemplates.length)];

  group('Vybe Qube Generation Session', () => {
    const authToken = authenticateInstructor(instructor);

    if (authToken) {
      generateVybeQube(authToken, template, instructor);
    }
  });

  // Simulate time between generation requests
  sleep(Math.random() * 10 + 5);
}

// Authenticate instructor
function authenticateInstructor(instructor) {
  const loginPayload = {
    email: instructor.email,
    password: instructor.password,
  };

  const response = http.post(
    `${baseUrl}/api/auth/login`,
    JSON.stringify(loginPayload),
    {
      headers: { 'Content-Type': 'application/json' },
    }
  );

  const authSuccess = check(response, {
    'instructor login successful': r => r.status === 200 || r.status === 201,
    'instructor login time OK': r => r.timings.duration < 2000,
  });

  if (authSuccess && response.status < 400) {
    try {
      const responseBody = JSON.parse(response.body);
      return (
        responseBody.token || responseBody.access_token || responseBody.jwt
      );
    } catch (e) {
      return null;
    }
  }

  return null;
}

// Generate Vybe Qube with comprehensive testing
function generateVybeQube(token, template, instructor) {
  const headers = {
    Authorization: `Bearer ${token}`,
    'Content-Type': 'application/json',
  };

  // Customize template based on instructor experience
  const customizedTemplate = customizeTemplate(template, instructor);

  group('Qube Generation Request', () => {
    initiateGeneration(headers, customizedTemplate);
  });
}

// Customize template based on instructor profile
function customizeTemplate(template, instructor) {
  const customized = { ...template };

  // Add complexity based on instructor experience
  if (instructor.experience === 'expert') {
    customized.features.push('advanced_analytics', 'api_integration');
    customized.complexity = 'high';
  } else if (instructor.experience === 'intermediate') {
    customized.features.push('basic_analytics');
  }

  // Add unique elements for load testing
  customized.uniqueId = `test_${__VU}_${Date.now()}`;
  customized.testMode = true;

  return customized;
}

// Initiate Vybe Qube generation
function initiateGeneration(headers, template) {
  const generationStart = Date.now();

  // Start generation request
  const generationPayload = {
    template: template.type,
    name: `${template.name} - Load Test ${__VU}`,
    description: template.description,
    features: template.features,
    style: template.style,
    complexity: template.complexity,
    customizations: {
      colorScheme: ['blue', 'green', 'purple', 'orange'][
        Math.floor(Math.random() * 4)
      ],
      layout: ['grid', 'list', 'masonry'][Math.floor(Math.random() * 3)],
      animations: Math.random() > 0.5,
    },
  };

  const response = http.post(
    `${baseUrl}/api/vybe-qubes/generate`,
    JSON.stringify(generationPayload),
    {
      headers,
      timeout: '60s', // Allow up to 60 seconds for initial response
    }
  );

  generationCounter.add(1);

  const requestSuccess = check(response, {
    'generation request accepted': r => r.status === 200 || r.status === 202,
    'generation request time OK': r => r.timings.duration < 5000,
    'generation response valid': r => {
      try {
        const body = JSON.parse(r.body);
        return body && (body.jobId || body.generationId || body.id);
      } catch (e) {
        return false;
      }
    },
  });

  if (!requestSuccess) {
    generationErrors.add(1);
    generationSuccessRate.add(false);
    return;
  }

  // Handle different response types
  if (response.status === 200) {
    // Synchronous generation (immediate response)
    const generationEnd = Date.now();
    const totalTime = generationEnd - generationStart;

    generationTime.add(totalTime);
    aiProcessingTime.add(totalTime);
    generationSuccessRate.add(true);

    validateGeneratedQube(response);
  } else if (response.status === 202) {
    // Asynchronous generation (polling required)
    try {
      const responseBody = JSON.parse(response.body);
      const jobId =
        responseBody.jobId || responseBody.generationId || responseBody.id;

      if (jobId) {
        pollGenerationStatus(headers, jobId, generationStart);
      } else {
        generationErrors.add(1);
        generationSuccessRate.add(false);
      }
    } catch (e) {
      generationErrors.add(1);
      generationSuccessRate.add(false);
    }
  }
}

// Poll generation status for async operations
function pollGenerationStatus(headers, jobId, generationStart) {
  let completed = false;
  let attempts = 0;
  const maxAttempts = 20; // Maximum 60 seconds of polling (3s intervals)
  const queueStart = Date.now();

  while (!completed && attempts < maxAttempts) {
    sleep(3); // Poll every 3 seconds
    attempts++;

    const statusResponse = http.get(
      `${baseUrl}/api/vybe-qubes/status/${jobId}`,
      { headers }
    );

    const statusCheck = check(statusResponse, {
      'status check successful': r => r.status === 200,
      'status response time OK': r => r.timings.duration < 2000,
    });

    if (statusCheck && statusResponse.status === 200) {
      try {
        const status = JSON.parse(statusResponse.body);

        if (status.status === 'completed') {
          completed = true;
          const generationEnd = Date.now();
          const totalTime = generationEnd - generationStart;
          const queueTime = generationEnd - queueStart;

          generationTime.add(totalTime);
          queueWaitTime.add(queueTime);
          aiProcessingTime.add(totalTime - queueTime);
          generationSuccessRate.add(true);

          // Validate the completed generation
          validateGeneratedQube(statusResponse);
        } else if (status.status === 'failed' || status.status === 'error') {
          completed = true;
          generationErrors.add(1);
          generationSuccessRate.add(false);

          console.error(
            `Generation failed: ${status.error || 'Unknown error'}`
          );
        } else if (
          status.status === 'processing' ||
          status.status === 'queued'
        ) {
          // Continue polling
          check(status, {
            'generation progress available': () =>
              typeof status.progress !== 'undefined',
          });
        }
      } catch (e) {
        console.error('Failed to parse status response:', e);
      }
    } else {
      // Status check failed
      if (attempts >= 3) {
        // Allow a few status check failures
        completed = true;
        generationErrors.add(1);
        generationSuccessRate.add(false);
      }
    }
  }

  // Timeout handling
  if (!completed) {
    generationErrors.add(1);
    generationSuccessRate.add(false);
    console.error(`Generation timeout after ${maxAttempts} attempts`);
  }
}

// Validate generated Vybe Qube
function validateGeneratedQube(response) {
  try {
    const result = JSON.parse(response.body);

    const validation = check(result, {
      'qube has valid structure': r => r && (r.qube || r.website || r.result),
      'qube has URL': r => {
        const qube = r.qube || r.website || r.result;
        return qube && (qube.url || qube.previewUrl || qube.liveUrl);
      },
      'qube has source code': r => {
        const qube = r.qube || r.website || r.result;
        return qube && (qube.sourceCode || qube.code || qube.files);
      },
      'qube has metadata': r => {
        const qube = r.qube || r.website || r.result;
        return qube && qube.metadata && qube.metadata.title;
      },
    });

    if (validation) {
      // Test accessing the generated website
      const qube = result.qube || result.website || result.result;
      if (qube && qube.url) {
        group('Generated Qube Validation', () => {
          testGeneratedWebsite(qube.url);
        });
      }
    }
  } catch (e) {
    console.error('Failed to validate generated qube:', e);
    check(false, { 'qube validation failed': () => false });
  }
}

// Test the generated website
function testGeneratedWebsite(url) {
  // Test if the generated website is accessible
  const websiteResponse = http.get(url, { timeout: '10s' });

  check(websiteResponse, {
    'generated website accessible': r => r.status === 200,
    'generated website loads quickly': r => r.timings.duration < 5000,
    'generated website has content': r => r.body && r.body.length > 100,
    'generated website is HTML': r =>
      r.headers['Content-Type'] &&
      r.headers['Content-Type'].includes('text/html'),
  });
}

// Custom summary report for Vybe Qube generation
export function handleSummary(data) {
  return {
    'results/vybe-qube-generation-summary.html': htmlReport(data),
    'results/vybe-qube-generation-summary.json': JSON.stringify(data, null, 2),
    stdout: textSummary(data, { indent: ' ', enableColors: true }),
  };
}
