// VybeCoding.ai Load Test - User Registration Journey
// K6 load testing script for user registration and onboarding flow

import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate, Trend } from 'k6/metrics';

// Custom metrics
export const errorRate = new Rate('errors');
export const registrationDuration = new Trend('registration_duration');

// Test configuration
export const options = {
  stages: [
    { duration: '2m', target: 10 }, // Ramp up to 10 users
    { duration: '5m', target: 50 }, // Stay at 50 users
    { duration: '2m', target: 100 }, // Ramp up to 100 users
    { duration: '5m', target: 100 }, // Stay at 100 users
    { duration: '2m', target: 0 }, // Ramp down
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95% of requests under 500ms
    http_req_failed: ['rate<0.01'], // Error rate under 1%
    errors: ['rate<0.01'],
    registration_duration: ['p(95)<2000'], // Registration under 2s
  },
};

const BASE_URL = __ENV.BASE_URL || 'http://localhost:3000';

// Generate unique test data
function generateTestUser() {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(7);

  return {
    email: `test.user.${timestamp}.${random}@vybecoding.test`,
    password: 'TestPassword123!',
    firstName: `Test${random}`,
    lastName: `User${timestamp}`,
    username: `testuser${timestamp}${random}`,
  };
}

// Main test function
export default function () {
  const user = generateTestUser();

  // Test user registration flow
  testUserRegistration(user);

  // Brief pause between iterations
  sleep(1);
}

function testUserRegistration(user) {
  const registrationStart = Date.now();

  // Step 1: Load registration page
  const registrationPageResponse = http.get(`${BASE_URL}/register`);

  check(registrationPageResponse, {
    'registration page loads': r => r.status === 200,
    'registration page has form': r =>
      r.body.includes('register') || r.body.includes('sign-up'),
  }) || errorRate.add(1);

  sleep(0.5); // Simulate user reading the form

  // Step 2: Submit registration
  const registrationPayload = {
    email: user.email,
    password: user.password,
    firstName: user.firstName,
    lastName: user.lastName,
    username: user.username,
    acceptTerms: true,
  };

  const registrationResponse = http.post(
    `${BASE_URL}/api/auth/register`,
    JSON.stringify(registrationPayload),
    {
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );

  const registrationSuccess = check(registrationResponse, {
    'registration successful': r => r.status === 201 || r.status === 200,
    'registration returns user data': r => {
      try {
        const body = JSON.parse(r.body);
        return body.user || body.id || body.success;
      } catch (e) {
        return false;
      }
    },
  });

  if (!registrationSuccess) {
    errorRate.add(1);
    console.error(
      `Registration failed for ${user.email}: ${registrationResponse.status} ${registrationResponse.body}`
    );
    return;
  }

  sleep(1); // Simulate user processing registration success

  // Step 3: Attempt login with new credentials
  const loginPayload = {
    email: user.email,
    password: user.password,
  };

  const loginResponse = http.post(
    `${BASE_URL}/api/auth/login`,
    JSON.stringify(loginPayload),
    {
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );

  const loginSuccess = check(loginResponse, {
    'login successful': r => r.status === 200,
    'login returns token': r => {
      try {
        const body = JSON.parse(r.body);
        return body.token || body.accessToken || body.jwt;
      } catch (e) {
        return false;
      }
    },
  });

  if (!loginSuccess) {
    errorRate.add(1);
    console.error(
      `Login failed for ${user.email}: ${loginResponse.status} ${loginResponse.body}`
    );
    return;
  }

  // Extract authentication token
  let authToken;
  try {
    const loginBody = JSON.parse(loginResponse.body);
    authToken = loginBody.token || loginBody.accessToken || loginBody.jwt;
  } catch (e) {
    console.error('Failed to parse login response');
    errorRate.add(1);
    return;
  }

  sleep(0.5);

  // Step 4: Access protected profile endpoint
  const profileResponse = http.get(`${BASE_URL}/api/user/profile`, {
    headers: {
      Authorization: `Bearer ${authToken}`,
    },
  });

  check(profileResponse, {
    'profile access successful': r => r.status === 200,
    'profile contains user data': r => {
      try {
        const body = JSON.parse(r.body);
        return body.email === user.email;
      } catch (e) {
        return false;
      }
    },
  }) || errorRate.add(1);

  sleep(0.5);

  // Step 5: Update user profile
  const profileUpdatePayload = {
    bio: `Test user bio for ${user.firstName}`,
    preferences: {
      theme: 'dark',
      notifications: true,
    },
  };

  const profileUpdateResponse = http.put(
    `${BASE_URL}/api/user/profile`,
    JSON.stringify(profileUpdatePayload),
    {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${authToken}`,
      },
    }
  );

  check(profileUpdateResponse, {
    'profile update successful': r => r.status === 200,
    'profile update confirmed': r => {
      try {
        const body = JSON.parse(r.body);
        return body.bio === profileUpdatePayload.bio;
      } catch (e) {
        return false;
      }
    },
  }) || errorRate.add(1);

  // Record total registration flow duration
  const registrationEnd = Date.now();
  registrationDuration.add(registrationEnd - registrationStart);

  sleep(1); // Final pause
}

// Setup function (runs once per VU)
export function setup() {
  console.log('Starting user registration load test');
  console.log(`Target URL: ${BASE_URL}`);

  // Verify the application is accessible
  const healthCheck = http.get(`${BASE_URL}/api/health`);
  if (healthCheck.status !== 200) {
    throw new Error(`Application not accessible: ${healthCheck.status}`);
  }

  return { baseUrl: BASE_URL };
}

// Teardown function (runs once after all VUs finish)
export function teardown(data) {
  console.log('User registration load test completed');
  console.log(`Base URL: ${data.baseUrl}`);
}
