/**
 * Deployment Infrastructure Tests
 * Comprehensive tests for Vybe Qube deployment system
 * STORY-3-001: Vybe Qube Deployment Infrastructure
 */

import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest';
import { vybeQubeDeployment } from '$lib/services/vybeQubeDeployment';
import type {
  DeploymentRequest,
  DeploymentStatus,
} from '$lib/services/vybeQubeDeployment';

// Mock deployment service for testing
const mockDeploymentService = {
  deployments: new Map<string, any>(),
  nextDeploymentId: 1,

  async deploy(request: DeploymentRequest) {
    const deploymentId = `deploy_${Date.now()}_${this.nextDeploymentId++}`;
    const subdomain =
      request.subdomain ||
      vybeQubeDeployment.generateSubdomain(
        request.business_idea,
        request.qube_id
      );

    const deployment = {
      deployment_id: deploymentId,
      qube_id: request.qube_id,
      subdomain,
      status: 'initializing',
      progress: 0,
      current_phase: 'Initializing deployment',
      url: `https://${subdomain}.vybequbes.com`,
      logs: [`Deployment ${deploymentId} initialized`],
      created_at: new Date().toISOString(),
      request,
    };

    this.deployments.set(deploymentId, deployment);

    // Simulate deployment progress
    setTimeout(() => this.simulateProgress(deploymentId), 100);

    return {
      deployment_id: deploymentId,
      qube_id: request.qube_id,
      subdomain,
      status: 'initializing',
      url: deployment.url,
      estimated_completion: new Date(Date.now() + 10 * 60 * 1000).toISOString(),
    };
  },

  async simulateProgress(deploymentId: string) {
    const deployment = this.deployments.get(deploymentId);
    if (!deployment) return;

    const phases = [
      { phase: 'Preparing container', progress: 10 },
      { phase: 'Building Docker image', progress: 25 },
      { phase: 'Creating DNS records', progress: 45 },
      { phase: 'Provisioning SSL certificate', progress: 65 },
      { phase: 'Deploying container', progress: 80 },
      { phase: 'Health check verification', progress: 95 },
      { phase: 'Deployment completed successfully', progress: 100 },
    ];

    for (const { phase, progress } of phases) {
      await new Promise(resolve => setTimeout(resolve, 50));

      deployment.current_phase = phase;
      deployment.progress = progress;
      deployment.status = progress === 100 ? 'deployed' : 'deploying';
      deployment.logs.push(`📋 ${phase} (${progress}%)`);

      if (progress === 100) {
        deployment.completed_at = new Date().toISOString();
      }
    }
  },

  async getStatus(deploymentId: string) {
    const deployment = this.deployments.get(deploymentId);
    if (!deployment) {
      throw new Error('Deployment not found');
    }
    return deployment;
  },

  async listDeployments() {
    return Array.from(this.deployments.values());
  },

  async deleteDeployment(deploymentId: string) {
    if (!this.deployments.has(deploymentId)) {
      throw new Error('Deployment not found');
    }
    this.deployments.delete(deploymentId);
    return { message: 'Deployment deleted successfully' };
  },
};

// Mock fetch for testing
global.fetch = async (url: string, options?: any) => {
  const urlObj = new URL(url);
  const path = urlObj.pathname;

  if (path === '/deploy' && options?.method === 'POST') {
    const request = JSON.parse(options.body);
    return {
      ok: true,
      json: async () => await mockDeploymentService.deploy(request),
    } as Response;
  }

  if (path.startsWith('/status/')) {
    const deploymentId = path.split('/').pop();
    try {
      const status = await mockDeploymentService.getStatus(deploymentId!);
      return {
        ok: true,
        json: async () => status,
      } as Response;
    } catch (error) {
      return {
        ok: false,
        status: 404,
        json: async () => ({ detail: 'Deployment not found' }),
      } as Response;
    }
  }

  if (path === '/deployments') {
    if (options?.method === 'DELETE') {
      return {
        ok: false,
        status: 405,
        json: async () => ({ detail: 'Method not allowed' }),
      } as Response;
    }

    const deployments = await mockDeploymentService.listDeployments();
    return {
      ok: true,
      json: async () => ({
        deployments,
        total: deployments.length,
        active: deployments.filter(
          d => d.status === 'deploying' || d.status === 'initializing'
        ).length,
      }),
    } as Response;
  }

  if (path.startsWith('/deployments/') && options?.method === 'DELETE') {
    const deploymentId = path.split('/').pop();
    try {
      const result = await mockDeploymentService.deleteDeployment(
        deploymentId!
      );
      return {
        ok: true,
        json: async () => result,
      } as Response;
    } catch (error) {
      return {
        ok: false,
        status: 404,
        json: async () => ({ detail: 'Deployment not found' }),
      } as Response;
    }
  }

  return {
    ok: false,
    status: 404,
    json: async () => ({ detail: 'Not found' }),
  } as Response;
};

describe('Vybe Qube Deployment Infrastructure', () => {
  beforeEach(() => {
    mockDeploymentService.deployments.clear();
    mockDeploymentService.nextDeploymentId = 1;
  });

  describe('Deployment Request Validation', () => {
    it('should validate required fields', () => {
      const invalidRequests = [
        {},
        { qube_id: '' },
        { qube_id: 'test', generated_files: null },
        { qube_id: 'test', generated_files: {}, business_idea: '' },
        { qube_id: 'test', generated_files: {}, business_idea: 'short' },
      ];

      invalidRequests.forEach(request => {
        const errors = vybeQubeDeployment.validateDeploymentRequest(
          request as any
        );
        expect(errors.length).toBeGreaterThan(0);
      });
    });

    it('should validate subdomain format', () => {
      const request: DeploymentRequest = {
        qube_id: 'test-qube',
        generated_files: { 'index.html': '<html></html>' },
        business_idea: 'Test business idea for validation',
      };

      const invalidSubdomains = [
        'UPPERCASE',
        'with_underscore',
        'with.dot',
        'with space',
        'ab', // too short
        'a'.repeat(64), // too long
        '-starts-with-dash',
        'ends-with-dash-',
      ];

      invalidSubdomains.forEach(subdomain => {
        const errors = vybeQubeDeployment.validateDeploymentRequest({
          ...request,
          subdomain,
        });
        expect(errors.some(error => error.includes('subdomain'))).toBe(true);
      });
    });

    it('should accept valid deployment requests', () => {
      const validRequest: DeploymentRequest = {
        qube_id: 'test-qube-123',
        generated_files: {
          'index.html': '<html><body>Test</body></html>',
          'style.css': 'body { margin: 0; }',
        },
        business_idea: 'AI-powered fitness coaching platform',
        subdomain: 'fitness-coach-ai',
      };

      const errors = vybeQubeDeployment.validateDeploymentRequest(validRequest);
      expect(errors).toHaveLength(0);
    });
  });

  describe('Subdomain Generation', () => {
    it('should generate valid subdomains from business ideas', () => {
      const testCases = [
        {
          businessIdea: 'AI-Powered Fitness Coaching',
          qubeId: 'qube-123-456',
          expected: /^ai-powered-fitness-456$/,
        },
        {
          businessIdea: 'E-commerce Store for Handmade Crafts!!!',
          qubeId: 'qube-789-abc',
          expected: /^e-commerce-store-for-abc$/,
        },
        {
          businessIdea: 'Very Long Business Idea That Should Be Truncated',
          qubeId: 'qube-def-ghi',
          expected: /^very-long-business-ghi$/,
        },
      ];

      testCases.forEach(({ businessIdea, qubeId, expected }) => {
        const subdomain = vybeQubeDeployment.generateSubdomain(
          businessIdea,
          qubeId
        );
        expect(subdomain).toMatch(expected);
        expect(subdomain.length).toBeLessThanOrEqual(63);
        expect(subdomain.length).toBeGreaterThanOrEqual(3);
      });
    });
  });

  describe('Deployment Process', () => {
    it('should initiate deployment successfully', async () => {
      const request: DeploymentRequest = {
        qube_id: 'test-qube-001',
        generated_files: {
          'index.html': '<html><body><h1>Test Site</h1></body></html>',
          'style.css': 'body { font-family: Arial; }',
        },
        business_idea: 'Test business for deployment',
        subdomain: 'test-deployment',
      };

      const response = await vybeQubeDeployment.deployVybeQube(request);

      expect(response.deployment_id).toBeDefined();
      expect(response.qube_id).toBe(request.qube_id);
      expect(response.subdomain).toBe('test-deployment');
      expect(response.status).toBe('initializing');
      expect(response.url).toBe('https://test-deployment.vybequbes.com');
    });

    it('should track deployment progress', async () => {
      const request: DeploymentRequest = {
        qube_id: 'test-qube-002',
        generated_files: { 'index.html': '<html></html>' },
        business_idea: 'Progress tracking test business',
      };

      const response = await vybeQubeDeployment.deployVybeQube(request);

      // Wait for initial status
      await new Promise(resolve => setTimeout(resolve, 50));

      const status = await vybeQubeDeployment.getDeploymentStatus(
        response.deployment_id
      );

      expect(status.deployment_id).toBe(response.deployment_id);
      expect(status.progress).toBeGreaterThanOrEqual(0);
      expect(status.logs.length).toBeGreaterThan(0);

      // Wait for completion
      await new Promise(resolve => setTimeout(resolve, 500));

      const finalStatus = await vybeQubeDeployment.getDeploymentStatus(
        response.deployment_id
      );
      expect(finalStatus.status).toBe('deployed');
      expect(finalStatus.progress).toBe(100);
    });

    it('should list deployments', async () => {
      // Create multiple deployments
      const requests = [
        {
          qube_id: 'qube-001',
          generated_files: { 'index.html': '<html></html>' },
          business_idea: 'First test business',
        },
        {
          qube_id: 'qube-002',
          generated_files: { 'index.html': '<html></html>' },
          business_idea: 'Second test business',
        },
      ];

      for (const request of requests) {
        await vybeQubeDeployment.deployVybeQube(request);
      }

      const deployments = await vybeQubeDeployment.listDeployments();

      expect(deployments).toHaveLength(2);
      expect(deployments.map(d => d.qube_id)).toContain('qube-001');
      expect(deployments.map(d => d.qube_id)).toContain('qube-002');
    });

    it('should delete deployments', async () => {
      const request: DeploymentRequest = {
        qube_id: 'test-qube-delete',
        generated_files: { 'index.html': '<html></html>' },
        business_idea: 'Deletion test business',
      };

      const response = await vybeQubeDeployment.deployVybeQube(request);

      // Verify deployment exists
      const status = await vybeQubeDeployment.getDeploymentStatus(
        response.deployment_id
      );
      expect(status.deployment_id).toBe(response.deployment_id);

      // Delete deployment
      await vybeQubeDeployment.deleteDeployment(response.deployment_id);

      // Verify deployment is deleted
      await expect(
        vybeQubeDeployment.getDeploymentStatus(response.deployment_id)
      ).rejects.toThrow('Deployment not found');
    });
  });

  describe('Deployment Metrics', () => {
    it('should calculate deployment metrics correctly', async () => {
      // Create deployments with different statuses
      const requests = [
        {
          qube_id: 'qube-metrics-1',
          generated_files: { 'index.html': '<html></html>' },
          business_idea: 'Metrics test 1',
        },
        {
          qube_id: 'qube-metrics-2',
          generated_files: { 'index.html': '<html></html>' },
          business_idea: 'Metrics test 2',
        },
        {
          qube_id: 'qube-metrics-3',
          generated_files: { 'index.html': '<html></html>' },
          business_idea: 'Metrics test 3',
        },
      ];

      for (const request of requests) {
        await vybeQubeDeployment.deployVybeQube(request);
      }

      // Wait for deployments to complete
      await new Promise(resolve => setTimeout(resolve, 600));

      const metrics = await vybeQubeDeployment.getDeploymentMetrics();

      expect(metrics.total_deployments).toBe(3);
      expect(metrics.success_rate).toBeGreaterThan(0);
      expect(metrics.average_deployment_time).toBeGreaterThan(0);
    });
  });

  describe('Error Handling', () => {
    it('should handle deployment service errors gracefully', async () => {
      // Mock a service error
      const originalFetch = global.fetch;
      global.fetch = async () =>
        ({
          ok: false,
          status: 500,
          json: async () => ({ detail: 'Service unavailable' }),
        }) as Response;

      const request: DeploymentRequest = {
        qube_id: 'error-test',
        generated_files: { 'index.html': '<html></html>' },
        business_idea: 'Error handling test',
      };

      await expect(
        vybeQubeDeployment.deployVybeQube(request)
      ).rejects.toThrow();

      // Restore original fetch
      global.fetch = originalFetch;
    });

    it('should handle non-existent deployment status requests', async () => {
      await expect(
        vybeQubeDeployment.getDeploymentStatus('non-existent-deployment')
      ).rejects.toThrow();
    });
  });
});
