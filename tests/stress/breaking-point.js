// VybeCoding.ai Stress Test - Breaking Point Analysis
// K6 stress testing script to identify system limits and failure modes

import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate, Trend, Counter, Gauge } from 'k6/metrics';

// Custom metrics
export const errorRate = new Rate('errors');
export const responseTime = new Trend('response_time');
export const activeUsers = new Gauge('active_users');
export const failedRequests = new Counter('failed_requests');
export const successfulRequests = new Counter('successful_requests');

// Stress test configuration - aggressive load ramping
export const options = {
  stages: [
    { duration: '2m', target: 50 }, // Warm up
    { duration: '3m', target: 200 }, // Normal load
    { duration: '3m', target: 500 }, // High load
    { duration: '5m', target: 1000 }, // Stress load
    { duration: '5m', target: 1500 }, // Breaking point
    { duration: '5m', target: 2000 }, // Beyond breaking point
    { duration: '3m', target: 2500 }, // Maximum stress
    { duration: '5m', target: 0 }, // Recovery
  ],
  thresholds: {
    // More lenient thresholds for stress testing
    http_req_duration: ['p(95)<5000'], // 95% under 5s (degraded performance expected)
    http_req_failed: ['rate<0.5'], // Allow up to 50% failure rate
    errors: ['rate<0.5'],
  },
};

const BASE_URL = __ENV.BASE_URL || 'http://localhost:3000';

// Test scenarios with different weights
const SCENARIOS = [
  { name: 'health_check', weight: 20, endpoint: '/api/health' },
  { name: 'course_list', weight: 30, endpoint: '/api/courses' },
  { name: 'user_profile', weight: 25, endpoint: '/api/user/profile' },
  { name: 'metrics', weight: 15, endpoint: '/api/metrics' },
  { name: 'security_status', weight: 10, endpoint: '/api/security' },
];

// Authentication tokens for test users
let authTokens = [];

// Main test function
export default function () {
  activeUsers.add(1);

  // Select random scenario based on weights
  const scenario = selectScenario();

  // Execute the selected scenario
  executeScenario(scenario);

  // Brief pause to simulate user think time
  sleep(Math.random() * 2 + 0.5); // 0.5-2.5 seconds

  activeUsers.add(-1);
}

function selectScenario() {
  const totalWeight = SCENARIOS.reduce((sum, s) => sum + s.weight, 0);
  let random = Math.random() * totalWeight;

  for (const scenario of SCENARIOS) {
    random -= scenario.weight;
    if (random <= 0) {
      return scenario;
    }
  }

  return SCENARIOS[0]; // Fallback
}

function executeScenario(scenario) {
  const startTime = Date.now();
  let response;

  switch (scenario.name) {
    case 'health_check':
      response = testHealthCheck();
      break;
    case 'course_list':
      response = testCourseList();
      break;
    case 'user_profile':
      response = testUserProfile();
      break;
    case 'metrics':
      response = testMetrics();
      break;
    case 'security_status':
      response = testSecurityStatus();
      break;
    default:
      response = testHealthCheck();
  }

  // Record metrics
  const duration = Date.now() - startTime;
  responseTime.add(duration);

  if (response && response.status >= 200 && response.status < 400) {
    successfulRequests.add(1);
  } else {
    failedRequests.add(1);
    errorRate.add(1);
  }
}

function testHealthCheck() {
  const response = http.get(`${BASE_URL}/api/health`, {
    timeout: '10s',
  });

  check(response, {
    'health check responds': r => r.status !== 0,
    'health check under 10s': r => r.timings.duration < 10000,
  });

  return response;
}

function testCourseList() {
  const response = http.get(`${BASE_URL}/api/courses`, {
    timeout: '15s',
  });

  check(response, {
    'courses endpoint responds': r => r.status !== 0,
    'courses response under 15s': r => r.timings.duration < 15000,
  });

  return response;
}

function testUserProfile() {
  // Use a random auth token if available
  const authToken = authTokens[Math.floor(Math.random() * authTokens.length)];
  const headers = authToken ? { Authorization: `Bearer ${authToken}` } : {};

  const response = http.get(`${BASE_URL}/api/user/profile`, {
    headers,
    timeout: '15s',
  });

  check(response, {
    'profile endpoint responds': r => r.status !== 0,
    'profile response under 15s': r => r.timings.duration < 15000,
  });

  return response;
}

function testMetrics() {
  const response = http.get(`${BASE_URL}/api/metrics`, {
    timeout: '20s',
  });

  check(response, {
    'metrics endpoint responds': r => r.status !== 0,
    'metrics response under 20s': r => r.timings.duration < 20000,
  });

  return response;
}

function testSecurityStatus() {
  const response = http.get(`${BASE_URL}/api/security`, {
    timeout: '25s',
  });

  check(response, {
    'security endpoint responds': r => r.status !== 0,
    'security response under 25s': r => r.timings.duration < 25000,
  });

  return response;
}

// Database stress test
function testDatabaseStress() {
  // Simulate heavy database operations
  const queries = [
    '/api/courses?limit=100',
    '/api/users?search=test',
    '/api/analytics/dashboard',
    '/api/progress/summary',
  ];

  const query = queries[Math.floor(Math.random() * queries.length)];
  const response = http.get(`${BASE_URL}${query}`, {
    timeout: '30s',
  });

  check(response, {
    'database query responds': r => r.status !== 0,
    'database query under 30s': r => r.timings.duration < 30000,
  });

  return response;
}

// Memory stress test
function testMemoryStress() {
  // Request large data sets to stress memory
  const response = http.get(`${BASE_URL}/api/export/full-data`, {
    timeout: '60s',
  });

  check(response, {
    'memory stress responds': r => r.status !== 0,
    'memory stress under 60s': r => r.timings.duration < 60000,
  });

  return response;
}

// CPU stress test
function testCPUStress() {
  // Trigger CPU-intensive operations
  const payload = {
    operation: 'complex_calculation',
    iterations: 10000,
    data: Array.from({ length: 1000 }, (_, i) => i),
  };

  const response = http.post(
    `${BASE_URL}/api/compute/stress`,
    JSON.stringify(payload),
    {
      headers: { 'Content-Type': 'application/json' },
      timeout: '45s',
    }
  );

  check(response, {
    'CPU stress responds': r => r.status !== 0,
    'CPU stress under 45s': r => r.timings.duration < 45000,
  });

  return response;
}

// Setup function
export function setup() {
  console.log('Starting breaking point stress test');
  console.log(`Target URL: ${BASE_URL}`);
  console.log('WARNING: This test will push the system to its limits');

  // Verify application is accessible
  const healthCheck = http.get(`${BASE_URL}/api/health`);
  if (healthCheck.status !== 200) {
    console.warn(`Application health check failed: ${healthCheck.status}`);
  }

  // Pre-authenticate some test users for authenticated endpoints
  const testUsers = [
    { email: '<EMAIL>', password: 'TestPassword123!' },
    { email: '<EMAIL>', password: 'TestPassword123!' },
    { email: '<EMAIL>', password: 'TestPassword123!' },
  ];

  for (const user of testUsers) {
    try {
      const loginResponse = http.post(
        `${BASE_URL}/api/auth/login`,
        JSON.stringify(user),
        { headers: { 'Content-Type': 'application/json' } }
      );

      if (loginResponse.status === 200) {
        const body = JSON.parse(loginResponse.body);
        const token = body.token || body.accessToken || body.jwt;
        if (token) {
          authTokens.push(token);
        }
      }
    } catch (e) {
      console.warn(`Failed to authenticate test user ${user.email}`);
    }
  }

  console.log(`Authenticated ${authTokens.length} test users`);

  return {
    baseUrl: BASE_URL,
    authTokens: authTokens.length,
    startTime: new Date().toISOString(),
  };
}

// Teardown function
export function teardown(data) {
  console.log('Breaking point stress test completed');
  console.log(
    `Test duration: ${new Date().toISOString()} (started: ${data.startTime})`
  );
  console.log(`Successful requests: ${successfulRequests.count}`);
  console.log(`Failed requests: ${failedRequests.count}`);

  const totalRequests = successfulRequests.count + failedRequests.count;
  const failureRate =
    totalRequests > 0
      ? ((failedRequests.count / totalRequests) * 100).toFixed(2)
      : 0;

  console.log(`Overall failure rate: ${failureRate}%`);

  // Determine breaking point analysis
  if (failureRate < 5) {
    console.log(
      '✅ System handled stress test well - no breaking point reached'
    );
  } else if (failureRate < 25) {
    console.log(
      '⚠️ System showed degradation under stress - monitor for optimization opportunities'
    );
  } else if (failureRate < 50) {
    console.log(
      '🔥 System reached breaking point - significant performance degradation detected'
    );
  } else {
    console.log(
      '💥 System failure under stress - immediate optimization required'
    );
  }

  // Wait for system recovery
  console.log('Waiting for system recovery...');
  sleep(30);

  const recoveryCheck = http.get(`${data.baseUrl}/api/health`);
  if (recoveryCheck.status === 200) {
    console.log('✅ System recovered successfully');
  } else {
    console.log('❌ System may need manual intervention to recover');
  }
}
