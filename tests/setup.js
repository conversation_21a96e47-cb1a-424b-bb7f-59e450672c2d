import '@testing-library/jest-dom';
import { cleanup } from '@testing-library/svelte';
import { afterEach, beforeEach, vi, expect } from 'vitest';
import './utils';
import './mocks/monaco-editor';
import './mocks/pyodide';
import './mocks/appwrite';

// Clean up after each test
afterEach(() => {
  cleanup();
  vi.clearAllMocks();
  vi.clearAllTimers();
  // Reset DOM
  document.body.innerHTML = '';
  document.head.innerHTML = '';
});

beforeEach(() => {
  // Reset any global state
  vi.useFakeTimers();
});

// Extend expect with custom matchers for Svelte testing
expect.extend({
  toBeInTheDocument(received) {
    const pass = received && document.body.contains(received);
    return {
      message: () =>
        `expected element ${pass ? 'not ' : ''}to be in the document`,
      pass,
    };
  },
  toHaveClass(received, className) {
    const pass = received && received.classList.contains(className);
    return {
      message: () =>
        `expected element ${pass ? 'not ' : ''}to have class "${className}"`,
      pass,
    };
  },
  toBeVisible(received) {
    const pass = received && received.offsetParent !== null;
    return {
      message: () => `expected element ${pass ? 'not ' : ''}to be visible`,
      pass,
    };
  },
});

// Enhanced window.matchMedia mock with media query support
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => {
    const mediaQuery = {
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(),
      removeListener: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    };

    // Handle common media queries
    if (query.includes('prefers-color-scheme: dark')) {
      mediaQuery.matches = false; // Default to light mode in tests
    }
    if (query.includes('max-width: 768px')) {
      mediaQuery.matches = false; // Default to desktop in tests
    }

    return mediaQuery;
  }),
});

// Enhanced ResizeObserver with callback support
global.ResizeObserver = class ResizeObserver {
  constructor(callback) {
    this.callback = callback;
    this.observations = new Map();
  }

  observe(target, options = {}) {
    this.observations.set(target, options);
    // Simulate initial observation
    if (this.callback) {
      this.callback(
        [
          {
            target,
            contentRect: {
              width: 1024,
              height: 768,
              top: 0,
              left: 0,
              bottom: 768,
              right: 1024,
            },
            borderBoxSize: [{ inlineSize: 1024, blockSize: 768 }],
            contentBoxSize: [{ inlineSize: 1024, blockSize: 768 }],
            devicePixelContentBoxSize: [{ inlineSize: 1024, blockSize: 768 }],
          },
        ],
        this
      );
    }
  }

  unobserve(target) {
    this.observations.delete(target);
  }

  disconnect() {
    this.observations.clear();
  }
};

// Enhanced IntersectionObserver with callback support
global.IntersectionObserver = class IntersectionObserver {
  constructor(callback, options = {}) {
    this.callback = callback;
    this.options = options;
    this.observations = new Set();
  }

  observe(target) {
    this.observations.add(target);
    // Simulate intersection
    if (this.callback) {
      this.callback(
        [
          {
            target,
            isIntersecting: true,
            intersectionRatio: 1,
            intersectionRect: {
              top: 0,
              left: 0,
              bottom: 100,
              right: 100,
              width: 100,
              height: 100,
            },
            boundingClientRect: {
              top: 0,
              left: 0,
              bottom: 100,
              right: 100,
              width: 100,
              height: 100,
            },
            rootBounds: {
              top: 0,
              left: 0,
              bottom: 1024,
              right: 768,
              width: 768,
              height: 1024,
            },
            time: Date.now(),
          },
        ],
        this
      );
    }
  }

  unobserve(target) {
    this.observations.delete(target);
  }

  disconnect() {
    this.observations.clear();
  }
};

// Mock Web APIs that might be missing in jsdom
global.structuredClone =
  global.structuredClone || (obj => JSON.parse(JSON.stringify(obj)));

// Mock crypto.randomUUID for tests
Object.defineProperty(global, 'crypto', {
  value: {
    randomUUID: () => 'test-uuid-' + Math.random().toString(36).substr(2, 9),
    getRandomValues: arr => {
      for (let i = 0; i < arr.length; i++) {
        arr[i] = Math.floor(Math.random() * 256);
      }
      return arr;
    },
  },
});

// Mock localStorage and sessionStorage
const createStorageMock = () => {
  let store = {};
  return {
    getItem: vi.fn(key => store[key] || null),
    setItem: vi.fn((key, value) => {
      store[key] = value.toString();
    }),
    removeItem: vi.fn(key => {
      delete store[key];
    }),
    clear: vi.fn(() => {
      store = {};
    }),
    get length() {
      return Object.keys(store).length;
    },
    key: vi.fn(index => Object.keys(store)[index] || null),
  };
};

Object.defineProperty(window, 'localStorage', { value: createStorageMock() });
Object.defineProperty(window, 'sessionStorage', { value: createStorageMock() });

// Mock URL.createObjectURL and URL.revokeObjectURL
global.URL.createObjectURL = vi.fn(() => 'mock-object-url');
global.URL.revokeObjectURL = vi.fn();

// Mock performance API
Object.defineProperty(window, 'performance', {
  value: {
    now: vi.fn(() => Date.now()),
    mark: vi.fn(),
    measure: vi.fn(),
    getEntriesByType: vi.fn(() => []),
    getEntriesByName: vi.fn(() => []),
    clearMarks: vi.fn(),
    clearMeasures: vi.fn(),
  },
});

// Mock requestAnimationFrame and cancelAnimationFrame
global.requestAnimationFrame = vi.fn(cb => setTimeout(cb, 16));
global.cancelAnimationFrame = vi.fn(id => clearTimeout(id));

// Mock requestIdleCallback
global.requestIdleCallback = vi.fn(cb => setTimeout(cb, 1));
global.cancelIdleCallback = vi.fn(id => clearTimeout(id));
