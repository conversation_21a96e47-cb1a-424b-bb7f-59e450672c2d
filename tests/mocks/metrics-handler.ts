import { vi } from 'vitest';
import type { RequestHandler } from '@sveltejs/kit';
import { json } from '@sveltejs/kit';

export const GET: RequestHandler = vi.fn(async () => {
  return json({
    timestamp: new Date().toISOString(),
    environment: 'test',
    application: {
      version: '1.0.0',
      uptime: 3600,
      active_connections: 42,
      requests_per_minute: 150,
    },
    performance: {
      response_time_avg: 120,
      response_time_p95: 250,
      memory_usage: 512,
      cpu_usage: 0.25,
    },
    system: {
      memory_total: 8192,
      memory_available: 4096,
      disk_usage: 60,
      load_average: 0.8,
    },
  });
});

export const POST: RequestHandler = vi.fn(async ({ request }) => {
  try {
    const body = await request.json();

    if (!body.metric_name || typeof body.value === 'undefined') {
      return json(
        {
          error: 'Missing required fields: metric_name, value',
        },
        { status: 400 }
      );
    }

    return json({
      success: true,
      message: 'Metric recorded successfully',
      metric: {
        name: body.metric_name,
        value: body.value,
        timestamp: new Date().toISOString(),
      },
    });
  } catch {
    return json(
      {
        error: 'Invalid JSON format',
      },
      { status: 500 }
    );
  }
});
