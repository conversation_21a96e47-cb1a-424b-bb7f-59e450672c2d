import { vi } from 'vitest';

const courseServiceMock = {
  getCourses: vi.fn().mockResolvedValue([
    {
      $id: 'course1',
      title: 'Test Course 1',
      description: 'A test course',
    },
    {
      $id: 'course2',
      title: 'Test Course 2',
      description: 'Another test course',
    },
  ]),

  getUserProgress: vi.fn().mockResolvedValue({
    userId: 'user1',
    courseId: 'course1',
    completedLessons: ['lesson1', 'lesson2'],
    lastAccessed: '2025-06-04T14:00:00.000Z',
    currentLesson: 'lesson3',
  }),

  completeLesson: vi.fn().mockResolvedValue(undefined),

  submitExercise: vi
    .fn()
    .mockResolvedValue('Great job! Your solution is correct.'),

  generateFeedback: vi
    .fn()
    .mockResolvedValue(
      'Your code looks good, but consider adding more comments.'
    ),
};

vi.mock('$lib/services/courses', () => ({
  CourseService: vi.fn().mockImplementation(() => courseServiceMock),
}));

export { courseServiceMock };
