import { vi } from 'vitest';

// Enhanced <PERSON>yodide mock for Python execution testing
const createMockPyodide = () => ({
  runPython: vi.fn((code) => {
    // Simulate Python execution results based on code
    if (code.includes('print(')) {
      const match = code.match(/print\(['"](.+?)['"]\)/);
      return match ? match[1] : 'Mock output';
    }
    if (code.includes('import')) {
      return undefined; // Import statements don't return values
    }
    if (code.includes('=')) {
      return undefined; // Assignment statements don't return values
    }
    return 'Mock Python result';
  }),
  
  runPythonAsync: vi.fn(async (code) => {
    await new Promise(resolve => setTimeout(resolve, 10)); // Simulate async delay
    return createMockPyodide().runPython(code);
  }),
  
  loadPackage: vi.fn(async (packages) => {
    await new Promise(resolve => setTimeout(resolve, 50)); // Simulate package loading
    return Array.isArray(packages) ? packages : [packages];
  }),
  
  loadPackagesFromImports: vi.fn(async (code) => {
    await new Promise(resolve => setTimeout(resolve, 30));
    return ['numpy', 'pandas']; // Mock common packages
  }),
  
  globals: new Proxy({}, {
    get: (target, prop) => {
      if (prop === 'get') {
        return vi.fn((name) => `Mock global: ${name}`);
      }
      if (prop === 'set') {
        return vi.fn((name, value) => { target[name] = value; });
      }
      return target[prop] || `Mock global: ${prop}`;
    },
    set: (target, prop, value) => {
      target[prop] = value;
      return true;
    }
  }),
  
  toPy: vi.fn((jsValue) => ({
    toJs: vi.fn(() => jsValue),
    destroy: vi.fn(),
    type: 'PyProxy'
  })),
  
  registerJsModule: vi.fn((name, module) => {
    return true;
  }),
  
  unregisterJsModule: vi.fn((name) => {
    return true;
  }),
  
  setStdout: vi.fn((callback) => {
    // Mock stdout capture
  }),
  
  setStderr: vi.fn((callback) => {
    // Mock stderr capture
  }),
  
  version: '0.24.1',
  
  isPyProxy: vi.fn((obj) => obj && obj.type === 'PyProxy'),
  
  destroy: vi.fn(),
  
  // File system operations
  FS: {
    writeFile: vi.fn((path, data) => {
      return true;
    }),
    readFile: vi.fn((path) => {
      return new Uint8Array([72, 101, 108, 108, 111]); // "Hello" in bytes
    }),
    mkdir: vi.fn((path) => {
      return true;
    }),
    rmdir: vi.fn((path) => {
      return true;
    }),
    unlink: vi.fn((path) => {
      return true;
    }),
    exists: vi.fn((path) => {
      return true;
    })
  }
});

// Mock loadPyodide function
const mockLoadPyodide = vi.fn(async (options = {}) => {
  await new Promise(resolve => setTimeout(resolve, 100)); // Simulate loading time
  return createMockPyodide();
});

vi.mock('pyodide', () => ({
  default: {
    loadPyodide: mockLoadPyodide
  },
  loadPyodide: mockLoadPyodide
}));

// Also mock the CDN import pattern
vi.mock('https://cdn.jsdelivr.net/pyodide/v0.24.1/full/pyodide.js', () => ({
  default: {
    loadPyodide: mockLoadPyodide
  },
  loadPyodide: mockLoadPyodide
}));

export { createMockPyodide, mockLoadPyodide };
