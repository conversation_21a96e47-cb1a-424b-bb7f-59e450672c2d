import { vi } from 'vitest';

const vybeQubeDeploymentMock = {
  deployVybeQube: vi.fn().mockImplementation(async request => {
    if (
      !request.qube_id ||
      !request.generated_files ||
      !request.business_idea
    ) {
      throw new Error('Missing required fields');
    }

    return {
      deployment_id: 'deploy123',
      qube_id: request.qube_id,
      subdomain: request.subdomain || `test-${request.qube_id}`,
      status: 'initializing',
      url: `https://test-${request.qube_id}.vybecoding.ai`,
      estimated_completion: new Date(Date.now() + 300000).toISOString(),
    };
  }),

  getDeploymentStatus: vi.fn().mockImplementation(async deploymentId => {
    if (deploymentId === 'nonexistent') {
      throw new Error('Deployment not found');
    }

    return {
      deployment_id: deploymentId,
      qube_id: 'qube123',
      status: 'deployed',
      progress: 100,
      current_phase: 'completed',
      subdomain: 'test-qube123',
      url: 'https://test-qube123.vybecoding.ai',
      logs: ['Deployment completed successfully'],
      created_at: '2025-06-04T13:00:00.000Z',
      completed_at: '2025-06-04T13:05:00.000Z',
    };
  }),

  listDeployments: vi.fn().mockResolvedValue([
    {
      deployment_id: 'deploy123',
      qube_id: 'qube123',
      status: 'deployed',
    },
    {
      deployment_id: 'deploy456',
      qube_id: 'qube456',
      status: 'failed',
    },
  ]),

  deleteDeployment: vi.fn().mockImplementation(async deploymentId => {
    if (deploymentId === 'nonexistent') {
      throw new Error('Deployment not found');
    }
    return undefined;
  }),

  getDeploymentMetrics: vi.fn().mockResolvedValue({
    total_deployments: 3,
    active_deployments: 1,
    success_rate: 0.85,
    average_deployment_time: 280,
  }),
};

vi.mock('$lib/services/vybeQubeDeployment', () => ({
  default: vi.fn().mockImplementation(() => vybeQubeDeploymentMock),
}));

export { vybeQubeDeploymentMock };
