import { vi } from 'vitest';
import type {
  PromptValidation,
  ValidationError,
  AutonomousInput,
} from '$lib/types/autonomous';

export const validateInput = vi
  .fn()
  .mockImplementation((input: string): PromptValidation => {
    if (!input) {
      return {
        isValid: false,
        errors: ['Input is required'],
      };
    }

    if (input.length < 10) {
      return {
        isValid: false,
        errors: ['Input must be at least 10 characters'],
      };
    }

    return {
      isValid: true,
      errors: [],
    };
  });

export const validateUrl = vi
  .fn()
  .mockImplementation((url: string): PromptValidation => {
    const urlRegex =
      /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w .-]*)*\/?$/;

    if (!url) {
      return {
        isValid: false,
        errors: ['URL is required'],
      };
    }

    if (!urlRegex.test(url)) {
      return {
        isValid: false,
        errors: ['Invalid URL format'],
      };
    }

    return {
      isValid: true,
      errors: [],
    };
  });

export const validateAutonomousInput = vi
  .fn()
  .mockImplementation(
    (
      input: Partial<AutonomousInput>
    ): { isValid: boolean; errors: ValidationError[] } => {
      const errors: ValidationError[] = [];

      if (!input.prompt) {
        errors.push({
          field: 'prompt',
          message: 'Prompt is required',
        });
      }

      if (!input.outputTypes || input.outputTypes.length === 0) {
        errors.push({
          field: 'outputTypes',
          message: 'At least one output type must be selected',
        });
      }

      return {
        isValid: errors.length === 0,
        errors,
      };
    }
  );
