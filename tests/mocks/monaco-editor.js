import { vi } from 'vitest';

// Enhanced Monaco Editor mock with comprehensive API coverage
const createMockEditor = () => ({
  getValue: vi.fn(() => '// Mock editor content'),
  setValue: vi.fn(),
  getModel: vi.fn(() => ({
    uri: { toString: () => 'mock://model/uri' },
    getValue: vi.fn(() => '// Mock model content'),
    setValue: vi.fn(),
    onDidChangeContent: vi.fn(),
    getLanguageId: vi.fn(() => 'javascript'),
    dispose: vi.fn(),
  })),
  setModel: vi.fn(),
  dispose: vi.fn(),
  focus: vi.fn(),
  layout: vi.fn(),
  getPosition: vi.fn(() => ({ lineNumber: 1, column: 1 })),
  setPosition: vi.fn(),
  getSelection: vi.fn(() => ({
    startLineNumber: 1,
    startColumn: 1,
    endLineNumber: 1,
    endColumn: 1,
  })),
  setSelection: vi.fn(),
  revealLine: vi.fn(),
  revealLineInCenter: vi.fn(),
  revealPosition: vi.fn(),
  getAction: vi.fn(),
  trigger: vi.fn(),
  executeCommand: vi.fn(),
  executeCommands: vi.fn(),
  executeEdits: vi.fn(),
  onDidChangeModelContent: vi.fn(() => ({ dispose: vi.fn() })),
  onDidChangeCursorPosition: vi.fn(() => ({ dispose: vi.fn() })),
  onDidChangeCursorSelection: vi.fn(() => ({ dispose: vi.fn() })),
  onDidFocusEditorWidget: vi.fn(() => ({ dispose: vi.fn() })),
  onDidBlurEditorWidget: vi.fn(() => ({ dispose: vi.fn() })),
  onKeyDown: vi.fn(() => ({ dispose: vi.fn() })),
  onKeyUp: vi.fn(() => ({ dispose: vi.fn() })),
  onMouseDown: vi.fn(() => ({ dispose: vi.fn() })),
  onMouseUp: vi.fn(() => ({ dispose: vi.fn() })),
  onMouseMove: vi.fn(() => ({ dispose: vi.fn() })),
  onContextMenu: vi.fn(() => ({ dispose: vi.fn() })),
  addCommand: vi.fn(),
  addAction: vi.fn(),
  getSupportedActions: vi.fn(() => []),
  getContribution: vi.fn(),
  getLineDecorations: vi.fn(() => []),
  deltaDecorations: vi.fn(() => []),
  removeDecorations: vi.fn(),
  changeViewZones: vi.fn(),
  getTargetAtClientPoint: vi.fn(),
  getScrollTop: vi.fn(() => 0),
  setScrollTop: vi.fn(),
  getScrollLeft: vi.fn(() => 0),
  setScrollLeft: vi.fn(),
  getScrollWidth: vi.fn(() => 100),
  getScrollHeight: vi.fn(() => 100),
  getOption: vi.fn(),
  updateOptions: vi.fn(),
  hasTextFocus: vi.fn(() => false),
  hasWidgetFocus: vi.fn(() => false),
});

const createMockModel = (
  value = '',
  language = 'javascript',
  uri = 'mock://model'
) => ({
  uri: { toString: () => uri },
  getValue: vi.fn(() => value),
  setValue: vi.fn(),
  getLanguageId: vi.fn(() => language),
  getLineCount: vi.fn(() => value.split('\n').length),
  getLineContent: vi.fn(lineNumber => value.split('\n')[lineNumber - 1] || ''),
  getLineLength: vi.fn(
    lineNumber => (value.split('\n')[lineNumber - 1] || '').length
  ),
  getPositionAt: vi.fn(() => ({ lineNumber: 1, column: 1 })),
  getOffsetAt: vi.fn(() => 0),
  getValueInRange: vi.fn(() => ''),
  getWordAtPosition: vi.fn(() => null),
  getWordUntilPosition: vi.fn(() => ({
    word: '',
    startColumn: 1,
    endColumn: 1,
  })),
  findMatches: vi.fn(() => []),
  findNextMatch: vi.fn(() => null),
  findPreviousMatch: vi.fn(() => null),
  onDidChangeContent: vi.fn(() => ({ dispose: vi.fn() })),
  onDidChangeLanguage: vi.fn(() => ({ dispose: vi.fn() })),
  onDidChangeOptions: vi.fn(() => ({ dispose: vi.fn() })),
  onWillDispose: vi.fn(() => ({ dispose: vi.fn() })),
  dispose: vi.fn(),
  isDisposed: vi.fn(() => false),
  getAlternativeVersionId: vi.fn(() => 1),
  getVersionId: vi.fn(() => 1),
  pushStackElement: vi.fn(),
  pushEditOperations: vi.fn(),
  applyEdits: vi.fn(),
  deltaDecorations: vi.fn(() => []),
  getDecorationOptions: vi.fn(),
  getDecorationRange: vi.fn(),
  getDecorationsInRange: vi.fn(() => []),
  getAllDecorations: vi.fn(() => []),
  undo: vi.fn(),
  redo: vi.fn(),
  canUndo: vi.fn(() => false),
  canRedo: vi.fn(() => false),
});

vi.mock('monaco-editor', () => ({
  default: {
    editor: {
      create: vi.fn((container, options = {}) => createMockEditor()),
      createDiffEditor: vi.fn((container, options = {}) => ({
        ...createMockEditor(),
        getOriginalEditor: vi.fn(() => createMockEditor()),
        getModifiedEditor: vi.fn(() => createMockEditor()),
        setModel: vi.fn(),
      })),
      defineTheme: vi.fn(),
      setTheme: vi.fn(),
      getTheme: vi.fn(() => 'vs'),
      createModel: vi.fn((value, language, uri) =>
        createMockModel(value, language, uri)
      ),
      getModel: vi.fn(() => createMockModel()),
      getModels: vi.fn(() => []),
      onDidCreateModel: vi.fn(() => ({ dispose: vi.fn() })),
      onWillDisposeModel: vi.fn(() => ({ dispose: vi.fn() })),
      onDidChangeModelLanguage: vi.fn(() => ({ dispose: vi.fn() })),
      createWebWorker: vi.fn(() =>
        Promise.resolve({
          getProxy: vi.fn(() => Promise.resolve({})),
          dispose: vi.fn(),
        })
      ),
      colorizeElement: vi.fn(() => Promise.resolve()),
      colorize: vi.fn(() => Promise.resolve('')),
      tokenize: vi.fn(() => []),
      setModelLanguage: vi.fn(),
      remeasureFonts: vi.fn(),
      registerCommand: vi.fn(),
      addKeybindingRule: vi.fn(),
      addKeybindingRules: vi.fn(),
    },
    languages: {
      register: vi.fn(),
      getLanguages: vi.fn(() => [
        { id: 'javascript', extensions: ['.js'], aliases: ['JavaScript'] },
        { id: 'typescript', extensions: ['.ts'], aliases: ['TypeScript'] },
        { id: 'python', extensions: ['.py'], aliases: ['Python'] },
        { id: 'html', extensions: ['.html'], aliases: ['HTML'] },
        { id: 'css', extensions: ['.css'], aliases: ['CSS'] },
        { id: 'json', extensions: ['.json'], aliases: ['JSON'] },
      ]),
      getEncodedLanguageId: vi.fn(() => 1),
      onDidChange: vi.fn(() => ({ dispose: vi.fn() })),
      setLanguageConfiguration: vi.fn(),
      setMonarchTokensProvider: vi.fn(),
      registerReferenceProvider: vi.fn(),
      registerRenameProvider: vi.fn(),
      registerSignatureHelpProvider: vi.fn(),
      registerHoverProvider: vi.fn(),
      registerDocumentSymbolProvider: vi.fn(),
      registerDocumentHighlightProvider: vi.fn(),
      registerDefinitionProvider: vi.fn(),
      registerImplementationProvider: vi.fn(),
      registerTypeDefinitionProvider: vi.fn(),
      registerCodeLensProvider: vi.fn(),
      registerCodeActionProvider: vi.fn(),
      registerDocumentFormattingEditProvider: vi.fn(),
      registerDocumentRangeFormattingEditProvider: vi.fn(),
      registerOnTypeFormattingEditProvider: vi.fn(),
      registerLinkProvider: vi.fn(),
      registerCompletionItemProvider: vi.fn(),
      registerColorProvider: vi.fn(),
      registerFoldingRangeProvider: vi.fn(),
      registerDeclarationProvider: vi.fn(),
      registerSelectionRangeProvider: vi.fn(),
      registerDocumentSemanticTokensProvider: vi.fn(),
      registerDocumentRangeSemanticTokensProvider: vi.fn(),
      registerInlineCompletionsProvider: vi.fn(),
    },
    Range: vi
      .fn()
      .mockImplementation(
        (startLineNumber, startColumn, endLineNumber, endColumn) => ({
          startLineNumber,
          startColumn,
          endLineNumber,
          endColumn,
          isEmpty: vi.fn(
            () => startLineNumber === endLineNumber && startColumn === endColumn
          ),
          containsPosition: vi.fn(() => false),
          containsRange: vi.fn(() => false),
          strictContainsRange: vi.fn(() => false),
          plusRange: vi.fn(),
          intersectRanges: vi.fn(),
          equalsRange: vi.fn(() => false),
          getEndPosition: vi.fn(() => ({
            lineNumber: endLineNumber,
            column: endColumn,
          })),
          getStartPosition: vi.fn(() => ({
            lineNumber: startLineNumber,
            column: startColumn,
          })),
          toString: vi.fn(
            () =>
              `[${startLineNumber},${startColumn} -> ${endLineNumber},${endColumn}]`
          ),
        })
      ),
    Position: vi.fn().mockImplementation((lineNumber, column) => ({
      lineNumber,
      column,
      equals: vi.fn(() => false),
      isBefore: vi.fn(() => false),
      isBeforeOrEqual: vi.fn(() => false),
      clone: vi.fn(() => ({ lineNumber, column })),
      toString: vi.fn(() => `(${lineNumber},${column})`),
    })),
    Selection: vi.fn(),
    Uri: {
      parse: vi.fn(uri => ({ toString: () => uri })),
      file: vi.fn(path => ({ toString: () => `file://${path}` })),
      from: vi.fn(components => ({ toString: () => 'mock://uri' })),
    },
    KeyCode: {
      Enter: 3,
      Escape: 9,
      Space: 10,
      Tab: 2,
      Backspace: 1,
      Delete: 20,
      ArrowLeft: 15,
      ArrowUp: 16,
      ArrowRight: 17,
      ArrowDown: 18,
    },
    KeyMod: {
      CtrlCmd: 2048,
      Shift: 1024,
      Alt: 512,
      WinCtrl: 256,
    },
    MarkerSeverity: {
      Hint: 1,
      Info: 2,
      Warning: 4,
      Error: 8,
    },
  },
  // Named exports
  editor: {
    create: vi.fn(() => createMockEditor()),
    defineTheme: vi.fn(),
    setTheme: vi.fn(),
  },
  languages: {
    register: vi.fn(),
    setMonarchTokensProvider: vi.fn(),
    registerCompletionItemProvider: vi.fn(),
  },
}));
