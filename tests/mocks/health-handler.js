// Mock implementations for health endpoint tests
export const healthHandlerMock = {
  GET: async () => {
    return new Response(JSON.stringify({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      checks: {
        database: { status: 'healthy', responseTime: 5 },
        api: { status: 'healthy', responseTime: 2 },
        memory: { status: 'healthy', usage: 45 },
        disk: { status: 'healthy', usage: 60 }
      },
      metrics: {
        uptime: Math.floor(process.uptime()),
        memory_usage: process.memoryUsage(),
        cpu_usage: 0.3
      },
      performance: {
        response_time: 150,
        throughput: 1200,
        error_rate: 0.01
      }
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  },
  
  HEAD: async () => {
    return new Response(null, {
      status: 200
    });
  }
};
