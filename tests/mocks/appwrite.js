import { vi } from 'vitest';

// Enhanced Appwrite mock for backend service testing
const createMockAccount = () => ({
  create: vi.fn(async (userId, email, password, name) => ({
    $id: userId || 'mock-user-id',
    email,
    name: name || 'Mock User',
    registration: new Date().toISOString(),
    status: true,
    emailVerification: false,
    phoneVerification: false,
    prefs: {}
  })),
  
  createEmailSession: vi.fn(async (email, password) => ({
    $id: 'mock-session-id',
    userId: 'mock-user-id',
    expire: new Date(Date.now() + ********).toISOString(),
    provider: 'email',
    providerUid: email,
    providerAccessToken: '',
    providerAccessTokenExpiry: '',
    providerRefreshToken: '',
    ip: '127.0.0.1',
    osCode: 'web',
    osName: 'Web',
    osVersion: '',
    clientType: 'browser',
    clientCode: 'chrome',
    clientName: 'Chrome',
    clientVersion: '100.0',
    clientEngine: 'Blink',
    clientEngineVersion: '100.0',
    deviceName: 'desktop',
    deviceBrand: '',
    deviceModel: '',
    countryCode: 'US',
    countryName: 'United States'
  })),
  
  get: vi.fn(async () => ({
    $id: 'mock-user-id',
    email: '<EMAIL>',
    name: 'Test User',
    registration: new Date().toISOString(),
    status: true,
    emailVerification: true,
    phoneVerification: false,
    prefs: {}
  })),
  
  getSession: vi.fn(async (sessionId) => ({
    $id: sessionId || 'current',
    userId: 'mock-user-id',
    expire: new Date(Date.now() + ********).toISOString()
  })),
  
  deleteSession: vi.fn(async (sessionId) => ({})),
  
  updateEmail: vi.fn(async (email, password) => ({
    $id: 'mock-user-id',
    email,
    name: 'Test User'
  })),
  
  updateName: vi.fn(async (name) => ({
    $id: 'mock-user-id',
    email: '<EMAIL>',
    name
  })),
  
  updatePassword: vi.fn(async (password, oldPassword) => ({
    $id: 'mock-user-id',
    email: '<EMAIL>',
    name: 'Test User'
  })),
  
  createVerification: vi.fn(async (url) => ({
    $id: 'mock-verification-id',
    userId: 'mock-user-id',
    secret: 'mock-secret',
    expire: new Date(Date.now() + 3600000).toISOString()
  })),
  
  updateVerification: vi.fn(async (userId, secret) => ({
    $id: 'mock-verification-id',
    userId,
    secret,
    expire: new Date(Date.now() + 3600000).toISOString()
  }))
});

const createMockDatabases = () => ({
  createDocument: vi.fn(async (databaseId, collectionId, documentId, data, permissions) => ({
    $id: documentId || 'mock-document-id',
    $collectionId: collectionId,
    $databaseId: databaseId,
    $createdAt: new Date().toISOString(),
    $updatedAt: new Date().toISOString(),
    $permissions: permissions || [],
    ...data
  })),
  
  getDocument: vi.fn(async (databaseId, collectionId, documentId) => ({
    $id: documentId,
    $collectionId: collectionId,
    $databaseId: databaseId,
    $createdAt: new Date().toISOString(),
    $updatedAt: new Date().toISOString(),
    $permissions: [],
    title: 'Mock Document',
    content: 'Mock content'
  })),
  
  updateDocument: vi.fn(async (databaseId, collectionId, documentId, data, permissions) => ({
    $id: documentId,
    $collectionId: collectionId,
    $databaseId: databaseId,
    $createdAt: new Date().toISOString(),
    $updatedAt: new Date().toISOString(),
    $permissions: permissions || [],
    ...data
  })),
  
  deleteDocument: vi.fn(async (databaseId, collectionId, documentId) => ({})),
  
  listDocuments: vi.fn(async (databaseId, collectionId, queries) => ({
    total: 2,
    documents: [
      {
        $id: 'doc1',
        $collectionId: collectionId,
        $databaseId: databaseId,
        $createdAt: new Date().toISOString(),
        $updatedAt: new Date().toISOString(),
        $permissions: [],
        title: 'Document 1'
      },
      {
        $id: 'doc2',
        $collectionId: collectionId,
        $databaseId: databaseId,
        $createdAt: new Date().toISOString(),
        $updatedAt: new Date().toISOString(),
        $permissions: [],
        title: 'Document 2'
      }
    ]
  }))
});

const createMockStorage = () => ({
  createFile: vi.fn(async (bucketId, fileId, file, permissions) => ({
    $id: fileId || 'mock-file-id',
    bucketId,
    $createdAt: new Date().toISOString(),
    $updatedAt: new Date().toISOString(),
    $permissions: permissions || [],
    name: file.name || 'mock-file.txt',
    signature: 'mock-signature',
    mimeType: file.type || 'text/plain',
    sizeOriginal: file.size || 1024,
    chunksTotal: 1,
    chunksUploaded: 1
  })),
  
  getFile: vi.fn(async (bucketId, fileId) => ({
    $id: fileId,
    bucketId,
    $createdAt: new Date().toISOString(),
    $updatedAt: new Date().toISOString(),
    $permissions: [],
    name: 'mock-file.txt',
    signature: 'mock-signature',
    mimeType: 'text/plain',
    sizeOriginal: 1024,
    chunksTotal: 1,
    chunksUploaded: 1
  })),
  
  deleteFile: vi.fn(async (bucketId, fileId) => ({})),
  
  getFileView: vi.fn((bucketId, fileId) => `https://mock-appwrite.com/v1/storage/buckets/${bucketId}/files/${fileId}/view`),
  
  getFileDownload: vi.fn((bucketId, fileId) => `https://mock-appwrite.com/v1/storage/buckets/${bucketId}/files/${fileId}/download`),
  
  listFiles: vi.fn(async (bucketId, queries) => ({
    total: 1,
    files: [
      {
        $id: 'mock-file-id',
        bucketId,
        $createdAt: new Date().toISOString(),
        $updatedAt: new Date().toISOString(),
        $permissions: [],
        name: 'mock-file.txt',
        signature: 'mock-signature',
        mimeType: 'text/plain',
        sizeOriginal: 1024
      }
    ]
  }))
});

const createMockClient = () => ({
  setEndpoint: vi.fn().mockReturnThis(),
  setProject: vi.fn().mockReturnThis(),
  setKey: vi.fn().mockReturnThis(),
  setJWT: vi.fn().mockReturnThis(),
  setLocale: vi.fn().mockReturnThis(),
  setSession: vi.fn().mockReturnThis(),
  headers: {},
  config: {
    endpoint: 'https://mock-appwrite.com/v1',
    project: 'mock-project-id',
    key: '',
    jwt: '',
    locale: 'en'
  }
});

// Mock Appwrite SDK
vi.mock('appwrite', () => ({
  Client: vi.fn(() => createMockClient()),
  Account: vi.fn(() => createMockAccount()),
  Databases: vi.fn(() => createMockDatabases()),
  Storage: vi.fn(() => createMockStorage()),
  Query: {
    equal: vi.fn((attribute, value) => `equal("${attribute}", "${value}")`),
    notEqual: vi.fn((attribute, value) => `notEqual("${attribute}", "${value}")`),
    lessThan: vi.fn((attribute, value) => `lessThan("${attribute}", ${value})`),
    greaterThan: vi.fn((attribute, value) => `greaterThan("${attribute}", ${value})`),
    search: vi.fn((attribute, value) => `search("${attribute}", "${value}")`),
    orderAsc: vi.fn((attribute) => `orderAsc("${attribute}")`),
    orderDesc: vi.fn((attribute) => `orderDesc("${attribute}")`),
    limit: vi.fn((limit) => `limit(${limit})`),
    offset: vi.fn((offset) => `offset(${offset})`)
  },
  Permission: {
    read: vi.fn((role) => `read("${role}")`),
    write: vi.fn((role) => `write("${role}")`),
    create: vi.fn((role) => `create("${role}")`),
    update: vi.fn((role) => `update("${role}")`),
    delete: vi.fn((role) => `delete("${role}")`)
  },
  Role: {
    any: () => 'any',
    user: (id) => `user:${id}`,
    users: () => 'users',
    guests: () => 'guests'
  },
  ID: {
    unique: vi.fn(() => 'mock-unique-id-' + Math.random().toString(36).substr(2, 9))
  }
}));

export { createMockClient, createMockAccount, createMockDatabases, createMockStorage };
