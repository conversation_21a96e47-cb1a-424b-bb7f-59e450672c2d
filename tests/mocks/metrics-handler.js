// Mock implementations for metrics endpoint tests
export const metricsHandlerMock = {
  GET: async () => {
    return new Response(JSON.stringify({
      timestamp: new Date().toISOString(),
      environment: 'test',
      application: {
        version: '1.0.0',
        uptime: Math.floor(process.uptime()),
        active_connections: 42,
        requests_per_minute: 150
      },
      performance: {
        response_time_avg: 120,
        response_time_p95: 250,
        memory_usage: process.memoryUsage().heapUsed / 1024 / 1024,
        cpu_usage: 0.25
      },
      system: {
        memory_total: 8192,
        memory_available: 4096,
        disk_usage: 60,
        load_average: 0.8
      }
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  },
  
  POST: async ({ request }) => {
    try {
      const body = await request.json();
      
      if (!body.metric_name || typeof body.value === 'undefined') {
        return new Response(JSON.stringify({
          error: 'Missing required fields: metric_name, value'
        }), {
          status: 400,
          headers: {
            'Content-Type': 'application/json'
          }
        });
      }

      return new Response(JSON.stringify({
        success: true,
        metric: {
          name: body.metric_name,
          value: body.value,
          timestamp: new Date().toISOString(),
          tags: body.tags || {}
        }
      }), {
        status: 200,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    } catch (error) {
      return new Response(JSON.stringify({
        error: 'Invalid JSON format'
      }), {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
  }
};
