import { vi } from 'vitest';
import type { RequestHandler } from '@sveltejs/kit';
import { json } from '@sveltejs/kit';

export const GET: RequestHandler = vi.fn(async () => {
  try {
    return json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      checks: {
        database: { status: 'healthy' },
        api: { status: 'healthy' },
        storage: { status: 'healthy' },
      },
      metrics: {
        uptime: 3600,
        requests: 1000,
        response_time: 150,
      },
    });
  } catch (error) {
    return json(
      {
        status: 'unhealthy',
        error: 'Health check failed',
      },
      { status: 503 }
    );
  }
});
