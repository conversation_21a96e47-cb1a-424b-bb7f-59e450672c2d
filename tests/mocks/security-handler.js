// Default mock responses for security endpoints
export const securityHandlerMock = {
  GET: async () => {
    return new Response(JSON.stringify({
      timestamp: new Date().toISOString(),
      environment: 'test',
      checks: {
        ssl_certificate: { status: 'valid', expires: '2025-12-31' },
        cors_policy: { status: 'configured', origins: ['localhost:5173'] },
        rate_limiting: { status: 'active', requests_per_minute: 100 },
        authentication: { status: 'enabled', provider: 'appwrite' }
      },
      recommendations: [
        'Enable Content Security Policy headers',
        'Implement API rate limiting',
        'Add request logging for audit trails'
      ],
      vulnerabilities: [],
      security_score: 85
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};
