import { render } from '@testing-library/svelte';
import { vi } from 'vitest';

// Mock SvelteKit modules
vi.mock('$app/navigation', () => ({
  goto: vi.fn(),
  beforeNavigate: vi.fn(),
  afterNavigate: vi.fn(),
}));

vi.mock('$app/environment', () => ({
  browser: true,
  dev: true,
  building: false,
}));

vi.mock('$app/stores', () => ({
  page: {
    subscribe: vi.fn((fn) => {
      fn({ url: new URL('http://localhost:5173'), params: {} });
      return () => {};
    }),
  },
  navigating: {
    subscribe: vi.fn(() => () => {}),
  },
  updated: {
    subscribe: vi.fn(() => () => {}),
  },
}));

// Mock fetch globally
global.fetch = vi.fn(() => 
  Promise.resolve({
    ok: true,
    json: () => Promise.resolve({}),
    text: () => Promise.resolve(''),
    blob: () => Promise.resolve(new Blob()),
    arrayBuffer: () => Promise.resolve(new ArrayBuffer(0)),
  })
);

// Custom render function that provides common wrappers/context
export function customRender(Component, props = {}) {
  return render(Component, { props });
}

// Helper to wait for component updates
export function waitForComponentUpdate() {
  return new Promise(resolve => setTimeout(resolve, 0));
}

// Mock Svelte transitions
vi.mock('svelte/transition', () => ({
  fade: () => ({
    duration: 0,
    css: () => '',
  }),
  fly: () => ({
    duration: 0,
    css: () => '',
  }),
  slide: () => ({
    duration: 0,
    css: () => '',
  }),
}));

// Mock Svelte animations
vi.mock('svelte/animate', () => ({
  flip: () => ({
    duration: 0,
    css: () => '',
  }),
}));

// Mock browser APIs that might be missing in jsdom
global.ResizeObserver = class ResizeObserver {
  observe() {}
  unobserve() {}
  disconnect() {}
};

global.IntersectionObserver = class IntersectionObserver {
  observe() {}
  unobserve() {}
  disconnect() {}
};

global.matchMedia = vi.fn().mockImplementation(query => ({
  matches: false,
  media: query,
  onchange: null,
  addListener: vi.fn(),
  removeListener: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  dispatchEvent: vi.fn(),
}));
