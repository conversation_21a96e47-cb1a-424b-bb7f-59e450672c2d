/**
 * MAS-008 Integration Tests
 * Tests for Sprint 3 content management components and Vybe Method integration
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/svelte';
import { get } from 'svelte/store';

// Components to test
import ContentCard from '$lib/components/content/ContentCard.svelte';
import FolderTree from '$lib/components/content/FolderTree.svelte';
import ContentEditor from '$lib/components/content/ContentEditor.svelte';
import ContentLibrary from '$lib/components/content/ContentLibrary.svelte';

// Services and stores
import {
  contentService,
  filteredContent,
  selectedFolder,
} from '$lib/services/contentService';
import type { ContentItem, ContentFolder } from '$lib/services/contentService';

// Mock data
const mockContentItem: ContentItem = {
  id: 'test-content-1',
  title: 'Test Article',
  description: 'A test article for MAS-008 integration testing',
  content: '# Test Article\n\nThis is test content.',
  type: 'article',
  tags: ['test', 'mas-008', 'integration'],
  author: 'Test Author',
  status: 'published',
  created: '2025-01-01T00:00:00Z',
  updated: '2025-01-01T12:00:00Z',
  folderId: null,
};

const mockFolders: ContentFolder[] = [
  {
    id: 'folder-1',
    name: 'Articles',
    parentId: null,
    children: [
      {
        id: 'folder-2',
        name: 'Technical',
        parentId: 'folder-1',
        children: [],
        itemCount: 5,
      },
    ],
    itemCount: 10,
  },
  {
    id: 'folder-3',
    name: 'Courses',
    parentId: null,
    children: [],
    itemCount: 3,
  },
];

describe('MAS-008 Content Management Integration', () => {
  beforeEach(() => {
    // Reset stores
    selectedFolder.set(null);

    // Mock content service
    vi.spyOn(contentService, 'getContent').mockResolvedValue([mockContentItem]);
    vi.spyOn(contentService, 'getFolders').mockResolvedValue(mockFolders);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('ContentCard Component', () => {
    it('should render content card in grid view', () => {
      render(ContentCard, {
        props: {
          content: mockContentItem,
          viewMode: 'grid',
        },
      });

      expect(screen.getByText('Test Article')).toBeInTheDocument();
      expect(
        screen.getByText(/A test article for MAS-008/)
      ).toBeInTheDocument();
      expect(screen.getByText('Test Author')).toBeInTheDocument();
    });

    it('should render content card in list view', () => {
      render(ContentCard, {
        props: {
          content: mockContentItem,
          viewMode: 'list',
        },
      });

      expect(screen.getByText('Test Article')).toBeInTheDocument();
      expect(screen.getByText('article')).toBeInTheDocument();
    });

    it('should emit select event when clicked', async () => {
      const { component } = render(ContentCard, {
        props: {
          content: mockContentItem,
          viewMode: 'grid',
        },
      });

      let selectEmitted = false;
      component.$on('select', () => {
        selectEmitted = true;
      });

      const card = screen.getByRole('button');
      await fireEvent.click(card);

      expect(selectEmitted).toBe(true);
    });

    it('should show action buttons on hover', async () => {
      render(ContentCard, {
        props: {
          content: mockContentItem,
          viewMode: 'grid',
        },
      });

      const card = screen.getByRole('button');
      await fireEvent.mouseEnter(card);

      expect(screen.getByTitle('View content')).toBeInTheDocument();
      expect(screen.getByTitle('Edit content')).toBeInTheDocument();
      expect(screen.getByTitle('Delete content')).toBeInTheDocument();
    });

    it('should display tags correctly', () => {
      render(ContentCard, {
        props: {
          content: mockContentItem,
          viewMode: 'grid',
        },
      });

      expect(screen.getByText('test')).toBeInTheDocument();
      expect(screen.getByText('mas-008')).toBeInTheDocument();
      expect(screen.getByText('integration')).toBeInTheDocument();
    });
  });

  describe('FolderTree Component', () => {
    it('should render folder tree structure', () => {
      render(FolderTree, {
        props: {
          folders: mockFolders,
          selectedFolderId: null,
        },
      });

      expect(screen.getByText('Articles')).toBeInTheDocument();
      expect(screen.getByText('Courses')).toBeInTheDocument();
      expect(screen.getByText('(10)')).toBeInTheDocument(); // Item count
      expect(screen.getByText('(3)')).toBeInTheDocument();
    });

    it('should expand/collapse folders', async () => {
      render(FolderTree, {
        props: {
          folders: mockFolders,
          selectedFolderId: null,
        },
      });

      // Initially, Technical subfolder should not be visible
      expect(screen.queryByText('Technical')).not.toBeInTheDocument();

      // Click expand button for Articles folder
      const expandButton = screen.getAllByRole('button')[0];
      await fireEvent.click(expandButton);

      // Now Technical subfolder should be visible
      await waitFor(() => {
        expect(screen.getByText('Technical')).toBeInTheDocument();
      });
    });

    it('should emit folderSelect event when folder is clicked', async () => {
      const { component } = render(FolderTree, {
        props: {
          folders: mockFolders,
          selectedFolderId: null,
        },
      });

      let selectedFolderId = '';
      component.$on('folderSelect', event => {
        selectedFolderId = event.detail;
      });

      const articlesFolder = screen
        .getByText('Articles')
        .closest('[role="button"]');
      await fireEvent.click(articlesFolder!);

      expect(selectedFolderId).toBe('folder-1');
    });

    it('should highlight selected folder', () => {
      render(FolderTree, {
        props: {
          folders: mockFolders,
          selectedFolderId: 'folder-1',
        },
      });

      const selectedFolder = screen
        .getByText('Articles')
        .closest('.folder-row');
      expect(selectedFolder).toHaveClass('selected');
    });
  });

  describe('ContentEditor Component', () => {
    it('should render content editor with form fields', () => {
      render(ContentEditor, {
        props: {
          content: mockContentItem,
          mode: 'edit',
        },
      });

      expect(screen.getByDisplayValue('Test Article')).toBeInTheDocument();
      expect(
        screen.getByDisplayValue(/A test article for MAS-008/)
      ).toBeInTheDocument();
      expect(screen.getByDisplayValue('Test Author')).toBeInTheDocument();
    });

    it('should switch between editor modes', async () => {
      render(ContentEditor, {
        props: {
          content: mockContentItem,
          mode: 'edit',
        },
      });

      // Should start in visual mode
      expect(screen.getByText('Visual')).toHaveClass('active');

      // Switch to code mode
      const codeButton = screen.getByText('Code');
      await fireEvent.click(codeButton);

      expect(screen.getByText('Code')).toHaveClass('active');
    });

    it('should handle tag management', async () => {
      render(ContentEditor, {
        props: {
          content: mockContentItem,
          mode: 'edit',
        },
      });

      // Should display existing tags
      expect(screen.getByText('test')).toBeInTheDocument();
      expect(screen.getByText('mas-008')).toBeInTheDocument();

      // Should be able to add new tag
      const addTagButton = screen.getByText('Add tag');
      await fireEvent.click(addTagButton);

      const tagInput = screen.getByPlaceholderText('New tag...');
      await fireEvent.input(tagInput, { target: { value: 'new-tag' } });
      await fireEvent.keyDown(tagInput, { key: 'Enter' });

      expect(screen.getByText('new-tag')).toBeInTheDocument();
    });

    it('should emit save event with updated content', async () => {
      const { component } = render(ContentEditor, {
        props: {
          content: mockContentItem,
          mode: 'edit',
        },
      });

      let savedContent: ContentItem | null = null;
      component.$on('save', event => {
        savedContent = event.detail;
      });

      // Modify title
      const titleInput = screen.getByDisplayValue('Test Article');
      await fireEvent.input(titleInput, {
        target: { value: 'Updated Test Article' },
      });

      // Click save button
      const saveButton = screen.getByText('Save');
      await fireEvent.click(saveButton);

      expect(savedContent).toBeTruthy();
      expect(savedContent?.title).toBe('Updated Test Article');
    });

    it('should show unsaved changes indicator', async () => {
      render(ContentEditor, {
        props: {
          content: mockContentItem,
          mode: 'edit',
        },
      });

      // Initially no unsaved changes
      expect(screen.queryByText('Unsaved changes')).not.toBeInTheDocument();

      // Modify content
      const titleInput = screen.getByDisplayValue('Test Article');
      await fireEvent.input(titleInput, {
        target: { value: 'Modified Title' },
      });

      // Should show unsaved changes indicator
      await waitFor(() => {
        expect(screen.getByText('Unsaved changes')).toBeInTheDocument();
      });
    });
  });

  describe('ContentLibrary Integration', () => {
    it('should render content library with all components', async () => {
      render(ContentLibrary);

      // Wait for content to load
      await waitFor(() => {
        expect(screen.getByText('📁 Folders')).toBeInTheDocument();
      });

      // Should show folder tree
      expect(screen.getByText('All Content')).toBeInTheDocument();

      // Should show content grid
      expect(screen.getByText('Test Article')).toBeInTheDocument();
    });

    it('should filter content by folder selection', async () => {
      render(ContentLibrary);

      await waitFor(() => {
        expect(screen.getByText('Articles')).toBeInTheDocument();
      });

      // Select a folder
      const articlesFolder = screen.getByText('Articles');
      await fireEvent.click(articlesFolder);

      // Should update selected folder store
      expect(get(selectedFolder)).toBe('folder-1');
    });

    it('should handle search functionality', async () => {
      render(ContentLibrary);

      await waitFor(() => {
        expect(
          screen.getByPlaceholderText(/Search content/)
        ).toBeInTheDocument();
      });

      const searchInput = screen.getByPlaceholderText(/Search content/);
      await fireEvent.input(searchInput, { target: { value: 'test' } });

      // Should filter content based on search
      await waitFor(() => {
        expect(get(filteredContent)).toHaveLength(1);
      });
    });

    it('should open content editor when edit is clicked', async () => {
      render(ContentLibrary);

      await waitFor(() => {
        expect(screen.getByText('Test Article')).toBeInTheDocument();
      });

      // Hover over content card to show actions
      const contentCard = screen
        .getByText('Test Article')
        .closest('.content-card');
      await fireEvent.mouseEnter(contentCard!);

      // Click edit button
      const editButton = screen.getByTitle('Edit content');
      await fireEvent.click(editButton);

      // Should open content editor
      await waitFor(() => {
        expect(screen.getByText('Edit Content')).toBeInTheDocument();
      });
    });
  });

  describe('Vybe Method Integration', () => {
    it('should support autonomous content generation', async () => {
      // Mock Vybe Method integration
      const mockVybeGeneration = vi.fn().mockResolvedValue({
        success: true,
        content: {
          ...mockContentItem,
          title: 'AI Generated Article',
          content: '# AI Generated Content\n\nThis was created by VYBRO agent.',
        },
      });

      // This would test the integration with Vybe Method agents
      const result = await mockVybeGeneration({
        type: 'article',
        prompt: 'Create an article about AI development',
        agent: 'vybro',
      });

      expect(result.success).toBe(true);
      expect(result.content.title).toBe('AI Generated Article');
    });

    it('should handle real-time collaboration features', async () => {
      // Mock real-time collaboration
      const mockCollaboration = {
        activeUsers: ['user1', 'user2'],
        currentEditor: 'user1',
        changes: [],
      };

      // This would test real-time collaboration features
      expect(mockCollaboration.activeUsers).toHaveLength(2);
      expect(mockCollaboration.currentEditor).toBe('user1');
    });
  });
});

describe('Performance and Accessibility', () => {
  it('should meet accessibility standards', () => {
    render(ContentCard, {
      props: {
        content: mockContentItem,
        viewMode: 'grid',
      },
    });

    // Check for proper ARIA attributes
    const card = screen.getByRole('button');
    expect(card).toHaveAttribute('tabindex', '0');

    // Check for keyboard navigation
    expect(card).toBeInTheDocument();
  });

  it('should handle large content lists efficiently', async () => {
    const largeContentList = Array.from({ length: 1000 }, (_, i) => ({
      ...mockContentItem,
      id: `content-${i}`,
      title: `Article ${i}`,
    }));

    vi.spyOn(contentService, 'getContent').mockResolvedValue(largeContentList);

    const startTime = performance.now();
    render(ContentLibrary);

    await waitFor(() => {
      expect(screen.getByText('📁 Folders')).toBeInTheDocument();
    });

    const endTime = performance.now();
    const renderTime = endTime - startTime;

    // Should render within reasonable time (less than 1 second)
    expect(renderTime).toBeLessThan(1000);
  });
});
