"""
Integration tests for Enhanced 2025 AI Agent Protocols
Tests the complete integration of A2A, AG-UI, Agentic Retrieval, and Safety Guardrails
"""

import pytest
import asyncio
import json
import tempfile
from pathlib import Path
from datetime import datetime
import logging

# Test imports
try:
    from method.vybe.agentic_retrieval_engine import Agentic<PERSON><PERSON>rieval<PERSON><PERSON><PERSON>, VybeAgenticRetrieval, RetrievalContext
    from method.vybe.a2a_protocol import A2AProtocolHandler, A2AMessage, A2AMessageType
    from method.vybe.ag_ui_protocol import AG<PERSON>ProtocolHandler, AGUIMessage, AGUIMessageType
    from method.vybe.enhanced_safety_guardrails import EnhancedSafetyGuardrails, SafetyLevel
    from method.vybe.real_mas_coordinator import RealMASCoordinator
    PROTOCOLS_AVAILABLE = True
except ImportError as e:
    PROTOCOLS_AVAILABLE = False
    print(f"Enhanced protocols not available: {e}")

# Configure logging for tests
logging.basicConfig(level=logging.INFO)

@pytest.mark.skipif(not PROTOCOLS_AVAILABLE, reason="Enhanced protocols not available")
class TestEnhancedProtocolsIntegration:
    """Test suite for enhanced 2025 AI agent protocols integration"""
    
    @pytest.fixture
    async def temp_project_dir(self):
        """Create temporary project directory for testing"""
        with tempfile.TemporaryDirectory() as temp_dir:
            project_path = Path(temp_dir)
            
            # Create test data structure
            (project_path / "data").mkdir()
            (project_path / "data" / "chroma_db").mkdir()
            (project_path / "data" / "graph_rag").mkdir()
            
            # Create test documents
            test_docs = [
                "Python is a programming language created by Guido van Rossum in 1991.",
                "JavaScript was created by Brendan Eich in 1995 for web development.",
                "Machine learning is a subset of artificial intelligence that enables computers to learn.",
                "The Vybe Method is an advanced AI development methodology using multi-agent systems."
            ]
            
            for i, doc in enumerate(test_docs):
                (project_path / "data" / f"test_doc_{i}.txt").write_text(doc)
            
            yield str(project_path)
    
    @pytest.fixture
    async def agentic_retrieval_engine(self, temp_project_dir):
        """Create agentic retrieval engine for testing"""
        engine = AgenticRetrievalEngine(
            project_root=temp_project_dir,
            project_name="TestProject"
        )
        
        # Create test knowledge indices
        await engine.create_knowledge_index(
            name="programming_languages",
            description="Information about programming languages and their creators",
            file_paths=[
                str(Path(temp_project_dir) / "data" / "test_doc_0.txt"),
                str(Path(temp_project_dir) / "data" / "test_doc_1.txt")
            ]
        )
        
        await engine.create_knowledge_index(
            name="ai_concepts",
            description="Artificial intelligence and machine learning concepts",
            file_paths=[
                str(Path(temp_project_dir) / "data" / "test_doc_2.txt"),
                str(Path(temp_project_dir) / "data" / "test_doc_3.txt")
            ]
        )
        
        # Setup composite retriever
        await engine.setup_composite_retriever()
        
        return engine
    
    @pytest.fixture
    async def safety_guardrails(self):
        """Create enhanced safety guardrails for testing"""
        return EnhancedSafetyGuardrails(safety_level=SafetyLevel.HIGH)
    
    @pytest.fixture
    async def a2a_handler(self):
        """Create A2A protocol handler for testing"""
        handler = A2AProtocolHandler(
            agent_id="test_agent",
            agent_type="test_type",
            port=8767  # Use different port for testing
        )
        return handler
    
    @pytest.fixture
    async def agui_handler(self):
        """Create AG-UI protocol handler for testing"""
        handler = AGUIProtocolHandler(port=8768)  # Use different port for testing
        return handler
    
    @pytest.mark.asyncio
    async def test_agentic_retrieval_basic_functionality(self, agentic_retrieval_engine):
        """Test basic agentic retrieval functionality"""
        vybe_retrieval = VybeAgenticRetrieval(agentic_retrieval_engine)
        
        # Test agent-specific retrieval
        results = await vybe_retrieval.agent_query(
            agent_id="vyba",
            query="Who created Python programming language?",
            context_type="factual"
        )
        
        assert len(results) > 0
        assert any("Guido van Rossum" in result.content for result in results)
        assert results[0].agent_id == "vyba"
        
        # Test different agent with different retrieval mode
        results = await vybe_retrieval.agent_query(
            agent_id="codex",
            query="What is machine learning?",
            context_type="technical"
        )
        
        assert len(results) > 0
        assert any("artificial intelligence" in result.content.lower() for result in results)
    
    @pytest.mark.asyncio
    async def test_safety_guardrails_comprehensive_validation(self, safety_guardrails):
        """Test comprehensive safety validation"""
        
        # Test safe content
        safe_content = "Python is a programming language that is widely used for web development."
        report = await safety_guardrails.validate_content(
            content=safe_content,
            agent_id="test_agent",
            content_type="educational"
        )
        
        assert report.overall_result.value in ["pass", "warning"]
        assert report.safety_score > 0.7
        
        # Test potentially unsafe content
        unsafe_content = "I think Python was definitely created by someone, but I'm not sure who. It might be John Doe."
        report = await safety_guardrails.validate_content(
            content=unsafe_content,
            agent_id="test_agent",
            content_type="educational"
        )
        
        # Should detect uncertainty/potential hallucination
        assert len(report.violations) > 0
        assert any("uncertainty" in violation.type.lower() for violation in report.violations)
    
    @pytest.mark.asyncio
    async def test_a2a_protocol_message_handling(self, a2a_handler):
        """Test A2A protocol message handling"""
        
        # Test capability registration
        from method.vybe.a2a_protocol import AgentCapability
        
        test_capability = AgentCapability(
            name="test_capability",
            description="Test capability for unit testing",
            input_schema={"query": "string"},
            output_schema={"result": "string"}
        )
        
        a2a_handler.register_capability(test_capability)
        assert "test_capability" in a2a_handler.capabilities
        
        # Test message creation
        test_message = A2AMessage(
            type=A2AMessageType.REQUEST,
            sender_id="test_sender",
            receiver_id="test_agent",
            payload={"test": "data"}
        )
        
        assert test_message.sender_id == "test_sender"
        assert test_message.type == A2AMessageType.REQUEST
        
        # Test message serialization
        message_dict = test_message.to_dict()
        assert "id" in message_dict
        assert message_dict["type"] == "request"
        
        # Test message deserialization
        reconstructed = A2AMessage.from_dict(message_dict)
        assert reconstructed.sender_id == test_message.sender_id
        assert reconstructed.type == test_message.type
    
    @pytest.mark.asyncio
    async def test_agui_protocol_message_handling(self, agui_handler):
        """Test AG-UI protocol message handling"""
        
        # Test AG-UI message creation
        test_message = AGUIMessage(
            type=AGUIMessageType.AGENT_STATUS,
            agent_id="test_agent",
            payload={"status": "active", "progress": 50}
        )
        
        assert test_message.agent_id == "test_agent"
        assert test_message.type == AGUIMessageType.AGENT_STATUS
        
        # Test message serialization
        message_dict = test_message.to_dict()
        assert "agent_id" in message_dict
        assert message_dict["type"] == "agent_status"
        
        # Test metrics
        metrics = agui_handler.get_metrics()
        assert "messages_sent" in metrics
        assert "active_connections" in metrics
    
    @pytest.mark.asyncio
    async def test_integrated_workflow_simulation(self, temp_project_dir, agentic_retrieval_engine, safety_guardrails):
        """Test integrated workflow with all protocols"""
        
        # Simulate a complete agent workflow
        vybe_retrieval = VybeAgenticRetrieval(agentic_retrieval_engine)
        
        # Step 1: Agent retrieves context
        context_results = await vybe_retrieval.agent_query(
            agent_id="vyba",
            query="What programming languages are mentioned in our knowledge base?",
            context_type="comprehensive"
        )
        
        assert len(context_results) > 0
        
        # Step 2: Agent generates content based on context
        generated_content = f"""
        Based on our knowledge base analysis, here are the key programming languages:
        
        1. Python - Created by Guido van Rossum in 1991
        2. JavaScript - Created by Brendan Eich in 1995
        
        These languages serve different purposes in modern development.
        """
        
        # Step 3: Safety validation of generated content
        safety_report = await safety_guardrails.validate_content(
            content=generated_content,
            agent_id="vyba",
            content_type="educational"
        )
        
        assert safety_report.overall_result.value in ["pass", "warning"]
        assert safety_report.safety_score > 0.8
        
        # Step 4: Verify metrics are updated
        retrieval_metrics = agentic_retrieval_engine.get_retrieval_metrics()
        assert retrieval_metrics["total_queries"] > 0
        assert retrieval_metrics["successful_retrievals"] > 0
        
        safety_metrics = safety_guardrails.get_safety_metrics()
        assert safety_metrics["total_validations"] > 0
        assert safety_metrics["pass_rate"] > 0
    
    @pytest.mark.asyncio
    async def test_error_handling_and_resilience(self, agentic_retrieval_engine, safety_guardrails):
        """Test error handling and system resilience"""
        
        vybe_retrieval = VybeAgenticRetrieval(agentic_retrieval_engine)
        
        # Test retrieval with invalid query
        results = await vybe_retrieval.agent_query(
            agent_id="invalid_agent",
            query="",
            context_type="invalid"
        )
        
        # Should handle gracefully
        assert isinstance(results, list)
        
        # Test safety validation with problematic content
        problematic_content = "This content contains eval() and exec() functions which are dangerous."
        report = await safety_guardrails.validate_content(
            content=problematic_content,
            agent_id="test_agent",
            content_type="code"
        )
        
        # Should detect security issues
        assert len(report.violations) > 0
        assert report.safety_score < 0.8
    
    @pytest.mark.asyncio
    async def test_performance_metrics_collection(self, agentic_retrieval_engine, safety_guardrails):
        """Test performance metrics collection across all protocols"""
        
        vybe_retrieval = VybeAgenticRetrieval(agentic_retrieval_engine)
        
        # Perform multiple operations to generate metrics
        for i in range(5):
            await vybe_retrieval.agent_query(
                agent_id=f"agent_{i % 3}",
                query=f"Test query {i}",
                context_type="test"
            )
            
            await safety_guardrails.validate_content(
                content=f"Test content {i}",
                agent_id=f"agent_{i % 3}",
                content_type="test"
            )
        
        # Check retrieval metrics
        retrieval_metrics = agentic_retrieval_engine.get_retrieval_metrics()
        assert retrieval_metrics["total_queries"] >= 5
        assert retrieval_metrics["cache_hit_rate"] >= 0
        assert "agent_queries" in retrieval_metrics
        
        # Check safety metrics
        safety_metrics = safety_guardrails.get_safety_metrics()
        assert safety_metrics["total_validations"] >= 5
        assert safety_metrics["avg_processing_time"] > 0
        assert "pass_rate" in safety_metrics
    
    def test_protocol_availability_check(self):
        """Test that all required protocols are available"""
        assert PROTOCOLS_AVAILABLE, "Enhanced protocols should be available for testing"
        
        # Verify all components can be imported
        from method.vybe.agentic_retrieval_engine import AgenticRetrievalEngine
        from method.vybe.a2a_protocol import A2AProtocolHandler
        from method.vybe.ag_ui_protocol import AGUIProtocolHandler
        from method.vybe.enhanced_safety_guardrails import EnhancedSafetyGuardrails
        
        assert AgenticRetrievalEngine is not None
        assert A2AProtocolHandler is not None
        assert AGUIProtocolHandler is not None
        assert EnhancedSafetyGuardrails is not None


# Performance benchmark tests
@pytest.mark.skipif(not PROTOCOLS_AVAILABLE, reason="Enhanced protocols not available")
class TestProtocolPerformance:
    """Performance tests for enhanced protocols"""
    
    @pytest.mark.asyncio
    async def test_retrieval_performance_benchmark(self, temp_project_dir):
        """Benchmark agentic retrieval performance"""
        engine = AgenticRetrievalEngine(
            project_root=temp_project_dir,
            project_name="BenchmarkTest"
        )
        
        vybe_retrieval = VybeAgenticRetrieval(engine)
        
        # Measure retrieval time
        start_time = datetime.now()
        
        for i in range(10):
            await vybe_retrieval.agent_query(
                agent_id="benchmark_agent",
                query=f"Benchmark query {i}",
                context_type="performance"
            )
        
        end_time = datetime.now()
        total_time = (end_time - start_time).total_seconds()
        avg_time_per_query = total_time / 10
        
        # Performance assertions
        assert avg_time_per_query < 2.0, f"Average query time {avg_time_per_query}s exceeds 2s threshold"
        
        metrics = engine.get_retrieval_metrics()
        assert metrics["total_queries"] == 10
    
    @pytest.mark.asyncio
    async def test_safety_validation_performance(self):
        """Benchmark safety validation performance"""
        guardrails = EnhancedSafetyGuardrails(safety_level=SafetyLevel.HIGH)
        
        test_content = "This is a test content for performance benchmarking of safety validation systems."
        
        # Measure validation time
        start_time = datetime.now()
        
        for i in range(10):
            await guardrails.validate_content(
                content=f"{test_content} Iteration {i}",
                agent_id="benchmark_agent",
                content_type="benchmark"
            )
        
        end_time = datetime.now()
        total_time = (end_time - start_time).total_seconds()
        avg_time_per_validation = total_time / 10
        
        # Performance assertions
        assert avg_time_per_validation < 1.0, f"Average validation time {avg_time_per_validation}s exceeds 1s threshold"
        
        metrics = guardrails.get_safety_metrics()
        assert metrics["total_validations"] == 10


if __name__ == "__main__":
    # Run tests if executed directly
    pytest.main([__file__, "-v"])
