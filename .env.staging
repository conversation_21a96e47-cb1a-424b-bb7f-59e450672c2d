# VybeCoding.ai Staging Environment Configuration
# This file contains staging-specific environment variables
# Actual secrets should be stored in GitHub Secrets

# Environment
VITE_ENVIRONMENT=staging
VITE_APP_NAME=VybeCoding.ai (Staging)
VITE_APP_VERSION=1.0.0-staging

# Appwrite Configuration (Staging)
VITE_APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
VITE_APPWRITE_PROJECT_ID=staging-project-id
VITE_APPWRITE_DATABASE_ID=staging-database-id

# API Configuration
VITE_API_BASE_URL=https://vybecoding-staging.vercel.app/api
VITE_API_TIMEOUT=30000

# Feature Flags (Staging)
VITE_ENABLE_DEBUG_MODE=true
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_ERROR_REPORTING=true
VITE_ENABLE_PERFORMANCE_MONITORING=true

# Security Settings (Staging)
VITE_ENABLE_CSP=true
VITE_ENABLE_HTTPS_ONLY=true
VITE_ENABLE_SECURE_COOKIES=true

# Logging Configuration
VITE_LOG_LEVEL=debug
VITE_ENABLE_CONSOLE_LOGS=true

# Development Tools (Staging)
VITE_ENABLE_DEVTOOLS=true
VITE_ENABLE_HOT_RELOAD=false
VITE_ENABLE_SOURCE_MAPS=true

# External Services (Staging)
VITE_ENABLE_THIRD_PARTY_SCRIPTS=false
VITE_GOOGLE_ANALYTICS_ID=
VITE_SENTRY_DSN=

# Build Configuration
VITE_BUILD_TARGET=staging
VITE_OPTIMIZE_BUNDLE=true
VITE_GENERATE_SOURCEMAP=true

# Cache Configuration
VITE_CACHE_STRATEGY=network-first
VITE_CACHE_TTL=300

# Deployment Information
VITE_DEPLOY_TIMESTAMP=
VITE_COMMIT_HASH=
VITE_BRANCH_NAME=develop
