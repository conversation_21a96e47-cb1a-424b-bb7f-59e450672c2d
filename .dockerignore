# VybeCoding.ai Docker Ignore File
# Excludes files and directories from Docker build context

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn

# Build outputs
build/
dist/
.svelte-kit/
.output/

# Environment files
.env
.env.*
!.env.template

# Development files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore
.gitattributes

# Documentation
README.md
CHANGELOG.md
docs/
*.md

# Testing
coverage/
.nyc_output/
test-results/
playwright-report/

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Docker files (avoid recursive Docker builds)
Dockerfile*
docker-compose*.yml
.dockerignore

# Scripts (not needed in container)
scripts/
.github/

# Method directories (development tools)
method/

# Story drafts (development documentation)
story-drafts/

# Monitoring configurations
monitoring/

# Nginx configurations
nginx/

# Redis configurations
redis/

# Backup files
*.bak
*.backup

# Archive files
*.zip
*.tar.gz
*.rar

# IDE files
*.sublime-project
*.sublime-workspace

# Vim files
*.vim
.vimrc

# Emacs files
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Local development certificates
*.pem
*.key
*.crt

# Local database files
*.sqlite
*.db

# Python files (if any)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# Java files (if any)
*.class
*.jar
*.war
*.ear

# Rust files (if any)
target/
Cargo.lock

# Go files (if any)
vendor/

# Ruby files (if any)
*.gem
*.rbc
/.config
/coverage/
/InstalledFiles
/pkg/
/spec/reports/
/spec/examples.txt
/test/tmp/
/test/version_tmp/
/tmp/

# PHP files (if any)
/vendor/
composer.phar
composer.lock

# Terraform files (if any)
*.tfstate
*.tfstate.*
.terraform/

# Kubernetes files (if any)
*.kubeconfig
