# VybeCoding.ai Production Dockerfile
# Multi-stage build for optimized SvelteKit application

# Build stage
FROM node:20-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production stage
FROM node:20-alpine AS runtime

# Install security updates and required packages
RUN apk update && apk upgrade && apk add --no-cache curl dumb-init

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S vybecoding -u 1001 -G nodejs

# Set working directory
WORKDIR /app

# Copy built application from builder stage
COPY --from=builder --chown=vybecoding:nodejs /app/build ./build
COPY --from=builder --chown=vybecoding:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=vybecoding:nodejs /app/package.json ./

# Create logs directory
RUN mkdir -p /app/logs && chown vybecoding:nodejs /app/logs

# Switch to non-root user
USER vybecoding

# Expose port
EXPOSE 3000

# Set environment variables
ENV NODE_ENV=production
ENV PORT=3000
ENV HOST=0.0.0.0

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:3000/api/health || exit 1

# Labels for metadata
LABEL maintainer="VybeCoding.ai Team"
LABEL version="1.0.0"
LABEL description="VybeCoding.ai - AI-powered education platform"

# Start the application with dumb-init for proper signal handling
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "build"]
