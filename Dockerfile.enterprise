# VybeCoding.ai Enterprise Dockerfile
# Phase 6: Production Scaling & Enterprise Deployment
# Multi-stage build for optimal performance and security

# Stage 1: Build environment
FROM node:20-alpine AS builder

# Set working directory
WORKDIR /app

# Install build dependencies
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git \
    curl

# Copy package files
COPY package*.json ./
COPY pnpm-lock.yaml* ./

# Install pnpm and dependencies
RUN npm install -g pnpm@latest
RUN pnpm install --frozen-lockfile --production=false

# Copy source code
COPY . .

# Build application with enterprise optimizations
ENV NODE_ENV=production
ENV VITE_ENVIRONMENT=enterprise
ENV VITE_BUILD_TARGET=enterprise

# Build the application
RUN pnpm run build:enterprise

# Optimize build output
RUN pnpm run optimize:enterprise || echo "Optimization step optional"

# Stage 2: Production runtime
FROM node:20-alpine AS runtime

# Install runtime dependencies and security updates
RUN apk add --no-cache \
    dumb-init \
    curl \
    ca-certificates \
    tzdata \
    && apk upgrade --no-cache \
    && rm -rf /var/cache/apk/*

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs \
    && adduser -S vybecoding -u 1001 -G nodejs

# Set working directory
WORKDIR /app

# Copy built application from builder stage
COPY --from=builder --chown=vybecoding:nodejs /app/dist ./dist
COPY --from=builder --chown=vybecoding:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=vybecoding:nodejs /app/package.json ./package.json

# Copy enterprise configuration
COPY --chown=vybecoding:nodejs ./enterprise/config ./config
COPY --chown=vybecoding:nodejs ./enterprise/scripts ./scripts

# Create necessary directories
RUN mkdir -p /app/logs /app/data /app/tmp \
    && chown -R vybecoding:nodejs /app/logs /app/data /app/tmp

# Set environment variables for enterprise deployment
ENV NODE_ENV=production
ENV VITE_ENVIRONMENT=enterprise
ENV PORT=3000
ENV HOST=0.0.0.0

# Enterprise-specific environment variables
ENV CLUSTER_MODE=true
ENV MAX_WORKERS=4
ENV MEMORY_LIMIT=4096
ENV CPU_LIMIT=4
ENV ENABLE_METRICS=true
ENV ENABLE_TRACING=true
ENV LOG_LEVEL=info
ENV CACHE_TTL=3600
ENV SESSION_TIMEOUT=1800

# Performance optimizations
ENV NODE_OPTIONS="--max-old-space-size=4096 --optimize-for-size"
ENV UV_THREADPOOL_SIZE=16

# Security configurations
ENV HELMET_ENABLED=true
ENV RATE_LIMIT_ENABLED=true
ENV CORS_ENABLED=true

# Health check configuration
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:3000/api/health || exit 1

# Expose port
EXPOSE 3000

# Switch to non-root user
USER vybecoding

# Use dumb-init for proper signal handling
ENTRYPOINT ["dumb-init", "--"]

# Start application with enterprise configuration
CMD ["node", "dist/server.js"]

# Labels for enterprise deployment
LABEL maintainer="VybeCoding.ai Enterprise Team"
LABEL version="2.0.0-enterprise"
LABEL description="VybeCoding.ai Enterprise Application"
LABEL org.opencontainers.image.title="VybeCoding.ai Enterprise"
LABEL org.opencontainers.image.description="Enterprise-grade AI-powered development platform"
LABEL org.opencontainers.image.vendor="VybeCoding.ai"
LABEL org.opencontainers.image.version="2.0.0-enterprise"
LABEL org.opencontainers.image.created="2025-01-27"
LABEL org.opencontainers.image.source="https://github.com/VybeCoding/VybeCoding.ai"
LABEL org.opencontainers.image.licenses="MIT"
