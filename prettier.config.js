// Prettier configuration for VybeCoding.ai platform
export default {
  // Basic formatting
  semi: true,
  singleQuote: true,
  quoteProps: 'as-needed',
  trailingComma: 'es5',
  tabWidth: 2,
  useTabs: false,
  printWidth: 80,
  endOfLine: 'lf',

  // Bracket formatting
  bracketSpacing: true,
  bracketSameLine: false,
  arrowParens: 'avoid',

  // Language-specific overrides
  overrides: [
    {
      files: '*.svelte',
      options: {
        parser: 'svelte',
        plugins: ['prettier-plugin-svelte'],
        svelteSortOrder: 'options-scripts-markup-styles',
        svelteStrictMode: false,
        svelteAllowShorthand: true,
        svelteIndentScriptAndStyle: true,
      },
    },
    {
      files: '*.md',
      options: {
        parser: 'markdown',
        proseWrap: 'preserve',
        printWidth: 100,
      },
    },
    {
      files: '*.json',
      options: {
        parser: 'json',
        printWidth: 120,
      },
    },
    {
      files: '*.yaml',
      options: {
        parser: 'yaml',
        printWidth: 120,
      },
    },
    {
      files: '*.yml',
      options: {
        parser: 'yaml',
        printWidth: 120,
      },
    },
  ],

  // Plugin configuration
  plugins: ['prettier-plugin-svelte'],
};
