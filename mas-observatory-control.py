#!/usr/bin/env python3
"""
🚀 MAS Observatory Control Interface
Revolutionary AI Agent Monitoring & Control for VybeCoding.ai

Usage:
  python mas-observatory-control.py start-observatory    # Start Observatory monitoring stack
  python mas-observatory-control.py stop-observatory     # Stop Observatory
  python mas-observatory-control.py status              # Check Observatory status
  python mas-observatory-control.py autonomous on       # Enable 24/7 autonomous mode
  python mas-observatory-control.py autonomous off      # Disable autonomous mode
  python mas-observatory-control.py feed-url <url>      # Feed URL for processing
  python mas-observatory-control.py feed-idea <idea>    # Feed content idea
  python mas-observatory-control.py dashboard           # Open Observatory dashboard
"""

import asyncio
import sys
import subprocess
import webbrowser
import requests
import json
from pathlib import Path
from datetime import datetime

# Observatory integration is optional for basic functionality
get_observatory_bridge = None


class MASObservatoryController:
    """
    🚀 MAS Observatory Controller
    
    Unified interface for controlling the revolutionary Observatory monitoring system
    """
    
    def __init__(self):
        self.observatory_url = "http://localhost:3001"
        self.api_url = "http://localhost:8003"
        self.observatory_dir = Path(__file__).parent / "mas-observatory"
    
    def start_observatory(self):
        """Start the Observatory monitoring stack"""
        print("🚀 Starting MAS Observatory...")
        
        if not self.observatory_dir.exists():
            print("❌ Observatory directory not found. Please ensure mas-observatory/ exists.")
            return False
        
        try:
            # Run the start script
            result = subprocess.run(
                ["./start-observatory.sh"],
                cwd=self.observatory_dir,
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                print("✅ Observatory started successfully!")
                print(f"🌐 Dashboard: {self.observatory_url}")
                print("📊 Login: admin / vybe_observatory")
                return True
            else:
                print(f"❌ Failed to start Observatory: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Error starting Observatory: {e}")
            return False
    
    def stop_observatory(self):
        """Stop the Observatory monitoring stack"""
        print("🛑 Stopping MAS Observatory...")
        
        try:
            result = subprocess.run(
                ["./stop-observatory.sh"],
                cwd=self.observatory_dir,
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                print("✅ Observatory stopped successfully!")
                return True
            else:
                print(f"❌ Failed to stop Observatory: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Error stopping Observatory: {e}")
            return False
    
    def check_status(self):
        """Check Observatory status"""
        print("📊 Checking MAS Observatory Status...")
        print("=" * 50)
        
        services = [
            ("Grafana Dashboard", "http://localhost:3001", "🎛️"),
            ("Prometheus Metrics", "http://localhost:9091", "📈"),
            ("Netdata Hardware", "http://localhost:19999", "⚡"),
            ("Jaeger Tracing", "http://localhost:16686", "🔍"),
            ("Kibana Logs", "http://localhost:5601", "📊"),
            ("MAS API", "http://localhost:8003", "🤖"),
            ("Portainer", "http://localhost:9001", "🐳")
        ]
        
        all_healthy = True
        
        for name, url, icon in services:
            try:
                response = requests.get(f"{url}/health" if "localhost:8001" in url else url, timeout=5)
                if response.status_code < 400:
                    print(f"{icon} {name}: ✅ HEALTHY")
                else:
                    print(f"{icon} {name}: ❌ ERROR ({response.status_code})")
                    all_healthy = False
            except requests.exceptions.RequestException:
                print(f"{icon} {name}: ❌ OFFLINE")
                all_healthy = False
        
        print()
        if all_healthy:
            print("🎉 All Observatory services are healthy!")
        else:
            print("⚠️  Some services are not responding. Check Docker containers.")
        
        # Check MAS integration
        try:
            response = requests.get(f"{self.api_url}/metrics/agents", timeout=5)
            if response.status_code == 200:
                agents = response.json()
                print(f"🤖 MAS Integration: ✅ ACTIVE ({len(agents)} agents)")
            else:
                print("🤖 MAS Integration: ❌ ERROR")
        except:
            print("🤖 MAS Integration: ❌ OFFLINE")
        
        return all_healthy
    
    def toggle_autonomous_mode(self, enabled: bool):
        """Toggle autonomous content generation mode"""
        mode_text = "ENABLED" if enabled else "DISABLED"
        print(f"🤖 Setting autonomous mode: {mode_text}")

        try:
            # Make API call to the web interface
            response = requests.post(
                'http://localhost:5173/api/autonomous/toggle',
                json={'enabled': enabled},
                timeout=10
            )

            if response.status_code == 200:
                if enabled:
                    print("✅ Autonomous mode ENABLED")
                    print("🚀 MAS will now generate content 24/7 automatically")
                    print("📊 Monitor progress at http://localhost:5173/mas")
                else:
                    print("✅ Autonomous mode DISABLED")
                    print("🛑 Autonomous content generation stopped")
                return True
            else:
                print(f"❌ Failed to toggle autonomous mode: HTTP {response.status_code}")
                return False

        except Exception as e:
            print(f"❌ Error toggling autonomous mode: {e}")
            print("💡 Make sure VybeCoding.ai dev server is running (npm run dev)")
            return False
    
    def feed_url(self, url: str):
        """Feed URL to MAS for processing"""
        print(f"📥 Feeding URL to MAS: {url}")

        try:
            response = requests.post(
                'http://localhost:5173/api/autonomous/feed-url',
                json={'url': url},
                timeout=10
            )

            if response.status_code == 200:
                print("✅ URL queued for processing")
                print("🎯 Will generate: News Article, Course, Vybe Qube")
                print("📊 Monitor progress at http://localhost:5173/mas")
                return True
            else:
                print(f"❌ Failed to queue URL: HTTP {response.status_code}")
                return False

        except Exception as e:
            print(f"❌ Error feeding URL: {e}")
            print("💡 Make sure VybeCoding.ai dev server is running (npm run dev)")
            return False

    def feed_idea(self, idea: str):
        """Feed content idea to MAS"""
        print(f"💡 Feeding idea to MAS: {idea}")

        try:
            # Use the content generation API with the idea as topic
            response = requests.post(
                'http://localhost:5173/api/content/generate',
                json={
                    'content_type': 'course',
                    'topic': idea,
                    'target_audience': 'developers',
                    'complexity_level': 'intermediate',
                    'requirements': {}
                },
                timeout=10
            )

            if response.status_code == 200:
                print("✅ Idea queued for processing")
                print("🎯 Will generate: Course, News Article")
                print("📊 Monitor progress at http://localhost:5173/mas")
                return True
            else:
                print(f"❌ Failed to queue idea: HTTP {response.status_code}")
                return False

        except Exception as e:
            print(f"❌ Error feeding idea: {e}")
            print("💡 Make sure VybeCoding.ai dev server is running (npm run dev)")
            return False
    
    def open_dashboard(self):
        """Open Observatory dashboard in browser"""
        print("🌐 Opening Observatory dashboard...")
        
        try:
            # Check if Grafana is accessible
            response = requests.get(self.observatory_url, timeout=5)
            if response.status_code < 400:
                webbrowser.open(self.observatory_url)
                print(f"✅ Dashboard opened: {self.observatory_url}")
                print("📊 Login: admin / vybe_observatory")
                return True
            else:
                print("❌ Observatory dashboard is not accessible")
                print("💡 Try: python mas-observatory-control.py start-observatory")
                return False
                
        except requests.exceptions.RequestException:
            print("❌ Observatory is not running")
            print("💡 Try: python mas-observatory-control.py start-observatory")
            return False
    
    def show_help(self):
        """Show help information"""
        print(__doc__)


async def main():
    """Main command handler"""
    controller = MASObservatoryController()
    
    if len(sys.argv) < 2:
        controller.show_help()
        return
    
    command = sys.argv[1].lower()
    
    if command == "start-observatory":
        controller.start_observatory()
        
    elif command == "stop-observatory":
        controller.stop_observatory()
        
    elif command == "status":
        controller.check_status()
        
    elif command == "autonomous":
        if len(sys.argv) < 3:
            print("Usage: python mas-observatory-control.py autonomous [on|off]")
            return
        
        mode = sys.argv[2].lower()
        if mode == "on":
            controller.toggle_autonomous_mode(True)
        elif mode == "off":
            controller.toggle_autonomous_mode(False)
        else:
            print("Invalid mode. Use 'on' or 'off'")
            
    elif command == "feed-url":
        if len(sys.argv) < 3:
            print("Usage: python mas-observatory-control.py feed-url <url>")
            return
        
        url = sys.argv[2]
        controller.feed_url(url)
        
    elif command == "feed-idea":
        if len(sys.argv) < 3:
            print("Usage: python mas-observatory-control.py feed-idea '<idea>'")
            return
        
        idea = ' '.join(sys.argv[2:])
        controller.feed_idea(idea)
        
    elif command == "dashboard":
        controller.open_dashboard()
        
    elif command == "help" or command == "--help" or command == "-h":
        controller.show_help()
        
    else:
        print(f"Unknown command: {command}")
        controller.show_help()


if __name__ == "__main__":
    asyncio.run(main())
