#!/bin/bash
# Fix common linting issues in VybeCoding.ai

echo "🔧 Fixing common linting issues..."

# Fix unused variables by prefixing with underscore
echo "📝 Fixing unused variables..."

# Replace common unused parameter patterns
find src/ -name "*.ts" -type f -exec sed -i 's/function \([^(]*\)(\([^)]*\)error\([^)]*\))/function \1(\2_error\3)/g' {} \;
find src/ -name "*.ts" -type f -exec sed -i 's/(\([^)]*\)payload\([^)]*\))/(\1_payload\2)/g' {} \;
find src/ -name "*.ts" -type f -exec sed -i 's/(\([^)]*\)input\([^)]*\))/(\1_input\2)/g' {} \;
find src/ -name "*.ts" -type f -exec sed -i 's/(\([^)]*\)code\([^)]*\))/(\1_code\2)/g' {} \;
find src/ -name "*.ts" -type f -exec sed -i 's/(\([^)]*\)module\([^)]*\))/(\1_module\2)/g' {} \;
find src/ -name "*.ts" -type f -exec sed -i 's/(\([^)]*\)exercise\([^)]*\))/(\1_exercise\2)/g' {} \;
find src/ -name "*.ts" -type f -exec sed -i 's/(\([^)]*\)userId\([^)]*\))/(\1_userId\2)/g' {} \;
find src/ -name "*.ts" -type f -exec sed -i 's/(\([^)]*\)progress\([^)]*\))/(\1_progress\2)/g' {} \;
find src/ -name "*.ts" -type f -exec sed -i 's/(\([^)]*\)url\([^)]*\))/(\1_url\2)/g' {} \;

# Fix unused imports by commenting them out
echo "📦 Fixing unused imports..."

# Remove unused single imports
sed -i 's/import { Calendar } from/#import { Calendar } from/g' src/lib/components/educational/CourseProgress.svelte
sed -i 's/import { onMount } from/#import { onMount } from/g' src/lib/components/workspace/OutputPanel.svelte

# Fix variable declarations
echo "🔧 Fixing variable declarations..."

# Comment out unused variable declarations
find src/ -name "*.svelte" -type f -exec sed -i 's/let mounted = false;/\/\/ let mounted = false;/g' {} \;
find src/ -name "*.svelte" -type f -exec sed -i 's/const dispatch = createEventDispatcher();/\/\/ const dispatch = createEventDispatcher();/g' {} \;

echo "✅ Basic fixes applied!"
echo "🔍 Run 'npm run lint' to check for remaining issues"
