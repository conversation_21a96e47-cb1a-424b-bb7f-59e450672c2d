#!/bin/bash
# VybeCoding.ai Python Environment Activation Script

if [ -d ".venv" ]; then
    source .venv/bin/activate
    echo "✅ VybeCoding.ai Python environment activated"
    echo "📦 Python: $(python --version)"
    echo "📍 Virtual env: $(which python)"
    echo ""
    echo "🎯 Quick commands:"
    echo "  deactivate     - Exit virtual environment"
    echo "  pip list       - Show installed packages"
    echo "  pip install -r requirements.txt  - Install dependencies"
else
    echo "❌ Virtual environment not found. Run ./scripts/setup-python-env.sh first"
    exit 1
fi
