{"numTotalTestSuites": 1, "numPassedTestSuites": 0, "numFailedTestSuites": 1, "numPendingTestSuites": 0, "numTotalTests": 0, "numPassedTests": 0, "numFailedTests": 0, "numPendingTests": 0, "numTodoTests": 0, "snapshot": {"added": 0, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0, "didUpdate": false}, "startTime": 1749214303290, "success": false, "testResults": [{"assertionResults": [], "startTime": 1749214303290, "endTime": 1749214303290, "status": "failed", "message": "Failed to resolve entry for package \"monaco-editor\". The package may have incorrect main/module/exports specified in its package.json.", "name": "/home/<USER>/Projects/vybecoding/tests/utils/validation.test.ts"}]}