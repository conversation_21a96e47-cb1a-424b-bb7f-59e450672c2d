# 🚀 VYBECODING.AI - QUICK SETUP GUIDE

## 📋 **Prerequisites**

- Node.js 20+ and npm
- Python 3.8+ and pip
- Git

## ⚡ **1-Minute Setup**

### **Step 1: Clone Repository**

```bash
git clone https://github.com/Hiram-Ducky/VybeCoding.ai.git
cd VybeCoding.ai
```

### **Step 2: Install Dependencies**

```bash
# Install Python dependencies
pip install -r requirements.txt

# Install Node.js dependencies
npm install
```

### **Step 3: Configure Environment**

```bash
# Copy environment template
cp .env.template .env

# Edit .env with your configuration
```

### **Step 4: Start Development**

```bash
# Start development server
npm run dev

# Or use the VS Code task: Ctrl+Shift+P -> "Tasks: Run Task" -> "dev"
```

## 🎯 **Advanced Features**

### **Milestone Management**

```bash
# Create milestone (VS Code Command Palette)
Ctrl+Shift+P -> "Tasks: Run Task" -> "Create Milestone"

# List milestones
./scripts/auto-milestone.sh list

# Rollback to milestone
./scripts/auto-milestone.sh rollback <milestone-number>
```

### **Python Environment**

```bash
# Activate Python environment (if using venv)
source .venv/bin/activate  # Linux/Mac
# or
.venv\Scripts\activate     # Windows

# Activate using provided script
./activate-env.sh
```

### **Testing**

```bash
# Run all tests
npm test

# Run Python tests
pytest vybe-agent/tests/

# Run linting
npm run lint
```

## 🔧 **Development Workflow**

### **Branch Strategy**

- `main` - Production-ready code
- `milestone-*-feature` - Feature development
- `milestone-*-story` - Story implementation

### **VS Code Integration**

This project is optimized for VS Code with:

- ✅ Custom milestone management tasks
- ✅ Python environment auto-detection
- ✅ GitHub Copilot context optimization
- ✅ Automated formatting and linting

### **GitHub Actions**

6 automated workflows handle:

- ✅ Milestone validation
- ✅ Security scanning
- ✅ Staging deployment
- ✅ Production deployment

## 📁 **Project Structure**

```
├── src/                 # SvelteKit application
├── vybe-agent/         # Python AI agent
├── .github/workflows/  # CI/CD automation
├── scripts/           # Development tools
├── docs/              # Documentation
├── .vscode/           # VS Code configuration
└── method/            # BMAD methodology
```

## 🆘 **Troubleshooting**

### **Common Issues**

1. **Node modules not found**: Run `npm install`
2. **Python import errors**: Run `pip install -r requirements.txt`
3. **Environment variables**: Copy `.env.template` to `.env`
4. **Permission errors**: Ensure scripts are executable: `chmod +x scripts/*`

### **Reset Environment**

```bash
# Clean install
rm -rf node_modules .venv
npm install
pip install -r requirements.txt
```

## 🎉 **Ready to Code!**

Once setup is complete, you'll have access to:

- Advanced milestone management system
- AI-powered development workflow
- Professional CI/CD pipeline
- Enterprise-grade documentation

**Happy coding with VybeCoding.ai!** 🚀
