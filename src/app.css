@tailwind base;
@tailwind components;
@tailwind utilities;

/* ===== ACCESSIBILITY UTILITIES ===== */
/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Show screen reader content when focused */
.sr-only:focus,
.focus\:not-sr-only:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* Skip link styles */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #000;
  color: #fff;
  padding: 8px;
  text-decoration: none;
  z-index: 1000;
  border-radius: 4px;
}

.skip-link:focus {
  top: 6px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  * {
    border-color: currentColor !important;
  }

  button,
  input,
  select,
  textarea {
    border: 2px solid currentColor !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Focus indicators */
*:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Ensure sufficient color contrast */
.text-muted {
  color: #6b7280;
}

.dark .text-muted {
  color: #9ca3af;
}

@layer base {
  :root {
    /* 🚀 REVOLUTIONARY AI-POWERED DESIGN SYSTEM 2025 */
    /* Light Mode: Ultra-Modern AI Interface */
    --background: 0 0% 99%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    /* AI-Powered Primary Colors */
    --primary: 217 91% 60%; /* Electric Blue */
    --primary-foreground: 0 0% 98%;
    --secondary: 270 95% 75%; /* Neural Purple */
    --secondary-foreground: 0 0% 98%;
    --accent: 180 100% 50%; /* Quantum Cyan */
    --accent-foreground: 0 0% 98%;

    /* AI Interface Colors */
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 217 91% 60%;
    --radius: 1rem;

    /* AI-Specific Variables */
    --ai-glow: 217 91% 60%;
    --neural-purple: 270 95% 75%;
    --quantum-cyan: 180 100% 50%;
    --matrix-green: 120 100% 50%;
    --plasma-pink: 320 100% 70%;
    --energy-orange: 30 100% 60%;

    /* Advanced Gradients */
    --gradient-ai: linear-gradient(
      135deg,
      hsl(217 91% 60%),
      hsl(270 95% 75%),
      hsl(180 100% 50%)
    );
    --gradient-neural: linear-gradient(
      45deg,
      hsl(270 95% 75%),
      hsl(320 100% 70%)
    );
    --gradient-quantum: linear-gradient(
      90deg,
      hsl(180 100% 50%),
      hsl(217 91% 60%)
    );
    --gradient-matrix: linear-gradient(
      180deg,
      hsl(120 100% 50%),
      hsl(180 100% 50%)
    );

    /* Animation Variables */
    --animation-speed-fast: 0.2s;
    --animation-speed-normal: 0.3s;
    --animation-speed-slow: 0.5s;
    --animation-curve: cubic-bezier(0.4, 0, 0.2, 1);
  }

  .dark {
    /* 🌌 DARK MODE: CYBERPUNK AI INTERFACE */
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;

    /* Enhanced AI Colors for Dark Mode */
    --primary: 217 91% 70%; /* Brighter Electric Blue */
    --primary-foreground: 240 10% 3.9%;
    --secondary: 270 95% 85%; /* Brighter Neural Purple */
    --secondary-foreground: 240 10% 3.9%;
    --accent: 180 100% 60%; /* Brighter Quantum Cyan */
    --accent-foreground: 240 10% 3.9%;

    /* Dark Interface Colors */
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 217 91% 70%;

    /* Enhanced AI Glow Effects for Dark Mode */
    --ai-glow: 217 91% 70%;
    --neural-purple: 270 95% 85%;
    --quantum-cyan: 180 100% 60%;
    --matrix-green: 120 100% 60%;
    --plasma-pink: 320 100% 80%;
    --energy-orange: 30 100% 70%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  /* 🚀 REVOLUTIONARY AI-POWERED COMPONENT SYSTEM 2025 */

  /* ⚡ QUANTUM GLASSMORPHISM - Next-Gen AI Interface */
  .glass-card {
    @apply backdrop-blur-2xl bg-white/5 border border-white/10 rounded-3xl shadow-2xl;
    @apply hover:bg-white/15 transition-all duration-300;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.05) 50%,
      rgba(255, 255, 255, 0.1) 100%
    );
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.3),
      0 0 0 1px rgba(255, 255, 255, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
  }

  .glass-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.1),
      transparent
    );
    transition: left 0.5s;
  }

  .glass-card:hover::before {
    left: 100%;
  }

  /* Light Mode Quantum Glass */
  html:not(.dark) .glass-card {
    @apply backdrop-blur-2xl bg-white/70 border border-gray-200/30 rounded-3xl;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.9) 0%,
      rgba(255, 255, 255, 0.7) 50%,
      rgba(255, 255, 255, 0.9) 100%
    );
    box-shadow:
      0 8px 32px rgba(31, 38, 135, 0.1),
      0 0 0 1px rgba(255, 255, 255, 0.8),
      inset 0 1px 0 rgba(255, 255, 255, 1);
    @apply hover:bg-white/85 hover:shadow-xl transition-all duration-300;
  }

  html:not(.dark) .glass-card::before {
    background: linear-gradient(
      90deg,
      transparent,
      rgba(59, 130, 246, 0.1),
      transparent
    );
  }

  /* Maya's Enhanced Neomorphism Elements - Dual Theme Implementation */

  /* Default (Dark Mode) Neomorphism - Preserve Original */
  .neo-card {
    @apply bg-gradient-to-br from-gray-800 to-gray-900 rounded-3xl;
    box-shadow:
      20px 20px 60px #1a1a1a,
      -20px -20px 60px #2a2a2a;
    @apply transition-all duration-300 hover:scale-105;
  }

  /* Light Mode Specific Neomorphism */
  html:not(.dark) .neo-card {
    @apply bg-gradient-to-br from-gray-50 to-gray-100;
    box-shadow:
      12px 12px 24px rgba(163, 177, 198, 0.6),
      -12px -12px 24px rgba(255, 255, 255, 0.8);
  }

  /* 🔥 NEURAL BUTTON SYSTEM - AI-Powered Interactions */

  /* Quantum Glass Buttons */
  .btn-glass {
    @apply backdrop-blur-2xl bg-white/5 border-2 border-white/20 text-white px-8 py-4 rounded-2xl font-bold;
    @apply transition-all duration-300 hover:scale-105 hover:bg-white/15 hover:shadow-2xl;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.1),
      rgba(255, 255, 255, 0.05)
    );
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.3),
      0 0 0 1px rgba(255, 255, 255, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
  }

  .btn-glass::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    transition: left 0.3s;
  }

  .btn-glass:hover::before {
    left: 100%;
  }

  /* Light Mode Quantum Buttons */
  html:not(.dark) .btn-glass {
    @apply backdrop-blur-2xl bg-white/80 border-2 border-gray-200/50 text-gray-800;
    @apply transition-all duration-300 hover:scale-105 hover:bg-white/95 hover:shadow-xl hover:text-gray-900;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.9),
      rgba(255, 255, 255, 0.7)
    );
    box-shadow:
      0 4px 16px rgba(31, 38, 135, 0.15),
      0 0 0 1px rgba(255, 255, 255, 0.8),
      inset 0 1px 0 rgba(255, 255, 255, 1);
  }

  html:not(.dark) .btn-glass::before {
    background: linear-gradient(
      90deg,
      transparent,
      rgba(59, 130, 246, 0.2),
      transparent
    );
  }

  /* AI-Powered Gradient Buttons */
  .btn-gradient {
    @apply text-white px-8 py-4 rounded-2xl font-bold;
    @apply transition-all duration-300 hover:scale-105 hover:shadow-2xl;
    @apply relative overflow-hidden;
    background: var(--gradient-ai);
    box-shadow:
      0 8px 32px rgba(59, 130, 246, 0.3),
      0 0 0 1px rgba(255, 255, 255, 0.1);
  }

  .btn-gradient::before {
    content: '';
    @apply absolute inset-0 opacity-0;
    @apply transition-opacity duration-300;
    background: linear-gradient(
      135deg,
      hsl(var(--quantum-cyan)) 0%,
      hsl(var(--neural-purple)) 50%,
      hsl(var(--plasma-pink)) 100%
    );
  }

  .btn-gradient:hover::before {
    @apply opacity-100;
  }

  .btn-gradient:hover {
    box-shadow:
      0 12px 40px rgba(59, 130, 246, 0.4),
      0 0 0 1px rgba(255, 255, 255, 0.2),
      0 0 20px rgba(180, 100, 255, 0.3);
  }

  /* Light Mode Gradient Button Fixes */
  html:not(.dark) .btn-gradient {
    @apply text-white; /* Ensure text stays white in light mode */
  }

  html:not(.dark) .btn-gradient:hover {
    @apply text-white; /* Ensure text stays white on hover in light mode */
  }

  /* Container System */
  .container-glass {
    @apply container mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* Badge System */
  .badge {
    @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium;
    @apply bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300;
    @apply border border-blue-200 dark:border-blue-700/50;
  }

  .badge-outline {
    @apply bg-transparent border-2;
    @apply border-gray-300 text-gray-700 dark:border-gray-600 dark:text-gray-300;
    @apply hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors;
  }

  .badge-destructive {
    @apply bg-red-100 text-red-800 border-red-200;
    @apply dark:bg-red-900/30 dark:text-red-300 dark:border-red-700/50;
  }

  /* Card Neo System */
  .card-neo {
    @apply bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg;
    @apply border border-gray-200 dark:border-gray-700;
    @apply hover:shadow-xl transition-all duration-300 hover:scale-[1.02];
    @apply backdrop-blur-sm;
  }

  /* Light mode specific card-neo */
  html:not(.dark) .card-neo {
    @apply bg-white/90 border-gray-200/60;
    box-shadow:
      0 8px 32px rgba(31, 38, 135, 0.12),
      0 2px 16px rgba(31, 38, 135, 0.08);
  }

  html:not(.dark) .card-neo:hover {
    @apply bg-white/95;
    box-shadow:
      0 12px 40px rgba(31, 38, 135, 0.15),
      0 4px 20px rgba(31, 38, 135, 0.1);
  }

  /* Grid System */
  .grid-auto {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8;
  }

  .grid-auto-4 {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8;
  }

  /* Community Page Light Mode Hover Fixes */
  html:not(.dark) .glass-card:hover {
    @apply text-gray-900; /* Ensure text is visible on hover in light mode */
  }

  html:not(.dark) .glass-card:hover * {
    @apply text-gray-900; /* Ensure all child text is visible on hover in light mode */
  }

  html:not(.dark) .glass-card:hover .text-white\/90 {
    @apply text-gray-800; /* Fix specific white text classes */
  }

  /* Specific fix for community hero text */
  .community-hero-text {
    @apply text-gray-800 dark:text-white/90;
  }

  html:not(.dark) .glass-card:hover .community-hero-text {
    @apply text-gray-900 !important; /* Force text to stay visible on hover */
  }

  /* Maya's Enhanced Card System - Dual Theme Implementation */

  /* Default (Dark Mode) Cards - Preserve Original Beauty */
  .card {
    @apply backdrop-blur-xl bg-white/10 border border-white/20 rounded-3xl p-6;
    @apply hover:bg-white/20 transition-all duration-500 hover:scale-105 hover:shadow-2xl;
  }

  /* Light Mode Specific Cards */
  html:not(.dark) .card {
    @apply backdrop-blur-xl bg-white/85 border border-gray-200/50 rounded-3xl p-6;
    @apply hover:bg-white/95 transition-all duration-500 hover:scale-105;
    box-shadow:
      0 8px 32px rgba(31, 38, 135, 0.12),
      0 2px 16px rgba(31, 38, 135, 0.08);
  }

  /* BMAD Method - Homepage Light Mode Fixes */

  /* Light Mode: Proven Results at Scale Section Background */
  html:not(.dark) .stats-section-bg {
    @apply bg-gradient-to-br from-blue-50/90 via-purple-50/60 to-cyan-50/80;
  }

  html:not(.dark) .stats-section-bg .stats-radial-1 {
    background: radial-gradient(
      circle at 30% 20%,
      rgba(59, 130, 246, 0.15),
      transparent 50%
    );
  }

  /* Light Mode: Stats Cards */
  html:not(.dark) .stats-card-bg {
    @apply bg-white/90 border-gray-200/60 backdrop-blur-sm;
  }

  html:not(.dark) .stats-card-inner {
    @apply bg-white;
  }

  html:not(.dark) .stats-card-label {
    @apply text-gray-800;
  }

  html:not(.dark) .stats-card-description {
    @apply text-gray-600;
  }

  html:not(.dark) .stats-card-trend {
    @apply bg-emerald-100 text-emerald-700;
  }

  /* Light Mode: Features Section Background */
  html:not(.dark) .features-section-bg {
    @apply bg-gradient-to-br from-gray-50 via-blue-50/50 to-gray-50;
  }

  html:not(.dark) .features-section-bg .features-radial-1 {
    background: radial-gradient(
      circle at 70% 80%,
      rgba(59, 130, 246, 0.08),
      transparent 50%
    );
  }

  html:not(.dark) .features-section-bg .features-radial-2 {
    background: radial-gradient(
      circle at 20% 30%,
      rgba(236, 72, 153, 0.06),
      transparent 50%
    );
  }

  /* Light Mode: Feature Cards */
  html:not(.dark) .feature-card-bg {
    @apply bg-white/90 border-gray-200/60 backdrop-blur-sm;
  }

  html:not(.dark) .feature-card-inner {
    @apply bg-white;
  }

  html:not(.dark) .feature-card-title {
    @apply text-gray-800;
  }

  html:not(.dark) .feature-card-description {
    @apply text-gray-600;
  }

  html:not(.dark) .feature-card-badge {
    @apply text-cyan-400;
  }

  /* Light Mode: Description Text */
  html:not(.dark) .section-description {
    @apply text-gray-600;
  }
}

@layer utilities {
  /* 🌟 REVOLUTIONARY AI-POWERED UTILITIES 2025 */

  /* 🎨 QUANTUM GRADIENT TEXT SYSTEM */
  h1.text-gradient,
  .gradient-text-hero,
  .gradient-vybe-qube,
  .gradient-vybecoding,
  .gradient-vybe-method,
  .showcase-title {
    font-weight: 800;
    display: inline-block;
    position: relative;
  }

  /* AI-Powered Gradient Text Effects */
  .text-gradient,
  .gradient-text-hero,
  .gradient-vybe-qube,
  .gradient-vybecoding,
  .gradient-vybe-method {
    background: linear-gradient(
      135deg,
      hsl(var(--primary)) 0%,
      hsl(var(--secondary)) 25%,
      hsl(var(--accent)) 50%,
      hsl(var(--neural-purple)) 75%,
      hsl(var(--primary)) 100%
    );
    background-size: 400% 400%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
    animation: quantumShift 8s ease-in-out infinite;
    position: relative;
  }

  .text-gradient::after,
  .gradient-text-hero::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      hsl(var(--quantum-cyan) / 0.3) 0%,
      hsl(var(--plasma-pink) / 0.3) 50%,
      hsl(var(--matrix-green) / 0.3) 100%
    );
    filter: blur(20px);
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .text-gradient:hover::after,
  .gradient-text-hero:hover::after {
    opacity: 1;
  }

  /* Special case for Vybe Qube Showcase title */
  .showcase-title {
    color: white;
  }

  /* Responsive styles - Let Tailwind handle font sizes */
  @media (min-width: 768px) {
    h1.text-gradient,
    .gradient-text-hero,
    .gradient-vybe-qube,
    .gradient-vybecoding,
    .gradient-vybe-method,
    .showcase-title {
      /* Remove fixed font sizes to allow Tailwind responsive classes to work */
      line-height: 1;
    }
  }

  /* Special case for Vybe Qube Showcase title */
  .showcase-title {
    color: white;
  }

  /* 🌊 QUANTUM ANIMATION KEYFRAMES */
  @keyframes quantumShift {
    0% {
      background-position: 0% 50%;
      filter: hue-rotate(0deg);
    }
    25% {
      background-position: 100% 50%;
      filter: hue-rotate(90deg);
    }
    50% {
      background-position: 100% 100%;
      filter: hue-rotate(180deg);
    }
    75% {
      background-position: 0% 100%;
      filter: hue-rotate(270deg);
    }
    100% {
      background-position: 0% 50%;
      filter: hue-rotate(360deg);
    }
  }

  @keyframes neuralPulse {
    0%,
    100% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.05);
      opacity: 0.8;
    }
  }

  @keyframes matrixGlow {
    0%,
    100% {
      box-shadow: 0 0 5px hsl(var(--matrix-green) / 0.5);
    }
    50% {
      box-shadow:
        0 0 20px hsl(var(--matrix-green) / 0.8),
        0 0 30px hsl(var(--matrix-green) / 0.6);
    }
  }

  @keyframes quantumFloat {
    0%,
    100% {
      transform: translateY(0px) rotate(0deg);
    }
    33% {
      transform: translateY(-10px) rotate(120deg);
    }
    66% {
      transform: translateY(5px) rotate(240deg);
    }
  }

  /* AI-Powered Animation Classes */
  .animate-quantum-shift {
    animation: quantumShift 8s ease-in-out infinite;
  }

  .animate-neural-pulse {
    animation: neuralPulse 2s ease-in-out infinite;
  }

  .animate-matrix-glow {
    animation: matrixGlow 3s ease-in-out infinite;
  }

  .animate-quantum-float {
    animation: quantumFloat 6s ease-in-out infinite;
  }

  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.6s ease-out;
  }

  /* Legacy input class for backward compatibility */
  .input {
    @apply flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes gradientFlow {
    0% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  /* Prevent unwanted ripple effects and double-click zoom */
  * {
    -webkit-tap-highlight-color: transparent !important;
    -webkit-touch-callout: none;
    -webkit-user-drag: none;
    -webkit-user-select: none;
    touch-action: manipulation;
    -ms-touch-action: manipulation;
  }

  /* Specifically target elements that might cause ripples */
  div,
  button,
  a,
  span,
  img {
    -webkit-tap-highlight-color: transparent !important;
    -webkit-touch-callout: none !important;
  }

  /* Allow text selection where needed */
  input,
  textarea,
  [contenteditable],
  p,
  span,
  div.prose,
  .selectable {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
  }

  /* Disable double-click zoom on mobile */
  html {
    touch-action: manipulation;
  }

  /* BMAD Method - Homepage Light Mode Fixes */

  /* Light Mode: Proven Results at Scale Section Background */
  html:not(.dark) .stats-section-bg {
    @apply bg-gradient-to-br from-blue-50/90 via-purple-50/60 to-cyan-50/80;
  }

  html:not(.dark) .stats-section-bg .stats-radial-1 {
    background: radial-gradient(
      circle at 30% 20%,
      rgba(59, 130, 246, 0.15),
      transparent 50%
    );
  }

  /* Light Mode: Stats Cards */
  html:not(.dark) .stats-card-bg {
    @apply bg-white/90 border-gray-200/60 backdrop-blur-sm;
  }

  html:not(.dark) .stats-card-inner {
    @apply bg-white;
  }

  html:not(.dark) .stats-card-label {
    @apply text-gray-800;
  }

  html:not(.dark) .stats-card-description {
    @apply text-gray-600;
  }

  html:not(.dark) .stats-card-trend {
    @apply bg-emerald-100 text-emerald-700;
  }

  /* Light Mode: Features Section Background */
  html:not(.dark) .features-section-bg {
    @apply bg-gradient-to-br from-gray-50 via-blue-50/50 to-gray-50;
  }

  html:not(.dark) .features-section-bg .features-radial-1 {
    background: radial-gradient(
      circle at 70% 80%,
      rgba(59, 130, 246, 0.08),
      transparent 50%
    );
  }

  html:not(.dark) .features-section-bg .features-radial-2 {
    background: radial-gradient(
      circle at 20% 30%,
      rgba(236, 72, 153, 0.06),
      transparent 50%
    );
  }

  /* Light Mode: Feature Cards */
  html:not(.dark) .feature-card-bg {
    @apply bg-white/90 border-gray-200/60 backdrop-blur-sm;
  }

  html:not(.dark) .feature-card-inner {
    @apply bg-white;
  }

  html:not(.dark) .feature-card-title {
    @apply text-gray-800;
  }

  html:not(.dark) .feature-card-description {
    @apply text-gray-600;
  }

  html:not(.dark) .feature-card-badge {
    @apply text-cyan-400;
  }

  /* Light Mode: Description Text */
  html:not(.dark) .section-description {
    @apply text-gray-600;
  }

  /* 🌟 ADVANCED ACCESSIBILITY FEATURES - KAREN'S DESIGN ARCHITECT IMPLEMENTATION */

  /* Enhanced focus indicators for accessibility */
  .enhanced-focus *:focus {
    outline: 3px solid hsl(var(--primary)) !important;
    outline-offset: 2px !important;
    box-shadow:
      0 0 0 1px hsl(var(--background)),
      0 0 0 4px hsl(var(--primary) / 0.3) !important;
  }

  /* Color blindness simulation filters */
  .color-blind-protanopia {
    filter: url('#protanopia-filter');
  }

  .color-blind-deuteranopia {
    filter: url('#deuteranopia-filter');
  }

  .color-blind-tritanopia {
    filter: url('#tritanopia-filter');
  }

  /* Screen reader optimizations */
  .screen-reader-mode {
    /* Enhance text readability for screen readers */
  }

  .screen-reader-mode * {
    /* Ensure all text is properly exposed to assistive technology */
    speak: normal;
  }

  /* Skip links for keyboard navigation */
  .skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1000;
    transition: top 0.3s;
  }

  .skip-link:focus {
    top: 6px;
  }

  /* Enhanced keyboard navigation indicators */
  .keyboard-navigation *:focus-visible {
    outline: 3px solid hsl(var(--primary)) !important;
    outline-offset: 2px !important;
    box-shadow:
      0 0 0 1px hsl(var(--background)),
      0 0 0 5px hsl(var(--primary) / 0.4),
      0 0 10px hsl(var(--primary) / 0.3) !important;
    border-radius: 4px;
  }

  /* High contrast mode enhancements */
  .high-contrast {
    --background: 0 0% 100%;
    --foreground: 0 0% 0%;
    --muted: 0 0% 90%;
    --muted-foreground: 0 0% 20%;
    --border: 0 0% 80%;
    --primary: 220 100% 50%;
    --primary-foreground: 0 0% 100%;
  }

  .high-contrast.dark {
    --background: 0 0% 0%;
    --foreground: 0 0% 100%;
    --muted: 0 0% 10%;
    --muted-foreground: 0 0% 80%;
    --border: 0 0% 20%;
    --primary: 220 100% 70%;
    --primary-foreground: 0 0% 0%;
  }

  /* Large text mode */
  .large-text {
    font-size: 120% !important;
  }

  .large-text * {
    line-height: 1.6 !important;
  }

  /* Reduced motion preferences */
  .reduced-motion * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}
