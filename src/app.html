<!doctype html>
<html lang="en" %sveltekit.theme%>
  <head>
    <meta charset="utf-8" />
    <link
      rel="icon"
      href="%sveltekit.assets%/favicon.svg"
      type="image/svg+xml"
    />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta
      name="description"
      content="VybeCoding.ai - Learn the Vybe Method for building profitable AI applications"
    />
    <meta
      name="keywords"
      content="AI education, coding, Vybe Method, SvelteKit, programming"
    />
    <meta name="author" content="VybeCoding.ai" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://vybecoding.ai/" />
    <meta
      property="og:title"
      content="VybeCoding.ai - AI-Powered Education Platform"
    />
    <meta
      property="og:description"
      content="Learn the Vybe Method for building profitable AI applications with hands-on tutorials and live proof of concept demonstrations."
    />
    <meta property="og:image" content="%sveltekit.assets%/og-image.png" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://vybecoding.ai/" />
    <meta
      property="twitter:title"
      content="VybeCoding.ai - AI-Powered Education Platform"
    />
    <meta
      property="twitter:description"
      content="Learn the Vybe Method for building profitable AI applications with hands-on tutorials and live proof of concept demonstrations."
    />
    <meta property="twitter:image" content="%sveltekit.assets%/og-image.png" />

    <!-- Accessibility -->
    <meta name="theme-color" content="#3b82f6" />
    <meta name="color-scheme" content="light dark" />

    <!-- Performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

    %sveltekit.head%
  </head>
  <body
    data-sveltekit-preload-data="hover"
    class="bg-white dark:bg-gray-900 text-gray-900 dark:text-white quantum-hero-background"
  >
    <!-- Color blindness simulation filters -->
    <svg
      style="position: absolute; width: 0; height: 0; overflow: hidden"
      aria-hidden="true"
    >
      <defs>
        <!-- Protanopia filter (red-blind) -->
        <filter id="protanopia-filter">
          <feColorMatrix
            type="matrix"
            values="0.567 0.433 0 0 0
                                               0.558 0.442 0 0 0
                                               0 0.242 0.758 0 0
                                               0 0 0 1 0"
          />
        </filter>

        <!-- Deuteranopia filter (green-blind) -->
        <filter id="deuteranopia-filter">
          <feColorMatrix
            type="matrix"
            values="0.625 0.375 0 0 0
                                               0.7 0.3 0 0 0
                                               0 0.3 0.7 0 0
                                               0 0 0 1 0"
          />
        </filter>

        <!-- Tritanopia filter (blue-blind) -->
        <filter id="tritanopia-filter">
          <feColorMatrix
            type="matrix"
            values="0.95 0.05 0 0 0
                                               0 0.433 0.567 0 0
                                               0 0.475 0.525 0 0
                                               0 0 0 1 0"
          />
        </filter>
      </defs>
    </svg>

    <div style="display: contents" class="app">%sveltekit.body%</div>

    <!-- Accessibility: Skip to main content -->
    <script>
      // Enhanced accessibility for screen readers
      if (typeof window !== 'undefined') {
        // Add skip link for keyboard navigation
        const skipLink = document.createElement('a');
        skipLink.href = '#main-content';
        skipLink.textContent = 'Skip to main content';
        skipLink.className =
          'sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-blue-600 focus:text-white focus:rounded-lg';
        document.body.insertBefore(skipLink, document.body.firstChild);
      }
    </script>
  </body>
</html>
