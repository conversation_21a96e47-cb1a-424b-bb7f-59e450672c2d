{"extends": "./.svelte-kit/tsconfig.json", "compilerOptions": {"allowJs": true, "checkJs": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "skipLibCheck": true, "sourceMap": true, "strict": true, "moduleResolution": "bundler", "target": "ES2022", "module": "ESNext", "lib": ["ES2022", "DOM", "DOM.Iterable"], "allowSyntheticDefaultImports": true, "declaration": true, "declarationMap": true, "noEmit": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noUncheckedIndexedAccess": true}, "include": ["**/*.d.ts", "**/*.ts", "**/*.js", "**/*.svelte"], "exclude": ["node_modules/**", "build/**", ".svelte-kit/**"]}