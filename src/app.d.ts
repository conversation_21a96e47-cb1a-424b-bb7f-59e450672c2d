// See https://kit.svelte.dev/docs/types#app
// for information about these interfaces
declare global {
  namespace App {
    // interface Error {}
    // interface Locals {}
    // interface PageData {}
    // interface PageState {}
    // interface Platform {}
  }

  // Extend Window interface for analytics
  interface Window {
    plausible?: (
      event: string,
      options?: { props?: Record<string, string | number> }
    ) => void;
  }

  // Declare module for CSS imports
  declare module '*.css' {
    const content: string;
    export default content;
  }

  // Declare module for PCSS imports
  declare module '*.pcss' {
    const content: string;
    export default content;
  }
}

export {};
