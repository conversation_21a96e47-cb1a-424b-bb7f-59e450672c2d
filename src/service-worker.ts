// Service Worker for VybeCoding.ai PWA
/// <reference types="@sveltejs/kit" />
/// <reference no-default-lib="true"/>
/// <reference lib="esnext" />
/// <reference lib="webworker" />

import { build, files, version } from '$service-worker';

const sw = self as unknown as ServiceWorkerGlobalScope;

// Create a unique cache name for this deployment
const CACHE = `vybecoding-cache-${version}`;

// Assets to cache on install
const ASSETS = [
  ...build, // the app itself
  ...files, // everything in static
];

// Install event - cache assets
sw.addEventListener('install', event => {
  // Create a new cache and add all files to it
  async function addFilesToCache() {
    const cache = await caches.open(CACHE);
    await cache.addAll(ASSETS);
  }

  event.waitUntil(addFilesToCache());
});

// Activate event - clean up old caches
sw.addEventListener('activate', event => {
  // Remove previous cached data from disk
  async function deleteOldCaches() {
    for (const key of await caches.keys()) {
      if (key !== CACHE) await caches.delete(key);
    }
  }

  event.waitUntil(deleteOldCaches());
});

// Fetch event - serve from cache, fallback to network
sw.addEventListener('fetch', event => {
  // ignore POST requests etc
  if (event.request.method !== 'GET') return;

  async function respond() {
    const url = new URL(event.request.url);
    const cache = await caches.open(CACHE);

    // `build`/`files` can always be served from the cache
    if (ASSETS.includes(url.pathname)) {
      const response = await cache.match(url.pathname);
      if (response) {
        return response;
      }
    }

    // For everything else, try the network first, but
    // fall back to the cache if we're offline
    try {
      const response = await fetch(event.request);

      // If we're offline, or if the request failed, serve from cache
      if (response.status === 200) {
        cache.put(event.request, response.clone());
      }

      return response;
    } catch {
      const response = await cache.match(event.request);
      if (response) {
        return response;
      }
    }

    return new Response('Not found', { status: 404 });
  }

  event.respondWith(respond());
});

// Background sync for offline actions
sw.addEventListener('sync', (event: any) => {
  if (event.tag === 'background-sync') {
    event.waitUntil(doBackgroundSync());
  }
});

async function doBackgroundSync() {
  // Handle offline actions when back online
  try {
    // Get pending actions from IndexedDB
    const pendingActions = await getPendingActions();

    for (const action of pendingActions) {
      try {
        await syncAction(action);
        await removePendingAction(action.id);
      } catch (error) {
        console.error('Failed to sync action:', error);
      }
    }
  } catch (error) {
    console.error('Background sync failed:', error);
  }
}

async function getPendingActions(): Promise<any[]> {
  // Real IndexedDB implementation for offline actions
  try {
    const db = await openDB();
    const transaction = db.transaction(['pendingActions'], 'readonly');
    const store = transaction.objectStore('pendingActions');
    const actions = await store.getAll();
    return actions || [];
  } catch (error) {
    console.error('Failed to get pending actions:', error);
    return [];
  }
}

async function syncAction(action: any): Promise<void> {
  // Real action synchronization with server
  try {
    const response = await fetch('/api/sync/action', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(action),
    });

    if (!response.ok) {
      throw new Error(`Sync failed: ${response.statusText}`);
    }

    console.log('Action synced successfully:', action.id);
  } catch (error) {
    console.error('Failed to sync action:', error);
    throw error;
  }
}

async function removePendingAction(actionId: string): Promise<void> {
  // Real IndexedDB removal
  try {
    const db = await openDB();
    const transaction = db.transaction(['pendingActions'], 'readwrite');
    const store = transaction.objectStore('pendingActions');
    await store.delete(actionId);
    console.log('Removed pending action:', actionId);
  } catch (error) {
    console.error('Failed to remove pending action:', error);
    throw error;
  }
}

// Push notifications
sw.addEventListener('push', event => {
  if (!event.data) return;

  const data = event.data.json();

  const options: any = {
    body: data.body,
    icon: '/favicon.png',
    badge: '/favicon.png',
    data: data.data,
    actions: [
      {
        action: 'open',
        title: 'Open App',
      },
      {
        action: 'dismiss',
        title: 'Dismiss',
      },
    ],
  };

  event.waitUntil(sw.registration.showNotification(data.title, options));
});

// Notification click handling
sw.addEventListener('notificationclick', event => {
  event.notification.close();

  if (event.action === 'open' || !event.action) {
    event.waitUntil(sw.clients.openWindow('/'));
  }
});

// Message handling for communication with main thread
sw.addEventListener('message', event => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    sw.skipWaiting();
  }
});

// Periodic background sync (if supported)
sw.addEventListener('periodicsync', (event: any) => {
  if (event.tag === 'content-sync') {
    event.waitUntil(syncContent());
  }
});

async function syncContent() {
  try {
    console.log('Syncing content in background');

    // Real content synchronization implementation
    const syncTasks = [
      syncCourseProgress(),
      syncBookmarksAndNotes(),
      cacheNewContent(),
    ];

    await Promise.allSettled(syncTasks);
    console.log('Content sync completed');
  } catch (error) {
    console.error('Content sync failed:', error);
  }
}

async function syncCourseProgress(): Promise<void> {
  try {
    const response = await fetch('/api/sync/progress');
    if (response.ok) {
      const progress = await response.json();
      await cacheProgressData(progress);
    }
  } catch (error) {
    console.error('Failed to sync course progress:', error);
  }
}

async function syncBookmarksAndNotes(): Promise<void> {
  try {
    const response = await fetch('/api/sync/bookmarks');
    if (response.ok) {
      const bookmarks = await response.json();
      await cacheBookmarkData(bookmarks);
    }
  } catch (error) {
    console.error('Failed to sync bookmarks:', error);
  }
}

async function cacheNewContent(): Promise<void> {
  try {
    const response = await fetch('/api/content/recent');
    if (response.ok) {
      const content = await response.json();
      await cacheContentData(content);
    }
  } catch (error) {
    console.error('Failed to cache new content:', error);
  }
}

// IndexedDB helper functions
async function openDB(): Promise<IDBDatabase> {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('VybeCodingDB', 1);

    request.onerror = () => reject(request.error);
    request.onsuccess = () => resolve(request.result);

    request.onupgradeneeded = event => {
      const db = (event.target as IDBOpenDBRequest).result;

      if (!db.objectStoreNames.contains('pendingActions')) {
        db.createObjectStore('pendingActions', { keyPath: 'id' });
      }

      if (!db.objectStoreNames.contains('progress')) {
        db.createObjectStore('progress', { keyPath: 'id' });
      }

      if (!db.objectStoreNames.contains('bookmarks')) {
        db.createObjectStore('bookmarks', { keyPath: 'id' });
      }

      if (!db.objectStoreNames.contains('content')) {
        db.createObjectStore('content', { keyPath: 'id' });
      }
    };
  });
}

async function cacheProgressData(progress: any[]): Promise<void> {
  try {
    const db = await openDB();
    const transaction = db.transaction(['progress'], 'readwrite');
    const store = transaction.objectStore('progress');

    for (const item of progress) {
      await store.put(item);
    }
  } catch (error) {
    console.error('Failed to cache progress data:', error);
  }
}

async function cacheBookmarkData(bookmarks: any[]): Promise<void> {
  try {
    const db = await openDB();
    const transaction = db.transaction(['bookmarks'], 'readwrite');
    const store = transaction.objectStore('bookmarks');

    for (const bookmark of bookmarks) {
      await store.put(bookmark);
    }
  } catch (error) {
    console.error('Failed to cache bookmark data:', error);
  }
}

async function cacheContentData(content: any[]): Promise<void> {
  try {
    const db = await openDB();
    const transaction = db.transaction(['content'], 'readwrite');
    const store = transaction.objectStore('content');

    for (const item of content) {
      await store.put(item);
    }
  } catch (error) {
    console.error('Failed to cache content data:', error);
  }
}

// Error handling
sw.addEventListener('error', event => {
  console.error('Service Worker error:', event.error);
});

sw.addEventListener('unhandledrejection', event => {
  console.error('Service Worker unhandled rejection:', event.reason);
});

export {};
