<script>
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import { stripeService } from '$lib/services/stripe.js';

  let loading = true;
  let paymentData = null;
  let error = null;

  onMount(async () => {
    const sessionId = $page.url.searchParams.get('session_id');

    if (!sessionId) {
      error = 'No session ID provided';
      loading = false;
      return;
    }

    try {
      paymentData = await stripeService.validatePayment(sessionId);
      loading = false;

      // Redirect to dashboard after 5 seconds
      setTimeout(() => {
        goto('/dashboard');
      }, 5000);
    } catch (err) {
      console.error('Payment validation error:', err);
      error = 'Failed to validate payment';
      loading = false;
    }
  });
</script>

<svelte:head>
  <title>Payment Success - VybeCoding.ai</title>
</svelte:head>

<div
  class="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center py-12"
>
  <div class="max-w-md w-full mx-auto px-4">
    {#if loading}
      <div class="bg-white rounded-2xl shadow-xl p-8 text-center">
        <div
          class="animate-spin rounded-full h-16 w-16 border-b-2 border-green-500 mx-auto mb-4"
        ></div>
        <h2 class="text-xl font-semibold text-gray-900 mb-2">
          Validating Payment...
        </h2>
        <p class="text-gray-600">Please wait while we confirm your payment.</p>
      </div>
    {:else if error}
      <div class="bg-white rounded-2xl shadow-xl p-8 text-center">
        <div
          class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4"
        >
          <svg
            class="w-8 h-8 text-red-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            ></path>
          </svg>
        </div>
        <h2 class="text-xl font-semibold text-gray-900 mb-2">
          Payment Validation Failed
        </h2>
        <p class="text-gray-600 mb-6">
          {error}
        </p>
        <div class="space-y-3">
          <button
            onclick={() => goto('/pricing')}
            class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Back to Pricing
          </button>
          <button
            onclick={() => goto('/support')}
            class="w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200 transition-colors"
          >
            Contact Support
          </button>
        </div>
      </div>
    {:else}
      <div class="bg-white rounded-2xl shadow-xl p-8 text-center">
        <!-- Success Icon -->
        <div
          class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4"
        >
          <svg
            class="w-8 h-8 text-green-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M5 13l4 4L19 7"
            ></path>
          </svg>
        </div>

        <!-- Success Message -->
        <h2 class="text-2xl font-bold text-gray-900 mb-2">
          Payment Successful! 🎉
        </h2>
        <p class="text-gray-600 mb-6">
          Welcome to VybeCoding.ai! Your subscription is now active.
        </p>

        {#if paymentData}
          <!-- Payment Details -->
          <div class="bg-gray-50 rounded-lg p-4 mb-6 text-left">
            <h3 class="font-semibold text-gray-900 mb-2">Payment Details</h3>
            <div class="space-y-1 text-sm text-gray-600">
              {#if paymentData.subscription}
                <div>Plan: {paymentData.subscription.tier || 'Pro'}</div>
                <div>Status: {paymentData.subscription.status}</div>
                {#if paymentData.subscription.trial_end}
                  <div>
                    Trial ends: {new Date(
                      paymentData.subscription.trial_end * 1000
                    ).toLocaleDateString()}
                  </div>
                {/if}
              {/if}
              {#if paymentData.course}
                <div>Course: {paymentData.course.title}</div>
                <div>Access: Lifetime</div>
              {/if}
            </div>
          </div>
        {/if}

        <!-- Next Steps -->
        <div class="space-y-3">
          <button
            onclick={() => goto('/dashboard')}
            class="w-full bg-purple-600 text-white py-3 px-4 rounded-lg hover:bg-purple-700 transition-colors font-semibold"
          >
            Go to Dashboard
          </button>
          <button
            onclick={() => goto('/courses')}
            class="w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200 transition-colors"
          >
            Browse Courses
          </button>
        </div>

        <!-- Auto-redirect Notice -->
        <p class="text-xs text-gray-500 mt-4">
          You'll be automatically redirected to your dashboard in a few seconds.
        </p>
      </div>
    {/if}
  </div>
</div>
