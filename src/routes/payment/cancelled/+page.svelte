<script>
  import { goto } from '$app/navigation';
</script>

<svelte:head>
  <title>Payment Cancelled - VybeCoding.ai</title>
</svelte:head>

<div
  class="min-h-screen bg-gradient-to-br from-orange-50 to-red-50 flex items-center justify-center py-12"
>
  <div class="max-w-md w-full mx-auto px-4">
    <div class="bg-white rounded-2xl shadow-xl p-8 text-center">
      <!-- Cancelled Icon -->
      <div
        class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4"
      >
        <svg
          class="w-8 h-8 text-orange-500"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
          ></path>
        </svg>
      </div>

      <!-- Cancelled Message -->
      <h2 class="text-2xl font-bold text-gray-900 mb-2">Payment Cancelled</h2>
      <p class="text-gray-600 mb-6">
        No worries! Your payment was cancelled and no charges were made.
      </p>

      <!-- Why did you cancel? -->
      <div class="bg-gray-50 rounded-lg p-4 mb-6 text-left">
        <h3 class="font-semibold text-gray-900 mb-2">Need help deciding?</h3>
        <p class="text-sm text-gray-600 mb-3">
          We're here to help you find the perfect plan for your learning
          journey.
        </p>
        <ul class="text-sm text-gray-600 space-y-1">
          <li>• Start with our free plan</li>
          <li>• Chat with our AI assistant</li>
          <li>• Contact our support team</li>
          <li>• Browse our course catalog</li>
        </ul>
      </div>

      <!-- Action Buttons -->
      <div class="space-y-3">
        <button
          onclick={() => goto('/pricing')}
          class="w-full bg-purple-600 text-white py-3 px-4 rounded-lg hover:bg-purple-700 transition-colors font-semibold"
        >
          View Pricing Again
        </button>
        <button
          onclick={() => goto('/courses')}
          class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
        >
          Browse Free Courses
        </button>
        <button
          onclick={() => goto('/support')}
          class="w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200 transition-colors"
        >
          Contact Support
        </button>
      </div>

      <!-- Special Offer -->
      <div
        class="mt-6 p-4 bg-gradient-to-r from-purple-100 to-blue-100 rounded-lg"
      >
        <h4 class="font-semibold text-gray-900 mb-1">Special Offer</h4>
        <p class="text-sm text-gray-600">
          Try our Pro plan with a 7-day free trial - no commitment required!
        </p>
      </div>
    </div>
  </div>
</div>
