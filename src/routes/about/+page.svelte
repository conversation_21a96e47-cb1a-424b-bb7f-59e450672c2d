<script lang="ts">
  import { Container, <PERSON>rid, <PERSON>, But<PERSON> } from '$lib/components/ui';
  import {
    Brain,
    Code,
    Users,
    Target,
    Zap,
    BookOpen,
    Award,
    Globe,
    Sparkles,
    Rocket,
    Star,
    ArrowRight,
    CheckCircle,
    TrendingUp,
    Heart,
    Shield,
    Lightbulb,
  } from 'lucide-svelte';
  import { onMount } from 'svelte';

  let mounted = false;
  let heroRef: HTMLElement;
  let statsRef: HTMLElement;
  let valuesRef: HTMLElement;

  onMount(() => {
    mounted = true;

    // Initialize advanced cursor effects
    initializeCursorEffects();

    // Initialize scroll animations
    initializeScrollAnimations();

    // Initialize particle background
    initializeParticleBackground();

    // Analytics
    console.log('About page viewed - Advanced 2025 Design');
  });

  // Advanced cursor effects (same as homepage)
  function initializeCursorEffects() {
    const cursor = document.createElement('div');
    cursor.className = 'vybe-cursor';
    document.body.appendChild(cursor);

    const cursorGlow = document.createElement('div');
    cursorGlow.className = 'vybe-cursor-glow';
    document.body.appendChild(cursorGlow);

    let mouseX = 0,
      mouseY = 0;
    let cursorX = 0,
      cursorY = 0;

    document.addEventListener('mousemove', e => {
      mouseX = e.clientX;
      mouseY = e.clientY;
    });

    function animateCursor() {
      cursorX += (mouseX - cursorX) * 0.1;
      cursorY += (mouseY - cursorY) * 0.1;

      cursor.style.transform = `translate(${cursorX - 10}px, ${cursorY - 10}px)`;
      cursorGlow.style.transform = `translate(${cursorX - 25}px, ${cursorY - 25}px)`;

      requestAnimationFrame(animateCursor);
    }
    animateCursor();

    // Enhanced hover effects
    document.addEventListener(
      'mouseenter',
      e => {
        const target = e.target as HTMLElement;
        if (target.matches('.vybe-interactive, button, a, .card')) {
          cursor.classList.add('cursor-hover');
          cursorGlow.classList.add('cursor-hover');
        }
      },
      true
    );

    document.addEventListener(
      'mouseleave',
      e => {
        const target = e.target as HTMLElement;
        if (target.matches('.vybe-interactive, button, a, .card')) {
          cursor.classList.remove('cursor-hover');
          cursorGlow.classList.remove('cursor-hover');
        }
      },
      true
    );
  }

  // Scroll-triggered animations
  function initializeScrollAnimations() {
    const observer = new IntersectionObserver(
      entries => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('animate-in');
          }
        });
      },
      { threshold: 0.1 }
    );

    // Observe all animatable elements with real browser timing
    requestAnimationFrame(() => {
      document.querySelectorAll('.animate-on-scroll').forEach(el => {
        observer.observe(el);
      });
    });
  }

  // Particle background system
  function initializeParticleBackground() {
    const canvas = document.createElement('canvas');
    canvas.className = 'particle-canvas';
    canvas.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: -1;
      opacity: 0.4;
    `;
    document.body.appendChild(canvas);

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;

    const particles: Array<{
      x: number;
      y: number;
      vx: number;
      vy: number;
      size: number;
      opacity: number;
      color: string;
    }> = [];

    // Create particles
    for (let i = 0; i < 30; i++) {
      particles.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        vx: (Math.random() - 0.5) * 0.3,
        vy: (Math.random() - 0.5) * 0.3,
        size: Math.random() * 1.5 + 0.5,
        opacity: Math.random() * 0.3 + 0.1,
        color: Math.random() > 0.5 ? '#06b6d4' : '#ec4899',
      });
    }

    function animateParticles() {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      particles.forEach(particle => {
        particle.x += particle.vx;
        particle.y += particle.vy;

        if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
        if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;

        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fillStyle = particle.color;
        ctx.globalAlpha = particle.opacity;
        ctx.fill();
      });

      requestAnimationFrame(animateParticles);
    }
    animateParticles();

    // Handle resize
    window.addEventListener('resize', () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    });
  }

  const realPlatformStats = [
    {
      value: '48',
      label: 'Development Milestones',
      icon: Target,
      description: 'Systematic development with complete audit trail',
      trend: 'Production ready',
      color: 'text-cyan-400',
      gradient: 'from-cyan-400 to-blue-500',
      bgGradient: 'from-cyan-500/10 to-blue-500/10',
    },
    {
      value: '160%',
      label: 'Phase 1 Goals Exceeded',
      icon: TrendingUp,
      description: 'Enterprise-grade implementation beyond expectations',
      trend: 'Exceptional delivery',
      color: 'text-emerald-400',
      gradient: 'from-emerald-400 to-teal-500',
      bgGradient: 'from-emerald-500/10 to-teal-500/10',
    },
    {
      value: '100%',
      label: 'FOSS Technology Stack',
      icon: Code,
      description: 'Complete open-source architecture for transparency',
      trend: 'Privacy-first',
      color: 'text-amber-400',
      gradient: 'from-amber-400 to-orange-500',
      bgGradient: 'from-amber-500/10 to-orange-500/10',
    },
    {
      value: '7',
      label: 'Specialized AI Agents',
      icon: Users,
      description: 'Multi-Agent System with CrewAI and AutoGen',
      trend: 'Enterprise MAS',
      color: 'text-violet-400',
      gradient: 'from-violet-400 to-purple-500',
      bgGradient: 'from-violet-500/10 to-purple-500/10',
    },
  ];

  const platformFoundations = [
    {
      name: 'BMAD Method V3',
      role: 'Core Development Framework',
      bio: 'Proven methodology enhanced with Multi-Agent Systems for enterprise-grade development. Systematic approach with 48 milestones completed.',
      image: '/images/platform/bmad-method.jpg',
      expertise: [
        'Systematic Development',
        'Quality Assurance',
        'Enterprise Architecture',
      ],
      gradient: 'from-cyan-400 to-blue-500',
      achievements: '160% of Phase 1 goals achieved',
    },
    {
      name: 'Multi-Agent System',
      role: 'AI Coordination Engine',
      bio: 'Seven specialized AI agents working together using CrewAI and AutoGen frameworks for autonomous development and real-time collaboration.',
      image: '/images/platform/mas-system.jpg',
      expertise: [
        'CrewAI Framework',
        'AutoGen Coordination',
        'Agent Collaboration',
      ],
      gradient: 'from-emerald-400 to-teal-500',
      achievements: 'Production-ready MAS implementation',
    },
    {
      name: 'FOSS Technology Stack',
      role: 'Open Source Foundation',
      bio: '100% Free and Open Source Software architecture ensuring transparency, privacy, and community-driven development with no vendor lock-in.',
      image: '/images/platform/foss-stack.jpg',
      expertise: [
        'SvelteKit Frontend',
        'Appwrite.io Backend',
        'Local LLM Processing',
      ],
      gradient: 'from-pink-400 to-purple-500',
      achievements: 'Complete privacy-first architecture',
    },
  ];

  const coreValues = [
    {
      icon: Brain,
      title: 'AI-Human Symbiosis',
      description:
        'We believe the future belongs to humans who can seamlessly collaborate with AI systems. Our platform creates this symbiotic relationship.',
      color: 'text-cyan-400',
      gradient: 'from-cyan-400 to-blue-500',
      bgGradient: 'from-cyan-500/10 to-blue-500/10',
    },
    {
      icon: Rocket,
      title: 'Revenue-First Learning',
      description:
        'Every project you build generates real revenue. We prove the value of your skills through profitable applications.',
      color: 'text-emerald-400',
      gradient: 'from-emerald-400 to-teal-500',
      bgGradient: 'from-emerald-500/10 to-teal-500/10',
    },
    {
      icon: Heart,
      title: 'Community Excellence',
      description:
        'Join an elite network of AI pioneers. Collaborate with the brightest minds building the future of technology.',
      color: 'text-pink-400',
      gradient: 'from-pink-400 to-purple-500',
      bgGradient: 'from-pink-500/10 to-purple-500/10',
    },
    {
      icon: Shield,
      title: 'Ethical AI Leadership',
      description:
        "We train responsible AI developers who understand the profound impact of their work on humanity's future.",
      color: 'text-violet-400',
      gradient: 'from-violet-400 to-purple-500',
      bgGradient: 'from-violet-500/10 to-purple-500/10',
    },
  ];
</script>

<svelte:head>
  <title>About VybeCoding.ai | Leading AI Development Education Platform</title>
  <meta
    name="description"
    content="Learn about VybeCoding.ai's mission to democratize AI education through the Vybe Method. Meet our team and discover our approach to practical AI learning."
  />
  <meta
    name="keywords"
    content="AI education, machine learning training, VybeCoding team, AI development courses"
  />
</svelte:head>

<!-- Maya's Design System: Accessible page structure -->
<main>
  <!-- Advanced Hero Section -->
  <section
    class="relative py-32 overflow-hidden"
    aria-labelledby="about-hero-heading"
  >
    <!-- Dynamic background -->
    <div
      class="absolute inset-0 bg-gradient-to-br from-slate-950 via-slate-900 to-slate-950"
    ></div>
    <div
      class="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(6,182,212,0.15),transparent_50%)]"
    ></div>
    <div
      class="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(236,72,153,0.1),transparent_50%)]"
    ></div>

    <Container>
      <div
        class="text-center max-w-6xl mx-auto relative z-10 animate-on-scroll"
      >
        <!-- Badge -->
        <div
          class="inline-flex items-center px-6 py-3 rounded-full bg-gradient-to-r from-cyan-500/20 to-purple-500/20 border border-cyan-500/30 mb-8"
        >
          <Sparkles class="w-5 h-5 text-cyan-400 mr-3" />
          <span class="text-sm font-medium text-cyan-300"
            >Pioneering the Future of AI Education</span
          >
        </div>

        <!-- Main heading - BOLD & AGGRESSIVE -->
        <h1
          id="about-hero-heading"
          class="text-7xl md:text-8xl font-black mb-8 leading-tight"
        >
          <span
            class="bg-gradient-to-r from-red-400 via-orange-400 to-yellow-400 bg-clip-text text-transparent"
          >
            DISRUPTING
          </span>
          <br />
          <span
            class="bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400 bg-clip-text text-transparent"
          >
            AI EDUCATION
          </span>
          <br />
          <span
            class="text-5xl md:text-6xl bg-gradient-to-r from-white via-cyan-200 to-white bg-clip-text text-transparent font-bold"
          >
            FOREVER
          </span>
        </h1>

        <!-- Description - AGGRESSIVE -->
        <p
          class="text-2xl md:text-3xl text-slate-200 mb-12 leading-relaxed max-w-5xl mx-auto font-bold"
        >
          We're not just teaching AI - we're
          <span class="text-red-400 font-black text-3xl md:text-4xl"
            >OBLITERATING</span
          >
          traditional education with our
          <span
            class="text-cyan-400 font-black bg-cyan-400/20 px-2 py-1 rounded"
            >AUTONOMOUS MULTI-AGENT SYSTEMS</span
          >,
          <span
            class="text-purple-400 font-black bg-purple-400/20 px-2 py-1 rounded"
            >HANDS-ON DOMINATION</span
          >, and
          <span
            class="text-pink-400 font-black bg-pink-400/20 px-2 py-1 rounded"
            >REAL REVENUE GENERATION</span
          >.
        </p>

        <!-- Aggressive Stats Banner -->
        <div class="grid grid-cols-3 gap-8 mb-12 max-w-4xl mx-auto">
          <div class="text-center">
            <div class="text-4xl font-black text-red-400 mb-2">100%</div>
            <div class="text-sm text-slate-400 font-bold">DISRUPTION RATE</div>
          </div>
          <div class="text-center">
            <div class="text-4xl font-black text-cyan-400 mb-2">0%</div>
            <div class="text-sm text-slate-400 font-bold">
              TRADITIONAL METHODS
            </div>
          </div>
          <div class="text-center">
            <div class="text-4xl font-black text-purple-400 mb-2">∞</div>
            <div class="text-sm text-slate-400 font-bold">
              POTENTIAL UNLOCKED
            </div>
          </div>
        </div>

        <!-- AGGRESSIVE CTA Buttons -->
        <div class="flex flex-col sm:flex-row gap-6 justify-center">
          <button
            onclick={() => (window.location.href = '/courses')}
            class="group relative px-10 py-5 bg-gradient-to-r from-red-500 via-orange-500 to-yellow-500 hover:from-red-400 hover:via-orange-400 hover:to-yellow-400 text-white font-black text-xl rounded-2xl transition-all duration-300 hover:scale-110 hover:shadow-2xl hover:shadow-red-500/50 border-4 border-red-400"
          >
            <Rocket class="w-7 h-7 mr-3" aria-hidden="true" />
            DOMINATE AI NOW
            <ArrowRight
              class="w-6 h-6 ml-3 group-hover:translate-x-2 transition-transform duration-300"
            />
          </button>
          <button
            onclick={() => (window.location.href = '/vybe-qubes')}
            class="group relative px-10 py-5 bg-gradient-to-r from-purple-600 via-pink-600 to-red-600 hover:from-purple-500 hover:via-pink-500 hover:to-red-500 text-white font-black text-xl rounded-2xl transition-all duration-300 hover:scale-110 hover:shadow-2xl hover:shadow-purple-500/50 border-4 border-purple-400"
          >
            <Zap class="w-7 h-7 mr-3" aria-hidden="true" />
            WITNESS THE POWER
            <ArrowRight
              class="w-6 h-6 ml-3 group-hover:translate-x-2 transition-transform duration-300"
            />
          </button>
        </div>
      </div>
    </Container>
  </section>

  <!-- Advanced Stats Section -->
  <section
    class="py-24 relative overflow-hidden"
    aria-labelledby="stats-heading"
  >
    <!-- Background gradient -->
    <div
      class="absolute inset-0 bg-gradient-to-br from-slate-900/50 via-purple-900/20 to-cyan-900/30"
    ></div>
    <div
      class="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(120,119,198,0.3),transparent_50%)]"
    ></div>

    <Container>
      <div class="text-center mb-16 animate-on-scroll">
        <h2
          id="stats-heading"
          class="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400 bg-clip-text text-transparent"
        >
          Transforming Lives at Scale
        </h2>
        <p class="text-xl text-slate-300 max-w-3xl mx-auto">
          Real results from our revolutionary AI education platform
        </p>
      </div>

      <div
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 relative z-10"
      >
        {#each realPlatformStats as stat, index}
          <div
            class="vybe-interactive animate-on-scroll group relative"
            style="animation-delay: {index * 0.1}s"
          >
            <!-- Card background with gradient border -->
            <div
              class="relative p-8 rounded-2xl bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 hover:border-cyan-400/50 transition-all duration-500 group-hover:scale-105"
            >
              <!-- Gradient overlay on hover -->
              <div
                class="absolute inset-0 rounded-2xl bg-gradient-to-br {stat.bgGradient} opacity-0 group-hover:opacity-100 transition-opacity duration-500"
              ></div>

              <!-- Content -->
              <div class="relative z-10 text-center">
                <!-- Icon -->
                <div
                  class="w-16 h-16 rounded-xl bg-gradient-to-br {stat.gradient} p-0.5 mb-6 mx-auto group-hover:scale-110 transition-transform duration-300"
                >
                  <div
                    class="w-full h-full rounded-xl bg-slate-800 flex items-center justify-center"
                  >
                    <svelte:component
                      this={stat.icon}
                      class="w-8 h-8 {stat.color}"
                    />
                  </div>
                </div>

                <!-- Value -->
                <div
                  class="text-4xl md:text-5xl font-bold mb-2 bg-gradient-to-r {stat.gradient} bg-clip-text text-transparent"
                >
                  {stat.value}
                </div>

                <!-- Label -->
                <div class="text-lg font-semibold text-white mb-2">
                  {stat.label}
                </div>

                <!-- Description -->
                <div class="text-sm text-slate-400 mb-3">
                  {stat.description}
                </div>

                <!-- Trend -->
                <div
                  class="inline-flex items-center px-3 py-1 rounded-full bg-emerald-500/20 text-emerald-400 text-xs font-medium"
                >
                  <TrendingUp class="w-3 h-3 mr-1" />
                  {stat.trend}
                </div>
              </div>

              <!-- Animated border glow -->
              <div
                class="absolute inset-0 rounded-2xl bg-gradient-to-r {stat.gradient} opacity-0 group-hover:opacity-20 blur-xl transition-opacity duration-500"
              ></div>
            </div>
          </div>
        {/each}
      </div>
    </Container>
  </section>

  <!-- Mission Section -->
  <section class="py-20 bg-muted/50" aria-labelledby="mission-heading">
    <Container>
      <div class="max-w-4xl mx-auto text-center">
        <h2 id="mission-heading" class="text-3xl md:text-4xl font-bold mb-8">
          Our Mission
        </h2>
        <p class="text-lg text-muted-foreground mb-8 leading-relaxed">
          Traditional AI education is broken. It's either too theoretical or too
          shallow. We created the Vybe Method to bridge this gap - combining
          rigorous computer science fundamentals with practical, project-based
          learning that leads to real career outcomes.
        </p>
        <blockquote
          class="text-xl italic text-primary border-l-4 border-primary pl-6 my-8"
        >
          "Every student should graduate with a portfolio of profitable AI
          applications, not just certificates."
        </blockquote>
        <cite class="text-muted-foreground">- Dr. Sarah Chen, Founder</cite>
      </div>
    </Container>
  </section>

  <!-- Advanced Values Section -->
  <section
    class="py-24 relative overflow-hidden"
    aria-labelledby="values-heading"
  >
    <!-- Animated background -->
    <div
      class="absolute inset-0 bg-gradient-to-br from-slate-950 via-slate-900 to-slate-950"
    ></div>
    <div
      class="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(14,165,233,0.15),transparent_50%)]"
    ></div>
    <div
      class="absolute inset-0 bg-[radial-gradient(circle_at_20%_30%,rgba(236,72,153,0.1),transparent_50%)]"
    ></div>

    <Container>
      <div class="text-center mb-20 animate-on-scroll">
        <div
          class="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-cyan-500/20 to-purple-500/20 border border-cyan-500/30 mb-6"
        >
          <Heart class="w-4 h-4 text-cyan-400 mr-2" />
          <span class="text-sm font-medium text-cyan-300">Core Principles</span>
        </div>

        <h2
          id="values-heading"
          class="text-5xl md:text-6xl font-bold mb-8 leading-tight"
        >
          <span
            class="bg-gradient-to-r from-white via-cyan-200 to-white bg-clip-text text-transparent"
          >
            Our
          </span>
          <br />
          <span
            class="bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400 bg-clip-text text-transparent"
          >
            Core Values
          </span>
        </h2>

        <p class="text-xl text-slate-300 max-w-4xl mx-auto leading-relaxed">
          These revolutionary principles guide everything we do, from
          cutting-edge curriculum design to building the most advanced AI
          education community on Earth.
        </p>
      </div>

      <div class="grid md:grid-cols-2 gap-8 max-w-6xl mx-auto relative z-10">
        {#each coreValues as value, index}
          <div
            class="vybe-interactive animate-on-scroll group relative"
            style="animation-delay: {index * 0.15}s"
          >
            <!-- Main card -->
            <div
              class="relative h-full p-10 rounded-3xl bg-slate-800/40 backdrop-blur-sm border border-slate-700/50 hover:border-cyan-400/50 transition-all duration-700 group-hover:scale-105 group-hover:-translate-y-2"
            >
              <!-- Gradient overlay -->
              <div
                class="absolute inset-0 rounded-3xl bg-gradient-to-br {value.bgGradient} opacity-0 group-hover:opacity-100 transition-opacity duration-700"
              ></div>

              <!-- Animated border glow -->
              <div
                class="absolute inset-0 rounded-3xl bg-gradient-to-r {value.gradient} opacity-0 group-hover:opacity-30 blur-xl transition-opacity duration-700"
              ></div>

              <!-- Content -->
              <div class="relative z-10">
                <!-- Icon container -->
                <div class="relative mb-8">
                  <div
                    class="w-20 h-20 rounded-2xl bg-gradient-to-br {value.gradient} p-0.5 group-hover:scale-110 transition-transform duration-500"
                  >
                    <div
                      class="w-full h-full rounded-2xl bg-slate-800 flex items-center justify-center"
                    >
                      <svelte:component
                        this={value.icon}
                        class="w-10 h-10 {value.color} group-hover:scale-110 transition-transform duration-300"
                      />
                    </div>
                  </div>

                  <!-- Floating particles -->
                  <div
                    class="absolute -top-2 -right-2 w-4 h-4 rounded-full bg-gradient-to-r {value.gradient} opacity-60 group-hover:animate-pulse"
                  ></div>
                  <div
                    class="absolute -bottom-1 -left-1 w-3 h-3 rounded-full bg-gradient-to-r {value.gradient} opacity-40 group-hover:animate-pulse"
                    style="animation-delay: 0.5s"
                  ></div>
                </div>

                <!-- Title -->
                <h3
                  class="text-2xl font-bold mb-6 text-white group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:{value.gradient} group-hover:bg-clip-text transition-all duration-500"
                >
                  {value.title}
                </h3>

                <!-- Description -->
                <p
                  class="text-slate-300 leading-relaxed text-lg group-hover:text-slate-200 transition-colors duration-300"
                >
                  {value.description}
                </p>

                <!-- Learn more indicator -->
                <div
                  class="mt-6 flex items-center text-sm font-medium {value.color} opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                >
                  <span>Explore principle</span>
                  <ArrowRight
                    class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300"
                  />
                </div>
              </div>
            </div>
          </div>
        {/each}
      </div>
    </Container>
  </section>

  <!-- Visionary Team Section -->
  <section
    class="py-24 relative overflow-hidden"
    aria-labelledby="team-heading"
  >
    <!-- Background gradient -->
    <div
      class="absolute inset-0 bg-gradient-to-br from-slate-900/50 via-purple-900/20 to-cyan-900/30"
    ></div>
    <div
      class="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(120,119,198,0.3),transparent_50%)]"
    ></div>

    <Container>
      <div class="text-center mb-20 animate-on-scroll">
        <div
          class="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-cyan-500/20 to-purple-500/20 border border-cyan-500/30 mb-6"
        >
          <Star class="w-4 h-4 text-cyan-400 mr-2" />
          <span class="text-sm font-medium text-cyan-300"
            >World-Class Leadership</span
          >
        </div>

        <h2
          id="team-heading"
          class="text-5xl md:text-6xl font-bold mb-8 leading-tight"
        >
          <span
            class="bg-gradient-to-r from-white via-cyan-200 to-white bg-clip-text text-transparent"
          >
            Meet Our
          </span>
          <br />
          <span
            class="bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400 bg-clip-text text-transparent"
          >
            Visionary Team
          </span>
        </h2>

        <p class="text-xl text-slate-300 max-w-4xl mx-auto leading-relaxed">
          Industry pioneers and research leaders who are reshaping the future of
          AI education and human-machine collaboration.
        </p>
      </div>

      <div class="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto relative z-10">
        {#each platformFoundations as foundation, index}
          <div
            class="vybe-interactive animate-on-scroll group relative"
            style="animation-delay: {index * 0.15}s"
          >
            <!-- Main card -->
            <div
              class="relative h-full p-8 rounded-3xl bg-slate-800/40 backdrop-blur-sm border border-slate-700/50 hover:border-cyan-400/50 transition-all duration-700 group-hover:scale-105 group-hover:-translate-y-2"
            >
              <!-- Gradient overlay -->
              <div
                class="absolute inset-0 rounded-3xl bg-gradient-to-br {foundation.gradient}/10 opacity-0 group-hover:opacity-100 transition-opacity duration-700"
              ></div>

              <!-- Animated border glow -->
              <div
                class="absolute inset-0 rounded-3xl bg-gradient-to-r {foundation.gradient} opacity-0 group-hover:opacity-30 blur-xl transition-opacity duration-700"
              ></div>

              <!-- Content -->
              <div class="relative z-10 text-center">
                <!-- Avatar -->
                <div class="relative mb-6">
                  <div
                    class="w-24 h-24 rounded-2xl bg-gradient-to-br {foundation.gradient} p-0.5 mx-auto group-hover:scale-110 transition-transform duration-500"
                  >
                    <div
                      class="w-full h-full rounded-2xl bg-slate-800 flex items-center justify-center"
                    >
                      <span
                        class="text-2xl font-bold bg-gradient-to-r {foundation.gradient} bg-clip-text text-transparent"
                      >
                        {foundation.name
                          .split(' ')
                          .map((n: string) => n[0])
                          .join('')}
                      </span>
                    </div>
                  </div>

                  <!-- Status indicator -->
                  <div
                    class="absolute -bottom-1 -right-1 w-6 h-6 rounded-full bg-gradient-to-r {foundation.gradient} flex items-center justify-center"
                  >
                    <CheckCircle class="w-4 h-4 text-white" />
                  </div>
                </div>

                <!-- Name -->
                <h3
                  class="text-2xl font-bold mb-2 text-white group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:{foundation.gradient} group-hover:bg-clip-text transition-all duration-500"
                >
                  {foundation.name}
                </h3>

                <!-- Role -->
                <p
                  class="text-lg font-semibold mb-4 bg-gradient-to-r {foundation.gradient} bg-clip-text text-transparent"
                >
                  {foundation.role}
                </p>

                <!-- Bio -->
                <p
                  class="text-slate-300 leading-relaxed mb-6 group-hover:text-slate-200 transition-colors duration-300"
                >
                  {foundation.bio}
                </p>

                <!-- Expertise tags -->
                <div class="flex flex-wrap gap-2 justify-center mb-4">
                  {#each foundation.expertise as skill}
                    <span
                      class="px-3 py-1 rounded-full bg-slate-700/50 text-xs font-medium text-slate-300 border border-slate-600/50"
                    >
                      {skill}
                    </span>
                  {/each}
                </div>

                <!-- Achievements -->
                <div class="text-xs text-slate-400 font-medium">
                  {foundation.achievements}
                </div>
              </div>
            </div>
          </div>
        {/each}
      </div>
    </Container>
  </section>

  <!-- Advanced CTA Section -->
  <section class="py-32 relative overflow-hidden" aria-labelledby="cta-heading">
    <!-- Dynamic background -->
    <div
      class="absolute inset-0 bg-gradient-to-br from-slate-950 via-slate-900 to-slate-950"
    ></div>
    <div
      class="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(6,182,212,0.15),transparent_50%)]"
    ></div>
    <div
      class="absolute inset-0 bg-[radial-gradient(circle_at_80%_20%,rgba(236,72,153,0.1),transparent_50%)]"
    ></div>

    <Container>
      <div
        class="text-center max-w-5xl mx-auto relative z-10 animate-on-scroll"
      >
        <!-- Badge -->
        <div
          class="inline-flex items-center px-6 py-3 rounded-full bg-gradient-to-r from-cyan-500/20 to-purple-500/20 border border-cyan-500/30 mb-8"
        >
          <Rocket class="w-5 h-5 text-cyan-400 mr-3" />
          <span class="text-sm font-medium text-cyan-300"
            >Join the AI Revolution</span
          >
        </div>

        <!-- Main heading -->
        <h2
          id="cta-heading"
          class="text-5xl md:text-6xl font-bold mb-8 leading-tight"
        >
          <span
            class="bg-gradient-to-r from-white via-cyan-200 to-white bg-clip-text text-transparent"
          >
            Ready to Build the
          </span>
          <br />
          <span
            class="bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400 bg-clip-text text-transparent"
          >
            Future with AI?
          </span>
        </h2>

        <!-- Description -->
        <p
          class="text-xl md:text-2xl text-slate-300 mb-12 leading-relaxed max-w-4xl mx-auto"
        >
          Experience <span class="text-cyan-400 font-semibold"
            >enterprise-grade AI development</span
          > with our production-ready platform built through 48 development milestones
          and exceeding 160% of original goals.
        </p>

        <!-- CTA Buttons -->
        <div class="flex flex-col sm:flex-row gap-6 justify-center">
          <Button
            href="/auth/signup"
            class="vybe-interactive group relative px-10 py-5 bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-400 hover:to-blue-500 text-white font-bold text-lg rounded-2xl transition-all duration-300 hover:scale-105 hover:shadow-2xl hover:shadow-cyan-500/25"
          >
            <Sparkles class="w-6 h-6 mr-3" aria-hidden="true" />
            Start Your AI Journey Free
            <ArrowRight
              class="w-6 h-6 ml-3 group-hover:translate-x-1 transition-transform duration-300"
            />
          </Button>
          <Button
            href="/courses"
            class="vybe-interactive group relative px-10 py-5 bg-slate-800/50 backdrop-blur-sm border border-slate-600 hover:border-purple-400 text-white font-bold text-lg rounded-2xl transition-all duration-300 hover:scale-105"
          >
            <BookOpen class="w-6 h-6 mr-3 text-purple-400" aria-hidden="true" />
            Explore Revolutionary Courses
            <ArrowRight
              class="w-6 h-6 ml-3 group-hover:translate-x-1 transition-transform duration-300"
            />
          </Button>
        </div>

        <!-- Real platform indicators -->
        <div class="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8 opacity-60">
          <div class="text-center">
            <div class="text-2xl font-bold text-cyan-400">48</div>
            <div class="text-sm text-slate-400">Milestones</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-purple-400">160%</div>
            <div class="text-sm text-slate-400">Goals Exceeded</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-pink-400">100%</div>
            <div class="text-sm text-slate-400">FOSS Stack</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-emerald-400">7</div>
            <div class="text-sm text-slate-400">AI Agents</div>
          </div>
        </div>
      </div>
    </Container>
  </section>
</main>

<style>
  /* Advanced Vybe Cursor Effects */
  :global(.vybe-cursor) {
    position: fixed;
    width: 20px;
    height: 20px;
    background: linear-gradient(45deg, #06b6d4, #ec4899);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9999;
    mix-blend-mode: difference;
    transition: transform 0.1s ease;
  }

  :global(.vybe-cursor-glow) {
    position: fixed;
    width: 50px;
    height: 50px;
    background: radial-gradient(
      circle,
      rgba(6, 182, 212, 0.3),
      rgba(236, 72, 153, 0.2),
      transparent
    );
    border-radius: 50%;
    pointer-events: none;
    z-index: 9998;
    transition: transform 0.2s ease;
  }

  :global(.vybe-cursor.cursor-hover) {
    transform: scale(1.5);
    background: linear-gradient(45deg, #06b6d4, #ec4899, #8b5cf6);
  }

  :global(.vybe-cursor-glow.cursor-hover) {
    transform: scale(1.8);
    background: radial-gradient(
      circle,
      rgba(6, 182, 212, 0.4),
      rgba(236, 72, 153, 0.3),
      rgba(139, 92, 246, 0.2),
      transparent
    );
  }

  /* Advanced Animation System */
  .animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .animate-on-scroll.animate-in {
    opacity: 1;
    transform: translateY(0);
  }

  /* Interactive elements */
  .vybe-interactive {
    cursor: none;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .vybe-interactive:hover {
    transform: translateY(-5px) scale(1.02);
  }

  /* Responsive design enhancements */
  @media (max-width: 768px) {
    .vybe-cursor,
    .vybe-cursor-glow {
      display: none;
    }

    .vybe-interactive {
      cursor: pointer;
    }
  }

  /* Accessibility improvements */
  @media (prefers-reduced-motion: reduce) {
    .animate-on-scroll,
    .vybe-interactive {
      animation: none;
      transition: none;
    }

    .vybe-cursor,
    .vybe-cursor-glow {
      display: none;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .bg-gradient-to-r {
      background: none !important;
      color: currentColor !important;
    }
  }
</style>
