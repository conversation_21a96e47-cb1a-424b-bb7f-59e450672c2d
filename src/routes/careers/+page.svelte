<script lang="ts">
  import { Container, <PERSON><PERSON>, Card, Button, Badge } from '$lib/components/ui';
  import { MapPin, Clock, Users, ArrowRight, Mail } from 'lucide-svelte';

  const openPositions = [
    {
      title: 'Senior AI Curriculum Developer',
      department: 'Education',
      location: 'Remote',
      type: 'Full-time',
      description:
        'Design and develop cutting-edge AI curriculum using the Vybe Method.',
      requirements: [
        'PhD in AI/ML or equivalent experience',
        '5+ years curriculum development',
        'Industry AI experience',
      ],
    },
    {
      title: 'Full-Stack Developer',
      department: 'Engineering',
      location: 'San Francisco, CA',
      type: 'Full-time',
      description: 'Build and maintain our SvelteKit-based education platform.',
      requirements: [
        '5+ years full-stack development',
        'SvelteKit/React experience',
        'AI/ML platform experience preferred',
      ],
    },
    {
      title: 'Community Manager',
      department: 'Marketing',
      location: 'Remote',
      type: 'Full-time',
      description: 'Grow and engage our global community of AI developers.',
      requirements: [
        '3+ years community management',
        'Technical background preferred',
        'Excellent communication skills',
      ],
    },
  ];

  const benefits = [
    'Competitive salary and equity',
    'Comprehensive health insurance',
    'Unlimited PTO',
    'Remote-first culture',
    'Learning & development budget',
    'Latest tech equipment',
  ];
</script>

<svelte:head>
  <title
    >Careers - VybeCoding.ai | Join Our Mission to Democratize AI Education</title
  >
  <meta
    name="description"
    content="Join VybeCoding.ai and help democratize AI education. Explore open positions in engineering, education, and community."
  />
</svelte:head>

<main role="main">
  <section class="py-20 bg-gradient-to-br from-primary/10 to-secondary/10">
    <Container>
      <div class="text-center max-w-4xl mx-auto">
        <h1 class="text-4xl md:text-5xl font-bold mb-6">
          Join Our <span class="text-primary">Mission</span>
        </h1>
        <p class="text-xl text-muted-foreground mb-8">
          Help us democratize AI education and empower the next generation of AI
          developers
        </p>
      </div>
    </Container>
  </section>

  <section class="py-20">
    <Container>
      <div class="text-center mb-16">
        <h2 class="text-3xl font-bold mb-4">Open Positions</h2>
        <p class="text-xl text-muted-foreground">
          We're looking for passionate individuals to join our team
        </p>
      </div>

      <div class="max-w-4xl mx-auto space-y-6">
        {#each openPositions as position}
          <Card class="p-6 hover:shadow-lg transition-shadow">
            <div class="flex flex-col md:flex-row md:items-center gap-4">
              <div class="flex-1">
                <div class="flex items-center gap-3 mb-2">
                  <h3 class="text-xl font-semibold">{position.title}</h3>
                  <Badge variant="outline">{position.department}</Badge>
                </div>
                <p class="text-muted-foreground mb-3">{position.description}</p>
                <div
                  class="flex items-center gap-4 text-sm text-muted-foreground mb-3"
                >
                  <span class="flex items-center gap-1">
                    <MapPin class="w-4 h-4" />
                    {position.location}
                  </span>
                  <span class="flex items-center gap-1">
                    <Clock class="w-4 h-4" />
                    {position.type}
                  </span>
                </div>
                <div class="text-sm">
                  <strong>Requirements:</strong>
                  <ul class="list-disc list-inside mt-1 text-muted-foreground">
                    {#each position.requirements as req}
                      <li>{req}</li>
                    {/each}
                  </ul>
                </div>
              </div>
              <div class="flex-shrink-0">
                <Button
                  href="mailto:<EMAIL>?subject=Application: {position.title}"
                >
                  Apply Now
                  <ArrowRight class="w-4 h-4 ml-2" />
                </Button>
              </div>
            </div>
          </Card>
        {/each}
      </div>
    </Container>
  </section>

  <section class="py-20 bg-muted/50">
    <Container>
      <div class="text-center mb-16">
        <h2 class="text-3xl font-bold mb-4">Why VybeCoding.ai?</h2>
        <p class="text-xl text-muted-foreground">
          Join a team that's passionate about transforming education
        </p>
      </div>

      <Grid cols="auto" gap="large" class="max-w-4xl mx-auto">
        {#each benefits as benefit}
          <Card class="p-6 text-center">
            <p class="font-medium">{benefit}</p>
          </Card>
        {/each}
      </Grid>
    </Container>
  </section>

  <section class="py-20">
    <Container>
      <div class="text-center max-w-3xl mx-auto">
        <h2 class="text-3xl font-bold mb-6">Don't See Your Role?</h2>
        <p class="text-xl text-muted-foreground mb-8">
          We're always looking for talented individuals. Send us your resume and
          tell us how you'd like to contribute.
        </p>
        <Button href="mailto:<EMAIL>" class="btn-primary">
          <Mail class="w-5 h-5 mr-2" />
          Get in Touch
        </Button>
      </div>
    </Container>
  </section>
</main>
