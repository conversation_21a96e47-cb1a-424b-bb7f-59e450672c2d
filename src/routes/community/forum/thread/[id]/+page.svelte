<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import { communityService } from '$lib/services/communityService';
  import type { ForumThread, ForumPostReply } from '$lib/types/community';
  import ForumPost from '$lib/components/community/ForumPost.svelte';
  import LoadingSpinner from '$lib/components/ui/LoadingSpinner.svelte';
  import { formatDistanceToNow } from 'date-fns';

  let thread: ForumThread | null = null;
  let posts: ForumPostReply[] = [];
  let loading = true;
  let error = '';
  let replyContent = '';
  let submittingReply = false;

  $: threadId = $page.params.id;

  onMount(async () => {
    if (threadId) {
      await loadThread();
    }
  });

  async function loadThread() {
    try {
      loading = true;
      error = '';

      const [threadData, postsData] = await Promise.all([
        communityService.getForumThread(threadId),
        communityService.getForumPosts(threadId),
      ]);

      thread = threadData;
      posts = postsData;
    } catch (err) {
      error = err instanceof Error ? err.message : 'Failed to load thread';
      console.error('Failed to load thread:', err);
    } finally {
      loading = false;
    }
  }

  async function handleReply() {
    if (!replyContent.trim() || !thread) return;

    try {
      submittingReply = true;

      // Get current user ID (in a real app, this would come from auth)
      const currentUserId = 'demo-user-id';

      const newPost = await communityService.createForumPost({
        threadId: thread.id,
        authorId: currentUserId,
        content: replyContent.trim(),
      });

      posts = [...posts, newPost];
      replyContent = '';

      // Update thread reply count
      if (thread) {
        thread.replyCount += 1;
      }
    } catch (err) {
      error = err instanceof Error ? err.message : 'Failed to post reply';
    } finally {
      submittingReply = false;
    }
  }

  function handleVote(event: CustomEvent) {
    const { postId, voteType } = event.detail;
    // Handle voting logic
    communityService.voteOnPost(postId, 'demo-user-id', voteType);
  }

  function formatTimeAgo(date: Date) {
    try {
      return formatDistanceToNow(date, { addSuffix: true });
    } catch {
      return 'recently';
    }
  }

  function getThreadTypeIcon(type: string) {
    return type === 'question' ? '❓' : '💬';
  }

  function getStatusColor(status: string) {
    switch (status) {
      case 'answered':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
      case 'closed':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';
      default:
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';
    }
  }
</script>

<svelte:head>
  <title
    >{thread?.title || 'Loading...'} - Community Forum - VybeCoding.ai</title
  >
</svelte:head>

<div
  class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-indigo-900"
>
  <div class="container mx-auto px-4 py-8 max-w-4xl">
    <!-- Back Button -->
    <button
      on:click={() => goto('/community/forum')}
      class="flex items-center gap-2 text-slate-600 dark:text-slate-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors mb-6"
    >
      <svg
        class="w-5 h-5"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M15 19l-7-7 7-7"
        ></path>
      </svg>
      Back to Forum
    </button>

    {#if loading}
      <div class="flex justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    {:else if error}
      <div
        class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-6 text-center"
      >
        <div class="text-red-600 dark:text-red-400 text-lg font-semibold mb-2">
          ⚠️ Error Loading Thread
        </div>
        <p class="text-red-600 dark:text-red-400">{error}</p>
        <button
          on:click={loadThread}
          class="mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
        >
          Try Again
        </button>
      </div>
    {:else if thread}
      <!-- Thread Header -->
      <div
        class="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-2xl border border-slate-200 dark:border-slate-700 p-8 mb-6"
      >
        <div class="flex items-start gap-4 mb-4">
          <div class="text-3xl">
            {getThreadTypeIcon(thread.type)}
          </div>

          <div class="flex-1">
            <div class="flex items-start justify-between gap-4 mb-3">
              <h1 class="text-2xl font-bold text-slate-800 dark:text-slate-200">
                {thread.title}
              </h1>

              <span
                class="px-3 py-1 text-sm font-medium rounded-full {getStatusColor(
                  thread.status
                )}"
              >
                {thread.status}
              </span>
            </div>

            <!-- Thread Meta -->
            <div
              class="flex flex-wrap items-center gap-4 text-sm text-slate-600 dark:text-slate-400 mb-4"
            >
              <span class="flex items-center gap-1">
                <span class="w-2 h-2 rounded-full bg-blue-500"></span>
                {thread.type === 'question' ? 'Question' : 'Discussion'}
              </span>

              <span class="flex items-center gap-1">
                <span class="text-sm">👁️</span>
                {thread.viewCount} views
              </span>

              <span class="flex items-center gap-1">
                <span class="text-sm">💬</span>
                {thread.replyCount} replies
              </span>

              <span class="flex items-center gap-1">
                <span class="text-sm">⏰</span>
                {formatTimeAgo(thread.createdAt)}
              </span>

              {#if thread.isPinned}
                <span
                  class="flex items-center gap-1 text-yellow-600 dark:text-yellow-400"
                >
                  <span class="text-sm">📌</span>
                  Pinned
                </span>
              {/if}
            </div>

            <!-- Thread Content -->
            <div class="prose prose-slate dark:prose-invert max-w-none">
              <p class="text-slate-700 dark:text-slate-300 leading-relaxed">
                {thread.content}
              </p>
            </div>

            <!-- Tags -->
            {#if thread.tags && thread.tags.length > 0}
              <div class="flex flex-wrap gap-2 mt-4">
                {#each thread.tags as tag}
                  <span
                    class="px-3 py-1 text-sm font-medium rounded-full bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-400"
                  >
                    #{tag.name}
                  </span>
                {/each}
              </div>
            {/if}
          </div>
        </div>
      </div>

      <!-- Replies Section -->
      <div
        class="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-2xl border border-slate-200 dark:border-slate-700 p-8 mb-6"
      >
        <h2
          class="text-xl font-bold text-slate-800 dark:text-slate-200 mb-6 flex items-center gap-2"
        >
          <span class="text-2xl">💬</span>
          Replies ({posts.length})
        </h2>

        {#if posts.length > 0}
          <div class="space-y-6">
            {#each posts as post}
              <ForumPost {post} on:vote={handleVote} />
            {/each}
          </div>
        {:else}
          <div class="text-center py-8 text-slate-500 dark:text-slate-400">
            <div class="text-4xl mb-2">💭</div>
            <p class="text-lg font-medium mb-2">No replies yet</p>
            <p>Be the first to share your thoughts!</p>
          </div>
        {/if}
      </div>

      <!-- Reply Form -->
      <div
        class="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-2xl border border-slate-200 dark:border-slate-700 p-8"
      >
        <h3
          class="text-lg font-bold text-slate-800 dark:text-slate-200 mb-4 flex items-center gap-2"
        >
          <span class="text-xl">✏️</span>
          Add Your Reply
        </h3>

        <form on:submit|preventDefault={handleReply} class="space-y-4">
          <textarea
            bind:value={replyContent}
            placeholder="Share your thoughts, provide an answer, or ask for clarification..."
            rows="6"
            class="w-full px-4 py-3 rounded-xl border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 text-slate-900 dark:text-slate-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-vertical"
            required
          ></textarea>

          <div class="flex justify-end">
            <button
              type="submit"
              disabled={submittingReply || !replyContent.trim()}
              class="btn-primary flex items-center gap-2 px-6 py-3 rounded-xl font-semibold transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {#if submittingReply}
                <LoadingSpinner size="sm" />
                Posting...
              {:else}
                <span class="text-xl">📤</span>
                Post Reply
              {/if}
            </button>
          </div>
        </form>
      </div>
    {/if}
  </div>
</div>

<style>
  .btn-primary {
    @apply bg-gradient-to-r from-blue-600 to-indigo-600 text-white;
    @apply hover:from-blue-700 hover:to-indigo-700;
    @apply focus:ring-4 focus:ring-blue-500/20;
    @apply shadow-lg hover:shadow-xl;
  }
</style>
