<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { communityService } from '$lib/services/communityService';
  import type { ForumCategory } from '$lib/types/community';
  import LoadingSpinner from '$lib/components/ui/LoadingSpinner.svelte';

  let categories: ForumCategory[] = [];
  let loading = false;
  let error = '';

  // Form data
  let title = '';
  let content = '';
  let selectedCategory = '';
  let threadType: 'discussion' | 'question' = 'discussion';
  let tags = '';

  // Form validation
  let titleError = '';
  let contentError = '';
  let categoryError = '';

  onMount(async () => {
    await loadCategories();
  });

  async function loadCategories() {
    try {
      categories = await communityService.getForumCategories();

      // If no categories exist, create default ones
      if (categories.length === 0) {
        await createDefaultCategories();
        categories = await communityService.getForumCategories();
      }
    } catch (err) {
      error = err instanceof Error ? err.message : 'Failed to load categories';
    }
  }

  async function createDefaultCategories() {
    const defaultCategories = [
      {
        name: 'General Discussion',
        description: 'General topics and community discussions',
        color: '#6366f1',
        icon: '💬',
        sortOrder: 0,
      },
      {
        name: 'Questions & Help',
        description: 'Get help with coding problems and questions',
        color: '#10b981',
        icon: '❓',
        sortOrder: 1,
      },
      {
        name: 'Show & Tell',
        description: 'Share your projects and achievements',
        color: '#f59e0b',
        icon: '🎨',
        sortOrder: 2,
      },
      {
        name: 'Learning Resources',
        description: 'Share and discover learning materials',
        color: '#8b5cf6',
        icon: '📚',
        sortOrder: 3,
      },
    ];

    for (const category of defaultCategories) {
      try {
        await communityService.createForumCategory(category);
      } catch (err) {
        console.error('Failed to create default category:', err);
      }
    }
  }

  function validateForm() {
    titleError = '';
    contentError = '';
    categoryError = '';

    if (!title.trim()) {
      titleError = 'Title is required';
    } else if (title.length < 10) {
      titleError = 'Title must be at least 10 characters';
    }

    if (!content.trim()) {
      contentError = 'Content is required';
    } else if (content.length < 20) {
      contentError = 'Content must be at least 20 characters';
    }

    if (!selectedCategory) {
      categoryError = 'Please select a category';
    }

    return !titleError && !contentError && !categoryError;
  }

  async function handleSubmit() {
    if (!validateForm()) return;

    try {
      loading = true;
      error = '';

      // Get current user ID (in a real app, this would come from auth)
      const currentUserId = 'demo-user-id';

      const tagList = tags
        .split(',')
        .map(tag => tag.trim())
        .filter(tag => tag.length > 0);

      const thread = await communityService.createForumThread({
        categoryId: selectedCategory,
        authorId: currentUserId,
        title: title.trim(),
        content: content.trim(),
        type: threadType,
        tags: tagList.length > 0 ? tagList : undefined,
      });

      // Redirect to the new thread
      goto(`/community/forum/thread/${thread.id}`);
    } catch (err) {
      error = err instanceof Error ? err.message : 'Failed to create thread';
    } finally {
      loading = false;
    }
  }

  function handleCancel() {
    goto('/community/forum');
  }
</script>

<svelte:head>
  <title>New Thread - Community Forum - VybeCoding.ai</title>
</svelte:head>

<div
  class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-indigo-900"
>
  <div class="container mx-auto px-4 py-8 max-w-4xl">
    <!-- Header -->
    <div class="mb-8">
      <h1
        class="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent"
      >
        Start a New Discussion
      </h1>
      <p class="text-slate-600 dark:text-slate-300 mt-2">
        Share your thoughts, ask questions, or start a conversation with the
        community
      </p>
    </div>

    <!-- Form -->
    <div
      class="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-2xl border border-slate-200 dark:border-slate-700 p-8"
    >
      <form on:submit|preventDefault={handleSubmit} class="space-y-6">
        <!-- Thread Type Selection -->
        <div>
          <label
            class="block text-sm font-semibold text-slate-700 dark:text-slate-300 mb-3"
          >
            Thread Type
          </label>
          <div class="flex gap-4">
            <label class="flex items-center gap-2 cursor-pointer">
              <input
                type="radio"
                bind:group={threadType}
                value="discussion"
                class="w-4 h-4 text-blue-600 focus:ring-blue-500"
              />
              <span
                class="text-sm font-medium text-slate-700 dark:text-slate-300"
              >
                💬 Discussion
              </span>
            </label>
            <label class="flex items-center gap-2 cursor-pointer">
              <input
                type="radio"
                bind:group={threadType}
                value="question"
                class="w-4 h-4 text-blue-600 focus:ring-blue-500"
              />
              <span
                class="text-sm font-medium text-slate-700 dark:text-slate-300"
              >
                ❓ Question
              </span>
            </label>
          </div>
        </div>

        <!-- Category Selection -->
        <div>
          <label
            for="category"
            class="block text-sm font-semibold text-slate-700 dark:text-slate-300 mb-2"
          >
            Category *
          </label>
          <select
            id="category"
            bind:value={selectedCategory}
            class="form-select w-full px-4 py-3 rounded-xl border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 text-slate-900 dark:text-slate-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            class:error={categoryError}
          >
            <option value="">Select a category...</option>
            {#each categories as category}
              <option value={category.id}>
                {category.icon}
                {category.name}
              </option>
            {/each}
          </select>
          {#if categoryError}
            <p class="text-red-600 dark:text-red-400 text-sm mt-1">
              {categoryError}
            </p>
          {/if}
        </div>

        <!-- Title -->
        <div>
          <label
            for="title"
            class="block text-sm font-semibold text-slate-700 dark:text-slate-300 mb-2"
          >
            Title *
          </label>
          <input
            id="title"
            type="text"
            bind:value={title}
            placeholder="Enter a descriptive title..."
            class="form-input w-full px-4 py-3 rounded-xl border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 text-slate-900 dark:text-slate-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            class:error={titleError}
            maxlength="200"
          />
          {#if titleError}
            <p class="text-red-600 dark:text-red-400 text-sm mt-1">
              {titleError}
            </p>
          {/if}
          <p class="text-slate-500 dark:text-slate-400 text-sm mt-1">
            {title.length}/200 characters
          </p>
        </div>

        <!-- Content -->
        <div>
          <label
            for="content"
            class="block text-sm font-semibold text-slate-700 dark:text-slate-300 mb-2"
          >
            Content *
          </label>
          <textarea
            id="content"
            bind:value={content}
            placeholder="Share your thoughts, ask your question, or start the discussion..."
            rows="8"
            class="form-textarea w-full px-4 py-3 rounded-xl border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 text-slate-900 dark:text-slate-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-vertical"
            class:error={contentError}
          ></textarea>
          {#if contentError}
            <p class="text-red-600 dark:text-red-400 text-sm mt-1">
              {contentError}
            </p>
          {/if}
        </div>

        <!-- Tags -->
        <div>
          <label
            for="tags"
            class="block text-sm font-semibold text-slate-700 dark:text-slate-300 mb-2"
          >
            Tags (Optional)
          </label>
          <input
            id="tags"
            type="text"
            bind:value={tags}
            placeholder="javascript, react, beginner (comma-separated)"
            class="form-input w-full px-4 py-3 rounded-xl border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 text-slate-900 dark:text-slate-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
          <p class="text-slate-500 dark:text-slate-400 text-sm mt-1">
            Add relevant tags to help others find your thread
          </p>
        </div>

        <!-- Error Message -->
        {#if error}
          <div
            class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-4"
          >
            <p class="text-red-600 dark:text-red-400">{error}</p>
          </div>
        {/if}

        <!-- Actions -->
        <div class="flex gap-4 pt-4">
          <button
            type="submit"
            disabled={loading}
            class="btn-primary flex items-center gap-2 px-6 py-3 rounded-xl font-semibold transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {#if loading}
              <LoadingSpinner size="sm" />
              Creating...
            {:else}
              <span class="text-xl">✨</span>
              Create Thread
            {/if}
          </button>

          <button
            type="button"
            on:click={handleCancel}
            disabled={loading}
            class="btn-secondary px-6 py-3 rounded-xl font-semibold transition-all duration-300 disabled:opacity-50"
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<style>
  .btn-primary {
    @apply bg-gradient-to-r from-blue-600 to-indigo-600 text-white;
    @apply hover:from-blue-700 hover:to-indigo-700;
    @apply focus:ring-4 focus:ring-blue-500/20;
    @apply shadow-lg hover:shadow-xl;
  }

  .btn-secondary {
    @apply bg-slate-200 dark:bg-slate-700 text-slate-700 dark:text-slate-300;
    @apply hover:bg-slate-300 dark:hover:bg-slate-600;
    @apply focus:ring-4 focus:ring-slate-500/20;
  }

  .form-input.error,
  .form-select.error,
  .form-textarea.error {
    @apply border-red-500 focus:border-red-500 focus:ring-red-500;
  }
</style>
