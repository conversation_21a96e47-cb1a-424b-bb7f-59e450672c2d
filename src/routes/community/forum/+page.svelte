<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import { communityService } from '$lib/services/communityService';
  import type { ForumCategory, ForumThread } from '$lib/types/community';
  import ForumCategoryCard from '$lib/components/community/ForumCategoryCard.svelte';
  import ForumThreadList from '$lib/components/community/ForumThreadList.svelte';
  import SearchBar from '$lib/components/ui/SearchBar.svelte';
  import LoadingSpinner from '$lib/components/ui/LoadingSpinner.svelte';

  let categories: ForumCategory[] = [];
  let recentThreads: ForumThread[] = [];
  let loading = true;
  let error = '';
  let searchQuery = '';
  let selectedCategory: string | null = null;

  onMount(async () => {
    await loadForumData();
  });

  async function loadForumData() {
    try {
      loading = true;
      error = '';

      // Load categories and recent threads in parallel
      const [categoriesData, threadsData] = await Promise.all([
        communityService.getForumCategories(),
        communityService.getForumThreads(undefined, { limit: 10 }),
      ]);

      categories = categoriesData;
      recentThreads = threadsData;
    } catch (err) {
      error = err instanceof Error ? err.message : 'Failed to load forum data';
      console.error('Failed to load forum data:', err);
    } finally {
      loading = false;
    }
  }

  async function handleSearch(query: string) {
    if (!query.trim()) {
      await loadForumData();
      return;
    }

    try {
      loading = true;
      const searchResults = await communityService.searchForumContent(query, {
        categoryId: selectedCategory || undefined,
      });
      recentThreads = searchResults;
    } catch (err) {
      error = err instanceof Error ? err.message : 'Search failed';
    } finally {
      loading = false;
    }
  }

  function handleCategorySelect(categoryId: string) {
    selectedCategory = categoryId === selectedCategory ? null : categoryId;
    goto(`/community/forum?category=${categoryId}`);
  }

  function createNewThread() {
    goto('/community/forum/new');
  }
</script>

<svelte:head>
  <title>Community Forum - VybeCoding.ai</title>
  <meta
    name="description"
    content="Join discussions, ask questions, and share knowledge with the VybeCoding.ai community"
  />
</svelte:head>

<div
  class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-indigo-900"
>
  <div class="container mx-auto px-4 py-8">
    <!-- Header Section -->
    <div class="mb-8">
      <div
        class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4"
      >
        <div>
          <h1
            class="text-4xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent"
          >
            Community Forum
          </h1>
          <p class="text-slate-600 dark:text-slate-300 mt-2">
            Connect, learn, and grow with fellow developers
          </p>
        </div>

        <button
          on:click={createNewThread}
          class="btn-primary flex items-center gap-2 px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105"
        >
          <span class="text-xl">✏️</span>
          Start Discussion
        </button>
      </div>
    </div>

    <!-- Search Section -->
    <div class="mb-8">
      <SearchBar
        bind:value={searchQuery}
        placeholder="Search discussions, questions, and answers..."
        on:search={e => handleSearch(e.detail)}
        class="max-w-2xl"
      />
    </div>

    {#if loading}
      <div class="flex justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    {:else if error}
      <div
        class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-6 text-center"
      >
        <div class="text-red-600 dark:text-red-400 text-lg font-semibold mb-2">
          ⚠️ Error Loading Forum
        </div>
        <p class="text-red-600 dark:text-red-400">{error}</p>
        <button
          on:click={loadForumData}
          class="mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
        >
          Try Again
        </button>
      </div>
    {:else}
      <div class="grid lg:grid-cols-3 gap-8">
        <!-- Categories Sidebar -->
        <div class="lg:col-span-1">
          <div
            class="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-2xl border border-slate-200 dark:border-slate-700 p-6"
          >
            <h2
              class="text-xl font-bold text-slate-800 dark:text-slate-200 mb-4 flex items-center gap-2"
            >
              <span class="text-2xl">📂</span>
              Categories
            </h2>

            <div class="space-y-3">
              {#each categories as category}
                <ForumCategoryCard
                  {category}
                  selected={selectedCategory === category.id}
                  on:select={() => handleCategorySelect(category.id)}
                />
              {/each}
            </div>

            {#if categories.length === 0}
              <div class="text-center py-8 text-slate-500 dark:text-slate-400">
                <div class="text-4xl mb-2">📝</div>
                <p>No categories yet</p>
                <p class="text-sm">Categories will appear here</p>
              </div>
            {/if}
          </div>
        </div>

        <!-- Main Content -->
        <div class="lg:col-span-2">
          <div
            class="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-2xl border border-slate-200 dark:border-slate-700"
          >
            <div class="p-6 border-b border-slate-200 dark:border-slate-700">
              <h2
                class="text-xl font-bold text-slate-800 dark:text-slate-200 flex items-center gap-2"
              >
                <span class="text-2xl">💬</span>
                {searchQuery ? 'Search Results' : 'Recent Discussions'}
              </h2>
              {#if selectedCategory}
                <p class="text-sm text-slate-600 dark:text-slate-400 mt-1">
                  Filtered by category
                </p>
              {/if}
            </div>

            <div class="p-6">
              <ForumThreadList
                threads={recentThreads}
                showCategory={!selectedCategory}
                on:threadClick={e =>
                  goto(`/community/forum/thread/${e.detail.id}`)}
              />

              {#if recentThreads.length === 0}
                <div
                  class="text-center py-12 text-slate-500 dark:text-slate-400"
                >
                  <div class="text-6xl mb-4">🌟</div>
                  <h3 class="text-xl font-semibold mb-2">
                    {searchQuery
                      ? 'No results found'
                      : 'Start the conversation!'}
                  </h3>
                  <p class="mb-6">
                    {searchQuery
                      ? 'Try different keywords or browse categories'
                      : 'Be the first to share your thoughts and questions'}
                  </p>
                  <button
                    on:click={createNewThread}
                    class="btn-primary px-6 py-3 rounded-xl font-semibold"
                  >
                    Create First Thread
                  </button>
                </div>
              {/if}
            </div>
          </div>
        </div>
      </div>
    {/if}
  </div>
</div>

<style>
  .btn-primary {
    @apply bg-gradient-to-r from-blue-600 to-indigo-600 text-white;
    @apply hover:from-blue-700 hover:to-indigo-700;
    @apply focus:ring-4 focus:ring-blue-500/20;
    @apply shadow-lg hover:shadow-xl;
  }
</style>
