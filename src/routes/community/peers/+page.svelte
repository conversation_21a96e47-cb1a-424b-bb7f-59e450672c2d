<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { userStore, isAuthenticated } from '$lib/stores/auth';
  import PeerDiscovery from '$lib/components/community/PeerDiscovery.svelte';
  import { Button } from '$lib/components/ui';

  // Authentication state
  $: user = $userStore;
  $: authenticated = $isAuthenticated;

  onMount(() => {
    // Redirect to login if not authenticated
    if (!authenticated) {
      goto('/auth/login?redirect=/community/peers');
    }
  });

  function handleSignIn() {
    goto('/auth/login?redirect=/community/peers');
  }
</script>

<svelte:head>
  <title>Find Learning Partners - VybeCoding.ai</title>
  <meta
    name="description"
    content="Discover and connect with fellow developers who share your interests and learning goals. Find study partners, mentors, and collaborators in the VybeCoding.ai community."
  />
  <meta
    name="keywords"
    content="peer learning, study partners, developer networking, coding mentors, programming collaboration"
  />
</svelte:head>

{#if !authenticated}
  <!-- Authentication Required State -->
  <div class="auth-required">
    <div class="auth-content">
      <div class="auth-icon">🤝</div>
      <h1>Find Your Learning Partners</h1>
      <p>
        Connect with fellow developers who share your interests and learning
        goals.
      </p>

      <div class="features-preview">
        <div class="feature">
          <span class="feature-icon">🎯</span>
          <div class="feature-text">
            <h3>Smart Matching</h3>
            <p>AI-powered recommendations based on your skills and interests</p>
          </div>
        </div>

        <div class="feature">
          <span class="feature-icon">🔍</span>
          <div class="feature-text">
            <h3>Advanced Filters</h3>
            <p>Find peers by experience level, timezone, and availability</p>
          </div>
        </div>

        <div class="feature">
          <span class="feature-icon">💬</span>
          <div class="feature-text">
            <h3>Direct Messaging</h3>
            <p>Connect instantly with potential learning partners</p>
          </div>
        </div>

        <div class="feature">
          <span class="feature-icon">👥</span>
          <div class="feature-text">
            <h3>Study Groups</h3>
            <p>Join or create collaborative learning groups</p>
          </div>
        </div>
      </div>

      <div class="auth-actions">
        <Button size="lg" on:click={handleSignIn}>
          Sign In to Find Partners
        </Button>
        <p class="auth-note">
          New to VybeCoding.ai?
          <a href="/auth/register?redirect=/community/peers" class="auth-link"
            >Create an account</a
          >
        </p>
      </div>
    </div>
  </div>
{:else}
  <!-- Authenticated Peer Discovery -->
  <div class="peer-discovery-page">
    <!-- Navigation Breadcrumb -->
    <nav class="breadcrumb">
      <a href="/community" class="breadcrumb-link">Community</a>
      <span class="breadcrumb-separator">›</span>
      <span class="breadcrumb-current">Find Learning Partners</span>
    </nav>

    <!-- Main Content -->
    <PeerDiscovery maxResults={24} showFilters={true} />
  </div>
{/if}

<style>
  .auth-required {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
  }

  .auth-content {
    max-width: 800px;
    text-align: center;
    color: white;
  }

  .auth-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
  }

  .auth-content h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0 0 1rem 0;
    line-height: 1.2;
  }

  .auth-content > p {
    font-size: 1.125rem;
    opacity: 0.9;
    margin: 0 0 3rem 0;
    line-height: 1.6;
  }

  .features-preview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
  }

  .feature {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    text-align: left;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 1.5rem;
    border-radius: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .feature-icon {
    font-size: 2rem;
    line-height: 1;
    flex-shrink: 0;
  }

  .feature-text h3 {
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
    color: white;
  }

  .feature-text p {
    font-size: 0.875rem;
    opacity: 0.8;
    margin: 0;
    line-height: 1.5;
  }

  .auth-actions {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .auth-note {
    font-size: 0.875rem;
    opacity: 0.8;
    margin: 0;
  }

  .auth-link {
    color: white;
    text-decoration: underline;
    font-weight: 500;
  }

  .auth-link:hover {
    opacity: 0.8;
  }

  .peer-discovery-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  }

  .breadcrumb {
    background: white;
    border-bottom: 1px solid #e5e7eb;
    padding: 1rem 2rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
  }

  .breadcrumb-link {
    color: #6366f1;
    text-decoration: none;
    font-weight: 500;
  }

  .breadcrumb-link:hover {
    text-decoration: underline;
  }

  .breadcrumb-separator {
    color: #9ca3af;
  }

  .breadcrumb-current {
    color: #374151;
    font-weight: 500;
  }

  /* Responsive design */
  @media (max-width: 768px) {
    .auth-required {
      padding: 1rem;
    }

    .auth-content h1 {
      font-size: 2rem;
    }

    .auth-content > p {
      font-size: 1rem;
    }

    .features-preview {
      grid-template-columns: 1fr;
      gap: 1.5rem;
      margin-bottom: 2rem;
    }

    .feature {
      padding: 1rem;
    }

    .feature-icon {
      font-size: 1.5rem;
    }

    .feature-text h3 {
      font-size: 1rem;
    }

    .feature-text p {
      font-size: 0.8rem;
    }

    .breadcrumb {
      padding: 0.75rem 1rem;
    }
  }

  @media (max-width: 480px) {
    .auth-content h1 {
      font-size: 1.75rem;
    }

    .auth-icon {
      font-size: 3rem;
    }

    .feature {
      flex-direction: column;
      text-align: center;
      gap: 0.75rem;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .feature {
      background: rgba(0, 0, 0, 0.8);
      border: 2px solid white;
    }

    .auth-link {
      text-decoration: underline;
      text-decoration-thickness: 2px;
    }

    .breadcrumb {
      border-bottom: 2px solid #000;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .feature {
      backdrop-filter: none;
    }
  }

  /* Dark mode support */
  @media (prefers-color-scheme: dark) {
    .peer-discovery-page {
      background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    }

    .breadcrumb {
      background: #1f2937;
      border-color: #374151;
    }

    .breadcrumb-link {
      color: #8b5cf6;
    }

    .breadcrumb-current {
      color: #f9fafb;
    }

    .breadcrumb-separator {
      color: #6b7280;
    }
  }

  /* Focus styles for accessibility */
  .auth-link:focus {
    outline: 2px solid white;
    outline-offset: 2px;
    border-radius: 2px;
  }

  .breadcrumb-link:focus {
    outline: 2px solid #6366f1;
    outline-offset: 2px;
    border-radius: 2px;
  }
</style>
