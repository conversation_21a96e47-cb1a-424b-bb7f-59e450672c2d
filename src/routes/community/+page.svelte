<script lang="ts">
  import {
    Users,
    MessageCircle,
    Code,
    Trophy,
    Calendar,
    ExternalLink,
    Newspaper,
    TrendingUp,
    Shield,
    Eye,
    ArrowRight,
  } from 'lucide-svelte';
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  // Simple authentication state for demo
  let authenticated = false;
  let mounted = false;

  onMount(() => {
    mounted = true;
    initializeScrollAnimations();
    initializeParticleBackground();
    console.log('AI Collective warfare page deployed');
  });

  // Scroll-triggered animations
  function initializeScrollAnimations() {
    const observer = new IntersectionObserver(
      entries => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('animate-in');
          }
        });
      },
      { threshold: 0.1 }
    );

    setTimeout(() => {
      document.querySelectorAll('.animate-on-scroll').forEach(el => {
        observer.observe(el);
      });
    }, 100);
  }

  // Particle background
  function initializeParticleBackground() {
    const canvas = document.createElement('canvas');
    canvas.className = 'particle-canvas';
    canvas.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: -1;
      opacity: 0.3;
    `;
    document.body.appendChild(canvas);

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;

    const particles: Array<{
      x: number;
      y: number;
      vx: number;
      vy: number;
      size: number;
      opacity: number;
      color: string;
    }> = [];

    for (let i = 0; i < 25; i++) {
      particles.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        vx: (Math.random() - 0.5) * 0.3,
        vy: (Math.random() - 0.5) * 0.3,
        size: Math.random() * 1.5 + 0.5,
        opacity: Math.random() * 0.3 + 0.1,
        color: Math.random() > 0.5 ? '#06b6d4' : '#ec4899',
      });
    }

    function animateParticles() {
      if (!ctx) return;
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      particles.forEach(particle => {
        particle.x += particle.vx;
        particle.y += particle.vy;

        if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
        if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;

        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fillStyle = particle.color;
        ctx.globalAlpha = particle.opacity;
        ctx.fill();
      });

      requestAnimationFrame(animateParticles);
    }
    animateParticles();

    window.addEventListener('resize', () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    });
  }

  const communityStats = [
    { value: '15,000+', label: 'AI Developers', icon: Users },
    { value: '2,500+', label: 'Live AI Projects', icon: Code },
    { value: '500+', label: 'Daily Discussions', icon: MessageCircle },
    { value: '100%', label: 'AI-Powered Learning', icon: Trophy },
  ];

  const featuredProjects = [
    {
      title: 'VybeCoding.ai Platform',
      description:
        'The main educational platform built with SvelteKit, featuring AI-powered learning paths and real-time collaboration.',
      author: 'VybeCoding Team',
      tech: ['SvelteKit', 'TypeScript', 'Tailwind CSS', 'Appwrite'],
      likes: 892,
      comments: 156,
      featured: true,
    },
    {
      title: 'BMAD Method Framework',
      description:
        'Breakthrough Method of Agile Development - our proprietary AI-driven development methodology.',
      author: 'VybeCoding Research',
      tech: ['Python', 'AI Agents', 'Automation', 'Project Management'],
      likes: 567,
      comments: 89,
      featured: false,
    },
    {
      title: 'Multi-Agent System (MAS)',
      description:
        'Our autonomous multi-agent system that generates and manages Vybe Qubes with intelligent coordination.',
      author: 'VybeCoding AI Team',
      tech: ['Python', 'FastAPI', 'Redis', 'PostgreSQL', 'Docker'],
      likes: 743,
      comments: 124,
      featured: true,
    },
  ];

  const upcomingEvents = [
    {
      title: 'Vybe Method Workshop: AI-Native Development',
      description:
        'Learn how to build AI applications from the ground up using our revolutionary methodology.',
      type: 'Workshop',
      date: '2025-01-25',
      time: '2:00 PM EST',
      attendees: 247,
    },
    {
      title: 'AI Collective Showcase: January Projects',
      description:
        'Present your latest AI projects and get feedback from the community and expert mentors.',
      type: 'Showcase',
      date: '2025-01-30',
      time: '7:00 PM EST',
      attendees: 189,
    },
  ];

  const studyGroups = [
    {
      name: 'AI Development Fundamentals',
      level: 'Beginner',
      focus:
        'Learn the basics of AI development, prompt engineering, and the Vybe Method principles.',
      members: 145,
      meetingTime: 'Tuesdays 6 PM EST',
    },
    {
      name: 'Advanced Multi-Agent Systems',
      level: 'Advanced',
      focus:
        'Deep dive into complex MAS architectures, coordination protocols, and real-world applications.',
      members: 73,
      meetingTime: 'Thursdays 8 PM EST',
    },
    {
      name: 'AI Business Applications',
      level: 'Intermediate',
      focus:
        'Explore how to build profitable AI applications and monetize your AI development skills.',
      members: 167,
      meetingTime: 'Saturdays 10 AM EST',
    },
  ];
</script>

<svelte:head>
  <title>AI Collective - VybeCoding.ai | Join the Future Builders Network</title
  >
  <meta
    name="description"
    content="JOIN THE AI COLLECTIVE! Elite network of developers building the future with AI. Share projects, learn together, and master AI development with the Vybe Method."
  />
  <meta
    name="keywords"
    content="AI collective, elite developers, AI community, developer network, AI revolution, Vybe Method"
  />
</svelte:head>

<!-- Maya's Design System: Community-focused accessible layout with 2025 cutting-edge design -->
{#if authenticated}
  <!-- Authenticated users see the community dashboard -->
  <div class="min-h-screen bg-slate-900 text-white p-8">
    <h1 class="text-4xl font-bold mb-8">Community Dashboard</h1>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <!-- Active Discussions -->
      <div class="bg-slate-800 rounded-lg p-6">
        <h2 class="text-xl font-bold mb-4">🔥 Active Discussions</h2>
        <p class="text-slate-300">Join ongoing conversations about AI-powered development.</p>
        <button class="mt-4 bg-cyan-600 hover:bg-cyan-700 px-4 py-2 rounded-lg transition-colors">
          View Discussions
        </button>
      </div>

      <!-- Your Projects -->
      <div class="bg-slate-800 rounded-lg p-6">
        <h2 class="text-xl font-bold mb-4">🚀 Your Projects</h2>
        <p class="text-slate-300">Manage and showcase your VybeCoding projects.</p>
        <button class="mt-4 bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded-lg transition-colors">
          View Projects
        </button>
      </div>

      <!-- Learning Progress -->
      <div class="bg-slate-800 rounded-lg p-6">
        <h2 class="text-xl font-bold mb-4">📚 Learning Progress</h2>
        <p class="text-slate-300">Track your progress through courses and challenges.</p>
        <button class="mt-4 bg-green-600 hover:bg-green-700 px-4 py-2 rounded-lg transition-colors">
          View Progress
        </button>
      </div>
    </div>
  </div>
{:else}
  <!-- Non-authenticated users see the marketing page -->
  <main>
    <!-- Hero Section with Advanced Glassmorphism -->
    <section
      class="relative py-32 overflow-hidden bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50 dark:from-slate-900 dark:via-blue-900 dark:to-purple-900"
      aria-labelledby="community-hero-heading"
    >
      <!-- Animated Background -->
      <div
        class="absolute inset-0 bg-gradient-to-br from-blue-600/10 via-purple-600/10 to-pink-600/10 dark:from-blue-600/20 dark:via-purple-600/20 dark:to-pink-600/20"
      ></div>
      <div
        class="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.15),transparent_50%)] dark:bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.3),transparent_50%)]"
      ></div>
      <div
        class="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(139,92,246,0.15),transparent_50%)] dark:bg-[radial-gradient(circle_at_70%_80%,rgba(139,92,246,0.3),transparent_50%)]"
      ></div>

      <!-- Glassmorphism Container -->
      <div class="relative container-glass">
        <div class="text-center max-w-5xl mx-auto">
          <!-- Enhanced Title with Advanced Gradient Animation -->
          <h1
            id="community-hero-heading"
            class="text-5xl md:text-7xl font-black mb-8 leading-tight"
            style="line-height: 1.2;"
          >
            <span class="text-gray-900 dark:text-white">Join the</span>
            <span
              class="gradient-vybecoding bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 animate-pulse"
              >AI Collective</span
            >
          </h1>

          <!-- Glassmorphism Description Card -->
          <div
            class="glass-card p-8 mb-12 bg-white/80 dark:bg-slate-800/80 backdrop-blur-xl border border-gray-200/50 dark:border-slate-700/50"
          >
            <p
              class="text-xl md:text-2xl text-gray-700 dark:text-white/90 leading-relaxed font-medium"
            >
              Connect with elite AI developers, share breakthrough projects,
              learn the Vybe Method, and build the future of artificial
              intelligence together in our exclusive network.
            </p>
          </div>

          <!-- Advanced Button Group with Micro-interactions -->
          <div class="flex flex-col sm:flex-row gap-6 justify-center">
            <a
              href="https://discord.gg/vybecoding"
              target="_blank"
              class="btn-gradient flex items-center justify-center"
            >
              <svg
                class="w-6 h-6 mr-3"
                aria-hidden="true"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  d="M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0 12.64 12.64 0 0 0-.617-1.25.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057 19.9 19.9 0 0 0 5.993 3.03.078.078 0 0 0 .084-.028 14.09 14.09 0 0 0 1.226-1.994.076.076 0 0 0-.041-.106 13.107 13.107 0 0 1-1.872-.892.077.077 0 0 1-.008-.128 10.2 10.2 0 0 0 .372-.292.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127 12.299 12.299 0 0 1-1.873.892.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028 19.839 19.839 0 0 0 6.002-3.03.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.956-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.946 2.418-2.157 2.418z"
                />
              </svg>
              Join Discord
            </a>

            <a
              href="https://github.com/vybecoding"
              target="_blank"
              class="btn-glass flex items-center justify-center"
            >
              <svg
                class="w-6 h-6 mr-3"
                aria-hidden="true"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"
                />
              </svg>
              GitHub Community
            </a>
          </div>
        </div>
      </div>
    </section>

    <!-- Community Stats with Advanced Glassmorphism Cards -->
    <section
      class="relative py-24 overflow-hidden bg-white dark:bg-slate-900"
      aria-labelledby="stats-heading"
    >
      <!-- Background Effects -->
      <div
        class="absolute inset-0 bg-gradient-to-r from-blue-50/50 to-purple-50/50 dark:from-blue-950/20 dark:to-purple-950/20"
      ></div>

      <div class="relative container-glass">
        <h2 id="stats-heading" class="sr-only">Community Statistics</h2>

        <!-- Stats Grid with Glassmorphism -->
        <div class="grid-auto-4 text-center">
          {#each communityStats as stat}
            <div
              class="glass-card p-8 group bg-white/80 dark:bg-slate-800/80 backdrop-blur-xl border border-gray-200/50 dark:border-slate-700/50"
            >
              <!-- Animated Background Gradient -->
              <div
                class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5 dark:from-blue-500/10 dark:to-purple-500/10 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"
              ></div>

              <div class="relative flex flex-col items-center">
                <!-- Icon with Glow Effect -->
                <div
                  class="w-16 h-16 rounded-2xl bg-gradient-to-br from-blue-500/10 to-purple-500/10 dark:from-blue-500/20 dark:to-purple-500/20 flex items-center justify-center mb-6 group-hover:shadow-lg group-hover:shadow-blue-500/25 transition-all duration-300"
                >
                  <svelte:component
                    this={stat.icon}
                    class="w-8 h-8 text-blue-600 dark:text-blue-400 group-hover:text-blue-500 dark:group-hover:text-blue-300 transition-colors duration-300"
                    aria-hidden="true"
                  />
                </div>

                <!-- Value with Enhanced Typography -->
                <div
                  class="text-4xl md:text-5xl font-black text-gray-900 dark:text-white mb-3 group-hover:text-blue-600 dark:group-hover:text-blue-100 transition-colors duration-300"
                >
                  {stat.value}
                </div>

                <!-- Label with Subtle Animation -->
                <div
                  class="text-gray-600 dark:text-white/70 font-medium group-hover:text-gray-800 dark:group-hover:text-white/90 transition-colors duration-300"
                >
                  {stat.label}
                </div>
              </div>
            </div>
          {/each}
        </div>
      </div>
    </section>

    <!-- AI News Hub Preview with Advanced Glassmorphism -->
    <section
      class="relative py-32 overflow-hidden bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50 dark:from-slate-900 dark:via-blue-900 dark:to-purple-900"
      aria-labelledby="news-heading"
    >
      <!-- Dynamic Background -->
      <div
        class="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-purple-500/5 to-pink-500/5 dark:from-blue-500/10 dark:via-purple-500/10 dark:to-pink-500/10"
      ></div>
      <div
        class="absolute inset-0 bg-[radial-gradient(circle_at_20%_50%,rgba(59,130,246,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_20%_50%,rgba(59,130,246,0.2),transparent_50%)]"
      ></div>
      <div
        class="absolute inset-0 bg-[radial-gradient(circle_at_80%_20%,rgba(139,92,246,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_80%_20%,rgba(139,92,246,0.2),transparent_50%)]"
      ></div>

      <div class="relative container-glass">
        <!-- Enhanced Title Section -->
        <div class="text-center mb-20">
          <h2
            id="news-heading"
            class="text-4xl md:text-6xl font-black mb-6 text-gray-900 dark:text-white"
            style="line-height: 1.2;"
          >
            Latest AI <span
              class="gradient-vybecoding bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600"
              >News & Insights</span
            >
          </h2>

          <!-- Glassmorphism Description Card -->
          <div
            class="glass-card max-w-4xl mx-auto p-8 bg-white/80 dark:bg-slate-800/80 backdrop-blur-xl border border-gray-200/50 dark:border-slate-700/50"
          >
            <p
              class="text-xl md:text-2xl text-gray-700 dark:text-white/90 leading-relaxed font-medium"
            >
              Stay ahead of the curve with curated AI news, security alerts, and
              trending topics from our autonomous monitoring system.
            </p>
          </div>
        </div>

        <!-- Advanced News Stats Grid -->
        <div class="grid-auto-4 mb-16">
          <div
            class="glass-card p-8 text-center group bg-white/80 dark:bg-slate-800/80 backdrop-blur-xl border border-gray-200/50 dark:border-slate-700/50"
          >
            <div
              class="w-16 h-16 rounded-2xl bg-gradient-to-br from-blue-500/10 to-blue-600/10 dark:from-blue-500/20 dark:to-blue-600/20 flex items-center justify-center mb-6 mx-auto group-hover:shadow-lg group-hover:shadow-blue-500/25 transition-all duration-300"
            >
              <Newspaper class="w-8 h-8 text-blue-600 dark:text-blue-400" />
            </div>
            <div
              class="text-3xl md:text-4xl font-black text-gray-900 dark:text-white mb-2"
            >
              12
            </div>
            <div class="text-gray-600 dark:text-white/70 font-medium">
              Articles Today
            </div>
          </div>

          <div
            class="glass-card p-8 text-center group bg-white/80 dark:bg-slate-800/80 backdrop-blur-xl border border-gray-200/50 dark:border-slate-700/50"
          >
            <div
              class="w-16 h-16 rounded-2xl bg-gradient-to-br from-green-500/10 to-green-600/10 dark:from-green-500/20 dark:to-green-600/20 flex items-center justify-center mb-6 mx-auto group-hover:shadow-lg group-hover:shadow-green-500/25 transition-all duration-300"
            >
              <Eye class="w-8 h-8 text-green-600 dark:text-green-400" />
            </div>
            <div
              class="text-3xl md:text-4xl font-black text-gray-900 dark:text-white mb-2"
            >
              89
            </div>
            <div class="text-gray-600 dark:text-white/70 font-medium">
              Active Readers
            </div>
          </div>

          <div
            class="glass-card p-8 text-center group bg-white/80 dark:bg-slate-800/80 backdrop-blur-xl border border-gray-200/50 dark:border-slate-700/50"
          >
            <div
              class="w-16 h-16 rounded-2xl bg-gradient-to-br from-red-500/10 to-red-600/10 dark:from-red-500/20 dark:to-red-600/20 flex items-center justify-center mb-6 mx-auto group-hover:shadow-lg group-hover:shadow-red-500/25 transition-all duration-300"
            >
              <Shield class="w-8 h-8 text-red-600 dark:text-red-400" />
            </div>
            <div
              class="text-3xl md:text-4xl font-black text-gray-900 dark:text-white mb-2"
            >
              3
            </div>
            <div class="text-gray-600 dark:text-white/70 font-medium">
              Security Alerts
            </div>
          </div>

          <div
            class="glass-card p-8 text-center group bg-white/80 dark:bg-slate-800/80 backdrop-blur-xl border border-gray-200/50 dark:border-slate-700/50"
          >
            <div
              class="w-16 h-16 rounded-2xl bg-gradient-to-br from-purple-500/10 to-purple-600/10 dark:from-purple-500/20 dark:to-purple-600/20 flex items-center justify-center mb-6 mx-auto group-hover:shadow-lg group-hover:shadow-purple-500/25 transition-all duration-300"
            >
              <TrendingUp
                class="w-8 h-8 text-purple-600 dark:text-purple-400"
              />
            </div>
            <div
              class="text-3xl md:text-4xl font-black text-gray-900 dark:text-white mb-2"
            >
              7
            </div>
            <div class="text-gray-600 dark:text-white/70 font-medium">
              Trending Topics
            </div>
          </div>
        </div>

        <!-- Featured Security Alert with Enhanced Design -->
        <div class="mb-12">
          <h3
            class="text-2xl font-bold text-gray-900 dark:text-white mb-8 text-center"
          >
            Featured Security Alert
          </h3>
          <div
            class="glass-card p-8 bg-gradient-to-r from-red-500/10 to-orange-500/10 dark:from-red-500/20 dark:to-orange-500/20 border-red-500/20 dark:border-red-500/30 bg-white/80 dark:bg-slate-800/80 backdrop-blur-xl"
          >
            <div class="flex items-start gap-6">
              <div class="p-4 bg-red-500/20 dark:bg-red-500/30 rounded-2xl">
                <Shield class="w-8 h-8 text-red-600 dark:text-red-300" />
              </div>
              <div class="flex-1">
                <h4
                  class="text-xl font-bold text-gray-900 dark:text-white mb-3"
                >
                  Security Alert: Lovable AI Platform Vulnerabilities
                </h4>
                <p
                  class="text-gray-700 dark:text-white/80 mb-6 text-lg leading-relaxed"
                >
                  Critical security flaws discovered in popular AI coding
                  platform. Learn how the Vybe Method's security-first approach
                  prevents similar issues.
                </p>
                <div
                  class="flex items-center gap-6 text-gray-600 dark:text-white/60 mb-4"
                >
                  <span class="font-medium">By VybeCoding Security Team</span>
                  <span>•</span>
                  <span>5 min read</span>
                  <span>•</span>
                  <span>2 hours ago</span>
                </div>
                <div class="flex gap-3">
                  <span class="badge badge-destructive">Security</span>
                  <span class="badge badge-outline">Vulnerability</span>
                  <span class="badge badge-outline">Best Practices</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Trending Topics -->
        <div class="mb-12">
          <h3
            class="text-2xl font-bold text-gray-900 dark:text-white mb-8 text-center"
          >
            Trending in AI
          </h3>
          <div class="flex flex-wrap gap-4 justify-center">
            <button
              class="glass-card px-6 py-3 flex items-center gap-3 hover:scale-105 transition-all duration-300 bg-white/80 dark:bg-slate-800/80 backdrop-blur-xl border border-gray-200/50 dark:border-slate-700/50 hover:bg-blue-50 dark:hover:bg-slate-700/90"
            >
              <TrendingUp class="w-5 h-5 text-green-600 dark:text-green-400" />
              <span
                class="font-medium text-gray-900 dark:text-white hover:text-blue-700 dark:hover:text-blue-300 transition-colors"
                >AI Security</span
              >
              <span class="text-sm text-green-600 dark:text-green-400"
                >+15%</span
              >
            </button>
            <button
              class="glass-card px-6 py-3 flex items-center gap-3 hover:scale-105 transition-all duration-300 bg-white/80 dark:bg-slate-800/80 backdrop-blur-xl border border-gray-200/50 dark:border-slate-700/50 hover:bg-blue-50 dark:hover:bg-slate-700/90"
            >
              <TrendingUp class="w-5 h-5 text-green-600 dark:text-green-400" />
              <span
                class="font-medium text-gray-900 dark:text-white hover:text-blue-700 dark:hover:text-blue-300 transition-colors"
                >FOSS AI Tools</span
              >
              <span class="text-sm text-green-600 dark:text-green-400"
                >+12%</span
              >
            </button>
            <button
              class="glass-card px-6 py-3 flex items-center gap-3 hover:scale-105 transition-all duration-300 bg-white/80 dark:bg-slate-800/80 backdrop-blur-xl border border-gray-200/50 dark:border-slate-700/50 hover:bg-blue-50 dark:hover:bg-slate-700/90"
            >
              <TrendingUp class="w-5 h-5 text-green-600 dark:text-green-400" />
              <span
                class="font-medium text-gray-900 dark:text-white hover:text-blue-700 dark:hover:text-blue-300 transition-colors"
                >LLM Integration</span
              >
              <span class="text-sm text-green-600 dark:text-green-400">+8%</span
              >
            </button>
          </div>
        </div>

        <!-- Call to Action -->
        <div class="text-center">
          <button
            onclick={() => goto('/news')}
            class="btn-gradient flex items-center justify-center"
          >
            <Newspaper class="w-6 h-6 mr-3" />
            Explore All AI News
            <ArrowRight class="w-5 h-5 ml-3" />
          </button>
        </div>
      </div>
    </section>

    <!-- Featured Projects with Neomorphism Design -->
    <section class="relative py-32" aria-labelledby="projects-heading">
      <div class="container-glass">
        <div class="text-center mb-20">
          <h2
            id="projects-heading"
            class="text-4xl md:text-6xl font-black mb-6 text-gray-900 dark:text-white"
            style="line-height: 1.2;"
          >
            Featured Community <span
              class="gradient-vybecoding bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-purple-600"
              >Projects</span
            >
          </h2>
          <div class="max-w-4xl mx-auto">
            <p
              class="text-xl md:text-2xl text-gray-600 dark:text-gray-300 leading-relaxed font-medium"
            >
              Discover amazing AI applications built by our community members
              using the Vybe Method.
            </p>
          </div>
        </div>

        <div class="grid-auto">
          {#each featuredProjects as project}
            <div class="card-neo h-full">
              <div class="flex flex-col h-full">
                <div class="flex items-start justify-between mb-6">
                  <h3
                    class="text-xl font-bold flex-1 text-gray-900 dark:text-white"
                  >
                    {project.title}
                  </h3>
                  {#if project.featured}
                    <span class="badge ml-3">Featured</span>
                  {/if}
                </div>

                <p
                  class="text-gray-600 dark:text-gray-300 mb-6 flex-1 leading-relaxed"
                >
                  {project.description}
                </p>

                <div class="flex flex-wrap gap-2 mb-6">
                  {#each project.tech as tech}
                    <span class="badge badge-outline text-xs">{tech}</span>
                  {/each}
                </div>

                <div
                  class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400"
                >
                  <span class="font-medium">by {project.author}</span>
                  <div class="flex items-center gap-4">
                    <span class="flex items-center gap-1">
                      <svg
                        class="w-4 h-4"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        aria-hidden="true"
                      >
                        <path
                          d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"
                        />
                      </svg>
                      {project.likes}
                    </span>
                    <span class="flex items-center gap-1">
                      <MessageCircle class="w-4 h-4" aria-hidden="true" />
                      {project.comments}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          {/each}
        </div>

        <div class="text-center mt-16">
          <a
            href="/vybeqube"
            class="btn-glass inline-flex items-center justify-center"
          >
            View All Projects
            <ExternalLink class="w-5 h-5 ml-3" aria-hidden="true" />
          </a>
        </div>
      </div>
    </section>

    <!-- Upcoming Events with Glassmorphism -->
    <section
      class="relative py-32 overflow-hidden bg-gradient-to-br from-slate-50 via-purple-50 to-pink-50 dark:from-slate-900 dark:via-purple-900 dark:to-pink-900"
      aria-labelledby="events-heading"
    >
      <!-- Background Effects -->
      <div
        class="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-pink-500/5 dark:from-purple-500/10 dark:to-pink-500/10"
      ></div>

      <div class="relative container-glass">
        <div class="text-center mb-20">
          <h2
            id="events-heading"
            class="text-4xl md:text-6xl font-black mb-6 text-gray-900 dark:text-white"
            style="line-height: 1.2;"
          >
            Upcoming <span
              class="gradient-vybecoding bg-clip-text text-transparent bg-gradient-to-r from-purple-600 to-pink-600"
              >Events</span
            >
          </h2>
          <div
            class="glass-card max-w-4xl mx-auto p-8 bg-white/80 dark:bg-slate-800/80 backdrop-blur-xl border border-gray-200/50 dark:border-slate-700/50"
          >
            <p
              class="text-xl md:text-2xl text-gray-700 dark:text-white/90 leading-relaxed font-medium"
            >
              Join workshops, showcases, and networking events to accelerate
              your AI learning journey.
            </p>
          </div>
        </div>

        <div class="max-w-5xl mx-auto space-y-8">
          {#each upcomingEvents as event}
            <div
              class="glass-card p-8 bg-white/80 dark:bg-slate-800/80 backdrop-blur-xl border border-gray-200/50 dark:border-slate-700/50"
            >
              <div class="flex flex-col md:flex-row md:items-center gap-6">
                <div class="flex-1">
                  <div class="flex items-center gap-4 mb-4">
                    <h3
                      class="text-2xl font-bold text-gray-900 dark:text-white"
                    >
                      {event.title}
                    </h3>
                    <span class="badge badge-outline">{event.type}</span>
                  </div>
                  <p
                    class="text-gray-700 dark:text-white/80 mb-6 text-lg leading-relaxed"
                  >
                    {event.description}
                  </p>
                  <div
                    class="flex items-center gap-6 text-gray-600 dark:text-white/60"
                  >
                    <span class="flex items-center gap-2">
                      <Calendar class="w-5 h-5" aria-hidden="true" />
                      {new Date(event.date).toLocaleDateString()} at {event.time}
                    </span>
                    <span class="flex items-center gap-2">
                      <Users class="w-5 h-5" aria-hidden="true" />
                      {event.attendees} attending
                    </span>
                  </div>
                </div>
                <div class="flex-shrink-0">
                  <button class="btn-gradient">Register</button>
                </div>
              </div>
            </div>
          {/each}
        </div>
      </div>
    </section>

    <!-- Study Groups with Neomorphism -->
    <section class="relative py-32" aria-labelledby="study-groups-heading">
      <div class="container-glass">
        <div class="text-center mb-20">
          <h2
            id="study-groups-heading"
            class="text-4xl md:text-6xl font-black mb-6 text-gray-900 dark:text-white"
            style="line-height: 1.2;"
          >
            Study <span
              class="gradient-vybecoding bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-purple-600"
              >Groups</span
            >
          </h2>
          <div class="max-w-4xl mx-auto">
            <p
              class="text-xl md:text-2xl text-gray-600 dark:text-gray-300 leading-relaxed font-medium"
            >
              Learn together with peers in focused study groups covering
              different AI topics and skill levels.
            </p>
          </div>
        </div>

        <div class="grid-auto max-w-6xl mx-auto">
          {#each studyGroups as group}
            <div class="card-neo h-full">
              <div class="flex flex-col h-full">
                <div class="flex items-start justify-between mb-6">
                  <h3
                    class="text-xl font-bold flex-1 text-gray-900 dark:text-white"
                  >
                    {group.name}
                  </h3>
                  <span
                    class="badge {group.level === 'Beginner'
                      ? 'badge-outline'
                      : group.level === 'Intermediate'
                        ? ''
                        : 'badge-destructive'} ml-3"
                  >
                    {group.level}
                  </span>
                </div>

                <p
                  class="text-gray-600 dark:text-gray-300 mb-6 flex-1 leading-relaxed"
                >
                  {group.focus}
                </p>

                <div class="space-y-3 text-gray-500 dark:text-gray-400 mb-8">
                  <div class="flex items-center gap-3">
                    <Users class="w-5 h-5" aria-hidden="true" />
                    <span class="font-medium">{group.members} members</span>
                  </div>
                  <div class="flex items-center gap-3">
                    <Calendar class="w-5 h-5" aria-hidden="true" />
                    <span class="font-medium">{group.meetingTime}</span>
                  </div>
                </div>

                <button class="btn-glass w-full">Join Group</button>
              </div>
            </div>
          {/each}
        </div>
      </div>
    </section>

    <!-- CTA Section with Advanced Glassmorphism -->
    <section
      class="relative py-32 overflow-hidden"
      aria-labelledby="cta-heading"
    >
      <!-- Animated Background -->
      <div
        class="absolute inset-0 bg-gradient-to-br from-blue-600/30 via-purple-600/30 to-pink-600/30"
      ></div>
      <div
        class="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(59,130,246,0.4),transparent_70%)]"
      ></div>

      <div class="relative container-glass">
        <div class="text-center max-w-4xl mx-auto">
          <h2
            id="cta-heading"
            class="text-5xl md:text-7xl font-black mb-8 text-white leading-tight"
            style="line-height: 1.2;"
          >
            Ready to <span
              class="gradient-vybecoding bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500"
              >Connect?</span
            >
          </h2>

          <div class="glass-card p-8 mb-12">
            <p
              class="text-xl md:text-2xl text-gray-800 dark:text-white/90 leading-relaxed font-medium community-hero-text"
            >
              Join our vibrant community and accelerate your AI development
              journey with peer support and collaboration.
            </p>
          </div>

          <div class="flex flex-col sm:flex-row gap-6 justify-center">
            <a
              href="/auth/signup"
              class="btn-gradient text-lg px-10 py-5 flex items-center justify-center"
            >
              Join VybeCoding
            </a>
            <a
              href="https://discord.gg/vybecoding"
              target="_blank"
              class="btn-glass text-lg px-10 py-5 flex items-center justify-center"
            >
              Discord Community
            </a>
          </div>
        </div>
      </div>
    </section>
  </main>
{/if}
