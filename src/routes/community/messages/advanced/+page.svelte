<!--
  Advanced Messaging Features Demo
  TASK-1-004-004: Advanced Messaging Features
  Showcase of all advanced messaging capabilities
-->

<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { authService } from '$lib/services/auth';
  import MessageThread from '$lib/components/community/MessageThread.svelte';
  import GroupChat from '$lib/components/community/GroupChat.svelte';
  import MessageList from '$lib/components/community/MessageList.svelte';

  // State
  let isAuthenticated = false;
  let currentView: 'overview' | 'direct' | 'group' | 'features' = 'overview';
  let selectedDemo = '';

  // Demo data
  const demoFeatures = [
    {
      id: 'group-messaging',
      title: 'Group Messaging',
      description: 'Create study groups and collaborate with multiple peers',
      icon: '👥',
      status: 'complete',
    },
    {
      id: 'file-sharing',
      title: 'File Sharing',
      description: 'Share code files, documents, and educational resources',
      icon: '📎',
      status: 'complete',
    },
    {
      id: 'message-threading',
      title: 'Message Threading',
      description: 'Reply to specific messages and create conversation threads',
      icon: '💬',
      status: 'complete',
    },
    {
      id: 'enhanced-reactions',
      title: 'Enhanced Reactions',
      description: 'Express yourself with emoji reactions and quick responses',
      icon: '😊',
      status: 'complete',
    },
    {
      id: 'real-time-sync',
      title: 'Real-time Sync',
      description: 'Instant message delivery and live typing indicators',
      icon: '⚡',
      status: 'complete',
    },
    {
      id: 'content-moderation',
      title: 'Content Moderation',
      description: 'AI-powered content filtering for safe learning environment',
      icon: '🛡️',
      status: 'complete',
    },
    {
      id: 'search-filtering',
      title: 'Advanced Search',
      description: 'Find messages, files, and conversations quickly',
      icon: '🔍',
      status: 'complete',
    },
    {
      id: 'voice-video',
      title: 'Voice & Video Calls',
      description: 'Integrated WebRTC for real-time communication',
      icon: '📹',
      status: 'planned',
    },
  ];

  onMount(async () => {
    const user = await authService.getCurrentUser();
    if (!user) {
      goto('/auth/login?redirect=/community/messages/advanced');
      return;
    }
    isAuthenticated = true;
  });

  function selectDemo(featureId: string) {
    selectedDemo = featureId;

    switch (featureId) {
      case 'group-messaging':
        currentView = 'group';
        break;
      case 'message-threading':
      case 'enhanced-reactions':
        currentView = 'direct';
        break;
      default:
        currentView = 'features';
    }
  }
</script>

<svelte:head>
  <title>Advanced Messaging Features - VybeCoding.ai</title>
  <meta
    name="description"
    content="Explore advanced messaging features for enhanced peer collaboration"
  />
</svelte:head>

{#if !isAuthenticated}
  <div class="auth-required">
    <div class="auth-content">
      <div class="auth-icon">🔒</div>
      <h2>Authentication Required</h2>
      <p>Please log in to explore advanced messaging features.</p>
      <a
        href="/auth/login?redirect=/community/messages/advanced"
        class="login-btn"
      >
        Log In
      </a>
    </div>
  </div>
{:else}
  <div class="advanced-messaging">
    <!-- Header -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1 class="page-title">Advanced Messaging Features</h1>
          <p class="page-description">
            Explore cutting-edge communication tools for enhanced learning
            collaboration
          </p>
        </div>

        <div class="header-actions">
          <button
            type="button"
            class="action-btn"
            class:active={currentView === 'overview'}
            on:click={() => (currentView = 'overview')}
          >
            <span class="btn-icon">📋</span>
            Overview
          </button>
          <a href="/community/messages" class="action-btn secondary">
            <span class="btn-icon">💬</span>
            Messages
          </a>
        </div>
      </div>
    </div>

    <!-- Content -->
    <div class="content-container">
      {#if currentView === 'overview'}
        <!-- Features Overview -->
        <div class="features-overview">
          <div class="features-grid">
            {#each demoFeatures as feature}
              <div
                class="feature-card"
                class:selected={selectedDemo === feature.id}
                on:click={() => selectDemo(feature.id)}
                role="button"
                tabindex="0"
                on:keydown={e => e.key === 'Enter' && selectDemo(feature.id)}
              >
                <div class="feature-header">
                  <div class="feature-icon">{feature.icon}</div>
                  <div
                    class="feature-status"
                    class:complete={feature.status === 'complete'}
                  >
                    {feature.status === 'complete' ? '✅' : '🚧'}
                  </div>
                </div>

                <div class="feature-content">
                  <h3 class="feature-title">{feature.title}</h3>
                  <p class="feature-description">{feature.description}</p>
                </div>

                <div class="feature-footer">
                  <span class="feature-status-text">
                    {feature.status === 'complete'
                      ? 'Available'
                      : 'In Development'}
                  </span>
                </div>
              </div>
            {/each}
          </div>

          <!-- Implementation Stats -->
          <div class="implementation-stats">
            <div class="stats-card">
              <div class="stat-number">7</div>
              <div class="stat-label">Features Complete</div>
            </div>
            <div class="stats-card">
              <div class="stat-number">1</div>
              <div class="stat-label">In Development</div>
            </div>
            <div class="stats-card">
              <div class="stat-number">100%</div>
              <div class="stat-label">FOSS Stack</div>
            </div>
          </div>
        </div>
      {:else if currentView === 'direct'}
        <!-- Direct Messaging Demo -->
        <div class="demo-container">
          <div class="demo-header">
            <h2>Direct Messaging with Threading & Reactions</h2>
            <p>Experience enhanced peer-to-peer communication</p>
          </div>

          <div class="demo-content">
            <MessageThread partnerId="demo-user-1" partnerName="Alex Chen" />
          </div>
        </div>
      {:else if currentView === 'group'}
        <!-- Group Messaging Demo -->
        <div class="demo-container">
          <div class="demo-header">
            <h2>Group Messaging & Study Groups</h2>
            <p>Collaborate with multiple peers in study groups</p>
          </div>

          <div class="demo-content">
            <GroupChat
              groupId="demo-group-1"
              groupName="JavaScript Study Group"
            />
          </div>
        </div>
      {:else if currentView === 'features'}
        <!-- Feature Details -->
        <div class="feature-details">
          <div class="feature-detail-header">
            <h2>Feature Implementation Details</h2>
            <p>Technical specifications and capabilities</p>
          </div>

          <div class="feature-specs">
            <div class="spec-section">
              <h3>🏗️ Architecture</h3>
              <ul>
                <li>Real-time WebSocket communication</li>
                <li>Appwrite.io backend with PostgreSQL</li>
                <li>SvelteKit frontend with TypeScript</li>
                <li>100% FOSS technology stack</li>
              </ul>
            </div>

            <div class="spec-section">
              <h3>🔒 Security & Moderation</h3>
              <ul>
                <li>Rule-based content filtering</li>
                <li>Educational context validation</li>
                <li>File type restrictions</li>
                <li>User reporting system</li>
              </ul>
            </div>

            <div class="spec-section">
              <h3>⚡ Performance</h3>
              <ul>
                <li>Optimized message pagination</li>
                <li>Efficient real-time subscriptions</li>
                <li>Debounced search queries</li>
                <li>Lazy loading for large conversations</li>
              </ul>
            </div>

            <div class="spec-section">
              <h3>📱 Accessibility</h3>
              <ul>
                <li>WCAG 2.1 AA compliance</li>
                <li>Keyboard navigation support</li>
                <li>Screen reader compatibility</li>
                <li>High contrast mode</li>
              </ul>
            </div>
          </div>
        </div>
      {/if}
    </div>
  </div>
{/if}

<style>
  .auth-required {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
    padding: 2rem;
  }

  .auth-content {
    text-align: center;
    max-width: 400px;
  }

  .auth-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
  }

  .auth-content h2 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
  }

  .auth-content p {
    color: var(--text-secondary);
    margin-bottom: 2rem;
    line-height: 1.6;
  }

  .login-btn {
    display: inline-flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    background: var(--primary-gradient);
    color: white;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
  }

  .login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .advanced-messaging {
    min-height: 100vh;
    background: var(--background-color);
  }

  .page-header {
    background: var(--surface-color);
    border-bottom: 1px solid var(--border-color);
    padding: 2rem;
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
  }

  .header-info {
    flex: 1;
  }

  .page-title {
    margin: 0 0 0.5rem 0;
    font-size: 2.5rem;
    font-weight: 700;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .page-description {
    margin: 0;
    color: var(--text-secondary);
    font-size: 1.1rem;
  }

  .header-actions {
    display: flex;
    gap: 1rem;
  }

  .action-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: var(--primary-color);
    color: white;
    text-decoration: none;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .action-btn.secondary {
    background: var(--surface-elevated);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
  }

  .action-btn.active {
    background: var(--primary-hover);
  }

  .action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .btn-icon {
    font-size: 1.1rem;
  }

  .content-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
  }

  .features-overview {
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }

  .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
  }

  .feature-card {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .feature-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-color);
  }

  .feature-card.selected {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: white;
  }

  .feature-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
  }

  .feature-icon {
    font-size: 2rem;
  }

  .feature-status {
    font-size: 1.2rem;
  }

  .feature-title {
    margin: 0 0 0.5rem 0;
    font-size: 1.25rem;
    font-weight: 600;
  }

  .feature-description {
    margin: 0 0 1rem 0;
    color: var(--text-secondary);
    line-height: 1.5;
  }

  .feature-card.selected .feature-description {
    color: rgba(255, 255, 255, 0.9);
  }

  .feature-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .feature-status-text {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--success-color);
  }

  .implementation-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }

  .stats-card {
    background: var(--surface-elevated);
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
  }

  .stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
  }

  .stat-label {
    color: var(--text-secondary);
    font-weight: 500;
  }

  .demo-container {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .demo-header {
    text-align: center;
  }

  .demo-header h2 {
    margin: 0 0 0.5rem 0;
    color: var(--text-primary);
  }

  .demo-header p {
    margin: 0;
    color: var(--text-secondary);
  }

  .demo-content {
    height: 600px;
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid var(--border-color);
  }

  .feature-details {
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }

  .feature-detail-header {
    text-align: center;
  }

  .feature-detail-header h2 {
    margin: 0 0 0.5rem 0;
    color: var(--text-primary);
  }

  .feature-detail-header p {
    margin: 0;
    color: var(--text-secondary);
  }

  .feature-specs {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
  }

  .spec-section {
    background: var(--surface-color);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid var(--border-color);
  }

  .spec-section h3 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
  }

  .spec-section ul {
    margin: 0;
    padding-left: 1.5rem;
    color: var(--text-secondary);
  }

  .spec-section li {
    margin-bottom: 0.5rem;
    line-height: 1.5;
  }

  /* Mobile responsiveness */
  @media (max-width: 768px) {
    .page-header {
      padding: 1rem;
    }

    .header-content {
      flex-direction: column;
      gap: 1rem;
      align-items: flex-start;
    }

    .page-title {
      font-size: 2rem;
    }

    .content-container {
      padding: 1rem;
    }

    .features-grid {
      grid-template-columns: 1fr;
    }

    .implementation-stats {
      grid-template-columns: 1fr;
    }

    .feature-specs {
      grid-template-columns: 1fr;
    }

    .demo-content {
      height: 500px;
    }
  }
</style>
