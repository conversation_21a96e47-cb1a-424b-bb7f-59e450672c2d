<!--
  Voice/Video Test Page
  TASK-1-004-005: Voice/Video Integration - Chunk 8
  Test page for WebRTC functionality
-->

<script lang="ts">
  import { onMount } from 'svelte';
  import { webrtcService } from '$lib/services/webrtcService';
  import CallInterface from '$lib/components/community/CallInterface.svelte';
  import CallNotification from '$lib/components/community/CallNotification.svelte';

  // State
  let testStatus = 'Not started';
  let webrtcSupported = false;
  let mediaPermissions = false;
  let localVideo: HTMLVideoElement;
  let localStream: MediaStream | null = null;

  onMount(async () => {
    await runWebRTCTests();
  });

  async function runWebRTCTests() {
    testStatus = 'Running tests...';

    // Test 1: WebRTC Support
    webrtcSupported = !!(
      window.RTCPeerConnection &&
      navigator.mediaDevices &&
      navigator.mediaDevices.getUserMedia
    );

    if (!webrtcSupported) {
      testStatus = 'WebRTC not supported';
      return;
    }

    // Test 2: Media Permissions
    try {
      localStream = await navigator.mediaDevices.getUserMedia({
        audio: true,
        video: true,
      });
      mediaPermissions = true;

      if (localVideo) {
        localVideo.srcObject = localStream;
      }

      testStatus = 'All tests passed!';
    } catch (error) {
      console.error('Media permission test failed:', error);
      testStatus = 'Media permissions denied';
    }
  }

  async function testVoiceCall() {
    try {
      await webrtcService.initialize();
      await webrtcService.initiateCall('voice', ['test-user']);
      testStatus = 'Voice call initiated';
    } catch (error) {
      console.error('Voice call test failed:', error);
      testStatus = 'Voice call failed';
    }
  }

  async function testVideoCall() {
    try {
      await webrtcService.initialize();
      await webrtcService.initiateCall('video', ['test-user']);
      testStatus = 'Video call initiated';
    } catch (error) {
      console.error('Video call test failed:', error);
      testStatus = 'Video call failed';
    }
  }

  function stopLocalStream() {
    if (localStream) {
      localStream.getTracks().forEach(track => track.stop());
      localStream = null;
      if (localVideo) {
        localVideo.srcObject = null;
      }
    }
  }
</script>

<svelte:head>
  <title>Voice/Video Test - VybeCoding.ai</title>
</svelte:head>

<div class="test-page">
  <div class="test-container">
    <h1>Voice/Video Integration Test</h1>
    <p>Testing WebRTC functionality for TASK-1-004-005</p>

    <!-- Test Status -->
    <div class="test-status">
      <h2>Test Status: {testStatus}</h2>

      <div class="test-results">
        <div
          class="test-item"
          class:passed={webrtcSupported}
          class:failed={!webrtcSupported}
        >
          <span class="test-icon">{webrtcSupported ? '✅' : '❌'}</span>
          <span class="test-label">WebRTC Support</span>
        </div>

        <div
          class="test-item"
          class:passed={mediaPermissions}
          class:failed={!mediaPermissions}
        >
          <span class="test-icon">{mediaPermissions ? '✅' : '❌'}</span>
          <span class="test-label">Media Permissions</span>
        </div>
      </div>
    </div>

    <!-- Local Video Preview -->
    {#if mediaPermissions}
      <div class="video-preview">
        <h3>Local Video Preview</h3>
        <video
          bind:this={localVideo}
          autoplay
          muted
          playsinline
          class="preview-video"
        ></video>
        <button type="button" class="stop-btn" on:click={stopLocalStream}>
          Stop Camera
        </button>
      </div>
    {/if}

    <!-- Test Controls -->
    <div class="test-controls">
      <h3>Call Tests</h3>
      <div class="control-buttons">
        <button
          type="button"
          class="test-btn voice"
          on:click={testVoiceCall}
          disabled={!webrtcSupported}
        >
          📞 Test Voice Call
        </button>

        <button
          type="button"
          class="test-btn video"
          on:click={testVideoCall}
          disabled={!webrtcSupported || !mediaPermissions}
        >
          📹 Test Video Call
        </button>
      </div>
    </div>

    <!-- Implementation Status -->
    <div class="implementation-status">
      <h3>Implementation Status</h3>
      <div class="status-grid">
        <div class="status-item complete">
          <span class="status-icon">✅</span>
          <span class="status-text">WebRTC Service</span>
        </div>
        <div class="status-item complete">
          <span class="status-icon">✅</span>
          <span class="status-text">Call Interface</span>
        </div>
        <div class="status-item complete">
          <span class="status-icon">✅</span>
          <span class="status-text">Call Controls</span>
        </div>
        <div class="status-item complete">
          <span class="status-icon">✅</span>
          <span class="status-text">Participant Grid</span>
        </div>
        <div class="status-item complete">
          <span class="status-icon">✅</span>
          <span class="status-text">Call Notifications</span>
        </div>
        <div class="status-item pending">
          <span class="status-icon">🔄</span>
          <span class="status-text">Signaling Server</span>
        </div>
      </div>
    </div>

    <!-- Navigation -->
    <div class="navigation">
      <a href="/community/messages" class="nav-btn"> ← Back to Messages </a>
      <a href="/community/messages/advanced" class="nav-btn">
        Advanced Features →
      </a>
    </div>
  </div>
</div>

<!-- Call Components -->
<CallInterface />
<CallNotification />

<style>
  .test-page {
    min-height: 100vh;
    background: var(--background-color);
    padding: 2rem;
  }

  .test-container {
    max-width: 800px;
    margin: 0 auto;
    background: var(--surface-color);
    border-radius: 12px;
    padding: 2rem;
    border: 1px solid var(--border-color);
  }

  h1 {
    margin: 0 0 0.5rem 0;
    color: var(--text-primary);
    text-align: center;
  }

  p {
    margin: 0 0 2rem 0;
    color: var(--text-secondary);
    text-align: center;
  }

  .test-status {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--surface-elevated);
    border-radius: 8px;
  }

  .test-status h2 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
  }

  .test-results {
    display: flex;
    gap: 1rem;
  }

  .test-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border-radius: 6px;
    background: var(--surface-color);
  }

  .test-item.passed {
    background: var(--success-color);
    color: white;
  }

  .test-item.failed {
    background: var(--error-color);
    color: white;
  }

  .test-icon {
    font-size: 1.2rem;
  }

  .test-label {
    font-weight: 500;
  }

  .video-preview {
    margin-bottom: 2rem;
    text-align: center;
  }

  .video-preview h3 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
  }

  .preview-video {
    width: 300px;
    height: 200px;
    border-radius: 8px;
    background: #000;
    margin-bottom: 1rem;
  }

  .stop-btn {
    padding: 0.5rem 1rem;
    background: var(--error-color);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .stop-btn:hover {
    background: var(--error-hover);
  }

  .test-controls {
    margin-bottom: 2rem;
  }

  .test-controls h3 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
  }

  .control-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
  }

  .test-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 1.5rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;
  }

  .test-btn.voice {
    background: var(--primary-color);
    color: white;
  }

  .test-btn.video {
    background: var(--success-color);
    color: white;
  }

  .test-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .test-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .implementation-status {
    margin-bottom: 2rem;
  }

  .implementation-status h3 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
  }

  .status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }

  .status-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    border-radius: 6px;
    background: var(--surface-elevated);
  }

  .status-item.complete {
    border-left: 4px solid var(--success-color);
  }

  .status-item.pending {
    border-left: 4px solid var(--warning-color);
  }

  .status-icon {
    font-size: 1.1rem;
  }

  .status-text {
    font-weight: 500;
    color: var(--text-primary);
  }

  .navigation {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
  }

  .nav-btn {
    display: inline-flex;
    align-items: center;
    padding: 0.75rem 1rem;
    background: var(--surface-elevated);
    color: var(--text-primary);
    text-decoration: none;
    border-radius: 6px;
    border: 1px solid var(--border-color);
    transition: all 0.2s ease;
  }

  .nav-btn:hover {
    background: var(--surface-hover);
    transform: translateY(-2px);
  }

  /* Mobile responsiveness */
  @media (max-width: 768px) {
    .test-page {
      padding: 1rem;
    }

    .test-container {
      padding: 1rem;
    }

    .test-results {
      flex-direction: column;
    }

    .control-buttons {
      flex-direction: column;
    }

    .navigation {
      flex-direction: column;
    }

    .preview-video {
      width: 100%;
      max-width: 300px;
    }
  }
</style>
