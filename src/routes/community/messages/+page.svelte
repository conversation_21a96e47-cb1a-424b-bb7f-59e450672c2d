<!--
  Community Messages Page
  TASK-1-004-003: Basic Messaging Infrastructure
  Main messaging interface with conversation list and thread view
-->

<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import { authService } from '$lib/services/auth';
  import MessageList from '$lib/components/community/MessageList.svelte';
  import MessageThread from '$lib/components/community/MessageThread.svelte';

  // State
  let selectedPartnerId: string | null = null;
  let selectedPartnerName: string = '';
  let isAuthenticated = false;
  let isMobile = false;
  let showThread = false;

  // Check authentication on mount
  onMount(async () => {
    const user = await authService.getCurrentUser();
    if (!user) {
      goto('/auth/login?redirect=/community/messages');
      return;
    }

    isAuthenticated = true;

    // Check if we have a partner ID in the URL
    const partnerId = $page.url.searchParams.get('partner');
    const partnerName = $page.url.searchParams.get('name');

    if (partnerId && partnerName) {
      selectedPartnerId = partnerId;
      selectedPartnerName = partnerName;
      showThread = true;
    }

    // Check for mobile view
    checkMobileView();
    window.addEventListener('resize', checkMobileView);

    return () => {
      window.removeEventListener('resize', checkMobileView);
    };
  });

  function checkMobileView() {
    isMobile = window.innerWidth < 768;
  }

  function handleConversationSelect(event: CustomEvent) {
    const { partnerId, partnerName } = event.detail;
    selectedPartnerId = partnerId;
    selectedPartnerName = partnerName;
    showThread = true;

    // Update URL without navigation
    const url = new URL(window.location.href);
    url.searchParams.set('partner', partnerId);
    url.searchParams.set('name', partnerName);
    window.history.replaceState({}, '', url.toString());
  }

  function handleBackToList() {
    if (isMobile) {
      showThread = false;
      selectedPartnerId = null;
      selectedPartnerName = '';

      // Clear URL params
      const url = new URL(window.location.href);
      url.searchParams.delete('partner');
      url.searchParams.delete('name');
      window.history.replaceState({}, '', url.toString());
    }
  }
</script>

<svelte:head>
  <title>Messages - VybeCoding.ai Community</title>
  <meta
    name="description"
    content="Connect and collaborate with fellow learners through direct messaging"
  />
</svelte:head>

{#if !isAuthenticated}
  <div class="auth-required">
    <div class="auth-content">
      <div class="auth-icon">🔒</div>
      <h2>Authentication Required</h2>
      <p>
        Please log in to access your messages and connect with the community.
      </p>
      <a href="/auth/login?redirect=/community/messages" class="login-btn">
        Log In
      </a>
    </div>
  </div>
{:else}
  <div class="messages-page">
    <!-- Page Header -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1 class="page-title">Messages</h1>
          <p class="page-description">
            Connect and collaborate with fellow learners
          </p>
        </div>

        <div class="header-actions">
          <a href="/community/peers" class="action-btn">
            <span class="btn-icon">👥</span>
            Find Peers
          </a>
          <a href="/community" class="action-btn secondary">
            <span class="btn-icon">🏠</span>
            Community
          </a>
        </div>
      </div>
    </div>

    <!-- Messages Interface -->
    <div class="messages-interface">
      <!-- Conversation List -->
      <div class="conversation-panel" class:hidden={isMobile && showThread}>
        <MessageList {selectedPartnerId} on:select={handleConversationSelect} />
      </div>

      <!-- Message Thread -->
      <div class="thread-panel" class:hidden={isMobile && !showThread}>
        {#if selectedPartnerId}
          <div class="thread-header">
            {#if isMobile}
              <button
                type="button"
                class="back-btn"
                on:click={handleBackToList}
              >
                ← Back
              </button>
            {/if}
            <div class="thread-title">
              <h3>{selectedPartnerName}</h3>
            </div>
          </div>

          <div class="thread-content">
            <MessageThread
              partnerId={selectedPartnerId}
              partnerName={selectedPartnerName}
            />
          </div>
        {:else}
          <div class="no-conversation">
            <div class="no-conversation-content">
              <div class="no-conversation-icon">💬</div>
              <h3>Select a conversation</h3>
              <p>Choose a conversation from the list to start messaging</p>

              <div class="quick-actions">
                <a href="/community/peers" class="quick-action-btn">
                  <span class="btn-icon">🔍</span>
                  Find New Peers
                </a>
                <a href="/community" class="quick-action-btn secondary">
                  <span class="btn-icon">👥</span>
                  Browse Community
                </a>
              </div>
            </div>
          </div>
        {/if}
      </div>
    </div>
  </div>
{/if}

<style>
  .auth-required {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
    padding: 2rem;
  }

  .auth-content {
    text-align: center;
    max-width: 400px;
  }

  .auth-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
  }

  .auth-content h2 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
  }

  .auth-content p {
    color: var(--text-secondary);
    margin-bottom: 2rem;
    line-height: 1.6;
  }

  .login-btn {
    display: inline-flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    background: var(--primary-gradient);
    color: white;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
  }

  .login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .messages-page {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: var(--background-color);
  }

  .page-header {
    background: var(--surface-color);
    border-bottom: 1px solid var(--border-color);
    padding: 1.5rem 2rem;
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
  }

  .header-info {
    flex: 1;
  }

  .page-title {
    margin: 0 0 0.5rem 0;
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
  }

  .page-description {
    margin: 0;
    color: var(--text-secondary);
    font-size: 1.1rem;
  }

  .header-actions {
    display: flex;
    gap: 1rem;
  }

  .action-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
  }

  .action-btn.secondary {
    background: var(--surface-elevated);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
  }

  .action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .action-btn.secondary:hover {
    background: var(--surface-hover);
  }

  .btn-icon {
    font-size: 1.1rem;
  }

  .messages-interface {
    display: flex;
    flex: 1;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
    overflow: hidden;
  }

  .conversation-panel {
    width: 320px;
    flex-shrink: 0;
    height: 100%;
  }

  .thread-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .thread-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--surface-elevated);
    border-bottom: 1px solid var(--border-color);
  }

  .back-btn {
    background: none;
    border: none;
    color: var(--primary-color);
    cursor: pointer;
    font-size: 1rem;
    padding: 0.5rem;
    border-radius: 6px;
    transition: background-color 0.2s ease;
  }

  .back-btn:hover {
    background: var(--surface-hover);
  }

  .thread-title h3 {
    margin: 0;
    color: var(--text-primary);
  }

  .thread-content {
    flex: 1;
    overflow: hidden;
  }

  .no-conversation {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: var(--surface-color);
  }

  .no-conversation-content {
    text-align: center;
    max-width: 400px;
    padding: 2rem;
  }

  .no-conversation-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
  }

  .no-conversation-content h3 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
  }

  .no-conversation-content p {
    color: var(--text-secondary);
    margin-bottom: 2rem;
    line-height: 1.6;
  }

  .quick-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .quick-action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
  }

  .quick-action-btn.secondary {
    background: var(--surface-elevated);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
  }

  .quick-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .quick-action-btn.secondary:hover {
    background: var(--surface-hover);
  }

  /* Mobile responsiveness */
  @media (max-width: 768px) {
    .page-header {
      padding: 1rem;
    }

    .header-content {
      flex-direction: column;
      gap: 1rem;
      align-items: flex-start;
    }

    .header-actions {
      width: 100%;
      justify-content: space-between;
    }

    .page-title {
      font-size: 1.5rem;
    }

    .messages-interface {
      max-width: none;
    }

    .conversation-panel {
      width: 100%;
    }

    .conversation-panel.hidden,
    .thread-panel.hidden {
      display: none;
    }

    .thread-panel {
      width: 100%;
    }

    .quick-actions {
      flex-direction: row;
    }

    .quick-action-btn {
      flex: 1;
    }
  }
</style>
