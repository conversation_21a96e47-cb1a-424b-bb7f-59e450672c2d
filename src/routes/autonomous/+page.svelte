<script lang="ts">
  /**
   * DEPRECATED: Autonomous Generation Page
   * REDIRECTS TO: /generator (Enhanced Vybe Method Interface)
   * This route has been replaced with the advanced Vybe Method generator
   */

  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';

  onMount(() => {
    // Redirect to the new enhanced generator page
    goto('/generator');
  });


</script>

<svelte:head>
  <title>Redirecting to Generator - VybeCoding.ai</title>
  <meta
    name="description"
    content="Redirecting to the enhanced Vybe Method Generator"
  />
</svelte:head>

<main class="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
  <div class="text-center">
    <div class="w-16 h-16 mx-auto mb-4 border-4 border-cyan-500/30 border-t-cyan-500 rounded-full animate-spin"></div>
    <h1 class="text-2xl font-bold text-white mb-2">Redirecting to Enhanced Generator</h1>
    <p class="text-gray-300">Taking you to the new Vybe Method Generator...</p>
  </div>
</main>

