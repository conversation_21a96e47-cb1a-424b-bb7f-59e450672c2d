// API endpoint for news aggregation
import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { NewsAggregationService } from '$lib/services/ai/newsAggregation';

const aggregationService = new NewsAggregationService();

export const POST: RequestHandler = async ({ request }) => {
  try {
    // Trigger news aggregation
    const articles = await aggregationService.aggregateNews();

    return json({
      success: true,
      articles,
      count: articles.length,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('News aggregation failed:', error);

    return json(
      {
        success: false,
        error: 'Failed to aggregate news',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
};

export const GET: RequestHandler = async () => {
  try {
    // Get trending topics
    const trends = await aggregationService.getTrendingTopics();

    return json({
      success: true,
      trends,
      count: trends.length,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Failed to get trending topics:', error);

    return json(
      {
        success: false,
        error: 'Failed to get trending topics',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
};
