// API endpoint for articles CRUD operations
import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { ContentCurationService } from '$lib/services/ai/contentCuration';
import { databases } from '$lib/services/appwrite';
import { config } from '$lib/config';
import { nanoid } from 'nanoid';

const curationService = new ContentCurationService();

export const GET: RequestHandler = async ({ url, locals }) => {
  try {
    const category = url.searchParams.get('category');
    const search = url.searchParams.get('search');
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const userId = locals.user?.id;

    let articles;

    if (search) {
      // Search articles
      articles = await curationService.searchArticles(search, limit);
    } else if (category && category !== 'all') {
      // Get articles by category
      articles = await curationService.getArticlesByCategory(category, limit);
    } else if (userId) {
      // Get personalized feed for authenticated users
      articles = await curationService.getPersonalizedFeed(userId, limit);
    } else {
      // Get trending articles for anonymous users
      articles = await curationService.getTrendingArticles(limit);
    }

    return json({
      success: true,
      articles,
      count: articles.length,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Failed to get articles:', error);

    return json(
      {
        success: false,
        error: 'Failed to get articles',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
};

export const POST: RequestHandler = async ({ request, locals }) => {
  try {
    const user = locals.user;
    if (!user) {
      return json(
        {
          success: false,
          error: 'Authentication required',
        },
        { status: 401 }
      );
    }

    const data = await request.json();

    // Validate required fields
    if (!data.title || !data.content) {
      return json(
        {
          success: false,
          error: 'Title and content are required',
        },
        { status: 400 }
      );
    }

    // Create new article
    const article = {
      id: nanoid(),
      title: data.title,
      slug: generateSlug(data.title),
      content: data.content,
      excerpt: data.excerpt || generateExcerpt(data.content),
      authorId: user.id,
      category: data.category || 'community',
      tags: data.tags || [],
      source: 'community',
      status: 'review', // Community submissions need review
      featured: false,
      aiMetadata: {
        sentiment: 0,
        complexity: 5,
        topics: [],
        factCheckScore: 0.5,
        trendPrediction: 0.1,
        readingTime: Math.ceil(data.content.length / 200),
        educationalValue: 0.5,
      },
      collaborators: [],
      realTimeEdits: [],
      aiSuggestions: [],
      votes: 0,
      views: 0,
      activeReaders: 0,
      comments: 0,
      shares: 0,
      publishedAt: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Save to database
    const saved = await databases.createDocument(
      config.appwrite.databaseId,
      'news_articles',
      article.id,
      article
    );

    return json({
      success: true,
      article: saved,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Failed to create article:', error);

    return json(
      {
        success: false,
        error: 'Failed to create article',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
};

function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/^-+|-+$/g, '');
}

function generateExcerpt(content: string, maxLength: number = 200): string {
  const text = content.replace(/<[^>]*>/g, ''); // Remove HTML tags
  return text.length > maxLength
    ? text.substring(0, maxLength).trim() + '...'
    : text;
}
