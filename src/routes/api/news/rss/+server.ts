// RSS feed parser for news aggregation
import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async ({ url }) => {
  try {
    const rssUrl = url.searchParams.get('url');

    if (!rssUrl) {
      return json(
        {
          success: false,
          error: 'RSS URL is required',
        },
        { status: 400 }
      );
    }

    // Fetch RSS feed
    const response = await fetch(rssUrl, {
      headers: {
        'User-Agent': 'VybeCoding.ai News Aggregator 1.0',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const rssText = await response.text();

    // Parse RSS XML (simplified parser)
    const articles = parseRSSFeed(rssText);

    return json({
      success: true,
      articles,
      count: articles.length,
      source: rssUrl,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('RSS parsing failed:', error);

    return json(
      {
        success: false,
        error: 'Failed to parse RSS feed',
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
};

function parseRSSFeed(rssText: string): any[] {
  const articles: any[] = [];

  try {
    // Simple regex-based RSS parsing (in production, use a proper XML parser)
    const itemRegex = /<item[^>]*>([\s\S]*?)<\/item>/gi;
    const titleRegex =
      /<title[^>]*><!\[CDATA\[(.*?)\]\]><\/title>|<title[^>]*>(.*?)<\/title>/i;
    const descriptionRegex =
      /<description[^>]*><!\[CDATA\[(.*?)\]\]><\/description>|<description[^>]*>(.*?)<\/description>/i;
    const linkRegex = /<link[^>]*>(.*?)<\/link>/i;
    const pubDateRegex = /<pubDate[^>]*>(.*?)<\/pubDate>/i;
    const categoryRegex = /<category[^>]*>(.*?)<\/category>/gi;

    let match;
    while ((match = itemRegex.exec(rssText)) !== null) {
      const itemContent = match[1];

      const titleMatch = titleRegex.exec(itemContent);
      const descriptionMatch = descriptionRegex.exec(itemContent);
      const linkMatch = linkRegex.exec(itemContent);
      const pubDateMatch = pubDateRegex.exec(itemContent);

      // Extract categories
      const categories: string[] = [];
      let categoryMatch;
      while ((categoryMatch = categoryRegex.exec(itemContent)) !== null) {
        categories.push(categoryMatch[1].trim());
      }

      const title = (titleMatch?.[1] || titleMatch?.[2] || '').trim();
      const description = (
        descriptionMatch?.[1] ||
        descriptionMatch?.[2] ||
        ''
      ).trim();
      const link = linkMatch?.[1]?.trim() || '';
      const pubDate = pubDateMatch?.[1]?.trim() || '';

      if (title && description) {
        articles.push({
          title: cleanText(title),
          description: cleanText(description),
          link: link,
          publishedAt: pubDate ? new Date(pubDate) : new Date(),
          categories: categories,
          source: 'rss',
        });
      }
    }
  } catch (error) {
    console.error('RSS parsing error:', error);
  }

  return articles;
}

function cleanText(text: string): string {
  return text
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&amp;/g, '&')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")
    .replace(/&nbsp;/g, ' ')
    .trim();
}
