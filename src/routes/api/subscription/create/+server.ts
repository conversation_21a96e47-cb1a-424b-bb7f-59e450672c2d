import { json } from '@sveltejs/kit';
import type { Request<PERSON>and<PERSON> } from './$types';
import { Client, Databases, ID } from 'appwrite';

const client = new Client()
  .setEndpoint('https://cloud.appwrite.io/v1')
  .setProject('vybecoding');

const databases = new Databases(client);

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { tier, userId } = await request.json();

    // Validate required fields
    if (!tier || !userId) {
      return json(
        { error: 'Tier and user ID are required' },
        { status: 400 }
      );
    }

    // Validate tier
    const validTiers = ['free', 'pro', 'enterprise'];
    if (!validTiers.includes(tier)) {
      return json(
        { error: 'Invalid subscription tier' },
        { status: 400 }
      );
    }

    // For free tier, just update user preferences
    if (tier === 'free') {
      const userSubscription = await databases.createDocument(
        '683b231d003c1c558e20', // Database ID
        'user_subscriptions', // Collection ID
        ID.unique(),
        {
          userId,
          tier: 'free',
          status: 'active',
          startDate: new Date().toISOString(),
          features: ['basic_courses', 'community_access'],
          created_at: new Date().toISOString()
        }
      );

      return json({
        success: true,
        message: 'Free tier activated successfully',
        subscription: userSubscription
      });
    }

    // For paid tiers, create subscription record
    // In a real implementation, this would integrate with Stripe or similar
    const subscription = await databases.createDocument(
      '683b231d003c1c558e20', // Database ID
      'user_subscriptions', // Collection ID
      ID.unique(),
      {
        userId,
        tier,
        status: 'pending_payment',
        startDate: new Date().toISOString(),
        features: tier === 'pro' 
          ? ['all_courses', 'community_access', 'ai_assistance', 'priority_support']
          : ['all_courses', 'community_access', 'ai_assistance', 'priority_support', 'custom_domains', 'team_features'],
        created_at: new Date().toISOString()
      }
    );

    // Log subscription attempt for admin notification
    console.log('New subscription created:', {
      id: subscription.$id,
      userId,
      tier,
      timestamp: new Date().toISOString()
    });

    return json({
      success: true,
      message: 'Subscription created successfully',
      subscription,
      // In real implementation, would return payment URL
      paymentRequired: tier !== 'free'
    });

  } catch (error) {
    console.error('Subscription API error:', error);
    
    return json(
      { 
        error: 'Failed to create subscription. Please try again later.',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
};
