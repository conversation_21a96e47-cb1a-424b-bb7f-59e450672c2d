import { json } from '@sveltejs/kit';
import type { RequestH<PERSON><PERSON> } from './$types';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { serviceName } = await request.json();

    if (!serviceName) {
      return json({ error: 'Service name is required' }, { status: 400 });
    }

    // Map service names to actual Docker container names
    const containerMap: Record<string, string> = {
      Grafana: 'grafana',
      Prometheus: 'prometheus',
      Netdata: 'netdata',
      Kibana: 'kibana',
      Elasticsearch: 'elasticsearch',
      Jaeger: 'jaeger',
    };

    const containerName = containerMap[serviceName];
    if (!containerName) {
      return json({ error: 'Unknown service' }, { status: 400 });
    }

    // Check if container exists and restart it
    try {
      await execAsync(`docker restart ${containerName}`);
      return json({
        success: true,
        message: `${serviceName} restarted successfully`,
      });
    } catch (error) {
      // If restart fails, try to start the container
      try {
        await execAsync(`docker start ${containerName}`);
        return json({
          success: true,
          message: `${serviceName} started successfully`,
        });
      } catch (startError) {
        throw new Error(`Failed to restart or start ${serviceName}`);
      }
    }
  } catch (error) {
    console.error('Docker restart error:', error);
    return json(
      {
        error:
          error instanceof Error ? error.message : 'Failed to restart service',
      },
      { status: 500 }
    );
  }
};
