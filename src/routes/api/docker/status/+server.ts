import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export const GET: RequestHandler = async () => {
  try {
    // Get status of all MAS-related containers
    const { stdout } = await execAsync(
      'docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(mas-|portainer)"'
    );

    const containers = stdout
      .trim()
      .split('\n')
      .slice(1)
      .map(line => {
        const [name, status, ports] = line.split('\t');
        return { name, status, ports };
      });

    // Map container names to service names
    const serviceStatus = {
      grafana:
        containers
          .find(c => c.name === 'mas-grafana')
          ?.status?.includes('Up') || false,
      prometheus:
        containers
          .find(c => c.name === 'mas-prometheus')
          ?.status?.includes('Up') || false,
      netdata:
        containers
          .find(c => c.name === 'mas-netdata')
          ?.status?.includes('Up') || false,
      kibana:
        containers.find(c => c.name === 'mas-kibana')?.status?.includes('Up') ||
        false,
      elasticsearch:
        containers
          .find(c => c.name === 'mas-elasticsearch')
          ?.status?.includes('Up') || false,
      jaeger:
        containers.find(c => c.name === 'mas-jaeger')?.status?.includes('Up') ||
        false,
      portainer:
        containers.find(c => c.name === 'portainer')?.status?.includes('Up') ||
        false,
    };

    return json(serviceStatus);
  } catch (error) {
    console.error('Error checking Docker status:', error);
    // Return all false if we can't check
    return json({
      grafana: false,
      prometheus: false,
      netdata: false,
      kibana: false,
      elasticsearch: false,
      jaeger: false,
      portainer: false,
    });
  }
};
