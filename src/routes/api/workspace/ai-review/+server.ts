/**
 * AI Code Review API Endpoint
 * STORY-4-001: Advanced Student Workspace Features
 */

import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import type {
  CodeAnalysis,
  CodeIssue,
  CodeSuggestion,
} from '$lib/types/workspace';

// Real AI Code Review Service using Ollama LLM
class AICodeReviewService {
  async analyzeCode(
    code: string,
    language: string,
    context?: any
  ): Promise<CodeAnalysis> {
    try {
      // Use real Ollama LLM for code analysis
      const analysisPrompt = this.buildAnalysisPrompt(code, language, context);

      const response = await fetch('http://localhost:11434/api/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          model: 'devstral:24b', // Use code-specialized model
          prompt: analysisPrompt,
          stream: false
        })
      });

      if (response.ok) {
        const data = await response.json();
        return this.parseAIAnalysis(data.response, code, language);
      } else {
        throw new Error(`LLM API error: ${response.status}`);
      }
    } catch (error) {
      console.error('Real AI code review failed:', error);
      // Fallback to static analysis instead of simulation
      return this.performStaticAnalysis(code, language);
    }
  }

  private buildAnalysisPrompt(code: string, language: string, context?: any): string {
    return `You are an expert code reviewer. Analyze this ${language} code and provide:

1. Issues (bugs, security vulnerabilities, performance problems)
2. Suggestions for improvement
3. Code complexity assessment
4. Maintainability score

Code to analyze:
\`\`\`${language}
${code}
\`\`\`

Provide your analysis in JSON format:
{
  "issues": [{"type": "error|warning|info", "line": number, "message": "description"}],
  "suggestions": [{"type": "improvement|optimization|style", "message": "suggestion"}],
  "complexity": number (1-10),
  "maintainability": number (1-100)
}`;
  }

  private parseAIAnalysis(aiResponse: string, code: string, language: string): CodeAnalysis {
    try {
      // Try to extract JSON from AI response
      const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const analysis = JSON.parse(jsonMatch[0]);
        return {
          score: this.calculateScore(analysis.issues || [], analysis.complexity || 5, analysis.maintainability || 70),
          issues: analysis.issues || [],
          suggestions: analysis.suggestions || [],
          complexity: analysis.complexity || 5,
          maintainability: analysis.maintainability || 70
        };
      }
    } catch (error) {
      console.error('Failed to parse AI analysis:', error);
    }

    // Fallback to static analysis
    return this.performStaticAnalysis(code, language);
  }

  private performStaticAnalysis(code: string, language: string): CodeAnalysis {

    // Perform basic static analysis
    const issues = this.detectIssues(code, language);
    const suggestions = this.generateSuggestions(code, language);
    const complexity = this.calculateComplexity(code);
    const maintainability = this.calculateMaintainability(code);
    const score = this.calculateScore(issues, complexity, maintainability);

    return {
      score,
      issues,
      suggestions,
      complexity,
      maintainability,
    };
  }

  private detectIssues(code: string, language: string): CodeIssue[] {
    const issues: CodeIssue[] = [];
    const lines = code.split('\n');

    lines.forEach((line, index) => {
      const lineNumber = index + 1;

      // Check for common issues
      if (language === 'javascript' || language === 'typescript') {
        // Check for var usage
        if (line.includes('var ')) {
          issues.push({
            id: `var-${lineNumber}`,
            type: 'warning',
            message: 'Consider using const or let instead of var',
            line: lineNumber,
            column: line.indexOf('var ') + 1,
            severity: 'medium',
            fixable: true,
          });
        }

        // Check for console.log
        if (line.includes('console.log')) {
          issues.push({
            id: `console-${lineNumber}`,
            type: 'info',
            message: 'Remove console.log statements before production',
            line: lineNumber,
            column: line.indexOf('console.log') + 1,
            severity: 'low',
            fixable: true,
          });
        }

        // Check for missing semicolons
        if (
          line.trim().length > 0 &&
          !line.trim().endsWith(';') &&
          !line.trim().endsWith('{') &&
          !line.trim().endsWith('}') &&
          !line.trim().startsWith('//') &&
          !line.trim().startsWith('*') &&
          !line.includes('if ') &&
          !line.includes('for ') &&
          !line.includes('while ')
        ) {
          issues.push({
            id: `semicolon-${lineNumber}`,
            type: 'warning',
            message: 'Missing semicolon',
            line: lineNumber,
            column: line.length,
            severity: 'low',
            fixable: true,
          });
        }

        // Check for long lines
        if (line.length > 100) {
          issues.push({
            id: `line-length-${lineNumber}`,
            type: 'info',
            message: 'Line too long (>100 characters)',
            line: lineNumber,
            column: 100,
            severity: 'low',
            fixable: false,
          });
        }
      }

      // Check for TODO comments
      if (line.includes('TODO') || line.includes('FIXME')) {
        issues.push({
          id: `todo-${lineNumber}`,
          type: 'info',
          message: 'TODO comment found',
          line: lineNumber,
          column:
            line.indexOf('TODO') !== -1
              ? line.indexOf('TODO') + 1
              : line.indexOf('FIXME') + 1,
          severity: 'low',
          fixable: false,
        });
      }
    });

    return issues;
  }

  private generateSuggestions(
    code: string,
    language: string
  ): CodeSuggestion[] {
    const suggestions: CodeSuggestion[] = [];

    // Basic suggestions based on code patterns
    if (language === 'javascript' || language === 'typescript') {
      if (code.includes('for (')) {
        suggestions.push({
          id: 'array-methods',
          type: 'optimization',
          message:
            'Consider using array methods like map(), filter(), or reduce() for better readability',
          confidence: 0.8,
          example: 'const result = array.map(item => item.property);',
        });
      }

      if (code.includes('function ')) {
        suggestions.push({
          id: 'arrow-functions',
          type: 'improvement',
          message: 'Consider using arrow functions for cleaner syntax',
          confidence: 0.7,
          example: 'const myFunction = (param) => { return param * 2; };',
        });
      }

      if (!code.includes('try') && !code.includes('catch')) {
        suggestions.push({
          id: 'error-handling',
          type: 'best-practice',
          message: 'Add error handling to make your code more robust',
          confidence: 0.9,
          example:
            'try { /* your code */ } catch (error) { console.error(error); }',
        });
      }

      if (!code.includes('const') && !code.includes('let')) {
        suggestions.push({
          id: 'modern-variables',
          type: 'best-practice',
          message: 'Use const and let instead of var for better scoping',
          confidence: 0.95,
          example: 'const immutableValue = "hello"; let mutableValue = 0;',
        });
      }
    }

    // Add documentation suggestion
    if (!code.includes('/**') && !code.includes('//')) {
      suggestions.push({
        id: 'documentation',
        type: 'best-practice',
        message: 'Add comments to explain complex logic',
        confidence: 0.6,
        example: '// This function calculates the total price including tax',
      });
    }

    return suggestions;
  }

  private calculateComplexity(code: string): number {
    // Simple cyclomatic complexity calculation
    const complexityKeywords = [
      'if',
      'else',
      'for',
      'while',
      'switch',
      'case',
      'catch',
      '&&',
      '||',
    ];
    let complexity = 1; // Base complexity

    complexityKeywords.forEach(keyword => {
      const matches = code.match(new RegExp(`\\b${keyword}\\b`, 'g'));
      if (matches) {
        complexity += matches.length;
      }
    });

    return Math.min(complexity, 20); // Cap at 20
  }

  private calculateMaintainability(code: string): number {
    const lines = code.split('\n').filter(line => line.trim().length > 0);
    const avgLineLength =
      lines.reduce((sum, line) => sum + line.length, 0) / lines.length;
    const functionCount = (code.match(/function\s+\w+/g) || []).length;
    const commentRatio = (code.match(/\/\/|\/\*/g) || []).length / lines.length;

    // Calculate maintainability score (0-100)
    let score = 100;

    // Penalize long average line length
    if (avgLineLength > 80) score -= (avgLineLength - 80) * 0.5;

    // Reward good comment ratio
    score += commentRatio * 20;

    // Penalize too many functions in one file
    if (functionCount > 10) score -= (functionCount - 10) * 2;

    return Math.max(0, Math.min(100, score));
  }

  private calculateScore(
    issues: CodeIssue[],
    complexity: number,
    maintainability: number
  ): number {
    let score = 100;

    // Deduct points for issues
    issues.forEach(issue => {
      switch (issue.severity) {
        case 'high':
          score -= 10;
          break;
        case 'medium':
          score -= 5;
          break;
        case 'low':
          score -= 2;
          break;
      }
    });

    // Factor in complexity (higher complexity reduces score)
    if (complexity > 10) {
      score -= (complexity - 10) * 2;
    }

    // Factor in maintainability
    score = (score + maintainability) / 2;

    return Math.max(0, Math.min(100, Math.round(score)));
  }
}

const aiService = new AICodeReviewService();

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { code, language, context } = await request.json();

    if (!code || !language) {
      return json({ error: 'Code and language are required' }, { status: 400 });
    }

    // Perform AI code analysis
    const analysis = await aiService.analyzeCode(code, language, context);

    return json({
      success: true,
      analysis,
      timestamp: new Date().toISOString(),
      provider: 'vybe-ai-local',
    });
  } catch (error) {
    console.error('AI code review error:', error);
    return json({ error: 'Failed to analyze code' }, { status: 500 });
  }
};

export const GET: RequestHandler = async () => {
  return json({
    service: 'AI Code Review',
    version: '1.0.0',
    capabilities: [
      'syntax-analysis',
      'code-quality-scoring',
      'issue-detection',
      'improvement-suggestions',
      'complexity-analysis',
      'maintainability-scoring',
    ],
    supportedLanguages: [
      'javascript',
      'typescript',
      'python',
      'svelte',
      'html',
      'css',
    ],
  });
};
