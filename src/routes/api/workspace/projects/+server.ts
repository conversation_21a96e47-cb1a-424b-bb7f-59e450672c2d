/**
 * Project Management API Endpoint
 * STORY-4-001: Advanced Student Workspace Features
 */

import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import type {
  Project,
  ProjectTemplate,
  ProjectFile,
} from '$lib/types/workspace';
import { nanoid } from 'nanoid';

// Mock project storage - replace with real database
const projects = new Map<string, Project>();
const templates = new Map<string, ProjectTemplate>();

// Initialize with some default templates
initializeTemplates();

function initializeTemplates() {
  const webTemplate: ProjectTemplate = {
    id: 'web-basic',
    name: 'Basic Web Project',
    description: 'HTML, CSS, and JavaScript starter template',
    category: 'web',
    difficulty: 'beginner',
    files: [
      {
        name: 'index.html',
        content: `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Web Project</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <h1>Welcome to My Web Project</h1>
    <p>Start building something amazing!</p>
    <script src="script.js"></script>
</body>
</html>`,
        path: 'index.html',
      },
      {
        name: 'style.css',
        content: `/* Basic styles for your web project */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f5f5f5;
}

h1 {
    color: #333;
    text-align: center;
}

p {
    color: #666;
    text-align: center;
    font-size: 18px;
}`,
        path: 'style.css',
      },
      {
        name: 'script.js',
        content: `// JavaScript for your web project
console.log('Welcome to your web project!');

// Add your JavaScript code here
document.addEventListener('DOMContentLoaded', function() {
    console.log('Page loaded successfully!');
});`,
        path: 'script.js',
      },
    ],
    tags: ['html', 'css', 'javascript', 'beginner'],
    metadata: {
      language: 'javascript',
      framework: 'vanilla',
      version: '1.0.0',
      tags: ['web', 'frontend'],
    },
  };

  const reactTemplate: ProjectTemplate = {
    id: 'react-basic',
    name: 'React App',
    description: 'Basic React application with components',
    category: 'web',
    difficulty: 'intermediate',
    files: [
      {
        name: 'App.jsx',
        content: `import React, { useState } from 'react';
import './App.css';

function App() {
  const [count, setCount] = useState(0);

  return (
    <div className="App">
      <header className="App-header">
        <h1>React App</h1>
        <p>Count: {count}</p>
        <button onClick={() => setCount(count + 1)}>
          Increment
        </button>
      </header>
    </div>
  );
}

export default App;`,
        path: 'src/App.jsx',
      },
      {
        name: 'App.css',
        content: `.App {
  text-align: center;
}

.App-header {
  background-color: #282c34;
  padding: 20px;
  color: white;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

button {
  background-color: #61dafb;
  border: none;
  padding: 10px 20px;
  margin: 10px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
}`,
        path: 'src/App.css',
      },
    ],
    tags: ['react', 'jsx', 'javascript', 'intermediate'],
    metadata: {
      language: 'javascript',
      framework: 'react',
      version: '1.0.0',
      tags: ['react', 'frontend', 'spa'],
    },
  };

  templates.set(webTemplate.id, webTemplate);
  templates.set(reactTemplate.id, reactTemplate);
}

export const GET: RequestHandler = async ({ url }) => {
  const action = url.searchParams.get('action');
  const id = url.searchParams.get('id');

  try {
    switch (action) {
      case 'list':
        return json({
          success: true,
          projects: Array.from(projects.values()),
        });

      case 'templates':
        return json({
          success: true,
          templates: Array.from(templates.values()),
        });

      case 'get':
        if (!id) {
          return json({ error: 'Project ID is required' }, { status: 400 });
        }

        const project = projects.get(id);
        if (!project) {
          return json({ error: 'Project not found' }, { status: 404 });
        }

        return json({
          success: true,
          project,
        });

      default:
        return json({
          service: 'Project Management API',
          version: '1.0.0',
          endpoints: {
            'GET ?action=list': 'List all projects',
            'GET ?action=templates': 'List project templates',
            'GET ?action=get&id=<id>': 'Get specific project',
            POST: 'Create new project',
            PUT: 'Update existing project',
            'DELETE ?id=<id>': 'Delete project',
          },
        });
    }
  } catch (error) {
    console.error('Project API error:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { name, description, templateId } = await request.json();

    if (!name) {
      return json({ error: 'Project name is required' }, { status: 400 });
    }

    const projectId = nanoid();
    let files: ProjectFile[] = [];

    // If template is specified, use template files
    if (templateId) {
      const template = templates.get(templateId);
      if (!template) {
        return json({ error: 'Template not found' }, { status: 404 });
      }

      files = template.files.map(templateFile => ({
        id: nanoid(),
        name: templateFile.name,
        path: templateFile.path || templateFile.name,
        language: getLanguageFromFileName(templateFile.name),
        content: templateFile.content,
        lastModified: new Date(),
        size: templateFile.content.length,
      }));
    }

    const project: Project = {
      id: projectId,
      name,
      description: description || '',
      template: templateId,
      files,
      settings: {
        autoSave: true,
        gitEnabled: false,
        collaborationEnabled: false,
      },
      metadata: {
        language: files.length > 0 ? files[0].language : 'javascript',
        version: '1.0.0',
        tags: [],
      },
      createdAt: new Date(),
      lastModified: new Date(),
      lastOpened: new Date(),
      isStarred: false,
      isArchived: false,
    };

    projects.set(projectId, project);

    return json({
      success: true,
      project,
      message: 'Project created successfully',
    });
  } catch (error) {
    console.error('Create project error:', error);
    return json({ error: 'Failed to create project' }, { status: 500 });
  }
};

export const PUT: RequestHandler = async ({ request }) => {
  try {
    const { id, ...updates } = await request.json();

    if (!id) {
      return json({ error: 'Project ID is required' }, { status: 400 });
    }

    const project = projects.get(id);
    if (!project) {
      return json({ error: 'Project not found' }, { status: 404 });
    }

    // Update project with new data
    const updatedProject: Project = {
      ...project,
      ...updates,
      lastModified: new Date(),
    };

    projects.set(id, updatedProject);

    return json({
      success: true,
      project: updatedProject,
      message: 'Project updated successfully',
    });
  } catch (error) {
    console.error('Update project error:', error);
    return json({ error: 'Failed to update project' }, { status: 500 });
  }
};

export const DELETE: RequestHandler = async ({ url }) => {
  try {
    const id = url.searchParams.get('id');

    if (!id) {
      return json({ error: 'Project ID is required' }, { status: 400 });
    }

    const project = projects.get(id);
    if (!project) {
      return json({ error: 'Project not found' }, { status: 404 });
    }

    projects.delete(id);

    return json({
      success: true,
      message: 'Project deleted successfully',
    });
  } catch (error) {
    console.error('Delete project error:', error);
    return json({ error: 'Failed to delete project' }, { status: 500 });
  }
};

// Helper function to determine language from file extension
function getLanguageFromFileName(fileName: string): string {
  const extension = fileName.split('.').pop()?.toLowerCase();

  const languageMap: Record<string, string> = {
    js: 'javascript',
    jsx: 'javascript',
    ts: 'typescript',
    tsx: 'typescript',
    py: 'python',
    html: 'html',
    css: 'css',
    scss: 'css',
    sass: 'css',
    svelte: 'svelte',
    vue: 'svelte',
    json: 'javascript',
    md: 'markdown',
  };

  return languageMap[extension || ''] || 'javascript';
}
