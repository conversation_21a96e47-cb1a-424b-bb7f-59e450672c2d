// Health check endpoint for deployment validation
import { json } from '@sveltejs/kit';
import { config } from '$lib/config';
import type { RequestHandler } from './$types';

/**
 * @swagger
 * /api/health:
 *   get:
 *     summary: System health check
 *     description: Returns comprehensive system health status including server, database, and service checks
 *     tags: [Health]
 *     responses:
 *       200:
 *         description: Health status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/HealthStatus'
 *             examples:
 *               healthy:
 *                 summary: Healthy system
 *                 value:
 *                   status: "healthy"
 *                   timestamp: "2024-01-15T10:30:00Z"
 *                   version: "1.0.0"
 *                   environment: "production"
 *                   checks:
 *                     server: "ok"
 *                     database: "ok"
 *                     appwrite: "ok"
 *       500:
 *         description: Health check failed
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
export const GET: RequestHandler = async ({ url }) => {
  const startTime = Date.now();

  try {
    // Basic health checks
    const healthChecks = {
      timestamp: new Date().toISOString(),
      environment: config.environment,
      version: config.app.version,
      status: 'healthy',
      checks: {
        server: 'ok',
        database: 'checking',
        appwrite: 'checking',
        memory: 'ok',
        uptime: process.uptime(),
      },
      performance: {
        responseTime: 0,
        memoryUsage: process.memoryUsage(),
      },
      deployment: {
        buildTime: config.app.buildTime,
        commitHash: config.app.commitHash || 'unknown',
        branch: config.app.branch || 'unknown',
      },
    };

    // Check Appwrite connection
    try {
      // This would typically test Appwrite connectivity
      // For now, we'll simulate a connection check
      if (config.appwrite.endpoint && config.appwrite.projectId) {
        healthChecks.checks.appwrite = 'ok';
      } else {
        healthChecks.checks.appwrite = 'misconfigured';
      }
    } catch (error) {
      healthChecks.checks.appwrite = 'error';
      console.error('Appwrite health check failed:', error);
    }

    // Check database connectivity (via Appwrite)
    try {
      // This would typically test database connectivity
      // For now, we'll check if database ID is configured
      if (config.appwrite.databaseId) {
        healthChecks.checks.database = 'ok';
      } else {
        healthChecks.checks.database = 'misconfigured';
      }
    } catch (error) {
      healthChecks.checks.database = 'error';
      console.error('Database health check failed:', error);
    }

    // Memory check
    const memoryUsage = process.memoryUsage();
    const memoryUsageMB = memoryUsage.heapUsed / 1024 / 1024;

    if (memoryUsageMB > 512) {
      healthChecks.checks.memory = 'warning';
    } else if (memoryUsageMB > 1024) {
      healthChecks.checks.memory = 'critical';
    }

    // Calculate response time
    healthChecks.performance.responseTime = Date.now() - startTime;

    // Determine overall status
    const failedChecks = Object.values(healthChecks.checks).filter(
      status => status === 'error' || status === 'critical'
    );

    const warningChecks = Object.values(healthChecks.checks).filter(
      status => status === 'warning' || status === 'misconfigured'
    );

    if (failedChecks.length > 0) {
      healthChecks.status = 'unhealthy';
    } else if (warningChecks.length > 0) {
      healthChecks.status = 'degraded';
    }

    // Return appropriate HTTP status
    const httpStatus =
      healthChecks.status === 'healthy'
        ? 200
        : healthChecks.status === 'degraded'
          ? 200
          : 503;

    return json(healthChecks, { status: httpStatus });
  } catch (error) {
    console.error('Health check error:', error);

    return json(
      {
        timestamp: new Date().toISOString(),
        environment: config.environment,
        status: 'error',
        error: 'Health check failed',
        performance: {
          responseTime: Date.now() - startTime,
        },
      },
      { status: 503 }
    );
  }
};

// Support HEAD requests for simple health checks
export const HEAD: RequestHandler = async () => {
  try {
    // Quick health check without detailed response
    return new Response(null, { status: 200 });
  } catch (error) {
    return new Response(null, { status: 503 });
  }
};
