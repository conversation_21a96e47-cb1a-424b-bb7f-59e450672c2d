// Performance metrics endpoint for monitoring and alerting
import { json } from '@sveltejs/kit';
import { config } from '$lib/config';
import type { RequestHandler } from './$types';

interface PerformanceMetrics {
  timestamp: string;
  environment: string;
  application: {
    uptime: number;
    version: string;
    nodeVersion: string;
    platform: string;
  };
  performance: {
    responseTime: number;
    memoryUsage: {
      heapUsed: number;
      heapTotal: number;
      external: number;
      rss: number;
    };
    cpuUsage: {
      user: number;
      system: number;
    };
    eventLoopLag: number;
  };
  system: {
    loadAverage: number[];
    freeMemory: number;
    totalMemory: number;
    cpuCount: number;
  };
  business: {
    activeUsers?: number;
    requestCount?: number;
    errorRate?: number;
    conversionRate?: number;
  };
}

// Simple in-memory metrics store (in production, use Redis or similar)
const metricsStore = new Map<string, any>();

function getEventLoopLag(): Promise<number> {
  return new Promise(resolve => {
    const start = process.hrtime.bigint();
    setImmediate(() => {
      const lag = Number(process.hrtime.bigint() - start) / 1e6; // Convert to milliseconds
      resolve(lag);
    });
  });
}

function getCpuUsage(): Promise<{ user: number; system: number }> {
  return new Promise(resolve => {
    const startUsage = process.cpuUsage();
    setTimeout(() => {
      const endUsage = process.cpuUsage(startUsage);
      resolve({
        user: endUsage.user / 1000, // Convert to milliseconds
        system: endUsage.system / 1000,
      });
    }, 100);
  });
}

/**
 * @swagger
 * /api/metrics:
 *   get:
 *     summary: System performance and business metrics
 *     description: Returns comprehensive performance metrics, system resource usage, and business KPIs
 *     tags: [Metrics]
 *     responses:
 *       200:
 *         description: Metrics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Metrics'
 *             examples:
 *               metrics:
 *                 summary: System metrics
 *                 value:
 *                   timestamp: "2024-01-15T10:30:00Z"
 *                   environment: "production"
 *                   application:
 *                     uptime: 3600
 *                     version: "1.0.0"
 *                     nodeVersion: "v18.17.0"
 *                     platform: "linux"
 *                   performance:
 *                     responseTime: 150
 *                     memoryUsage:
 *                       heapUsed: 18
 *                       heapTotal: 29
 *                       external: 2
 *                       rss: 52
 *                     cpuUsage:
 *                       user: 100
 *                       system: 50
 *                     eventLoopLag: 1.2
 *                   system:
 *                     loadAverage: [0.5, 0.3, 0.2]
 *                     freeMemory: 2048
 *                     totalMemory: 8192
 *                     cpuCount: 4
 *                   business:
 *                     activeUsers: 1250
 *                     requestCount: 50000
 *                     errorRate: 0.05
 *                     conversionRate: 2.3
 *       500:
 *         description: Metrics collection failed
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
export const GET: RequestHandler = async ({ url }) => {
  const startTime = Date.now();

  try {
    // Get system metrics
    const [eventLoopLag, cpuUsage] = await Promise.all([
      getEventLoopLag(),
      getCpuUsage(),
    ]);

    const memoryUsage = process.memoryUsage();
    const loadAverage =
      process.platform !== 'win32' ? require('os').loadavg() : [0, 0, 0];
    const freeMemory = require('os').freemem();
    const totalMemory = require('os').totalmem();
    const cpuCount = require('os').cpus().length;

    // Get business metrics from store
    const activeUsers = metricsStore.get('activeUsers') || 0;
    const requestCount = metricsStore.get('requestCount') || 0;
    const errorRate = metricsStore.get('errorRate') || 0;
    const conversionRate = metricsStore.get('conversionRate') || 0;

    const metrics: PerformanceMetrics = {
      timestamp: new Date().toISOString(),
      environment: config.environment,
      application: {
        uptime: process.uptime(),
        version: config.app.version,
        nodeVersion: process.version,
        platform: process.platform,
      },
      performance: {
        responseTime: Date.now() - startTime,
        memoryUsage: {
          heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
          heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024), // MB
          external: Math.round(memoryUsage.external / 1024 / 1024), // MB
          rss: Math.round(memoryUsage.rss / 1024 / 1024), // MB
        },
        cpuUsage,
        eventLoopLag,
      },
      system: {
        loadAverage,
        freeMemory: Math.round(freeMemory / 1024 / 1024), // MB
        totalMemory: Math.round(totalMemory / 1024 / 1024), // MB
        cpuCount,
      },
      business: {
        activeUsers,
        requestCount,
        errorRate,
        conversionRate,
      },
    };

    return json(metrics);
  } catch (error) {
    console.error('Metrics collection error:', error);

    return json(
      {
        timestamp: new Date().toISOString(),
        environment: config.environment,
        error: 'Metrics collection failed',
        responseTime: Date.now() - startTime,
      },
      { status: 500 }
    );
  }
};

/**
 * @swagger
 * /api/metrics:
 *   post:
 *     summary: Store custom business metrics
 *     description: Store custom metrics for time-series analysis and monitoring
 *     tags: [Metrics]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               metric:
 *                 type: string
 *                 description: Metric name
 *                 example: "user_registrations"
 *               value:
 *                 type: number
 *                 description: Metric value
 *                 example: 42
 *               labels:
 *                 type: object
 *                 description: Optional metric labels
 *                 example: { "source": "web", "campaign": "summer2024" }
 *             required: [metric, value]
 *     responses:
 *       200:
 *         description: Metric stored successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 metric:
 *                   type: string
 *                 value:
 *                   type: number
 *       400:
 *         description: Invalid metric data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Failed to store metric
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
export const POST: RequestHandler = async ({ request }) => {
  try {
    const body = await request.json();

    // Store custom metrics
    if (body.metric && body.value !== undefined) {
      metricsStore.set(body.metric, body.value);

      // Store with timestamp for time-series data
      const timeSeriesKey = `${body.metric}_timeseries`;
      const timeSeries = metricsStore.get(timeSeriesKey) || [];
      timeSeries.push({
        timestamp: new Date().toISOString(),
        value: body.value,
        labels: body.labels || {},
      });

      // Keep only last 100 data points
      if (timeSeries.length > 100) {
        timeSeries.splice(0, timeSeries.length - 100);
      }

      metricsStore.set(timeSeriesKey, timeSeries);

      return json({ success: true, metric: body.metric, value: body.value });
    }

    return json({ error: 'Invalid metric data' }, { status: 400 });
  } catch (error) {
    console.error('Metrics storage error:', error);
    return json({ error: 'Failed to store metric' }, { status: 500 });
  }
};
