/**
 * Enhanced Context Engine API
 * Provides Augment Code-style context retrieval capabilities
 * STORY-MAS-005: Enhanced Context Engine
 */

import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { spawn } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(require('child_process').exec);

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { query, maxTokens = 10000, includeMetrics = false } = await request.json();
    
    if (!query || typeof query !== 'string') {
      return json({
        success: false,
        error: 'Query parameter is required and must be a string'
      }, { status: 400 });
    }
    
    console.log(`🧠 Context retrieval request: "${query}" (max ${maxTokens} tokens)`);
    
    // Call the enhanced context engine
    const result = await retrieveContext(query, maxTokens, includeMetrics);
    
    return json({
      success: true,
      query,
      maxTokens,
      result,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Context retrieval error:', error);
    
    return json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
};

export const GET: RequestHandler = async ({ url }) => {
  try {
    const query = url.searchParams.get('query');
    const maxTokens = parseInt(url.searchParams.get('maxTokens') || '10000');
    const includeMetrics = url.searchParams.get('includeMetrics') === 'true';
    
    if (!query) {
      return json({
        success: false,
        error: 'Query parameter is required'
      }, { status: 400 });
    }
    
    console.log(`🧠 Context retrieval request: "${query}" (max ${maxTokens} tokens)`);
    
    const result = await retrieveContext(query, maxTokens, includeMetrics);
    
    return json({
      success: true,
      query,
      maxTokens,
      result,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Context retrieval error:', error);
    
    return json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
};

async function retrieveContext(query: string, maxTokens: number, includeMetrics: boolean) {
  try {
    // Create a Python script to call the enhanced context engine
    const pythonScript = `
import asyncio
import sys
import json
sys.path.append('method/vybe')
from enhanced_context_engine import enhanced_context_engine

async def retrieve():
    try:
        result = await enhanced_context_engine.retrieve_context("${query.replace(/"/g, '\\"')}", ${maxTokens})
        
        # Convert result to JSON-serializable format
        items = []
        for item in result.items:
            items.append({
                'id': item.id,
                'content': item.content[:500] + '...' if len(item.content) > 500 else item.content,
                'file_path': item.file_path,
                'line_start': item.line_start,
                'line_end': item.line_end,
                'symbols': item.symbols,
                'timestamp': item.timestamp.isoformat(),
                'access_count': item.access_count,
                'relevance_score': item.relevance_score,
                'tier': item.tier,
                'token_count': item.token_count
            })
        
        response = {
            'items': items,
            'total_tokens': result.total_tokens,
            'retrieval_time_ms': result.retrieval_time_ms,
            'sources': result.sources,
            'item_count': len(items)
        }
        
        ${includeMetrics ? `
        # Add metrics if requested
        metrics = enhanced_context_engine.get_metrics()
        response['metrics'] = metrics
        ` : ''}
        
        print(json.dumps(response))
        
    except Exception as e:
        print(json.dumps({
            'error': str(e),
            'items': [],
            'total_tokens': 0,
            'retrieval_time_ms': 0,
            'sources': [],
            'item_count': 0
        }))

asyncio.run(retrieve())
`;
    
    // Execute the Python script
    const { stdout, stderr } = await execAsync(`python3 -c "${pythonScript}"`, {
      cwd: process.cwd(),
      timeout: 30000 // 30 second timeout
    });
    
    if (stderr) {
      console.warn('Context engine stderr:', stderr);
    }
    
    try {
      const result = JSON.parse(stdout);
      
      if (result.error) {
        throw new Error(result.error);
      }
      
      console.log(`✅ Retrieved ${result.item_count} context items (${result.total_tokens} tokens) in ${result.retrieval_time_ms?.toFixed(1)}ms`);
      
      return result;
      
    } catch (parseError) {
      console.error('Failed to parse context engine output:', stdout);
      throw new Error('Failed to parse context engine response');
    }
    
  } catch (error) {
    console.error('Context retrieval failed:', error);
    
    // Return fallback context
    return {
      items: [
        {
          id: 'fallback-1',
          content: `// Fallback context for query: ${query}\n// Enhanced context engine is initializing...\n// This is a placeholder response while the system starts up.`,
          file_path: 'system/fallback.ts',
          line_start: 1,
          line_end: 3,
          symbols: ['fallback'],
          timestamp: new Date().toISOString(),
          access_count: 0,
          relevance_score: 0.1,
          tier: 'fallback',
          token_count: 50
        }
      ],
      total_tokens: 50,
      retrieval_time_ms: 0,
      sources: ['system/fallback.ts'],
      item_count: 1,
      error: error instanceof Error ? error.message : 'Context engine unavailable'
    };
  }
}

// Additional endpoint for context engine metrics
export const OPTIONS: RequestHandler = async () => {
  try {
    const { stdout } = await execAsync(`python3 -c "
import sys
import json
sys.path.append('method/vybe')
from enhanced_context_engine import enhanced_context_engine

metrics = enhanced_context_engine.get_metrics()
print(json.dumps(metrics))
"`, {
      cwd: process.cwd(),
      timeout: 10000
    });
    
    const metrics = JSON.parse(stdout);
    
    return json({
      success: true,
      metrics,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    return json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get metrics',
      metrics: {
        total_retrievals: 0,
        avg_retrieval_time: 0,
        cache_hits: 0,
        cache_misses: 0,
        tier_sizes: { hot: 0, warm: 0, cold: 0 },
        tier_tokens: { hot: 0, warm: 0, cold: 0 }
      }
    });
  }
};
