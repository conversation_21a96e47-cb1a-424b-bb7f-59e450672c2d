import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

// Store autonomous mode state (persisted via MAS Observatory control)
let autonomousMode = false;

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { enabled } = await request.json();

    // Toggle autonomous mode via MAS Observatory control
    autonomousMode = enabled;

    // Real implementation: Control MAS Observatory autonomous mode
    try {
      const command = enabled
        ? 'python3 mas-observatory-control.py autonomous on'
        : 'python3 mas-observatory-control.py autonomous off';

      const { stdout, stderr } = await execAsync(command, {
        cwd: process.cwd(),
        timeout: 10000,
      });

      if (stderr && !stderr.includes('Warning')) {
        console.error('MAS Observatory control error:', stderr);
      }

      console.log(
        `🤖 Autonomous mode ${autonomousMode ? 'ENABLED' : 'DISABLED'}`
      );
      console.log('MAS Observatory response:', stdout);
    } catch (masError) {
      console.error('Failed to control MAS Observatory:', masError);
      // Continue with local state change even if MAS control fails
    }

    return json({
      success: true,
      autonomousMode,
      message: `Autonomous mode ${autonomousMode ? 'enabled' : 'disabled'}`,
    });
  } catch (error) {
    console.error('Failed to toggle autonomous mode:', error);
    return json(
      {
        success: false,
        error: 'Failed to toggle autonomous mode',
      },
      { status: 500 }
    );
  }
};

export const GET: RequestHandler = async () => {
  return json({
    autonomousMode,
    status: autonomousMode ? 'active' : 'inactive',
  });
};
