/**
 * Autonomous Generation Status API Endpoint
 * Provides real-time status updates for generation tasks
 * Designed by <PERSON> (BMAD Design Architect) for real-time monitoring
 */

import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { activeGenerations } from '$lib/services/generationTracker';

export const GET: RequestHandler = async ({ params }) => {
  try {
    const { id } = params;

    if (!id) {
      return json({ error: 'Generation ID is required' }, { status: 400 });
    }

    // Get the generation task
    const task = activeGenerations.get(id);

    if (!task) {
      return json({ error: 'Generation not found' }, { status: 404 });
    }

    // Calculate overall progress
    const phaseProgress = Object.values(task.phases).reduce(
      (sum: number, phase: any) => {
        return sum + phase.progress;
      },
      0
    );
    const overallProgress = Math.round(
      phaseProgress / Object.keys(task.phases).length
    );

    // Get current phase
    const currentPhase = getCurrentPhase(task.phases);

    // Get active agents
    const activeAgents = Object.entries(task.agents)
      .filter(([_, agent]: [string, any]) => agent.status === 'active')
      .map(([name, agent]: [string, any]) => ({ name, ...agent }));

    // Prepare response
    const response = {
      id: task.id,
      status: task.status,
      outputType: task.outputType,
      overallProgress,
      currentPhase,
      phases: task.phases,
      agents: task.agents,
      activeAgents,
      estimatedCompletion: getEstimatedCompletion(task, overallProgress),
      createdAt: task.createdAt,
      ...(task.status === 'completed' && {
        result: task.result,
        completedAt: task.completedAt,
      }),
      ...(task.status === 'failed' && {
        error: task.error,
      }),
    };

    return json(response);
  } catch (error) {
    console.error('Status API error:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};

function getCurrentPhase(phases: any): { name: string; progress: number } {
  const phaseOrder = [
    'research',
    'planning',
    'architecture',
    'design',
    'quality',
    'deployment',
  ];

  for (const phaseName of phaseOrder) {
    const phase = phases[phaseName];
    if (phase.status === 'active') {
      return { name: phaseName, progress: phase.progress };
    }
    if (phase.status === 'pending') {
      return { name: phaseName, progress: 0 };
    }
  }

  // All phases completed
  return { name: 'completed', progress: 100 };
}

function getEstimatedCompletion(task: any, overallProgress: number): string {
  if (task.status === 'completed') return 'Completed';
  if (task.status === 'failed') return 'Failed';

  const elapsed = Date.now() - new Date(task.createdAt).getTime();
  const elapsedMinutes = Math.floor(elapsed / 60000);

  // Estimate based on progress and output type
  const totalEstimatedTime =
    {
      article: 3, // 3 minutes
      course: 4, // 4 minutes
      website: 5, // 5 minutes
    }[task.outputType] || 4;

  const remainingTime = Math.max(
    1,
    Math.round((totalEstimatedTime * (100 - overallProgress)) / 100)
  );

  return `${remainingTime} minute${remainingTime !== 1 ? 's' : ''}`;
}
