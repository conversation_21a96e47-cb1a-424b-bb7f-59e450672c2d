import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { getSwaggerSpec } from '$lib/api/swagger';

export const GET: RequestHandler = async () => {
  const spec = await getSwaggerSpec();
  return json(spec, {
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
};
