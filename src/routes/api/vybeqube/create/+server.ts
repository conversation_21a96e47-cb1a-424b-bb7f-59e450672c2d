/**
 * Vybe Qube Creation API
 * Creates actual Vybe Qube files from generation results
 */

import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { writeFileSync, mkdirSync } from 'fs';
import { join } from 'path';

interface VybeQubeData {
  title: string;
  description: string;
  content: any;
  outputType: string;
  generationId: string;
}

export const POST: RequestHandler = async ({ request }) => {
  try {
    const data: VybeQubeData = await request.json();
    
    // Create URL-safe directory name
    const urlSafeName = data.title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
    
    // Create directory path
    const vybeQubePath = join(process.cwd(), 'src', 'routes', 'vybeqube', urlSafeName);
    
    try {
      mkdirSync(vybeQubePath, { recursive: true });
    } catch (error) {
      console.log('Directory already exists or created:', vybeQubePath);
    }
    
    // Generate Svelte component content
    const componentContent = generateVybeQubeComponent(data);
    
    // Write the file
    const filePath = join(vybeQubePath, '+page.svelte');
    writeFileSync(filePath, componentContent, 'utf8');
    
    console.log(`✅ Created Vybe Qube: ${filePath}`);
    
    return json({
      success: true,
      path: `/vybeqube/${urlSafeName}`,
      filePath: filePath,
      message: 'Vybe Qube created successfully'
    });
    
  } catch (error) {
    console.error('Vybe Qube creation error:', error);
    return json({ 
      success: false, 
      error: 'Failed to create Vybe Qube',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
};

function generateVybeQubeComponent(data: VybeQubeData): string {
  const currentDate = new Date().toLocaleDateString('en-US', {
    month: 'numeric',
    day: 'numeric',
    year: '2-digit',
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  });

  return `<script lang="ts">
  import { onMount } from 'svelte';

  // Vybe Qube data generated by MAS
  const qube = ${JSON.stringify({
    title: data.title,
    description: data.description,
    type: data.outputType,
    generated_at: new Date().toISOString(),
    generation_id: data.generationId,
    agents_used: ['VYBA', 'QUBERT', 'CODEX', 'PIXY', 'DUCKY', 'HAPPY', 'VYBRO'],
    ...data.content
  }, null, 2)};

  onMount(() => {
    console.log('🚀 Vybe Qube loaded:', qube.title);
  });
</script>

<svelte:head>
  <title>{qube.title} - VybeCoding.ai Vybe Qube</title>
  <meta name="description" content={qube.description || ''} />
</svelte:head>

<main class="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900">
  <div class="container mx-auto px-4 py-8">
    <!-- Header with Timestamp -->
    <div class="text-center mb-8">
      <h1 class="text-4xl md:text-6xl font-bold text-white mb-4">
        🚀 {qube.title}
      </h1>
      <p class="text-xl text-gray-300 mb-4">
        {qube.description}
      </p>
      <div class="text-sm text-gray-400">
        Last Updated: ${currentDate}
      </div>
    </div>

    <!-- Qube Content -->
    <div class="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 mb-8">
      <h2 class="text-2xl font-bold text-white mb-4">Generated Content</h2>
      
      {#if qube.features && qube.features.length > 0}
        <div class="mb-6">
          <h3 class="text-lg font-semibold text-white mb-3">Features</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            {#each qube.features as feature}
              <div class="bg-gray-700/50 rounded-lg p-4">
                <div class="text-cyan-400 font-semibold">✨ {feature}</div>
              </div>
            {/each}
          </div>
        </div>
      {/if}

      {#if qube.techStack && qube.techStack.length > 0}
        <div class="mb-6">
          <h3 class="text-lg font-semibold text-white mb-3">Technology Stack</h3>
          <div class="flex flex-wrap gap-2">
            {#each qube.techStack as tech}
              <span class="bg-gradient-to-r from-cyan-600 to-purple-600 text-white px-3 py-1 rounded-full text-sm">
                {tech}
              </span>
            {/each}
          </div>
        </div>
      {/if}

      <!-- Interactive Demo Placeholder -->
      <div class="bg-gray-900 rounded-lg p-8 text-center mb-6">
        <div class="text-6xl mb-4">🎮</div>
        <h3 class="text-xl text-white mb-2">Interactive Demo</h3>
        <p class="text-gray-400">Generated by autonomous MAS agents</p>
        <div class="mt-4">
          <span class="bg-green-600 text-white px-3 py-1 rounded-full text-sm">
            ✅ Generation Complete
          </span>
        </div>
      </div>
    </div>

    <!-- Generation Metadata -->
    <div class="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6">
      <h3 class="text-xl font-bold text-white mb-4">Generation Information</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
        <div>
          <span class="text-gray-400">Generation ID:</span>
          <span class="text-cyan-400 ml-2 font-mono">{qube.generation_id}</span>
        </div>
        <div>
          <span class="text-gray-400">Content Type:</span>
          <span class="text-white ml-2 capitalize">{qube.type}</span>
        </div>
        <div class="md:col-span-2">
          <span class="text-gray-400">MAS Agents:</span>
          <span class="text-cyan-400 ml-2">{qube.agents_used.join(', ')}</span>
        </div>
        <div class="md:col-span-2">
          <span class="text-gray-400">Generated:</span>
          <span class="text-white ml-2">${currentDate}</span>
        </div>
      </div>
    </div>
  </div>
</main>`;
}
