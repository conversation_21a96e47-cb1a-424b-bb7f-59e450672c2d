import { json } from '@sveltejs/kit';
import type { <PERSON>quest<PERSON><PERSON><PERSON> } from './$types';
import { Client, Databases, ID } from 'appwrite';

const client = new Client()
  .setEndpoint('https://cloud.appwrite.io/v1')
  .setProject('vybecoding');

const databases = new Databases(client);

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { title, content, category, tags, author, submittedAt } = await request.json();

    // Validate required fields
    if (!title || !content || !author?.name || !author?.email) {
      return json(
        { error: 'Title, content, author name, and author email are required' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(author.email)) {
      return json(
        { error: 'Please provide a valid email address' },
        { status: 400 }
      );
    }

    // Content validation
    if (content.length < 100) {
      return json(
        { error: 'Article content must be at least 100 characters long' },
        { status: 400 }
      );
    }

    if (content.length > 50000) {
      return json(
        { error: 'Article content must be less than 50,000 characters' },
        { status: 400 }
      );
    }

    // Basic content moderation - check for inappropriate content
    const inappropriateWords = ['spam', 'scam', 'hack', 'illegal'];
    const contentLower = content.toLowerCase();
    const hasInappropriateContent = inappropriateWords.some(word => 
      contentLower.includes(word) && !isEducationalContext(contentLower, word)
    );

    if (hasInappropriateContent) {
      return json(
        { error: 'Content contains inappropriate material. Please review and resubmit.' },
        { status: 400 }
      );
    }

    // Generate article slug
    const slug = generateSlug(title);

    // Create article submission in Appwrite database
    const articleSubmission = await databases.createDocument(
      '683b231d003c1c558e20', // Database ID
      'article_submissions', // Collection ID
      ID.unique(),
      {
        title: title.trim(),
        content: content.trim(),
        category: category || 'community',
        tags: Array.isArray(tags) ? tags : [],
        slug,
        author: {
          name: author.name.trim(),
          email: author.email.trim()
        },
        status: 'pending_review',
        submittedAt: submittedAt || new Date().toISOString(),
        moderationFlags: [],
        reviewNotes: '',
        publishedAt: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    );

    // Log submission for analytics
    console.log('Article submitted:', {
      id: articleSubmission.$id,
      title,
      author: author.name,
      category,
      wordCount: content.split(' ').length
    });

    // Send notification email (in real implementation)
    await sendSubmissionNotification(articleSubmission.$id, author.email, title);

    return json({
      success: true,
      message: 'Article submitted successfully! It will be reviewed and published soon.',
      submissionId: articleSubmission.$id,
      status: 'pending_review',
      estimatedReviewTime: '24-48 hours'
    });

  } catch (error) {
    console.error('Article submission API error:', error);
    
    return json(
      { 
        error: 'Failed to submit article. Please try again later.',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
};

function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim()
    .substring(0, 50); // Limit length
}

function isEducationalContext(content: string, word: string): boolean {
  // Check if potentially inappropriate words are used in educational context
  const educationalPhrases = [
    'ethical hacking',
    'security research',
    'penetration testing',
    'cybersecurity',
    'educational purposes',
    'learning about',
    'understanding how'
  ];

  const wordIndex = content.indexOf(word);
  if (wordIndex === -1) return false;

  // Check surrounding context (100 characters before and after)
  const contextStart = Math.max(0, wordIndex - 100);
  const contextEnd = Math.min(content.length, wordIndex + 100);
  const context = content.substring(contextStart, contextEnd);

  return educationalPhrases.some(phrase => context.includes(phrase));
}

async function sendSubmissionNotification(submissionId: string, authorEmail: string, title: string): Promise<void> {
  try {
    // In a real implementation, this would send an email notification
    // For now, we'll just log it
    console.log('Submission notification:', {
      submissionId,
      authorEmail,
      title,
      message: 'Thank you for your submission! We will review it within 24-48 hours.'
    });

    // Could integrate with email service like:
    // - Appwrite Functions for email
    // - SendGrid API
    // - Mailgun API
    // - SMTP service

  } catch (error) {
    console.error('Failed to send submission notification:', error);
    // Don't throw error - submission was successful even if notification failed
  }
}
