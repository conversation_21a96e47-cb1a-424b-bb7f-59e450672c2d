import { json } from '@sveltejs/kit';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export async function POST() {
  try {
    // Stop the MAS Observatory
    const { stdout, stderr } = await execAsync(
      'cd mas-observatory && ./stop-observatory.sh',
      {
        cwd: process.cwd(),
        timeout: 30000, // 30 second timeout
      }
    );

    if (
      stderr &&
      !stderr.includes('Warning') &&
      !stderr.includes('Observatory stopped successfully')
    ) {
      throw new Error(stderr);
    }

    return json({
      success: true,
      message: 'Observatory stopped successfully',
      output: stdout,
    });
  } catch (error) {
    console.error('Failed to stop Observatory:', error);

    return json(
      {
        success: false,
        message: 'Failed to stop Observatory',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
