import { json } from '@sveltejs/kit';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export async function POST() {
  try {
    // Start the MAS Observatory
    const { stdout, stderr } = await execAsync(
      'cd mas-observatory && ./start-observatory.sh',
      {
        cwd: process.cwd(),
        timeout: 60000, // 60 second timeout for Docker operations
      }
    );

    if (stderr && !stderr.includes('Warning')) {
      throw new Error(stderr);
    }

    return json({
      success: true,
      message: 'Observatory started successfully',
      output: stdout,
    });
  } catch (error) {
    console.error('Failed to start Observatory:', error);

    return json(
      {
        success: false,
        message: 'Failed to start Observatory',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
