import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async () => {
  try {
    // Check multiple observatory endpoints to determine status
    const endpoints = [
      { name: '<PERSON><PERSON>', url: 'http://localhost:3001', icon: '🎛️' },
      { name: 'Prometheus', url: 'http://localhost:9091', icon: '📈' },
      { name: 'Netdata', url: 'http://localhost:19999', icon: '⚡' },
      { name: '<PERSON><PERSON><PERSON>', url: 'http://localhost:16686', icon: '🔍' },
      { name: '<PERSON><PERSON>', url: 'http://localhost:5601', icon: '📊' },
      { name: 'MAS API', url: 'http://localhost:5173', icon: '🤖' },
      { name: '<PERSON><PERSON><PERSON>', url: 'http://localhost:9000', icon: '🐳' },
    ];

    const serviceStatus = [];
    let healthyCount = 0;

    for (const endpoint of endpoints) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 2000);

        const response = await fetch(endpoint.url, {
          method: 'HEAD',
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        if (response.ok || response.status < 500) {
          serviceStatus.push({
            name: endpoint.name,
            url: endpoint.url,
            icon: endpoint.icon,
            status: 'healthy',
          });
          healthyCount++;
        } else {
          serviceStatus.push({
            name: endpoint.name,
            url: endpoint.url,
            icon: endpoint.icon,
            status: 'error',
            statusCode: response.status,
          });
        }
      } catch (error) {
        serviceStatus.push({
          name: endpoint.name,
          url: endpoint.url,
          icon: endpoint.icon,
          status: 'offline',
          error: error.message,
        });
      }
    }

    let overallStatus = 'offline';
    if (healthyCount >= 3) {
      overallStatus = 'running';
    } else if (healthyCount >= 1) {
      overallStatus = 'partial';
    }

    return json({
      success: true,
      status: overallStatus,
      healthyServices: healthyCount,
      totalServices: endpoints.length,
      services: serviceStatus,
      autonomousMode: healthyCount >= 3,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Failed to check observatory status:', error);

    return json(
      {
        success: false,
        status: 'offline',
        error: 'Failed to check observatory status',
        details: error.message,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
};
