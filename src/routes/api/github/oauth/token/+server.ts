/**
 * GitHub OAuth Token Exchange API Endpoint
 * Handles the server-side OAuth flow for GitHub integration
 */

import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

const GITHUB_CLIENT_ID = process.env.GITHUB_CLIENT_ID;
const GITHUB_CLIENT_SECRET = process.env.GITHUB_CLIENT_SECRET;

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { code, state } = await request.json();

    if (!code) {
      return json({ error: 'Authorization code is required' }, { status: 400 });
    }

    if (!GITHUB_CLIENT_ID || !GITHUB_CLIENT_SECRET) {
      return json({ error: 'GitHub OAuth not configured' }, { status: 500 });
    }

    // Exchange authorization code for access token
    const tokenResponse = await fetch(
      'https://github.com/login/oauth/access_token',
      {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          client_id: GITHUB_CLIENT_ID,
          client_secret: GITHUB_CLIENT_SECRET,
          code,
          state,
        }),
      }
    );

    if (!tokenResponse.ok) {
      throw new Error('Failed to exchange code for token');
    }

    const tokenData = await tokenResponse.json();

    if (tokenData.error) {
      throw new Error(tokenData.error_description || tokenData.error);
    }

    // Return the access token to the client
    return json({
      access_token: tokenData.access_token,
      token_type: tokenData.token_type,
      scope: tokenData.scope,
    });
  } catch (error) {
    console.error('GitHub OAuth error:', error);
    return json(
      {
        error: error instanceof Error ? error.message : 'OAuth exchange failed',
      },
      { status: 500 }
    );
  }
};
