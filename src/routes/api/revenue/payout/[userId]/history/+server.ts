import { json, error } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

const REVENUE_SERVICE_URL =
  process.env.REVENUE_SERVICE_URL || 'http://localhost:8003';

export const GET: RequestHandler = async ({ params, url }) => {
  try {
    const { userId } = params;
    const limit = url.searchParams.get('limit') || '20';

    // Forward request to revenue tracking service
    const response = await fetch(
      `${REVENUE_SERVICE_URL}/payout/${userId}/history?limit=${limit}`
    );

    if (!response.ok) {
      throw new Error(`Revenue service error: ${response.statusText}`);
    }

    const data = await response.json();
    return json(data);
  } catch (err) {
    console.error('Error fetching payout history:', err);
    throw error(500, 'Failed to fetch payout history');
  }
};
