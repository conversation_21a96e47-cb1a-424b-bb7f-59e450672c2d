import { json, error } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

const REVENUE_SERVICE_URL =
  process.env.REVENUE_SERVICE_URL || 'http://localhost:8003';

export const GET: RequestHandler = async ({ params }) => {
  try {
    const { userId } = params;

    // Forward request to revenue tracking service
    const response = await fetch(`${REVENUE_SERVICE_URL}/payout/${userId}`);

    if (!response.ok) {
      throw new Error(`Revenue service error: ${response.statusText}`);
    }

    const data = await response.json();
    return json(data);
  } catch (err) {
    console.error('Error fetching payout info:', err);
    throw error(500, 'Failed to fetch payout information');
  }
};

export const POST: RequestHandler = async ({ params, request }) => {
  try {
    const { userId } = params;
    const body = await request.json();

    // Forward request to revenue tracking service
    const response = await fetch(
      `${REVENUE_SERVICE_URL}/payout/${userId}/request`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      }
    );

    if (!response.ok) {
      throw new Error(`Revenue service error: ${response.statusText}`);
    }

    const data = await response.json();
    return json(data);
  } catch (err) {
    console.error('Error requesting payout:', err);
    throw error(500, 'Failed to request payout');
  }
};
