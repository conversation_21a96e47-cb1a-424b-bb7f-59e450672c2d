import { json, error } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

const REVENUE_SERVICE_URL =
  process.env.REVENUE_SERVICE_URL || 'http://localhost:8003';

export const GET: RequestHandler = async ({ params }) => {
  try {
    const { userId } = params;

    // Forward request to revenue tracking service
    const response = await fetch(`${REVENUE_SERVICE_URL}/insights/${userId}`);

    if (!response.ok) {
      throw new Error(`Revenue service error: ${response.statusText}`);
    }

    const data = await response.json();
    return json(data);
  } catch (err) {
    console.error('Error fetching revenue insights:', err);
    throw error(500, 'Failed to fetch revenue insights');
  }
};
