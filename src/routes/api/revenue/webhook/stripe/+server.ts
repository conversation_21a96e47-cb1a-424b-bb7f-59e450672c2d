import { json, error } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

const REVENUE_SERVICE_URL =
  process.env.REVENUE_SERVICE_URL || 'http://localhost:8003';

export const POST: RequestHandler = async ({ request }) => {
  try {
    // Get the raw body and headers
    const body = await request.text();
    const signature = request.headers.get('stripe-signature');

    if (!signature) {
      throw error(400, 'Missing Stripe signature');
    }

    // Forward the webhook to the revenue tracking service
    const response = await fetch(`${REVENUE_SERVICE_URL}/webhook/stripe`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'stripe-signature': signature,
      },
      body: body,
    });

    if (!response.ok) {
      throw new Error(`Revenue service error: ${response.statusText}`);
    }

    const data = await response.json();
    return json(data);
  } catch (err) {
    console.error('Error processing Stripe webhook:', err);
    throw error(500, 'Failed to process webhook');
  }
};
