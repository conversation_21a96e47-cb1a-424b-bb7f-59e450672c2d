import { json, error } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

const REVENUE_SERVICE_URL =
  process.env.REVENUE_SERVICE_URL || 'http://localhost:8003';

export const GET: RequestHandler = async ({ params, url }) => {
  try {
    const { userId } = params;
    const qubeId = url.searchParams.get('qube_id');

    let apiUrl = `${REVENUE_SERVICE_URL}/analytics/${userId}`;
    if (qubeId) {
      apiUrl += `?qube_id=${qubeId}`;
    }

    // Forward request to revenue tracking service
    const response = await fetch(apiUrl);

    if (!response.ok) {
      throw new Error(`Revenue service error: ${response.statusText}`);
    }

    const data = await response.json();
    return json(data);
  } catch (err) {
    console.error('Error fetching revenue analytics:', err);
    throw error(500, 'Failed to fetch revenue analytics');
  }
};
