import { json, error } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

const REVENUE_SERVICE_URL =
  process.env.REVENUE_SERVICE_URL || 'http://localhost:8003';

export const POST: RequestHandler = async ({ request }) => {
  try {
    const body = await request.json();
    const { userId, email, country = 'US' } = body;

    if (!userId || !email) {
      throw error(400, 'Missing required fields: userId and email');
    }

    // Forward request to revenue tracking service
    const response = await fetch(
      `${REVENUE_SERVICE_URL}/stripe/create-account`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId, email, country }),
      }
    );

    if (!response.ok) {
      throw new Error(`Revenue service error: ${response.statusText}`);
    }

    const data = await response.json();
    return json(data);
  } catch (err) {
    console.error('Error creating Stripe account:', err);
    throw error(500, 'Failed to create Stripe account');
  }
};
