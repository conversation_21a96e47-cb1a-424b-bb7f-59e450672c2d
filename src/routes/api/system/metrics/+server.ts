import { json } from '@sveltejs/kit';
import type { Request<PERSON>and<PERSON> } from './$types';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export const GET: RequestHandler = async () => {
  try {
    // Get real system metrics using system commands
    const metrics = await getRealSystemMetrics();
    return json(metrics);
  } catch (error) {
    console.error('Error fetching system metrics:', error);
    // Return null so UI knows no real data is available
    return json(null);
  }
};

async function getRealSystemMetrics() {
  try {
    // Get CPU usage
    const cpuUsage = await getCPUUsage();

    // Get memory usage
    const memoryUsage = await getMemoryUsage();

    // Get GPU usage (if nvidia-smi is available)
    const gpuUsage = await getGPUUsage();

    return {
      cpu: {
        usage: cpuUsage.usage,
        temperature: cpuUsage.temperature,
      },
      ram: {
        used: memoryUsage.used,
        total: memoryUsage.total,
      },
      gpu: gpuUsage
        ? {
            utilization: gpuUsage.utilization,
            temperature: gpuUsage.temperature,
            memory: gpuUsage.memory,
          }
        : null,
    };
  } catch (error) {
    throw new Error('Failed to get system metrics');
  }
}

async function getCPUUsage() {
  try {
    // Get CPU usage percentage
    const { stdout } = await execAsync(
      "top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1"
    );
    const usage = parseFloat(stdout.trim()) || 0;

    // Try to get CPU temperature (varies by system)
    let temperature = 0;
    try {
      const { stdout: tempOut } = await execAsync(
        "sensors | grep 'Core 0' | awk '{print $3}' | cut -d'+' -f2 | cut -d'°' -f1"
      );
      temperature = parseFloat(tempOut.trim()) || 0;
    } catch {
      // Temperature not available
    }

    return { usage, temperature };
  } catch (error) {
    return { usage: 0, temperature: 0 };
  }
}

async function getMemoryUsage() {
  try {
    const { stdout } = await execAsync(
      "free -m | grep '^Mem:' | awk '{print $3,$2}'"
    );
    const [used, total] = stdout.trim().split(' ').map(Number);

    return {
      used: used / 1024, // Convert MB to GB
      total: total / 1024,
    };
  } catch (error) {
    return { used: 0, total: 16 };
  }
}

async function getGPUUsage() {
  try {
    // Check if nvidia-smi is available
    const { stdout } = await execAsync(
      'nvidia-smi --query-gpu=utilization.gpu,temperature.gpu,memory.used --format=csv,noheader,nounits'
    );
    const [utilization, temperature, memory] = stdout
      .trim()
      .split(', ')
      .map(Number);

    return {
      utilization,
      temperature,
      memory: memory / 1024, // Convert MB to GB
    };
  } catch (error) {
    // No NVIDIA GPU or nvidia-smi not available
    return null;
  }
}
