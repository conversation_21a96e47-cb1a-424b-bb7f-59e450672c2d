import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

interface SystemHealth {
  timestamp: string;
  overall_status: 'healthy' | 'degraded' | 'unhealthy';
  services: {
    [key: string]: {
      status: 'running' | 'stopped' | 'error';
      cpu_usage?: number;
      memory_usage?: number;
      uptime?: number;
      last_check: string;
    };
  };
  agents: {
    [key: string]: {
      cpuUsage: number;
      memoryUsage: number;
      status: 'active' | 'idle' | 'error';
      modelLoaded: boolean;
    };
  };
  system: {
    cpu_usage: number;
    memory_usage: number;
    disk_usage: number;
    load_average: number[];
  };
}

async function getSystemMetrics() {
  try {
    // Get CPU usage
    const { stdout: cpuInfo } = await execAsync("top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1");
    const cpuUsage = parseFloat(cpuInfo.trim()) || 0;

    // Get memory usage
    const { stdout: memInfo } = await execAsync("free | grep Mem | awk '{printf \"%.1f\", $3/$2 * 100.0}'");
    const memoryUsage = parseFloat(memInfo.trim()) || 0;

    // Get disk usage
    const { stdout: diskInfo } = await execAsync("df -h / | awk 'NR==2 {print $5}' | cut -d'%' -f1");
    const diskUsage = parseFloat(diskInfo.trim()) || 0;

    // Get load average
    const { stdout: loadInfo } = await execAsync("uptime | awk -F'load average:' '{print $2}' | sed 's/,//g'");
    const loadAverage = loadInfo.trim().split(' ').map(l => parseFloat(l.trim())).filter(l => !isNaN(l));

    return {
      cpu_usage: cpuUsage,
      memory_usage: memoryUsage,
      disk_usage: diskUsage,
      load_average: loadAverage.length > 0 ? loadAverage : [0, 0, 0]
    };
  } catch (error) {
    console.warn('Failed to get system metrics:', error);
    return {
      cpu_usage: 0,
      memory_usage: 0,
      disk_usage: 0,
      load_average: [0, 0, 0]
    };
  }
}

async function checkServiceHealth(serviceName: string, port?: number) {
  try {
    if (port) {
      // Check if service is listening on port
      const { stdout } = await execAsync(`netstat -tuln | grep :${port} || echo "not_found"`);
      const isRunning = !stdout.includes('not_found');
      
      if (isRunning) {
        // Try to get process info
        try {
          const { stdout: processInfo } = await execAsync(`lsof -ti:${port} | head -1`);
          const pid = processInfo.trim();
          
          if (pid) {
            const { stdout: cpuMem } = await execAsync(`ps -p ${pid} -o %cpu,%mem --no-headers`);
            const [cpu, mem] = cpuMem.trim().split(/\s+/).map(v => parseFloat(v));
            
            return {
              status: 'running' as const,
              cpu_usage: cpu || 0,
              memory_usage: mem || 0,
              last_check: new Date().toISOString()
            };
          }
        } catch (processError) {
          console.warn(`Failed to get process info for ${serviceName}:`, processError);
        }
        
        return {
          status: 'running' as const,
          last_check: new Date().toISOString()
        };
      }
    }
    
    return {
      status: 'stopped' as const,
      last_check: new Date().toISOString()
    };
  } catch (error) {
    console.warn(`Failed to check ${serviceName} health:`, error);
    return {
      status: 'error' as const,
      last_check: new Date().toISOString()
    };
  }
}

async function getAgentHealth() {
  // Simulate agent health data - in real implementation, this would connect to actual agent processes
  const agents = ['vyba', 'qubert', 'codex', 'pixy', 'ducky', 'happy', 'vybro'];
  const agentHealth: { [key: string]: any } = {};
  
  for (const agent of agents) {
    // Check if agent process is running (placeholder - would check actual processes)
    const isActive = Math.random() > 0.5; // Random for demo
    
    agentHealth[agent] = {
      cpuUsage: isActive ? Math.random() * 50 + 10 : 0, // 10-60% when active
      memoryUsage: isActive ? Math.random() * 40 + 20 : 0, // 20-60% when active
      status: isActive ? 'active' : 'idle',
      modelLoaded: isActive
    };
  }
  
  return agentHealth;
}

export const GET: RequestHandler = async () => {
  try {
    const timestamp = new Date().toISOString();
    
    // Get system metrics
    const systemMetrics = await getSystemMetrics();
    
    // Check service health
    const services = {
      ollama: await checkServiceHealth('ollama', 11434),
      appwrite: await checkServiceHealth('appwrite', 80),
      redis: await checkServiceHealth('redis', 6379),
      postgresql: await checkServiceHealth('postgresql', 5432),
      mcp_server: await checkServiceHealth('mcp_server', 3001),
      websocket_server: await checkServiceHealth('websocket_server', 8080)
    };
    
    // Get agent health
    const agents = await getAgentHealth();
    
    // Determine overall status
    const serviceStatuses = Object.values(services).map(s => s.status);
    const runningServices = serviceStatuses.filter(s => s === 'running').length;
    const totalServices = serviceStatuses.length;
    
    let overall_status: 'healthy' | 'degraded' | 'unhealthy';
    if (runningServices === totalServices) {
      overall_status = 'healthy';
    } else if (runningServices >= totalServices * 0.7) {
      overall_status = 'degraded';
    } else {
      overall_status = 'unhealthy';
    }
    
    const health: SystemHealth = {
      timestamp,
      overall_status,
      services,
      agents,
      system: systemMetrics
    };
    
    return json({
      success: true,
      data: health
    });
    
  } catch (error) {
    console.error('Health check error:', error);
    
    return json({
      success: false,
      error: 'Failed to get system health',
      data: {
        timestamp: new Date().toISOString(),
        overall_status: 'unhealthy',
        services: {},
        agents: {},
        system: {
          cpu_usage: 0,
          memory_usage: 0,
          disk_usage: 0,
          load_average: [0, 0, 0]
        }
      }
    }, { status: 500 });
  }
};

// POST endpoint for updating agent health manually
export const POST: RequestHandler = async ({ request }) => {
  try {
    const { agent_id, health_data } = await request.json();
    
    if (!agent_id || !health_data) {
      return json({
        success: false,
        error: 'agent_id and health_data are required'
      }, { status: 400 });
    }
    
    // In a real implementation, this would update agent health in a database or cache
    console.log(`Updating health for agent ${agent_id}:`, health_data);
    
    return json({
      success: true,
      message: `Health updated for agent ${agent_id}`
    });
    
  } catch (error) {
    console.error('Health update error:', error);
    return json({
      success: false,
      error: 'Failed to update agent health'
    }, { status: 500 });
  }
};
