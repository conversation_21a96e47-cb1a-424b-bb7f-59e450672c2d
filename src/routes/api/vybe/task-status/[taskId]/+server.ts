import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { getTask } from '$lib/server/vybe-storage';

export const GET: RequestHandler = async ({ params }) => {
  try {
    const { taskId } = params;

    if (!taskId) {
      return json({ error: 'Task ID is required' }, { status: 400 });
    }

    const task = getTask(taskId);

    if (!task) {
      return json({ error: 'Task not found' }, { status: 404 });
    }

    return json(task);
  } catch (error) {
    console.error('Error fetching task status:', error);
    return json(
      {
        error: 'Failed to fetch task status',
      },
      { status: 500 }
    );
  }
};
