/**
 * Vybe Qube Status API Endpoint
 * Provides real-time status updates for Vybe Qube generation
 * Based on Alex's Technical Architecture (ALEX-001-VYBE-BUTTONS)
 */

import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

// Import qubes from generate-qube endpoint
// In production, this would be a database query
const qubes = new Map<string, any>();

export const GET: RequestHandler = async ({ params, locals }) => {
  try {
    const { qubeId } = params;

    if (!qubeId) {
      return json({ error: 'Qube ID is required' }, { status: 400 });
    }

    // Get qube from storage
    const qube = qubes.get(qubeId);

    if (!qube) {
      return json({ error: 'Qube not found' }, { status: 404 });
    }

    // Check if user has access to this qube
    const userId = locals.user?.id || 'anonymous';
    if (qube.userId !== userId) {
      return json({ error: 'Access denied' }, { status: 403 });
    }

    // Prepare response
    const response = {
      qubeId: qube.id,
      status: qube.status,
      progress: qube.progress || null,
      qube: qube.qube || null,
      error: qube.error || null,
      createdAt: qube.createdAt,
      completedAt: qube.completedAt || null,
      estimatedCompletion: qube.estimatedCompletion,
    };

    return json(response);
  } catch (error) {
    console.error('Qube status check error:', error);
    return json({ error: 'Failed to get qube status' }, { status: 500 });
  }
};
