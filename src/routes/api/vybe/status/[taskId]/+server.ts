/**
 * Vybe Task Status API Endpoint
 * Provides real-time status updates for Vybe Method processing tasks
 * Based on Alex's Technical Architecture (ALEX-001-VYBE-BUTTONS)
 */

import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { getTask } from '$lib/server/vybe-storage';

export const GET: RequestHandler = async ({ params, locals }) => {
  try {
    const { taskId } = params;

    if (!taskId) {
      return json({ error: 'Task ID is required' }, { status: 400 });
    }

    // Get task from storage
    const task = getTask(taskId);

    if (!task) {
      return json({ error: 'Task not found' }, { status: 404 });
    }

    // Check if user has access to this task
    const userId = locals.user?.id || 'anonymous';
    if (task.userId !== userId) {
      return json({ error: 'Access denied' }, { status: 403 });
    }

    // Prepare response
    const response = {
      taskId: task.id,
      status: task.status,
      progress: task.progress,
      currentAgent: task.currentAgent,
      result: task.result || null,
      error: task.error || null,
      logs: task.logs || [],
      createdAt: task.createdAt,
      completedAt: task.completedAt || null,
      estimatedDuration: task.estimatedDuration,
    };

    return json(response);
  } catch (error) {
    console.error('Status check error:', error);
    return json({ error: 'Failed to get task status' }, { status: 500 });
  }
};
