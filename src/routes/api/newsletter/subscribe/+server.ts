import { json } from '@sveltejs/kit';
import type { <PERSON>quest<PERSON><PERSON><PERSON> } from './$types';
import { Client, Databases, ID } from 'appwrite';

// Appwrite configuration
const client = new Client()
  .setEndpoint('https://cloud.appwrite.io/v1')
  .setProject('683b231d003c1c558e20');

const databases = new Databases(client);

interface NewsletterSubscription {
  email: string;
  subscribed_at: string;
  status: 'active' | 'unsubscribed';
  source: string;
}

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { email } = await request.json();

    // Validate email
    if (!email || typeof email !== 'string') {
      return json({
        success: false,
        error: 'Email is required'
      }, { status: 400 });
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return json({
        success: false,
        error: 'Invalid email format'
      }, { status: 400 });
    }

    // Check if email already exists
    try {
      const existingSubscriptions = await databases.listDocuments(
        '683b231d003c1c558e20', // database ID
        'newsletter_subscriptions', // collection ID
        [
          `email=${email}`
        ]
      );

      if (existingSubscriptions.documents.length > 0) {
        const existing = existingSubscriptions.documents[0];
        if (existing.status === 'active') {
          return json({
            success: false,
            error: 'Email is already subscribed'
          }, { status: 409 });
        } else {
          // Reactivate subscription
          await databases.updateDocument(
            '683b231d003c1c558e20',
            'newsletter_subscriptions',
            existing.$id,
            {
              status: 'active',
              subscribed_at: new Date().toISOString()
            }
          );

          return json({
            success: true,
            message: 'Newsletter subscription reactivated successfully'
          });
        }
      }
    } catch (error) {
      console.warn('Error checking existing subscription:', error);
      // Continue with new subscription if check fails
    }

    // Create new subscription
    const subscription: NewsletterSubscription = {
      email: email.toLowerCase().trim(),
      subscribed_at: new Date().toISOString(),
      status: 'active',
      source: 'website_footer'
    };

    const document = await databases.createDocument(
      '683b231d003c1c558e20', // database ID
      'newsletter_subscriptions', // collection ID
      ID.unique(),
      subscription
    );

    console.log('Newsletter subscription created:', document.$id);

    return json({
      success: true,
      message: 'Successfully subscribed to newsletter',
      subscription_id: document.$id
    });

  } catch (error) {
    console.error('Newsletter subscription error:', error);
    
    // Handle specific Appwrite errors
    if (error instanceof Error) {
      if (error.message.includes('Collection not found')) {
        return json({
          success: false,
          error: 'Newsletter service temporarily unavailable'
        }, { status: 503 });
      }
      
      if (error.message.includes('Document already exists')) {
        return json({
          success: false,
          error: 'Email is already subscribed'
        }, { status: 409 });
      }
    }

    return json({
      success: false,
      error: 'Failed to subscribe to newsletter'
    }, { status: 500 });
  }
};

export const DELETE: RequestHandler = async ({ request }) => {
  try {
    const { email } = await request.json();

    if (!email) {
      return json({
        success: false,
        error: 'Email is required'
      }, { status: 400 });
    }

    // Find subscription
    const subscriptions = await databases.listDocuments(
      '683b231d003c1c558e20',
      'newsletter_subscriptions',
      [
        `email=${email.toLowerCase().trim()}`
      ]
    );

    if (subscriptions.documents.length === 0) {
      return json({
        success: false,
        error: 'Email not found in newsletter'
      }, { status: 404 });
    }

    // Update status to unsubscribed
    const subscription = subscriptions.documents[0];
    await databases.updateDocument(
      '683b231d003c1c558e20',
      'newsletter_subscriptions',
      subscription.$id,
      {
        status: 'unsubscribed',
        unsubscribed_at: new Date().toISOString()
      }
    );

    return json({
      success: true,
      message: 'Successfully unsubscribed from newsletter'
    });

  } catch (error) {
    console.error('Newsletter unsubscribe error:', error);
    return json({
      success: false,
      error: 'Failed to unsubscribe from newsletter'
    }, { status: 500 });
  }
};

export const GET: RequestHandler = async ({ url }) => {
  try {
    const email = url.searchParams.get('email');
    
    if (!email) {
      return json({
        success: false,
        error: 'Email parameter is required'
      }, { status: 400 });
    }

    // Check subscription status
    const subscriptions = await databases.listDocuments(
      '683b231d003c1c558e20',
      'newsletter_subscriptions',
      [
        `email=${email.toLowerCase().trim()}`
      ]
    );

    if (subscriptions.documents.length === 0) {
      return json({
        success: true,
        subscribed: false,
        message: 'Email not found in newsletter'
      });
    }

    const subscription = subscriptions.documents[0];
    return json({
      success: true,
      subscribed: subscription.status === 'active',
      subscription_date: subscription.subscribed_at,
      status: subscription.status
    });

  } catch (error) {
    console.error('Newsletter status check error:', error);
    return json({
      success: false,
      error: 'Failed to check newsletter status'
    }, { status: 500 });
  }
};
