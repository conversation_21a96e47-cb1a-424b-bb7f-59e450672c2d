/**
 * Real-time WebSocket API Endpoint
 * Provides WebSocket connections for real-time MAS Observatory updates
 */

import { json } from '@sveltejs/kit';
import type { <PERSON>questHand<PERSON> } from './$types';

// In-memory storage for active connections and data
const activeConnections = new Set<WebSocket>();
const agentStatuses = new Map();
const systemMetrics = {
  cpu: 0,
  memory: 0,
  disk: 0,
  network: { incoming: 0, outgoing: 0 },
  timestamp: new Date()
};

// Simulate real-time data updates
let dataUpdateInterval: NodeJS.Timeout | null = null;

function startDataSimulation() {
  if (dataUpdateInterval) return;

  dataUpdateInterval = setInterval(() => {
    // Update agent statuses
    const agents = ['VYBA', 'QUBERT', 'CODEX', 'PIXY', 'VYBRO', 'DUCKY', 'HAPPY'];
    
    agents.forEach(agentName => {
      const status = {
        id: agentName.toLowerCase(),
        name: agent<PERSON><PERSON>,
        status: Math.random() > 0.7 ? 'active' : Math.random() > 0.5 ? 'busy' : 'idle',
        currentTask: Math.random() > 0.5 ? `Processing ${Math.random() > 0.5 ? 'content generation' : 'code analysis'}` : undefined,
        progress: Math.random() > 0.5 ? Math.floor(Math.random() * 100) : undefined,
        lastActivity: new Date(),
        performance: {
          tasksCompleted: Math.floor(Math.random() * 50) + 10,
          averageResponseTime: Math.floor(Math.random() * 2000) + 500,
          successRate: 85 + Math.random() * 15
        }
      };
      
      agentStatuses.set(agentName, status);
      broadcastToClients('agent-status', status);
    });

    // Update system metrics
    const newMetrics = {
      cpu: Math.max(10, Math.min(90, systemMetrics.cpu + (Math.random() - 0.5) * 10)),
      memory: Math.max(20, Math.min(85, systemMetrics.memory + (Math.random() - 0.5) * 5)),
      disk: Math.max(30, Math.min(70, systemMetrics.disk + (Math.random() - 0.5) * 2)),
      network: {
        incoming: Math.floor(Math.random() * 1000) + 100,
        outgoing: Math.floor(Math.random() * 500) + 50
      },
      timestamp: new Date()
    };
    
    Object.assign(systemMetrics, newMetrics);
    broadcastToClients('system-metrics', newMetrics);

    // Occasionally send conversation messages
    if (Math.random() > 0.8) {
      const fromAgent = agents[Math.floor(Math.random() * agents.length)];
      const toAgent = agents[Math.floor(Math.random() * agents.length)];
      
      const messages = [
        'Analysis complete, passing results to next phase',
        'Code generation in progress, estimated completion in 2 minutes',
        'Quality check passed, content meets standards',
        'Requesting consensus on design approach',
        'Optimization suggestions identified',
        'Ready for handoff to next agent'
      ];
      
      const conversation = {
        id: `msg-${Date.now()}`,
        fromAgent,
        toAgent,
        message: messages[Math.floor(Math.random() * messages.length)],
        type: Math.random() > 0.7 ? 'handoff' : Math.random() > 0.5 ? 'collaboration' : 'consensus',
        timestamp: new Date()
      };
      
      broadcastToClients('conversation', conversation);
    }

  }, 2000); // Update every 2 seconds
}

function stopDataSimulation() {
  if (dataUpdateInterval) {
    clearInterval(dataUpdateInterval);
    dataUpdateInterval = null;
  }
}

function broadcastToClients(type: string, payload: any) {
  const message = JSON.stringify({ type, payload });
  
  activeConnections.forEach(ws => {
    try {
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(message);
      }
    } catch (error) {
      console.error('Failed to send message to client:', error);
      activeConnections.delete(ws);
    }
  });
}

export const GET: RequestHandler = async ({ request }) => {
  // Check if this is a WebSocket upgrade request
  const upgrade = request.headers.get('upgrade');
  
  if (upgrade !== 'websocket') {
    // Return HTTP endpoint info
    return json({
      service: 'Real-time Monitoring API',
      version: '1.0.0',
      websocket: 'Use WebSocket upgrade to connect',
      endpoints: {
        'GET /api/realtime': 'WebSocket upgrade endpoint',
        'GET /api/realtime/status': 'Connection status',
        'GET /api/realtime/agents': 'Current agent statuses',
        'GET /api/realtime/metrics': 'Current system metrics'
      },
      activeConnections: activeConnections.size
    });
  }

  // Handle WebSocket upgrade
  try {
    const { socket, response } = Deno.upgradeWebSocket(request);
    
    socket.onopen = () => {
      console.log('✅ New real-time monitoring client connected');
      activeConnections.add(socket);
      
      // Start data simulation if this is the first connection
      if (activeConnections.size === 1) {
        startDataSimulation();
      }
      
      // Send current state to new client
      agentStatuses.forEach(status => {
        socket.send(JSON.stringify({ type: 'agent-status', payload: status }));
      });
      
      socket.send(JSON.stringify({ type: 'system-metrics', payload: systemMetrics }));
    };
    
    socket.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        handleClientMessage(socket, data);
      } catch (error) {
        console.error('Failed to parse client message:', error);
      }
    };
    
    socket.onclose = () => {
      console.log('🔌 Real-time monitoring client disconnected');
      activeConnections.delete(socket);
      
      // Stop data simulation if no more connections
      if (activeConnections.size === 0) {
        stopDataSimulation();
      }
    };
    
    socket.onerror = (error) => {
      console.error('WebSocket error:', error);
      activeConnections.delete(socket);
    };
    
    return response;
    
  } catch (error) {
    console.error('WebSocket upgrade failed:', error);
    return json({ error: 'WebSocket upgrade failed' }, { status: 400 });
  }
};

function handleClientMessage(socket: WebSocket, data: any) {
  switch (data.type) {
    case 'get-agent-status':
      if (data.agentId) {
        const status = agentStatuses.get(data.agentId.toUpperCase());
        if (status) {
          socket.send(JSON.stringify({ type: 'agent-status', payload: status }));
        }
      }
      break;
      
    case 'get-all-agents':
      agentStatuses.forEach(status => {
        socket.send(JSON.stringify({ type: 'agent-status', payload: status }));
      });
      break;
      
    case 'get-system-metrics':
      socket.send(JSON.stringify({ type: 'system-metrics', payload: systemMetrics }));
      break;
      
    case 'ping':
      socket.send(JSON.stringify({ type: 'pong', timestamp: new Date() }));
      break;
      
    default:
      console.warn('Unknown client message type:', data.type);
  }
}

// HTTP endpoints for fallback polling
export const POST: RequestHandler = async ({ request }) => {
  try {
    const { action } = await request.json();
    
    switch (action) {
      case 'get-agents':
        return json({
          success: true,
          agents: Array.from(agentStatuses.values())
        });
        
      case 'get-metrics':
        return json({
          success: true,
          metrics: systemMetrics
        });
        
      case 'get-status':
        return json({
          success: true,
          status: {
            activeConnections: activeConnections.size,
            dataSimulationActive: dataUpdateInterval !== null,
            uptime: process.uptime?.() || 0
          }
        });
        
      default:
        return json({ error: 'Invalid action' }, { status: 400 });
    }
    
  } catch (error) {
    console.error('Real-time API error:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};
