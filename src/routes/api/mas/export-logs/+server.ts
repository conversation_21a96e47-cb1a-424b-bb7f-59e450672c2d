import { json } from '@sveltejs/kit';
import type { Request<PERSON><PERSON><PERSON> } from './$types';
import { exec } from 'child_process';
import { promisify } from 'util';
import { writeFile } from 'fs/promises';
import path from 'path';

const execAsync = promisify(exec);

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { format = 'json' } = await request.json();

    // Get logs from all MAS containers
    const containers = [
      'mas-coordinator',
      'mas-grafana',
      'mas-prometheus',
      'mas-netdata',
      'mas-kibana',
      'mas-elasticsearch',
      'mas-jaeger',
      'mas-metrics-exporter',
    ];

    const logs: Record<string, string> = {};

    // Collect logs from each container
    for (const container of containers) {
      try {
        const { stdout } = await execAsync(
          `docker logs --tail 100 ${container} 2>&1`
        );
        logs[container] = stdout;
      } catch (error) {
        logs[container] = `Error getting logs: ${error}`;
      }
    }

    // Add system information
    const systemInfo = {
      timestamp: new Date().toISOString(),
      containers: Object.keys(logs),
      export_format: format,
    };

    // Create export data
    const exportData = {
      system_info: systemInfo,
      container_logs: logs,
    };

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `mas-logs-export-${timestamp}.${format}`;

    if (format === 'json') {
      // Return JSON data for download
      return new Response(JSON.stringify(exportData, null, 2), {
        headers: {
          'Content-Type': 'application/json',
          'Content-Disposition': `attachment; filename="${filename}"`,
        },
      });
    } else if (format === 'txt') {
      // Create text format
      let textContent = `MAS Observatory Log Export\n`;
      textContent += `Generated: ${systemInfo.timestamp}\n`;
      textContent += `Containers: ${systemInfo.containers.join(', ')}\n\n`;
      textContent += '='.repeat(80) + '\n\n';

      for (const [container, log] of Object.entries(logs)) {
        textContent += `CONTAINER: ${container}\n`;
        textContent += '-'.repeat(40) + '\n';
        textContent += log + '\n\n';
      }

      return new Response(textContent, {
        headers: {
          'Content-Type': 'text/plain',
          'Content-Disposition': `attachment; filename="${filename}"`,
        },
      });
    }

    return json({ success: true, message: 'Logs exported successfully' });
  } catch (error) {
    console.error('Error exporting logs:', error);
    return json(
      {
        error: error instanceof Error ? error.message : 'Failed to export logs',
      },
      { status: 500 }
    );
  }
};
