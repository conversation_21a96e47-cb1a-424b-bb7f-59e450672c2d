/**
 * DUCKY Agent API Endpoint
 * Quality Assurance Perfectionist for Multi-Agent Consensus
 * SWR-001: AI Mentor Chat Interface - Zero Hallucination Validation
 */

import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { prompt, context } = await request.json();
    
    // DUCKY specializes in quality validation and accuracy checking
    const duckyResponse = await generateDuckyResponse(prompt, context);
    
    return json({
      response: JSON.stringify(duckyResponse),
      confidence: duckyResponse.confidence,
      reasoning: duckyResponse.reasoning
    });
  } catch (error) {
    console.error('DUCKY agent error:', error);
    return json(
      { error: 'DUCKY agent temporarily unavailable' },
      { status: 500 }
    );
  }
};

async function generateDuckyResponse(prompt: string, context: any) {
  // DUCKY performs comprehensive quality validation
  const { originalResponse, userMessage, codeContext, language } = context;
  
  const accuracyCheck = validateAccuracy(originalResponse, userMessage, codeContext, language);
  const clarityCheck = validateClarity(originalResponse);
  const completenessCheck = validateCompleteness(originalResponse, userMessage);
  const safetyCheck = validateSafety(originalResponse);
  
  const overallAccuracy = accuracyCheck.isAccurate && 
                         clarityCheck.isClear && 
                         completenessCheck.isComplete && 
                         safetyCheck.isSafe;
  
  const overallConfidence = Math.min(
    accuracyCheck.confidence,
    clarityCheck.confidence,
    completenessCheck.confidence,
    safetyCheck.confidence
  );
  
  return {
    isAccurate: overallAccuracy,
    confidence: overallConfidence,
    concerns: [
      ...accuracyCheck.concerns,
      ...clarityCheck.concerns,
      ...completenessCheck.concerns,
      ...safetyCheck.concerns
    ].filter(Boolean),
    improvements: [
      ...accuracyCheck.improvements,
      ...clarityCheck.improvements,
      ...completenessCheck.improvements,
      ...safetyCheck.improvements
    ].filter(Boolean),
    reasoning: `DUCKY Quality Analysis: Accuracy=${accuracyCheck.confidence.toFixed(2)}, Clarity=${clarityCheck.confidence.toFixed(2)}, Completeness=${completenessCheck.confidence.toFixed(2)}, Safety=${safetyCheck.confidence.toFixed(2)}`
  };
}

function validateAccuracy(response: string, userMessage: string, codeContext?: string, language?: string) {
  const concerns: string[] = [];
  const improvements: string[] = [];
  let confidence = 0.9;
  let isAccurate = true;
  
  // Check for common inaccuracies
  const inaccuratePatterns = [
    /var\s+\w+\s*=.*let\s+\w+/i, // Mixing var and let incorrectly
    /function\s+\w+\(\)\s*=>/i, // Incorrect arrow function syntax
    /console\.log\(\)\s*{/i, // Missing semicolon before block
    /\barray\.length\(\)/i, // Calling length as function
    /\bstring\.length\(\)/i, // Calling length as function
  ];
  
  inaccuratePatterns.forEach(pattern => {
    if (pattern.test(response)) {
      concerns.push('Response contains potential syntax errors');
      improvements.push('Verify code syntax and best practices');
      confidence -= 0.3;
      isAccurate = false;
    }
  });
  
  // Check language-specific accuracy
  if (language === 'javascript') {
    if (response.includes('print(') && !response.includes('console.log')) {
      concerns.push('Using Python syntax in JavaScript context');
      improvements.push('Use console.log() for JavaScript output');
      confidence -= 0.4;
      isAccurate = false;
    }
  }
  
  if (language === 'python') {
    if (response.includes('console.log') && !response.includes('print(')) {
      concerns.push('Using JavaScript syntax in Python context');
      improvements.push('Use print() for Python output');
      confidence -= 0.4;
      isAccurate = false;
    }
  }
  
  // Check for outdated information
  const outdatedPatterns = [
    /var\s+/i, // Prefer let/const in modern JS
    /function\s+\w+\(\)\s*{.*return/i, // Could suggest arrow functions
  ];
  
  if (language === 'javascript') {
    outdatedPatterns.forEach(pattern => {
      if (pattern.test(response)) {
        improvements.push('Consider using modern JavaScript syntax (let/const, arrow functions)');
        confidence -= 0.1;
      }
    });
  }
  
  return {
    isAccurate,
    confidence: Math.max(0.1, confidence),
    concerns,
    improvements,
    reasoning: 'Validated syntax accuracy, language-specific conventions, and modern best practices'
  };
}

function validateClarity(response: string) {
  const concerns: string[] = [];
  const improvements: string[] = [];
  let confidence = 0.8;
  let isClear = true;
  
  // Check response length
  if (response.length < 50) {
    concerns.push('Response may be too brief to be helpful');
    improvements.push('Provide more detailed explanation');
    confidence -= 0.2;
    isClear = false;
  }
  
  if (response.length > 2000) {
    concerns.push('Response may be too verbose');
    improvements.push('Condense to key points for better clarity');
    confidence -= 0.1;
  }
  
  // Check for clear structure
  const hasStructure = response.includes('\n') || 
                      response.includes('1.') || 
                      response.includes('•') || 
                      response.includes('-');
  
  if (!hasStructure && response.length > 200) {
    improvements.push('Use bullet points or numbered lists for better structure');
    confidence -= 0.1;
  }
  
  // Check for jargon without explanation
  const technicalTerms = ['API', 'DOM', 'callback', 'promise', 'async', 'closure', 'prototype'];
  const unexplainedJargon = technicalTerms.filter(term => 
    response.toLowerCase().includes(term.toLowerCase()) && 
    !response.includes(`${term} is`) && 
    !response.includes(`${term} means`)
  );
  
  if (unexplainedJargon.length > 2) {
    improvements.push('Define technical terms for better understanding');
    confidence -= 0.15;
  }
  
  return {
    isClear,
    confidence: Math.max(0.1, confidence),
    concerns,
    improvements,
    reasoning: 'Evaluated response clarity, structure, and technical term usage'
  };
}

function validateCompleteness(response: string, userMessage: string) {
  const concerns: string[] = [];
  const improvements: string[] = [];
  let confidence = 0.8;
  let isComplete = true;
  
  // Check if response addresses the question
  const questionWords = ['what', 'how', 'why', 'when', 'where', 'which'];
  const hasQuestion = questionWords.some(word => 
    userMessage.toLowerCase().includes(word)
  );
  
  if (hasQuestion) {
    const questionWord = questionWords.find(word => 
      userMessage.toLowerCase().includes(word)
    );
    
    if (questionWord === 'how' && !response.includes('step') && !response.includes('process')) {
      concerns.push('How-to question not fully addressed with steps');
      improvements.push('Provide step-by-step instructions');
      confidence -= 0.2;
      isComplete = false;
    }
    
    if (questionWord === 'why' && !response.includes('because') && !response.includes('reason')) {
      concerns.push('Why question not fully explained');
      improvements.push('Provide reasoning and explanation');
      confidence -= 0.2;
      isComplete = false;
    }
  }
  
  // Check for code examples when appropriate
  if (userMessage.toLowerCase().includes('example') || userMessage.toLowerCase().includes('show me')) {
    if (!response.includes('```') && !response.includes('`')) {
      concerns.push('Code example requested but not provided');
      improvements.push('Include relevant code examples');
      confidence -= 0.3;
      isComplete = false;
    }
  }
  
  return {
    isComplete,
    confidence: Math.max(0.1, confidence),
    concerns,
    improvements,
    reasoning: 'Checked if response fully addresses the user question and provides requested information'
  };
}

function validateSafety(response: string) {
  const concerns: string[] = [];
  const improvements: string[] = [];
  let confidence = 0.9;
  let isSafe = true;
  
  // Check for potentially harmful code patterns
  const unsafePatterns = [
    /eval\(/i,
    /innerHTML\s*=/i,
    /document\.write/i,
    /setTimeout\s*\(\s*["'].*["']/i, // String in setTimeout
    /setInterval\s*\(\s*["'].*["']/i, // String in setInterval
  ];
  
  unsafePatterns.forEach(pattern => {
    if (pattern.test(response)) {
      concerns.push('Response contains potentially unsafe code patterns');
      improvements.push('Suggest safer alternatives and explain security implications');
      confidence -= 0.4;
      isSafe = false;
    }
  });
  
  // Check for encouraging bad practices
  const badPractices = [
    /var\s+password/i,
    /hardcode/i,
    /disable.*security/i,
    /ignore.*error/i,
  ];
  
  badPractices.forEach(pattern => {
    if (pattern.test(response)) {
      concerns.push('Response may encourage poor security practices');
      improvements.push('Emphasize security best practices');
      confidence -= 0.2;
      isSafe = false;
    }
  });
  
  return {
    isSafe,
    confidence: Math.max(0.1, confidence),
    concerns,
    improvements,
    reasoning: 'Validated response for security concerns and safe coding practices'
  };
}
