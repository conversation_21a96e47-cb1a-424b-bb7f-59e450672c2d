import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

// In-memory storage for agent status (replace with database in production)
let currentAgentStatus = [
  {
    id: 'vyba',
    name: 'VY<PERSON>',
    role: 'Business Analyst',
    status: 'idle',
    tasks: 127,
    conversations: 0,
    quality: 95,
  },
  {
    id: 'qubert',
    name: 'QUBERT',
    role: 'Product Manager',
    status: 'idle',
    tasks: 131,
    conversations: 0,
    quality: 92,
  },
  {
    id: 'codex',
    name: 'CODEX',
    role: 'Architect',
    status: 'idle',
    tasks: 123,
    conversations: 0,
    quality: 98,
  },
  {
    id: 'pixy',
    name: '<PERSON>IX<PERSON>',
    role: 'Designer',
    status: 'idle',
    tasks: 118,
    conversations: 0,
    quality: 94,
  },
  {
    id: 'ducky',
    name: 'DUCK<PERSON>',
    role: 'Quality Guardian',
    status: 'idle',
    tasks: 117,
    conversations: 0,
    quality: 97,
  },
  {
    id: 'happy',
    name: 'HAPPY',
    role: 'Harmony Coordinator',
    status: 'idle',
    tasks: 128,
    conversations: 0,
    quality: 93,
  },
  {
    id: 'vybro',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    role: 'Developer',
    status: 'idle',
    tasks: 141,
    conversations: 0,
    quality: 96,
  },
];

export const GET: RequestHandler = async () => {
  try {
    return json(currentAgentStatus);
  } catch (error) {
    console.error('Error fetching agent status:', error);
    return json({ error: 'Failed to fetch agent status' }, { status: 500 });
  }
};

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { agents } = await request.json();

    if (!agents || !Array.isArray(agents)) {
      return json({ error: 'Invalid agent data' }, { status: 400 });
    }

    // Update the current agent status
    currentAgentStatus = agents.map(agent => ({
      ...agent,
      quality: agent.quality || 95, // Ensure quality is preserved
    }));

    console.log('Agent status updated:', currentAgentStatus.map(a => `${a.name}: ${a.status}`).join(', '));

    return json({ success: true, agents: currentAgentStatus });
  } catch (error) {
    console.error('Error updating agent status:', error);
    return json({ error: 'Failed to update agent status' }, { status: 500 });
  }
};
