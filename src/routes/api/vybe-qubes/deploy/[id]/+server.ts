/**
 * Vybe Qube Deployment Status API Endpoint
 * Handles individual deployment status and management
 * STORY-3-001: Vybe Qube Deployment Infrastructure
 */

import { json, error } from '@sveltejs/kit';
import type { RequestHandler } from '@sveltejs/kit';

const DEPLOYER_SERVICE_URL =
  process.env['VITE_DEPLOYER_ENDPOINT'] || 'http://localhost:8002';

/**
 * @swagger
 * /api/vybe-qubes/deploy/{deployment_id}:
 *   get:
 *     summary: Get deployment status
 *     description: Retrieve detailed status information for a specific deployment
 *     tags:
 *       - Vybe Qubes
 *       - Deployment
 *     parameters:
 *       - in: path
 *         name: deployment_id
 *         required: true
 *         schema:
 *           type: string
 *         description: Unique deployment identifier
 *         example: "deploy_1234567890_abc123"
 *     responses:
 *       200:
 *         description: Deployment status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 deployment_id:
 *                   type: string
 *                   description: Unique deployment identifier
 *                 qube_id:
 *                   type: string
 *                   description: Associated Vybe Qube ID
 *                 status:
 *                   type: string
 *                   enum: [initializing, deploying, deployed, failed]
 *                   description: Current deployment status
 *                 progress:
 *                   type: integer
 *                   minimum: 0
 *                   maximum: 100
 *                   description: Deployment progress percentage
 *                 current_phase:
 *                   type: string
 *                   description: Current deployment phase description
 *                 subdomain:
 *                   type: string
 *                   description: Assigned subdomain
 *                 url:
 *                   type: string
 *                   description: Live URL of the deployed site
 *                 logs:
 *                   type: array
 *                   items:
 *                     type: string
 *                   description: Deployment log entries
 *                 created_at:
 *                   type: string
 *                   format: date-time
 *                   description: Deployment creation timestamp
 *                 completed_at:
 *                   type: string
 *                   format: date-time
 *                   description: Deployment completion timestamp (if completed)
 *                 error:
 *                   type: string
 *                   description: Error message (if deployment failed)
 *       404:
 *         description: Deployment not found
 *       500:
 *         description: Service error
 */
export const GET: RequestHandler = async ({ params }) => {
  try {
    const { id: deploymentId } = params;

    if (!deploymentId) {
      throw error(400, 'Deployment ID is required');
    }

    const response = await fetch(
      `${DEPLOYER_SERVICE_URL}/status/${deploymentId}`
    );

    if (response.status === 404) {
      throw error(404, 'Deployment not found');
    }

    if (!response.ok) {
      throw error(response.status, 'Failed to fetch deployment status');
    }

    const deploymentStatus = await response.json();

    return json(deploymentStatus);
  } catch (err) {
    console.error('Deployment status API error:', err);

    if (err instanceof Error && 'status' in err) {
      throw err;
    }

    throw error(500, 'Internal server error while fetching deployment status');
  }
};

/**
 * @swagger
 * /api/vybe-qubes/deploy/{deployment_id}:
 *   delete:
 *     summary: Delete deployment
 *     description: Delete a deployment and clean up all associated resources
 *     tags:
 *       - Vybe Qubes
 *       - Deployment
 *     parameters:
 *       - in: path
 *         name: deployment_id
 *         required: true
 *         schema:
 *           type: string
 *         description: Unique deployment identifier
 *     responses:
 *       200:
 *         description: Deployment deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Success message
 *                 deployment_id:
 *                   type: string
 *                   description: Deleted deployment ID
 *       404:
 *         description: Deployment not found
 *       500:
 *         description: Service error
 */
export const DELETE: RequestHandler = async ({ params }) => {
  try {
    const { id: deploymentId } = params;

    if (!deploymentId) {
      throw error(400, 'Deployment ID is required');
    }

    const response = await fetch(
      `${DEPLOYER_SERVICE_URL}/deployments/${deploymentId}`,
      {
        method: 'DELETE',
      }
    );

    if (response.status === 404) {
      throw error(404, 'Deployment not found');
    }

    if (!response.ok) {
      const errorData = await response
        .json()
        .catch(() => ({ detail: 'Failed to delete deployment' }));
      throw error(
        response.status,
        errorData.detail || 'Failed to delete deployment'
      );
    }

    const result = await response.json();

    // Log deployment deletion
    console.log(`Deployment deleted: ${deploymentId}`);

    return json({
      message: 'Deployment deleted successfully',
      deployment_id: deploymentId,
      ...result,
    });
  } catch (err) {
    console.error('Deployment deletion API error:', err);

    if (err instanceof Error && 'status' in err) {
      throw err;
    }

    throw error(500, 'Internal server error while deleting deployment');
  }
};
