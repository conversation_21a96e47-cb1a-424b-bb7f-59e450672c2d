/**
 * Vybe Qube Deployment API Endpoint
 * Handles deployment requests and status tracking
 * STORY-3-001: Vybe Qube Deployment Infrastructure
 */

import { json, error } from '@sveltejs/kit';
import type { RequestHandler } from '@sveltejs/kit';

const DEPLOYER_SERVICE_URL =
  process.env['VITE_DEPLOYER_ENDPOINT'] || 'http://localhost:8002';

/**
 * @swagger
 * /api/vybe-qubes/deploy:
 *   post:
 *     summary: Deploy a Vybe Qube to live subdomain
 *     description: Initiates deployment of a generated Vybe Qube to a live subdomain with SSL
 *     tags:
 *       - Vybe Qubes
 *       - Deployment
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - qube_id
 *               - generated_files
 *               - business_idea
 *             properties:
 *               qube_id:
 *                 type: string
 *                 description: Unique identifier for the Vybe Qube
 *                 example: "qube_1234567890"
 *               generated_files:
 *                 type: object
 *                 description: Generated website files (path -> content mapping)
 *                 additionalProperties:
 *                   type: string
 *                 example:
 *                   "index.html": "<html>...</html>"
 *                   "style.css": "body { margin: 0; }"
 *               business_idea:
 *                 type: string
 *                 description: Business idea description for the website
 *                 example: "AI-powered fitness coaching platform"
 *               subdomain:
 *                 type: string
 *                 description: Optional custom subdomain (auto-generated if not provided)
 *                 pattern: "^[a-z0-9]([a-z0-9-]*[a-z0-9])?$"
 *                 example: "fitness-coach-ai"
 *               environment:
 *                 type: string
 *                 description: Deployment environment
 *                 enum: [production, staging]
 *                 default: production
 *     responses:
 *       200:
 *         description: Deployment initiated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 deployment_id:
 *                   type: string
 *                   description: Unique deployment identifier
 *                 qube_id:
 *                   type: string
 *                   description: Vybe Qube identifier
 *                 subdomain:
 *                   type: string
 *                   description: Assigned subdomain
 *                 status:
 *                   type: string
 *                   description: Initial deployment status
 *                 url:
 *                   type: string
 *                   description: Future live URL of the deployed site
 *                 estimated_completion:
 *                   type: string
 *                   format: date-time
 *                   description: Estimated completion time
 *       400:
 *         description: Invalid request parameters
 *       500:
 *         description: Deployment service error
 */
export const POST: RequestHandler = async ({ request }) => {
  try {
    const body = await request.json();

    // Validate required fields
    const {
      qube_id,
      generated_files,
      business_idea,
      subdomain,
      environment = 'production',
    } = body;

    if (!qube_id) {
      throw error(400, 'qube_id is required');
    }

    if (!generated_files || typeof generated_files !== 'object') {
      throw error(400, 'generated_files is required and must be an object');
    }

    if (!business_idea || business_idea.length < 10) {
      throw error(
        400,
        'business_idea is required and must be at least 10 characters'
      );
    }

    // Validate subdomain format if provided
    if (subdomain) {
      const subdomainRegex = /^[a-z0-9]([a-z0-9-]*[a-z0-9])?$/;
      if (!subdomainRegex.test(subdomain)) {
        throw error(
          400,
          'subdomain must contain only lowercase letters, numbers, and hyphens'
        );
      }

      if (subdomain.length < 3 || subdomain.length > 63) {
        throw error(400, 'subdomain must be between 3 and 63 characters');
      }
    }

    // Forward request to deployment service
    const deploymentRequest = {
      qube_id,
      generated_files,
      business_idea,
      subdomain,
      environment,
    };

    const response = await fetch(`${DEPLOYER_SERVICE_URL}/deploy`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(deploymentRequest),
    });

    if (!response.ok) {
      const errorData = await response
        .json()
        .catch(() => ({ detail: 'Deployment service error' }));
      throw error(
        response.status,
        errorData.detail || 'Failed to initiate deployment'
      );
    }

    const deploymentResponse = await response.json();

    // Log deployment initiation
    console.log(
      `Deployment initiated for qube ${qube_id}: ${deploymentResponse.deployment_id}`
    );

    return json(deploymentResponse);
  } catch (err) {
    console.error('Deployment API error:', err);

    if (err instanceof Error && 'status' in err) {
      throw err; // Re-throw SvelteKit errors
    }

    throw error(500, 'Internal server error during deployment');
  }
};

/**
 * @swagger
 * /api/vybe-qubes/deploy:
 *   get:
 *     summary: List all deployments
 *     description: Retrieve a list of all Vybe Qube deployments
 *     tags:
 *       - Vybe Qubes
 *       - Deployment
 *     parameters:
 *       - in: query
 *         name: qube_id
 *         schema:
 *           type: string
 *         description: Filter deployments by Vybe Qube ID
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [initializing, deploying, deployed, failed]
 *         description: Filter deployments by status
 *     responses:
 *       200:
 *         description: List of deployments
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 deployments:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       deployment_id:
 *                         type: string
 *                       qube_id:
 *                         type: string
 *                       status:
 *                         type: string
 *                       subdomain:
 *                         type: string
 *                       url:
 *                         type: string
 *                       created_at:
 *                         type: string
 *                         format: date-time
 *                 total:
 *                   type: integer
 *                   description: Total number of deployments
 *                 active:
 *                   type: integer
 *                   description: Number of active deployments
 *       500:
 *         description: Service error
 */
export const GET: RequestHandler = async ({ url }) => {
  try {
    const qubeId = url.searchParams.get('qube_id');
    const status = url.searchParams.get('status');

    // Build query parameters
    const params = new URLSearchParams();
    if (qubeId) params.append('qube_id', qubeId);
    if (status) params.append('status', status);

    const queryString = params.toString();
    const deploymentUrl = `${DEPLOYER_SERVICE_URL}/deployments${queryString ? `?${queryString}` : ''}`;

    const response = await fetch(deploymentUrl);

    if (!response.ok) {
      throw error(response.status, 'Failed to fetch deployments');
    }

    const deployments = await response.json();

    return json(deployments);
  } catch (err) {
    console.error('Deployment list API error:', err);

    if (err instanceof Error && 'status' in err) {
      throw err;
    }

    throw error(500, 'Internal server error while fetching deployments');
  }
};
