import { json } from '@sveltejs/kit';
import type { RequestH<PERSON><PERSON> } from './$types';
import { Client, Databases, ID } from 'appwrite';

const client = new Client()
  .setEndpoint('https://cloud.appwrite.io/v1')
  .setProject('vybecoding');

const databases = new Databases(client);

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { name, email, subject, message, timestamp } = await request.json();

    // Validate required fields
    if (!name || !email || !subject || !message) {
      return json(
        { error: 'All fields are required' },
        { status: 400 }
      );
    }

    // Enhanced email validation with domain checking
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Real domain validation - check if email domain exists
    try {
      const domain = email.split('@')[1];
      const domainValid = await checkEmailDomain(domain);
      if (!domainValid) {
        return json(
          { error: 'Email domain does not exist or is unreachable' },
          { status: 400 }
        );
      }
    } catch (domainError) {
      console.warn('Domain validation failed:', domainError);
      // Continue with submission but log the issue
    }

    // Real spam detection
    const spamScore = await detectSpam(message, email, name);
    if (spamScore > 0.8) {
      return json(
        { error: 'Message appears to be spam. Please revise and try again.' },
        { status: 400 }
      );
    }

    // Store contact message in Appwrite database
    const contactMessage = await databases.createDocument(
      '683b231d003c1c558e20', // Database ID
      'contact_messages', // Collection ID
      ID.unique(),
      {
        name,
        email,
        subject,
        message,
        timestamp: timestamp || new Date().toISOString(),
        status: 'new',
        created_at: new Date().toISOString()
      }
    );

    // Send notification email (implement with your preferred email service)
    // For now, log the message for admin notification
    console.log('New contact message received:', {
      id: contactMessage.$id,
      name,
      email,
      subject,
      timestamp
    });

    return json({
      success: true,
      message: 'Contact message sent successfully',
      id: contactMessage.$id,
      spam_score: spamScore
    });

  } catch (error) {
    console.error('Contact API error:', error);

    return json(
      {
        error: 'Failed to send message. Please try again later.',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
};

/**
 * Real email domain validation using DNS MX record lookup
 */
async function checkEmailDomain(domain: string): Promise<boolean> {
  try {
    // Use DNS over HTTPS to check MX records
    const response = await fetch(`https://dns.google/resolve?name=${domain}&type=MX`);
    const data = await response.json();

    // Check if domain has MX records (can receive email)
    return data.Status === 0 && data.Answer && data.Answer.length > 0;
  } catch (error) {
    console.warn('DNS lookup failed for domain:', domain, error);
    return true; // Allow submission if DNS check fails
  }
}

/**
 * Real spam detection using multiple heuristics
 */
async function detectSpam(message: string, email: string, name: string): Promise<number> {
  let spamScore = 0;

  // Check for spam keywords
  const spamKeywords = [
    'viagra', 'casino', 'lottery', 'winner', 'congratulations',
    'click here', 'free money', 'make money fast', 'work from home',
    'urgent', 'act now', 'limited time', 'guaranteed', 'risk free'
  ];

  const messageWords = message.toLowerCase().split(/\s+/);
  const spamWordCount = messageWords.filter(word =>
    spamKeywords.some(spam => word.includes(spam))
  ).length;

  spamScore += (spamWordCount / messageWords.length) * 0.5;

  // Check for excessive capitalization
  const capsCount = (message.match(/[A-Z]/g) || []).length;
  const capsRatio = capsCount / message.length;
  if (capsRatio > 0.3) spamScore += 0.2;

  // Check for suspicious email patterns
  if (email.includes('temp') || email.includes('disposable') || email.includes('10minute')) {
    spamScore += 0.3;
  }

  // Check for suspicious name patterns
  if (name.length < 2 || /^\d+$/.test(name) || name.includes('test')) {
    spamScore += 0.2;
  }

  // Check message length (too short or too long can be spam)
  if (message.length < 20) spamScore += 0.2;
  if (message.length > 2000) spamScore += 0.1;

  // Check for repeated characters or words
  const repeatedChars = message.match(/(.)\1{4,}/g);
  if (repeatedChars) spamScore += 0.2;

  return Math.min(spamScore, 1.0); // Cap at 1.0
}
