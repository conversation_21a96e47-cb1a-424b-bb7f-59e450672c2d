// Stripe webhook handler for VybeCoding.ai platform
import { json } from '@sveltejs/kit';
import <PERSON><PERSON> from 'stripe';
import {
  STRIPE_SECRET_KEY,
  STRIPE_WEBHOOK_SECRET,
  VITE_APPWRITE_DATABASE_ID,
} from '$env/static/private';
import { databases } from '$lib/server/appwrite.js';

const stripe = new Stripe(STRIPE_SECRET_KEY);

export async function POST({ request }) {
  const body = await request.text();
  const signature = request.headers.get('stripe-signature');

  let event;

  try {
    event = stripe.webhooks.constructEvent(
      body,
      signature,
      STRIPE_WEBHOOK_SECRET
    );
  } catch (err) {
    console.error('Webhook signature verification failed:', err.message);
    return json({ error: 'Invalid signature' }, { status: 400 });
  }

  try {
    switch (event.type) {
      case 'checkout.session.completed':
        await handleCheckoutCompleted(event.data.object);
        break;

      case 'customer.subscription.created':
        await handleSubscriptionCreated(event.data.object);
        break;

      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object);
        break;

      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object);
        break;

      case 'invoice.payment_succeeded':
        await handlePaymentSucceeded(event.data.object);
        break;

      case 'invoice.payment_failed':
        await handlePaymentFailed(event.data.object);
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return json({ received: true });
  } catch (error) {
    console.error('Error processing webhook:', error);
    return json({ error: 'Webhook processing failed' }, { status: 500 });
  }
}

async function handleCheckoutCompleted(session) {
  const userId = session.metadata?.userId;
  const courseId = session.metadata?.courseId;

  if (!userId) {
    console.error('No user ID in checkout session metadata');
    return;
  }

  try {
    if (session.mode === 'subscription') {
      // Handle subscription purchase
      await updateUserSubscription(userId, {
        stripeCustomerId: session.customer,
        subscriptionId: session.subscription,
        status: 'active',
        tier: 'pro', // Determine tier based on price
      });
    } else if (session.mode === 'payment' && courseId) {
      // Handle course purchase
      await grantCourseAccess(userId, courseId, {
        paymentIntentId: session.payment_intent,
        amount: session.amount_total,
        currency: session.currency,
      });
    }
  } catch (error) {
    console.error('Error handling checkout completion:', error);
  }
}

async function handleSubscriptionCreated(subscription) {
  const userId = subscription.metadata?.userId;

  if (!userId) {
    console.error('No user ID in subscription metadata');
    return;
  }

  try {
    await updateUserSubscription(userId, {
      subscriptionId: subscription.id,
      status: subscription.status,
      currentPeriodStart: new Date(subscription.current_period_start * 1000),
      currentPeriodEnd: new Date(subscription.current_period_end * 1000),
      tier: determineTierFromPrice(subscription.items.data[0]?.price?.id),
    });
  } catch (error) {
    console.error('Error handling subscription creation:', error);
  }
}

async function handleSubscriptionUpdated(subscription) {
  const userId = subscription.metadata?.userId;

  if (!userId) {
    console.error('No user ID in subscription metadata');
    return;
  }

  try {
    await updateUserSubscription(userId, {
      status: subscription.status,
      currentPeriodStart: new Date(subscription.current_period_start * 1000),
      currentPeriodEnd: new Date(subscription.current_period_end * 1000),
      tier: determineTierFromPrice(subscription.items.data[0]?.price?.id),
    });
  } catch (error) {
    console.error('Error handling subscription update:', error);
  }
}

async function handleSubscriptionDeleted(subscription) {
  const userId = subscription.metadata?.userId;

  if (!userId) {
    console.error('No user ID in subscription metadata');
    return;
  }

  try {
    await updateUserSubscription(userId, {
      status: 'cancelled',
      tier: 'free',
      cancelledAt: new Date(),
    });
  } catch (error) {
    console.error('Error handling subscription deletion:', error);
  }
}

async function handlePaymentSucceeded(invoice) {
  // Handle successful payment
  console.log('Payment succeeded for invoice:', invoice.id);
}

async function handlePaymentFailed(invoice) {
  // Handle failed payment
  console.log('Payment failed for invoice:', invoice.id);
}

async function updateUserSubscription(userId, subscriptionData) {
  try {
    // Update user document with subscription information
    await databases.updateDocument(VITE_APPWRITE_DATABASE_ID, 'users', userId, {
      subscription: JSON.stringify(subscriptionData),
      subscriptionStatus: subscriptionData.status,
      subscriptionTier: subscriptionData.tier,
      updatedAt: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error updating user subscription:', error);
    throw error;
  }
}

async function grantCourseAccess(userId, courseId, paymentData) {
  try {
    // Create course access record
    await databases.createDocument(
      VITE_APPWRITE_DATABASE_ID,
      'course_purchases',
      'unique()',
      {
        userId,
        courseId,
        purchasedAt: new Date().toISOString(),
        paymentData: JSON.stringify(paymentData),
        status: 'active',
      }
    );
  } catch (error) {
    console.error('Error granting course access:', error);
    throw error;
  }
}

function determineTierFromPrice(priceId) {
  // Map Stripe price IDs to subscription tiers
  const priceToTierMap = {
    price_pro_monthly: 'pro',
    price_enterprise_monthly: 'enterprise',
  };

  return priceToTierMap[priceId] || 'free';
}
