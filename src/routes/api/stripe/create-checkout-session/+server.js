// Stripe checkout session creation endpoint
import { json } from '@sveltejs/kit';
import <PERSON><PERSON> from 'stripe';
import { STRIPE_SECRET_KEY } from '$env/static/private';
import { checkoutConfig } from '$lib/config/stripe.js';

const stripe = new Stripe(STRIPE_SECRET_KEY);

export async function POST({ request }) {
  try {
    const {
      priceId,
      courseId,
      price,
      userId,
      mode = 'subscription',
    } = await request.json();

    if (!userId) {
      return json({ error: 'User ID is required' }, { status: 400 });
    }

    let sessionConfig = {
      customer_creation: 'always',
      success_url: checkoutConfig.successUrl,
      cancel_url: checkoutConfig.cancelUrl,
      allow_promotion_codes: true,
      billing_address_collection: 'auto',
      metadata: {
        userId,
        ...(courseId && { courseId }),
      },
    };

    if (mode === 'subscription') {
      if (!priceId) {
        return json(
          { error: 'Price ID is required for subscription' },
          { status: 400 }
        );
      }

      sessionConfig = {
        ...sessionConfig,
        mode: 'subscription',
        line_items: [
          {
            price: priceId,
            quantity: 1,
          },
        ],
        subscription_data: {
          trial_period_days: 7,
          metadata: {
            userId,
          },
        },
      };
    } else if (mode === 'payment') {
      if (!courseId || !price) {
        return json(
          { error: 'Course ID and price are required for one-time payment' },
          { status: 400 }
        );
      }

      sessionConfig = {
        ...sessionConfig,
        mode: 'payment',
        line_items: [
          {
            price_data: {
              currency: 'usd',
              product_data: {
                name: `VybeCoding Course Access`,
                description: `Access to course ${courseId}`,
                metadata: {
                  courseId,
                  type: 'course_purchase',
                },
              },
              unit_amount: price * 100, // Convert to cents
            },
            quantity: 1,
          },
        ],
        payment_intent_data: {
          metadata: {
            userId,
            courseId,
            type: 'course_purchase',
          },
        },
      };
    } else {
      return json(
        { error: 'Invalid mode. Must be "subscription" or "payment"' },
        { status: 400 }
      );
    }

    const session = await stripe.checkout.sessions.create(sessionConfig);

    return json({
      id: session.id,
      url: session.url,
    });
  } catch (error) {
    console.error('Error creating checkout session:', error);
    return json(
      {
        error: 'Failed to create checkout session',
        details: error.message,
      },
      { status: 500 }
    );
  }
}
