import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { Client, Databases, ID } from 'appwrite';

const client = new Client()
  .setEndpoint('https://cloud.appwrite.io/v1')
  .setProject('vybecoding');

const databases = new Databases(client);

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { courseId, lessonId, sectionId, userId, completedAt } = await request.json();

    // Validate required fields
    if (!courseId || !lessonId || !sectionId || !userId || !completedAt) {
      return json(
        { error: 'Course ID, lesson ID, section ID, user ID, and completion time are required' },
        { status: 400 }
      );
    }

    // Create section completion record
    const sectionCompletion = await databases.createDocument(
      '683b231d003c1c558e20', // Database ID
      'section_completions', // Collection ID
      ID.unique(),
      {
        userId,
        courseId,
        lessonId,
        sectionId,
        completedAt,
        pointsEarned: 25, // Points for completing a section
        created_at: new Date().toISOString()
      }
    );

    // Update user's section completion stats
    await updateUserSectionStats(userId, 25);

    return json({
      success: true,
      message: 'Section completed successfully',
      sectionId,
      pointsEarned: 25,
      completionId: sectionCompletion.$id
    });

  } catch (error) {
    console.error('Section completion API error:', error);
    
    return json(
      { 
        error: 'Failed to complete section. Please try again later.',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
};

async function updateUserSectionStats(userId: string, pointsToAdd: number): Promise<void> {
  try {
    // This would update user statistics for section completions
    // Implementation similar to lesson completion stats
    console.log(`Updated user ${userId} stats with ${pointsToAdd} points for section completion`);
  } catch (error) {
    console.error('Error updating user section stats:', error);
  }
}
