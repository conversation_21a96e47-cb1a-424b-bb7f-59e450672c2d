import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { Client, Databases, Query, ID } from 'appwrite';

const client = new Client()
  .setEndpoint('https://cloud.appwrite.io/v1')
  .setProject('vybecoding');

const databases = new Databases(client);

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { courseId, lessonId, userId, completedAt, finalProgress, totalReadingTime } = await request.json();

    // Validate required fields
    if (!courseId || !lessonId || !userId || !completedAt) {
      return json(
        { error: 'Course ID, lesson ID, user ID, and completion time are required' },
        { status: 400 }
      );
    }

    // Mark lesson as completed
    const lessonCompletion = await databases.createDocument(
      '683b231d003c1c558e20', // Database ID
      'lesson_completions', // Collection ID
      ID.unique(),
      {
        userId,
        courseId,
        lessonId,
        completedAt,
        finalProgress: finalProgress || 100,
        totalReadingTime: totalReadingTime || 0,
        pointsEarned: calculatePointsEarned(finalProgress, totalReadingTime),
        created_at: new Date().toISOString()
      }
    );

    // Update reading session status
    const sessions = await databases.listDocuments(
      '683b231d003c1c558e20',
      'reading_sessions',
      [
        Query.equal('userId', userId),
        Query.equal('courseId', courseId),
        Query.equal('lessonId', lessonId),
        Query.equal('status', 'active'),
        Query.limit(1)
      ]
    );

    if (sessions.documents.length > 0) {
      await databases.updateDocument(
        '683b231d003c1c558e20',
        'reading_sessions',
        sessions.documents[0].$id,
        {
          status: 'completed',
          completedAt,
          finalProgress: finalProgress || 100
        }
      );
    }

    // Check if this completes the entire course
    const courseCompleted = await checkCourseCompletion(userId, courseId);
    
    // Calculate points earned
    const pointsEarned = calculatePointsEarned(finalProgress, totalReadingTime);

    // Update user's total points
    await updateUserPoints(userId, pointsEarned);

    return json({
      success: true,
      message: 'Lesson completed successfully',
      lessonId,
      pointsEarned,
      courseCompleted,
      completionId: lessonCompletion.$id
    });

  } catch (error) {
    console.error('Lesson completion API error:', error);
    
    return json(
      { 
        error: 'Failed to complete lesson. Please try again later.',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
};

function calculatePointsEarned(progress: number, readingTime: number): number {
  // Base points for completion
  let points = 100;
  
  // Bonus for full completion
  if (progress >= 100) {
    points += 50;
  }
  
  // Bonus for thorough reading (spending adequate time)
  if (readingTime >= 300) { // 5 minutes minimum
    points += 25;
  }
  
  return points;
}

async function checkCourseCompletion(userId: string, courseId: string): Promise<boolean> {
  try {
    // Get all lessons in the course (this would normally come from course data)
    const expectedLessons = ['lesson-1', 'lesson-2', 'lesson-3']; // In real app, fetch from course data
    
    // Check completed lessons
    const completedLessons = await databases.listDocuments(
      '683b231d003c1c558e20',
      'lesson_completions',
      [
        Query.equal('userId', userId),
        Query.equal('courseId', courseId)
      ]
    );

    const completedLessonIds = completedLessons.documents.map(doc => doc.lessonId);
    
    // Check if all lessons are completed
    return expectedLessons.every(lessonId => completedLessonIds.includes(lessonId));
  } catch (error) {
    console.error('Error checking course completion:', error);
    return false;
  }
}

async function updateUserPoints(userId: string, pointsToAdd: number): Promise<void> {
  try {
    // Get user's current points
    const userStats = await databases.listDocuments(
      '683b231d003c1c558e20',
      'user_stats',
      [
        Query.equal('userId', userId),
        Query.limit(1)
      ]
    );

    if (userStats.documents.length > 0) {
      // Update existing stats
      const currentPoints = userStats.documents[0].totalPoints || 0;
      await databases.updateDocument(
        '683b231d003c1c558e20',
        'user_stats',
        userStats.documents[0].$id,
        {
          totalPoints: currentPoints + pointsToAdd,
          lastPointsEarned: pointsToAdd,
          lastActivity: new Date().toISOString()
        }
      );
    } else {
      // Create new stats record
      await databases.createDocument(
        '683b231d003c1c558e20',
        'user_stats',
        ID.unique(),
        {
          userId,
          totalPoints: pointsToAdd,
          lastPointsEarned: pointsToAdd,
          coursesCompleted: 0,
          lessonsCompleted: 1,
          created_at: new Date().toISOString(),
          lastActivity: new Date().toISOString()
        }
      );
    }
  } catch (error) {
    console.error('Error updating user points:', error);
  }
}
