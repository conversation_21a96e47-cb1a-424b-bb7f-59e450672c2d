import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { Client, Databases, Query } from 'appwrite';

const client = new Client()
  .setEndpoint('https://cloud.appwrite.io/v1')
  .setProject('vybecoding');

const databases = new Databases(client);

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { courseId, lessonId, userId } = await request.json();

    // Validate required fields
    if (!courseId || !lessonId || !userId) {
      return json(
        { error: 'Course ID, lesson ID, and user ID are required' },
        { status: 400 }
      );
    }

    // Reset reading sessions for this lesson
    const sessions = await databases.listDocuments(
      '683b231d003c1c558e20', // Database ID
      'reading_sessions', // Collection ID
      [
        Query.equal('userId', userId),
        Query.equal('courseId', courseId),
        Query.equal('lessonId', lessonId)
      ]
    );

    // Update all sessions to reset status
    for (const session of sessions.documents) {
      await databases.updateDocument(
        '683b231d003c1c558e20',
        'reading_sessions',
        session.$id,
        {
          status: 'reset',
          progress: 0,
          readingTime: 0,
          completedSections: [],
          resetAt: new Date().toISOString()
        }
      );
    }

    // Reset user progress for this lesson
    const userProgress = await databases.listDocuments(
      '683b231d003c1c558e20',
      'user_course_progress',
      [
        Query.equal('userId', userId),
        Query.equal('courseId', courseId),
        Query.equal('lessonId', lessonId)
      ]
    );

    for (const progress of userProgress.documents) {
      await databases.updateDocument(
        '683b231d003c1c558e20',
        'user_course_progress',
        progress.$id,
        {
          progress: 0,
          readingTime: 0,
          completedSections: [],
          resetAt: new Date().toISOString()
        }
      );
    }

    return json({
      success: true,
      message: 'Progress reset successfully',
      lessonId,
      resetAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('Progress reset API error:', error);
    
    return json(
      { 
        error: 'Failed to reset progress. Please try again later.',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
};
