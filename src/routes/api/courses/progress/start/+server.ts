import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { Client, Databases, ID } from 'appwrite';

const client = new Client()
  .setEndpoint('https://cloud.appwrite.io/v1')
  .setProject('vybecoding');

const databases = new Databases(client);

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { courseId, lessonId, userId, startTime } = await request.json();

    // Validate required fields
    if (!courseId || !lessonId || !userId || !startTime) {
      return json(
        { error: 'Course ID, lesson ID, user ID, and start time are required' },
        { status: 400 }
      );
    }

    // Create or update reading session in Appwrite database
    const readingSession = await databases.createDocument(
      '683b231d003c1c558e20', // Database ID
      'reading_sessions', // Collection ID
      ID.unique(),
      {
        userId,
        courseId,
        lessonId,
        startTime,
        status: 'active',
        progress: 0,
        readingTime: 0,
        completedSections: [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    );

    // Log reading session start for analytics
    console.log('Reading session started:', {
      sessionId: readingSession.$id,
      userId,
      courseId,
      lessonId,
      startTime
    });

    return json({
      success: true,
      message: 'Reading session started successfully',
      sessionId: readingSession.$id,
      startTime
    });

  } catch (error) {
    console.error('Reading session start API error:', error);
    
    return json(
      { 
        error: 'Failed to start reading session. Please try again later.',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
};
