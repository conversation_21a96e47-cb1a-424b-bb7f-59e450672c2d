import { json } from '@sveltejs/kit';
import type { Request<PERSON>and<PERSON> } from './$types';
import { Client, Databases, Query, ID } from 'appwrite';

const client = new Client()
  .setEndpoint('https://cloud.appwrite.io/v1')
  .setProject('vybecoding');

const databases = new Databases(client);

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { courseId, lessonId, userId, progress, readingTime, completedSections, timestamp } = await request.json();

    // Validate required fields
    if (!courseId || !lessonId || !userId || progress === undefined || readingTime === undefined) {
      return json(
        { error: 'Course ID, lesson ID, user ID, progress, and reading time are required' },
        { status: 400 }
      );
    }

    // Find the most recent active reading session
    const sessions = await databases.listDocuments(
      '683b231d003c1c558e20', // Database ID
      'reading_sessions', // Collection ID
      [
        Query.equal('userId', userId),
        Query.equal('courseId', courseId),
        Query.equal('lessonId', lessonId),
        Query.equal('status', 'active'),
        Query.orderDesc('created_at'),
        Query.limit(1)
      ]
    );

    if (sessions.documents.length === 0) {
      return json(
        { error: 'No active reading session found' },
        { status: 404 }
      );
    }

    const session = sessions.documents[0];

    // Update the reading session with current progress
    const updatedSession = await databases.updateDocument(
      '683b231d003c1c558e20', // Database ID
      'reading_sessions', // Collection ID
      session.$id,
      {
        progress: Math.round(progress),
        readingTime,
        completedSections: completedSections || [],
        updated_at: timestamp || new Date().toISOString()
      }
    );

    // Create or update user progress record
    try {
      const userProgress = await databases.createDocument(
        '683b231d003c1c558e20', // Database ID
        'user_course_progress', // Collection ID
        ID.unique(),
        {
          userId,
          courseId,
          lessonId,
          progress: Math.round(progress),
          readingTime,
          completedSections: completedSections || [],
          lastAccessed: timestamp || new Date().toISOString(),
          created_at: new Date().toISOString()
        }
      );
    } catch (error) {
      // If document already exists, update it
      const existingProgress = await databases.listDocuments(
        '683b231d003c1c558e20',
        'user_course_progress',
        [
          Query.equal('userId', userId),
          Query.equal('courseId', courseId),
          Query.equal('lessonId', lessonId),
          Query.limit(1)
        ]
      );

      if (existingProgress.documents.length > 0) {
        await databases.updateDocument(
          '683b231d003c1c558e20',
          'user_course_progress',
          existingProgress.documents[0].$id,
          {
            progress: Math.round(progress),
            readingTime,
            completedSections: completedSections || [],
            lastAccessed: timestamp || new Date().toISOString()
          }
        );
      }
    }

    return json({
      success: true,
      message: 'Progress updated successfully',
      progress: Math.round(progress),
      readingTime,
      sessionId: session.$id
    });

  } catch (error) {
    console.error('Progress update API error:', error);
    
    return json(
      { 
        error: 'Failed to update progress. Please try again later.',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
};
