import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { Client, Databases, Query, ID } from 'appwrite';

const client = new Client()
  .setEndpoint('https://cloud.appwrite.io/v1')
  .setProject('vybecoding');

const databases = new Databases(client);

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { courseId, userId, completedAt } = await request.json();

    // Validate required fields
    if (!courseId || !userId || !completedAt) {
      return json(
        { error: 'Course ID, user ID, and completion time are required' },
        { status: 400 }
      );
    }

    // Verify all lessons are actually completed
    const lessonCompletions = await databases.listDocuments(
      '683b231d003c1c558e20', // Database ID
      'lesson_completions', // Collection ID
      [
        Query.equal('userId', userId),
        Query.equal('courseId', courseId)
      ]
    );

    // Calculate total points earned from all lessons
    const totalPoints = lessonCompletions.documents.reduce((sum, completion) => {
      return sum + (completion.pointsEarned || 0);
    }, 0);

    // Bonus points for course completion
    const completionBonus = 500;
    const finalTotalPoints = totalPoints + completionBonus;

    // Create course completion record
    const courseCompletion = await databases.createDocument(
      '683b231d003c1c558e20',
      'course_completions',
      ID.unique(),
      {
        userId,
        courseId,
        completedAt,
        totalLessonsCompleted: lessonCompletions.documents.length,
        totalPointsEarned: finalTotalPoints,
        completionBonus,
        certificateIssued: true,
        created_at: new Date().toISOString()
      }
    );

    // Update user stats
    await updateUserCourseStats(userId, finalTotalPoints);

    // Determine unlocked features based on course completion
    const unlockedFeatures = await getUnlockedFeatures(userId, courseId);

    // Generate certificate
    const certificateUrl = await generateCertificate(userId, courseId, completedAt);

    return json({
      success: true,
      message: 'Course completed successfully!',
      courseId,
      totalPoints: finalTotalPoints,
      completionBonus,
      unlockedFeatures,
      certificateUrl,
      redirectUrl: '/dashboard',
      completionId: courseCompletion.$id
    });

  } catch (error) {
    console.error('Course completion API error:', error);
    
    return json(
      { 
        error: 'Failed to complete course. Please try again later.',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
};

async function updateUserCourseStats(userId: string, pointsToAdd: number): Promise<void> {
  try {
    const userStats = await databases.listDocuments(
      '683b231d003c1c558e20',
      'user_stats',
      [
        Query.equal('userId', userId),
        Query.limit(1)
      ]
    );

    if (userStats.documents.length > 0) {
      const stats = userStats.documents[0];
      await databases.updateDocument(
        '683b231d003c1c558e20',
        'user_stats',
        stats.$id,
        {
          totalPoints: (stats.totalPoints || 0) + pointsToAdd,
          coursesCompleted: (stats.coursesCompleted || 0) + 1,
          lastActivity: new Date().toISOString()
        }
      );
    } else {
      await databases.createDocument(
        '683b231d003c1c558e20',
        'user_stats',
        ID.unique(),
        {
          userId,
          totalPoints: pointsToAdd,
          coursesCompleted: 1,
          lessonsCompleted: 0,
          created_at: new Date().toISOString(),
          lastActivity: new Date().toISOString()
        }
      );
    }
  } catch (error) {
    console.error('Error updating user course stats:', error);
  }
}

async function getUnlockedFeatures(userId: string, courseId: string): Promise<string[]> {
  const features: string[] = [];
  
  // Get user's completed courses
  const completedCourses = await databases.listDocuments(
    '683b231d003c1c558e20',
    'course_completions',
    [
      Query.equal('userId', userId)
    ]
  );

  const completedCourseIds = completedCourses.documents.map(doc => doc.courseId);

  // Define feature unlocks based on course completion
  if (completedCourseIds.includes('intro-to-ai-tools')) {
    features.push('MAS Observatory Access');
    features.push('Basic Code Generation');
  }

  if (completedCourseIds.includes('advanced-ai-development')) {
    features.push('Advanced MAS Features');
    features.push('Custom Agent Creation');
  }

  if (completedCourseIds.includes('website-building-mastery')) {
    features.push('Deployment Tools');
    features.push('Domain Management');
  }

  // Always unlock certificate download
  features.push('Course Certificate');

  return features;
}

async function generateCertificate(userId: string, courseId: string, completedAt: string): Promise<string> {
  // In a real implementation, this would generate a PDF certificate
  // For now, return a placeholder URL
  const certificateId = `cert_${userId}_${courseId}_${Date.now()}`;
  
  // Store certificate record
  try {
    await databases.createDocument(
      '683b231d003c1c558e20',
      'certificates',
      ID.unique(),
      {
        userId,
        courseId,
        certificateId,
        issuedAt: completedAt,
        downloadUrl: `/api/certificates/${certificateId}/download`,
        created_at: new Date().toISOString()
      }
    );
  } catch (error) {
    console.error('Error storing certificate record:', error);
  }

  return `/api/certificates/${certificateId}/download`;
}
