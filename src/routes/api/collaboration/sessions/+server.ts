/**
 * Collaboration Sessions API Endpoint
 * STORY-4-003: Real-time Collaboration & Mentorship
 */

import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import type {
  CollaborationSession,
  SessionParticipant,
} from '$lib/services/collaborationService';
import { nanoid } from 'nanoid';

// Mock session storage - replace with real database
const sessions = new Map<string, CollaborationSession>();
const sessionInvites = new Map<string, SessionInvite>();

interface SessionInvite {
  id: string;
  sessionId: string;
  email: string;
  role: 'collaborator' | 'viewer';
  invitedBy: string;
  createdAt: Date;
  expiresAt: Date;
  status: 'pending' | 'accepted' | 'declined' | 'expired';
}

// Initialize with sample sessions
initializeSampleSessions();

function initializeSampleSessions() {
  const sampleSession: CollaborationSession = {
    id: 'demo-session-1',
    name: 'JavaScript Learning Session',
    description: 'Learning async/await and promises together',
    ownerId: 'demo-user-1',
    ownerName: 'Demo User',
    participants: [
      {
        id: 'demo-user-1',
        name: 'Demo User',
        role: 'owner',
        status: 'online',
        lastActivity: new Date(),
        permissions: {
          canEdit: true,
          canGenerate: true,
          canInvite: true,
          canManageSettings: true,
          canViewAnalytics: true,
        },
      },
    ],
    status: 'active',
    createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    updatedAt: new Date(),
    settings: {
      isPublic: true,
      allowAnonymous: true,
      maxParticipants: 10,
      requireApproval: false,
      enableChat: true,
      enableVoice: false,
      recordSession: false,
      autoSave: true,
    },
  };

  sessions.set(sampleSession.id, sampleSession);
}

export const GET: RequestHandler = async ({ url }) => {
  const action = url.searchParams.get('action');
  const sessionId = url.searchParams.get('sessionId');
  const userId = url.searchParams.get('userId');

  try {
    switch (action) {
      case 'list':
        // List sessions for a user
        if (!userId) {
          return json({ error: 'User ID is required' }, { status: 400 });
        }

        const userSessions = Array.from(sessions.values()).filter(
          session =>
            session.ownerId === userId ||
            session.participants.some(p => p.id === userId) ||
            (session.settings.isPublic && session.status === 'active')
        );

        return json({
          success: true,
          sessions: userSessions,
        });

      case 'get':
        if (!sessionId) {
          return json({ error: 'Session ID is required' }, { status: 400 });
        }

        const session = sessions.get(sessionId);
        if (!session) {
          return json({ error: 'Session not found' }, { status: 404 });
        }

        return json({
          success: true,
          session,
        });

      case 'public':
        // List public sessions
        const publicSessions = Array.from(sessions.values()).filter(
          session => session.settings.isPublic && session.status === 'active'
        );

        return json({
          success: true,
          sessions: publicSessions,
        });

      case 'invites':
        if (!userId) {
          return json({ error: 'User ID is required' }, { status: 400 });
        }

        // Get pending invites for user
        const userInvites = Array.from(sessionInvites.values()).filter(
          invite => invite.email === userId && invite.status === 'pending'
        );

        return json({
          success: true,
          invites: userInvites,
        });

      default:
        return json({
          service: 'Collaboration Sessions API',
          version: '1.0.0',
          endpoints: {
            'GET ?action=list&userId=<id>': 'List user sessions',
            'GET ?action=get&sessionId=<id>': 'Get specific session',
            'GET ?action=public': 'List public sessions',
            'GET ?action=invites&userId=<id>': 'Get user invites',
            POST: 'Create new session',
            PUT: 'Update session',
            'DELETE ?sessionId=<id>': 'Delete session',
          },
        });
    }
  } catch (error) {
    console.error('Sessions API error:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { action, ...data } = await request.json();

    switch (action) {
      case 'create':
        const { name, description, ownerId, ownerName, settings } = data;

        if (!name || !ownerId || !ownerName) {
          return json(
            { error: 'Name, owner ID, and owner name are required' },
            { status: 400 }
          );
        }

        const sessionId = nanoid();
        const defaultSettings = {
          isPublic: false,
          allowAnonymous: false,
          maxParticipants: 10,
          requireApproval: false,
          enableChat: true,
          enableVoice: false,
          recordSession: true,
          autoSave: true,
          ...settings,
        };

        const newSession: CollaborationSession = {
          id: sessionId,
          name,
          description: description || '',
          ownerId,
          ownerName,
          participants: [
            {
              id: ownerId,
              name: ownerName,
              role: 'owner',
              status: 'online',
              lastActivity: new Date(),
              permissions: {
                canEdit: true,
                canGenerate: true,
                canInvite: true,
                canManageSettings: true,
                canViewAnalytics: true,
              },
            },
          ],
          status: 'active',
          createdAt: new Date(),
          updatedAt: new Date(),
          settings: defaultSettings,
        };

        sessions.set(sessionId, newSession);

        return json({
          success: true,
          session: newSession,
          message: 'Session created successfully',
        });

      case 'join':
        const { sessionId: joinSessionId, userId, userName } = data;

        if (!joinSessionId || !userId || !userName) {
          return json(
            { error: 'Session ID, user ID, and user name are required' },
            { status: 400 }
          );
        }

        const sessionToJoin = sessions.get(joinSessionId);
        if (!sessionToJoin) {
          return json({ error: 'Session not found' }, { status: 404 });
        }

        // Check if user is already in session
        const existingParticipant = sessionToJoin.participants.find(
          p => p.id === userId
        );
        if (existingParticipant) {
          return json({
            success: true,
            session: sessionToJoin,
            message: 'Already in session',
          });
        }

        // Check session capacity
        if (
          sessionToJoin.participants.length >=
          sessionToJoin.settings.maxParticipants
        ) {
          return json({ error: 'Session is full' }, { status: 400 });
        }

        // Check if anonymous users are allowed
        if (!sessionToJoin.settings.allowAnonymous && !userName) {
          return json(
            { error: 'Anonymous users not allowed' },
            { status: 403 }
          );
        }

        // Add participant
        const newParticipant: SessionParticipant = {
          id: userId,
          name: userName,
          role: 'collaborator',
          status: 'online',
          lastActivity: new Date(),
          permissions: {
            canEdit: true,
            canGenerate: false,
            canInvite: false,
            canManageSettings: false,
            canViewAnalytics: false,
          },
        };

        sessionToJoin.participants.push(newParticipant);
        sessionToJoin.updatedAt = new Date();

        sessions.set(joinSessionId, sessionToJoin);

        return json({
          success: true,
          session: sessionToJoin,
          message: 'Joined session successfully',
        });

      case 'leave':
        const { sessionId: leaveSessionId, userId: leaveUserId } = data;

        if (!leaveSessionId || !leaveUserId) {
          return json(
            { error: 'Session ID and user ID are required' },
            { status: 400 }
          );
        }

        const sessionToLeave = sessions.get(leaveSessionId);
        if (!sessionToLeave) {
          return json({ error: 'Session not found' }, { status: 404 });
        }

        // Remove participant
        sessionToLeave.participants = sessionToLeave.participants.filter(
          p => p.id !== leaveUserId
        );
        sessionToLeave.updatedAt = new Date();

        // If owner leaves and there are other participants, transfer ownership
        if (
          sessionToLeave.ownerId === leaveUserId &&
          sessionToLeave.participants.length > 0
        ) {
          const newOwner = sessionToLeave.participants[0];
          sessionToLeave.ownerId = newOwner.id;
          sessionToLeave.ownerName = newOwner.name;
          newOwner.role = 'owner';
          newOwner.permissions = {
            canEdit: true,
            canGenerate: true,
            canInvite: true,
            canManageSettings: true,
            canViewAnalytics: true,
          };
        }

        // If no participants left, archive session
        if (sessionToLeave.participants.length === 0) {
          sessionToLeave.status = 'archived';
        }

        sessions.set(leaveSessionId, sessionToLeave);

        return json({
          success: true,
          message: 'Left session successfully',
        });

      case 'invite':
        const { sessionId: inviteSessionId, email, role, invitedBy } = data;

        if (!inviteSessionId || !email || !invitedBy) {
          return json(
            { error: 'Session ID, email, and inviter ID are required' },
            { status: 400 }
          );
        }

        const sessionToInvite = sessions.get(inviteSessionId);
        if (!sessionToInvite) {
          return json({ error: 'Session not found' }, { status: 404 });
        }

        // Check if inviter has permission
        const inviter = sessionToInvite.participants.find(
          p => p.id === invitedBy
        );
        if (!inviter || !inviter.permissions.canInvite) {
          return json({ error: 'No permission to invite' }, { status: 403 });
        }

        const inviteId = nanoid();
        const invite: SessionInvite = {
          id: inviteId,
          sessionId: inviteSessionId,
          email,
          role: role || 'collaborator',
          invitedBy,
          createdAt: new Date(),
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
          status: 'pending',
        };

        sessionInvites.set(inviteId, invite);

        return json({
          success: true,
          invite,
          message: 'Invitation sent successfully',
        });

      default:
        return json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Sessions API error:', error);
    return json({ error: 'Failed to process request' }, { status: 500 });
  }
};

export const PUT: RequestHandler = async ({ request }) => {
  try {
    const { sessionId, userId, updates } = await request.json();

    if (!sessionId || !userId) {
      return json(
        { error: 'Session ID and user ID are required' },
        { status: 400 }
      );
    }

    const session = sessions.get(sessionId);
    if (!session) {
      return json({ error: 'Session not found' }, { status: 404 });
    }

    // Check if user has permission to update
    const user = session.participants.find(p => p.id === userId);
    if (
      !user ||
      (!user.permissions.canManageSettings && session.ownerId !== userId)
    ) {
      return json(
        { error: 'No permission to update session' },
        { status: 403 }
      );
    }

    // Update session
    const updatedSession = {
      ...session,
      ...updates,
      updatedAt: new Date(),
    };

    sessions.set(sessionId, updatedSession);

    return json({
      success: true,
      session: updatedSession,
      message: 'Session updated successfully',
    });
  } catch (error) {
    console.error('Update session error:', error);
    return json({ error: 'Failed to update session' }, { status: 500 });
  }
};

export const DELETE: RequestHandler = async ({ url }) => {
  try {
    const sessionId = url.searchParams.get('sessionId');
    const userId = url.searchParams.get('userId');

    if (!sessionId || !userId) {
      return json(
        { error: 'Session ID and user ID are required' },
        { status: 400 }
      );
    }

    const session = sessions.get(sessionId);
    if (!session) {
      return json({ error: 'Session not found' }, { status: 404 });
    }

    // Only owner can delete session
    if (session.ownerId !== userId) {
      return json({ error: 'Only session owner can delete' }, { status: 403 });
    }

    sessions.delete(sessionId);

    // Clean up related invites
    for (const [inviteId, invite] of sessionInvites.entries()) {
      if (invite.sessionId === sessionId) {
        sessionInvites.delete(inviteId);
      }
    }

    return json({
      success: true,
      message: 'Session deleted successfully',
    });
  } catch (error) {
    console.error('Delete session error:', error);
    return json({ error: 'Failed to delete session' }, { status: 500 });
  }
};
