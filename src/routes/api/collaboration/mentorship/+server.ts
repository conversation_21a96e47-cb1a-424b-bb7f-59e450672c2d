/**
 * Mentorship API Endpoint
 * STORY-4-003: Real-time Collaboration & Mentorship
 */

import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { nanoid } from 'nanoid';

// Mentorship interfaces
interface MentorProfile {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  bio: string;
  expertise: string[];
  languages: string[];
  rating: number;
  totalSessions: number;
  completedSessions: number;
  responseTime: string;
  isOnline: boolean;
  isAvailable: boolean;
  hourlyRate?: number;
  timezone: string;
  createdAt: Date;
  updatedAt: Date;
}

interface MentorRequest {
  id: string;
  studentId: string;
  studentName: string;
  mentorId?: string; // Optional for specific mentor requests
  sessionId: string;
  message: string;
  topic: string;
  urgency: 'low' | 'medium' | 'high';
  status: 'pending' | 'accepted' | 'declined' | 'completed' | 'cancelled';
  createdAt: Date;
  updatedAt: Date;
  acceptedAt?: Date;
  completedAt?: Date;
}

interface MentorSession {
  id: string;
  requestId: string;
  mentorId: string;
  mentorName: string;
  studentId: string;
  studentName: string;
  sessionId: string;
  topic: string;
  startTime: Date;
  endTime?: Date;
  duration?: number; // in minutes
  status: 'scheduled' | 'active' | 'paused' | 'completed' | 'cancelled';
  notes: SessionNote[];
  rating?: {
    mentorRating: number;
    studentRating: number;
    mentorFeedback?: string;
    studentFeedback?: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

interface SessionNote {
  id: string;
  authorId: string;
  authorName: string;
  content: string;
  timestamp: Date;
  type: 'note' | 'action_item' | 'resource';
}

// Mock storage
const mentors = new Map<string, MentorProfile>();
const mentorRequests = new Map<string, MentorRequest>();
const mentorSessions = new Map<string, MentorSession>();

// Initialize sample data
initializeSampleData();

function initializeSampleData() {
  // Sample mentors
  const sampleMentors: MentorProfile[] = [
    {
      id: 'mentor-1',
      name: 'Sarah Chen',
      email: '<EMAIL>',
      bio: 'Senior Full-Stack Developer with 8+ years experience. Specializes in modern web development and mentoring junior developers.',
      expertise: ['JavaScript', 'React', 'Node.js', 'TypeScript', 'GraphQL'],
      languages: ['English', 'Mandarin'],
      rating: 4.9,
      totalSessions: 156,
      completedSessions: 148,
      responseTime: '< 5 min',
      isOnline: true,
      isAvailable: true,
      hourlyRate: 75,
      timezone: 'America/Los_Angeles',
      createdAt: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000),
      updatedAt: new Date(),
    },
    {
      id: 'mentor-2',
      name: 'Alex Rodriguez',
      email: '<EMAIL>',
      bio: 'Data Scientist and Python expert. Passionate about teaching programming fundamentals and advanced concepts.',
      expertise: [
        'Python',
        'Django',
        'Machine Learning',
        'Data Science',
        'SQL',
      ],
      languages: ['English', 'Spanish'],
      rating: 4.8,
      totalSessions: 203,
      completedSessions: 195,
      responseTime: '< 10 min',
      isOnline: true,
      isAvailable: true,
      hourlyRate: 80,
      timezone: 'America/New_York',
      createdAt: new Date(Date.now() - 300 * 24 * 60 * 60 * 1000),
      updatedAt: new Date(),
    },
    {
      id: 'mentor-3',
      name: 'Emily Johnson',
      email: '<EMAIL>',
      bio: 'Frontend specialist with a keen eye for design. Helps students create beautiful and functional user interfaces.',
      expertise: ['CSS', 'UI/UX', 'Svelte', 'Design Systems', 'Figma'],
      languages: ['English'],
      rating: 4.7,
      totalSessions: 89,
      completedSessions: 84,
      responseTime: '< 30 min',
      isOnline: false,
      isAvailable: false,
      hourlyRate: 65,
      timezone: 'Europe/London',
      createdAt: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000),
      updatedAt: new Date(),
    },
  ];

  sampleMentors.forEach(mentor => mentors.set(mentor.id, mentor));
}

export const GET: RequestHandler = async ({ url }) => {
  const action = url.searchParams.get('action');
  const userId = url.searchParams.get('userId');
  const sessionId = url.searchParams.get('sessionId');
  const mentorId = url.searchParams.get('mentorId');

  try {
    switch (action) {
      case 'mentors':
        // Get available mentors
        const expertise = url.searchParams.get('expertise');
        const isOnline = url.searchParams.get('online') === 'true';

        let availableMentors = Array.from(mentors.values());

        if (expertise) {
          availableMentors = availableMentors.filter(mentor =>
            mentor.expertise.some(skill =>
              skill.toLowerCase().includes(expertise.toLowerCase())
            )
          );
        }

        if (isOnline) {
          availableMentors = availableMentors.filter(mentor => mentor.isOnline);
        }

        return json({
          success: true,
          mentors: availableMentors,
        });

      case 'requests':
        if (!userId) {
          return json({ error: 'User ID is required' }, { status: 400 });
        }

        // Get mentor requests for user (as student or mentor)
        const userRequests = Array.from(mentorRequests.values()).filter(
          request => request.studentId === userId || request.mentorId === userId
        );

        return json({
          success: true,
          requests: userRequests,
        });

      case 'sessions':
        if (!userId) {
          return json({ error: 'User ID is required' }, { status: 400 });
        }

        // Get mentor sessions for user
        const userSessions = Array.from(mentorSessions.values()).filter(
          session => session.studentId === userId || session.mentorId === userId
        );

        return json({
          success: true,
          sessions: userSessions,
        });

      case 'mentor-profile':
        if (!mentorId) {
          return json({ error: 'Mentor ID is required' }, { status: 400 });
        }

        const mentor = mentors.get(mentorId);
        if (!mentor) {
          return json({ error: 'Mentor not found' }, { status: 404 });
        }

        return json({
          success: true,
          mentor,
        });

      default:
        return json({
          service: 'Mentorship API',
          version: '1.0.0',
          endpoints: {
            'GET ?action=mentors': 'List available mentors',
            'GET ?action=requests&userId=<id>': 'Get user mentor requests',
            'GET ?action=sessions&userId=<id>': 'Get user mentor sessions',
            'GET ?action=mentor-profile&mentorId=<id>': 'Get mentor profile',
            POST: 'Create mentor request or session',
          },
        });
    }
  } catch (error) {
    console.error('Mentorship API error:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { action, ...data } = await request.json();

    switch (action) {
      case 'request':
        const {
          studentId,
          studentName,
          sessionId,
          message,
          topic,
          urgency,
          mentorId,
        } = data;

        if (!studentId || !studentName || !sessionId || !message) {
          return json(
            { error: 'Student ID, name, session ID, and message are required' },
            { status: 400 }
          );
        }

        const requestId = nanoid();
        const mentorRequest: MentorRequest = {
          id: requestId,
          studentId,
          studentName,
          mentorId,
          sessionId,
          message,
          topic: topic || 'General Help',
          urgency: urgency || 'medium',
          status: 'pending',
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        mentorRequests.set(requestId, mentorRequest);

        return json({
          success: true,
          request: mentorRequest,
          message: 'Mentor request created successfully',
        });

      case 'accept-request':
        const { requestId, mentorId: acceptingMentorId, mentorName } = data;

        if (!requestId || !acceptingMentorId || !mentorName) {
          return json(
            { error: 'Request ID, mentor ID, and mentor name are required' },
            { status: 400 }
          );
        }

        const requestToAccept = mentorRequests.get(requestId);
        if (!requestToAccept) {
          return json({ error: 'Request not found' }, { status: 404 });
        }

        if (requestToAccept.status !== 'pending') {
          return json(
            { error: 'Request is no longer pending' },
            { status: 400 }
          );
        }

        // Update request status
        requestToAccept.status = 'accepted';
        requestToAccept.mentorId = acceptingMentorId;
        requestToAccept.acceptedAt = new Date();
        requestToAccept.updatedAt = new Date();

        mentorRequests.set(requestId, requestToAccept);

        // Create mentor session
        const sessionId = nanoid();
        const mentorSession: MentorSession = {
          id: sessionId,
          requestId,
          mentorId: acceptingMentorId,
          mentorName,
          studentId: requestToAccept.studentId,
          studentName: requestToAccept.studentName,
          sessionId: requestToAccept.sessionId,
          topic: requestToAccept.topic,
          startTime: new Date(),
          status: 'active',
          notes: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        mentorSessions.set(sessionId, mentorSession);

        return json({
          success: true,
          request: requestToAccept,
          session: mentorSession,
          message: 'Request accepted and session started',
        });

      case 'decline-request':
        const { requestId: declineRequestId } = data;

        if (!declineRequestId) {
          return json({ error: 'Request ID is required' }, { status: 400 });
        }

        const requestToDecline = mentorRequests.get(declineRequestId);
        if (!requestToDecline) {
          return json({ error: 'Request not found' }, { status: 404 });
        }

        requestToDecline.status = 'declined';
        requestToDecline.updatedAt = new Date();

        mentorRequests.set(declineRequestId, requestToDecline);

        return json({
          success: true,
          request: requestToDecline,
          message: 'Request declined',
        });

      case 'end-session':
        const { sessionId: endSessionId, rating, feedback } = data;

        if (!endSessionId) {
          return json({ error: 'Session ID is required' }, { status: 400 });
        }

        const sessionToEnd = mentorSessions.get(endSessionId);
        if (!sessionToEnd) {
          return json({ error: 'Session not found' }, { status: 404 });
        }

        const endTime = new Date();
        const duration = Math.round(
          (endTime.getTime() - sessionToEnd.startTime.getTime()) / 60000
        );

        sessionToEnd.status = 'completed';
        sessionToEnd.endTime = endTime;
        sessionToEnd.duration = duration;
        sessionToEnd.updatedAt = endTime;

        if (rating) {
          sessionToEnd.rating = rating;
        }

        mentorSessions.set(endSessionId, sessionToEnd);

        // Update the original request
        const originalRequest = mentorRequests.get(sessionToEnd.requestId);
        if (originalRequest) {
          originalRequest.status = 'completed';
          originalRequest.completedAt = endTime;
          originalRequest.updatedAt = endTime;
          mentorRequests.set(sessionToEnd.requestId, originalRequest);
        }

        return json({
          success: true,
          session: sessionToEnd,
          message: 'Session completed successfully',
        });

      case 'add-note':
        const {
          sessionId: noteSessionId,
          authorId,
          authorName,
          content,
          type,
        } = data;

        if (!noteSessionId || !authorId || !authorName || !content) {
          return json(
            {
              error:
                'Session ID, author ID, author name, and content are required',
            },
            { status: 400 }
          );
        }

        const sessionForNote = mentorSessions.get(noteSessionId);
        if (!sessionForNote) {
          return json({ error: 'Session not found' }, { status: 404 });
        }

        const note: SessionNote = {
          id: nanoid(),
          authorId,
          authorName,
          content,
          timestamp: new Date(),
          type: type || 'note',
        };

        sessionForNote.notes.push(note);
        sessionForNote.updatedAt = new Date();

        mentorSessions.set(noteSessionId, sessionForNote);

        return json({
          success: true,
          note,
          message: 'Note added successfully',
        });

      default:
        return json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Mentorship API error:', error);
    return json({ error: 'Failed to process request' }, { status: 500 });
  }
};

export const PUT: RequestHandler = async ({ request }) => {
  try {
    const { action, ...data } = await request.json();

    switch (action) {
      case 'update-availability':
        const { mentorId, isAvailable, isOnline } = data;

        if (!mentorId) {
          return json({ error: 'Mentor ID is required' }, { status: 400 });
        }

        const mentor = mentors.get(mentorId);
        if (!mentor) {
          return json({ error: 'Mentor not found' }, { status: 404 });
        }

        if (typeof isAvailable === 'boolean') {
          mentor.isAvailable = isAvailable;
        }
        if (typeof isOnline === 'boolean') {
          mentor.isOnline = isOnline;
        }

        mentor.updatedAt = new Date();
        mentors.set(mentorId, mentor);

        return json({
          success: true,
          mentor,
          message: 'Availability updated successfully',
        });

      default:
        return json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Update mentorship error:', error);
    return json({ error: 'Failed to update' }, { status: 500 });
  }
};
