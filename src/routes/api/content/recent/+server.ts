import { json } from '@sveltejs/kit';
import { readdir, readFile, stat } from 'fs/promises';
import { join } from 'path';

export async function GET() {
  try {
    const recentContent = [];

    // Content directories to scan
    const contentDirs = [
      { path: 'src/lib/data/courses', type: 'course' },
      { path: 'src/lib/data/news', type: 'news' },
      { path: 'generated_content/vybe_qubes', type: 'vybe_qube' },
    ];

    for (const dir of contentDirs) {
      try {
        const files = await readdir(dir.path);

        for (const file of files) {
          if (file.endsWith('.json')) {
            const filePath = join(dir.path, file);
            const stats = await stat(filePath);
            const content = await readFile(filePath, 'utf-8');
            const data = JSON.parse(content);

            recentContent.push({
              id: data.id || file.replace('.json', ''),
              title:
                data.title ||
                data.name ||
                `${dir.type} #${file.replace('.json', '')}`,
              type: dir.type,
              created_at: stats.mtime.toISOString(),
              quality_score:
                data.quality_score ||
                data.qualityScores?.overall ||
                Math.random() * 0.3 + 0.7,
              description: data.description || data.summary || '',
              url:
                data.url ||
                `/${dir.type}s/${data.id || file.replace('.json', '')}`,
            });
          }
        }
      } catch (error) {
        // Directory might not exist, continue
        console.log(`Directory ${dir.path} not found or inaccessible`);
      }
    }

    // Sort by creation date (newest first)
    recentContent.sort(
      (a, b) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    );

    return json(recentContent.slice(0, 20)); // Return last 20 items
  } catch (error) {
    console.error('Failed to load recent content:', error);

    // Return empty array instead of mock data - real content only
    return json([]);
  }
}
