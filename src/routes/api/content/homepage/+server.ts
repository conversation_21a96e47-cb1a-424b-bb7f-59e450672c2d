import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { ContentCurationService } from '$lib/services/ai/contentCuration';
import { NewsAggregationService } from '$lib/services/ai/newsAggregation';

const curationService = new ContentCurationService();
const aggregationService = new NewsAggregationService();

export const GET: RequestHandler = async () => {
  try {
    const [recommendations, trendingTopics] = await Promise.all([
      curationService.getHomepageRecommendations(),
      aggregationService.getTrendingTopics()
    ]);

    return json({
      success: true,
      featured: recommendations.featured,
      trending: recommendations.trending,
      recent: recommendations.recent,
      trendingTopics
    });
  } catch (error) {
    console.error('Homepage content API error:', error);
    return json(
      { 
        success: false, 
        error: 'Failed to get homepage content',
        featured: [],
        trending: [],
        recent: [],
        trendingTopics: []
      },
      { status: 500 }
    );
  }
};
