import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { databases } from '$lib/services/appwrite';
import { config } from '$lib/config';

export const GET: RequestHandler = async ({ url }) => {
  try {
    // Get query parameters
    const category = url.searchParams.get('category') || 'all';
    const difficulty = url.searchParams.get('difficulty') || 'all';
    const limit = parseInt(url.searchParams.get('limit') || '50');

    // Query MAS-generated courses from Appwrite
    let queries = [];

    if (category !== 'all') {
      queries.push(`category="${category}"`);
    }

    if (difficulty !== 'all') {
      queries.push(`difficulty="${difficulty}"`);
    }

    // Add published filter
    queries.push('isPublished=true');

    const response = await databases.listDocuments(
      config.appwrite.databaseId,
      'vybe_courses', // Collection for MAS-generated courses
      queries
    );

    // Transform courses to match the expected format
    const courses = response.documents.map(doc => ({
      id: doc.$id,
      title: doc.title,
      description: doc.description,
      difficulty: doc.difficulty,
      category: doc.category,
      estimatedDuration: doc.estimatedDuration || 120,
      price: doc.price || 0,
      isPublished: doc.isPublished,
      thumbnailUrl:
        doc.thumbnailUrl ||
        'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=400&h=200&fit=crop',
      lessonCount: doc.lessonCount || 8,
      rating: doc.rating || 4.5,
      studentCount: doc.studentCount || 0,
      tags: doc.tags || [],
      instructor: doc.instructor || {
        name: 'VybeCoding.ai MAS',
        avatar: '/images/avatars/mas.jpg',
        rating: 4.9,
      },
      gradient: doc.gradient || 'from-purple-400 to-blue-500',
      featured: doc.featured || false,
      icon: doc.iconName || 'BookOpen', // Store icon name as string
      stats: doc.stats || 'MAS Generated',
      createdAt: doc.$createdAt,
      updatedAt: doc.$updatedAt,
    }));

    return json({
      success: true,
      courses,
      total: response.total,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Failed to fetch MAS courses:', error);

    // Return empty courses array on error to allow fallback to static courses
    return json({
      success: false,
      courses: [],
      total: 0,
      error: 'Failed to fetch courses',
      timestamp: new Date().toISOString(),
    });
  }
};

export const POST: RequestHandler = async ({ request, locals }) => {
  try {
    // Only allow authenticated users to create courses
    if (!locals.user) {
      return json({ error: 'Authentication required' }, { status: 401 });
    }

    const data = await request.json();

    // Validate required fields
    if (
      !data.title ||
      !data.description ||
      !data.category ||
      !data.difficulty
    ) {
      return json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Create new MAS-generated course
    const course = {
      title: data.title,
      description: data.description,
      difficulty: data.difficulty,
      category: data.category,
      estimatedDuration: data.estimatedDuration || 120,
      price: data.price || 0,
      isPublished: data.isPublished || false,
      thumbnailUrl:
        data.thumbnailUrl ||
        'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=400&h=200&fit=crop',
      lessonCount: data.lessonCount || 8,
      rating: 4.5,
      studentCount: 0,
      tags: data.tags || [],
      instructor: data.instructor || {
        name: 'VybeCoding.ai MAS',
        avatar: '/images/avatars/mas.jpg',
        rating: 4.9,
      },
      gradient: data.gradient || 'from-purple-400 to-blue-500',
      featured: data.featured || false,
      iconName: data.iconName || 'BookOpen',
      stats: data.stats || 'MAS Generated',
      generatedBy: 'MAS',
      generatorId: data.generatorId || 'unknown',
      createdBy: locals.user.id,
    };

    // Save to database
    const saved = await databases.createDocument(
      config.appwrite.databaseId,
      'vybe_courses',
      'unique()',
      course
    );

    return json({
      success: true,
      course: saved,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Failed to create course:', error);

    return json(
      {
        success: false,
        error: 'Failed to create course',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
};
