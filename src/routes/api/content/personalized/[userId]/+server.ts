import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { ContentCurationService } from '$lib/services/ai/contentCuration';

const curationService = new ContentCurationService();

export const GET: RequestHandler = async ({ params, url }) => {
  try {
    const { userId } = params;
    const limit = parseInt(url.searchParams.get('limit') || '20');

    if (!userId) {
      return json({ error: 'User ID is required' }, { status: 400 });
    }

    const articles = await curationService.getPersonalizedFeed(userId, limit);

    return json({
      success: true,
      articles,
      total: articles.length,
      userId
    });
  } catch (error) {
    console.error('Personalized content API error:', error);
    return json(
      { 
        success: false, 
        error: 'Failed to get personalized content',
        articles: []
      },
      { status: 500 }
    );
  }
};
