import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { ContentCurationService } from '$lib/services/ai/contentCuration';

const curationService = new ContentCurationService();

export const GET: RequestHandler = async ({ url }) => {
  try {
    const query = url.searchParams.get('q');
    const limit = parseInt(url.searchParams.get('limit') || '20');

    if (!query) {
      return json({ error: 'Search query is required' }, { status: 400 });
    }

    const articles = await curationService.searchArticles(query, limit);

    return json({
      success: true,
      articles,
      total: articles.length,
      query
    });
  } catch (error) {
    console.error('Content search API error:', error);
    return json(
      { 
        success: false, 
        error: 'Failed to search content',
        articles: []
      },
      { status: 500 }
    );
  }
};
