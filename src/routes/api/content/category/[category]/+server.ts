import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { ContentCurationService } from '$lib/services/ai/contentCuration';

const curationService = new ContentCurationService();

export const GET: RequestHandler = async ({ params, url }) => {
  try {
    const { category } = params;
    const limit = parseInt(url.searchParams.get('limit') || '20');

    if (!category) {
      return json({ error: 'Category is required' }, { status: 400 });
    }

    const articles = await curationService.getArticlesByCategory(category, limit);

    return json({
      success: true,
      articles,
      total: articles.length,
      category
    });
  } catch (error) {
    console.error('Content category API error:', error);
    return json(
      { 
        success: false, 
        error: 'Failed to get category content',
        articles: []
      },
      { status: 500 }
    );
  }
};
