/**
 * Deployment Management API
 * Manages automated content deployment from Enhanced MAS to live platform
 */

import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { deploymentOrchestrator } from '$lib/services/deployment-orchestrator';

export const GET: RequestHandler = async ({ url }) => {
  try {
    const action = url.searchParams.get('action');

    switch (action) {
      case 'status':
        return json({
          success: true,
          stats: deploymentOrchestrator.getStats(),
          activeTasks: deploymentOrchestrator.getActiveTasks(),
          isMonitoring: true // TODO: Add actual monitoring status
        });

      case 'tasks':
        const taskId = url.searchParams.get('taskId');
        if (taskId) {
          const task = deploymentOrchestrator.getTask(taskId);
          if (!task) {
            return json({ error: 'Task not found' }, { status: 404 });
          }
          return json({ success: true, task });
        }
        
        return json({
          success: true,
          tasks: deploymentOrchestrator.getActiveTasks()
        });

      case 'stats':
        return json({
          success: true,
          stats: deploymentOrchestrator.getStats()
        });

      default:
        return json({
          success: true,
          message: 'Deployment API is operational',
          endpoints: {
            'GET ?action=status': 'Get deployment system status',
            'GET ?action=tasks': 'Get all deployment tasks',
            'GET ?action=tasks&taskId=ID': 'Get specific task',
            'GET ?action=stats': 'Get deployment statistics',
            'POST': 'Start/stop monitoring or force deployment'
          }
        });
    }

  } catch (error) {
    console.error('Deployment API error:', error);
    return json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
};

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { action, ...params } = await request.json();

    switch (action) {
      case 'start_monitoring':
        await deploymentOrchestrator.startMonitoring();
        return json({
          success: true,
          message: 'Deployment monitoring started',
          stats: deploymentOrchestrator.getStats()
        });

      case 'stop_monitoring':
        deploymentOrchestrator.stopMonitoring();
        return json({
          success: true,
          message: 'Deployment monitoring stopped'
        });

      case 'force_deployment':
        const { contentData, contentType } = params;
        
        if (!contentData || !contentType) {
          return json({
            success: false,
            error: 'contentData and contentType are required for force deployment'
          }, { status: 400 });
        }

        const task = await deploymentOrchestrator.forceDeployment(contentData, contentType);
        
        return json({
          success: true,
          message: 'Forced deployment initiated',
          task
        });

      case 'deploy_existing_content':
        // Deploy content that was generated but not deployed
        const { generationId } = params;
        
        if (!generationId) {
          return json({
            success: false,
            error: 'generationId is required'
          }, { status: 400 });
        }

        // This would trigger deployment of existing generated content
        // Implementation would depend on how Enhanced MAS stores completed generations
        
        return json({
          success: true,
          message: 'Deployment of existing content initiated',
          generationId
        });

      default:
        return json({
          success: false,
          error: `Unknown action: ${action}`
        }, { status: 400 });
    }

  } catch (error) {
    console.error('Deployment API POST error:', error);
    return json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
};
