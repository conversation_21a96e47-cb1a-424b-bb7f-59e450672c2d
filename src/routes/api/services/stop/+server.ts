/**
 * Protocol Service Stop API
 * Handles stopping protocol services via Docker containers
 * STORY-MAS-003: Protocol Service Management
 */

import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { serviceName, dockerContainer } = await request.json();
    
    console.log(`⏹️ Stopping service: ${serviceName} (${dockerContainer})`);
    
    // In a real implementation, this would stop the Docker container
    // For now, we'll simulate the stop process
    
    // Simulate different responses based on service
    const simulatedResponses: Record<string, any> = {
      'mcp': {
        success: true,
        message: 'MCP Server stopped successfully'
      },
      'a2a': {
        success: true,
        message: 'A2A Protocol stopped successfully'
      },
      'agentic_retrieval': {
        success: true,
        message: 'Agentic Retrieval stopped successfully'
      },
      'guardrails': {
        success: true,
        message: 'Guardrails Service stopped successfully'
      }
    };
    
    const response = simulatedResponses[serviceName];
    
    if (response) {
      // Stop the actual Docker service
      try {
        const { exec } = await import('child_process');
        const { promisify } = await import('util');
        const execAsync = promisify(exec);

        // Stop the Docker container
        await execAsync(`docker stop ${dockerContainer}`);
        console.log(`✅ Stopped Docker container: ${dockerContainer}`);
      } catch (dockerError) {
        console.warn(`⚠️ Docker stop failed for ${dockerContainer}:`, dockerError);
        // Continue anyway - service might already be stopped
      }

      return json({
        success: true,
        serviceName,
        dockerContainer,
        message: response.message,
        status: 'stopped',
        timestamp: new Date().toISOString()
      });
    } else {
      return json({
        success: false,
        error: `Unknown service: ${serviceName}`,
        timestamp: new Date().toISOString()
      }, { status: 400 });
    }
    
  } catch (error) {
    console.error('Service stop error:', error);
    
    return json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
};
