/**
 * Service Health Check Proxy API
 * Bypasses CORS issues by checking service health server-side
 * STORY-MAS-003: Protocol Service Management
 */

import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { serviceName, port, endpoint } = await request.json();
    
    if (!serviceName || !port || !endpoint) {
      return json({
        healthy: false,
        error: 'Missing required parameters: serviceName, port, endpoint'
      }, { status: 400 });
    }
    
    console.log(`🔍 Health check: ${serviceName} on port ${port}${endpoint}`);
    
    try {
      // Use node's fetch to check service health
      const healthUrl = `http://localhost:${port}${endpoint}`;
      const response = await fetch(healthUrl, {
        method: 'GET',
        signal: AbortSignal.timeout(3000)
      });
      
      if (response.ok) {
        const contentType = response.headers.get('content-type');
        let responseData: any = null;
        
        try {
          if (contentType?.includes('application/json')) {
            responseData = await response.json();
          } else {
            responseData = await response.text();
          }
        } catch (parseError) {
          console.log(`Could not parse response for ${serviceName}:`, parseError);
        }
        
        // Special handling for different services
        let isHealthy = true;
        
        if (serviceName === 'ollama') {
          // For Ollama, check if response contains expected content
          isHealthy = typeof responseData === 'string' && 
                     responseData.includes('Ollama is running');
        } else if (serviceName === 'mcp' || serviceName === 'a2a' || 
                   serviceName === 'agentic_retrieval' || serviceName === 'guardrails') {
          // For protocol services, check for JSON health response
          isHealthy = responseData && 
                     (responseData.status === 'healthy' || 
                      responseData.health === 'healthy' ||
                      response.status === 200);
        }
        
        console.log(`✅ ${serviceName}: ${isHealthy ? 'HEALTHY' : 'UNHEALTHY'}`);
        
        return json({
          healthy: isHealthy,
          serviceName,
          port,
          status: response.status,
          responseData: responseData,
          timestamp: new Date().toISOString()
        });
        
      } else {
        console.log(`❌ ${serviceName}: HTTP ${response.status}`);
        
        return json({
          healthy: false,
          serviceName,
          port,
          status: response.status,
          error: `HTTP ${response.status}: ${response.statusText}`,
          timestamp: new Date().toISOString()
        });
      }
      
    } catch (fetchError) {
      console.log(`❌ ${serviceName}: Connection failed -`, fetchError);
      
      return json({
        healthy: false,
        serviceName,
        port,
        error: fetchError instanceof Error ? fetchError.message : 'Connection failed',
        timestamp: new Date().toISOString()
      });
    }
    
  } catch (error) {
    console.error('Health check API error:', error);
    
    return json({
      healthy: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
};
