// VybeCoding.ai Alerting System API
// Manages performance and security alerts with notification delivery

import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

interface Alert {
  id: string;
  timestamp: string;
  type: 'performance' | 'security' | 'infrastructure' | 'business';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  source: string;
  status: 'active' | 'acknowledged' | 'resolved';
  metrics?: Record<string, any>;
  actions?: string[];
  escalated?: boolean;
  resolvedAt?: string;
  acknowledgedBy?: string;
}

interface AlertRule {
  id: string;
  name: string;
  type: 'performance' | 'security' | 'infrastructure' | 'business';
  condition: string;
  threshold: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  enabled: boolean;
  cooldown: number; // minutes
  notifications: NotificationChannel[];
}

interface NotificationChannel {
  type: 'email' | 'slack' | 'discord' | 'webhook';
  target: string;
  enabled: boolean;
  severityFilter?: string[];
}

// In-memory alert storage (in production, use database)
let alertStore = {
  alerts: [] as Alert[],
  rules: [
    {
      id: 'high-response-time',
      name: 'High Response Time',
      type: 'performance',
      condition: 'avg_response_time > threshold',
      threshold: 1000,
      severity: 'high',
      enabled: true,
      cooldown: 5,
      notifications: [
        {
          type: 'slack',
          target: process.env.SLACK_WEBHOOK_URL || '',
          enabled: true,
        },
      ],
    },
    {
      id: 'high-error-rate',
      name: 'High Error Rate',
      type: 'performance',
      condition: 'error_rate > threshold',
      threshold: 5,
      severity: 'critical',
      enabled: true,
      cooldown: 2,
      notifications: [
        {
          type: 'slack',
          target: process.env.SLACK_WEBHOOK_URL || '',
          enabled: true,
        },
      ],
    },
    {
      id: 'memory-usage-high',
      name: 'High Memory Usage',
      type: 'infrastructure',
      condition: 'memory_percentage > threshold',
      threshold: 85,
      severity: 'medium',
      enabled: true,
      cooldown: 10,
      notifications: [
        {
          type: 'slack',
          target: process.env.SLACK_WEBHOOK_URL || '',
          enabled: true,
        },
      ],
    },
    {
      id: 'security-vulnerabilities',
      name: 'Critical Security Vulnerabilities',
      type: 'security',
      condition: 'critical_vulnerabilities > threshold',
      threshold: 0,
      severity: 'critical',
      enabled: true,
      cooldown: 60,
      notifications: [
        {
          type: 'slack',
          target: process.env.SLACK_WEBHOOK_URL || '',
          enabled: true,
        },
      ],
    },
  ] as AlertRule[],
  lastCheck: new Date().toISOString(),
};

export const GET: RequestHandler = async ({ url }) => {
  try {
    const status = url.searchParams.get('status');
    const type = url.searchParams.get('type');
    const severity = url.searchParams.get('severity');
    const limit = parseInt(url.searchParams.get('limit') || '50');

    let filteredAlerts = alertStore.alerts;

    // Apply filters
    if (status) {
      filteredAlerts = filteredAlerts.filter(alert => alert.status === status);
    }

    if (type) {
      filteredAlerts = filteredAlerts.filter(alert => alert.type === type);
    }

    if (severity) {
      filteredAlerts = filteredAlerts.filter(
        alert => alert.severity === severity
      );
    }

    // Sort by timestamp (newest first) and limit
    filteredAlerts = filteredAlerts
      .sort(
        (a, b) =>
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      )
      .slice(0, limit);

    // Get alert statistics
    const stats = {
      total: alertStore.alerts.length,
      active: alertStore.alerts.filter(a => a.status === 'active').length,
      acknowledged: alertStore.alerts.filter(a => a.status === 'acknowledged')
        .length,
      resolved: alertStore.alerts.filter(a => a.status === 'resolved').length,
      critical: alertStore.alerts.filter(
        a => a.severity === 'critical' && a.status === 'active'
      ).length,
      high: alertStore.alerts.filter(
        a => a.severity === 'high' && a.status === 'active'
      ).length,
      lastCheck: alertStore.lastCheck,
    };

    return json({
      alerts: filteredAlerts,
      stats,
      rules: alertStore.rules.filter(rule => rule.enabled),
    });
  } catch (error) {
    console.error('Error fetching alerts:', error);
    return json(
      { error: 'Failed to fetch alerts', timestamp: new Date().toISOString() },
      { status: 500 }
    );
  }
};

export const POST: RequestHandler = async ({ request }) => {
  try {
    const body = await request.json();

    if (body.action === 'check_metrics') {
      // Check current metrics against alert rules
      await checkMetricsForAlerts(body.metrics);
      return json({ success: true, checked: true });
    }

    if (body.action === 'acknowledge') {
      // Acknowledge an alert
      const alert = alertStore.alerts.find(a => a.id === body.alertId);
      if (alert) {
        alert.status = 'acknowledged';
        alert.acknowledgedBy = body.acknowledgedBy || 'system';
        return json({ success: true, acknowledged: true });
      }
      return json({ error: 'Alert not found' }, { status: 404 });
    }

    if (body.action === 'resolve') {
      // Resolve an alert
      const alert = alertStore.alerts.find(a => a.id === body.alertId);
      if (alert) {
        alert.status = 'resolved';
        alert.resolvedAt = new Date().toISOString();
        return json({ success: true, resolved: true });
      }
      return json({ error: 'Alert not found' }, { status: 404 });
    }

    if (body.action === 'create_alert') {
      // Create a new alert
      const alert: Alert = {
        id: generateAlertId(),
        timestamp: new Date().toISOString(),
        type: body.type || 'performance',
        severity: body.severity || 'medium',
        title: body.title || 'Alert',
        description: body.description || '',
        source: body.source || 'manual',
        status: 'active',
        metrics: body.metrics,
        actions: body.actions,
      };

      alertStore.alerts.push(alert);

      // Send notifications
      await sendNotifications(alert);

      return json({ success: true, alert });
    }

    return json({ error: 'Invalid action' }, { status: 400 });
  } catch (error) {
    console.error('Error processing alert request:', error);
    return json({ error: 'Failed to process alert request' }, { status: 500 });
  }
};

// Check metrics against alert rules
async function checkMetricsForAlerts(metrics: any) {
  alertStore.lastCheck = new Date().toISOString();

  for (const rule of alertStore.rules) {
    if (!rule.enabled) continue;

    // Check if rule is in cooldown
    const lastAlert = alertStore.alerts
      .filter(a => a.source === rule.id && a.status === 'active')
      .sort(
        (a, b) =>
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      )[0];

    if (lastAlert) {
      const cooldownEnd =
        new Date(lastAlert.timestamp).getTime() + rule.cooldown * 60 * 1000;
      if (Date.now() < cooldownEnd) {
        continue; // Still in cooldown
      }
    }

    // Evaluate rule condition
    const shouldAlert = evaluateAlertCondition(rule, metrics);

    if (shouldAlert) {
      const alert: Alert = {
        id: generateAlertId(),
        timestamp: new Date().toISOString(),
        type: rule.type,
        severity: rule.severity,
        title: rule.name,
        description: generateAlertDescription(rule, metrics),
        source: rule.id,
        status: 'active',
        metrics: extractRelevantMetrics(rule, metrics),
        actions: generateAlertActions(rule),
      };

      alertStore.alerts.push(alert);

      // Send notifications
      await sendNotifications(alert);
    }
  }

  // Clean up old alerts (keep last 1000)
  if (alertStore.alerts.length > 1000) {
    alertStore.alerts = alertStore.alerts
      .sort(
        (a, b) =>
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      )
      .slice(0, 500);
  }
}

// Evaluate alert condition
function evaluateAlertCondition(rule: AlertRule, metrics: any): boolean {
  try {
    switch (rule.id) {
      case 'high-response-time':
        return metrics.performance?.responseTime?.avg > rule.threshold;
      case 'high-error-rate':
        return metrics.performance?.errors?.rate > rule.threshold;
      case 'memory-usage-high':
        return metrics.resources?.memory?.percentage > rule.threshold;
      case 'security-vulnerabilities':
        return (
          (metrics.security?.vulnerabilities?.critical || 0) > rule.threshold
        );
      default:
        return false;
    }
  } catch (error) {
    console.error('Error evaluating alert condition:', error);
    return false;
  }
}

// Generate alert description
function generateAlertDescription(rule: AlertRule, metrics: any): string {
  switch (rule.id) {
    case 'high-response-time':
      return `Average response time is ${metrics.performance?.responseTime?.avg}ms, exceeding threshold of ${rule.threshold}ms`;
    case 'high-error-rate':
      return `Error rate is ${metrics.performance?.errors?.rate}%, exceeding threshold of ${rule.threshold}%`;
    case 'memory-usage-high':
      return `Memory usage is ${metrics.resources?.memory?.percentage}%, exceeding threshold of ${rule.threshold}%`;
    case 'security-vulnerabilities':
      return `${metrics.security?.vulnerabilities?.critical || 0} critical security vulnerabilities detected`;
    default:
      return `Alert condition met for ${rule.name}`;
  }
}

// Extract relevant metrics for alert
function extractRelevantMetrics(
  rule: AlertRule,
  metrics: any
): Record<string, any> {
  switch (rule.type) {
    case 'performance':
      return {
        responseTime: metrics.performance?.responseTime,
        errorRate: metrics.performance?.errors?.rate,
        throughput: metrics.performance?.throughput,
      };
    case 'infrastructure':
      return {
        memory: metrics.resources?.memory,
        cpu: metrics.resources?.cpu,
      };
    case 'security':
      return {
        vulnerabilities: metrics.security?.vulnerabilities,
        status: metrics.security?.status,
      };
    default:
      return {};
  }
}

// Generate alert actions
function generateAlertActions(rule: AlertRule): string[] {
  const actions = [];

  switch (rule.type) {
    case 'performance':
      actions.push(
        'Check application logs',
        'Review recent deployments',
        'Monitor resource usage'
      );
      break;
    case 'infrastructure':
      actions.push(
        'Check system resources',
        'Review scaling policies',
        'Monitor container health'
      );
      break;
    case 'security':
      actions.push(
        'Review security scan results',
        'Update dependencies',
        'Check for security patches'
      );
      break;
  }

  return actions;
}

// Send notifications
async function sendNotifications(alert: Alert) {
  const rule = alertStore.rules.find(r => r.id === alert.source);
  if (!rule) return;

  for (const channel of rule.notifications) {
    if (!channel.enabled) continue;

    // Check severity filter
    if (
      channel.severityFilter &&
      !channel.severityFilter.includes(alert.severity)
    ) {
      continue;
    }

    try {
      switch (channel.type) {
        case 'slack':
          await sendSlackNotification(channel.target, alert);
          break;
        case 'discord':
          await sendDiscordNotification(channel.target, alert);
          break;
        case 'webhook':
          await sendWebhookNotification(channel.target, alert);
          break;
      }
    } catch (error) {
      console.error(`Failed to send ${channel.type} notification:`, error);
    }
  }
}

// Send Slack notification
async function sendSlackNotification(webhookUrl: string, alert: Alert) {
  if (!webhookUrl) return;

  const color =
    {
      low: '#36a64f',
      medium: '#ff9500',
      high: '#ff6b6b',
      critical: '#ff0000',
    }[alert.severity] || '#808080';

  const payload = {
    text: `🚨 VybeCoding.ai Alert: ${alert.title}`,
    attachments: [
      {
        color,
        fields: [
          {
            title: 'Severity',
            value: alert.severity.toUpperCase(),
            short: true,
          },
          { title: 'Type', value: alert.type, short: true },
          { title: 'Description', value: alert.description, short: false },
          {
            title: 'Timestamp',
            value: new Date(alert.timestamp).toLocaleString(),
            short: true,
          },
        ],
      },
    ],
  };

  await fetch(webhookUrl, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(payload),
  });
}

// Send Discord notification
async function sendDiscordNotification(webhookUrl: string, alert: Alert) {
  if (!webhookUrl) return;

  const color =
    {
      low: 0x36a64f,
      medium: 0xff9500,
      high: 0xff6b6b,
      critical: 0xff0000,
    }[alert.severity] || 0x808080;

  const payload = {
    embeds: [
      {
        title: `🚨 VybeCoding.ai Alert: ${alert.title}`,
        description: alert.description,
        color,
        fields: [
          {
            name: 'Severity',
            value: alert.severity.toUpperCase(),
            inline: true,
          },
          { name: 'Type', value: alert.type, inline: true },
          {
            name: 'Timestamp',
            value: new Date(alert.timestamp).toLocaleString(),
            inline: false,
          },
        ],
        timestamp: alert.timestamp,
      },
    ],
  };

  await fetch(webhookUrl, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(payload),
  });
}

// Send webhook notification
async function sendWebhookNotification(webhookUrl: string, alert: Alert) {
  await fetch(webhookUrl, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ alert, source: 'vybecoding-monitoring' }),
  });
}

// Generate unique alert ID
function generateAlertId(): string {
  return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}
