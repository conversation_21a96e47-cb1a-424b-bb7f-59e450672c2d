<!--
  Vybe Qube Deployment Details Page
  Shows real-time deployment progress and status
  STORY-3-001: Vybe Qube Deployment Infrastructure
-->

<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import DeploymentProgress from '$lib/components/DeploymentProgress.svelte';
  import {
    vybeQubeDeployment,
    deploymentStore,
  } from '$lib/services/vybeQubeDeployment';
  import { Button, Card, Badge } from '$lib/components/ui';
  import { ArrowLeft, RefreshCw, Trash2, ExternalLink } from 'lucide-svelte';

  let qubeId = $page.params.id;
  let deploymentId: string | null = null;
  let loading = true;
  let error: string | null = null;

  onMount(async () => {
    await loadDeploymentInfo();
  });

  async function loadDeploymentInfo() {
    try {
      loading = true;
      error = null;

      // Get all deployments and find the one for this qube
      const deployments = await vybeQubeDeployment.listDeployments();
      const qubeDeployment = deployments.find(d => d.qube_id === qubeId);

      if (qubeDeployment) {
        deploymentId = qubeDeployment.deployment_id;
      } else {
        error = 'No deployment found for this Vybe Qube';
      }
    } catch (err) {
      error =
        err instanceof Error
          ? err.message
          : 'Failed to load deployment information';
    } finally {
      loading = false;
    }
  }

  async function refreshDeployment() {
    if (deploymentId) {
      await vybeQubeDeployment.getDeploymentStatus(deploymentId);
    }
  }

  async function deleteDeployment() {
    if (
      deploymentId &&
      confirm(
        'Are you sure you want to delete this deployment? This will remove the live website.'
      )
    ) {
      try {
        await vybeQubeDeployment.deleteDeployment(deploymentId);
        goto(`/vybe-qubes/${qubeId}`);
      } catch (err) {
        console.error('Failed to delete deployment:', err);
        // Could show a toast notification here
      }
    }
  }

  function goBack() {
    goto(`/vybe-qubes/${qubeId}`);
  }

  // Subscribe to deployment store for real-time updates
  $: deployment = deploymentId ? $deploymentStore.get(deploymentId) : null;
</script>

<svelte:head>
  <title>Deployment Status - Vybe Qube {qubeId} | VybeCoding.ai</title>
  <meta
    name="description"
    content="Real-time deployment status for Vybe Qube {qubeId}"
  />
</svelte:head>

<div class="min-h-screen bg-gray-50 py-8">
  <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <Button variant="outline" on:click={goBack}>
            <ArrowLeft class="h-4 w-4 mr-2" />
            Back to Qube
          </Button>

          <div>
            <h1 class="text-3xl font-bold text-gray-900">Deployment Status</h1>
            <p class="text-gray-600">Vybe Qube: {qubeId}</p>
          </div>
        </div>

        <div class="flex items-center space-x-3">
          {#if deploymentId}
            <Button variant="outline" on:click={refreshDeployment}>
              <RefreshCw class="h-4 w-4 mr-2" />
              Refresh
            </Button>

            {#if deployment?.status === 'deployed'}
              <Button
                variant="outline"
                on:click={() => window.open(deployment.url, '_blank')}
              >
                <ExternalLink class="h-4 w-4 mr-2" />
                View Live
              </Button>
            {/if}

            <Button variant="destructive" on:click={deleteDeployment}>
              <Trash2 class="h-4 w-4 mr-2" />
              Delete
            </Button>
          {/if}
        </div>
      </div>
    </div>

    <!-- Loading State -->
    {#if loading}
      <Card class="p-8">
        <div class="flex items-center justify-center">
          <div
            class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"
          ></div>
          <span class="ml-3 text-gray-600"
            >Loading deployment information...</span
          >
        </div>
      </Card>

      <!-- Error State -->
    {:else if error}
      <Card class="p-8">
        <div class="text-center">
          <div class="bg-red-50 border border-red-200 rounded-lg p-6">
            <h3 class="text-lg font-medium text-red-800 mb-2">
              Unable to Load Deployment
            </h3>
            <p class="text-red-600 mb-4">{error}</p>

            <div class="flex justify-center space-x-3">
              <Button variant="outline" on:click={loadDeploymentInfo}>
                Try Again
              </Button>

              <Button variant="outline" on:click={goBack}>Go Back</Button>
            </div>
          </div>
        </div>
      </Card>

      <!-- Deployment Progress -->
    {:else if deploymentId}
      <DeploymentProgress {deploymentId} showLogs={true} autoRefresh={true} />

      <!-- Additional Deployment Info -->
      {#if deployment}
        <div class="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- Deployment Details -->
          <Card class="p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
              Deployment Details
            </h3>

            <div class="space-y-3">
              <div class="flex justify-between">
                <span class="text-gray-500">Deployment ID:</span>
                <span class="font-mono text-sm">{deployment.deployment_id}</span
                >
              </div>

              <div class="flex justify-between">
                <span class="text-gray-500">Qube ID:</span>
                <span class="font-mono text-sm">{deployment.qube_id}</span>
              </div>

              <div class="flex justify-between">
                <span class="text-gray-500">Subdomain:</span>
                <span class="font-mono text-sm"
                  >{deployment.subdomain}.vybequbes.com</span
                >
              </div>

              <div class="flex justify-between">
                <span class="text-gray-500">Status:</span>
                <Badge
                  variant={deployment.status === 'deployed'
                    ? 'success'
                    : deployment.status === 'failed'
                      ? 'destructive'
                      : 'secondary'}
                >
                  {deployment.status}
                </Badge>
              </div>

              <div class="flex justify-between">
                <span class="text-gray-500">Progress:</span>
                <span>{deployment.progress}%</span>
              </div>
            </div>
          </Card>

          <!-- Quick Actions -->
          <Card class="p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
              Quick Actions
            </h3>

            <div class="space-y-3">
              {#if deployment.status === 'deployed'}
                <Button
                  class="w-full"
                  on:click={() => window.open(deployment.url, '_blank')}
                >
                  <ExternalLink class="h-4 w-4 mr-2" />
                  Visit Live Website
                </Button>

                <Button
                  variant="outline"
                  class="w-full"
                  on:click={() => navigator.clipboard.writeText(deployment.url)}
                >
                  Copy Website URL
                </Button>
              {:else if deployment.status === 'failed'}
                <Button
                  variant="outline"
                  class="w-full"
                  on:click={() => goto(`/vybe-qubes/${qubeId}/generate`)}
                >
                  Retry Deployment
                </Button>
              {:else}
                <div class="text-center text-gray-500 py-4">
                  <p>Deployment in progress...</p>
                  <p class="text-sm">
                    Actions will be available once deployment completes
                  </p>
                </div>
              {/if}

              <Button
                variant="destructive"
                class="w-full"
                on:click={deleteDeployment}
              >
                <Trash2 class="h-4 w-4 mr-2" />
                Delete Deployment
              </Button>
            </div>
          </Card>
        </div>

        <!-- Deployment Timeline -->
        {#if deployment.logs.length > 0}
          <Card class="p-6 mt-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
              Deployment Timeline
            </h3>

            <div class="space-y-3">
              {#each deployment.logs as log, index}
                <div class="flex items-start space-x-3">
                  <div
                    class="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"
                  ></div>
                  <div class="flex-1">
                    <p class="text-sm text-gray-700 font-mono">{log}</p>
                  </div>
                </div>
              {/each}
            </div>
          </Card>
        {/if}
      {/if}
    {/if}
  </div>
</div>

<style>
  /* Custom styles for deployment page */
  :global(.deployment-timeline .timeline-item:last-child .timeline-line) {
    display: none;
  }
</style>
