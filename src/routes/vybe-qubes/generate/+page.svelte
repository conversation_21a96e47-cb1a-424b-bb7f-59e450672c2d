<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { Card, Button, Input, Badge } from '$lib/components/ui';
  import { vybeQubeGenerator } from '$lib/services/vybeQubeGenerator';
  import { config } from '$lib/config';
  import type { VybeQubeRequest, VybeQubeResponse } from '$lib/types/vybeQube';
  import { <PERSON><PERSON><PERSON>, ArrowRight, Clock } from 'lucide-svelte';

  let selectedTemplate: string = '';
  let businessConcept: string = '';
  let targetAudience: string = '';
  let revenueModel: string = '';
  let customization = {
    branding: {
      primaryColor: '#3B82F6',
      secondaryColor: '#10B981',
      companyName: '',
    },
    features: [] as string[],
    integrations: [] as string[],
  };

  let generating: boolean = false;
  let generationResponse: VybeQubeResponse | null = null;
  let error: string | null = null;
  let currentStep: number = 1;
  const totalSteps: number = 4;

  // Define available templates
  const templates = {
    ecommerce: {
      name: 'E-commerce Store',
      description: 'Online store with payments and inventory',
      features: [
        'Payment Processing',
        'Inventory Management',
        'Customer Reviews',
      ],
    },
    saas: {
      name: 'SaaS Platform',
      description: 'Software as a Service application',
      features: [
        'User Management',
        'Subscription Billing',
        'Analytics Dashboard',
      ],
    },
    content: {
      name: 'Content Site',
      description: 'Blog or content marketing website',
      features: ['Content Management', 'SEO Optimization', 'Social Sharing'],
    },
    marketplace: {
      name: 'Marketplace',
      description: 'Multi-vendor marketplace platform',
      features: ['Vendor Management', 'Commission System', 'Product Listings'],
    },
    'digital-products': {
      name: 'Digital Products',
      description: 'Sell digital downloads and courses',
      features: ['Digital Downloads', 'Course Platform', 'Payment Gateway'],
    },
  };

  onMount(() => {
    // Auto-select first template if none selected
    if (!selectedTemplate && Object.keys(templates).length > 0) {
      selectedTemplate = Object.keys(templates)[0];
    }
  });

  function selectTemplate(templateId: string) {
    selectedTemplate = templateId;
    currentStep = 2;
  }

  function nextStep() {
    if (currentStep < totalSteps) {
      currentStep++;
    }
  }

  function prevStep() {
    if (currentStep > 1) {
      currentStep--;
    }
  }

  async function generateVybeQube() {
    if (
      !selectedTemplate ||
      !businessConcept ||
      !targetAudience ||
      !revenueModel
    ) {
      error = 'Please fill in all required fields';
      return;
    }

    generating = true;
    error = null;

    try {
      const request: VybeQubeRequest = {
        templateType: selectedTemplate as VybeQubeRequest['templateType'],
        businessConcept,
        targetAudience,
        revenueModel,
        customization,
      };

      const response = await vybeQubeGenerator.generateVybeQube(request);
      generationResponse = response;

      // Redirect to status page
      goto(`/vybe-qubes/${response.qubeId}`);
    } catch (err) {
      error = err instanceof Error ? err.message : 'Generation failed';
      generating = false;
    }
  }

  function getTemplateIcon(category: string) {
    switch (category) {
      case 'retail':
        return '🛍️';
      case 'software':
        return '💻';
      case 'media':
        return '📝';
      case 'services':
        return '🤝';
      case 'digital':
        return '📱';
      default:
        return '🌟';
    }
  }

  $: selectedTemplateData = selectedTemplate
    ? templates[selectedTemplate]
    : null;
  $: canProceed =
    currentStep === 1
      ? selectedTemplate
      : currentStep === 2
        ? businessConcept && targetAudience
        : currentStep === 3
          ? revenueModel
          : true;
</script>

<svelte:head>
  <title>Generate Vybe Qube - VybeCoding.ai</title>
  <meta
    name="description"
    content="Generate a profitable AI-powered website using the Vybe Method"
  />
</svelte:head>

<div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12">
  <div class="max-w-4xl mx-auto px-4">
    <!-- Header -->
    <div class="text-center mb-8">
      <h1 class="text-3xl font-bold text-gray-900 mb-4">
        Generate Your Vybe Qube
      </h1>
      <p class="text-lg text-gray-600 mb-6">
        Create a profitable AI-powered website in minutes using our Multi-Agent
        System
      </p>

      <!-- Progress indicator -->
      <div class="flex items-center justify-center gap-4 mb-8">
        {#each Array(totalSteps) as _, i}
          <div class="flex items-center">
            <div
              class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium"
              class:bg-blue-600={i + 1 <= currentStep}
              class:text-white={i + 1 <= currentStep}
              class:bg-gray-200={i + 1 > currentStep}
              class:text-gray-600={i + 1 > currentStep}
            >
              {i + 1 === currentStep
                ? i + 1
                : i + 1 < currentStep
                  ? '✓'
                  : i + 1}
            </div>
            {#if i < totalSteps - 1}
              <div
                class="w-12 h-0.5 mx-2"
                class:bg-blue-600={i + 1 < currentStep}
                class:bg-gray-200={i + 1 >= currentStep}
              ></div>
            {/if}
          </div>
        {/each}
      </div>
    </div>

    {#if error}
      <Card class="p-6 mb-6 border-red-200 bg-red-50">
        <p class="text-red-600">{error}</p>
      </Card>
    {/if}

    <!-- Step 1: Template Selection -->
    {#if currentStep === 1}
      <Card class="p-6">
        <h2 class="text-xl font-semibold mb-6">Choose Your Template</h2>

        <Grid cols="1 md:2" gap="4">
          {#each Object.entries(templates) as [templateId, template]}
            <button
              class="p-6 border-2 rounded-lg text-left transition-all hover:shadow-lg"
              class:border-blue-500={selectedTemplate === templateId}
              class:bg-blue-50={selectedTemplate === templateId}
              class:border-gray-200={selectedTemplate !== templateId}
              onclick={() => selectTemplate(templateId)}
            >
              <div class="flex items-start gap-4">
                <div class="text-3xl">{getTemplateIcon(template.category)}</div>
                <div class="flex-1">
                  <h3 class="font-semibold text-gray-900 mb-2">
                    {template.name}
                  </h3>
                  <p class="text-sm text-gray-600 mb-3">
                    {template.description}
                  </p>

                  <div class="flex items-center gap-2 mb-3">
                    <Badge variant="outline" class="text-xs">
                      {template.category}
                    </Badge>
                    <Badge variant="success" class="text-xs">
                      {template.estimatedRevenue}
                    </Badge>
                  </div>

                  <div class="flex flex-wrap gap-1">
                    {#each template.features.slice(0, 3) as feature}
                      <span
                        class="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded"
                      >
                        {feature}
                      </span>
                    {/each}
                    {#if template.features.length > 3}
                      <span class="text-xs text-gray-500"
                        >+{template.features.length - 3} more</span
                      >
                    {/if}
                  </div>
                </div>
              </div>
            </button>
          {/each}
        </Grid>

        <div class="flex justify-end mt-6">
          <Button onclick={nextStep} disabled={!canProceed}>
            Continue
            <ArrowRight class="w-4 h-4 ml-2" />
          </Button>
        </div>
      </Card>
    {/if}

    <!-- Step 2: Business Details -->
    {#if currentStep === 2}
      <Card class="p-6">
        <h2 class="text-xl font-semibold mb-6">Business Details</h2>

        {#if selectedTemplateData}
          <div class="mb-6 p-4 bg-blue-50 rounded-lg">
            <div class="flex items-center gap-2 mb-2">
              <span class="text-2xl"
                >{getTemplateIcon(selectedTemplateData.category)}</span
              >
              <h3 class="font-medium">{selectedTemplateData.name}</h3>
              <Badge variant="success" class="text-xs"
                >{selectedTemplateData.estimatedRevenue}</Badge
              >
            </div>
            <p class="text-sm text-gray-600">
              {selectedTemplateData.description}
            </p>
          </div>
        {/if}

        <div class="space-y-6">
          <div>
            <label
              for="businessConcept"
              class="block text-sm font-medium text-gray-700 mb-2"
            >
              Business Concept *
            </label>
            <textarea
              id="businessConcept"
              bind:value={businessConcept}
              placeholder="Describe your business idea, what problem it solves, and how it works..."
              class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              rows="4"
            ></textarea>
          </div>

          <div>
            <label
              for="targetAudience"
              class="block text-sm font-medium text-gray-700 mb-2"
            >
              Target Audience *
            </label>
            <Input
              id="targetAudience"
              bind:value={targetAudience}
              placeholder="Who are your ideal customers? (e.g., small business owners, students, professionals)"
            />
          </div>

          <div>
            <label
              for="companyName"
              class="block text-sm font-medium text-gray-700 mb-2"
            >
              Company Name
            </label>
            <Input
              id="companyName"
              bind:value={customization.branding.companyName}
              placeholder="Your company or brand name"
            />
          </div>
        </div>

        <div class="flex justify-between mt-6">
          <Button variant="outline" onclick={prevStep}>Back</Button>
          <Button onclick={nextStep} disabled={!canProceed}>
            Continue
            <ArrowRight class="w-4 h-4 ml-2" />
          </Button>
        </div>
      </Card>
    {/if}

    <!-- Step 3: Revenue Model -->
    {#if currentStep === 3}
      <Card class="p-6">
        <h2 class="text-xl font-semibold mb-6">Revenue Model</h2>

        <div class="space-y-6">
          <div>
            <label
              for="revenueModel"
              class="block text-sm font-medium text-gray-700 mb-2"
            >
              How will your website make money? *
            </label>
            <textarea
              id="revenueModel"
              bind:value={revenueModel}
              placeholder="Describe your monetization strategy (e.g., product sales, subscriptions, advertising, commissions)..."
              class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              rows="4"
            ></textarea>
          </div>

          <div>
            <span class="block text-sm font-medium text-gray-700 mb-2">
              Brand Colors
            </span>
            <div class="flex gap-4">
              <div>
                <label
                  for="primaryColor"
                  class="block text-xs text-gray-500 mb-1">Primary Color</label
                >
                <input
                  id="primaryColor"
                  type="color"
                  bind:value={customization.branding.primaryColor}
                  class="w-16 h-10 border border-gray-300 rounded cursor-pointer"
                />
              </div>
              <div>
                <label
                  for="secondaryColor"
                  class="block text-xs text-gray-500 mb-1"
                  >Secondary Color</label
                >
                <input
                  id="secondaryColor"
                  type="color"
                  bind:value={customization.branding.secondaryColor}
                  class="w-16 h-10 border border-gray-300 rounded cursor-pointer"
                />
              </div>
            </div>
          </div>
        </div>

        <div class="flex justify-between mt-6">
          <Button variant="outline" onclick={prevStep}>Back</Button>
          <Button onclick={nextStep} disabled={!canProceed}>
            Continue
            <ArrowRight class="w-4 h-4 ml-2" />
          </Button>
        </div>
      </Card>
    {/if}

    <!-- Step 4: Review & Generate -->
    {#if currentStep === 4}
      <Card class="p-6">
        <h2 class="text-xl font-semibold mb-6">Review & Generate</h2>

        <div class="space-y-6 mb-8">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 class="font-medium text-gray-900 mb-2">Template</h3>
              <div class="flex items-center gap-2">
                <span class="text-xl"
                  >{getTemplateIcon(selectedTemplateData?.category || '')}</span
                >
                <span>{selectedTemplateData?.name}</span>
                <Badge variant="success" class="text-xs"
                  >{selectedTemplateData?.estimatedRevenue}</Badge
                >
              </div>
            </div>

            <div>
              <h3 class="font-medium text-gray-900 mb-2">Company</h3>
              <p class="text-gray-600">
                {customization.branding.companyName || 'Not specified'}
              </p>
            </div>
          </div>

          <div>
            <h3 class="font-medium text-gray-900 mb-2">Business Concept</h3>
            <p class="text-gray-600 text-sm">{businessConcept}</p>
          </div>

          <div>
            <h3 class="font-medium text-gray-900 mb-2">Target Audience</h3>
            <p class="text-gray-600 text-sm">{targetAudience}</p>
          </div>

          <div>
            <h3 class="font-medium text-gray-900 mb-2">Revenue Model</h3>
            <p class="text-gray-600 text-sm">{revenueModel}</p>
          </div>
        </div>

        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
          <div class="flex items-start gap-3">
            <Clock class="w-5 h-5 text-yellow-600 mt-0.5" />
            <div>
              <h4 class="font-medium text-yellow-800">Generation Time</h4>
              <p class="text-sm text-yellow-700">
                Your Vybe Qube will be generated using our Multi-Agent System.
                This typically takes 15-20 minutes to complete.
              </p>
            </div>
          </div>
        </div>

        <div class="flex justify-between">
          <Button variant="outline" onclick={prevStep} disabled={generating}>
            Back
          </Button>
          <Button
            onclick={generateVybeQube}
            disabled={generating || !canProceed}
          >
            {#if generating}
              <div
                class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"
              ></div>
              Generating...
            {:else}
              <Sparkles class="w-4 h-4 mr-2" />
              Generate Vybe Qube
            {/if}
          </Button>
        </div>
      </Card>
    {/if}
  </div>
</div>
