<script>
  import { page } from '$app/stores';
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';

  let course = null;
  let lessons = [];
  let loading = true;
  let error = null;

  $: courseId = $page.params.id;

  onMount(async () => {
    try {
      // Try to load course data from static files
      const courseResponse = await fetch(`/courses/${courseId}/course.json`);

      if (courseResponse.ok) {
        course = await courseResponse.json();

        // Load lessons
        if (course.lessons) {
          const lessonPromises = course.lessons.map(async lessonId => {
            try {
              const lessonResponse = await fetch(
                `/courses/${courseId}/${lessonId}.json`
              );
              if (lessonResponse.ok) {
                return await lessonResponse.json();
              }
            } catch (err) {
              console.warn(`Failed to load lesson ${lessonId}:`, err);
            }
            return null;
          });

          const loadedLessons = await Promise.all(lessonPromises);
          lessons = loadedLessons.filter(lesson => lesson !== null);
        }
      } else {
        // Fallback sample data
        if (courseId === 'vybe-method-intro') {
          course = {
            id: 'vybe-method-intro',
            title: 'Introduction to the Vybe Method',
            description:
              'Learn the revolutionary Vybe Method that combines BMAD methodology with Multi-Agent Systems for AI-powered development.',
            difficulty: 'beginner',
            category: 'Methodology',
            estimatedDuration: 120,
            price: 0,
            isPublished: true,
            thumbnailUrl: '/images/courses/vybe-method-intro.svg',
          };

          lessons = [
            {
              id: 'lesson-1',
              title: 'What is the Vybe Method?',
              order: 1,
              estimatedDuration: 30,
              isPublished: true,
            },
            {
              id: 'lesson-2',
              title: 'BMAD Method Fundamentals',
              order: 2,
              estimatedDuration: 45,
              isPublished: true,
            },
            {
              id: 'lesson-3',
              title: 'Multi-Agent Systems Introduction',
              order: 3,
              estimatedDuration: 45,
              isPublished: true,
            },
          ];
        } else {
          throw new Error('Course not found');
        }
      }
    } catch (err) {
      console.error('Error loading course:', err);
      error = 'Course not found';
    } finally {
      loading = false;
    }
  });

  function formatPrice(price) {
    if (price === 0) return 'Free';
    return `$${(price / 100).toFixed(2)}`;
  }

  function formatDuration(minutes) {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`;
    }
    return `${mins}m`;
  }

  function getDifficultyColor(difficulty) {
    const colors = {
      beginner: 'bg-green-100 text-green-800',
      intermediate: 'bg-yellow-100 text-yellow-800',
      advanced: 'bg-orange-100 text-orange-800',
      masterclass: 'bg-red-100 text-red-800',
    };
    return colors[difficulty] || 'bg-gray-100 text-gray-800';
  }

  function startCourse() {
    if (course.price === 0) {
      // Free course - go directly to first lesson
      goto(`/courses/${courseId}/lessons/${lessons[0]?.id || 'lesson-1'}`);
    } else {
      // Paid course - go to pricing/purchase
      goto('/pricing');
    }
  }

  function viewLesson(lessonId) {
    goto(`/courses/${courseId}/lessons/${lessonId}`);
  }
</script>

<svelte:head>
  <title>{course?.title || 'Course'} - VybeCoding.ai</title>
  <meta
    name="description"
    content={course?.description ||
      'Learn AI-powered development with VybeCoding.ai'}
  />
</svelte:head>

<div class="min-h-screen bg-gradient-mesh relative overflow-hidden">
  <!-- Animated Background Elements -->
  <div
    class="absolute inset-0 bg-gradient-to-br from-blue-900/20 via-purple-900/20 to-pink-900/20"
  ></div>
  <div
    class="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-blue-500/30 to-purple-500/30 rounded-full blur-3xl animate-pulse"
  ></div>
  <div
    class="absolute bottom-0 right-0 w-96 h-96 bg-gradient-to-br from-purple-500/30 to-pink-500/30 rounded-full blur-3xl animate-pulse delay-1000"
  ></div>
  <div
    class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-br from-cyan-500/20 to-blue-500/20 rounded-full blur-2xl animate-bounce"
  ></div>

  <div class="relative z-10">
    {#if loading}
      <div class="flex justify-center items-center py-24">
        <div
          class="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600"
        ></div>
      </div>
    {:else if error}
      <div class="max-w-4xl mx-auto px-4 py-24 text-center">
        <div
          class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4"
        >
          {error}
        </div>
        <button
          onclick={() => goto('/courses')}
          class="bg-purple-600 text-white px-6 py-2 rounded hover:bg-purple-700"
        >
          Back to Courses
        </button>
      </div>
    {:else if course}
      <!-- Course Header -->
      <div class="glass-card-enhanced mx-4 mt-8 mb-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div class="flex items-center mb-4">
            <button
              onclick={() => goto('/courses')}
              class="text-cyan-400 hover:text-cyan-300 mr-4 transition-colors"
            >
              ← Back to Courses
            </button>
            <span
              class="bg-white text-gray-800 px-3 py-1 text-sm font-semibold rounded-full shadow-lg"
            >
              {course.difficulty}
            </span>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Course Info -->
            <div class="lg:col-span-2">
              <h1 class="text-4xl font-bold text-white mb-4">
                {course.title}
              </h1>

              <p class="text-xl text-white/80 mb-6">
                {course.description}
              </p>

              <!-- Course Stats -->
              <div class="flex flex-wrap gap-6 text-sm text-white/70 mb-6">
                <div class="flex items-center">
                  <svg
                    class="w-5 h-5 mr-2"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                  {formatDuration(course.estimatedDuration)}
                </div>
                <div class="flex items-center">
                  <svg
                    class="w-5 h-5 mr-2"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                    ></path>
                  </svg>
                  {lessons.length} lessons
                </div>
                <div class="flex items-center">
                  <svg
                    class="w-5 h-5 mr-2"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z"
                    ></path>
                  </svg>
                  {course.category}
                </div>
              </div>
            </div>

            <!-- Course Action Card -->
            <div class="lg:col-span-1">
              <div
                class="tech-card-course p-6 sticky top-8 hover:transform hover:scale-[1.02] transition-all duration-300"
              >
                <!-- Course Header -->
                <div
                  class="h-24 bg-gradient-to-br from-gray-900 via-slate-900 to-black relative flex items-center justify-center overflow-hidden border-b border-gray-700 mb-6 rounded-lg"
                >
                  <div class="absolute inset-0 tech-matrix opacity-30"></div>
                  <div class="text-center z-10">
                    <div class="text-3xl font-bold text-white mb-1 font-mono">
                      {formatPrice(course.price)}
                    </div>
                    {#if course.price === 0}
                      <p class="text-green-400 font-medium text-sm">
                        Free Course
                      </p>
                    {:else}
                      <p class="text-white/70 text-sm">One-time purchase</p>
                    {/if}
                  </div>
                </div>

                <button
                  onclick={startCourse}
                  class="w-full bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 text-white py-3 px-4 rounded-lg font-semibold mb-6 transition-all duration-200 border border-blue-500/30 font-mono"
                >
                  {course.price === 0 ? 'Start Free Course' : 'Purchase Course'}
                </button>

                <div class="text-sm text-gray-700 space-y-3">
                  <div class="flex items-center">
                    <svg
                      class="w-4 h-4 mr-3 text-green-500"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                    Lifetime access
                  </div>
                  <div class="flex items-center">
                    <svg
                      class="w-4 h-4 mr-3 text-green-500"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                    AI-powered assistance
                  </div>
                  <div class="flex items-center">
                    <svg
                      class="w-4 h-4 mr-3 text-green-500"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                    Progress tracking
                  </div>
                  <div class="flex items-center">
                    <svg
                      class="w-4 h-4 mr-3 text-green-500"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                    Community support
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Course Content -->
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <!-- Lessons List -->
          <div class="lg:col-span-2">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">
              Course Content
            </h2>

            <div class="space-y-4">
              {#each lessons as lesson, index}
                <div
                  class="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                >
                  <div class="flex items-center justify-between">
                    <div class="flex-1">
                      <div class="flex items-center mb-2">
                        <span
                          class="bg-purple-100 text-purple-800 text-sm font-medium px-2 py-1 rounded mr-3"
                        >
                          {index + 1}
                        </span>
                        <h3 class="text-lg font-semibold text-gray-900">
                          {lesson.title}
                        </h3>
                      </div>
                      <div class="text-sm text-gray-600">
                        {formatDuration(lesson.estimatedDuration)}
                      </div>
                    </div>

                    {#if course.price === 0}
                      <button
                        onclick={() => viewLesson(lesson.id)}
                        class="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 transition-colors"
                      >
                        Start
                      </button>
                    {:else}
                      <svg
                        class="w-5 h-5 text-gray-400"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z"
                          clip-rule="evenodd"
                        ></path>
                      </svg>
                    {/if}
                  </div>
                </div>
              {/each}
            </div>
          </div>

          <!-- Course Info Sidebar -->
          <div class="lg:col-span-1">
            <div class="bg-white border border-gray-200 rounded-xl p-6">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">
                What You'll Learn
              </h3>
              <ul class="space-y-3 text-sm text-gray-600">
                <li class="flex items-start">
                  <svg
                    class="w-4 h-4 mr-2 mt-0.5 text-green-500"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                  Core principles of the Vybe Method
                </li>
                <li class="flex items-start">
                  <svg
                    class="w-4 h-4 mr-2 mt-0.5 text-green-500"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                  AI-powered development workflows
                </li>
                <li class="flex items-start">
                  <svg
                    class="w-4 h-4 mr-2 mt-0.5 text-green-500"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                  Multi-Agent Systems integration
                </li>
                <li class="flex items-start">
                  <svg
                    class="w-4 h-4 mr-2 mt-0.5 text-green-500"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                  Building profitable applications
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    {/if}
  </div>
</div>

<style>
  /* Gradient Mesh Background */
  .bg-gradient-mesh {
    background:
      radial-gradient(
        circle at 20% 80%,
        rgba(120, 119, 198, 0.3) 0%,
        transparent 50%
      ),
      radial-gradient(
        circle at 80% 20%,
        rgba(255, 119, 198, 0.3) 0%,
        transparent 50%
      ),
      radial-gradient(
        circle at 40% 40%,
        rgba(120, 200, 255, 0.3) 0%,
        transparent 50%
      ),
      linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
    min-height: 100vh;
  }

  /* Enhanced Glassmorphism Components */
  .glass-card-enhanced {
    background: rgba(15, 23, 42, 0.8);
    backdrop-filter: blur(20px);
    border: 2px solid transparent;
    border-radius: 28px;
    background-clip: padding-box;
    position: relative;
    box-shadow:
      0 25px 50px rgba(0, 0, 0, 0.5),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  .glass-card-enhanced::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: 2px;
    background: linear-gradient(
      135deg,
      rgba(6, 182, 212, 0.5) 0%,
      rgba(139, 92, 246, 0.5) 50%,
      rgba(236, 72, 153, 0.5) 100%
    );
    border-radius: inherit;
    mask:
      linear-gradient(#fff 0 0) content-box,
      linear-gradient(#fff 0 0);
    mask-composite: xor;
    -webkit-mask-composite: xor;
  }

  /* Tech Course Card */
  .tech-card-course {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 12px;
    box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
  }

  .tech-card-course:hover {
    border-color: rgba(59, 130, 246, 0.4);
    box-shadow:
      0 25px 50px rgba(0, 0, 0, 0.15),
      0 0 20px rgba(59, 130, 246, 0.1);
  }

  /* Matrix Grid Pattern */
  .tech-matrix {
    background-image:
      linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
    background-size: 16px 16px;
    animation: matrix-move 20s linear infinite;
  }

  @keyframes matrix-move {
    0% {
      background-position: 0 0;
    }
    100% {
      background-position: 16px 16px;
    }
  }
</style>
