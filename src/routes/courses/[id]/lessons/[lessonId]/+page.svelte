<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import {
    ArrowLeft,
    ArrowRight,
    Play,
    Pause,
    RotateCcw,
    CheckCircle,
    Clock,
    BookOpen,
    Lightbulb,
    Target,
    Zap,
    Trophy,
    Star,
  } from 'lucide-svelte';

  let course = $state<any>(null);
  let lesson = $state<any>(null);
  let loading = $state(true);
  let error = $state('');
  let courseId = $state('');
  let lessonId = $state('');
  let progress = $state(0);
  let isPlaying = $state(false);
  let readingTime = $state(0);
  let completedSections = $state<string[]>([]);
  let currentSection = $state(0);

  onMount(async () => {
    try {
      // Extract parameters from URL
      const pathParts = window.location.pathname.split('/');
      courseId = pathParts[2] || ''; // /courses/[id]/lessons/[lessonId]
      lessonId = pathParts[4] || '';

      // Load lesson data from course API
      if (courseId === 'vybe-method-intro') {
        course = {
          id: 'vybe-method-intro',
          title: 'Introduction to the Vybe Method',
        };

        const lessons = {
          'lesson-1': {
            id: 'lesson-1',
            title: 'What is the Vybe Method?',
            content: `
              <h2>Welcome to the Vybe Method</h2>
              <p>The Vybe Method is a revolutionary approach to AI-powered development that combines:</p>
              <ul>
                <li><strong>BMAD Method V3:</strong> Proven development methodology</li>
                <li><strong>Multi-Agent Systems:</strong> Collaborative AI agents</li>
                <li><strong>Educational Excellence:</strong> Structured learning paths</li>
              </ul>
              
              <h3>Key Principles</h3>
              <p>The Vybe Method is built on these core principles:</p>
              <ol>
                <li><strong>Planning Prevents Failures:</strong> Thorough upfront planning eliminates 80% of development problems</li>
                <li><strong>Agent Specialization:</strong> Each AI agent has specific roles and expertise</li>
                <li><strong>Document-Driven Development:</strong> Artifacts pass context between agents and phases</li>
                <li><strong>Iterative Refinement:</strong> Build, test, learn, adapt</li>
              </ol>

              <h3>What You'll Learn</h3>
              <p>In this course, you'll master:</p>
              <ul>
                <li>Core Vybe Method principles and workflows</li>
                <li>How to work with Multi-Agent Systems</li>
                <li>Building profitable AI applications</li>
                <li>Educational best practices for AI development</li>
              </ul>
            `,
            duration: 30,
            order: 1,
          },
          'lesson-2': {
            id: 'lesson-2',
            title: 'BMAD Method Fundamentals',
            content: `
              <h2>Understanding BMAD Method V3</h2>
              <p>BMAD Method V3 is the foundation of the Vybe Method, providing a structured approach to development with specialized agents:</p>
              
              <h3>The 7 BMAD Agents</h3>
              <ol>
                <li><strong>Mary (Analyst):</strong> Project analysis and research</li>
                <li><strong>John (Product Manager):</strong> Requirements and PRD creation</li>
                <li><strong>Alex (Architect):</strong> Technical architecture design</li>
                <li><strong>Maya (Designer):</strong> UI/UX specifications</li>
                <li><strong>Sarah (Product Owner):</strong> Quality validation and alignment</li>
                <li><strong>Bob (Scrum Master):</strong> Story generation and planning</li>
                <li><strong>Larry (Developer):</strong> Feature implementation</li>
              </ol>

              <h3>BMAD Workflow</h3>
              <p>The proven sequence ensures quality and efficiency:</p>
              <ol>
                <li><strong>Analysis Phase:</strong> Mary researches and creates project brief</li>
                <li><strong>Planning Phase:</strong> John creates comprehensive PRD</li>
                <li><strong>Architecture Phase:</strong> Alex designs technical architecture</li>
                <li><strong>Design Phase:</strong> Maya creates UI/UX specifications</li>
                <li><strong>Validation Phase:</strong> Sarah validates all documents align</li>
                <li><strong>Story Generation:</strong> Bob generates user stories</li>
                <li><strong>Development:</strong> Larry implements stories with tests</li>
              </ol>
            `,
            duration: 45,
            order: 2,
          },
          'lesson-3': {
            id: 'lesson-3',
            title: 'Multi-Agent Systems Introduction',
            content: `
              <h2>Multi-Agent Systems (MAS) in the Vybe Method</h2>
              <p>Multi-Agent Systems represent the next evolution of the BMAD Method, enabling autonomous collaboration between AI agents.</p>

              <h3>What are Multi-Agent Systems?</h3>
              <p>MAS are networks of autonomous AI agents that can:</p>
              <ul>
                <li><strong>Collaborate:</strong> Work together on complex tasks</li>
                <li><strong>Communicate:</strong> Share information and context</li>
                <li><strong>Coordinate:</strong> Synchronize their activities</li>
                <li><strong>Consensus:</strong> Reach agreements on decisions</li>
              </ul>

              <h3>MAS in VybeCoding.ai</h3>
              <p>Our platform uses MAS for:</p>
              <ul>
                <li><strong>Autonomous Development:</strong> Agents can build applications independently</li>
                <li><strong>Quality Assurance:</strong> Multi-layer validation and consensus</li>
                <li><strong>Educational Optimization:</strong> Personalized learning experiences</li>
                <li><strong>Real-time Collaboration:</strong> Instant agent coordination</li>
              </ul>

              <h3>Getting Started with MAS</h3>
              <p>To access the MAS Control panel:</p>
              <ol>
                <li>Complete this course</li>
                <li>Sign up for a VybeCoding.ai account</li>
                <li>Navigate to the MAS Control panel</li>
                <li>Start collaborating with AI agents!</li>
              </ol>

              <p><strong>Congratulations!</strong> You've completed the Introduction to the Vybe Method course. You're now ready to start building with AI agents!</p>
            `,
            duration: 45,
            order: 3,
          },
        };

        lesson = lessons[lessonId as keyof typeof lessons];

        if (!lesson) {
          throw new Error('Lesson not found');
        }
      } else {
        throw new Error('Course not found');
      }
    } catch (err: any) {
      console.error('Error loading lesson:', err);
      error = err.message || 'Failed to load lesson';
    } finally {
      loading = false;
    }
  });

  // REAL learning progress tracking with Appwrite database
  let progressInterval: ReturnType<typeof setInterval> | null = null;
  let startTime: number | null = null;

  async function startReading() {
    if (isPlaying) return;

    isPlaying = true;
    startTime = Date.now();

    // Save reading session start to database
    try {
      await fetch('/api/courses/progress/start', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          courseId,
          lessonId: lesson.id,
          userId: 'demo-user', // In real app, get from auth
          startTime: new Date().toISOString()
        })
      });
    } catch (error) {
      console.error('Failed to save reading start:', error);
    }

    // Real-time progress tracking based on actual reading behavior
    progressInterval = setInterval(async () => {
      if (!isPlaying || !startTime) return;

      const currentTime = Date.now();
      const elapsedSeconds = Math.floor((currentTime - startTime) / 1000);
      readingTime = elapsedSeconds;

      // Calculate progress based on estimated reading time
      const estimatedReadingTime = lesson.duration * 60; // Convert minutes to seconds
      progress = Math.min(100, (readingTime / estimatedReadingTime) * 100);

      // Save progress every 30 seconds
      if (readingTime % 30 === 0) {
        await saveProgressToDatabase();
      }

      // Auto-complete when reaching estimated reading time
      if (progress >= 100) {
        await completeLesson();
      }
    }, 1000);
  }

  async function pauseReading() {
    isPlaying = false;
    if (progressInterval) {
      clearInterval(progressInterval);
      progressInterval = null;
    }

    // Save current progress when pausing
    await saveProgressToDatabase();
  }

  async function saveProgressToDatabase() {
    try {
      await fetch('/api/courses/progress/update', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          courseId,
          lessonId: lesson.id,
          userId: 'demo-user', // In real app, get from auth
          progress: Math.round(progress),
          readingTime,
          completedSections,
          timestamp: new Date().toISOString()
        })
      });
    } catch (error) {
      console.error('Failed to save progress:', error);
    }
  }

  async function resetProgress() {
    progress = 0;
    readingTime = 0;
    isPlaying = false;
    completedSections = [];
    currentSection = 0;
    startTime = null;

    if (progressInterval) {
      clearInterval(progressInterval);
      progressInterval = null;
    }

    // Reset progress in database
    try {
      await fetch('/api/courses/progress/reset', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          courseId,
          lessonId: lesson.id,
          userId: 'demo-user' // In real app, get from auth
        })
      });
    } catch (error) {
      console.error('Failed to reset progress:', error);
    }
  }

  async function markSectionComplete(sectionId: string) {
    if (!completedSections.includes(sectionId)) {
      completedSections = [...completedSections, sectionId];

      // Save section completion to database
      try {
        await fetch('/api/courses/sections/complete', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            courseId,
            lessonId: lesson.id,
            sectionId,
            userId: 'demo-user', // In real app, get from auth
            completedAt: new Date().toISOString()
          })
        });
      } catch (error) {
        console.error('Failed to save section completion:', error);
      }
    }
  }

  async function completeLesson() {
    isPlaying = false;
    if (progressInterval) {
      clearInterval(progressInterval);
      progressInterval = null;
    }

    try {
      const response = await fetch('/api/courses/lessons/complete', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          courseId,
          lessonId: lesson.id,
          userId: 'demo-user', // In real app, get from auth
          completedAt: new Date().toISOString(),
          finalProgress: 100,
          totalReadingTime: readingTime
        })
      });

      if (response.ok) {
        const result = await response.json();
        alert(`🎉 Lesson completed! You earned ${result.pointsEarned} points!`);

        // Check if this completes the course
        if (result.courseCompleted) {
          await completeCourse();
        }
      }
    } catch (error) {
      console.error('Failed to complete lesson:', error);
      alert('Lesson completed locally, but failed to save to server.');
    }
  }

  function goBack() {
    // Save progress before leaving
    if (isPlaying) {
      pauseReading();
    }
    goto(`/courses/${courseId}`);
  }

  function nextLesson() {
    // Save progress before moving to next lesson
    if (isPlaying) {
      pauseReading();
    }
    const nextLessonId = `lesson-${lesson.order + 1}`;
    goto(`/courses/${courseId}/lessons/${nextLessonId}`);
  }

  async function completeCourse() {
    try {
      const response = await fetch('/api/courses/complete', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          courseId,
          userId: 'demo-user', // In real app, get from auth
          completedAt: new Date().toISOString()
        })
      });

      if (response.ok) {
        const result = await response.json();
        alert(
          `🎉 Congratulations! You've completed the course and earned ${result.totalPoints} points! ` +
          `You now have access to: ${result.unlockedFeatures.join(', ')}`
        );

        // Redirect to unlocked features or dashboard
        goto(result.redirectUrl || '/courses');
      }
    } catch (error) {
      console.error('Failed to complete course:', error);
      alert('Course completed locally, but failed to save to server.');
      goto('/courses');
    }
  }
</script>

<svelte:head>
  <title
    >{lesson?.title || 'Lesson'} - {course?.title || 'Course'} - VybeCoding.ai</title
  >
  <meta
    name="description"
    content="Learn AI development with the Vybe Method"
  />
</svelte:head>

<!-- Revolutionary Learning Interface -->
<div class="min-h-screen bg-gradient-mesh relative overflow-hidden">
  <!-- Animated Background Elements -->
  <div
    class="absolute inset-0 bg-gradient-to-br from-blue-900/20 via-purple-900/20 to-pink-900/20"
  ></div>
  <div
    class="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-blue-500/30 to-purple-500/30 rounded-full blur-3xl animate-pulse"
  ></div>
  <div
    class="absolute bottom-0 right-0 w-96 h-96 bg-gradient-to-br from-purple-500/30 to-pink-500/30 rounded-full blur-3xl animate-pulse delay-1000"
  ></div>
  <div
    class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-br from-cyan-500/20 to-blue-500/20 rounded-full blur-2xl animate-bounce"
  ></div>

  {#if loading}
    <!-- Futuristic Loading Animation -->
    <div class="flex justify-center items-center min-h-screen">
      <div class="relative">
        <div
          class="w-24 h-24 border-4 border-transparent border-t-blue-500 border-r-purple-500 border-b-pink-500 border-l-cyan-500 rounded-full animate-spin"
        ></div>
        <div
          class="absolute inset-0 w-24 h-24 border-4 border-transparent border-t-purple-500 border-r-pink-500 border-b-cyan-500 border-l-blue-500 rounded-full animate-spin animate-reverse"
        ></div>
        <div
          class="absolute inset-2 w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full animate-pulse"
        ></div>
        <div class="absolute inset-0 flex items-center justify-center">
          <Zap class="w-8 h-8 text-white animate-bounce" />
        </div>
      </div>
    </div>
  {:else if error}
    <!-- Glassmorphic Error Display -->
    <div class="min-h-screen flex items-center justify-center p-4">
      <div class="glass-card max-w-md w-full p-8 text-center">
        <div
          class="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4"
        >
          <Target class="w-8 h-8 text-red-400" />
        </div>
        <h3 class="text-xl font-bold text-white mb-2">
          Oops! Something went wrong
        </h3>
        <p class="text-gray-300 mb-6">{error}</p>
        <button onclick={goBack} class="btn-primary w-full">
          <ArrowLeft class="w-4 h-4 mr-2" />
          Back to Course
        </button>
      </div>
    </div>
  {:else if lesson}
    <!-- Immersive Lesson Header -->
    <div class="relative z-10">
      <!-- Floating Navigation Bar -->
      <div class="glass-nav fixed top-20 left-4 right-4 z-40 mx-auto max-w-6xl">
        <div class="flex items-center justify-between p-4">
          <button onclick={goBack} class="btn-ghost group">
            <ArrowLeft
              class="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform"
            />
            Back to Course
          </button>

          <!-- Progress Indicator -->
          <div class="flex items-center gap-4">
            <div class="flex items-center gap-2 text-white/80">
              <Clock class="w-4 h-4" />
              <span class="text-sm"
                >{Math.floor(readingTime / 60)}:{(readingTime % 60)
                  .toString()
                  .padStart(2, '0')}</span
              >
            </div>
            <div class="w-32 h-2 bg-white/20 rounded-full overflow-hidden">
              <div
                class="h-full bg-gradient-to-r from-blue-400 to-purple-400 transition-all duration-300 ease-out"
                style="width: {progress}%"
              ></div>
            </div>
            <span class="text-sm text-white/80">{Math.round(progress)}%</span>
          </div>

          <!-- Lesson Info -->
          <div class="text-right">
            <div class="text-sm text-white/60">Lesson {lesson.order}</div>
            <div class="text-xs text-white/40">{lesson.duration} min</div>
          </div>
        </div>
      </div>

      <!-- Hero Section -->
      <div class="pt-32 pb-12 px-4">
        <div class="max-w-4xl mx-auto text-center">
          <!-- Lesson Badge -->
          <div class="inline-flex items-center gap-2 glass-badge mb-6">
            <BookOpen class="w-4 h-4 text-blue-400" />
            <span class="text-white/80">Lesson {lesson.order}</span>
            <div class="w-1 h-1 bg-white/40 rounded-full"></div>
            <span class="text-white/60">{lesson.duration} minutes</span>
          </div>

          <!-- Lesson Title -->
          <h1 class="text-5xl md:text-6xl font-black mb-6 leading-tight">
            <span class="gradient-text-hero">{lesson.title}</span>
          </h1>

          <!-- Course Context -->
          <p class="text-xl text-white/70 mb-8 max-w-2xl mx-auto">
            {course.title}
          </p>

          <!-- Interactive Controls -->
          <div class="flex items-center justify-center gap-4 mb-8">
            <button
              onclick={isPlaying ? pauseReading : startReading}
              class="btn-primary-large group"
            >
              {#if isPlaying}
                <Pause
                  class="w-6 h-6 mr-3 group-hover:scale-110 transition-transform"
                />
                Pause Reading
              {:else}
                <Play
                  class="w-6 h-6 mr-3 group-hover:scale-110 transition-transform"
                />
                Start Reading
              {/if}
            </button>

            <button onclick={resetProgress} class="btn-secondary group">
              <RotateCcw
                class="w-5 h-5 mr-2 group-hover:rotate-180 transition-transform duration-500"
              />
              Reset
            </button>
          </div>

          <!-- Achievement Indicators -->
          <div class="flex items-center justify-center gap-6">
            <div class="flex items-center gap-2 text-white/60">
              <Star
                class="w-4 h-4 {completedSections.length > 0
                  ? 'text-yellow-400'
                  : 'text-white/30'}"
              />
              <span class="text-sm"
                >{completedSections.length} sections completed</span
              >
            </div>
            <div class="flex items-center gap-2 text-white/60">
              <Trophy
                class="w-4 h-4 {progress >= 100
                  ? 'text-yellow-400'
                  : 'text-white/30'}"
              />
              <span class="text-sm"
                >{progress >= 100 ? 'Lesson Complete!' : 'In Progress'}</span
              >
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Revolutionary Content Section -->
    <div class="relative z-10 max-w-5xl mx-auto px-4 pb-20">
      <!-- Main Content Card -->
      <div class="glass-content-card p-8 md:p-12">
        <!-- Content Header -->
        <div class="flex items-center gap-4 mb-8">
          <div
            class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center"
          >
            <Lightbulb class="w-6 h-6 text-white" />
          </div>
          <div>
            <h2 class="text-2xl font-bold text-white">Learning Content</h2>
            <p class="text-white/60">Interactive lesson material</p>
          </div>
        </div>

        <!-- Enhanced Content Display -->
        <div class="prose prose-lg prose-invert max-w-none">
          <div class="content-sections">
            {@html lesson.content}
          </div>
        </div>

        <!-- Interactive Elements -->
        <div class="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- Key Concepts -->
          <div class="glass-mini-card p-6">
            <div class="flex items-center gap-3 mb-4">
              <Target class="w-5 h-5 text-blue-400" />
              <h3 class="font-semibold text-white">Key Concepts</h3>
            </div>
            <div class="space-y-2">
              <div class="flex items-center gap-2">
                <CheckCircle class="w-4 h-4 text-green-400" />
                <span class="text-sm text-white/80">BMAD Method V3</span>
              </div>
              <div class="flex items-center gap-2">
                <CheckCircle class="w-4 h-4 text-green-400" />
                <span class="text-sm text-white/80">Multi-Agent Systems</span>
              </div>
              <div class="flex items-center gap-2">
                <CheckCircle class="w-4 h-4 text-green-400" />
                <span class="text-sm text-white/80">AI Development</span>
              </div>
            </div>
          </div>

          <!-- Progress Stats -->
          <div class="glass-mini-card p-6">
            <div class="flex items-center gap-3 mb-4">
              <Trophy class="w-5 h-5 text-yellow-400" />
              <h3 class="font-semibold text-white">Your Progress</h3>
            </div>
            <div class="space-y-3">
              <div>
                <div class="flex justify-between text-sm mb-1">
                  <span class="text-white/80">Reading Progress</span>
                  <span class="text-white/60">{Math.round(progress)}%</span>
                </div>
                <div
                  class="w-full h-2 bg-white/20 rounded-full overflow-hidden"
                >
                  <div
                    class="h-full bg-gradient-to-r from-green-400 to-blue-400 transition-all duration-300"
                    style="width: {progress}%"
                  ></div>
                </div>
              </div>
              <div class="text-xs text-white/60">
                Time spent: {Math.floor(readingTime / 60)}m {readingTime % 60}s
              </div>
            </div>
          </div>

          <!-- Next Steps -->
          <div class="glass-mini-card p-6">
            <div class="flex items-center gap-3 mb-4">
              <ArrowRight class="w-5 h-5 text-purple-400" />
              <h3 class="font-semibold text-white">Next Steps</h3>
            </div>
            <div class="space-y-2">
              {#if lesson.order < 3}
                <button
                  onclick={nextLesson}
                  class="w-full text-left p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors text-sm text-white/80"
                >
                  Continue to Lesson {lesson.order + 1}
                </button>
              {:else}
                <button
                  onclick={completeCourse}
                  class="w-full text-left p-2 rounded-lg bg-gradient-to-r from-green-500/20 to-blue-500/20 hover:from-green-500/30 hover:to-blue-500/30 transition-colors text-sm text-white/80"
                >
                  🎉 Complete Course
                </button>
              {/if}
              <button
                onclick={() => markSectionComplete('main-content')}
                class="w-full text-left p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors text-sm text-white/80"
              >
                Mark as Complete
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Floating Action Buttons -->
      <div class="fixed bottom-8 right-8 flex flex-col gap-4 z-50">
        <button onclick={goBack} class="btn-floating" title="Back to Course">
          <ArrowLeft class="w-5 h-5" />
        </button>

        {#if lesson.order < 3}
          <button
            onclick={nextLesson}
            class="btn-floating-primary"
            title="Next Lesson"
          >
            <ArrowRight class="w-5 h-5" />
          </button>
        {:else}
          <button
            onclick={completeCourse}
            class="btn-floating-success"
            title="Complete Course"
          >
            <Trophy class="w-5 h-5" />
          </button>
        {/if}
      </div>
    </div>
  {/if}
</div>

<style>
  /* Revolutionary Learning Interface Styles */

  /* Gradient Mesh Background */
  .bg-gradient-mesh {
    background:
      radial-gradient(
        circle at 20% 80%,
        rgba(120, 119, 198, 0.3) 0%,
        transparent 50%
      ),
      radial-gradient(
        circle at 80% 20%,
        rgba(255, 119, 198, 0.3) 0%,
        transparent 50%
      ),
      radial-gradient(
        circle at 40% 40%,
        rgba(120, 200, 255, 0.3) 0%,
        transparent 50%
      ),
      linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
    min-height: 100vh;
  }

  /* Glassmorphism Components */
  .glass-nav {
    background: rgba(15, 23, 42, 0.8);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .glass-card {
    background: rgba(15, 23, 42, 0.7);
    backdrop-filter: blur(16px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 24px;
    box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .glass-content-card {
    background: rgba(15, 23, 42, 0.6);
    backdrop-filter: blur(24px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 32px;
    box-shadow:
      0 32px 64px rgba(0, 0, 0, 0.5),
      inset 0 1px 0 rgba(255, 255, 255, 0.1),
      0 0 0 1px rgba(255, 255, 255, 0.05);
    position: relative;
    overflow: hidden;
  }

  .glass-content-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.3),
      transparent
    );
  }

  .glass-mini-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    transition: all 0.3s ease;
  }

  .glass-mini-card:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-2px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.3);
  }

  .glass-badge {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50px;
    padding: 8px 16px;
  }

  /* Gradient Text Effects */
  .gradient-text-hero {
    background: linear-gradient(135deg, #60a5fa 0%, #a855f7 50%, #ec4899 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradient-shift 3s ease-in-out infinite;
  }

  @keyframes gradient-shift {
    0%,
    100% {
      background: linear-gradient(
        135deg,
        #60a5fa 0%,
        #a855f7 50%,
        #ec4899 100%
      );
      -webkit-background-clip: text;
      background-clip: text;
    }
    50% {
      background: linear-gradient(
        135deg,
        #ec4899 0%,
        #60a5fa 50%,
        #a855f7 100%
      );
      -webkit-background-clip: text;
      background-clip: text;
    }
  }

  /* Button Styles */
  .btn-primary-large {
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    color: white;
    padding: 16px 32px;
    border-radius: 16px;
    font-weight: 600;
    font-size: 18px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow:
      0 8px 24px rgba(59, 130, 246, 0.3),
      0 4px 12px rgba(139, 92, 246, 0.2);
    display: inline-flex;
    align-items: center;
    position: relative;
    overflow: hidden;
  }

  .btn-primary-large::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    transition: left 0.5s ease;
  }

  .btn-primary-large:hover::before {
    left: 100%;
  }

  .btn-primary-large:hover {
    transform: translateY(-2px);
    box-shadow:
      0 12px 32px rgba(59, 130, 246, 0.4),
      0 6px 16px rgba(139, 92, 246, 0.3);
  }

  .btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    padding: 12px 24px;
    border-radius: 12px;
    font-weight: 500;
    border: 1px solid rgba(255, 255, 255, 0.2);
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    backdrop-filter: blur(8px);
  }

  .btn-secondary:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
  }

  .btn-ghost {
    background: rgba(255, 255, 255, 0.05);
    color: white;
    padding: 12px 20px;
    border-radius: 12px;
    font-weight: 500;
    border: 1px solid rgba(255, 255, 255, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
  }

  .btn-ghost:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
  }

  .btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    color: white;
    padding: 12px 24px;
    border-radius: 12px;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
  }

  .btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 8px 16px rgba(59, 130, 246, 0.3);
  }

  /* Floating Action Buttons */
  .btn-floating {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
  }

  .btn-floating:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.4);
  }

  .btn-floating-primary {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 24px rgba(59, 130, 246, 0.4);
    border: none;
  }

  .btn-floating-primary:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 12px 32px rgba(59, 130, 246, 0.5);
  }

  .btn-floating-success {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 24px rgba(16, 185, 129, 0.4);
    border: none;
  }

  .btn-floating-success:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 12px 32px rgba(16, 185, 129, 0.5);
  }

  /* Content Enhancements */
  .content-sections :global(h2) {
    color: #60a5fa;
    font-size: 2rem;
    font-weight: 700;
    margin-top: 2rem;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #60a5fa 0%, #a855f7 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .content-sections :global(h3) {
    color: #a855f7;
    font-size: 1.5rem;
    font-weight: 600;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
  }

  .content-sections :global(p) {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.7;
    margin-bottom: 1rem;
  }

  .content-sections :global(ul),
  .content-sections :global(ol) {
    color: rgba(255, 255, 255, 0.8);
    margin-left: 1.5rem;
    margin-bottom: 1rem;
  }

  .content-sections :global(li) {
    margin-bottom: 0.5rem;
    line-height: 1.6;
  }

  .content-sections :global(strong) {
    color: #fbbf24;
    font-weight: 600;
  }

  /* Animations */
  @keyframes animate-reverse {
    from {
      transform: rotate(360deg);
    }
    to {
      transform: rotate(0deg);
    }
  }

  .animate-reverse {
    animation: animate-reverse 2s linear infinite;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .glass-nav {
      margin: 1rem;
    }

    .btn-primary-large {
      padding: 12px 24px;
      font-size: 16px;
    }

    .gradient-text-hero {
      font-size: 2.5rem;
    }
  }
</style>
