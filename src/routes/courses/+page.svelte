<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import {
    Target,
    Rocket,
    Brain,
    BookOpen,
    Zap,
    Shield,
    Database,
    Search,
    Menu,
    X,
  } from 'lucide-svelte';

  let courses: any[] = [];
  let loading = true;
  let error: string | null = null;
  let searchQuery = '';
  let selectedCategory = 'all';
  let selectedDifficulty = 'all';
  let sortBy = 'popular';
  let mounted = false;
  let sidebarOpen = false;
  let showLearningPaths = true;
  let viewMode: 'path' | 'grid' | 'timeline' = 'grid';

  onMount(async () => {
    mounted = true;
    await loadCourses();
    initializeScrollAnimations();
  });

  async function loadCourses() {
    try {
      loading = true;
      error = null;

      // Load both static courses and MAS-generated courses
      const [staticResponse, masResponse] = await Promise.all([
        fetch('/courses/catalog.json').catch(() => ({ ok: false })),
        fetch('/api/content/courses').catch(() => ({ ok: false })),
      ]);

      let staticCourses: any[] = [];
      let masCourses: any[] = [];

      // Load static courses
      if (staticResponse.ok && 'json' in staticResponse) {
        const data = await staticResponse.json();
        staticCourses = data.courses || [];
      }

      // Load MAS-generated courses
      if (masResponse.ok && 'json' in masResponse) {
        const data = await masResponse.json();
        masCourses = data.courses || [];
      }

      // Combine and deduplicate courses
      const allCourses = [...staticCourses, ...masCourses];
      const uniqueCourses = allCourses.filter(
        (course, index, self) =>
          index === self.findIndex(c => c.id === course.id)
      );

      courses = uniqueCourses.length > 0 ? uniqueCourses : getDefaultCourses();

      // Apply initial filters after loading
      console.log('Loaded', courses.length, 'courses');
    } catch (err) {
      console.error('Error loading courses:', err);
      error = 'Failed to load courses. Please try again.';
      courses = getDefaultCourses();
    } finally {
      loading = false;
    }
  }

  function getDefaultCourses() {
    return [
      {
        id: 'vybe-method-intro',
        title: 'Introduction to the Vybe Method',
        description:
          'Master the revolutionary Vybe Method that combines BMAD methodology with Multi-Agent Systems for autonomous AI development.',
        difficulty: 'beginner',
        category: 'methodology',
        estimatedDuration: 120,
        price: 0,
        isPublished: true,
        thumbnailUrl:
          'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=400&h=200&fit=crop',
        lessonCount: 8,
        rating: 4.9,
        studentCount: 12500,
        tags: ['vybe-method', 'bmad', 'ai', 'methodology'],
        instructor: {
          name: 'VybeCoding.ai Team',
          avatar: '/images/avatars/team.jpg',
          rating: 4.9,
        },
        gradient: 'from-cyan-400 to-blue-500',
        featured: true,
        icon: Target,
        stats: 'Revolutionary Method',
      },
      {
        id: 'ai-development-fundamentals',
        title: 'AI Development Fundamentals',
        description:
          'Learn the core concepts of AI development including machine learning, neural networks, and practical implementation strategies.',
        difficulty: 'beginner',
        category: 'ai-development',
        estimatedDuration: 180,
        price: 29,
        isPublished: true,
        thumbnailUrl:
          'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=200&fit=crop',
        lessonCount: 12,
        rating: 4.8,
        studentCount: 8900,
        tags: ['ai', 'machine-learning', 'neural-networks', 'fundamentals'],
        instructor: {
          name: 'Alex Rodriguez',
          avatar: '/images/avatars/alex.jpg',
          rating: 4.8,
        },
        gradient: 'from-purple-400 to-pink-500',
        featured: false,
        icon: Brain,
        stats: 'AI-powered coding',
      },
      {
        id: 'svelte-mastery',
        title: 'SvelteKit Mastery',
        description:
          'Build modern web applications with SvelteKit. Learn reactive programming, component architecture, and deployment strategies.',
        difficulty: 'intermediate',
        category: 'web-development',
        estimatedDuration: 240,
        price: 49,
        isPublished: true,
        thumbnailUrl:
          'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=400&h=200&fit=crop',
        lessonCount: 16,
        rating: 4.9,
        studentCount: 6750,
        tags: ['svelte', 'sveltekit', 'web-development', 'javascript'],
        instructor: {
          name: 'Maya Patel',
          avatar: '/images/avatars/maya.jpg',
          rating: 4.9,
        },
        gradient: 'from-green-400 to-emerald-500',
        featured: true,
        icon: Rocket,
        stats: '98+ Lighthouse score',
      },
      {
        id: 'docker-containerization',
        title: 'Docker & Containerization',
        description:
          'Master containerization with Docker. Learn to build, deploy, and manage scalable applications using container technology.',
        difficulty: 'intermediate',
        category: 'devops',
        estimatedDuration: 150,
        price: 39,
        isPublished: true,
        thumbnailUrl:
          'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=200&fit=crop',
        lessonCount: 10,
        rating: 4.7,
        studentCount: 5420,
        tags: ['docker', 'containerization', 'devops', 'deployment'],
        instructor: {
          name: 'James Wilson',
          avatar: '/images/avatars/james.jpg',
          rating: 4.7,
        },
        gradient: 'from-blue-400 to-indigo-500',
        featured: false,
        icon: Database,
        stats: '99.9% uptime',
      },
      {
        id: 'advanced-ai-agents',
        title: 'Advanced AI Agent Systems',
        description:
          'Build sophisticated AI agent systems with autonomous decision-making, multi-agent coordination, and real-world applications.',
        difficulty: 'advanced',
        category: 'ai-development',
        estimatedDuration: 300,
        price: 79,
        isPublished: true,
        thumbnailUrl:
          'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=200&fit=crop',
        lessonCount: 20,
        rating: 4.9,
        studentCount: 3200,
        tags: ['ai-agents', 'automation', 'advanced', 'multi-agent'],
        instructor: {
          name: 'Dr. Emily Zhang',
          avatar: '/images/avatars/emily.jpg',
          rating: 4.9,
        },
        gradient: 'from-orange-400 to-red-500',
        featured: true,
        icon: Zap,
        stats: '160% efficiency gain',
      },
      {
        id: 'typescript-mastery',
        title: 'TypeScript for Modern Development',
        description:
          'Master TypeScript for building robust, type-safe applications. Learn advanced patterns, generics, and integration strategies.',
        difficulty: 'intermediate',
        category: 'programming',
        estimatedDuration: 200,
        price: 0,
        isPublished: true,
        thumbnailUrl:
          'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=200&fit=crop',
        lessonCount: 14,
        rating: 4.8,
        studentCount: 7800,
        tags: ['typescript', 'javascript', 'programming', 'type-safety'],
        instructor: {
          name: 'Chris Thompson',
          avatar: '/images/avatars/chris.jpg',
          rating: 4.8,
        },
        gradient: 'from-teal-400 to-cyan-500',
        featured: false,
        icon: Shield,
        stats: '100% type coverage',
      },
    ];
  }

  // Scroll-triggered animations (from methods page)
  function initializeScrollAnimations() {
    const observer = new IntersectionObserver(
      entries => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('animate-in');
          }
        });
      },
      { threshold: 0.1 }
    );

    // Use requestAnimationFrame for real browser timing
    requestAnimationFrame(() => {
      document.querySelectorAll('.animate-on-scroll').forEach(el => {
        observer.observe(el);
      });
    });
  }

  function viewCourse(courseId: string) {
    console.log('Navigating to course:', courseId);
    goto(`/courses/${courseId}`);
  }

  function handleClearFilters() {
    searchQuery = '';
    selectedCategory = 'all';
    selectedDifficulty = 'all';
  }

  function toggleSidebar() {
    sidebarOpen = !sidebarOpen;
  }

  function toggleLearningPaths() {
    showLearningPaths = !showLearningPaths;
  }

  // Filtering and sorting functions
  $: filteredCourses = courses
    .filter(course => {
      const matchesSearch =
        (course.title || '')
          .toLowerCase()
          .includes(searchQuery.toLowerCase()) ||
        (course.description || '')
          .toLowerCase()
          .includes(searchQuery.toLowerCase());
      const matchesCategory =
        selectedCategory === 'all' ||
        (course.category || '') === selectedCategory;
      const matchesDifficulty =
        selectedDifficulty === 'all' ||
        (course.difficulty || '') === selectedDifficulty;

      return matchesSearch && matchesCategory && matchesDifficulty;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'price-low':
          return a.price - b.price;
        case 'price-high':
          return b.price - a.price;
        case 'duration':
          return a.estimatedDuration - b.estimatedDuration;
        case 'rating':
          return b.rating - a.rating;
        case 'popular':
        default:
          return b.studentCount - a.studentCount;
      }
    });
</script>

<svelte:head>
  <title>Courses - VybeCoding.ai</title>
  <meta
    name="description"
    content="Explore our AI-powered coding courses and master the Vybe Method"
  />
</svelte:head>

<!-- HYBRID PROFESSIONAL + AGGRESSIVE COURSES PAGE -->
<main class="min-h-screen relative overflow-hidden bg-gray-50 dark:bg-gray-900">
  <!-- Subtle background effects -->
  <div
    class="absolute inset-0 bg-gradient-to-br from-slate-50 via-white to-slate-50 dark:from-slate-950 dark:via-slate-900 dark:to-slate-950"
  ></div>
  <div
    class="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(6,182,212,0.05),transparent_50%)] dark:bg-[radial-gradient(circle_at_30%_20%,rgba(6,182,212,0.15),transparent_50%)]"
  ></div>
  <div
    class="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(236,72,153,0.03),transparent_50%)] dark:bg-[radial-gradient(circle_at_70%_80%,rgba(236,72,153,0.1),transparent_50%)]"
  ></div>

  <!-- Mobile Filter Sidebar Overlay -->
  {#if sidebarOpen}
    <div
      class="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
      onclick={toggleSidebar}
      onkeydown={e => e.key === 'Escape' && toggleSidebar()}
      role="button"
      tabindex="0"
      aria-label="Close sidebar"
    ></div>
  {/if}

  <!-- Professional Header with Aggressive Accents -->
  <header
    class="relative z-30 bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl shadow-sm border-b border-gray-200/50 dark:border-gray-700/50"
  >
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between h-16">
        <!-- Mobile Menu Button -->
        <button
          class="md:hidden p-2 rounded-lg text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          onclick={toggleSidebar}
          aria-label="Open filters"
        >
          <Menu class="w-5 h-5" />
        </button>

        <!-- Page Title with Aggressive Accent -->
        <div class="flex-1 md:flex-none">
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
            <span
              class="bg-gradient-to-r from-purple-600 to-cyan-600 bg-clip-text text-transparent"
              >Course Discovery</span
            >
          </h1>
        </div>

        <!-- Header Actions -->
        <div class="flex items-center space-x-4">
          <!-- Learning Paths Toggle -->
          <button
            class="hidden md:flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors font-medium"
            class:bg-gradient-to-r={showLearningPaths}
            class:from-cyan-500={showLearningPaths}
            class:to-blue-500={showLearningPaths}
            class:text-white={showLearningPaths}
            class:bg-gray-100={!showLearningPaths}
            class:dark:bg-gray-700={!showLearningPaths}
            class:text-gray-700={!showLearningPaths}
            class:dark:text-gray-300={!showLearningPaths}
            onclick={toggleLearningPaths}
          >
            <Target class="w-4 h-4" />
            <span class="text-sm">Learning Paths</span>
          </button>
        </div>
      </div>
    </div>
  </header>

  <!-- Main Content Layout -->
  <div class="flex relative z-10">
    <!-- Professional Filter Sidebar -->
    <div class="hidden md:block w-80 flex-shrink-0">
      <div
        class="h-full bg-white/60 dark:bg-gray-800/60 backdrop-blur-xl border-r border-gray-200/50 dark:border-gray-700/50 p-6"
      >
        <div class="flex items-center mb-6">
          <Search class="w-5 h-5 text-purple-500 mr-2" />
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            Smart Filters
          </h3>
        </div>

        <!-- Search -->
        <div class="mb-6">
          <label
            for="search"
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
          >
            Search Courses
          </label>
          <div class="relative">
            <Search
              class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"
            />
            <input
              id="search"
              type="text"
              bind:value={searchQuery}
              placeholder="Search courses..."
              class="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white/80 dark:bg-gray-700/80 backdrop-blur-sm text-gray-900 dark:text-white focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all"
            />
          </div>
        </div>

        <!-- Category Filter -->
        <div class="mb-6">
          <label
            for="category"
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
          >
            Category
          </label>
          <select
            id="category"
            bind:value={selectedCategory}
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white/80 dark:bg-gray-700/80 backdrop-blur-sm text-gray-900 dark:text-white focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all"
          >
            <option value="all">All Categories</option>
            <option value="methodology">Vybe Method</option>
            <option value="ai-development">AI Development</option>
            <option value="web-development">Web Development</option>
            <option value="devops">DevOps</option>
            <option value="programming">Programming</option>
            <option value="automation">Automation</option>
          </select>
        </div>

        <!-- Difficulty Filter -->
        <div class="mb-6">
          <label
            for="difficulty"
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
          >
            Difficulty
          </label>
          <select
            id="difficulty"
            bind:value={selectedDifficulty}
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white/80 dark:bg-gray-700/80 backdrop-blur-sm text-gray-900 dark:text-white focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all"
          >
            <option value="all">All Levels</option>
            <option value="beginner">Beginner</option>
            <option value="intermediate">Intermediate</option>
            <option value="advanced">Advanced</option>
          </select>
        </div>

        <!-- Filter Actions -->
        <div class="space-y-3">
          <button
            type="button"
            class="w-full px-4 py-3 bg-gradient-to-r from-cyan-500 to-blue-500 text-white rounded-lg hover:from-cyan-600 hover:to-blue-600 transition-all font-medium flex items-center justify-center gap-2 shadow-lg"
          >
            <Search class="w-4 h-4" />
            Apply Filters ({filteredCourses.length} results)
          </button>

          <button
            type="button"
            class="w-full px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors font-medium"
            onclick={handleClearFilters}
          >
            Clear All Filters
          </button>
        </div>

        <!-- Course Stats -->
        <div
          class="mt-8 p-4 bg-gradient-to-r from-purple-500/10 to-cyan-500/10 rounded-lg border border-purple-200/20 dark:border-purple-700/20"
        >
          <h4 class="font-semibold text-gray-900 dark:text-white mb-2">
            Course Stats
          </h4>
          <div class="space-y-2 text-sm">
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400"
                >Total Courses:</span
              >
              <span class="font-medium text-purple-600 dark:text-purple-400"
                >{courses.length}</span
              >
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400"
                >MAS Generated:</span
              >
              <span class="font-medium text-cyan-600 dark:text-cyan-400"
                >Live</span
              >
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Mobile Filter Sidebar -->
    {#if sidebarOpen}
      <div class="fixed inset-0 z-50 md:hidden">
        <div
          class="fixed inset-0 bg-black bg-opacity-50"
          onclick={toggleSidebar}
          onkeydown={e => e.key === 'Escape' && toggleSidebar()}
          role="button"
          tabindex="0"
          aria-label="Close sidebar"
        ></div>
        <div
          class="fixed inset-y-0 left-0 w-80 bg-white/90 dark:bg-gray-800/90 backdrop-blur-xl shadow-xl p-6"
        >
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
              Filters
            </h3>
            <button
              onclick={toggleSidebar}
              class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              <X class="w-5 h-5" />
            </button>
          </div>
          <!-- Same filters as desktop version -->
          <div class="space-y-6">
            <!-- Search -->
            <div>
              <label
                for="mobile-search"
                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
              >
                Search Courses
              </label>
              <input
                id="mobile-search"
                type="text"
                bind:value={searchQuery}
                placeholder="Search courses..."
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
              />
            </div>
            <!-- Category -->
            <div>
              <label
                for="mobile-category"
                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
              >
                Category
              </label>
              <select
                id="mobile-category"
                bind:value={selectedCategory}
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
              >
                <option value="all">All Categories</option>
                <option value="methodology">Vybe Method</option>
                <option value="ai-development">AI Development</option>
                <option value="web-development">Web Development</option>
                <option value="devops">DevOps</option>
                <option value="programming">Programming</option>
                <option value="automation">Automation</option>
              </select>
            </div>
            <!-- Difficulty -->
            <div>
              <label
                for="mobile-difficulty"
                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
              >
                Difficulty
              </label>
              <select
                id="mobile-difficulty"
                bind:value={selectedDifficulty}
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
              >
                <option value="all">All Levels</option>
                <option value="beginner">Beginner</option>
                <option value="intermediate">Intermediate</option>
                <option value="advanced">Advanced</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    {/if}

    <!-- Main Content Area -->
    <div class="flex-1 p-6">
      <!-- Learning Path Visualization -->
      {#if showLearningPaths}
        <section class="mb-12 animate-on-scroll">
          <div
            class="bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-2xl p-8 mb-8 shadow-xl"
          >
            <div class="flex items-center justify-between mb-6">
              <div>
                <h2 class="text-3xl font-bold mb-2">🎯 Learning Paths</h2>
                <p class="text-blue-100">
                  Structured learning journeys to master AI development
                </p>
              </div>
              <div class="flex space-x-2">
                <button
                  class="px-4 py-2 bg-white bg-opacity-20 rounded-lg hover:bg-opacity-30 transition-colors"
                  class:bg-opacity-40={viewMode === 'grid'}
                  onclick={() => (viewMode = 'grid')}
                >
                  Grid
                </button>
                <button
                  class="px-4 py-2 bg-white bg-opacity-20 rounded-lg hover:bg-opacity-30 transition-colors"
                  class:bg-opacity-40={viewMode === 'path'}
                  onclick={() => (viewMode = 'path')}
                >
                  Path
                </button>
              </div>
            </div>

            <!-- Learning Paths -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {#each [{ id: 'vybe-method', name: 'Vybe Method Foundation', courses: 3, progress: 0, icon: Target }, { id: 'ai-programming', name: 'AI Programming Mastery', courses: 5, progress: 40, icon: Brain }, { id: 'automation', name: 'Automation Expert', courses: 4, progress: 0, icon: Zap }] as path}
                <div
                  class="bg-white bg-opacity-10 backdrop-blur-sm rounded-xl p-6 hover:bg-opacity-20 transition-all cursor-pointer"
                >
                  <div class="flex items-center mb-4">
                    <div
                      class="w-12 h-12 bg-white bg-opacity-20 rounded-xl flex items-center justify-center mr-4"
                    >
                      <svelte:component this={path.icon} class="w-6 h-6" />
                    </div>
                    <div>
                      <h3 class="font-semibold text-lg">{path.name}</h3>
                      <p class="text-blue-100 text-sm">
                        {path.courses} courses
                      </p>
                    </div>
                  </div>
                  <div
                    class="w-full bg-white bg-opacity-20 rounded-full h-2 mb-2"
                  >
                    <div
                      class="bg-white rounded-full h-2"
                      style="width: {path.progress}%"
                    ></div>
                  </div>
                  <p class="text-blue-100 text-sm">{path.progress}% complete</p>
                </div>
              {/each}
            </div>
          </div>
        </section>
      {/if}

      <!-- Course Content Header -->
      <div class="flex items-center justify-between mb-8">
        <div>
          <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            {#if showLearningPaths}
              All Courses
            {:else}
              <span
                class="bg-gradient-to-r from-purple-600 to-cyan-600 bg-clip-text text-transparent"
                >Course Arsenal</span
              >
            {/if}
          </h2>
          <p class="text-gray-600 dark:text-gray-300">
            {filteredCourses.length} course{filteredCourses.length !== 1
              ? 's'
              : ''} found
          </p>
        </div>

        <!-- Sort Options -->
        <div class="flex items-center space-x-2">
          <label
            for="sort-select"
            class="text-sm font-medium text-gray-700 dark:text-gray-300"
          >
            Sort by:
          </label>
          <select
            id="sort-select"
            bind:value={sortBy}
            class="px-3 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm text-gray-900 dark:text-white focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
          >
            <option value="popular">Most Popular</option>
            <option value="rating">Highest Rated</option>
            <option value="price-low">Price: Low to High</option>
            <option value="price-high">Price: High to Low</option>
            <option value="duration">Duration</option>
          </select>
        </div>
      </div>

      <!-- Course Cards Grid -->
      {#if loading}
        <!-- Loading State -->
        <div class="flex items-center justify-center min-h-96">
          <div class="text-center">
            <div
              class="animate-spin rounded-full h-12 w-12 border-b-2 border-cyan-500 mx-auto mb-4"
            ></div>
            <p class="text-gray-600 dark:text-gray-300">Loading courses...</p>
          </div>
        </div>
      {:else if error}
        <!-- Error State -->
        <div class="text-center py-12">
          <div class="text-red-500 text-6xl mb-4">⚠️</div>
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Error Loading Courses
          </h3>
          <p class="text-gray-500 dark:text-gray-400 mb-4">{error}</p>
          <button
            class="px-4 py-2 bg-cyan-500 text-white rounded-lg hover:bg-cyan-600 transition-colors"
            onclick={() => loadCourses()}
          >
            Try Again
          </button>
        </div>
      {:else if filteredCourses.length > 0}
        <!-- Hybrid Course Cards -->
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 animate-on-scroll">
          {#each filteredCourses as course, index}
            <div
              class="group relative animate-on-scroll"
              style="animation-delay: {index * 0.1}s"
            >
              <!-- Dynamic color-changing card exactly like methods page -->
              <div
                class="relative h-full p-8 rounded-3xl bg-slate-800/40 backdrop-blur-sm border border-slate-700/50 hover:border-cyan-400/50 transition-all duration-700 group-hover:scale-105 group-hover:-translate-y-2 overflow-hidden cursor-pointer"
                role="button"
                tabindex="0"
                onclick={() => viewCourse(course.id)}
                onkeydown={e => e.key === 'Enter' && viewCourse(course.id)}
              >
                <!-- Gradient overlay -->
                <div
                  class="absolute inset-0 rounded-3xl bg-gradient-to-br {course.gradient}/10 opacity-0 group-hover:opacity-100 transition-opacity duration-700"
                ></div>

                <!-- Animated border glow -->
                <div
                  class="absolute inset-0 rounded-3xl bg-gradient-to-r {course.gradient} opacity-0 group-hover:opacity-30 blur-xl transition-opacity duration-700"
                ></div>

                <!-- Content -->
                <div class="relative z-10">
                  <!-- Category Badge -->
                  <div
                    class="inline-flex items-center px-3 py-1 rounded-full bg-gradient-to-r {course.gradient}/20 border border-current border-opacity-30 mb-4"
                  >
                    <span class="text-xs font-bold text-white"
                      >{course.category}</span
                    >
                  </div>

                  <!-- Icon -->
                  <div
                    class="w-16 h-16 rounded-2xl bg-gradient-to-br {course.gradient} p-0.5 mb-6 group-hover:scale-110 transition-transform duration-500"
                  >
                    <div
                      class="w-full h-full rounded-2xl bg-slate-800 flex items-center justify-center"
                    >
                      <svelte:component
                        this={course.icon || BookOpen}
                        class="w-8 h-8 text-white"
                      />
                    </div>
                  </div>

                  <!-- Course Title -->
                  <h3
                    class="text-xl font-bold mb-2 text-white transition-all duration-500"
                  >
                    {course.title}
                  </h3>

                  <!-- Difficulty & Lessons -->
                  <div
                    class="text-sm font-medium text-slate-300 mb-3 capitalize transition-colors duration-300"
                  >
                    {course.difficulty} • {course.lessonCount} lessons • {Math.ceil(
                      course.estimatedDuration / 60
                    )}h
                  </div>

                  <!-- Description -->
                  <p
                    class="text-slate-300 leading-relaxed mb-4 line-clamp-3 group-hover:text-slate-200 transition-colors duration-300"
                  >
                    {course.description}
                  </p>

                  <!-- Stats -->
                  <div
                    class="inline-flex items-center px-3 py-1 rounded-full bg-gradient-to-r {course.gradient}/20 border border-current border-opacity-30 mb-4"
                  >
                    <span class="text-xs font-bold text-white"
                      >📊 {course.stats ||
                        `${course.studentCount} students`}</span
                    >
                  </div>

                  <!-- Price -->
                  {#if course.price === 0}
                    <div class="text-green-400 font-bold mb-4">FREE</div>
                  {:else}
                    <div class="text-white font-bold mb-4">
                      ${(course.price / 100).toFixed(2)}
                    </div>
                  {/if}
                </div>
              </div>
            </div>
          {/each}
        </div>
      {:else}
        <!-- Empty State -->
        <div class="text-center py-16">
          <div
            class="mx-auto w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-6"
          >
            <Search class="w-12 h-12 text-gray-400" />
          </div>
          <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            No courses found
          </h3>
          <p class="text-gray-500 dark:text-gray-400 mb-6 max-w-md mx-auto">
            We couldn't find any courses matching your current filters. Try
            adjusting your search criteria.
          </p>
          <button
            class="px-6 py-3 bg-cyan-500 text-white rounded-lg hover:bg-cyan-600 transition-colors font-medium"
            onclick={handleClearFilters}
          >
            Clear All Filters
          </button>
        </div>
      {/if}
    </div>
  </div>
</main>

<style>
  /* Animations from methods page */
  @keyframes float {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  @keyframes float-delayed {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-15px);
    }
  }

  @keyframes float-slow {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-5px);
    }
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-float-delayed {
    animation: float-delayed 4s ease-in-out infinite;
  }

  .animate-float-slow {
    animation: float-slow 5s ease-in-out infinite;
  }

  /* Scroll animations */
  .animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease-out;
  }

  .animate-on-scroll.animate-in {
    opacity: 1;
    transform: translateY(0);
  }

  /* Focus states for accessibility */
  button:focus,
  [role='button']:focus {
    outline: none;
    box-shadow:
      0 0 0 2px #06b6d4,
      0 0 0 4px rgba(6, 182, 212, 0.2);
  }
</style>
