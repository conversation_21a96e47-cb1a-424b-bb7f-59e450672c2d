<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';

  // Course data
  const course = {
  "type": "course",
  "title": " - Complete Course",
  "description": "A comprehensive course on  designed for . Enhanced with real-world examples and practical applications.",
  "lessons": [
    {
      "title": "Introduction to ",
      "duration": 30,
      "description": "Foundation concepts and overview"
    },
    {
      "title": "Core Principles & Theory",
      "duration": 45,
      "description": "Deep dive into fundamental principles"
    },
    {
      "title": "Practical Implementation",
      "duration": 60,
      "description": "Hands-on exercises and real examples"
    },
    {
      "title": "Advanced Techniques",
      "duration": 45,
      "description": "Professional-level strategies"
    },
    {
      "title": "Capstone Project",
      "duration": 90,
      "description": "Apply everything learned"
    }
  ],
  "assessments": [
    {
      "type": "quiz",
      "questions": 10,
      "passing_score": 80
    },
    {
      "type": "project",
      "requirements": "Build a  application"
    },
    {
      "type": "peer_review",
      "criteria": [
        "functionality",
        "code_quality",
        "documentation"
      ]
    }
  ],
  "estimatedDuration": "4.5 hours",
  "target_audience": "",
  "complexity_level": "intermediate",
  "generated_at": "2025-06-06T03:45:37.874Z",
  "agents_used": [
    "VYBA",
    "QUBERT",
    "CODEX",
    "PIXY",
    "DUCKY",
    "HAPPY"
  ],
  "inspirationSources": [],
  "supportingDocs": [],
  "agent_activities": [
    "QUBERT assessing complexity level automatically",
    "PIXY designing course structure",
    "DUCKY validating educational content quality",
    "HAPPY coordinating agent collaboration"
  ]
};

  let currentLesson = 0;
  let progress = 0;

  onMount(() => {
    // Track course view
    console.log('Course viewed:', course.title);
  });

  function nextLesson() {
    if (currentLesson < course.lessons.length - 1) {
      currentLesson++;
      progress = ((currentLesson + 1) / course.lessons.length) * 100;
    }
  }

  function prevLesson() {
    if (currentLesson > 0) {
      currentLesson--;
      progress = ((currentLesson + 1) / course.lessons.length) * 100;
    }
  }
</script>

<svelte:head>
  <title>{course.title} - VybeCoding.ai</title>
  <meta name="description" content={course.description} />
</svelte:head>

<div class="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900">
  <div class="container mx-auto px-4 py-8">
    <!-- Course Header -->
    <div class="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 mb-8">
      <h1 class="text-4xl font-bold text-white mb-4">{course.title}</h1>
      <p class="text-gray-300 text-lg mb-4">{course.description}</p>

      <div class="flex flex-wrap gap-4 text-sm">
        <span class="bg-cyan-600 text-white px-3 py-1 rounded-full">
          {course.difficulty || 'Intermediate'}
        </span>
        <span class="bg-purple-600 text-white px-3 py-1 rounded-full">
          {course.estimatedDuration || '4 hours'}
        </span>
        <span class="bg-green-600 text-white px-3 py-1 rounded-full">
          {course.lessons?.length || 0} Lessons
        </span>
        <span class="bg-orange-600 text-white px-3 py-1 rounded-full">
          Generated by MAS
        </span>
      </div>
    </div>

    <!-- Progress Bar -->
    <div class="bg-gray-800/50 backdrop-blur-sm rounded-lg p-4 mb-8">
      <div class="flex justify-between items-center mb-2">
        <span class="text-white font-semibold">Progress</span>
        <span class="text-cyan-400">{Math.round(progress)}%</span>
      </div>
      <div class="w-full bg-gray-700 rounded-full h-2">
        <div class="bg-gradient-to-r from-cyan-500 to-purple-500 h-2 rounded-full transition-all duration-300"
             style="width: {progress}%"></div>
      </div>
    </div>

    <!-- Current Lesson -->
    {#if course.lessons && course.lessons.length > 0}
      <div class="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 mb-8">
        <h2 class="text-2xl font-bold text-white mb-4">
          Lesson {currentLesson + 1}: {course.lessons[currentLesson].title}
        </h2>
        <p class="text-gray-300 mb-4">{course.lessons[currentLesson].description}</p>
        <div class="text-sm text-cyan-400 mb-4">
          Duration: {course.lessons[currentLesson].duration} minutes
        </div>

        <!-- Lesson Navigation -->
        <div class="flex justify-between">
          <button
            class="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg disabled:opacity-50"
            on:click={prevLesson}
            disabled={currentLesson === 0}
          >
            Previous Lesson
          </button>

          <button
            class="bg-cyan-600 hover:bg-cyan-700 text-white px-4 py-2 rounded-lg disabled:opacity-50"
            on:click={nextLesson}
            disabled={currentLesson === course.lessons.length - 1}
          >
            Next Lesson
          </button>
        </div>
      </div>
    {/if}

    <!-- Course Metadata -->
    <div class="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6">
      <h3 class="text-xl font-bold text-white mb-4">Course Information</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
        <div>
          <span class="text-gray-400">Target Audience:</span>
          <span class="text-white ml-2">{course.targetAudience || course.target_audience || 'Developers'}</span>
        </div>
        <div>
          <span class="text-gray-400">Generated:</span>
          <span class="text-white ml-2">{new Date(course.generated_at || course.createdAt || Date.now()).toLocaleDateString()}</span>
        </div>
        {#if course.agents_used}
          <div class="md:col-span-2">
            <span class="text-gray-400">MAS Agents:</span>
            <span class="text-cyan-400 ml-2">{course.agents_used.join(', ')}</span>
          </div>
        {/if}
      </div>
    </div>
  </div>
</div>