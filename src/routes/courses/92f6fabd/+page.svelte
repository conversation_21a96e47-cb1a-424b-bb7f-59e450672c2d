<script>
  import { onMount } from 'svelte';
  
  let course = {
    id: '92f6fabd',
    title: 'Machine Learning Fundamentals',
    description: 'Learn the basics of machine learning',
    duration: 90,
    difficulty: 'beginner',
    lessons: [
  {
    "title": "Introduction to ML",
    "description": "What is machine learning?",
    "content": "<p>Machine learning is a subset of AI...</p>"
  }
],
    createdAt: '2025-06-06T13:36:10.262371'
  };
</script>

<svelte:head>
  <title>{course.title} - VybeCoding.ai</title>
  <meta name="description" content="{course.description}" />
</svelte:head>

<div class="course-container">
  <header class="course-header">
    <h1>{course.title}</h1>
    <p class="course-description">{course.description}</p>
    <div class="course-meta">
      <span class="duration">Duration: {course.duration} minutes</span>
      <span class="difficulty">Difficulty: {course.difficulty}</span>
    </div>
  </header>
  
  <div class="lessons-container">
    <h2>Course Lessons</h2>
    {#each course.lessons as lesson, index}
      <div class="lesson-card">
        <h3>Lesson {index + 1}: {lesson.title}</h3>
        <p>{lesson.description}</p>
        <div class="lesson-content">
          {@html lesson.content}
        </div>
      </div>
    {/each}
  </div>
</div>

<style>
  .course-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
  }
  
  .course-header {
    margin-bottom: 2rem;
    text-align: center;
  }
  
  .course-meta {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 1rem;
  }
  
  .lesson-card {
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
  }
  
  .lesson-card h3 {
    color: #2d3748;
    margin-bottom: 0.5rem;
  }
</style>