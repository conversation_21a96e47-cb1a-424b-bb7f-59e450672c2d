<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { userVybeQubeService } from '$lib/services/userVybeQubes';
  import { authService } from '$lib/services/auth';
  import type { UserVybeQube } from '$lib/types';
  import { But<PERSON>, Card, Badge } from '$lib/components/ui';
  import {
    ExternalLink,
    Heart,
    Eye,
    DollarSign,
    Code,
    Calendar,
    User,
    ArrowLeft,
  } from 'lucide-svelte';

  let qube: UserVybeQube | null = null;
  let loading = true;
  let error: string | null = null;
  let liked = false;

  $: qubeId = $page.params.id;

  onMount(async () => {
    if (qubeId) {
      await loadQube(qubeId);
    }
  });

  async function loadQube(id: string) {
    try {
      loading = true;
      error = null;

      qube = await userVybeQubeService.getVybeQubeById(id);

      if (!qube) {
        error = 'Vybe Qube not found';
      }
    } catch (err) {
      error = err instanceof Error ? err.message : 'Failed to load Vybe Qube';
    } finally {
      loading = false;
    }
  }

  function getPreviewImageUrl(qube: UserVybeQube): string {
    if (qube.previewImage) {
      return userVybeQubeService.getPreviewImageUrl(qube.previewImage);
    }
    return `https://via.placeholder.com/800x400/6366f1/ffffff?text=${encodeURIComponent(qube.title)}`;
  }

  async function handleLike() {
    if (!qube) return;

    // Get user ID from auth service
    const userId = authService.getCurrentUser()?.id || 'anonymous';
    const success = await userVybeQubeService.toggleLike(qube.id, userId);

    if (success) {
      qube.likes += liked ? -1 : 1;
      liked = !liked;
    }
  }

  function formatDate(date: Date): string {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }).format(date);
  }
</script>

<svelte:head>
  {#if qube}
    <title>{qube.title} - Vybe Qube Showcase - VybeCoding.ai</title>
    <meta name="description" content={qube.description} />
    <meta property="og:title" content={qube.title} />
    <meta property="og:description" content={qube.description} />
    {#if qube.previewImage}
      <meta property="og:image" content={getPreviewImageUrl(qube)} />
    {/if}
  {:else}
    <title>Vybe Qube - VybeCoding.ai</title>
  {/if}
</svelte:head>

<div class="container mx-auto px-4 py-8 max-w-6xl">
  <!-- Back Button -->
  <div class="mb-6">
    <Button variant="outline" href="/vybeqube">
      <ArrowLeft class="w-4 h-4 mr-2" />
      Back to Showcase
    </Button>
  </div>

  {#if loading}
    <div class="text-center py-12">
      <div
        class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"
      ></div>
      <p class="text-gray-600 mt-4">Loading Vybe Qube...</p>
    </div>
  {:else if error}
    <div class="text-center py-12">
      <p class="text-red-600 text-lg">{error}</p>
      <Button class="mt-4" href="/vybeqube">Browse All Qubes</Button>
    </div>
  {:else if qube}
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Main Content -->
      <div class="lg:col-span-2 space-y-6">
        <!-- Header -->
        <div>
          <div class="flex items-start justify-between mb-4">
            <div>
              <h1 class="text-3xl font-bold text-gray-900 mb-2">
                {qube.title}
              </h1>
              <div class="flex items-center gap-4 text-sm text-gray-600">
                <span class="flex items-center gap-1">
                  <User class="w-4 h-4" />
                  by {qube.userName}
                </span>
                <span class="flex items-center gap-1">
                  <Calendar class="w-4 h-4" />
                  {formatDate(qube.submittedAt)}
                </span>
                <span class="flex items-center gap-1">
                  <Eye class="w-4 h-4" />
                  {qube.views} views
                </span>
              </div>
            </div>

            <div class="flex items-center gap-2">
              {#if qube.featured}
                <Badge class="bg-yellow-500 text-white">Featured</Badge>
              {/if}
              <Badge variant="outline">{qube.category}</Badge>
            </div>
          </div>

          <!-- Preview Image -->
          <div class="aspect-video bg-gray-100 rounded-lg overflow-hidden mb-6">
            <img
              src={getPreviewImageUrl(qube)}
              alt={qube.title}
              class="w-full h-full object-cover"
            />
          </div>

          <!-- Action Buttons -->
          <div class="flex gap-3 mb-6">
            <Button href={qube.websiteUrl} target="_blank" class="flex-1">
              <ExternalLink class="w-4 h-4 mr-2" />
              Visit Website
            </Button>

            {#if qube.sourceCodeUrl}
              <Button
                variant="outline"
                href={qube.sourceCodeUrl}
                target="_blank"
              >
                <Code class="w-4 h-4 mr-2" />
                View Code
              </Button>
            {/if}

            <Button
              variant="outline"
              onclick={handleLike}
              class={liked ? 'text-red-500 border-red-500' : ''}
            >
              <Heart class="w-4 h-4 mr-2 {liked ? 'fill-current' : ''}" />
              {qube.likes}
            </Button>
          </div>
        </div>

        <!-- Description -->
        <Card class="p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">
            About This Project
          </h2>
          <p class="text-gray-700 leading-relaxed whitespace-pre-wrap">
            {qube.description}
          </p>
        </Card>

        <!-- Learning Outcomes -->
        {#if qube.learningOutcomes.length > 0}
          <Card class="p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">
              Learning Outcomes
            </h2>
            <ul class="space-y-2">
              {#each qube.learningOutcomes as outcome}
                <li class="flex items-start gap-2">
                  <span class="text-green-500 mt-1">✓</span>
                  <span class="text-gray-700">{outcome}</span>
                </li>
              {/each}
            </ul>
          </Card>
        {/if}

        <!-- Vybe Method Components -->
        {#if qube.vybeMethodUsed.length > 0}
          <Card class="p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">
              Vybe Method Components Applied
            </h2>
            <div class="flex flex-wrap gap-2">
              {#each qube.vybeMethodUsed as component}
                <Badge variant="secondary">{component}</Badge>
              {/each}
            </div>
          </Card>
        {/if}
      </div>

      <!-- Sidebar -->
      <div class="space-y-6">
        <!-- Project Stats -->
        <Card class="p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">
            Project Stats
          </h3>
          <div class="space-y-3">
            <div class="flex justify-between">
              <span class="text-gray-600">Views</span>
              <span class="font-medium">{qube.views}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">Likes</span>
              <span class="font-medium">{qube.likes}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">Category</span>
              <span class="font-medium">{qube.category}</span>
            </div>
            {#if qube.revenue?.isMonetized}
              <div class="flex justify-between">
                <span class="text-gray-600">Monthly Revenue</span>
                <span class="font-medium text-green-600">
                  <DollarSign class="w-4 h-4 inline mr-1" />
                  {qube.revenue.monthlyRevenue || 0}
                </span>
              </div>
            {/if}
          </div>
        </Card>

        <!-- Technologies -->
        {#if qube.technologies.length > 0}
          <Card class="p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
              Technologies Used
            </h3>
            <div class="flex flex-wrap gap-2">
              {#each qube.technologies as tech}
                <Badge variant="outline">{tech}</Badge>
              {/each}
            </div>
          </Card>
        {/if}

        <!-- Tags -->
        {#if qube.tags.length > 0}
          <Card class="p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Tags</h3>
            <div class="flex flex-wrap gap-2">
              {#each qube.tags as tag}
                <Badge variant="secondary">{tag}</Badge>
              {/each}
            </div>
          </Card>
        {/if}

        <!-- Revenue Model -->
        {#if qube.revenue?.isMonetized && qube.revenue.revenueModel.length > 0}
          <Card class="p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
              Revenue Model
            </h3>
            <div class="space-y-2">
              {#each qube.revenue.revenueModel as model}
                <div class="flex items-center gap-2">
                  <DollarSign class="w-4 h-4 text-green-500" />
                  <span class="text-gray-700">{model}</span>
                </div>
              {/each}
            </div>
            {#if qube.revenue.verified}
              <div class="mt-3 text-sm text-green-600">✓ Revenue verified</div>
            {/if}
          </Card>
        {/if}

        <!-- Call to Action -->
        <Card class="p-6 bg-primary/5 border-primary/20">
          <h3 class="text-lg font-semibold text-gray-900 mb-2">Inspired?</h3>
          <p class="text-gray-600 text-sm mb-4">
            Build your own amazing project using the Vybe Method and share it
            with the community.
          </p>
          <Button href="/vybeqube/submit" class="w-full">
            Submit Your Vybe Qube
          </Button>
        </Card>
      </div>
    </div>
  {/if}
</div>
