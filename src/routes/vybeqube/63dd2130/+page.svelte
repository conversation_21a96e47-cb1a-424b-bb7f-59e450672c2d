<script>
  let qube = {
    id: '63dd2130',
    title: 'Portfolio Generator',
    description: 'AI-powered portfolio websites',
    websiteContent: {
  "html": "<html><body><h1>Portfolio Generator</h1></body></html>",
  "css": "body { font-family: Arial; }",
  "js": "console.log(\"Portfolio ready\");"
},
    paymentConfig: {
  "price": 29,
  "currency": "USD"
},
    createdAt: '2025-06-06T13:36:10.262572'
  };

  function handlePurchase() {
    console.log('Processing payment for Vybe Qube:', qube.id);
  }
</script>

<svelte:head>
  <title>{qube.title} - VybeCoding.ai Vybe Qube</title>
  <meta name="description" content="{qube.description}" />
</svelte:head>

<div class="vybe-qube">
  <header class="qube-header">
    <h1>{qube.title}</h1>
    <p class="qube-description">{qube.description}</p>
  </header>

  <div class="qube-content">
    {#if qube.websiteContent.html}
      <div class="website-preview">
        <h2>Website Preview</h2>
        <iframe srcdoc="{qube.websiteContent.html}" class="preview-frame"></iframe>
      </div>
    {/if}

    {#if qube.paymentConfig.price}
      <div class="purchase-section">
        <h3>Purchase This Vybe Qube</h3>
        <p class="price">${qube.paymentConfig.price}</p>
        <button on:click={handlePurchase} class="purchase-btn">
          Buy Now
        </button>
      </div>
    {/if}
  </div>
</div>

<style>
  .vybe-qube {
    max-width: 900px;
    margin: 0 auto;
    padding: 2rem;
  }

  .qube-header {
    text-align: center;
    margin-bottom: 2rem;
  }

  .preview-frame {
    width: 100%;
    height: 400px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
  }

  .purchase-section {
    background: #f7fafc;
    padding: 2rem;
    border-radius: 8px;
    text-align: center;
    margin-top: 2rem;
  }

  .price {
    font-size: 2rem;
    font-weight: bold;
    color: #2d3748;
    margin: 1rem 0;
  }

  .purchase-btn {
    background: #4299e1;
    color: white;
    padding: 1rem 2rem;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    cursor: pointer;
  }

  .purchase-btn:hover {
    background: #3182ce;
  }
</style>