<script lang="ts">
  import { onMount } from 'svelte';

  // Vybe Qube data
  const qube = {
  "type": "vybe_qube",
  "title": "Overwatch 2 Stadium Builds and Map Creation - Interactive Vybe Qube",
  "description": "Advanced interactive application for Overwatch 2 players and map creators. Built using modern web technologies with real-time features.\n\n*Reference documentation: docs/OW Stadium Build Creator - START HERE.csv*\n\n*Additional requirements: Focus on stadium builds, map creation tools, and competitive gameplay features for Overwatch 2*",
  "features": [
    "Responsive design optimized for all devices",
    "Real-time data updates and synchronization",
    "Interactive user interface with smooth animations",
    "Advanced functionality tailored for target audience",
    "Offline capability and data persistence",
    "Integration with external APIs and services"
  ],
  "techStack": [
    "SvelteKit",
    "TypeScript",
    "Tailwind CSS",
    "WebSocket",
    "PWA"
  ],
  "target_audience": "Overwatch 2 players and map creators",
  "complexity_level": "advanced",
  "generated_at": "2025-06-06T02:14:35.507Z",
  "agents_used": [
    "VYBA",
    "CODEX",
    "VYBRO",
    "PIXY",
    "DUCKY",
    "HAPPY"
  ],
  "inspirationSources": [],
  "supportingDocs": [
    "docs/OW Stadium Build Creator - START HERE.csv"
  ],
  "deploymentReady": true,
  "agent_activities": [
    "CODEX reading document: docs/OW Stadium Build Creator - START HERE.csv",
    "QUBERT analyzing business requirements and complexity",
    "CODEX architecting technical solution",
    "VYBRO implementing core functionality",
    "PIXY designing user interface",
    "DUCKY ensuring quality and testing",
    "HAPPY coordinating deployment pipeline"
  ]
};

  onMount(() => {
    // Track Vybe Qube view
    console.log('Vybe Qube viewed:', qube.title);
  });
</script>

<svelte:head>
  <title>{qube.title} - VybeCoding.ai Vybe Qube</title>
  <meta name="description" content={qube.description || ''} />
</svelte:head>

<div class="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900">
  <div class="container mx-auto px-4 py-8">
    <!-- Qube Header -->
    <div class="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 mb-8">
      <div class="flex flex-wrap gap-2 mb-4">
        <span class="bg-cyan-600 text-white px-3 py-1 rounded-full text-sm">
          {qube.complexity_level || 'Advanced'}
        </span>
        <span class="bg-purple-600 text-white px-3 py-1 rounded-full text-sm">
          Interactive Demo
        </span>
        <span class="bg-orange-600 text-white px-3 py-1 rounded-full text-sm">
          Generated by MAS
        </span>
      </div>

      <h1 class="text-4xl font-bold text-white mb-4">{qube.title}</h1>
      <p class="text-xl text-gray-300 mb-4">{qube.description}</p>

      <div class="text-sm text-gray-400">
        <span>Target Audience: {qube.target_audience || qube.targetAudience || 'Developers'}</span>
        <span class="ml-4">Generated: {new Date(qube.generated_at || qube.createdAt || Date.now()).toLocaleDateString()}</span>
      </div>
    </div>

    <!-- Features -->
    {#if qube.features && qube.features.length > 0}
      <div class="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 mb-8">
        <h2 class="text-2xl font-bold text-white mb-4">Features</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          {#each qube.features as feature}
            <div class="bg-gray-700/50 rounded-lg p-4">
              <div class="text-cyan-400 font-semibold">✨ {feature}</div>
            </div>
          {/each}
        </div>
      </div>
    {/if}

    <!-- Tech Stack -->
    {#if qube.techStack && qube.techStack.length > 0}
      <div class="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 mb-8">
        <h2 class="text-2xl font-bold text-white mb-4">Technology Stack</h2>
        <div class="flex flex-wrap gap-2">
          {#each qube.techStack as tech}
            <span class="bg-gradient-to-r from-cyan-600 to-purple-600 text-white px-3 py-1 rounded-full text-sm">
              {tech}
            </span>
          {/each}
        </div>
      </div>
    {/if}

    <!-- Demo Placeholder -->
    <div class="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 mb-8">
      <h2 class="text-2xl font-bold text-white mb-4">Interactive Demo</h2>
      <div class="bg-gray-900 rounded-lg p-8 text-center">
        <div class="text-6xl mb-4">🚀</div>
        <h3 class="text-xl text-white mb-2">Demo Coming Soon</h3>
        <p class="text-gray-400">This Vybe Qube demo is being prepared by the MAS agents.</p>
        {#if qube.deploymentReady}
          <div class="mt-4">
            <span class="bg-green-600 text-white px-3 py-1 rounded-full text-sm">
              Deployment Ready
            </span>
          </div>
        {/if}
      </div>
    </div>

    <!-- Qube Metadata -->
    <div class="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6">
      <h3 class="text-xl font-bold text-white mb-4">Development Information</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
        {#if qube.agents_used}
          <div class="md:col-span-2">
            <span class="text-gray-400">MAS Agents:</span>
            <span class="text-cyan-400 ml-2">{qube.agents_used.join(', ')}</span>
          </div>
        {/if}
        {#if qube.inspirationSources && qube.inspirationSources.length > 0}
          <div class="md:col-span-2">
            <span class="text-gray-400">Inspiration:</span>
            <div class="mt-2">
              {#each qube.inspirationSources as source}
                <a href={source} class="text-cyan-400 hover:text-cyan-300 block">• {source}</a>
              {/each}
            </div>
          </div>
        {/if}
      </div>
    </div>
  </div>
</div>