<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { page } from '$app/stores';
  import { userVybeQubeService } from '$lib/services/userVybeQubes';
  import { authService } from '$lib/services/auth';
  import type { VybeQubeSubmission } from '$lib/types';
  import { Button, Input, Card, Badge } from '$lib/components/ui';
  import {
    Upload,
    ExternalLink,
    Code,
    DollarSign,
    BookOpen,
  } from 'lucide-svelte';

  let loading = false;
  let error: string | null = null;
  let success = false;

  // Form data
  let submission: VybeQubeSubmission = {
    title: '',
    description: '',
    category: '',
    tags: [],
    websiteUrl: '',
    sourceCodeUrl: '',
    technologies: [],
    vybeMethodUsed: [],
    learningOutcomes: [],
    isMonetized: false,
    revenueModel: [],
    monthlyRevenue: 0,
  };

  let tagInput = '';
  let technologyInput = '';
  let learningOutcomeInput = '';
  let previewFile: File | null = null;

  // Available options
  let categories: string[] = [];
  let vybeMethodComponents: string[] = [];
  let revenueModels = [
    'Advertising',
    'Affiliate Marketing',
    'Product Sales',
    'Subscriptions',
    'Donations',
    'Freelance Services',
    'Course Sales',
    'Other',
  ];

  onMount(async () => {
    categories = userVybeQubeService.getCategories();
    vybeMethodComponents = userVybeQubeService.getVybeMethodComponents();
  });

  function addTag() {
    if (tagInput.trim() && !submission.tags.includes(tagInput.trim())) {
      submission.tags = [...submission.tags, tagInput.trim()];
      tagInput = '';
    }
  }

  function removeTag(tag: string) {
    submission.tags = submission.tags.filter(t => t !== tag);
  }

  function addTechnology() {
    if (
      technologyInput.trim() &&
      !submission.technologies.includes(technologyInput.trim())
    ) {
      submission.technologies = [
        ...submission.technologies,
        technologyInput.trim(),
      ];
      technologyInput = '';
    }
  }

  function removeTechnology(tech: string) {
    submission.technologies = submission.technologies.filter(t => t !== tech);
  }

  function addLearningOutcome() {
    if (
      learningOutcomeInput.trim() &&
      !submission.learningOutcomes.includes(learningOutcomeInput.trim())
    ) {
      submission.learningOutcomes = [
        ...submission.learningOutcomes,
        learningOutcomeInput.trim(),
      ];
      learningOutcomeInput = '';
    }
  }

  function removeLearningOutcome(outcome: string) {
    submission.learningOutcomes = submission.learningOutcomes.filter(
      o => o !== outcome
    );
  }

  function toggleVybeMethod(component: string) {
    if (submission.vybeMethodUsed.includes(component)) {
      submission.vybeMethodUsed = submission.vybeMethodUsed.filter(
        c => c !== component
      );
    } else {
      submission.vybeMethodUsed = [...submission.vybeMethodUsed, component];
    }
  }

  function toggleRevenueModel(model: string) {
    if (submission.revenueModel?.includes(model)) {
      submission.revenueModel = submission.revenueModel.filter(
        m => m !== model
      );
    } else {
      submission.revenueModel = [...(submission.revenueModel || []), model];
    }
  }

  function handleFileUpload(event: Event) {
    const target = event.target as HTMLInputElement;
    if (target.files && target.files[0]) {
      previewFile = target.files[0];
    }
  }

  async function handleSubmit() {
    if (!validateForm()) return;

    loading = true;
    error = null;

    try {
      // Get user data from auth service
      const currentUser = authService.getCurrentUser();
      const userId = currentUser?.id || 'anonymous';
      const userName = currentUser?.name || 'Anonymous User';

      const submissionData = {
        ...submission,
        previewImage: previewFile || undefined,
      };

      await userVybeQubeService.submitVybeQube(
        userId,
        userName,
        submissionData
      );
      success = true;

      // Use real timing for navigation delay
      if (typeof requestIdleCallback !== 'undefined') {
        requestIdleCallback(() => {
          goto('/vybeqube');
        }, { timeout: 2000 });
      } else {
        // Fallback for environments without requestIdleCallback
        setTimeout(() => {
          goto('/vybeqube');
        }, 2000);
      }
    } catch (err) {
      error = err instanceof Error ? err.message : 'Failed to submit Vybe Qube';
    } finally {
      loading = false;
    }
  }

  function validateForm(): boolean {
    if (!submission.title.trim()) {
      error = 'Title is required';
      return false;
    }
    if (!submission.description.trim()) {
      error = 'Description is required';
      return false;
    }
    if (!submission.category) {
      error = 'Category is required';
      return false;
    }
    if (!submission.websiteUrl.trim()) {
      error = 'Website URL is required';
      return false;
    }
    if (submission.vybeMethodUsed.length === 0) {
      error = 'Please select at least one Vybe Method component you used';
      return false;
    }
    if (submission.learningOutcomes.length === 0) {
      error = 'Please add at least one learning outcome';
      return false;
    }
    return true;
  }
</script>

<svelte:head>
  <title>Submit Your Vybe Qube - VybeCoding.ai</title>
  <meta
    name="description"
    content="Share your website built with the Vybe Method and inspire other learners."
  />
</svelte:head>

<div class="container mx-auto px-4 py-8 max-w-4xl">
  <div class="mb-8">
    <h1 class="text-3xl font-bold text-gray-900 mb-2">Submit Your Vybe Qube</h1>
    <p class="text-gray-600">
      Share your website built with the Vybe Method and inspire other learners.
      Your submission will be reviewed before being featured in our showcase.
    </p>
  </div>

  {#if success}
    <Card class="p-6 text-center">
      <div class="text-green-600 mb-4">
        <BookOpen class="w-12 h-12 mx-auto" />
      </div>
      <h2 class="text-xl font-semibold text-gray-900 mb-2">
        Submission Successful!
      </h2>
      <p class="text-gray-600 mb-4">
        Your Vybe Qube has been submitted for review. You'll be notified once
        it's approved.
      </p>
      <Button href="/vybeqube">View All Vybe Qubes</Button>
    </Card>
  {:else}
    <form on:submit|preventDefault={handleSubmit} class="space-y-8">
      <!-- Basic Information -->
      <Card class="p-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">
          Basic Information
        </h2>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label
              for="title"
              class="block text-sm font-medium text-gray-700 mb-2"
              >Project Title *</label
            >
            <Input
              id="title"
              bind:value={submission.title}
              placeholder="Project title"
              required
            />
          </div>

          <div>
            <label
              for="category"
              class="block text-sm font-medium text-gray-700 mb-2"
              >Category *</label
            >
            <select
              id="category"
              bind:value={submission.category}
              class="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            >
              <option value="">Select category</option>
              {#each categories as category}
                <option value={category}>{category}</option>
              {/each}
            </select>
          </div>
        </div>

        <div class="mt-6">
          <label
            for="description"
            class="block text-sm font-medium text-gray-700 mb-2"
            >Description *</label
          >
          <textarea
            id="description"
            bind:value={submission.description}
            placeholder="Project description"
            rows="4"
            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            required
          ></textarea>
        </div>

        <div class="mt-6">
          <label
            for="websiteUrl"
            class="block text-sm font-medium text-gray-700 mb-2"
            >Website URL *</label
          >
          <div class="flex">
            <Input
              id="websiteUrl"
              bind:value={submission.websiteUrl}
              placeholder="Website URL"
              type="url"
              required
              class="flex-1"
            />
            <Button
              type="button"
              variant="outline"
              size="sm"
              class="ml-2"
              href={submission.websiteUrl}
              target="_blank"
              disabled={!submission.websiteUrl}
            >
              <ExternalLink class="w-4 h-4" />
            </Button>
          </div>
        </div>

        <div class="mt-6">
          <label
            for="sourceCodeUrl"
            class="block text-sm font-medium text-gray-700 mb-2"
            >Source Code URL (Optional)</label
          >
          <div class="flex">
            <Input
              id="sourceCodeUrl"
              bind:value={submission.sourceCodeUrl}
              placeholder="Source code URL"
              type="url"
              class="flex-1"
            />
            <Button
              type="button"
              variant="outline"
              size="sm"
              class="ml-2"
              href={submission.sourceCodeUrl}
              target="_blank"
              disabled={!submission.sourceCodeUrl}
            >
              <Code class="w-4 h-4" />
            </Button>
          </div>
        </div>
      </Card>

      <!-- Tags and Technologies -->
      <Card class="p-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">
          Tags & Technologies
        </h2>

        <div class="space-y-6">
          <div>
            <label
              for="tags"
              class="block text-sm font-medium text-gray-700 mb-2">Tags</label
            >
            <div class="flex gap-2 mb-2">
              <Input
                id="tags"
                bind:value={tagInput}
                placeholder="Add a tag..."
                onkeydown={(e: KeyboardEvent) =>
                  e.key === 'Enter' && (e.preventDefault(), addTag())}
                class="flex-1"
              />
              <Button type="button" variant="outline" onclick={addTag}
                >Add</Button
              >
            </div>
            <div class="flex flex-wrap gap-2">
              {#each submission.tags as tag}
                <Badge
                  variant="secondary"
                  class="cursor-pointer"
                  onclick={() => removeTag(tag)}
                >
                  {tag} ×
                </Badge>
              {/each}
            </div>
          </div>

          <div>
            <label
              for="technologies"
              class="block text-sm font-medium text-gray-700 mb-2"
              >Technologies Used</label
            >
            <div class="flex gap-2 mb-2">
              <Input
                id="technologies"
                bind:value={technologyInput}
                placeholder="e.g., React, Node.js, MongoDB..."
                onkeydown={(e: KeyboardEvent) =>
                  e.key === 'Enter' && (e.preventDefault(), addTechnology())}
                class="flex-1"
              />
              <Button type="button" variant="outline" onclick={addTechnology}
                >Add</Button
              >
            </div>
            <div class="flex flex-wrap gap-2">
              {#each submission.technologies as tech}
                <Badge
                  variant="outline"
                  class="cursor-pointer"
                  onclick={() => removeTechnology(tech)}
                >
                  {tech} ×
                </Badge>
              {/each}
            </div>
          </div>
        </div>
      </Card>

      <!-- Submit Button -->
      <div class="flex justify-end gap-4">
        <Button type="button" variant="outline" href="/vybeqube">Cancel</Button>
        <Button type="submit" disabled={loading}>
          {loading ? 'Submitting...' : 'Submit Vybe Qube'}
        </Button>
      </div>

      {#if error}
        <div class="bg-red-50 border border-red-200 rounded-md p-4">
          <p class="text-red-800">{error}</p>
        </div>
      {/if}
    </form>
  {/if}
</div>
