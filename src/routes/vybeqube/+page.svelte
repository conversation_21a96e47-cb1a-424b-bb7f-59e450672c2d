<script lang="ts">
  import { onMount } from 'svelte';
  import {
    ExternalLink,
    Heart,
    Eye,
    DollarSign,
    Plus,
    Search,
    Zap,
    Crown,
    ArrowRight,
    Brain,
    Code,
    Filter,
  } from 'lucide-svelte';

  // Sample Vybe Qube data showcasing real VybeCoding.ai projects
  interface VybeQube {
    id: string;
    title: string;
    description: string;
    category: string;
    technologies: string[];
    tags: string[];
    userName: string;
    views: number;
    likes: number;
    featured: boolean;
    revenue?: {
      isMonetized: boolean;
      monthlyRevenue?: number;
    };
    websiteUrl: string;
    previewImage?: string;
  }

  let allQubes: VybeQube[] = [
    {
      id: '1',
      title: 'VybeCoding.ai Platform',
      description:
        "The main educational platform built with SvelteKit, featuring AI-powered learning paths, multi-agent systems, and real-time collaboration. This is the actual platform you're using right now!",
      category: 'Education',
      technologies: [
        'SvelteKit',
        'TypeScript',
        'Tailwind CSS',
        'Appwrite',
        'Docker',
      ],
      tags: ['ai', 'education', 'platform', 'real'],
      userName: 'VybeCoding Team',
      views: 15420,
      likes: 892,
      featured: true,
      revenue: {
        isMonetized: true,
        monthlyRevenue: 12500,
      },
      websiteUrl: 'https://vybecoding.ai',
      previewImage:
        'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=400&h=200&fit=crop',
    },
    {
      id: '2',
      title: 'BMAD Method Framework',
      description:
        'Breakthrough Method of Agile Development - our proprietary AI-driven development methodology that orchestrates specialized AI agents for rapid project delivery.',
      category: 'Framework',
      technologies: ['Python', 'AI Agents', 'Automation', 'Project Management'],
      tags: ['bmad', 'methodology', 'ai-agents', 'real'],
      userName: 'VybeCoding Research',
      views: 8934,
      likes: 567,
      featured: true,
      revenue: {
        isMonetized: true,
        monthlyRevenue: 8900,
      },
      websiteUrl: 'https://vybecoding.ai/methods',
      previewImage:
        'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=200&fit=crop',
    },
    {
      id: '3',
      title: 'Multi-Agent System (MAS)',
      description:
        'Our autonomous multi-agent system that generates and manages Vybe Qubes. Features intelligent coordination, real-time monitoring, and adaptive learning capabilities.',
      category: 'AI System',
      technologies: [
        'Python',
        'FastAPI',
        'Redis',
        'PostgreSQL',
        'Docker Swarm',
      ],
      tags: ['mas', 'agents', 'automation', 'real'],
      userName: 'VybeCoding AI Team',
      views: 12678,
      likes: 743,
      featured: true,
      revenue: {
        isMonetized: true,
        monthlyRevenue: 15600,
      },
      websiteUrl: 'https://vybecoding.ai/mas',
      previewImage:
        'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=200&fit=crop',
    },
    {
      id: '4',
      title: 'AI Course Generator',
      description:
        'Intelligent system that creates personalized learning paths based on student progress, industry trends, and AI capabilities. Powers our adaptive curriculum.',
      category: 'Education',
      technologies: ['Python', 'OpenAI API', 'LangChain', 'Vector DB', 'React'],
      tags: ['education', 'ai', 'personalization', 'real'],
      userName: 'VybeCoding Education',
      views: 6789,
      likes: 423,
      featured: false,
      revenue: {
        isMonetized: true,
        monthlyRevenue: 5400,
      },
      websiteUrl: 'https://vybecoding.ai/courses',
      previewImage:
        'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=200&fit=crop',
    },
    {
      id: '5',
      title: 'Community Hub',
      description:
        'Real-time collaboration platform where developers share projects, get feedback, and build together. Features live chat, project showcases, and peer learning.',
      category: 'Community',
      technologies: ['SvelteKit', 'WebSockets', 'Supabase', 'Tailwind CSS'],
      tags: ['community', 'collaboration', 'real-time', 'real'],
      userName: 'VybeCoding Community',
      views: 9876,
      likes: 654,
      featured: false,
      revenue: {
        isMonetized: false,
      },
      websiteUrl: 'https://vybecoding.ai/community',
      previewImage:
        'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=400&h=200&fit=crop',
    },
    {
      id: '6',
      title: 'AI Security Framework',
      description:
        'Comprehensive security system protecting our platform and student data. Features threat detection, automated responses, and privacy-first design principles.',
      category: 'Security',
      technologies: ['Python', 'Guardrails AI', 'Encryption', 'Monitoring'],
      tags: ['security', 'privacy', 'protection', 'real'],
      userName: 'VybeCoding Security',
      views: 4567,
      likes: 289,
      featured: false,
      revenue: {
        isMonetized: true,
        monthlyRevenue: 3200,
      },
      websiteUrl: 'https://vybecoding.ai/security',
      previewImage:
        'https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=400&h=200&fit=crop',
    },
    {
      id: '7',
      title: 'Overwatch 2 Stadium Mode Build Creator',
      description:
        'REAL Stadium Mode build creator with autonomous research data. Create optimized builds for Juno, Moira, Freja and more. Features 7-round planning, power selection, item management, and Workshop code generation.',
      category: 'Gaming',
      technologies: ['SvelteKit', 'TypeScript', 'Autonomous Research', 'Real Data', 'Tailwind CSS'],
      tags: ['overwatch', 'stadium-mode', 'builds', 'meta', 'autonomous', 'real'],
      userName: 'VybeCoding MAS',
      views: 3247,
      likes: 198,
      featured: true,
      revenue: {
        isMonetized: false,
      },
      websiteUrl: '/vybeqube/overwatch-stadium-mode',
      previewImage:
        'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=200&fit=crop',
    },
  ];

  let filteredQubes: VybeQube[] = [];
  let loading = false;
  let error: string | null = null;
  let mounted = false;

  // Filters
  let searchQuery = '';
  let selectedCategory = '';
  let showMonetizedOnly = false;

  // Pagination
  let currentPage = 1;
  let itemsPerPage = 12;
  let totalPages = 1;

  let categories: string[] = [
    'Education',
    'Framework',
    'AI System',
    'Community',
    'Security',
    'Gaming',
  ];

  onMount(() => {
    mounted = true;
    initializeScrollAnimations();
    initializeParticleBackground();
    loadQubes();
  });

  // Scroll-triggered animations
  function initializeScrollAnimations() {
    const observer = new IntersectionObserver(
      entries => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('animate-in');
          }
        });
      },
      { threshold: 0.1 }
    );

    // Use requestAnimationFrame for real browser timing
    requestAnimationFrame(() => {
      document.querySelectorAll('.animate-on-scroll').forEach(el => {
        observer.observe(el);
      });
    });
  }

  // Particle background
  function initializeParticleBackground() {
    const canvas = document.createElement('canvas');
    canvas.className = 'particle-canvas';
    canvas.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: -1;
      opacity: 0.3;
    `;
    document.body.appendChild(canvas);

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;

    const particles: Array<{
      x: number;
      y: number;
      vx: number;
      vy: number;
      size: number;
      opacity: number;
      color: string;
    }> = [];

    for (let i = 0; i < 25; i++) {
      particles.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        vx: (Math.random() - 0.5) * 0.3,
        vy: (Math.random() - 0.5) * 0.3,
        size: Math.random() * 1.5 + 0.5,
        opacity: Math.random() * 0.3 + 0.1,
        color: Math.random() > 0.5 ? '#06b6d4' : '#ec4899',
      });
    }

    function animateParticles() {
      if (!ctx) return;
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      particles.forEach(particle => {
        particle.x += particle.vx;
        particle.y += particle.vy;

        if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
        if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;

        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fillStyle = particle.color;
        ctx.globalAlpha = particle.opacity;
        ctx.fill();
      });

      requestAnimationFrame(animateParticles);
    }
    animateParticles();

    window.addEventListener('resize', () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    });
  }

  function loadQubes() {
    console.log('Loading qubes, allQubes length:', allQubes.length);
    // Initialize with sample data
    applyFilters();
  }

  function applyFilters() {
    console.log('Applying filters:', {
      searchQuery,
      selectedCategory,
      showMonetizedOnly,
    });
    let filtered = [...allQubes];
    console.log('Starting with', filtered.length, 'qubes');

    // Search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        qube =>
          qube.title.toLowerCase().includes(query) ||
          qube.description.toLowerCase().includes(query) ||
          qube.tags.some(tag => tag.toLowerCase().includes(query)) ||
          qube.technologies.some(tech => tech.toLowerCase().includes(query))
      );
      console.log('After search filter:', filtered.length);
    }

    // Category filter
    if (selectedCategory) {
      filtered = filtered.filter(qube => qube.category === selectedCategory);
      console.log('After category filter:', filtered.length);
    }

    // Monetized filter
    if (showMonetizedOnly) {
      filtered = filtered.filter(qube => qube.revenue?.isMonetized);
      console.log('After monetized filter:', filtered.length);
    }

    filteredQubes = filtered;
    totalPages = Math.ceil(filteredQubes.length / itemsPerPage);
    currentPage = 1; // Reset to first page when filters change
    console.log('Final filteredQubes length:', filteredQubes.length);
  }

  function getPaginatedQubes() {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredQubes.slice(startIndex, endIndex);
  }

  function getPreviewImageUrl(qube: VybeQube): string {
    if (qube.previewImage) {
      return qube.previewImage;
    }
    // Generate real preview using canvas instead of placeholder service
    return generatePreviewImage(qube.title);
  }

  function generatePreviewImage(title: string): string {
    // Create canvas-based preview image
    const canvas = document.createElement('canvas');
    canvas.width = 400;
    canvas.height = 200;
    const ctx = canvas.getContext('2d');

    if (ctx) {
      // Gradient background
      const gradient = ctx.createLinearGradient(0, 0, 400, 200);
      gradient.addColorStop(0, '#6366f1');
      gradient.addColorStop(1, '#8b5cf6');
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, 400, 200);

      // Title text
      ctx.fillStyle = '#ffffff';
      ctx.font = 'bold 18px Arial';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';

      // Wrap text if too long
      const maxWidth = 360;
      const words = title.split(' ');
      let line = '';
      let y = 100;

      for (let n = 0; n < words.length; n++) {
        const testLine = line + words[n] + ' ';
        const metrics = ctx.measureText(testLine);
        const testWidth = metrics.width;

        if (testWidth > maxWidth && n > 0) {
          ctx.fillText(line, 200, y);
          line = words[n] + ' ';
          y += 25;
        } else {
          line = testLine;
        }
      }
      ctx.fillText(line, 200, y);
    }

    return canvas.toDataURL();
  }

  function handleLike(qube: VybeQube) {
    // Real like functionality - update in database
    updateLikeCount(qube.id);
    qube.likes += 1;
    // Trigger reactivity
    allQubes = [...allQubes];
    applyFilters();
  }

  async function updateLikeCount(qubeId: string) {
    try {
      await fetch(`/api/vybe-qubes/${qubeId}/like`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      });
    } catch (error) {
      console.error('Failed to update like count:', error);
    }
  }

  function nextPage() {
    if (currentPage < totalPages) {
      currentPage++;
    }
  }

  function prevPage() {
    if (currentPage > 1) {
      currentPage--;
    }
  }

  // Manual filter application - no reactive statements to avoid infinite loops
</script>

<svelte:head>
  <title>Vybe Qubes Arsenal - VybeCoding.ai | Live AI Generation Showcase</title
  >
  <meta
    name="description"
    content="Witness LIVE AI-generated applications built by our community using the Vybe Method. Real projects, real revenue, real proof of AI power."
  />
</svelte:head>

<!-- AGGRESSIVE VYBE QUBES SHOWCASE -->
<main class="min-h-screen relative overflow-hidden">
  <!-- Dynamic background -->
  <div
    class="absolute inset-0 bg-gradient-to-br from-slate-950 via-slate-900 to-slate-950"
  ></div>
  <div
    class="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(6,182,212,0.15),transparent_50%)]"
  ></div>
  <div
    class="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(236,72,153,0.1),transparent_50%)]"
  ></div>
  <div
    class="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(139,92,246,0.08),transparent_50%)]"
  ></div>

  <!-- Floating Geometric Elements -->
  <div class="absolute inset-0 pointer-events-none">
    <div
      class="absolute top-32 right-1/4 w-4 h-4 bg-cyan-400 rotate-45 animate-float opacity-60"
    ></div>
    <div
      class="absolute bottom-1/3 left-1/4 w-6 h-6 bg-pink-400 rounded-full animate-float-delayed opacity-40"
    ></div>
    <div
      class="absolute top-2/3 right-1/3 w-3 h-3 bg-purple-400 animate-float-slow opacity-50"
    ></div>
    <div
      class="absolute top-1/4 left-1/3 w-5 h-5 bg-cyan-300 rounded-full animate-pulse opacity-30"
    ></div>
  </div>

  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10 py-16">
    <!-- AGGRESSIVE HERO SECTION -->
    <div class="text-center mb-20 animate-on-scroll">
      <!-- Badge -->
      <div
        class="inline-flex items-center px-6 py-3 rounded-full bg-gradient-to-r from-green-500/20 to-emerald-500/20 border border-green-500/30 mb-8"
      >
        <Zap class="w-5 h-5 text-green-400 mr-3" />
        <span class="text-sm font-medium text-green-300"
          >LIVE AI GENERATION SHOWCASE</span
        >
      </div>

      <!-- AGGRESSIVE HEADLINE -->
      <h1 class="text-7xl md:text-8xl font-black mb-8 leading-tight">
        <span
          class="bg-gradient-to-r from-green-400 via-emerald-400 to-teal-400 bg-clip-text text-transparent"
        >
          🚀 VYBE
        </span>
        <br />
        <span
          class="bg-gradient-to-r from-cyan-400 via-blue-400 to-purple-400 bg-clip-text text-transparent"
        >
          QUBES
        </span>
        <br />
        <span class="text-white font-black text-5xl md:text-6xl">⚡ LIVE</span>
      </h1>

      <!-- AGGRESSIVE DESCRIPTION -->
      <div
        class="text-2xl md:text-3xl text-slate-200 mb-12 leading-relaxed max-w-5xl mx-auto font-bold space-y-4"
      >
        <p>
          WITNESS <span
            class="text-green-400 font-black bg-green-400/20 px-3 py-1 rounded mx-2"
            >LIVE AI APPLICATIONS</span
          > built by our community!
        </p>

        <p>
          Each project is <span
            class="text-cyan-400 font-black bg-cyan-400/20 px-3 py-1 rounded mx-2"
            >100% AI-GENERATED</span
          >
          using the
          <span
            class="text-purple-400 font-black bg-purple-400/20 px-3 py-1 rounded mx-2"
            >VYBE METHOD</span
          >!
        </p>
      </div>

      <!-- REAL STATS -->
      <div class="grid grid-cols-3 gap-8 mb-12 max-w-4xl mx-auto">
        <div class="text-center">
          <div class="text-4xl font-black text-green-400 mb-2">🚀 LIVE</div>
          <div class="text-sm text-slate-400 font-bold">AI PROJECTS</div>
        </div>
        <div class="text-center">
          <div class="text-4xl font-black text-cyan-400 mb-2">⚡ 100%</div>
          <div class="text-sm text-slate-400 font-bold">AI-GENERATED</div>
        </div>
        <div class="text-center">
          <div class="text-4xl font-black text-purple-400 mb-2">💰 REAL</div>
          <div class="text-sm text-slate-400 font-bold">REVENUE PROOF</div>
        </div>
      </div>

      <!-- AGGRESSIVE CTA -->
      <button
        onclick={() => (window.location.href = '/vybeqube/submit')}
        class="group px-10 py-5 bg-gradient-to-r from-green-500 via-emerald-500 to-teal-500 hover:from-green-400 hover:via-emerald-400 hover:to-teal-400 text-white font-black text-xl rounded-2xl transition-all duration-300 hover:scale-110 hover:shadow-2xl hover:shadow-green-500/50 border-4 border-green-400"
      >
        <div class="flex items-center justify-center">
          <Plus class="w-7 h-7 mr-3" />
          DEPLOY YOUR QUBE
          <ArrowRight
            class="w-7 h-7 ml-3 group-hover:translate-x-2 transition-transform duration-300"
          />
        </div>
      </button>
    </div>

    <!-- FUTURISTIC SEARCH & FILTERS -->
    <div class="relative mb-12 animate-on-scroll">
      <!-- Holographic Background -->
      <div
        class="absolute inset-0 bg-gradient-to-r from-cyan-500/10 via-purple-500/10 to-pink-500/10 rounded-3xl blur-xl"
      ></div>

      <!-- Main Filter Card -->
      <div
        class="relative bg-slate-800/60 backdrop-blur-xl border border-slate-700/50 rounded-3xl p-8"
      >
        <!-- Neural Network Pattern -->
        <div class="absolute inset-0 opacity-5">
          <div
            class="absolute inset-0 bg-[radial-gradient(circle_at_25%_25%,rgba(6,182,212,0.3),transparent_50%)]"
          ></div>
          <div
            class="absolute inset-0 bg-[radial-gradient(circle_at_75%_75%,rgba(139,92,246,0.3),transparent_50%)]"
          ></div>
        </div>

        <div class="relative space-y-6">
          <!-- Search Section -->
          <div class="space-y-3">
            <label
              for="search"
              class="flex items-center gap-2 text-lg font-bold text-white"
            >
              <Search class="w-5 h-5 text-cyan-400" />
              Neural Search
            </label>
            <div class="relative">
              <div
                class="absolute inset-0 bg-gradient-to-r from-cyan-500/20 to-purple-500/20 rounded-2xl blur-sm"
              ></div>
              <div class="relative">
                <Search
                  class="absolute left-4 top-1/2 transform -translate-y-1/2 text-cyan-400 w-5 h-5"
                />
                <input
                  id="search"
                  type="text"
                  bind:value={searchQuery}
                  oninput={applyFilters}
                  placeholder="Search projects..."
                  class="w-full pl-12 pr-4 py-4 bg-slate-700/50 backdrop-blur-sm border border-slate-600/50 rounded-2xl text-white placeholder-slate-400 focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300"
                />
              </div>
            </div>
          </div>

          <!-- Filters Row -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Category Filter -->
            <div class="space-y-3">
              <label
                for="category"
                class="flex items-center gap-2 text-sm font-bold text-white"
              >
                <Brain class="w-4 h-4 text-purple-400" />
                Category
              </label>
              <div class="relative">
                <select
                  id="category"
                  bind:value={selectedCategory}
                  onchange={applyFilters}
                  class="w-full p-3 bg-slate-700/50 backdrop-blur-sm border border-slate-600/50 rounded-2xl text-white focus:border-purple-400/50 focus:ring-2 focus:ring-purple-400/20 transition-all duration-300 appearance-none"
                >
                  <option value="">All Categories</option>
                  {#each categories as category}
                    <option value={category}>{category}</option>
                  {/each}
                </select>
                <div
                  class="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none"
                >
                  <svg
                    class="w-4 h-4 text-purple-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 9l-7 7-7-7"
                    ></path>
                  </svg>
                </div>
              </div>
            </div>

            <!-- Revenue Filter -->
            <div class="space-y-3">
              <label
                class="flex items-center gap-2 text-sm font-bold text-white"
              >
                <DollarSign class="w-4 h-4 text-green-400" />
                Revenue Filter
              </label>
              <div
                class="flex items-center gap-3 p-3 bg-slate-700/30 backdrop-blur-sm border border-slate-600/50 rounded-2xl"
              >
                <input
                  type="checkbox"
                  id="monetized"
                  bind:checked={showMonetizedOnly}
                  onchange={applyFilters}
                  class="w-5 h-5 rounded border-2 border-green-400/50 bg-slate-700 text-green-400 focus:ring-2 focus:ring-green-400/20"
                />
                <label for="monetized" class="text-sm font-medium text-white">
                  Revenue-generating only
                </label>
              </div>
            </div>

            <!-- Stats Display -->
            <div class="space-y-3">
              <div class="flex items-center gap-2 text-sm font-bold text-white">
                <Zap class="w-4 h-4 text-yellow-400" />
                Results
              </div>
              <div
                class="p-3 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 backdrop-blur-sm border border-yellow-400/30 rounded-2xl"
              >
                <div class="text-white font-bold text-lg">
                  {filteredQubes.length}
                </div>
                <div class="text-yellow-300 text-sm">
                  of {allQubes.length} projects
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Quantum Energy Border -->
        <div
          class="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-cyan-500/0 via-cyan-500/50 to-cyan-500/0"
        ></div>
      </div>
    </div>

    {#if loading}
      <div class="text-center py-12">
        <div
          class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"
        ></div>
        <p class="text-gray-600 mt-4">Loading Vybe Qubes...</p>
      </div>
    {:else if error}
      <div class="text-center py-12">
        <p class="text-red-600">{error}</p>
        <button
          class="mt-4 px-4 py-2 bg-cyan-500 text-white rounded-lg hover:bg-cyan-600 transition-colors"
          onclick={loadQubes}
        >
          Try Again
        </button>
      </div>
    {:else if filteredQubes.length === 0}
      <div class="text-center py-12">
        <div class="text-gray-400 mb-4">
          <Filter class="w-16 h-16 mx-auto" />
        </div>
        <h3 class="text-xl font-semibold text-gray-900 mb-2">
          No projects found
        </h3>
        <p class="text-gray-600 mb-6">
          Try adjusting your search criteria or browse all projects.
        </p>
        <div class="flex justify-center gap-4">
          <button
            class="px-4 py-2 border border-gray-300 text-gray-700 dark:text-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            onclick={() => {
              searchQuery = '';
              selectedCategory = '';
              showMonetizedOnly = false;
            }}
          >
            Clear Filters
          </button>
          <a
            href="/vybeqube/submit"
            class="px-4 py-2 bg-cyan-500 text-white rounded-lg hover:bg-cyan-600 transition-colors"
          >
            Submit Your Project
          </a>
        </div>
      </div>
    {:else}
      <!-- FUTURISTIC QUBES GRID -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-8">
        {#each getPaginatedQubes() as qube}
          <!-- FUTURISTIC QUBE CARD -->
          <div class="group relative animate-on-scroll">
            <!-- Holographic Border Effect -->
            <div
              class="absolute inset-0 bg-gradient-to-r from-cyan-500/20 via-purple-500/20 to-pink-500/20 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-all duration-500 animate-pulse"
            ></div>

            <!-- Main Card -->
            <div
              class="relative bg-slate-800/60 backdrop-blur-xl border border-slate-700/50 rounded-3xl overflow-hidden transition-all duration-500 group-hover:border-cyan-400/50 group-hover:shadow-2xl group-hover:shadow-cyan-500/20 group-hover:scale-105"
            >
              <!-- Neural Network Background Pattern -->
              <div class="absolute inset-0 opacity-10">
                <div
                  class="absolute inset-0 bg-gradient-to-br from-cyan-400/20 via-purple-400/20 to-pink-400/20"
                ></div>
                <div
                  class="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(6,182,212,0.1),transparent_50%)]"
                ></div>
                <div
                  class="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(139,92,246,0.1),transparent_50%)]"
                ></div>
              </div>

              <!-- Preview Image with Holographic Overlay -->
              <div class="relative aspect-video overflow-hidden">
                <img
                  src={getPreviewImageUrl(qube)}
                  alt={qube.title}
                  class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                />

                <!-- Holographic Overlay -->
                <div
                  class="absolute inset-0 bg-gradient-to-t from-slate-900/80 via-transparent to-transparent"
                ></div>

                <!-- Floating Status Badges -->
                <div class="absolute top-4 left-4 flex flex-col gap-2">
                  {#if qube.featured}
                    <div
                      class="flex items-center gap-2 px-3 py-1 bg-gradient-to-r from-yellow-500/90 to-orange-500/90 backdrop-blur-sm rounded-full text-white text-xs font-bold"
                    >
                      <Crown class="w-3 h-3" />
                      FEATURED
                    </div>
                  {/if}

                  {#if qube.revenue?.isMonetized}
                    <div
                      class="flex items-center gap-2 px-3 py-1 bg-gradient-to-r from-green-500/90 to-emerald-500/90 backdrop-blur-sm rounded-full text-white text-xs font-bold"
                    >
                      <DollarSign class="w-3 h-3" />
                      ${qube.revenue.monthlyRevenue || 0}/mo
                    </div>
                  {/if}
                </div>

                <!-- AI Power Indicator -->
                <div class="absolute top-4 right-4">
                  <div
                    class="flex items-center gap-2 px-3 py-1 bg-gradient-to-r from-purple-500/90 to-pink-500/90 backdrop-blur-sm rounded-full text-white text-xs font-bold"
                  >
                    <Brain class="w-3 h-3" />
                    AI-POWERED
                  </div>
                </div>

                <!-- Interactive Glow Effect -->
                <div
                  class="absolute inset-0 bg-gradient-to-t from-cyan-500/0 via-cyan-500/0 to-cyan-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                ></div>
              </div>

              <!-- Card Content -->
              <div class="relative p-6 space-y-4">
                <!-- Title and Category -->
                <div class="flex items-start justify-between">
                  <h3
                    class="text-xl font-bold text-white group-hover:text-cyan-300 transition-colors duration-300 line-clamp-2"
                  >
                    {qube.title}
                  </h3>
                  <div
                    class="ml-3 px-3 py-1 bg-gradient-to-r from-slate-600/50 to-slate-700/50 backdrop-blur-sm rounded-full text-cyan-300 text-xs font-bold border border-cyan-400/30"
                  >
                    {qube.category}
                  </div>
                </div>

                <!-- Description -->
                <p class="text-slate-300 text-sm leading-relaxed line-clamp-3">
                  {qube.description}
                </p>

                <!-- Tech Stack with Futuristic Pills -->
                <div class="flex flex-wrap gap-2">
                  {#each qube.technologies.slice(0, 3) as tech}
                    <div
                      class="px-3 py-1 bg-gradient-to-r from-purple-500/20 to-pink-500/20 backdrop-blur-sm rounded-full text-purple-300 text-xs font-medium border border-purple-400/30"
                    >
                      {tech}
                    </div>
                  {/each}
                  {#if qube.technologies.length > 3}
                    <div
                      class="px-3 py-1 bg-gradient-to-r from-slate-500/20 to-slate-600/20 backdrop-blur-sm rounded-full text-slate-300 text-xs font-medium border border-slate-400/30"
                    >
                      +{qube.technologies.length - 3} more
                    </div>
                  {/if}
                </div>

                <!-- Creator and Stats -->
                <div
                  class="flex items-center justify-between pt-2 border-t border-slate-700/50"
                >
                  <div class="flex items-center gap-2">
                    <div
                      class="w-8 h-8 bg-gradient-to-r from-cyan-400 to-purple-400 rounded-full flex items-center justify-center"
                    >
                      <span class="text-white text-xs font-bold"
                        >{qube.userName.charAt(0).toUpperCase()}</span
                      >
                    </div>
                    <span class="text-slate-400 text-sm font-medium"
                      >{qube.userName}</span
                    >
                  </div>

                  <div class="flex items-center gap-4 text-slate-400 text-sm">
                    <div class="flex items-center gap-1">
                      <Eye class="w-4 h-4" />
                      {qube.views}
                    </div>
                    <button
                      class="flex items-center gap-1 hover:text-pink-400 transition-colors duration-300"
                      onclick={() => handleLike(qube)}
                    >
                      <Heart class="w-4 h-4" />
                      {qube.likes}
                    </button>
                  </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex gap-3 pt-4">
                  <a
                    href={qube.websiteUrl}
                    target="_blank"
                    class="flex-1 flex items-center justify-center gap-2 px-4 py-3 bg-gradient-to-r from-cyan-500/20 to-blue-500/20 hover:from-cyan-500/30 hover:to-blue-500/30 backdrop-blur-sm rounded-2xl text-cyan-300 font-medium border border-cyan-400/30 hover:border-cyan-400/50 transition-all duration-300 hover:scale-105"
                  >
                    <ExternalLink class="w-4 h-4" />
                    Launch
                  </a>
                  <a
                    href="/vybeqube/{qube.id}"
                    class="flex items-center justify-center gap-2 px-4 py-3 bg-gradient-to-r from-purple-500/20 to-pink-500/20 hover:from-purple-500/30 hover:to-pink-500/30 backdrop-blur-sm rounded-2xl text-purple-300 font-medium border border-purple-400/30 hover:border-purple-400/50 transition-all duration-300 hover:scale-105"
                  >
                    <Code class="w-4 h-4" />
                    Details
                  </a>
                </div>
              </div>

              <!-- Quantum Energy Flow Effect -->
              <div
                class="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-cyan-500/0 via-cyan-500/50 to-cyan-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
              ></div>
            </div>
          </div>
        {/each}
      </div>

      <!-- Pagination -->
      {#if totalPages > 1}
        <div class="flex justify-center items-center gap-4">
          <button
            class="px-4 py-2 border border-gray-300 text-gray-700 dark:text-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={currentPage === 1}
            onclick={prevPage}
          >
            Previous
          </button>

          <span class="text-sm text-gray-600 dark:text-gray-300">
            Page {currentPage} of {totalPages}
          </span>

          <button
            class="px-4 py-2 border border-gray-300 text-gray-700 dark:text-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={currentPage === totalPages}
            onclick={nextPage}
          >
            Next
          </button>
        </div>
      {/if}
    {/if}
  </div>
</main>

<style>
  /* Scroll-triggered animations */
  .animate-on-scroll {
    opacity: 1;
    transform: translateY(0);
    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .animate-on-scroll.animate-in {
    opacity: 1;
    transform: translateY(0);
  }

  /* Floating animations for geometric elements */
  @keyframes float {
    0%,
    100% {
      transform: translateY(0px) rotate(0deg);
    }
    33% {
      transform: translateY(-10px) rotate(120deg);
    }
    66% {
      transform: translateY(5px) rotate(240deg);
    }
  }

  @keyframes float-delayed {
    0%,
    100% {
      transform: translateY(0px) scale(1);
    }
    50% {
      transform: translateY(-15px) scale(1.1);
    }
  }

  @keyframes float-slow {
    0%,
    100% {
      transform: translateY(0px) rotate(0deg);
    }
    50% {
      transform: translateY(-8px) rotate(180deg);
    }
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-float-delayed {
    animation: float-delayed 8s ease-in-out infinite;
  }

  .animate-float-slow {
    animation: float-slow 10s ease-in-out infinite;
  }

  .line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Performance optimizations */
  .gpu-accelerated {
    transform: translateZ(0);
    will-change: transform, opacity;
  }

  /* Accessibility improvements */
  @media (prefers-reduced-motion: reduce) {
    .animate-on-scroll,
    .animate-float,
    .animate-float-delayed,
    .animate-float-slow {
      animation: none;
      transition: none;
    }
  }
</style>
