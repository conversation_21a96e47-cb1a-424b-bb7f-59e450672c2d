<script lang="ts">
  import '../app.css';
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  // import { authService } from '$lib/services/auth';
  // import { userStore } from '$lib/stores/auth';
  import { themeActions } from '$lib/stores/theme';
  // import { initializeAccessibility } from '$lib/components/ui/utils/accessibility';
  import Header from '../lib/components/Header.svelte';
  import Footer from '../lib/components/Footer.svelte';
  import Toast from '../lib/components/Toast.svelte';
  import LoadingBar from '../lib/components/LoadingBar.svelte';
  import ErrorBoundary from '../lib/components/ErrorBoundary.svelte';

  let mounted = false;
  let loading = true;

  // Cursor and fluid effects have been removed - using default browser cursor

  onMount(async () => {
    mounted = true;
    loading = false;

    // Initialize theme
    themeActions.init();
  });

  // Reactive statement to handle route changes
  $: if (mounted && $page.url.pathname) {
    // Track page views for analytics
    if (typeof window !== 'undefined' && window.plausible) {
      window.plausible('pageview');
    }
  }
</script>

<svelte:head>
  <title
    >{$page.data?.['title']
      ? `${$page.data['title']} | VybeCoding.ai`
      : 'VybeCoding.ai - Learn AI Development with the Vybe Method'}</title
  >

  {#if $page.data?.['description']}
    <meta name="description" content={$page.data['description']} />
  {/if}

  {#if $page.data?.['keywords']}
    <meta name="keywords" content={$page.data['keywords']} />
  {/if}
</svelte:head>

{#if loading}
  <!-- Initial loading screen -->
  <div
    class="fixed inset-0 bg-background flex items-center justify-center z-50"
  >
    <div class="text-center">
      <div
        class="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"
      ></div>
      <h2 class="text-xl font-semibold text-foreground mb-2">VybeCoding.ai</h2>
      <p class="text-muted-foreground">Loading your learning experience...</p>
    </div>
  </div>
{:else}
  <ErrorBoundary>
    <div class="min-h-screen flex flex-col bg-background">
      <!-- Loading bar for navigation -->
      <LoadingBar />

      <!-- Header -->
      <Header />

      <!-- Main content -->
      <main id="main-content" class="flex-1" aria-label="Main content">
        <slot />
      </main>

      <!-- Footer -->
      <Footer />

      <!-- Toast notifications -->
      <Toast />

      <!-- Default browser cursor -->
    </div>
  </ErrorBoundary>
{/if}

<style>
  /* Global styles for the layout */
  :global(html) {
    scroll-behavior: smooth;
  }

  :global(body) {
    overflow-x: hidden;
  }

  /* Ensure proper stacking context for modals and overlays */
  :global(.modal-backdrop) {
    z-index: 40;
  }

  :global(.modal) {
    z-index: 50;
  }

  :global(.toast) {
    z-index: 60;
  }

  /* Custom scrollbar for webkit browsers */
  :global(*::-webkit-scrollbar) {
    width: 8px;
    height: 8px;
  }

  :global(*::-webkit-scrollbar-track) {
    background: transparent;
  }

  :global(*::-webkit-scrollbar-thumb) {
    background: rgba(156, 163, 175, 0.3);
    border-radius: 4px;
  }

  :global(*::-webkit-scrollbar-thumb:hover) {
    background: rgba(156, 163, 175, 0.5);
  }

  :global(.dark *::-webkit-scrollbar-thumb) {
    background: rgba(75, 85, 99, 0.3);
  }

  :global(.dark *::-webkit-scrollbar-thumb:hover) {
    background: rgba(75, 85, 99, 0.5);
  }

  /* Focus styles for better accessibility */
  :global(*:focus-visible) {
    outline: 2px solid rgb(14 165 233);
    outline-offset: 2px;
  }

  /* Smooth transitions for theme changes */
  :global(*) {
    transition-property: color, background-color, border-color;
    transition-duration: 200ms;
    transition-timing-function: ease-in-out;
  }

  /* Prevent layout shift during loading */
  :global(img) {
    max-width: 100%;
    height: auto;
  }

  /* Ensure proper text rendering */
  :global(body) {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  /* Print styles */
  @media print {
    :global(.no-print) {
      display: none !important;
    }

    :global(body) {
      background: white !important;
      color: black !important;
    }

    :global(a) {
      text-decoration: underline !important;
    }

    :global(a[href^='http']:after) {
      content: ' (' attr(href) ')';
      font-size: 0.8em;
      color: #666;
    }
  }

  /* Reduced motion preferences */
  @media (prefers-reduced-motion: reduce) {
    :global(*) {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }

    :global(html) {
      scroll-behavior: auto;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    :global(*) {
      border-color: currentColor !important;
    }

    :global(button, input, select, textarea) {
      border: 2px solid currentColor !important;
    }
  }

  /* Mobile-specific optimizations */
  @media (max-width: 768px) {
    :global(body) {
      -webkit-text-size-adjust: 100%;
    }

    /* Prevent zoom on input focus */
    :global(input, select, textarea) {
      font-size: 16px;
    }
  }
</style>
