<script lang="ts">
  import { onMount } from 'svelte';

  // Article data
  const article = {
  "type": "news_article",
  "title": "Breaking: AI-Powered Development Tools Test Developments Impact developers",
  "subtitle": "Latest insights and analysis on AI-Powered Development Tools Test",
  "content": "Recent developments in AI-Powered Development Tools Test have significant implications for developers. This analysis explores the key factors, trends, and potential outcomes.\n\nKey highlights:\n• Industry-changing developments in AI-Powered Development Tools Test\n• Direct impact on developers workflows\n• Expert analysis and future predictions\n• Actionable insights for immediate implementation\n\nThe landscape is rapidly evolving, and staying informed is crucial for success.\n\n*Additional requirements: Test deployment pipeline*",
  "readTime": "6 min read",
  "category": "Technology",
  "target_audience": "developers",
  "complexity_level": "intermediate",
  "generated_at": "2025-06-06T01:37:04.693Z",
  "agents_used": [
    "VYBA",
    "QUBERT",
    "VYBRO",
    "DUCKY",
    "PIXY"
  ],
  "sources": [
    {
      "title": "Industry Analysis Report",
      "url": "#"
    },
    {
      "title": "Expert Opinion Survey",
      "url": "#"
    }
  ],
  "inspirationSources": [],
  "agent_activities": [
    "QUBERT analyzing news trends and complexity",
    "DUCKY fact-checking and validation",
    "PIXY optimizing content presentation"
  ]
};

  onMount(() => {
    // Track article view
    console.log('Article viewed:', article.title);
  });
</script>

<svelte:head>
  <title>{article.title} - VybeCoding.ai News</title>
  <meta name="description" content={article.subtitle || article.excerpt || ''} />
</svelte:head>

<div class="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900">
  <div class="container mx-auto px-4 py-8">
    <!-- Article Header -->
    <div class="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 mb-8">
      <div class="flex flex-wrap gap-2 mb-4">
        <span class="bg-cyan-600 text-white px-3 py-1 rounded-full text-sm">
          {article.category || 'AI News'}
        </span>
        <span class="bg-purple-600 text-white px-3 py-1 rounded-full text-sm">
          {article.readTime || '5 min read'}
        </span>
        <span class="bg-orange-600 text-white px-3 py-1 rounded-full text-sm">
          Generated by MAS
        </span>
      </div>

      <h1 class="text-4xl font-bold text-white mb-4">{article.title}</h1>

      {#if article.subtitle}
        <p class="text-xl text-gray-300 mb-4">{article.subtitle}</p>
      {/if}

      <div class="flex items-center text-sm text-gray-400">
        <span>Published: {new Date(article.generated_at || article.publishedAt || Date.now()).toLocaleDateString()}</span>
        {#if article.target_audience}
          <span class="ml-4">Target: {article.target_audience}</span>
        {/if}
      </div>
    </div>

    <!-- Article Content -->
    <div class="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 mb-8">
      <div class="prose prose-invert max-w-none">
        {#if article.content}
          {@html article.content.replace(/\n/g, '<br>')}
        {:else}
          <p class="text-gray-300">Article content not available.</p>
        {/if}
      </div>
    </div>

    <!-- Article Metadata -->
    <div class="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6">
      <h3 class="text-xl font-bold text-white mb-4">Article Information</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
        {#if article.agents_used}
          <div class="md:col-span-2">
            <span class="text-gray-400">MAS Agents:</span>
            <span class="text-cyan-400 ml-2">{article.agents_used.join(', ')}</span>
          </div>
        {/if}
        {#if article.sources}
          <div class="md:col-span-2">
            <span class="text-gray-400">Sources:</span>
            <div class="mt-2">
              {#each article.sources as source}
                <div class="text-cyan-400">• {source.title}</div>
              {/each}
            </div>
          </div>
        {/if}
        {#if article.inspirationSources && article.inspirationSources.length > 0}
          <div class="md:col-span-2">
            <span class="text-gray-400">Inspiration:</span>
            <div class="mt-2">
              {#each article.inspirationSources as source}
                <a href={source} class="text-cyan-400 hover:text-cyan-300 block">• {source}</a>
              {/each}
            </div>
          </div>
        {/if}
      </div>
    </div>
  </div>
</div>