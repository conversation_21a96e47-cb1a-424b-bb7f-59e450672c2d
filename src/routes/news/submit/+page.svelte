<script lang="ts">
  import { goto } from '$app/navigation';
  import { ArrowLeft, Send } from 'lucide-svelte';
  import { onMount } from 'svelte';

  // Form state
  let title = '';
  let content = '';
  let category = 'ai-development';
  let tags = '';
  let authorName = '';
  let authorEmail = '';
  let isSubmitting = false;
  let submitSuccess = false;

  function goBack() {
    goto('/news');
  }

  async function submitArticle(event: Event) {
    event.preventDefault();

    if (!title || !content || !authorName || !authorEmail) {
      alert('Please fill in all required fields');
      return;
    }

    isSubmitting = true;

    try {
      const response = await fetch('/api/community/articles/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          title,
          content,
          category,
          tags: tags.split(',').map(tag => tag.trim()).filter(tag => tag),
          author: {
            name: authorName,
            email: authorEmail
          },
          submittedAt: new Date().toISOString()
        })
      });

      if (response.ok) {
        submitSuccess = true;
        // Reset form
        title = '';
        content = '';
        tags = '';
        authorName = '';
        authorEmail = '';
      } else {
        const error = await response.json();
        alert(`Submission failed: ${error.message}`);
      }
    } catch (error) {
      console.error('Submission error:', error);
      alert('Failed to submit article. Please try again.');
    } finally {
      isSubmitting = false;
    }
  }

  onMount(() => {
    console.log('Article submission page loaded');
  });
</script>

<svelte:head>
  <title>Submit Article - VybeCoding.ai Community News</title>
  <meta
    name="description"
    content="Share your AI insights, security findings, and FOSS discoveries with the VybeCoding.ai community."
  />
</svelte:head>

<main
  class="min-h-screen bg-gradient-to-br from-gray-900 to-gray-800 text-white py-20"
>
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="text-center mb-16">
      <button
        on:click={goBack}
        class="btn-glass mb-8 inline-flex items-center gap-2"
      >
        <ArrowLeft class="w-4 h-4" />
        Back to News
      </button>

      <h1 class="text-4xl md:text-6xl font-black mb-6">
        Submit <span
          class="gradient-vybecoding bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500"
          >Article</span
        >
      </h1>

      <div class="glass-card max-w-3xl mx-auto p-8">
        <p class="text-xl text-white/90 leading-relaxed">
          Share your AI insights, security findings, and FOSS discoveries with
          our community. All submissions are reviewed to ensure quality and
          relevance.
        </p>
      </div>
    </div>

    <!-- Real Article Submission Form -->
    <div class="glass-card max-w-4xl mx-auto p-8">
      <h2 class="text-2xl font-bold text-white mb-8">Submit Your Article</h2>

      <form on:submit={submitArticle} class="space-y-6">
        <!-- Title -->
        <div>
          <label for="title" class="block text-white font-medium mb-2">
            Article Title *
          </label>
          <input
            id="title"
            type="text"
            bind:value={title}
            class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
        </div>

        <!-- Author Information -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label for="authorName" class="block text-white font-medium mb-2">
              Author Name *
            </label>
            <input
              id="authorName"
              type="text"
              bind:value={authorName}
              class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>
          <div>
            <label for="authorEmail" class="block text-white font-medium mb-2">
              Author Email *
            </label>
            <input
              id="authorEmail"
              type="email"
              bind:value={authorEmail}
              class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>
        </div>

        <!-- Category and Tags -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label for="category" class="block text-white font-medium mb-2">
              Category
            </label>
            <select
              id="category"
              bind:value={category}
              class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="ai-development">AI Development</option>
              <option value="security">Security & Privacy</option>
              <option value="foss">FOSS & Open Source</option>
              <option value="education">Educational Content</option>
              <option value="community">Community News</option>
            </select>
          </div>
          <div>
            <label for="tags" class="block text-white font-medium mb-2">
              Tags (comma-separated)
            </label>
            <input
              id="tags"
              type="text"
              bind:value={tags}
              class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        <!-- Content -->
        <div>
          <label for="content" class="block text-white font-medium mb-2">
            Article Content *
          </label>
          <textarea
            id="content"
            bind:value={content}
            rows="12"
            class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 resize-vertical"
            required
          ></textarea>
          <p class="text-white/60 text-sm mt-2">
            Supports basic HTML formatting. Use line breaks for paragraphs.
          </p>
        </div>

        <!-- Submit Button -->
        <div class="flex justify-end gap-4">
          <button
            type="button"
            on:click={goBack}
            class="px-6 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg text-white hover:bg-white/20 transition-all duration-200"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            class="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg text-white font-medium hover:from-blue-600 hover:to-purple-700 transition-all duration-200 inline-flex items-center gap-2"
          >
            {#if isSubmitting}
              <div class="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
              Submitting...
            {:else}
              <Send class="w-4 h-4" />
              Submit Article
            {/if}
          </button>
        </div>
      </form>
    </div>
  </div>
</main>
