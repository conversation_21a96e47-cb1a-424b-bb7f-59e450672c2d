<script lang="ts">
  import { goto } from '$app/navigation';
  import {
    ArrowLeft,
    Share,
    BookOpen,
    Users,
    Eye,
    Clock,
    Shield,
    ExternalLink,
    MessageCircle,
    ThumbsUp,
  } from 'lucide-svelte';

  // Article data - loaded from content management system
  const article = {
    id: '1',
    title: 'Security Alert: Lovable AI Platform Vulnerabilities Discovered',
    slug: 'lovable-ai-security-vulnerabilities',
    excerpt:
      "Critical security flaws found in popular AI coding platform. Learn how the Vybe Method's security-first approach prevents similar issues.",
    content: `
      <h2>Critical Security Vulnerabilities Found in Lovable AI Platform</h2>
      
      <p>Security researchers have discovered multiple critical vulnerabilities in the Lovable AI coding platform that could allow attackers to execute arbitrary code and access sensitive user data. This incident highlights the importance of security-first development practices in AI applications.</p>
      
      <h3>The Vulnerabilities</h3>
      
      <p>The discovered vulnerabilities include:</p>
      <ul>
        <li><strong>SQL Injection:</strong> Improper input validation in user queries</li>
        <li><strong>Cross-Site Scripting (XSS):</strong> Unescaped user content in generated code</li>
        <li><strong>Authentication Bypass:</strong> Weak session management allowing privilege escalation</li>
        <li><strong>Code Injection:</strong> Unsafe execution of AI-generated code without sandboxing</li>
      </ul>
      
      <h3>How the Vybe Method Prevents These Issues</h3>
      
      <p>The VybeCoding.ai platform and the Vybe Method incorporate security-first principles that would have prevented these vulnerabilities:</p>
      
      <ol>
        <li><strong>Input Validation:</strong> All user inputs are validated and sanitized using Guardrails AI</li>
        <li><strong>Secure Code Generation:</strong> AI-generated code is automatically scanned for security issues</li>
        <li><strong>Sandboxed Execution:</strong> All code execution happens in isolated environments</li>
        <li><strong>Multi-layer Authentication:</strong> Robust authentication and authorization systems</li>
        <li><strong>Regular Security Audits:</strong> Automated and manual security testing</li>
      </ol>
      
      <h3>Lessons for the AI Development Community</h3>
      
      <p>This incident serves as a crucial reminder that AI-powered development tools must prioritize security. The rapid pace of AI development should not come at the expense of fundamental security practices.</p>
      
      <h4>Best Practices:</h4>
      <ul>
        <li>Implement security validation at every layer</li>
        <li>Use AI to enhance security, not bypass it</li>
        <li>Regular penetration testing and code audits</li>
        <li>Community-driven security reviews</li>
        <li>Transparent disclosure of security practices</li>
      </ul>
      
      <h3>Community Response and Action Items</h3>
      
      <p>The VybeCoding.ai community is committed to learning from these incidents and strengthening our collective security posture. We encourage all developers to:</p>
      
      <ol>
        <li>Review your own AI applications for similar vulnerabilities</li>
        <li>Implement the Vybe Method's security guidelines</li>
        <li>Participate in our security-focused study groups</li>
        <li>Share security findings with the community</li>
      </ol>
      
      <p>Together, we can build a more secure AI development ecosystem that benefits everyone.</p>
    `,
    author: 'VybeCoding Security Team',
    category: 'security',
    tags: ['security', 'vulnerability', 'ai-platform', 'best-practices'],
    sourceUrl:
      'https://www.semafor.com/article/05/29/2025/the-hottest-new-vibe-coding-startup-lovable-is-a-sitting-duck-for-hackers',
    featured: true,
    votes: 67,
    views: 2340,
    activeReaders: 23,
    comments: 45,
    shares: 28,
    readingTime: 8,
    publishedAt: new Date(Date.now() - 3 * 60 * 60 * 1000), // 3 hours ago
  };

  let hasVoted = false;
  let voteCount = article.votes;

  function handleVote() {
    if (!hasVoted) {
      voteCount += 1;
      hasVoted = true;
    }
  }

  function handleShare() {
    if (navigator.share) {
      navigator.share({
        title: article.title,
        text: article.excerpt,
        url: window.location.href,
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
    }
  }

  function handleCollaborate() {
    goto(`/news/${article.slug}/collaborate`);
  }

  function handleLearnMore() {
    goto(`/courses/from-news/${article.id}`);
  }

  function goBack() {
    goto('/news');
  }
</script>

<svelte:head>
  <title>{article.title} - VybeCoding.ai News</title>
  <meta name="description" content={article.excerpt} />
  <meta property="og:title" content={article.title} />
  <meta property="og:description" content={article.excerpt} />
  <meta property="og:type" content="article" />
  <meta
    property="og:url"
    content={`https://vybecoding.ai/news/${article.slug}`}
  />
</svelte:head>

<main
  class="min-h-screen bg-gradient-to-br from-gray-900 to-gray-800 text-white"
>
  <!-- Article Header -->
  <section class="relative py-20 overflow-hidden">
    <!-- Background Effects -->
    <div
      class="absolute inset-0 bg-gradient-to-br from-red-600/20 via-orange-600/20 to-yellow-600/20"
    ></div>

    <div class="relative container-glass">
      <!-- Navigation -->
      <button
        on:click={goBack}
        class="btn-glass mb-8 inline-flex items-center gap-2"
      >
        <ArrowLeft class="w-4 h-4" />
        Back to News
      </button>

      <!-- Article Meta -->
      <div class="flex flex-wrap items-center gap-4 mb-8">
        <span class="badge badge-destructive">
          <Shield class="w-3 h-3 mr-1" />
          SECURITY
        </span>

        {#if article.featured}
          <span class="badge">Featured</span>
        {/if}

        <div class="flex items-center gap-4 text-sm text-white/60">
          <span class="flex items-center gap-1">
            <Eye class="w-4 h-4" />
            {article.views} views
          </span>
          <span class="flex items-center gap-1">
            <Clock class="w-4 h-4" />
            {article.readingTime} min read
          </span>
          <span class="flex items-center gap-1">
            <MessageCircle class="w-4 h-4" />
            {article.comments} comments
          </span>
          {#if article.activeReaders > 0}
            <span class="flex items-center gap-1 text-green-400">
              <div
                class="w-2 h-2 bg-green-400 rounded-full animate-pulse"
              ></div>
              {article.activeReaders} reading now
            </span>
          {/if}
        </div>
      </div>

      <!-- Article Title -->
      <h1 class="text-4xl md:text-6xl font-black mb-6 leading-tight">
        {article.title}
      </h1>

      <!-- Article Excerpt -->
      <div class="glass-card p-8 mb-12">
        <p
          class="text-xl md:text-2xl text-white/90 leading-relaxed font-medium"
        >
          {article.excerpt}
        </p>
      </div>

      <!-- Author and Actions -->
      <div
        class="flex flex-col md:flex-row justify-between items-start md:items-center gap-6"
      >
        <div class="flex items-center gap-4">
          <div
            class="w-12 h-12 rounded-full bg-gradient-to-br from-red-500/20 to-orange-500/20 flex items-center justify-center"
          >
            <Shield class="w-6 h-6 text-red-400" />
          </div>
          <div>
            <div class="font-bold text-white">{article.author}</div>
            <div class="text-white/60">
              Published {article.publishedAt.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
              })}
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex gap-3">
          <button
            class="btn-glass {hasVoted ? 'text-green-400' : ''}"
            on:click={handleVote}
          >
            <ThumbsUp class="w-4 h-4 mr-2" />
            {voteCount}
          </button>

          <button class="btn-glass" on:click={handleShare}>
            <Share class="w-4 h-4 mr-2" />
            Share
          </button>

          <button class="btn-glass" on:click={handleCollaborate}>
            <Users class="w-4 h-4 mr-2" />
            Collaborate
          </button>

          <button class="btn-glass" on:click={handleLearnMore}>
            <BookOpen class="w-4 h-4 mr-2" />
            Learn More
          </button>
        </div>
      </div>
    </div>
  </section>

  <!-- Article Content -->
  <section class="relative py-16">
    <div class="container-glass">
      <div class="max-w-4xl mx-auto">
        <article class="prose prose-lg prose-invert max-w-none">
          {@html article.content}
        </article>

        <!-- Source Link -->
        {#if article.sourceUrl}
          <div class="glass-card p-6 mt-12">
            <div class="flex items-center gap-4">
              <ExternalLink class="w-6 h-6 text-white/60" />
              <div>
                <div class="font-medium text-white mb-1">Original Source</div>
                <a
                  href={article.sourceUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  class="text-blue-400 hover:text-blue-300 transition-colors"
                >
                  {new URL(article.sourceUrl).hostname}
                </a>
              </div>
            </div>
          </div>
        {/if}

        <!-- Tags -->
        {#if article.tags.length > 0}
          <div class="mt-12">
            <h3 class="text-xl font-bold text-white mb-4">Tags</h3>
            <div class="flex flex-wrap gap-3">
              {#each article.tags as tag}
                <span class="badge badge-outline">{tag}</span>
              {/each}
            </div>
          </div>
        {/if}
      </div>
    </div>
  </section>
</main>
