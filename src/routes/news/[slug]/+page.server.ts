// Server-side data loading for individual article page
import type { PageServerLoad } from './$types';
import { error } from '@sveltejs/kit';
import { databases } from '$lib/services/appwrite';
import { config } from '$lib/config';
import { ContentCurationService } from '$lib/services/ai/contentCuration';
import type { AIArticle } from '$lib/types/news';

const curationService = new ContentCurationService();

export const load: PageServerLoad = async ({ params, locals }) => {
  try {
    const { slug } = params;

    // Try to find article by slug
    const response = await databases.listDocuments(
      config.appwrite.databaseId,
      'news_articles',
      [`slug=${slug}`, 'status=published']
    );

    if (response.documents.length === 0) {
      // Fallback to mock data for demo purposes
      if (slug === 'lovable-ai-security-vulnerabilities') {
        const mockArticle: AIArticle = {
          id: '1',
          title:
            'Security Alert: Lovable AI Platform Vulnerabilities Discovered',
          slug: 'lovable-ai-security-vulnerabilities',
          excerpt:
            "Critical security flaws found in popular AI coding platform. Learn how the Vybe Method's security-first approach prevents similar issues.",
          content: generateSecurityArticleContent(),
          authorId: 'security-team',
          author: {
            id: 'security-team',
            name: 'VybeCoding Security Team',
            email: '<EMAIL>',
            avatar: '/team-avatar.png',
            aiProfile: {
              interests: [],
              expertise: { Security: 1.0, 'AI Safety': 0.9 },
              learningPath: [],
              collaborationStyle: { style: 'expert', preferences: [] },
              trustScore: 1.0,
            },
            reputationScore: 100,
            contributionCount: 50,
            expertiseAreas: ['Security', 'AI Safety'],
            verifiedExpert: true,
          },
          category: 'security' as const,
          tags: ['security', 'vulnerability', 'ai-platform', 'best-practices'],
          source: 'expert' as const,
          sourceUrl:
            'https://www.semafor.com/article/05/29/2025/the-hottest-new-vibe-coding-startup-lovable-is-a-sitting-duck-for-hackers',
          status: 'published' as const,
          featured: true,
          aiMetadata: {
            sentiment: -0.3,
            complexity: 7,
            topics: ['Security', 'AI Platform', 'Vulnerability Assessment'],
            factCheckScore: 0.95,
            trendPrediction: 0.8,
            readingTime: 8,
            educationalValue: 0.95,
          },
          collaborators: [],
          realTimeEdits: [],
          aiSuggestions: [],
          votes: 67,
          views: 2340,
          activeReaders: 23,
          comments: 45,
          shares: 28,
          publishedAt: new Date(Date.now() - 3 * 60 * 60 * 1000),
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        // Get related articles
        const relatedArticles = await curationService.getRelatedArticles(
          mockArticle.id,
          3
        );

        return {
          article: mockArticle,
          relatedArticles,
        };
      }

      throw error(404, 'Article not found');
    }

    const article = response.documents[0] as AIArticle;

    // Increment view count
    await databases.updateDocument(
      config.appwrite.databaseId,
      'news_articles',
      article.id,
      {
        views: (article.views || 0) + 1,
      }
    );

    // Track user interaction if authenticated
    if (locals.user) {
      await curationService.updateUserInterests(
        locals.user.id,
        article.id,
        'view'
      );
    }

    // Get related articles
    const relatedArticles = await curationService.getRelatedArticles(
      article.id,
      3
    );

    return {
      article: {
        ...article,
        views: (article.views || 0) + 1,
      },
      relatedArticles,
    };
  } catch (err) {
    console.error('Failed to load article:', err);
    throw error(404, 'Article not found');
  }
};

function generateSecurityArticleContent(): string {
  return `
    <h2>Critical Security Vulnerabilities Found in Lovable AI Platform</h2>
    
    <p>Security researchers have discovered multiple critical vulnerabilities in the Lovable AI coding platform that could allow attackers to execute arbitrary code and access sensitive user data. This incident highlights the importance of security-first development practices in AI applications.</p>
    
    <h3>The Vulnerabilities</h3>
    
    <p>The discovered vulnerabilities include:</p>
    <ul>
      <li><strong>SQL Injection:</strong> Improper input validation in user queries</li>
      <li><strong>Cross-Site Scripting (XSS):</strong> Unescaped user content in generated code</li>
      <li><strong>Authentication Bypass:</strong> Weak session management allowing privilege escalation</li>
      <li><strong>Code Injection:</strong> Unsafe execution of AI-generated code without sandboxing</li>
    </ul>
    
    <h3>How the Vybe Method Prevents These Issues</h3>
    
    <p>The VybeCoding.ai platform and the Vybe Method incorporate security-first principles that would have prevented these vulnerabilities:</p>
    
    <ol>
      <li><strong>Input Validation:</strong> All user inputs are validated and sanitized using Guardrails AI</li>
      <li><strong>Secure Code Generation:</strong> AI-generated code is automatically scanned for security issues</li>
      <li><strong>Sandboxed Execution:</strong> All code execution happens in isolated environments</li>
      <li><strong>Multi-layer Authentication:</strong> Robust authentication and authorization systems</li>
      <li><strong>Regular Security Audits:</strong> Automated and manual security testing</li>
    </ol>
    
    <h3>Lessons for the AI Development Community</h3>
    
    <p>This incident serves as a crucial reminder that AI-powered development tools must prioritize security. The rapid pace of AI development should not come at the expense of fundamental security practices.</p>
    
    <h4>Best Practices:</h4>
    <ul>
      <li>Implement security validation at every layer</li>
      <li>Use AI to enhance security, not bypass it</li>
      <li>Regular penetration testing and code audits</li>
      <li>Community-driven security reviews</li>
      <li>Transparent disclosure of security practices</li>
    </ul>
    
    <h3>Community Response and Action Items</h3>
    
    <p>The VybeCoding.ai community is committed to learning from these incidents and strengthening our collective security posture. We encourage all developers to:</p>
    
    <ol>
      <li>Review your own AI applications for similar vulnerabilities</li>
      <li>Implement the Vybe Method's security guidelines</li>
      <li>Participate in our security-focused study groups</li>
      <li>Share security findings with the community</li>
    </ol>
    
    <p>Together, we can build a more secure AI development ecosystem that benefits everyone.</p>
  `;
}
