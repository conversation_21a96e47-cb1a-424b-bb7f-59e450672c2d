<script>
  let article = {
    id: '419c498b',
    title: 'AI Development Trends in 2025',
    content: `<h2>Introduction</h2><p>AI is evolving rapidly in 2025...</p>`,
    author: 'VybeCoding AI Team',
    tags: ["AI", "Development", "Trends"],
    publishedAt: '2025-06-06T13:35:37.187578'
  };
</script>

<svelte:head>
  <title>{article.title} - VybeCoding.ai News</title>
  <meta name="description" content="{article.content.substring(0, 160)}..." />
</svelte:head>

<article class="news-article">
  <header class="article-header">
    <h1>{article.title}</h1>
    <div class="article-meta">
      <span class="author">By {article.author}</span>
      <time class="published-date">{new Date(article.publishedAt).toLocaleDateString()}</time>
    </div>
    {#if article.tags.length > 0}
      <div class="tags">
        {#each article.tags as tag}
          <span class="tag">{tag}</span>
        {/each}
      </div>
    {/if}
  </header>
  
  <div class="article-content">
    {@html article.content}
  </div>
</article>

<style>
  .news-article {
    max-width: 700px;
    margin: 0 auto;
    padding: 2rem;
  }
  
  .article-header {
    margin-bottom: 2rem;
    border-bottom: 1px solid #e2e8f0;
    padding-bottom: 1rem;
  }
  
  .article-meta {
    display: flex;
    gap: 1rem;
    margin: 1rem 0;
    color: #718096;
  }
  
  .tags {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
  }
  
  .tag {
    background: #edf2f7;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
  }
  
  .article-content {
    line-height: 1.6;
  }
</style>