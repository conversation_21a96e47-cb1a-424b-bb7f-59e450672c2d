<script lang="ts">
  import { onMount } from 'svelte';
  import {
    Calendar,
    Clock,
    TrendingUp,
    Zap,
    Brain,
    Cpu,
    Users,
    ExternalLink,
    ArrowRight,
    Sparkles,
  } from 'lucide-svelte';

  let mounted = false;

  onMount(() => {
    mounted = true;
  });

  // Featured news data - loaded from content management system
  const featuredNews = [
    {
      id: 1,
      title: 'Revolutionary Multi-Agent System Breakthrough',
      excerpt:
        'VybeCoding.ai announces major advancement in autonomous AI development with new MAS framework achieving 95% accuracy in code generation.',
      category: 'AI Development',
      date: '2025-01-15',
      readTime: '5 min read',
      trending: true,
      image:
        'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=600&h=300&fit=crop',
    },
    {
      id: 2,
      title: 'Vybe Method 2.0: The Future of AI-Powered Development',
      excerpt:
        'Introducing enhanced workflows, improved agent coordination, and revolutionary new features that make AI development accessible to everyone.',
      category: 'Platform Update',
      date: '2025-01-14',
      readTime: '8 min read',
      trending: true,
      image:
        'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=600&h=300&fit=crop',
    },
    {
      id: 3,
      title: 'Community Spotlight: 1000+ Vybe Qubes Created',
      excerpt:
        'Our community has reached an incredible milestone with over 1000 AI-generated applications built using the Vybe Method.',
      category: 'Community',
      date: '2025-01-13',
      readTime: '3 min read',
      trending: false,
      image:
        'https://images.unsplash.com/photo-1522071820081-009f0129c71c?w=600&h=300&fit=crop',
    },
  ];

  const recentNews = [
    {
      id: 4,
      title: 'New Neural Course: Advanced Agent Coordination',
      category: 'Education',
      date: '2025-01-12',
      readTime: '4 min read',
    },
    {
      id: 5,
      title: 'Partnership Announcement: Enterprise AI Solutions',
      category: 'Business',
      date: '2025-01-11',
      readTime: '6 min read',
    },
    {
      id: 6,
      title: 'Open Source Release: Vybe Agent Framework',
      category: 'Open Source',
      date: '2025-01-10',
      readTime: '7 min read',
    },
    {
      id: 7,
      title: 'Security Update: Enhanced Authentication System',
      category: 'Security',
      date: '2025-01-09',
      readTime: '3 min read',
    },
    {
      id: 8,
      title: 'Performance Improvements: 50% Faster Code Generation',
      category: 'Performance',
      date: '2025-01-08',
      readTime: '5 min read',
    },
  ];

  function formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  }

  function getCategoryIcon(category: string) {
    switch (category) {
      case 'AI Development':
        return Brain;
      case 'Platform Update':
        return Zap;
      case 'Community':
        return Users;
      case 'Education':
        return Brain;
      case 'Business':
        return TrendingUp;
      case 'Open Source':
        return Cpu;
      case 'Security':
        return Sparkles;
      case 'Performance':
        return Zap;
      default:
        return Calendar;
    }
  }

  function getCategoryColor(category: string): string {
    switch (category) {
      case 'AI Development':
        return 'from-cyan-400 to-blue-600';
      case 'Platform Update':
        return 'from-purple-400 to-pink-600';
      case 'Community':
        return 'from-indigo-400 to-purple-600';
      case 'Education':
        return 'from-green-400 to-emerald-600';
      case 'Business':
        return 'from-orange-400 to-red-600';
      case 'Open Source':
        return 'from-yellow-400 to-orange-600';
      case 'Security':
        return 'from-red-400 to-pink-600';
      case 'Performance':
        return 'from-teal-400 to-cyan-600';
      default:
        return 'from-gray-400 to-gray-600';
    }
  }
</script>

<svelte:head>
  <title>AI Development News Hub - VybeCoding.ai</title>
  <meta
    name="description"
    content="Stay updated with the latest AI development news, platform updates, and community highlights from VybeCoding.ai"
  />
  <meta
    name="keywords"
    content="AI news, development updates, VybeCoding, artificial intelligence, programming news"
  />
</svelte:head>

<main class="min-h-screen bg-slate-900 pt-4 pb-16">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header Section -->
    <div class="text-center mb-8">
      <div class="flex items-center justify-center gap-3 mb-4">
        <div
          class="w-10 h-10 rounded-xl bg-gradient-to-br from-cyan-500 to-purple-600 flex items-center justify-center"
        >
          <TrendingUp class="w-5 h-5 text-white" />
        </div>
        <h1 class="text-3xl font-bold text-white">AI Development News Hub</h1>
      </div>
      <p class="text-lg text-slate-400 max-w-2xl mx-auto">
        Stay ahead of the curve with the latest developments in AI, platform
        updates, and community highlights from the VybeCoding.ai ecosystem.
      </p>
    </div>

    <!-- Featured News -->
    <section class="mb-12">
      <div class="flex items-center gap-3 mb-6">
        <Sparkles class="w-6 h-6 text-cyan-400" />
        <h2 class="text-2xl font-bold text-white">Featured Stories</h2>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
        {#each featuredNews as article}
          <article
            class="group bg-slate-800 border border-slate-700 rounded-2xl overflow-hidden hover:border-cyan-500/50 transition-all duration-300 hover:transform hover:scale-[1.02]"
          >
            <div class="relative overflow-hidden">
              <img
                src={article.image}
                alt={article.title}
                class="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-500"
              />
              <div class="absolute top-4 left-4">
                <div
                  class="flex items-center gap-2 px-3 py-1 bg-gradient-to-r {getCategoryColor(
                    article.category
                  )} rounded-full text-white text-sm font-semibold"
                >
                  <svelte:component
                    this={getCategoryIcon(article.category)}
                    class="w-3 h-3"
                  />
                  {article.category}
                </div>
              </div>
              {#if article.trending}
                <div class="absolute top-4 right-4">
                  <div
                    class="flex items-center gap-1 px-2 py-1 bg-red-500 rounded-full text-white text-xs font-bold"
                  >
                    <TrendingUp class="w-3 h-3" />
                    TRENDING
                  </div>
                </div>
              {/if}
            </div>

            <div class="p-6">
              <h3
                class="text-xl font-bold text-white mb-3 group-hover:text-cyan-400 transition-colors"
              >
                {article.title}
              </h3>
              <p class="text-slate-400 mb-4 line-clamp-3">
                {article.excerpt}
              </p>

              <div
                class="flex items-center justify-between text-sm text-slate-500"
              >
                <div class="flex items-center gap-4">
                  <div class="flex items-center gap-1">
                    <Calendar class="w-4 h-4" />
                    {formatDate(article.date)}
                  </div>
                  <div class="flex items-center gap-1">
                    <Clock class="w-4 h-4" />
                    {article.readTime}
                  </div>
                </div>
                <button
                  class="flex items-center gap-1 text-cyan-400 hover:text-cyan-300 transition-colors"
                >
                  Read More
                  <ArrowRight class="w-4 h-4" />
                </button>
              </div>
            </div>
          </article>
        {/each}
      </div>
    </section>

    <!-- Recent News -->
    <section>
      <div class="flex items-center gap-3 mb-8">
        <Clock class="w-6 h-6 text-purple-400" />
        <h2 class="text-2xl font-bold text-white">Recent Updates</h2>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        {#each recentNews as article}
          <article
            class="group bg-slate-800 border border-slate-700 rounded-xl p-6 hover:border-purple-500/50 transition-all duration-300 hover:transform hover:translateY(-1px)"
          >
            <div class="flex items-start justify-between mb-4">
              <div
                class="flex items-center gap-2 px-3 py-1 bg-gradient-to-r {getCategoryColor(
                  article.category
                )} rounded-full text-white text-sm font-semibold"
              >
                <svelte:component
                  this={getCategoryIcon(article.category)}
                  class="w-3 h-3"
                />
                {article.category}
              </div>
              <div class="text-slate-500 text-sm">
                {formatDate(article.date)}
              </div>
            </div>

            <h3
              class="text-lg font-bold text-white mb-3 group-hover:text-purple-400 transition-colors"
            >
              {article.title}
            </h3>

            <div class="flex items-center justify-between">
              <div class="flex items-center gap-1 text-slate-500 text-sm">
                <Clock class="w-4 h-4" />
                {article.readTime}
              </div>
              <button
                class="flex items-center gap-1 text-purple-400 hover:text-purple-300 transition-colors"
              >
                Read More
                <ExternalLink class="w-4 h-4" />
              </button>
            </div>
          </article>
        {/each}
      </div>
    </section>

    <!-- Newsletter Signup -->
    <section class="mt-16 text-center">
      <div
        class="bg-gradient-to-r from-cyan-500/10 to-purple-600/10 border border-cyan-500/20 rounded-2xl p-8"
      >
        <h3 class="text-2xl font-bold text-white mb-4">Stay in the Loop</h3>
        <p class="text-slate-400 mb-6 max-w-2xl mx-auto">
          Get the latest AI development news, platform updates, and exclusive
          insights delivered directly to your inbox.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
          <input
            type="email"
            placeholder="Enter your email"
            class="flex-1 px-4 py-3 bg-slate-800 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:border-cyan-500"
          />
          <button
            class="px-6 py-3 bg-gradient-to-r from-cyan-500 to-purple-600 text-white font-semibold rounded-lg hover:from-cyan-600 hover:to-purple-700 transition-all duration-300"
          >
            Subscribe
          </button>
        </div>
      </div>
    </section>
  </div>
</main>

<style>
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
