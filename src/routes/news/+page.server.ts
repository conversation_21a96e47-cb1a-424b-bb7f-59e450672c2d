// Server-side data loading for news page
import type { PageServerLoad } from './$types';
import { ContentCurationService } from '$lib/services/ai/contentCuration';
import { NewsAggregationService } from '$lib/services/ai/newsAggregation';

const curationService = new ContentCurationService();
const aggregationService = new NewsAggregationService();

export const load: PageServerLoad = async ({ url, locals }) => {
  try {
    const category = url.searchParams.get('category') || 'all';
    const search = url.searchParams.get('search');
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const userId = locals.user?.id;

    // Load content based on parameters
    let articles;
    let featuredArticles = [];
    let trendingTopics = [];

    if (search) {
      // Search articles
      articles = await curationService.searchArticles(search, limit);
    } else if (category && category !== 'all') {
      // Get articles by category
      articles = await curationService.getArticlesByCategory(category, limit);
    } else if (userId) {
      // Get personalized feed for authenticated users
      const [personalizedFeed, homepage, trends] = await Promise.all([
        curationService.getPersonalizedFeed(userId, limit),
        curationService.getHomepageRecommendations(),
        aggregationService.getTrendingTopics(),
      ]);

      articles = personalizedFeed;
      featuredArticles = homepage.featured;
      trendingTopics = trends;
    } else {
      // Get public feed for anonymous users
      const [homepage, trends] = await Promise.all([
        curationService.getHomepageRecommendations(),
        aggregationService.getTrendingTopics(),
      ]);

      articles = [...homepage.trending, ...homepage.recent];
      featuredArticles = homepage.featured;
      trendingTopics = trends;
    }

    // Calculate live stats
    const liveStats = {
      totalArticles: articles.length,
      activeReaders: articles.reduce(
        (sum, article) => sum + (article.activeReaders || 0),
        0
      ),
      todayArticles: articles.filter(
        article =>
          new Date(article.publishedAt).toDateString() ===
          new Date().toDateString()
      ).length,
      trendingTopics: trendingTopics.length,
    };

    return {
      articles,
      featuredArticles,
      trendingTopics,
      liveStats,
      category,
      search,
    };
  } catch (error) {
    console.error('Failed to load news data:', error);

    // Return fallback data
    return {
      articles: [],
      featuredArticles: [],
      trendingTopics: [],
      liveStats: {
        totalArticles: 0,
        activeReaders: 0,
        todayArticles: 0,
        trendingTopics: 0,
      },
      category: 'all',
      search: null,
      error: 'Failed to load news content',
    };
  }
};
