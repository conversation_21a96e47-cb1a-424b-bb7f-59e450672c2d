<script lang="ts">
  import { onMount } from 'svelte';
  import {
    <PERSON><PERSON>,
    Brain,
    Zap,
    Code,
    Shield,
    Target,
    Rocket,
    Cpu,
    Database,
    Globe,
    Lock,
    ArrowRight,
    Sparkles,
    Sword,
    Crown,
    Star,
  } from 'lucide-svelte';

  let mounted = false;

  onMount(() => {
    mounted = true;
    initializeScrollAnimations();
    initializeParticleBackground();
  });

  // Scroll-triggered animations
  function initializeScrollAnimations() {
    const observer = new IntersectionObserver(
      entries => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('animate-in');
          }
        });
      },
      { threshold: 0.1 }
    );

    // Use requestAnimationFrame for real browser timing
    requestAnimationFrame(() => {
      document.querySelectorAll('.animate-on-scroll').forEach(el => {
        observer.observe(el);
      });
    });
  }

  // Particle background
  function initializeParticleBackground() {
    const canvas = document.createElement('canvas');
    canvas.className = 'particle-canvas';
    canvas.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: -1;
      opacity: 0.3;
    `;
    document.body.appendChild(canvas);

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;

    const particles: Array<{
      x: number;
      y: number;
      vx: number;
      vy: number;
      size: number;
      opacity: number;
      color: string;
    }> = [];

    for (let i = 0; i < 30; i++) {
      particles.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        vx: (Math.random() - 0.5) * 0.3,
        vy: (Math.random() - 0.5) * 0.3,
        size: Math.random() * 1.5 + 0.5,
        opacity: Math.random() * 0.3 + 0.1,
        color: Math.random() > 0.5 ? '#06b6d4' : '#ec4899',
      });
    }

    function animateParticles() {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      particles.forEach(particle => {
        particle.x += particle.vx;
        particle.y += particle.vy;

        if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
        if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;

        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fillStyle = particle.color;
        ctx.globalAlpha = particle.opacity;
        ctx.fill();
      });

      requestAnimationFrame(animateParticles);
    }
    animateParticles();

    window.addEventListener('resize', () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    });
  }

  // AI TOOLS ARSENAL - The actual tools used to build VybeCoding.ai
  const aiToolsArsenal = [
    {
      name: 'Claude Sonnet 4',
      role: 'PRIMARY AI ENGINE',
      description:
        'The core AI brain powering all code generation, architecture decisions, and strategic planning',
      icon: Brain,
      gradient: 'from-purple-500 to-blue-500',
      stats: 'Primary AI Engine',
      category: 'Core AI',
    },
    {
      name: 'Augment Code',
      role: 'CONTEXT ENGINE',
      description:
        'World-class codebase context and retrieval system for intelligent code understanding',
      icon: Database,
      gradient: 'from-cyan-500 to-teal-500',
      stats: 'Real-time indexing',
      category: 'Context',
    },
    {
      name: 'GitHub Copilot',
      role: 'CODE ASSISTANT',
      description:
        'Real-time code completion and suggestion engine integrated into development workflow',
      icon: Code,
      gradient: 'from-green-500 to-emerald-500',
      stats: 'AI-powered coding',
      category: 'Development',
    },
    {
      name: 'BMAD Method',
      role: 'ORCHESTRATION PROTOCOL',
      description:
        'Build-Measure-Analyze-Decide methodology for coordinating AI agent workflows',
      icon: Target,
      gradient: 'from-orange-500 to-red-500',
      stats: '7 specialized agents',
      category: 'Protocol',
    },
    {
      name: 'Multi-Agent System',
      role: 'AUTONOMOUS COORDINATION',
      description:
        'Advanced agent coordination system enabling parallel development and consensus building',
      icon: Bot,
      gradient: 'from-pink-500 to-purple-500',
      stats: '160% efficiency gain',
      category: 'Coordination',
    },
    {
      name: 'TypeScript',
      role: 'TYPE SAFETY GUARDIAN',
      description:
        'Enterprise-grade type safety ensuring robust, maintainable, and scalable codebase',
      icon: Shield,
      gradient: 'from-blue-500 to-indigo-500',
      stats: '100% type coverage',
      category: 'Safety',
    },
    {
      name: 'SvelteKit',
      role: 'PERFORMANCE ENGINE',
      description:
        'Lightning-fast web framework providing optimal performance and developer experience',
      icon: Rocket,
      gradient: 'from-red-500 to-orange-500',
      stats: '98+ Lighthouse score',
      category: 'Framework',
    },
    {
      name: 'Appwrite',
      role: 'BACKEND FORTRESS',
      description:
        'Secure, scalable backend-as-a-service handling authentication, database, and storage',
      icon: Lock,
      gradient: 'from-emerald-500 to-green-500',
      stats: '99.9% uptime',
      category: 'Backend',
    },
    {
      name: 'Tailwind CSS',
      role: 'DESIGN SYSTEM',
      description:
        'Utility-first CSS framework enabling rapid, consistent, and responsive design implementation',
      icon: Sparkles,
      gradient: 'from-cyan-500 to-blue-500',
      stats: '50+ components',
      category: 'Design',
    },
  ];

  const guardrails = [
    {
      name: 'Code Quality Gates',
      description:
        'Automated quality checks ensuring enterprise-grade code standards',
      icon: Shield,
      color: 'text-green-400',
    },
    {
      name: 'Security Protocols',
      description: 'Multi-layer security validation and vulnerability scanning',
      icon: Lock,
      color: 'text-red-400',
    },
    {
      name: 'Performance Monitoring',
      description: 'Real-time performance tracking and optimization alerts',
      icon: Zap,
      color: 'text-yellow-400',
    },
    {
      name: 'Type Safety Enforcement',
      description: 'Strict TypeScript validation preventing runtime errors',
      icon: Target,
      color: 'text-blue-400',
    },
  ];
</script>

<svelte:head>
  <title
    >AI Tools Arsenal - VybeCoding.ai | The Tech Stack That Built This Platform</title
  >
  <meta
    name="description"
    content="Discover the complete AI tools arsenal used to build VybeCoding.ai - Claude Sonnet 4, Augment Code, BMAD Method, and cutting-edge protocols"
  />
</svelte:head>

<!-- AGGRESSIVE AI TOOLS ARSENAL PAGE -->
<main class="min-h-screen relative overflow-hidden">
  <!-- Dynamic background -->
  <div
    class="absolute inset-0 bg-gradient-to-br from-slate-950 via-slate-900 to-slate-950"
  ></div>
  <div
    class="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(6,182,212,0.15),transparent_50%)]"
  ></div>
  <div
    class="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(236,72,153,0.1),transparent_50%)]"
  ></div>
  <div
    class="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(139,92,246,0.08),transparent_50%)]"
  ></div>

  <!-- Floating Geometric Elements -->
  <div class="absolute inset-0 pointer-events-none">
    <div
      class="absolute top-32 right-1/4 w-4 h-4 bg-cyan-400 rotate-45 animate-float opacity-60"
    ></div>
    <div
      class="absolute bottom-1/3 left-1/4 w-6 h-6 bg-pink-400 rounded-full animate-float-delayed opacity-40"
    ></div>
    <div
      class="absolute top-2/3 right-1/3 w-3 h-3 bg-purple-400 animate-float-slow opacity-50"
    ></div>
    <div
      class="absolute top-1/4 left-1/3 w-5 h-5 bg-cyan-300 rounded-full animate-pulse opacity-30"
    ></div>
  </div>

  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10 py-16">
    <!-- AGGRESSIVE HERO SECTION -->
    <div class="text-center mb-20 animate-on-scroll">
      <!-- Badge -->
      <div
        class="inline-flex items-center px-6 py-3 rounded-full bg-gradient-to-r from-purple-500/20 to-blue-500/20 border border-purple-500/30 mb-8"
      >
        <Sword class="w-5 h-5 text-purple-400 mr-3" />
        <span class="text-sm font-medium text-purple-300">AI TOOLS ARSENAL</span
        >
      </div>

      <!-- AGGRESSIVE HEADLINE -->
      <h1 class="text-7xl md:text-8xl font-black mb-8 leading-tight">
        <span
          class="bg-gradient-to-r from-purple-400 via-blue-400 to-cyan-400 bg-clip-text text-transparent"
        >
          🛠️ TECH
        </span>
        <br />
        <span
          class="bg-gradient-to-r from-cyan-400 via-green-400 to-emerald-400 bg-clip-text text-transparent"
        >
          ARSENAL
        </span>
        <br />
        <span class="text-white font-black text-5xl md:text-6xl"
          >⚔️ REVEALED</span
        >
      </h1>

      <!-- AGGRESSIVE DESCRIPTION -->
      <p
        class="text-2xl md:text-3xl text-slate-200 mb-12 leading-relaxed max-w-5xl mx-auto font-semibold font-sans"
        style="line-height: 1.7; letter-spacing: 0.01em;"
      >
        <span class="block mb-8">
          Behold the <span
            class="text-purple-400 font-black bg-purple-400/20 px-4 py-2 rounded inline-block mx-2"
            >legendary tech stack</span
          > that built VybeCoding.ai!
        </span>
        <span class="block mt-8">
          Every <span
            class="text-cyan-400 font-black bg-cyan-400/20 px-4 py-2 rounded inline-block mx-2"
            >AI TOOL</span
          >,
          <span
            class="text-green-400 font-black bg-green-400/20 px-4 py-2 rounded inline-block mx-2"
            >PROTOCOL</span
          >, and
          <span
            class="text-blue-400 font-black bg-blue-400/20 px-4 py-2 rounded inline-block mx-2"
            >GUARDRAIL</span
          > is exposed.
        </span>
      </p>

      <!-- REAL STATS -->
      <div class="grid grid-cols-3 gap-8 mb-12 max-w-4xl mx-auto">
        <div class="text-center">
          <div class="text-4xl font-black text-purple-400 mb-2">🛠️ 9</div>
          <div class="text-sm text-slate-400 font-bold">CORE TOOLS</div>
        </div>
        <div class="text-center">
          <div class="text-4xl font-black text-cyan-400 mb-2">⚡ 2025</div>
          <div class="text-sm text-slate-400 font-bold">CUTTING EDGE</div>
        </div>
        <div class="text-center">
          <div class="text-4xl font-black text-green-400 mb-2">🔒 100%</div>
          <div class="text-sm text-slate-400 font-bold">AI-POWERED</div>
        </div>
      </div>
    </div>

    <!-- AI TOOLS ARSENAL GRID -->
    <div
      class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20 animate-on-scroll"
    >
      {#each aiToolsArsenal as tool, index}
        <div
          class="group relative animate-on-scroll"
          style="animation-delay: {index * 0.1}s"
        >
          <!-- Main card -->
          <div
            class="relative h-full p-8 rounded-3xl bg-slate-800/40 backdrop-blur-sm border border-slate-700/50 hover:border-cyan-400/50 transition-all duration-700 group-hover:scale-105 group-hover:-translate-y-2 overflow-hidden"
          >
            <!-- Gradient overlay -->
            <div
              class="absolute inset-0 rounded-3xl bg-gradient-to-br {tool.gradient}/10 opacity-0 group-hover:opacity-100 transition-opacity duration-700"
            ></div>

            <!-- Animated border glow -->
            <div
              class="absolute inset-0 rounded-3xl bg-gradient-to-r {tool.gradient} opacity-0 group-hover:opacity-30 blur-xl transition-opacity duration-700"
            ></div>

            <!-- Content -->
            <div class="relative z-10">
              <!-- Category Badge -->
              <div
                class="inline-flex items-center px-3 py-1 rounded-full bg-gradient-to-r {tool.gradient}/20 border border-current border-opacity-30 mb-4"
              >
                <span class="text-xs font-bold text-white">{tool.category}</span
                >
              </div>

              <!-- Icon -->
              <div
                class="w-16 h-16 rounded-2xl bg-gradient-to-br {tool.gradient} p-0.5 mb-6 group-hover:scale-110 transition-transform duration-500"
              >
                <div
                  class="w-full h-full rounded-2xl bg-slate-800 flex items-center justify-center"
                >
                  <svelte:component
                    this={tool.icon}
                    class="w-8 h-8 text-white"
                  />
                </div>
              </div>

              <!-- Tool Name -->
              <h3
                class="text-2xl font-bold mb-2 text-white transition-all duration-500"
              >
                {tool.name}
              </h3>

              <!-- Role -->
              <div class="text-sm font-black text-cyan-400 mb-4 tracking-wider">
                {tool.role}
              </div>

              <!-- Description -->
              <p
                class="text-slate-300 leading-relaxed group-hover:text-slate-200 transition-colors duration-300 mb-4"
              >
                {tool.description}
              </p>

              <!-- Stats -->
              <div
                class="inline-flex items-center px-3 py-1 rounded-full bg-gradient-to-r {tool.gradient}/20 border border-current border-opacity-30"
              >
                <span class="text-xs font-bold text-white">📊 {tool.stats}</span
                >
              </div>
            </div>
          </div>
        </div>
      {/each}
    </div>

    <!-- GUARDRAILS SECTION -->
    <div class="mb-20 animate-on-scroll">
      <!-- Section Header -->
      <div class="text-center mb-16">
        <div
          class="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-red-500/20 to-orange-500/20 border border-red-500/30 mb-6"
        >
          <Shield class="w-4 h-4 text-red-400 mr-2" />
          <span class="text-sm font-medium text-red-300"
            >SECURITY PROTOCOLS</span
          >
        </div>

        <h2 class="text-4xl md:text-5xl font-bold mb-6 leading-tight">
          <span
            class="bg-gradient-to-r from-white via-red-200 to-white bg-clip-text text-transparent"
          >
            GUARDRAILS &
          </span>
          <br />
          <span
            class="bg-gradient-to-r from-red-400 via-orange-400 to-yellow-400 bg-clip-text text-transparent"
          >
            PROTOCOLS
          </span>
        </h2>

        <p class="text-xl text-slate-300 max-w-3xl mx-auto leading-relaxed">
          The security fortress protecting every line of AI-generated code with
          enterprise-grade validation and monitoring.
        </p>
      </div>

      <!-- Guardrails Grid -->
      <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
        {#each guardrails as guardrail, index}
          <div
            class="group relative animate-on-scroll"
            style="animation-delay: {index * 0.15}s"
          >
            <!-- Main card -->
            <div
              class="relative h-full p-6 rounded-2xl bg-slate-800/40 backdrop-blur-sm border border-slate-700/50 hover:border-red-400/50 transition-all duration-500 group-hover:scale-105 text-center"
            >
              <!-- Icon -->
              <div
                class="w-12 h-12 rounded-xl bg-gradient-to-br from-red-500 to-orange-500 p-0.5 mb-4 mx-auto group-hover:scale-110 transition-transform duration-300"
              >
                <div
                  class="w-full h-full rounded-xl bg-slate-800 flex items-center justify-center"
                >
                  <svelte:component
                    this={guardrail.icon}
                    class="w-6 h-6 {guardrail.color}"
                  />
                </div>
              </div>

              <!-- Name -->
              <h3 class="text-lg font-bold mb-3 text-white">
                {guardrail.name}
              </h3>

              <!-- Description -->
              <p class="text-slate-400 text-sm leading-relaxed">
                {guardrail.description}
              </p>
            </div>
          </div>
        {/each}
      </div>
    </div>

    <!-- AGGRESSIVE CTA SECTION -->
    <div class="text-center animate-on-scroll">
      <div
        class="relative p-12 rounded-3xl bg-slate-800/40 backdrop-blur-sm border border-slate-700/50 max-w-4xl mx-auto"
      >
        <!-- Gradient overlay -->
        <div
          class="absolute inset-0 rounded-3xl bg-gradient-to-br from-purple-500/10 to-blue-500/10"
        ></div>

        <!-- Animated border glow -->
        <div
          class="absolute inset-0 rounded-3xl bg-gradient-to-r from-purple-400 to-blue-500 opacity-20 blur-xl"
        ></div>

        <!-- Content -->
        <div class="relative z-10">
          <!-- Badge -->
          <div
            class="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-purple-500/20 to-blue-500/20 border border-purple-500/30 mb-6"
          >
            <Crown class="w-4 h-4 text-purple-400 mr-2" />
            <span class="text-sm font-medium text-purple-300"
              >MASTER THE ARSENAL</span
            >
          </div>

          <!-- Heading -->
          <h2 class="text-4xl md:text-5xl font-bold mb-6 leading-tight">
            <span
              class="bg-gradient-to-r from-white via-purple-200 to-white bg-clip-text text-transparent"
            >
              READY TO WIELD
            </span>
            <br />
            <span
              class="bg-gradient-to-r from-purple-400 via-blue-400 to-cyan-400 bg-clip-text text-transparent"
            >
              THESE TOOLS?
            </span>
          </h2>

          <!-- Description -->
          <p
            class="text-xl text-slate-300 mb-8 max-w-2xl mx-auto leading-relaxed"
          >
            Join the elite developers who master these legendary AI tools. Start
            your journey to becoming an AI development warrior.
          </p>

          <!-- CTA Buttons -->
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onclick={() => (window.location.href = '/courses')}
              class="group px-8 py-4 bg-gradient-to-r from-purple-500 via-blue-500 to-cyan-500 hover:from-purple-400 hover:via-blue-400 hover:to-cyan-400 text-white font-black text-xl rounded-2xl transition-all duration-300 hover:scale-110 hover:shadow-2xl hover:shadow-purple-500/50 border-4 border-purple-400"
            >
              <div class="flex items-center justify-center">
                <Rocket class="w-6 h-6 mr-3" />
                MASTER THE TOOLS
                <ArrowRight
                  class="w-6 h-6 ml-3 group-hover:translate-x-2 transition-transform duration-300"
                />
              </div>
            </button>
            <button
              onclick={() => (window.location.href = '/mas')}
              class="group px-8 py-4 bg-gradient-to-r from-cyan-600 via-teal-600 to-green-600 hover:from-cyan-500 hover:via-teal-500 hover:to-green-500 text-white font-black text-xl rounded-2xl transition-all duration-300 hover:scale-110 hover:shadow-2xl hover:shadow-cyan-500/50 border-4 border-cyan-400"
            >
              <div class="flex items-center justify-center">
                <Bot class="w-6 h-6 mr-3" />
                TRY MAS CONTROL
                <ArrowRight
                  class="w-6 h-6 ml-3 group-hover:translate-x-2 transition-transform duration-300"
                />
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</main>

<style>
  /* Scroll-triggered animations */
  .animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .animate-on-scroll.animate-in {
    opacity: 1;
    transform: translateY(0);
  }

  /* Floating animations for geometric elements */
  @keyframes float {
    0%,
    100% {
      transform: translateY(0px) rotate(0deg);
    }
    33% {
      transform: translateY(-10px) rotate(120deg);
    }
    66% {
      transform: translateY(5px) rotate(240deg);
    }
  }

  @keyframes float-delayed {
    0%,
    100% {
      transform: translateY(0px) scale(1);
    }
    50% {
      transform: translateY(-15px) scale(1.1);
    }
  }

  @keyframes float-slow {
    0%,
    100% {
      transform: translateY(0px) rotate(0deg);
    }
    50% {
      transform: translateY(-8px) rotate(180deg);
    }
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-float-delayed {
    animation: float-delayed 8s ease-in-out infinite;
  }

  .animate-float-slow {
    animation: float-slow 10s ease-in-out infinite;
  }

  /* Performance optimizations */
  .gpu-accelerated {
    transform: translateZ(0);
    will-change: transform, opacity;
  }

  /* Accessibility improvements */
  @media (prefers-reduced-motion: reduce) {
    .animate-on-scroll,
    .animate-float,
    .animate-float-delayed,
    .animate-float-slow {
      animation: none;
      transition: none;
    }
  }
</style>
