<script lang="ts">
  import { onMount } from 'svelte';
  import {
    Mail,
    Phone,
    MessageCircle,
    Send,
    Zap,
    Target,
    ArrowRight,
    Sparkles,
    User,
  } from 'lucide-svelte';

  let name = $state('');
  let email = $state('');
  let subject = $state('');
  let message = $state('');
  let loading = $state(false);
  let errors = $state({
    name: '',
    email: '',
    subject: '',
    message: ''
  });
  let mounted = false;

  onMount(() => {
    mounted = true;
    initializeScrollAnimations();
    initializeParticleBackground();
  });

  function validateForm() {
    errors = { name: '', email: '', subject: '', message: '' };
    let isValid = true;

    // Name validation
    if (!name.trim()) {
      errors.name = 'Name is required';
      isValid = false;
    } else if (name.trim().length < 2) {
      errors.name = 'Name must be at least 2 characters';
      isValid = false;
    }

    // Enhanced email validation
    if (!email.trim()) {
      errors.email = 'Email is required';
      isValid = false;
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      errors.email = 'Please enter a valid email address';
      isValid = false;
    } else {
      // Check for common disposable email domains
      const disposableDomains = ['10minutemail.com', 'tempmail.org', 'guerrillamail.com', 'mailinator.com'];
      const domain = email.split('@')[1]?.toLowerCase();
      if (domain && disposableDomains.includes(domain)) {
        errors.email = 'Please use a permanent email address';
        isValid = false;
      }
    }

    // Subject validation
    if (!subject.trim()) {
      errors.subject = 'Subject is required';
      isValid = false;
    } else if (subject.trim().length < 5) {
      errors.subject = 'Subject must be at least 5 characters';
      isValid = false;
    }

    // Enhanced message validation with spam detection
    if (!message.trim()) {
      errors.message = 'Message is required';
      isValid = false;
    } else if (message.trim().length < 10) {
      errors.message = 'Message must be at least 10 characters';
      isValid = false;
    } else if (message.trim().length > 1000) {
      errors.message = 'Message must be less than 1000 characters';
      isValid = false;
    } else {
      // Check for spam patterns
      const spamKeywords = ['viagra', 'casino', 'lottery', 'winner', 'click here', 'free money'];
      const messageWords = message.toLowerCase().split(/\s+/);
      const spamWordCount = messageWords.filter(word =>
        spamKeywords.some(spam => word.includes(spam))
      ).length;

      if (spamWordCount > 0) {
        errors.message = 'Message contains inappropriate content';
        isValid = false;
      }

      // Check for excessive capitalization
      const capsCount = (message.match(/[A-Z]/g) || []).length;
      const capsRatio = capsCount / message.length;
      if (capsRatio > 0.5) {
        errors.message = 'Please reduce excessive capitalization';
        isValid = false;
      }
    }

    return isValid;
  }

  async function handleSubmit() {
    if (!validateForm()) {
      return;
    }

    loading = true;

    try {
      // Real form submission to Appwrite
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name,
          email,
          subject,
          message,
          timestamp: new Date().toISOString()
        })
      });

      if (!response.ok) {
        throw new Error('Failed to send message');
      }

      const result = await response.json();

      alert(
        '🚀 MESSAGE DEPLOYED! Our elite response team will STRIKE BACK within 24 hours!'
      );

      // Clear form on success
      name = '';
      email = '';
      subject = '';
      message = '';
    } catch (error) {
      console.error('Contact form error:', error);
      alert('Failed to send message. Please try again or contact us directly.');
    } finally {
      loading = false;
    }
  }

  // Scroll-triggered animations
  function initializeScrollAnimations() {
    const observer = new IntersectionObserver(
      entries => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('animate-in');
          }
        });
      },
      { threshold: 0.1 }
    );

    // Use requestAnimationFrame for real browser timing
    requestAnimationFrame(() => {
      document.querySelectorAll('.animate-on-scroll').forEach(el => {
        observer.observe(el);
      });
    });
  }

  // Particle background
  function initializeParticleBackground() {
    const canvas = document.createElement('canvas');
    canvas.className = 'particle-canvas';
    canvas.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: -1;
      opacity: 0.3;
    `;
    document.body.appendChild(canvas);

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;

    const particles: Array<{
      x: number;
      y: number;
      vx: number;
      vy: number;
      size: number;
      opacity: number;
      color: string;
    }> = [];

    for (let i = 0; i < 25; i++) {
      particles.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        vx: (Math.random() - 0.5) * 0.3,
        vy: (Math.random() - 0.5) * 0.3,
        size: Math.random() * 1.5 + 0.5,
        opacity: Math.random() * 0.3 + 0.1,
        color: Math.random() > 0.5 ? '#06b6d4' : '#ec4899',
      });
    }

    function animateParticles() {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      particles.forEach(particle => {
        particle.x += particle.vx;
        particle.y += particle.vy;

        if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
        if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;

        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fillStyle = particle.color;
        ctx.globalAlpha = particle.opacity;
        ctx.fill();
      });

      requestAnimationFrame(animateParticles);
    }
    animateParticles();

    window.addEventListener('resize', () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    });
  }

  const contactMethods = [
    {
      icon: Mail,
      title: '⚡ EMAIL STRIKE',
      description: 'DEPLOY your message directly to our command center',
      contact: '<EMAIL>',
      action: 'mailto:<EMAIL>',
      gradient: 'from-red-500 to-orange-500',
    },
    {
      icon: Phone,
      title: '📞 VOICE ASSAULT',
      description: 'ENGAGE our elite support squadron directly',
      contact: '+1 (555) VYBE-AI',
      action: 'tel:******-VYBE-AI',
      gradient: 'from-blue-500 to-cyan-500',
    },
    {
      icon: MessageCircle,
      title: '💬 LIVE COMBAT',
      description: 'REAL-TIME tactical communication warfare',
      contact: 'Available 24/7 for BATTLE',
      action: '#',
      gradient: 'from-purple-500 to-pink-500',
    },
  ];
</script>

<svelte:head>
  <title>Contact Us - VybeCoding.ai | Get Support and Connect</title>
  <meta
    name="description"
    content="Contact VybeCoding.ai for support, questions, or partnership opportunities. Multiple ways to reach our team."
  />
</svelte:head>

<!-- AGGRESSIVE COMMUNICATION WARFARE HOMEPAGE -->
<main class="min-h-screen relative overflow-hidden">
  <!-- Dynamic background -->
  <div
    class="absolute inset-0 bg-gradient-to-br from-slate-950 via-slate-900 to-slate-950"
  ></div>
  <div
    class="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(6,182,212,0.15),transparent_50%)]"
  ></div>
  <div
    class="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(236,72,153,0.1),transparent_50%)]"
  ></div>
  <div
    class="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(139,92,246,0.08),transparent_50%)]"
  ></div>

  <!-- Floating Geometric Elements -->
  <div class="absolute inset-0 pointer-events-none">
    <div
      class="absolute top-32 right-1/4 w-4 h-4 bg-cyan-400 rotate-45 animate-float opacity-60"
    ></div>
    <div
      class="absolute bottom-1/3 left-1/4 w-6 h-6 bg-pink-400 rounded-full animate-float-delayed opacity-40"
    ></div>
    <div
      class="absolute top-2/3 right-1/3 w-3 h-3 bg-purple-400 animate-float-slow opacity-50"
    ></div>
    <div
      class="absolute top-1/4 left-1/3 w-5 h-5 bg-cyan-300 rounded-full animate-pulse opacity-30"
    ></div>
  </div>

  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10 py-16">
    <!-- AGGRESSIVE HERO SECTION -->
    <div class="text-center mb-20 animate-on-scroll">
      <!-- Badge -->
      <div
        class="inline-flex items-center px-6 py-3 rounded-full bg-gradient-to-r from-red-500/20 to-orange-500/20 border border-red-500/30 mb-8"
      >
        <Zap class="w-5 h-5 text-red-400 mr-3" />
        <span class="text-sm font-medium text-red-300"
          >COMMUNICATION WARFARE</span
        >
      </div>

      <!-- AGGRESSIVE HEADLINE -->
      <h1 class="text-7xl md:text-8xl font-black mb-8 leading-tight">
        <span
          class="bg-gradient-to-r from-red-400 via-orange-400 to-yellow-400 bg-clip-text text-transparent"
        >
          🔥 CONTACT
        </span>
        <br />
        <span
          class="bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400 bg-clip-text text-transparent"
        >
          COMMAND
        </span>
        <br />
        <span class="text-white font-black text-5xl md:text-6xl">⚔️ CENTER</span
        >
      </h1>

      <!-- AGGRESSIVE DESCRIPTION -->
      <p
        class="text-2xl md:text-3xl text-slate-200 mb-12 leading-relaxed max-w-5xl mx-auto font-bold"
      >
        DEPLOY your message to our
        <span class="text-red-400 font-black bg-red-400/20 px-3 py-1 rounded"
          >ELITE RESPONSE TEAM</span
        >! We DOMINATE communication with
        <span class="text-cyan-400 font-black bg-cyan-400/20 px-3 py-1 rounded"
          >LIGHTNING-FAST RESPONSES</span
        >
        and
        <span
          class="text-purple-400 font-black bg-purple-400/20 px-3 py-1 rounded"
          >TACTICAL PRECISION</span
        >!
      </p>

      <!-- AGGRESSIVE STATS -->
      <div class="grid grid-cols-3 gap-8 mb-12 max-w-4xl mx-auto">
        <div class="text-center">
          <div class="text-4xl font-black text-red-400 mb-2">⚡ &lt;1hr</div>
          <div class="text-sm text-slate-400 font-bold">RESPONSE TIME</div>
        </div>
        <div class="text-center">
          <div class="text-4xl font-black text-cyan-400 mb-2">🎯 100%</div>
          <div class="text-sm text-slate-400 font-bold">MISSION SUCCESS</div>
        </div>
        <div class="text-center">
          <div class="text-4xl font-black text-purple-400 mb-2">🔥 24/7</div>
          <div class="text-sm text-slate-400 font-bold">COMBAT READY</div>
        </div>
      </div>
    </div>

    <!-- CONTACT METHODS GRID -->
    <section class="mb-20 animate-on-scroll">
      <div class="grid md:grid-cols-3 gap-8 mb-16">
        {#each contactMethods as method}
          {@const IconComponent = method.icon}
          <div
            class="group relative p-8 rounded-3xl bg-slate-800/60 backdrop-blur-xl border border-slate-700/50 hover:border-purple-400/50 transition-all duration-500 hover:scale-105"
          >
            <!-- Gradient overlay -->
            <div
              class="absolute inset-0 rounded-3xl bg-gradient-to-br {method.gradient}/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
            ></div>

            <!-- Content -->
            <div class="relative z-10 text-center">
              <div
                class="w-16 h-16 rounded-2xl bg-gradient-to-r {method.gradient} flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300"
              >
                <IconComponent class="w-8 h-8 text-white" />
              </div>
              <h3 class="text-2xl font-bold text-white mb-4">{method.title}</h3>
              <p class="text-slate-300 mb-6 leading-relaxed">
                {method.description}
              </p>
              <p class="font-bold text-cyan-400 mb-6 text-lg">
                {method.contact}
              </p>
              <a
                href={method.action}
                class="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r {method.gradient} text-white font-bold rounded-2xl hover:scale-105 transition-all duration-300"
              >
                <span>ENGAGE</span>
                <ArrowRight class="w-4 h-4" />
              </a>
            </div>
          </div>
        {/each}
      </div>
    </section>

    <!-- CONTACT FORM -->
    <section class="animate-on-scroll">
      <div
        class="relative p-10 rounded-3xl bg-slate-800/60 backdrop-blur-xl border border-slate-700/50 shadow-2xl"
      >
        <!-- Gradient overlay -->
        <div
          class="absolute inset-0 rounded-3xl bg-gradient-to-br from-purple-500/10 to-cyan-500/10"
        ></div>

        <!-- Content -->
        <div class="relative z-10">
          <div class="text-center mb-10">
            <h2 class="text-4xl font-black text-white mb-4">
              🚀 DEPLOY YOUR MESSAGE
            </h2>
            <p class="text-xl text-slate-300">
              Send us your tactical communication and we'll
              <span class="text-cyan-400 font-bold">STRIKE BACK</span> within 24
              hours!
            </p>
          </div>

          <form
            onsubmit={e => {
              e.preventDefault();
              handleSubmit();
            }}
            class="space-y-6"
          >
            <div class="grid md:grid-cols-2 gap-6">
              <div class="space-y-2">
                <label for="name" class="block text-sm font-bold text-white">
                  <User class="w-4 h-4 inline mr-2" />
                  Name
                </label>
                <input
                  id="name"
                  type="text"
                  bind:value={name}
                  placeholder="Name"
                  required
                  disabled={loading}
                  class="w-full px-4 py-3 rounded-2xl bg-slate-700/50 border {errors.name ? 'border-red-500' : 'border-slate-600'} text-white placeholder-slate-400 focus:border-cyan-400 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300 disabled:opacity-50"
                />
                {#if errors.name}
                  <p class="text-red-400 text-sm mt-1">{errors.name}</p>
                {/if}
              </div>
              <div class="space-y-2">
                <label for="email" class="block text-sm font-bold text-white">
                  <Mail class="w-4 h-4 inline mr-2" />
                  Email
                </label>
                <input
                  id="email"
                  type="email"
                  bind:value={email}
                  placeholder="Email address"
                  required
                  disabled={loading}
                  class="w-full px-4 py-3 rounded-2xl bg-slate-700/50 border {errors.email ? 'border-red-500' : 'border-slate-600'} text-white placeholder-slate-400 focus:border-cyan-400 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300 disabled:opacity-50"
                />
                {#if errors.email}
                  <p class="text-red-400 text-sm mt-1">{errors.email}</p>
                {/if}
              </div>
            </div>

            <div class="space-y-2">
              <label for="subject" class="block text-sm font-bold text-white">
                <Target class="w-4 h-4 inline mr-2" />
                Mission Objective
              </label>
              <input
                id="subject"
                type="text"
                bind:value={subject}
                placeholder="Subject"
                required
                disabled={loading}
                class="w-full px-4 py-3 rounded-2xl bg-slate-700/50 border border-slate-600 text-white placeholder-slate-400 focus:border-cyan-400 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300 disabled:opacity-50"
              />
            </div>

            <div class="space-y-2">
              <label for="message" class="block text-sm font-bold text-white">
                <MessageCircle class="w-4 h-4 inline mr-2" />
                Battle Plan
              </label>
              <textarea
                id="message"
                bind:value={message}
                placeholder="Message"
                required
                disabled={loading}
                class="w-full px-4 py-3 rounded-2xl bg-slate-700/50 border border-slate-600 text-white placeholder-slate-400 focus:border-cyan-400 focus:ring-2 focus:ring-cyan-400/20 transition-all duration-300 disabled:opacity-50 h-32 resize-none"
              ></textarea>
            </div>

            <button
              type="submit"
              disabled={loading}
              class="w-full px-8 py-4 bg-gradient-to-r from-red-500 via-orange-500 to-yellow-500 hover:from-red-400 hover:via-orange-400 hover:to-yellow-400 text-white font-black text-xl rounded-2xl transition-all duration-300 hover:scale-105 hover:shadow-2xl hover:shadow-orange-500/50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {#if loading}
                <div class="flex items-center justify-center">
                  <div
                    class="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-3"
                  ></div>
                  DEPLOYING MESSAGE...
                </div>
              {:else}
                <div class="flex items-center justify-center">
                  <Send class="w-6 h-6 mr-3" />
                  🚀 DEPLOY MESSAGE
                  <Sparkles class="w-6 h-6 ml-3" />
                </div>
              {/if}
            </button>
          </form>
        </div>
      </div>
    </section>
  </div>
</main>
