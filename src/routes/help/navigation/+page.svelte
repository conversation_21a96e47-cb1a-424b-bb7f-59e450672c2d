<script lang="ts">
  import { goto } from '$app/navigation';
</script>

<svelte:head>
  <title
    >Navigation Help - VybeCoding.ai | Understanding Platform Features</title
  >
  <meta
    name="description"
    content="Learn about VybeCoding.ai platform features: MAS Control, Workspace, Courses, and more."
  />
</svelte:head>

<div class="min-h-screen bg-gray-50 py-12">
  <div class="max-w-4xl mx-auto px-4">
    <div class="text-center mb-12">
      <h1 class="text-4xl font-bold text-gray-900 mb-4">
        Platform Navigation Guide
      </h1>
      <p class="text-xl text-gray-600">
        Understanding the different features and tools available in
        VybeCoding.ai
      </p>
    </div>

    <div class="space-y-8">
      <!-- MAS Control -->
      <div class="bg-white rounded-lg shadow-sm border p-6">
        <div class="flex items-center gap-3 mb-4">
          <div
            class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center"
          >
            <span class="text-2xl">🤖</span>
          </div>
          <div>
            <h2 class="text-2xl font-bold text-gray-900">MAS Control</h2>
            <p class="text-blue-600 font-medium">
              Multi-Agent System Control Panel
            </p>
          </div>
        </div>

        <p class="text-gray-600 mb-4">
          The MAS Control panel is where you interact with our 7 BMAD Method AI
          agents. This is the heart of the Vybe Method - a collaborative
          workspace where AI agents work together on your projects.
        </p>

        <div class="grid md:grid-cols-2 gap-4 mb-4">
          <div>
            <h4 class="font-semibold text-gray-900 mb-2">What you can do:</h4>
            <ul class="text-sm text-gray-600 space-y-1">
              <li>• Start collaborative AI tasks</li>
              <li>• Monitor agent status and activity</li>
              <li>• View consensus framework</li>
              <li>• Track task history</li>
              <li>• Manage BMAD workflows</li>
            </ul>
          </div>
          <div>
            <h4 class="font-semibold text-gray-900 mb-2">Best for:</h4>
            <ul class="text-sm text-gray-600 space-y-1">
              <li>• Project planning and analysis</li>
              <li>• Architecture design</li>
              <li>• Story generation</li>
              <li>• Quality validation</li>
              <li>• Full BMAD workflows</li>
            </ul>
          </div>
        </div>

        <div class="flex gap-3">
          <button
            onclick={() => goto('/mas')}
            class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
          >
            Open MAS Control
          </button>
          <button
            onclick={() => goto('/courses/vybe-method-intro')}
            class="bg-gray-100 text-gray-700 px-4 py-2 rounded hover:bg-gray-200 transition-colors"
          >
            Learn More
          </button>
        </div>
      </div>

      <!-- Workspace -->
      <div class="bg-white rounded-lg shadow-sm border p-6">
        <div class="flex items-center gap-3 mb-4">
          <div
            class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center"
          >
            <span class="text-2xl">💻</span>
          </div>
          <div>
            <h2 class="text-2xl font-bold text-gray-900">Workspace</h2>
            <p class="text-green-600 font-medium">Development Environment</p>
          </div>
        </div>

        <p class="text-gray-600 mb-4">
          Your personal development workspace where you write code, manage
          projects, and build applications. This is where you implement the
          plans created by the MAS Control agents.
        </p>

        <div class="grid md:grid-cols-2 gap-4 mb-4">
          <div>
            <h4 class="font-semibold text-gray-900 mb-2">What you can do:</h4>
            <ul class="text-sm text-gray-600 space-y-1">
              <li>• Write and edit code</li>
              <li>• Manage project files</li>
              <li>• Run and test applications</li>
              <li>• Version control with Git</li>
              <li>• Deploy applications</li>
            </ul>
          </div>
          <div>
            <h4 class="font-semibold text-gray-900 mb-2">Best for:</h4>
            <ul class="text-sm text-gray-600 space-y-1">
              <li>• Hands-on coding</li>
              <li>• Project implementation</li>
              <li>• Testing and debugging</li>
              <li>• File management</li>
              <li>• Application deployment</li>
            </ul>
          </div>
        </div>

        <div class="flex gap-3">
          <button
            onclick={() => goto('/workspace')}
            class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors"
          >
            Open Workspace
          </button>
          <button
            onclick={() => goto('/docs')}
            class="bg-gray-100 text-gray-700 px-4 py-2 rounded hover:bg-gray-200 transition-colors"
          >
            View Documentation
          </button>
        </div>
      </div>

      <!-- Courses -->
      <div class="bg-white rounded-lg shadow-sm border p-6">
        <div class="flex items-center gap-3 mb-4">
          <div
            class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center"
          >
            <span class="text-2xl">📚</span>
          </div>
          <div>
            <h2 class="text-2xl font-bold text-gray-900">Courses</h2>
            <p class="text-purple-600 font-medium">Learning Platform</p>
          </div>
        </div>

        <p class="text-gray-600 mb-4">
          Structured learning paths to master the Vybe Method and AI-powered
          development. Start with our free Introduction course to understand the
          fundamentals.
        </p>

        <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
          <div class="flex items-center gap-2 mb-2">
            <span class="text-green-600">🎯</span>
            <span class="font-semibold text-green-800"
              >Recommended Starting Point</span
            >
          </div>
          <p class="text-green-700 text-sm">
            Begin with "Introduction to the Vybe Method" - it's free and teaches
            you everything you need to know to use MAS Control effectively.
          </p>
        </div>

        <div class="flex gap-3">
          <button
            onclick={() => goto('/courses')}
            class="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 transition-colors"
          >
            Browse Courses
          </button>
          <button
            onclick={() => goto('/courses/vybe-method-intro')}
            class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors"
          >
            Start Free Course
          </button>
        </div>
      </div>

      <!-- Vybe Qubes -->
      <div class="bg-white rounded-lg shadow-sm border p-6">
        <div class="flex items-center gap-3 mb-4">
          <div
            class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center"
          >
            <span class="text-2xl">🎲</span>
          </div>
          <div>
            <h2 class="text-2xl font-bold text-gray-900">Vybe Qubes</h2>
            <p class="text-orange-600 font-medium">Community Projects</p>
          </div>
        </div>

        <p class="text-gray-600 mb-4">
          Explore projects built by the community using the Vybe Method. See
          real examples of profitable applications created with AI assistance.
        </p>

        <button
          onclick={() => goto('/vybeqube')}
          class="bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700 transition-colors"
        >
          Explore Vybe Qubes
        </button>
      </div>

      <!-- Quick Start Guide -->
      <div
        class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border p-6"
      >
        <h2 class="text-2xl font-bold text-gray-900 mb-4">
          🚀 Quick Start Guide
        </h2>

        <div class="space-y-3">
          <div class="flex items-center gap-3">
            <span
              class="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold"
              >1</span
            >
            <span
              >Start with the <strong>free Introduction course</strong> to learn
              the Vybe Method</span
            >
          </div>
          <div class="flex items-center gap-3">
            <span
              class="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold"
              >2</span
            >
            <span
              >Create an account and explore the <strong>MAS Control</strong> panel</span
            >
          </div>
          <div class="flex items-center gap-3">
            <span
              class="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold"
              >3</span
            >
            <span
              >Use the <strong>Workspace</strong> to implement your AI-generated
              plans</span
            >
          </div>
          <div class="flex items-center gap-3">
            <span
              class="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold"
              >4</span
            >
            <span
              >Share your projects in <strong>Vybe Qubes</strong> and join the community</span
            >
          </div>
        </div>

        <div class="mt-6">
          <button
            onclick={() => goto('/courses/vybe-method-intro')}
            class="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-colors font-semibold"
          >
            Start Your Journey →
          </button>
        </div>
      </div>
    </div>

    <!-- Back to Home -->
    <div class="text-center mt-12">
      <button
        onclick={() => goto('/')}
        class="text-gray-600 hover:text-gray-800 transition-colors"
      >
        ← Back to Home
      </button>
    </div>
  </div>
</div>
