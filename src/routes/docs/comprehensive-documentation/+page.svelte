<script lang="ts">
  import { onMount } from 'svelte';

  // Documentation data
  const docs = {
  "type": "documentation",
  "title": " - Comprehensive Documentation",
  "sections": [
    {
      "title": "Overview",
      "content": "Complete guide to  for . This documentation covers everything from basic concepts to advanced implementation."
    },
    {
      "title": "Getting Started",
      "content": "Step-by-step setup instructions, prerequisites, and initial configuration."
    },
    {
      "title": "Core Concepts",
      "content": "Fundamental principles, terminology, and key concepts you need to understand."
    },
    {
      "title": "Implementation Guide",
      "content": "Detailed implementation instructions with code examples and best practices."
    },
    {
      "title": "Troubleshooting",
      "content": "Common issues, solutions, and debugging techniques."
    }
  ],
  "target_audience": "",
  "complexity_level": "intermediate",
  "generated_at": "2025-06-06T03:20:24.257Z",
  "agents_used": [
    "CODEX",
    "PIXY",
    "QUBERT",
    "DUCKY"
  ],
  "inspirationSources": [],
  "supportingDocs": [],
  "additionalNotes": "",
  "agent_activities": [
    "QUBERT structuring documentation complexity",
    "PIXY designing documentation layout",
    "DUCKY reviewing technical accuracy"
  ]
};

  let activeSection = 0;

  onMount(() => {
    // Track documentation view
    console.log('Documentation viewed:', docs.title);
  });
</script>

<svelte:head>
  <title>{docs.title} - VybeCoding.ai Documentation</title>
  <meta name="description" content={docs.sections?.[0]?.content || ''} />
</svelte:head>

<div class="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900">
  <div class="container mx-auto px-4 py-8">
    <!-- Documentation Header -->
    <div class="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 mb-8">
      <h1 class="text-4xl font-bold text-white mb-4">{docs.title}</h1>
      <div class="flex flex-wrap gap-2 mb-4">
        <span class="bg-cyan-600 text-white px-3 py-1 rounded-full text-sm">
          {docs.complexity_level || 'Intermediate'}
        </span>
        <span class="bg-purple-600 text-white px-3 py-1 rounded-full text-sm">
          {docs.sections?.length || 0} Sections
        </span>
        <span class="bg-orange-600 text-white px-3 py-1 rounded-full text-sm">
          Generated by MAS
        </span>
      </div>

      <div class="text-sm text-gray-400">
        <span>Target Audience: {docs.target_audience || docs.targetAudience || 'Developers'}</span>
        <span class="ml-4">Generated: {new Date(docs.generated_at || docs.createdAt || Date.now()).toLocaleDateString()}</span>
      </div>
    </div>

    <!-- Documentation Sections -->
    {#if docs.sections && docs.sections.length > 0}
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- Table of Contents -->
        <div class="lg:col-span-1">
          <div class="bg-gray-800/50 backdrop-blur-sm rounded-lg p-4 sticky top-4">
            <h3 class="text-lg font-bold text-white mb-4">Table of Contents</h3>
            <nav class="space-y-2">
              {#each docs.sections as section, index}
                <button
                  class="w-full text-left px-3 py-2 rounded-lg transition-colors {activeSection === index ? 'bg-cyan-600 text-white' : 'text-gray-300 hover:bg-gray-700'}"
                  on:click={() => activeSection = index}
                >
                  {section.title}
                </button>
              {/each}
            </nav>
          </div>
        </div>

        <!-- Content -->
        <div class="lg:col-span-3">
          <div class="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6">
            <h2 class="text-2xl font-bold text-white mb-4">
              {docs.sections[activeSection].title}
            </h2>
            <div class="prose prose-invert max-w-none">
              <p class="text-gray-300 leading-relaxed">
                {docs.sections[activeSection].content}
              </p>
            </div>
          </div>
        </div>
      </div>
    {/if}

    <!-- Documentation Metadata -->
    <div class="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 mt-8">
      <h3 class="text-xl font-bold text-white mb-4">Documentation Information</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
        {#if docs.agents_used}
          <div class="md:col-span-2">
            <span class="text-gray-400">MAS Agents:</span>
            <span class="text-cyan-400 ml-2">{docs.agents_used.join(', ')}</span>
          </div>
        {/if}
        {#if docs.inspirationSources && docs.inspirationSources.length > 0}
          <div class="md:col-span-2">
            <span class="text-gray-400">Inspiration:</span>
            <div class="mt-2">
              {#each docs.inspirationSources as source}
                <a href={source} class="text-cyan-400 hover:text-cyan-300 block">• {source}</a>
              {/each}
            </div>
          </div>
        {/if}
      </div>
    </div>
  </div>
</div>