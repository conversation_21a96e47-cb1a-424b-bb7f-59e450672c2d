<script>
  let documentation = {
    id: '64326c45',
    title: 'API Documentation',
    content: `<p>This is the API documentation...</p>`,
    sections: [
  {
    "title": "Getting Started",
    "content": "<p>How to get started with our API...</p>"
  }
],
    updatedAt: '2025-06-06T13:36:10.262789'
  };
</script>

<svelte:head>
  <title>{documentation.title} - VybeCoding.ai Docs</title>
</svelte:head>

<div class="documentation">
  <main class="docs-content">
    <header class="docs-header">
      <h1>{documentation.title}</h1>
    </header>

    <div class="content-body">
      {@html documentation.content}
    </div>

    {#each documentation.sections as section}
      <section class="docs-section">
        <h2>{section.title}</h2>
        <div class="section-content">
          {@html section.content}
        </div>
      </section>
    {/each}
  </main>
</div>

<style>
  .documentation {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
  }

  .docs-header {
    margin-bottom: 2rem;
    border-bottom: 1px solid #e2e8f0;
    padding-bottom: 1rem;
  }

  .docs-section {
    margin: 2rem 0;
  }
</style>