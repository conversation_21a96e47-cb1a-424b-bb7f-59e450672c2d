<script lang="ts">
  import { Container, Grid, Card, Button } from '$lib/components/ui';
  import {
    BookOpen,
    Code,
    Users,
    Zap,
    ArrowRight,
    ExternalLink,
  } from 'lucide-svelte';

  const docSections = [
    {
      icon: BookOpen,
      title: 'Getting Started',
      description:
        'Learn the basics of the Vybe Method and how to navigate our platform',
      links: [
        { title: 'Platform Overview', href: '#' },
        { title: 'Your First Course', href: '#' },
        { title: 'Setting Up Your Profile', href: '#' },
        { title: 'Understanding Progress Tracking', href: '#' },
      ],
    },
    {
      icon: Code,
      title: 'Development Guides',
      description: 'Technical documentation for building AI applications',
      links: [
        { title: 'AI Development Fundamentals', href: '#' },
        { title: 'Multi-Agent Systems', href: '#' },
        { title: 'API Integration Guide', href: '#' },
        { title: 'Deployment Best Practices', href: '#' },
      ],
    },
    {
      icon: Users,
      title: 'Community Resources',
      description: 'Connect with other learners and share your projects',
      links: [
        { title: 'Community Guidelines', href: '#' },
        { title: 'Project Showcase', href: '#' },
        { title: 'Study Groups', href: '#' },
        { title: 'Mentorship Program', href: '#' },
      ],
    },
    {
      icon: Zap,
      title: 'Advanced Features',
      description: 'Make the most of our AI-powered learning tools',
      links: [
        { title: 'AI Tutoring System', href: '#' },
        { title: 'Personalized Learning Paths', href: '#' },
        { title: 'Enterprise Features', href: '#' },
        { title: 'API Documentation', href: '#' },
      ],
    },
  ];
</script>

<svelte:head>
  <title>Documentation - VybeCoding.ai | Learn How to Use Our Platform</title>
  <meta
    name="description"
    content="Comprehensive documentation for VybeCoding.ai platform. Learn how to use our AI education tools and features."
  />
</svelte:head>

<main role="main">
  <section class="py-20 bg-gradient-to-br from-primary/10 to-secondary/10">
    <Container>
      <div class="text-center max-w-4xl mx-auto">
        <h1 class="text-4xl md:text-5xl font-bold mb-6">
          <span class="text-primary">Documentation</span>
        </h1>
        <p class="text-xl text-muted-foreground mb-8">
          Everything you need to know about using VybeCoding.ai effectively
        </p>
      </div>
    </Container>
  </section>

  <section class="py-20">
    <Container>
      <Grid cols="auto" gap="large">
        {#each docSections as section}
          <Card class="p-6 hover:shadow-lg transition-shadow h-full">
            <div class="flex flex-col h-full">
              <div
                class="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4"
              >
                <svelte:component
                  this={section.icon}
                  class="w-6 h-6 text-primary"
                />
              </div>
              <h3 class="text-xl font-semibold mb-3">{section.title}</h3>
              <p class="text-muted-foreground mb-6 flex-1">
                {section.description}
              </p>

              <div class="space-y-2">
                {#each section.links as link}
                  <a
                    href={link.href}
                    class="flex items-center justify-between p-2 rounded hover:bg-muted/50 transition-colors group"
                  >
                    <span class="text-sm">{link.title}</span>
                    <ArrowRight
                      class="w-4 h-4 text-muted-foreground group-hover:text-foreground group-hover:translate-x-1 transition-all"
                    />
                  </a>
                {/each}
              </div>
            </div>
          </Card>
        {/each}
      </Grid>
    </Container>
  </section>

  <section class="py-20 bg-muted/50">
    <Container>
      <div class="text-center max-w-3xl mx-auto">
        <h2 class="text-3xl font-bold mb-6">Need More Help?</h2>
        <p class="text-xl text-muted-foreground mb-8">
          Can't find what you're looking for? Our support team is here to help.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <Button href="/contact" class="btn-primary">Contact Support</Button>
          <Button href="/community" variant="outline">
            Join Community
            <ExternalLink class="w-4 h-4 ml-2" />
          </Button>
        </div>
      </div>
    </Container>
  </section>
</main>
