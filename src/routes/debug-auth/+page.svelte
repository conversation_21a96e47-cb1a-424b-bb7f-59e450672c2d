<script lang="ts">
  import { onMount } from 'svelte';

  let status = 'Loading...';
  let error = '';
  let authService: any = null;

  onMount(async () => {
    try {
      status = 'Importing auth service...';

      // Direct import test
      const authModule = await import('../../lib/services/auth.js');
      authService = authModule.authService;

      status = 'Auth service loaded successfully!';
    } catch (err: any) {
      error = `Failed to load auth service: ${err.message}`;
      status = 'Error';
      console.error('Auth service import error:', err);
    }
  });

  async function testSignUp() {
    if (!authService) {
      error = 'Auth service not loaded';
      return;
    }

    try {
      status = 'Testing sign up...';
      error = '';

      const testEmail = `test-${Date.now()}@vybecoding.ai`;
      const testPassword = 'testpassword123';
      const testName = 'Test User';

      console.log('Attempting signup with:', {
        email: testEmail,
        name: testName,
      });

      const user = await authService.signUp(testEmail, testPassword, testName);

      status = `Sign up successful! User ID: ${user.id}`;
      console.log('Signup successful:', user);
    } catch (err: any) {
      error = `Sign up failed: ${err.message}`;
      status = 'Sign up failed';
      console.error('Signup error:', err);
    }
  }

  async function testSignIn() {
    if (!authService) {
      error = 'Auth service not loaded';
      return;
    }

    try {
      status = 'Testing sign in...';
      error = '';

      // Use a test account that should exist
      const testEmail = '<EMAIL>';
      const testPassword = 'testpassword123';

      console.log('Attempting signin with:', { email: testEmail });

      const user = await authService.signIn(testEmail, testPassword);

      status = `Sign in successful! User ID: ${user.id}`;
      console.log('Signin successful:', user);
    } catch (err: any) {
      error = `Sign in failed: ${err.message}`;
      status = 'Sign in failed';
      console.error('Signin error:', err);
    }
  }
</script>

<svelte:head>
  <title>Debug Auth - VybeCoding.ai</title>
</svelte:head>

<div class="min-h-screen bg-slate-900 text-white p-8">
  <div class="max-w-2xl mx-auto">
    <h1 class="text-3xl font-bold mb-8">Debug Authentication</h1>

    <div class="bg-slate-800 rounded-lg p-6 mb-6">
      <h2 class="text-xl font-semibold mb-4">Status</h2>
      <p class="text-lg">{status}</p>

      {#if error}
        <div class="mt-4 p-4 bg-red-900 text-red-200 rounded">
          <strong>Error:</strong>
          {error}
        </div>
      {/if}
    </div>

    <div class="space-y-4">
      <button
        onclick={testSignUp}
        disabled={!authService}
        class="w-full bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 px-6 py-3 rounded-lg font-semibold transition-colors"
      >
        Test Sign Up
      </button>

      <button
        onclick={testSignIn}
        disabled={!authService}
        class="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 px-6 py-3 rounded-lg font-semibold transition-colors"
      >
        Test Sign In
      </button>
    </div>

    <div class="mt-8 text-sm text-slate-400">
      <p>Check the browser console for detailed logs.</p>
      <p>This page tests the auth service directly without form validation.</p>
    </div>
  </div>
</div>
