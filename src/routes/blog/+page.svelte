<script lang="ts">
  import { <PERSON><PERSON>er, <PERSON><PERSON>, <PERSON>, But<PERSON>, Badge } from '$lib/components/ui';
  import { Calendar, User, ArrowRight, BookOpen } from 'lucide-svelte';
  import { onMount } from 'svelte';

  onMount(() => {
    console.log('Blog page viewed');
  });

  const featuredPosts = [
    {
      title: 'The Future of AI Education: Why Traditional Methods Are Failing',
      excerpt:
        'Exploring how the Vybe Method revolutionizes AI learning through practical, project-based education.',
      author: 'Dr. <PERSON>',
      date: '2025-05-28',
      category: 'Education',
      readTime: '8 min read',
      featured: true,
    },
    {
      title: 'Building Your First Profitable AI Application',
      excerpt:
        'Step-by-step guide to creating AI applications that generate real revenue using the Vybe Method.',
      author: '<PERSON>',
      date: '2025-05-25',
      category: 'Tutorial',
      readTime: '12 min read',
      featured: false,
    },
    {
      title: 'Multi-Agent Systems: The Next Frontier in AI Development',
      excerpt:
        'Understanding how autonomous agent systems are transforming software development and business operations.',
      author: 'Dr<PERSON><PERSON>',
      date: '2025-05-22',
      category: 'Technology',
      readTime: '10 min read',
      featured: false,
    },
  ];
</script>

<svelte:head>
  <title>Blog - VybeCoding.ai | AI Development Insights and Tutorials</title>
  <meta
    name="description"
    content="Read the latest insights on AI development, machine learning tutorials, and industry trends from VybeCoding.ai experts."
  />
</svelte:head>

<main role="main">
  <section class="py-20 bg-gradient-to-br from-primary/10 to-secondary/10">
    <Container>
      <div class="text-center max-w-4xl mx-auto">
        <h1 class="text-4xl md:text-5xl font-bold mb-6">
          AI Development <span class="text-primary">Insights</span>
        </h1>
        <p class="text-xl text-muted-foreground mb-8">
          Expert insights, tutorials, and industry trends from the VybeCoding.ai
          team
        </p>
      </div>
    </Container>
  </section>

  <section class="py-20">
    <Container>
      <Grid cols="auto" gap="large">
        {#each featuredPosts as post}
          <Card class="p-6 hover:shadow-lg transition-shadow h-full">
            <div class="flex flex-col h-full">
              {#if post.featured}
                <Badge class="w-fit mb-4">Featured</Badge>
              {/if}

              <h2 class="text-xl font-semibold mb-3">{post.title}</h2>
              <p class="text-muted-foreground mb-4 flex-1">{post.excerpt}</p>

              <div
                class="flex items-center justify-between text-sm text-muted-foreground mb-4"
              >
                <div class="flex items-center gap-4">
                  <span class="flex items-center gap-1">
                    <User class="w-4 h-4" />
                    {post.author}
                  </span>
                  <span class="flex items-center gap-1">
                    <Calendar class="w-4 h-4" />
                    {new Date(post.date).toLocaleDateString()}
                  </span>
                </div>
                <Badge variant="outline">{post.category}</Badge>
              </div>

              <div class="flex items-center justify-between">
                <span class="text-sm text-muted-foreground"
                  >{post.readTime}</span
                >
                <Button variant="outline" size="sm">
                  Read More
                  <ArrowRight class="w-4 h-4 ml-2" />
                </Button>
              </div>
            </div>
          </Card>
        {/each}
      </Grid>

      <div class="text-center mt-12">
        <div class="bg-muted/50 rounded-lg p-8 mb-6">
          <h3 class="text-xl font-semibold mb-4">Stay Updated with AI Development</h3>
          <p class="text-muted-foreground mb-6">
            Get the latest insights on AI development, FOSS tools, and educational content delivered to your inbox.
          </p>
          <div class="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
            <input
              type="email"
              placeholder="Enter your email"
              class="flex-1 px-4 py-2 rounded-md border border-border bg-background"
            />
            <Button type="submit">Subscribe</Button>
          </div>
        </div>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <Button href="/courses">
            <BookOpen class="w-4 h-4 mr-2" />
            Explore Courses
          </Button>
          <Button variant="outline" href="/community">
            Join Community Discussions
          </Button>
        </div>
      </div>
    </Container>
  </section>
</main>
