<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import WorkspaceLayout from '$lib/components/workspace/WorkspaceLayout.svelte';
  import { workspaceActions } from '$lib/stores/workspace';
  import { ArrowLeft, Zap, BookOpen, Code, Lightbulb } from 'lucide-svelte';

  let loading = true;

  onMount(async () => {
    // Load workspace state from localStorage
    workspaceActions.loadWorkspace();
    loading = false;
  });

  function goBack() {
    goto('/courses');
  }
</script>

<svelte:head>
  <title>Interactive Workspace - VybeCoding.ai</title>
  <meta
    name="description"
    content="Interactive coding workspace with AI assistance and real-time execution"
  />
</svelte:head>

{#if loading}
  <!-- Loading Animation -->
  <div class="min-h-screen bg-gradient-mesh flex items-center justify-center">
    <div class="relative">
      <div
        class="w-24 h-24 border-4 border-transparent border-t-blue-500 border-r-purple-500 border-b-pink-500 border-l-cyan-500 rounded-full animate-spin"
      ></div>
      <div
        class="absolute inset-0 w-24 h-24 border-4 border-transparent border-t-purple-500 border-r-pink-500 border-b-cyan-500 border-l-blue-500 rounded-full animate-spin animate-reverse"
      ></div>
      <div
        class="absolute inset-2 w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full animate-pulse"
      ></div>
      <div class="absolute inset-0 flex items-center justify-center">
        <Code class="w-8 h-8 text-white animate-bounce" />
      </div>
    </div>
  </div>
{:else}
  <!-- Workspace Header -->
  <div class="bg-gradient-mesh border-b border-white/10">
    <div class="max-w-7xl mx-auto px-4 py-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-4">
          <button onclick={goBack} class="btn-ghost group">
            <ArrowLeft
              class="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform"
            />
            Back to Courses
          </button>

          <div class="flex items-center gap-3">
            <div
              class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center"
            >
              <Code class="w-5 h-5 text-white" />
            </div>
            <div>
              <h1 class="text-xl font-bold text-white">
                Interactive Workspace
              </h1>
              <p class="text-white/60 text-sm">
                Code, test, and learn with AI assistance
              </p>
            </div>
          </div>
        </div>

        <!-- Feature Badges -->
        <div class="hidden md:flex items-center gap-3">
          <div class="glass-badge">
            <Zap class="w-4 h-4 text-yellow-400" />
            <span class="text-white/80">Real-time Execution</span>
          </div>
          <div class="glass-badge">
            <Lightbulb class="w-4 h-4 text-blue-400" />
            <span class="text-white/80">AI Code Review</span>
          </div>
          <div class="glass-badge">
            <BookOpen class="w-4 h-4 text-green-400" />
            <span class="text-white/80">Interactive Exercises</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Workspace -->
  <div class="h-[calc(100vh-120px)]">
    <WorkspaceLayout />
  </div>
{/if}

<style>
  :global(.animate-reverse) {
    animation-direction: reverse;
  }
</style>
