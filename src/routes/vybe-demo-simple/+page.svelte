<!--
  Vybe Method Demo Page - Simplified Version
  Showcases the new Full Vybe Button functionality
  Implementation by <PERSON> (Developer) based on BMAD analysis
-->
<script lang="ts">
  import FullVybeButton from '$lib/components/vybe/FullVybeButtonSimple.svelte';
  import { <PERSON>rk<PERSON>, ArrowRight, CheckCircle, Globe } from 'lucide-svelte';

  // Demo state
  let contentGenerated = false;
  let lastGeneratedContent: any[] = [];

  // Event handlers
  function handleContentGenerated(event: CustomEvent) {
    contentGenerated = true;
    lastGeneratedContent = event.detail.content;
  }

  function handleError(event: CustomEvent) {
    console.error('Content generation error:', event.detail.error);
  }
</script>

<svelte:head>
  <title>Vybe Method Demo - VybeCoding.ai</title>
  <meta
    name="description"
    content="Experience the revolutionary Vybe Method for content transformation and business automation"
  />
</svelte:head>

<main
  class="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-orange-50"
>
  <!-- Hero Section -->
  <section class="relative overflow-hidden">
    <div
      class="absolute inset-0 bg-gradient-to-r from-indigo-600/10 to-orange-600/10"
    ></div>
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
      <div class="text-center">
        <div
          class="inline-flex items-center px-4 py-2 rounded-full bg-white/80 border border-gray-200 mb-4"
        >
          <Sparkles class="w-4 h-4 mr-2 text-indigo-600" />
          <span class="text-sm font-medium text-gray-700"
            >New Features Available</span
          >
        </div>

        <h1 class="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
          Experience the
          <span
            class="bg-gradient-to-r from-indigo-600 to-orange-600 bg-clip-text text-transparent"
          >
            Vybe Method
          </span>
        </h1>

        <p class="text-xl text-gray-600 mb-12 max-w-3xl mx-auto">
          Transform any content into multiple formats instantly. Generate
          profitable websites with autonomous AI agents. The future of content
          creation and business automation is here.
        </p>

        <!-- Demo Buttons -->
        <div
          class="flex flex-col sm:flex-row gap-6 justify-center items-center"
        >
          <div class="text-center">
            <p class="text-sm text-gray-500 mb-3">
              Transform Content Instantly
            </p>
            <FullVybeButton
              size="lg"
              on:contentGenerated={handleContentGenerated}
              on:error={handleError}
            />
          </div>

          <div class="text-center">
            <p class="text-sm text-gray-500 mb-3">
              Generate Profitable Website
            </p>
            <button
              class="px-8 py-4 text-lg bg-gradient-to-r from-orange-600 to-red-600 text-white rounded-2xl hover:from-orange-700 hover:to-red-700 transition-all duration-300 hover:scale-105 hover:shadow-xl focus:outline-none focus:ring-4 focus:ring-orange-500/50 flex items-center gap-3"
              aria-label="Generate Vybe Qube using autonomous AI agents"
            >
              <Globe class="w-6 h-6" />
              <div class="text-left">
                <div class="font-semibold">🚀 Qube</div>
                <div class="text-xs opacity-90">Generate Business</div>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Features Section -->
  <section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
          Revolutionary AI-Powered Features
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Experience the cutting-edge capabilities that make VybeCoding.ai the
          most advanced platform for content creation and business automation.
        </p>
      </div>

      <div class="grid md:grid-cols-2 gap-12 items-center">
        <!-- Full Vybe Feature -->
        <div class="space-y-6">
          <div
            class="inline-flex items-center px-4 py-2 rounded-full bg-indigo-100 text-indigo-700"
          >
            <Sparkles class="w-4 h-4 mr-2" />
            <span class="text-sm font-medium">Full Vybe Transform</span>
          </div>

          <h3 class="text-2xl font-bold text-gray-900">
            Transform Any Content Instantly
          </h3>

          <p class="text-gray-600 text-lg">
            Input any URL or text and watch our AI agents transform it into
            multiple content formats. Generate news articles, blog posts, and
            course materials with a single click.
          </p>

          <ul class="space-y-3">
            <li class="flex items-center gap-3">
              <CheckCircle class="w-5 h-5 text-green-500" />
              <span>URL to multiple content formats</span>
            </li>
            <li class="flex items-center gap-3">
              <CheckCircle class="w-5 h-5 text-green-500" />
              <span>Text transformation and enhancement</span>
            </li>
            <li class="flex items-center gap-3">
              <CheckCircle class="w-5 h-5 text-green-500" />
              <span>Real-time AI agent collaboration</span>
            </li>
            <li class="flex items-center gap-3">
              <CheckCircle class="w-5 h-5 text-green-500" />
              <span>Professional content output</span>
            </li>
          </ul>
        </div>

        <div
          class="bg-gradient-to-br from-indigo-50 to-purple-50 rounded-2xl p-8"
        >
          <div class="text-center">
            <h4 class="text-lg font-semibold text-gray-900 mb-4">
              Try Full Vybe Transform
            </h4>
            <FullVybeButton
              variant="primary"
              size="md"
              on:contentGenerated={handleContentGenerated}
              on:error={handleError}
            />
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Results Section -->
  {#if contentGenerated && lastGeneratedContent.length > 0}
    <section class="py-20 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gray-900 mb-4">
            🎉 Content Generated Successfully!
          </h2>
          <p class="text-xl text-gray-600">
            Your content has been transformed using the Vybe Method
          </p>
        </div>

        <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {#each lastGeneratedContent as content}
            <div class="bg-white border border-gray-200 rounded-lg p-6">
              <div class="flex items-center gap-2 mb-3">
                <span
                  class="px-2 py-1 bg-indigo-100 text-indigo-700 rounded text-xs font-medium"
                  >{content.type}</span
                >
                <span class="text-sm text-gray-500"
                  >{content.metadata.wordCount} words</span
                >
              </div>
              <h4 class="font-semibold text-gray-900 mb-3">{content.title}</h4>
              <p class="text-gray-600 text-sm line-clamp-3">
                {content.content.substring(0, 150)}...
              </p>
              <div class="mt-4 flex items-center gap-2">
                <span class="text-xs text-gray-500"
                  >Reading time: {content.metadata.readingTime} min</span
                >
              </div>
            </div>
          {/each}
        </div>
      </div>
    </section>
  {/if}

  <!-- CTA Section -->
  <section class="py-20 bg-gradient-to-r from-indigo-600 to-purple-600">
    <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
      <h2 class="text-3xl md:text-4xl font-bold text-white mb-6">
        Ready to Transform Your Content?
      </h2>
      <p class="text-xl text-indigo-100 mb-8">
        Join thousands of creators and businesses using the Vybe Method to
        revolutionize their content strategy.
      </p>
      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <FullVybeButton
          variant="outline"
          size="lg"
          className="bg-white/10 border-white/20 text-white hover:bg-white/20"
          on:contentGenerated={handleContentGenerated}
          on:error={handleError}
        />
        <button
          class="px-8 py-4 text-lg bg-white text-indigo-600 rounded-2xl hover:bg-gray-50 transition-all duration-300 hover:scale-105 font-semibold flex items-center gap-2 justify-center"
        >
          Learn More
          <ArrowRight class="w-5 h-5" />
        </button>
      </div>
    </div>
  </section>
</main>

<style>
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
