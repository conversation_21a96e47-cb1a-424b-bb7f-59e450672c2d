<script lang="ts">
  import { onMount } from 'svelte';

  let testResults: string[] = [];
  let loading = false;

  function addResult(
    message: string,
    type: 'info' | 'success' | 'error' = 'info'
  ) {
    const timestamp = new Date().toLocaleTimeString();
    testResults = [
      ...testResults,
      `[${timestamp}] ${type.toUpperCase()}: ${message}`,
    ];
  }

  async function runComprehensiveTest() {
    loading = true;
    testResults = [];

    addResult('Starting comprehensive auth test...');

    try {
      // Test 1: Environment Variables
      addResult('Testing environment variables...');
      const envVars = {
        VITE_APPWRITE_ENDPOINT: import.meta.env.VITE_APPWRITE_ENDPOINT,
        VITE_APPWRITE_PROJECT_ID: import.meta.env.VITE_APPWRITE_PROJECT_ID,
        VITE_APPWRITE_DATABASE_ID: import.meta.env.VITE_APPWRITE_DATABASE_ID,
      };
      addResult(`Environment variables: ${JSON.stringify(envVars)}`, 'info');

      // Test 2: Appwrite Import
      addResult('Testing Appwrite import...');
      const { Client, Account, ID } = await import('appwrite');
      addResult('Appwrite imported successfully', 'success');

      // Test 3: Client Creation
      addResult('Testing Appwrite client creation...');
      const client = new Client()
        .setEndpoint(
          import.meta.env.VITE_APPWRITE_ENDPOINT ||
            'https://fra.cloud.appwrite.io/v1'
        )
        .setProject(
          import.meta.env.VITE_APPWRITE_PROJECT_ID || '683b200e00153d705da3'
        );

      const account = new Account(client);
      addResult('Appwrite client created successfully', 'success');

      // Test 4: Auth Service Import
      addResult('Testing auth service import...');
      const { authService } = await import('../../lib/services/auth.js');
      addResult('Auth service imported successfully', 'success');

      // Test 5: Basic Account Creation
      addResult('Testing account creation...');
      const testEmail = `test-${Date.now()}@vybecoding.ai`;
      const testPassword = 'testpassword123';
      const testName = 'Test User';

      try {
        const user = await authService.signUp(
          testEmail,
          testPassword,
          testName
        );
        addResult(`Account created successfully: ${user.id}`, 'success');

        // Test 6: Sign Out
        addResult('Testing sign out...');
        await authService.signOut();
        addResult('Sign out successful', 'success');

        // Test 7: Sign In
        addResult('Testing sign in...');
        const signInUser = await authService.signIn(testEmail, testPassword);
        addResult(`Sign in successful: ${signInUser.id}`, 'success');

        // Final cleanup
        await authService.signOut();
        addResult('All tests completed successfully!', 'success');
      } catch (authError: any) {
        addResult(`Auth operation failed: ${authError.message}`, 'error');
        addResult(`Error details: ${JSON.stringify(authError)}`, 'error');
      }
    } catch (error: any) {
      addResult(`Test failed: ${error.message}`, 'error');
      addResult(`Error stack: ${error.stack}`, 'error');
    } finally {
      loading = false;
    }
  }

  onMount(() => {
    addResult('Test page loaded successfully');
  });
</script>

<svelte:head>
  <title>Auth Test - VybeCoding.ai</title>
</svelte:head>

<div class="min-h-screen bg-slate-900 text-white p-8">
  <div class="max-w-4xl mx-auto">
    <h1 class="text-3xl font-bold mb-8">VybeCoding.ai Authentication Test</h1>

    <div class="mb-8">
      <button
        onclick={runComprehensiveTest}
        disabled={loading}
        class="bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 px-6 py-3 rounded-lg font-semibold transition-colors"
      >
        {loading ? 'Running Tests...' : 'Run Comprehensive Auth Test'}
      </button>
    </div>

    <div class="bg-slate-800 rounded-lg p-6">
      <h2 class="text-xl font-semibold mb-4">Test Results</h2>
      <div class="space-y-2 max-h-96 overflow-y-auto">
        {#each testResults as result}
          <div
            class="font-mono text-sm p-2 rounded {result.includes('ERROR:')
              ? 'bg-red-900 text-red-200'
              : result.includes('SUCCESS:')
                ? 'bg-green-900 text-green-200'
                : 'bg-slate-700 text-slate-200'}"
          >
            {result}
          </div>
        {/each}

        {#if testResults.length === 0}
          <div class="text-slate-400 italic">
            No test results yet. Click the button above to run tests.
          </div>
        {/if}
      </div>
    </div>
  </div>
</div>

<style>
  /* Ensure proper scrolling */
  .max-h-96 {
    max-height: 24rem;
  }
</style>
