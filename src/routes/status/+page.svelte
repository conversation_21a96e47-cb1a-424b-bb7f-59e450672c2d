<script lang="ts">
  import { Container, Grid, <PERSON>, Badge } from '$lib/components/ui';
  import { CheckCircle, AlertCircle, XCircle, Clock } from 'lucide-svelte';
  import { onMount } from 'svelte';

  let currentTime = $state(new Date());

  onMount(() => {
    const interval = setInterval(() => {
      currentTime = new Date();
    }, 1000);

    return () => clearInterval(interval);
  });

  const services = [
    {
      name: 'Platform API',
      status: 'operational',
      uptime: '99.98%',
      responseTime: '145ms',
    },
    {
      name: 'Learning Management System',
      status: 'operational',
      uptime: '99.95%',
      responseTime: '89ms',
    },
    {
      name: 'Video Streaming',
      status: 'operational',
      uptime: '99.92%',
      responseTime: '234ms',
    },
    {
      name: 'AI Tutoring Service',
      status: 'operational',
      uptime: '99.87%',
      responseTime: '567ms',
    },
    {
      name: 'Authentication Service',
      status: 'operational',
      uptime: '99.99%',
      responseTime: '67ms',
    },
    {
      name: 'Payment Processing',
      status: 'operational',
      uptime: '99.94%',
      responseTime: '123ms',
    },
  ];

  const incidents = [
    {
      title: 'Scheduled Maintenance - Database Optimization',
      status: 'completed',
      date: '2025-05-28',
      time: '02:00 - 04:00 UTC',
      description: 'Routine database maintenance to improve performance.',
    },
    {
      title: 'Brief API Slowdown',
      status: 'resolved',
      date: '2025-05-25',
      time: '14:30 - 14:45 UTC',
      description:
        'Temporary increase in API response times due to high traffic.',
    },
  ];

  function getStatusIcon(status: string) {
    switch (status) {
      case 'operational':
        return CheckCircle;
      case 'degraded':
        return AlertCircle;
      case 'outage':
        return XCircle;
      case 'maintenance':
        return Clock;
      default:
        return CheckCircle;
    }
  }

  function getStatusColor(status: string) {
    switch (status) {
      case 'operational':
        return 'text-green-500';
      case 'degraded':
        return 'text-yellow-500';
      case 'outage':
        return 'text-red-500';
      case 'maintenance':
        return 'text-blue-500';
      default:
        return 'text-green-500';
    }
  }

  function getStatusBadge(status: string) {
    switch (status) {
      case 'operational':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'degraded':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'outage':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'maintenance':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      default:
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
    }
  }
</script>

<svelte:head>
  <title>System Status - VybeCoding.ai | Platform Health and Uptime</title>
  <meta
    name="description"
    content="Check the current status of VybeCoding.ai services, uptime statistics, and recent incidents."
  />
</svelte:head>

<main role="main">
  <section class="py-20 bg-gradient-to-br from-primary/10 to-secondary/10">
    <Container>
      <div class="text-center max-w-4xl mx-auto">
        <h1 class="text-4xl md:text-5xl font-bold mb-6">
          System <span class="text-primary">Status</span>
        </h1>
        <p class="text-xl text-muted-foreground mb-8">
          Real-time status of VybeCoding.ai services and infrastructure
        </p>

        <!-- Overall Status -->
        <div class="flex items-center justify-center gap-3 mb-4">
          <CheckCircle class="w-6 h-6 text-green-500" />
          <span class="text-lg font-semibold">All Systems Operational</span>
        </div>

        <p class="text-sm text-muted-foreground">
          Last updated: {currentTime.toLocaleString()}
        </p>
      </div>
    </Container>
  </section>

  <!-- Service Status -->
  <section class="py-20">
    <Container>
      <div class="text-center mb-16">
        <h2 class="text-3xl font-bold mb-4">Service Status</h2>
        <p class="text-xl text-muted-foreground">
          Current status of all VybeCoding.ai services
        </p>
      </div>

      <div class="max-w-4xl mx-auto space-y-4">
        {#each services as service}
          <Card class="p-6">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-4">
                <svelte:component
                  this={getStatusIcon(service.status)}
                  class="w-6 h-6 {getStatusColor(service.status)}"
                />
                <div>
                  <h3 class="text-lg font-semibold">{service.name}</h3>
                  <div
                    class="flex items-center gap-4 text-sm text-muted-foreground"
                  >
                    <span>Uptime: {service.uptime}</span>
                    <span>Response: {service.responseTime}</span>
                  </div>
                </div>
              </div>

              <Badge class={getStatusBadge(service.status)}>
                {service.status.charAt(0).toUpperCase() +
                  service.status.slice(1)}
              </Badge>
            </div>
          </Card>
        {/each}
      </div>
    </Container>
  </section>

  <!-- Recent Incidents -->
  <section class="py-20 bg-muted/50">
    <Container>
      <div class="text-center mb-16">
        <h2 class="text-3xl font-bold mb-4">Recent Incidents</h2>
        <p class="text-xl text-muted-foreground">
          Past incidents and maintenance windows
        </p>
      </div>

      <div class="max-w-4xl mx-auto space-y-6">
        {#each incidents as incident}
          <Card class="p-6">
            <div class="flex items-start gap-4">
              <div class="flex-shrink-0 mt-1">
                {#if incident.status === 'resolved' || incident.status === 'completed'}
                  <CheckCircle class="w-5 h-5 text-green-500" />
                {:else}
                  <AlertCircle class="w-5 h-5 text-yellow-500" />
                {/if}
              </div>

              <div class="flex-1">
                <div class="flex items-center gap-3 mb-2">
                  <h3 class="text-lg font-semibold">{incident.title}</h3>
                  <Badge
                    class={incident.status === 'resolved' ||
                    incident.status === 'completed'
                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'}
                  >
                    {incident.status.charAt(0).toUpperCase() +
                      incident.status.slice(1)}
                  </Badge>
                </div>

                <p class="text-muted-foreground mb-2">{incident.description}</p>

                <div class="text-sm text-muted-foreground">
                  {new Date(incident.date).toLocaleDateString()} • {incident.time}
                </div>
              </div>
            </div>
          </Card>
        {/each}
      </div>
    </Container>
  </section>

  <!-- Uptime Statistics -->
  <section class="py-20">
    <Container>
      <div class="text-center mb-16">
        <h2 class="text-3xl font-bold mb-4">Uptime Statistics</h2>
        <p class="text-xl text-muted-foreground">
          30-day uptime performance across all services
        </p>
      </div>

      <Grid cols="auto" gap="large" class="max-w-4xl mx-auto text-center">
        <Card class="p-6">
          <div class="text-3xl font-bold text-green-500 mb-2">99.96%</div>
          <div class="text-muted-foreground">Overall Uptime</div>
        </Card>

        <Card class="p-6">
          <div class="text-3xl font-bold text-blue-500 mb-2">156ms</div>
          <div class="text-muted-foreground">Avg Response Time</div>
        </Card>

        <Card class="p-6">
          <div class="text-3xl font-bold text-purple-500 mb-2">2</div>
          <div class="text-muted-foreground">Incidents This Month</div>
        </Card>

        <Card class="p-6">
          <div class="text-3xl font-bold text-orange-500 mb-2">4h 23m</div>
          <div class="text-muted-foreground">Total Downtime</div>
        </Card>
      </Grid>
    </Container>
  </section>

  <!-- Subscribe to Updates -->
  <section class="py-20 bg-muted/50">
    <Container>
      <div class="text-center max-w-3xl mx-auto">
        <h2 class="text-3xl font-bold mb-6">Stay Updated</h2>
        <p class="text-xl text-muted-foreground mb-8">
          Subscribe to status updates and incident notifications
        </p>
        <div
          class="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto"
        >
          <input
            type="email"
            placeholder="Email address"
            class="input flex-1"
          />
          <button class="btn btn-primary">Subscribe</button>
        </div>
      </div>
    </Container>
  </section>
</main>
