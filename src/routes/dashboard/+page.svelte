<!--
	Educational Dashboard - Maya's Design System v2.0 Showcase
	
	DESIGN IMPLEMENTATION:
	- Demonstrates complete educational component ecosystem
	- Showcases accessibility-first design patterns
	- Integrates all three component tiers (Custom, Melt UI, Third-party)
	- Provides comprehensive learning analytics and progress tracking
-->
<script lang="ts">
  import { onMount } from 'svelte';
  import {
    Container,
    Grid,
    Card,
    Button,
    Badge,
    Progress,
    CourseProgress,
    LearningPathCard,
    SkillAssessment,
    InteractiveTutorial,
    Dialog,
    Tooltip,
  } from '$lib/components/ui';
  import {
    BookOpen,
    Target,
    TrendingUp,
    Award,
    Clock,
    Users,
    Star,
    Zap,
    Brain,
    Code,
    Palette,
    Settings,
    Sparkles,
    Rocket,
    ArrowRight,
    Play,
    CheckCircle,
    TrendingUp as Bar<PERSON><PERSON>,
    Calendar,
    Trophy,
    Flame,
    Activity,
    Lightbulb,
    Shield,
    Heart,
  } from 'lucide-svelte';

  let mounted = false;
  let dashboardRef: HTMLElement;
  let statsRef: HTMLElement;

  // Enhanced learning stats with advanced properties
  let learningStats = {
    streakDays: 12,
    averageSessionTime: 45,
    totalStudyTime: 1260, // in minutes
    masteryLevel: 78,
    nextMilestone: 'Complete 3 more modules to reach Advanced level',
    achievements: [
      'First Course',
      'Week Streak',
      'Code Master',
      'AI Pioneer',
      'Revenue Generator',
    ],
    weeklyGoal: 300, // minutes
    weeklyProgress: 240,
    skillPoints: 2450,
    rank: 'Advanced Developer',
    nextRank: 'AI Expert',
    pointsToNextRank: 550,
    studyStreak: {
      current: 12,
      longest: 18,
      goal: 30,
    },
    recentActivity: [
      {
        type: 'course_completed',
        title: 'AI Fundamentals',
        date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      },
      {
        type: 'skill_unlocked',
        title: 'Advanced Prompting',
        date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      },
      {
        type: 'milestone_reached',
        title: 'First Vybe Qube',
        date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
      },
    ],
    gradient: 'from-cyan-400 to-blue-500',
    color: 'text-cyan-400',
  };

  let courseData = {
    id: 'vybe-method-fundamentals',
    title: 'Vybe Method Fundamentals',
    description: 'Master the core principles of AI-powered development',
    totalModules: 8,
    completedModules: 6,
    totalSkillPoints: 400,
    earnedSkillPoints: 320,
    estimatedDuration: 480, // in minutes
    actualDuration: 420,
    difficultyLevel: 'intermediate' as const,
    // Add missing properties for the UI
    progress: Math.round((6 / 8) * 100), // 75%
    completedLessons: 6,
    totalLessons: 8,
    estimatedTime: 2, // remaining hours
    modules: [
      {
        id: 'intro',
        title: 'Introduction to Vybe Method',
        description: 'Learn the foundational concepts',
        status: 'completed' as const,
        progress: 100,
        estimatedTime: 60,
        actualTime: 55,
        skillPoints: 50,
        prerequisites: [],
      },
      {
        id: 'ai-tools',
        title: 'AI Tool Integration',
        description: 'Master AI-powered development tools',
        status: 'in-progress' as const,
        progress: 75,
        estimatedTime: 90,
        skillPoints: 75,
        prerequisites: ['intro'],
      },
    ],
  };

  let skillData = {
    id: 'frontend-development',
    name: 'Frontend Development',
    description: 'Building modern, accessible user interfaces',
    level: 3,
    maxLevel: 5,
    progress: 78,
    lastAssessed: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
    trend: 'improving' as const,
    competencies: [
      {
        id: 'html-css',
        name: 'HTML & CSS',
        description: 'Semantic markup and modern styling',
        level: 4,
        maxLevel: 5,
        isCore: true,
        examples: ['Semantic HTML5', 'CSS Grid & Flexbox', 'Responsive Design'],
        assessmentCriteria: ['Code quality', 'Accessibility', 'Performance'],
      },
      {
        id: 'javascript',
        name: 'JavaScript',
        description: 'Modern JavaScript and frameworks',
        level: 3,
        maxLevel: 5,
        isCore: true,
        examples: ['ES6+ Features', 'DOM Manipulation', 'Async Programming'],
        assessmentCriteria: [
          'Code organization',
          'Error handling',
          'Performance',
        ],
      },
    ],
  };

  let learningPaths = [
    {
      id: 'ai-development',
      title: 'AI-Powered Development',
      description:
        'Learn to build applications using AI tools and methodologies',
      difficulty: 'intermediate' as const,
      duration: 24,
      moduleCount: 12,
      completedModules: 8,
      enrolledStudents: 1247,
      rating: 4.8,
      tags: ['AI', 'Development', 'Automation'],
      prerequisites: ['JavaScript Fundamentals'],
      learningObjectives: [
        'Master AI tool integration',
        'Build autonomous systems',
      ],
      instructor: {
        name: 'Dr. Sarah Chen',
        avatar: '/avatars/sarah-chen.jpg',
        title: 'AI Development Expert',
      },
      thumbnail: '/courses/ai-development.jpg',
      isEnrolled: true,
      isCompleted: false,
      progress: 67,
      estimatedCompletion: '2 weeks',
      nextModule: {
        id: 'advanced-prompting',
        title: 'Advanced Prompting Techniques',
      },
      // Add missing properties for the UI
      icon: Rocket,
      courses: 12,
      students: 1247,
    },
  ];

  let showWelcomeDialog = false;
  let currentTime = new Date();

  function getGreeting(): string {
    const hour = currentTime.getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 18) return 'Good afternoon';
    return 'Good evening';
  }

  function handleCourseAction(event: CustomEvent) {
    console.log('Course action:', event.detail);
  }

  function handleSkillAssessment(event: CustomEvent) {
    console.log('Skill assessment:', event.detail);
  }

  function handleLearningPathAction(event: CustomEvent) {
    console.log('Learning path action:', event.detail);
  }

  onMount(() => {
    mounted = true;

    // Initialize advanced cursor effects
    initializeCursorEffects();

    // Initialize scroll animations
    initializeScrollAnimations();

    // Initialize particle background
    initializeParticleBackground();

    // Show welcome dialog for new users
    setTimeout(() => {
      showWelcomeDialog = true;
    }, 1000);
  });

  // Advanced cursor effects (same as other pages)
  function initializeCursorEffects() {
    const cursor = document.createElement('div');
    cursor.className = 'vybe-cursor';
    document.body.appendChild(cursor);

    const cursorGlow = document.createElement('div');
    cursorGlow.className = 'vybe-cursor-glow';
    document.body.appendChild(cursorGlow);

    let mouseX = 0,
      mouseY = 0;
    let cursorX = 0,
      cursorY = 0;

    document.addEventListener('mousemove', e => {
      mouseX = e.clientX;
      mouseY = e.clientY;
    });

    function animateCursor() {
      cursorX += (mouseX - cursorX) * 0.1;
      cursorY += (mouseY - cursorY) * 0.1;

      cursor.style.transform = `translate(${cursorX - 10}px, ${cursorY - 10}px)`;
      cursorGlow.style.transform = `translate(${cursorX - 25}px, ${cursorY - 25}px)`;

      requestAnimationFrame(animateCursor);
    }
    animateCursor();

    // Enhanced hover effects
    document.addEventListener(
      'mouseenter',
      e => {
        const target = e.target as HTMLElement;
        if (target.matches('.vybe-interactive, button, a, .card')) {
          cursor.classList.add('cursor-hover');
          cursorGlow.classList.add('cursor-hover');
        }
      },
      true
    );

    document.addEventListener(
      'mouseleave',
      e => {
        const target = e.target as HTMLElement;
        if (target.matches('.vybe-interactive, button, a, .card')) {
          cursor.classList.remove('cursor-hover');
          cursorGlow.classList.remove('cursor-hover');
        }
      },
      true
    );
  }

  // Scroll-triggered animations
  function initializeScrollAnimations() {
    const observer = new IntersectionObserver(
      entries => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('animate-in');
          }
        });
      },
      { threshold: 0.1 }
    );

    // Observe all animatable elements
    setTimeout(() => {
      document.querySelectorAll('.animate-on-scroll').forEach(el => {
        observer.observe(el);
      });
    }, 100);
  }

  // Particle background system
  function initializeParticleBackground() {
    const canvas = document.createElement('canvas');
    canvas.className = 'particle-canvas';
    canvas.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: -1;
      opacity: 0.2;
    `;
    document.body.appendChild(canvas);

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;

    const particles: Array<{
      x: number;
      y: number;
      vx: number;
      vy: number;
      size: number;
      opacity: number;
      color: string;
    }> = [];

    // Create particles
    for (let i = 0; i < 25; i++) {
      particles.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        vx: (Math.random() - 0.5) * 0.2,
        vy: (Math.random() - 0.5) * 0.2,
        size: Math.random() * 1.5 + 0.5,
        opacity: Math.random() * 0.3 + 0.1,
        color: Math.random() > 0.5 ? '#06b6d4' : '#ec4899',
      });
    }

    function animateParticles() {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      particles.forEach(particle => {
        particle.x += particle.vx;
        particle.y += particle.vy;

        if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
        if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;

        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fillStyle = particle.color;
        ctx.globalAlpha = particle.opacity;
        ctx.fill();
      });

      requestAnimationFrame(animateParticles);
    }
    animateParticles();

    // Handle resize
    window.addEventListener('resize', () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    });
  }
</script>

<svelte:head>
  <title>Learning Dashboard | VybeCoding.ai</title>
  <meta
    name="description"
    content="Your personalized learning dashboard with progress tracking, skill assessments, and course recommendations."
  />
</svelte:head>

<!-- Advanced Dashboard Layout -->
<main class="min-h-screen relative overflow-hidden">
  <!-- Dynamic background -->
  <div
    class="absolute inset-0 bg-gradient-to-br from-slate-950 via-slate-900 to-slate-950"
  ></div>
  <div
    class="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(6,182,212,0.1),transparent_50%)]"
  ></div>
  <div
    class="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(236,72,153,0.08),transparent_50%)]"
  ></div>

  <Container class="py-12 space-y-12 relative z-10">
    <!-- Advanced Header -->
    <div class="flex items-center justify-between animate-on-scroll">
      <div>
        <!-- Greeting with enhanced styling -->
        <div class="flex items-center gap-4 mb-2">
          <div
            class="w-12 h-12 rounded-2xl bg-gradient-to-br from-cyan-400 to-blue-500 p-0.5"
          >
            <div
              class="w-full h-full rounded-2xl bg-slate-800 flex items-center justify-center"
            >
              <span class="text-xl">👋</span>
            </div>
          </div>
          <div>
            <h1
              class="text-4xl md:text-5xl font-bold bg-gradient-to-r from-white via-cyan-200 to-white bg-clip-text text-transparent"
            >
              {getGreeting()}, Alex!
            </h1>
            <p class="text-slate-300 text-lg mt-1">
              Ready to continue your <span class="text-cyan-400 font-semibold"
                >AI learning journey</span
              >?
            </p>
          </div>
        </div>

        <!-- Quick status indicators -->
        <div class="flex items-center gap-6 mt-4">
          <div class="flex items-center gap-2">
            <div
              class="w-3 h-3 rounded-full bg-emerald-400 animate-pulse"
            ></div>
            <span class="text-sm text-slate-400">Online & Learning</span>
          </div>
          <div class="flex items-center gap-2">
            <Flame class="w-4 h-4 text-orange-400" />
            <span class="text-sm text-slate-400"
              >{learningStats.streakDays} day streak</span
            >
          </div>
          <div class="flex items-center gap-2">
            <Trophy class="w-4 h-4 text-amber-400" />
            <span class="text-sm text-slate-400">{learningStats.rank}</span>
          </div>
        </div>
      </div>

      <!-- Action buttons -->
      <div class="flex gap-3">
        <Tooltip
          content="View your learning analytics and detailed progress reports"
        >
          <Button
            class="vybe-interactive group px-6 py-3 bg-slate-800/50 backdrop-blur-sm border border-slate-600 hover:border-cyan-400 text-white rounded-2xl transition-all duration-300 hover:scale-105"
          >
            <TrendingUp class="w-5 h-5 mr-2 text-cyan-400" />
            Analytics
            <ArrowRight
              class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300"
            />
          </Button>
        </Tooltip>

        <Tooltip
          content="Customize your learning preferences and notification settings"
        >
          <Button
            class="vybe-interactive p-3 bg-slate-800/50 backdrop-blur-sm border border-slate-600 hover:border-purple-400 text-white rounded-2xl transition-all duration-300 hover:scale-105"
          >
            <Settings class="w-5 h-5 text-purple-400" />
          </Button>
        </Tooltip>
      </div>
    </div>

    <!-- Advanced Quick Stats -->
    <div
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 animate-on-scroll"
    >
      <!-- Streak Stats -->
      <div class="vybe-interactive group relative">
        <div
          class="relative p-8 rounded-3xl bg-slate-800/40 backdrop-blur-sm border border-slate-700/50 hover:border-orange-400/50 transition-all duration-500 group-hover:scale-105"
        >
          <!-- Gradient overlay -->
          <div
            class="absolute inset-0 rounded-3xl bg-gradient-to-br from-orange-500/10 to-red-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
          ></div>

          <!-- Content -->
          <div class="relative z-10 text-center">
            <!-- Icon -->
            <div
              class="w-16 h-16 rounded-2xl bg-gradient-to-br from-orange-400 to-red-500 p-0.5 mb-6 mx-auto group-hover:scale-110 transition-transform duration-300"
            >
              <div
                class="w-full h-full rounded-2xl bg-slate-800 flex items-center justify-center"
              >
                <Flame class="w-8 h-8 text-orange-400" />
              </div>
            </div>

            <!-- Value -->
            <div
              class="text-4xl font-bold mb-2 bg-gradient-to-r from-orange-400 to-red-500 bg-clip-text text-transparent"
            >
              {learningStats.streakDays}
            </div>

            <!-- Label -->
            <div class="text-lg font-semibold text-white mb-2">Day Streak</div>

            <!-- Progress to goal -->
            <div class="text-sm text-slate-400">
              Goal: {learningStats.studyStreak.goal} days
            </div>
          </div>

          <!-- Animated border glow -->
          <div
            class="absolute inset-0 rounded-3xl bg-gradient-to-r from-orange-400 to-red-500 opacity-0 group-hover:opacity-20 blur-xl transition-opacity duration-500"
          ></div>
        </div>
      </div>

      <!-- Study Time Stats -->
      <div class="vybe-interactive group relative">
        <div
          class="relative p-8 rounded-3xl bg-slate-800/40 backdrop-blur-sm border border-slate-700/50 hover:border-cyan-400/50 transition-all duration-500 group-hover:scale-105"
        >
          <!-- Gradient overlay -->
          <div
            class="absolute inset-0 rounded-3xl bg-gradient-to-br from-cyan-500/10 to-blue-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
          ></div>

          <!-- Content -->
          <div class="relative z-10 text-center">
            <!-- Icon -->
            <div
              class="w-16 h-16 rounded-2xl bg-gradient-to-br from-cyan-400 to-blue-500 p-0.5 mb-6 mx-auto group-hover:scale-110 transition-transform duration-300"
            >
              <div
                class="w-full h-full rounded-2xl bg-slate-800 flex items-center justify-center"
              >
                <Clock class="w-8 h-8 text-cyan-400" />
              </div>
            </div>

            <!-- Value -->
            <div
              class="text-4xl font-bold mb-2 bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent"
            >
              {Math.round(learningStats.totalStudyTime / 60)}h
            </div>

            <!-- Label -->
            <div class="text-lg font-semibold text-white mb-2">Study Time</div>

            <!-- Weekly progress -->
            <div class="text-sm text-slate-400">
              This week: {Math.round(learningStats.weeklyProgress / 60)}h
            </div>
          </div>

          <!-- Animated border glow -->
          <div
            class="absolute inset-0 rounded-3xl bg-gradient-to-r from-cyan-400 to-blue-500 opacity-0 group-hover:opacity-20 blur-xl transition-opacity duration-500"
          ></div>
        </div>
      </div>

      <!-- Mastery Stats -->
      <div class="vybe-interactive group relative">
        <div
          class="relative p-8 rounded-3xl bg-slate-800/40 backdrop-blur-sm border border-slate-700/50 hover:border-purple-400/50 transition-all duration-500 group-hover:scale-105"
        >
          <!-- Gradient overlay -->
          <div
            class="absolute inset-0 rounded-3xl bg-gradient-to-br from-purple-500/10 to-pink-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
          ></div>

          <!-- Content -->
          <div class="relative z-10 text-center">
            <!-- Icon -->
            <div
              class="w-16 h-16 rounded-2xl bg-gradient-to-br from-purple-400 to-pink-500 p-0.5 mb-6 mx-auto group-hover:scale-110 transition-transform duration-300"
            >
              <div
                class="w-full h-full rounded-2xl bg-slate-800 flex items-center justify-center"
              >
                <Star class="w-8 h-8 text-purple-400 fill-current" />
              </div>
            </div>

            <!-- Value -->
            <div
              class="text-4xl font-bold mb-2 bg-gradient-to-r from-purple-400 to-pink-500 bg-clip-text text-transparent"
            >
              {learningStats.masteryLevel}%
            </div>

            <!-- Label -->
            <div class="text-lg font-semibold text-white mb-2">
              Mastery Level
            </div>

            <!-- Skill points -->
            <div class="text-sm text-slate-400">
              {learningStats.skillPoints} skill points
            </div>
          </div>

          <!-- Animated border glow -->
          <div
            class="absolute inset-0 rounded-3xl bg-gradient-to-r from-purple-400 to-pink-500 opacity-0 group-hover:opacity-20 blur-xl transition-opacity duration-500"
          ></div>
        </div>
      </div>

      <!-- Achievements Stats -->
      <div class="vybe-interactive group relative">
        <div
          class="relative p-8 rounded-3xl bg-slate-800/40 backdrop-blur-sm border border-slate-700/50 hover:border-emerald-400/50 transition-all duration-500 group-hover:scale-105"
        >
          <!-- Gradient overlay -->
          <div
            class="absolute inset-0 rounded-3xl bg-gradient-to-br from-emerald-500/10 to-teal-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
          ></div>

          <!-- Content -->
          <div class="relative z-10 text-center">
            <!-- Icon -->
            <div
              class="w-16 h-16 rounded-2xl bg-gradient-to-br from-emerald-400 to-teal-500 p-0.5 mb-6 mx-auto group-hover:scale-110 transition-transform duration-300"
            >
              <div
                class="w-full h-full rounded-2xl bg-slate-800 flex items-center justify-center"
              >
                <Award class="w-8 h-8 text-emerald-400" />
              </div>
            </div>

            <!-- Value -->
            <div
              class="text-4xl font-bold mb-2 bg-gradient-to-r from-emerald-400 to-teal-500 bg-clip-text text-transparent"
            >
              {learningStats.achievements.length}
            </div>

            <!-- Label -->
            <div class="text-lg font-semibold text-white mb-2">
              Achievements
            </div>

            <!-- Next rank -->
            <div class="text-sm text-slate-400">
              Next: {learningStats.nextRank}
            </div>
          </div>

          <!-- Animated border glow -->
          <div
            class="absolute inset-0 rounded-3xl bg-gradient-to-r from-emerald-400 to-teal-500 opacity-0 group-hover:opacity-20 blur-xl transition-opacity duration-500"
          ></div>
        </div>
      </div>
    </div>

    <!-- Advanced Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 animate-on-scroll">
      <!-- Left Column - Course Progress & Learning Paths -->
      <div class="lg:col-span-2 space-y-8">
        <!-- Current Course Progress -->
        <div class="vybe-interactive group relative">
          <div
            class="relative p-8 rounded-3xl bg-slate-800/40 backdrop-blur-sm border border-slate-700/50 hover:border-cyan-400/50 transition-all duration-500"
          >
            <!-- Gradient overlay -->
            <div
              class="absolute inset-0 rounded-3xl bg-gradient-to-br from-cyan-500/10 to-blue-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
            ></div>

            <!-- Content -->
            <div class="relative z-10">
              <!-- Header -->
              <div class="flex items-center justify-between mb-6">
                <div class="flex items-center gap-3">
                  <div
                    class="w-12 h-12 rounded-2xl bg-gradient-to-br from-cyan-400 to-blue-500 p-0.5"
                  >
                    <div
                      class="w-full h-full rounded-2xl bg-slate-800 flex items-center justify-center"
                    >
                      <BookOpen class="w-6 h-6 text-cyan-400" />
                    </div>
                  </div>
                  <div>
                    <h2 class="text-2xl font-bold text-white">
                      Current Course
                    </h2>
                    <p class="text-slate-400">Continue your learning journey</p>
                  </div>
                </div>
                <Badge
                  class="bg-gradient-to-r from-cyan-500/20 to-blue-500/20 border border-cyan-500/30 text-cyan-300"
                >
                  In Progress
                </Badge>
              </div>

              <!-- Course Info -->
              <div class="mb-6">
                <h3 class="text-xl font-semibold text-white mb-2">
                  {courseData.title}
                </h3>
                <p class="text-slate-300 mb-4">{courseData.description}</p>

                <!-- Progress Bar -->
                <div class="mb-4">
                  <div class="flex items-center justify-between mb-2">
                    <span class="text-sm text-slate-400">Progress</span>
                    <span class="text-sm font-semibold text-cyan-400"
                      >{courseData.progress}%</span
                    >
                  </div>
                  <div
                    class="w-full h-3 bg-slate-700 rounded-full overflow-hidden"
                  >
                    <div
                      class="h-full bg-gradient-to-r from-cyan-400 to-blue-500 rounded-full transition-all duration-500"
                      style="width: {courseData.progress}%"
                    ></div>
                  </div>
                </div>

                <!-- Course Stats -->
                <div class="grid grid-cols-3 gap-4 mb-6">
                  <div class="text-center">
                    <div class="text-lg font-bold text-white">
                      {courseData.completedLessons}
                    </div>
                    <div class="text-xs text-slate-400">Completed</div>
                  </div>
                  <div class="text-center">
                    <div class="text-lg font-bold text-white">
                      {courseData.totalLessons}
                    </div>
                    <div class="text-xs text-slate-400">Total Lessons</div>
                  </div>
                  <div class="text-center">
                    <div class="text-lg font-bold text-white">
                      {courseData.estimatedTime}h
                    </div>
                    <div class="text-xs text-slate-400">Remaining</div>
                  </div>
                </div>
              </div>

              <!-- Action Buttons -->
              <div class="flex gap-3">
                <Button
                  class="flex-1 bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-400 hover:to-blue-500 text-white font-semibold rounded-2xl transition-all duration-300 hover:scale-105"
                >
                  <Play class="w-5 h-5 mr-2" />
                  Continue Learning
                </Button>
                <Button
                  class="px-6 bg-slate-700/50 backdrop-blur-sm border border-slate-600 hover:border-cyan-400 text-white rounded-2xl transition-all duration-300 hover:scale-105"
                >
                  <BarChart class="w-5 h-5" />
                </Button>
              </div>
            </div>

            <!-- Animated border glow -->
            <div
              class="absolute inset-0 rounded-3xl bg-gradient-to-r from-cyan-400 to-blue-500 opacity-0 group-hover:opacity-20 blur-xl transition-opacity duration-500"
            ></div>
          </div>
        </div>

        <!-- Learning Paths -->
        <div class="space-y-6">
          <div class="flex items-center justify-between">
            <h2 class="text-2xl font-bold text-white">
              Recommended Learning Paths
            </h2>
            <Button
              class="bg-slate-800/50 backdrop-blur-sm border border-slate-600 hover:border-purple-400 text-white rounded-2xl transition-all duration-300 hover:scale-105"
            >
              View All
              <ArrowRight class="w-4 h-4 ml-2" />
            </Button>
          </div>

          <div class="grid gap-6">
            {#each learningPaths as path, index}
              <div
                class="vybe-interactive group relative"
                style="animation-delay: {index * 0.1}s"
              >
                <div
                  class="relative p-6 rounded-3xl bg-slate-800/40 backdrop-blur-sm border border-slate-700/50 hover:border-purple-400/50 transition-all duration-500 group-hover:scale-105"
                >
                  <!-- Gradient overlay -->
                  <div
                    class="absolute inset-0 rounded-3xl bg-gradient-to-br from-purple-500/10 to-pink-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                  ></div>

                  <!-- Content -->
                  <div class="relative z-10">
                    <div class="flex items-start justify-between mb-4">
                      <div class="flex items-center gap-3">
                        <div
                          class="w-10 h-10 rounded-xl bg-gradient-to-br from-purple-400 to-pink-500 p-0.5"
                        >
                          <div
                            class="w-full h-full rounded-xl bg-slate-800 flex items-center justify-center"
                          >
                            <svelte:component
                              this={path.icon}
                              class="w-5 h-5 text-purple-400"
                            />
                          </div>
                        </div>
                        <div>
                          <h3 class="text-lg font-semibold text-white">
                            {path.title}
                          </h3>
                          <p class="text-sm text-slate-400">
                            {path.description}
                          </p>
                        </div>
                      </div>
                      <Badge
                        class="bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30 text-purple-300"
                      >
                        {path.difficulty}
                      </Badge>
                    </div>

                    <!-- Path Stats -->
                    <div class="flex items-center gap-6 mb-4">
                      <div class="flex items-center gap-2">
                        <Clock class="w-4 h-4 text-purple-400" />
                        <span class="text-sm text-slate-400"
                          >{path.duration}</span
                        >
                      </div>
                      <div class="flex items-center gap-2">
                        <BookOpen class="w-4 h-4 text-purple-400" />
                        <span class="text-sm text-slate-400"
                          >{path.courses} courses</span
                        >
                      </div>
                      <div class="flex items-center gap-2">
                        <Users class="w-4 h-4 text-purple-400" />
                        <span class="text-sm text-slate-400"
                          >{path.students} students</span
                        >
                      </div>
                    </div>

                    <!-- Action Button -->
                    <Button
                      class="w-full bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-400 hover:to-pink-500 text-white font-semibold rounded-2xl transition-all duration-300 hover:scale-105"
                    >
                      <Rocket class="w-5 h-5 mr-2" />
                      Start Learning Path
                    </Button>
                  </div>

                  <!-- Animated border glow -->
                  <div
                    class="absolute inset-0 rounded-3xl bg-gradient-to-r from-purple-400 to-pink-500 opacity-0 group-hover:opacity-20 blur-xl transition-opacity duration-500"
                  ></div>
                </div>
              </div>
            {/each}
          </div>
        </div>
      </div>

      <!-- Right Column - Skills & Assessment -->
      <div class="space-y-8">
        <!-- Next Milestone -->
        <div class="vybe-interactive group relative">
          <div
            class="relative p-8 rounded-3xl bg-slate-800/40 backdrop-blur-sm border border-slate-700/50 hover:border-emerald-400/50 transition-all duration-500"
          >
            <!-- Gradient overlay -->
            <div
              class="absolute inset-0 rounded-3xl bg-gradient-to-br from-emerald-500/10 to-teal-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
            ></div>

            <!-- Content -->
            <div class="relative z-10">
              <!-- Header -->
              <div class="flex items-center gap-3 mb-6">
                <div
                  class="w-12 h-12 rounded-2xl bg-gradient-to-br from-emerald-400 to-teal-500 p-0.5"
                >
                  <div
                    class="w-full h-full rounded-2xl bg-slate-800 flex items-center justify-center"
                  >
                    <Target class="w-6 h-6 text-emerald-400" />
                  </div>
                </div>
                <div>
                  <h2 class="text-2xl font-bold text-white">Next Milestone</h2>
                  <p class="text-slate-400">Your learning goal</p>
                </div>
              </div>

              <!-- Milestone Info -->
              <div class="mb-6">
                <p class="text-slate-300 mb-4">{learningStats.nextMilestone}</p>

                <!-- Progress to next rank -->
                <div class="mb-4">
                  <div class="flex items-center justify-between mb-2">
                    <span class="text-sm text-slate-400"
                      >Progress to {learningStats.nextRank}</span
                    >
                    <span class="text-sm font-semibold text-emerald-400"
                      >{Math.round(
                        (learningStats.skillPoints /
                          (learningStats.skillPoints +
                            learningStats.pointsToNextRank)) *
                          100
                      )}%</span
                    >
                  </div>
                  <div
                    class="w-full h-3 bg-slate-700 rounded-full overflow-hidden"
                  >
                    <div
                      class="h-full bg-gradient-to-r from-emerald-400 to-teal-500 rounded-full transition-all duration-500"
                      style="width: {Math.round(
                        (learningStats.skillPoints /
                          (learningStats.skillPoints +
                            learningStats.pointsToNextRank)) *
                          100
                      )}%"
                    ></div>
                  </div>
                </div>

                <!-- Points needed -->
                <div class="text-center p-4 rounded-2xl bg-slate-700/30">
                  <div class="text-2xl font-bold text-emerald-400 mb-1">
                    {learningStats.pointsToNextRank}
                  </div>
                  <div class="text-sm text-slate-400">points to next rank</div>
                </div>
              </div>

              <!-- Action Button -->
              <Button
                class="w-full bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-400 hover:to-teal-500 text-white font-semibold rounded-2xl transition-all duration-300 hover:scale-105"
              >
                <Target class="w-5 h-5 mr-2" />
                View Progress Plan
              </Button>
            </div>

            <!-- Animated border glow -->
            <div
              class="absolute inset-0 rounded-3xl bg-gradient-to-r from-emerald-400 to-teal-500 opacity-0 group-hover:opacity-20 blur-xl transition-opacity duration-500"
            ></div>
          </div>
        </div>

        <!-- Recent Achievements -->
        <div class="vybe-interactive group relative">
          <div
            class="relative p-8 rounded-3xl bg-slate-800/40 backdrop-blur-sm border border-slate-700/50 hover:border-amber-400/50 transition-all duration-500"
          >
            <!-- Gradient overlay -->
            <div
              class="absolute inset-0 rounded-3xl bg-gradient-to-br from-amber-500/10 to-orange-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
            ></div>

            <!-- Content -->
            <div class="relative z-10">
              <!-- Header -->
              <div class="flex items-center gap-3 mb-6">
                <div
                  class="w-12 h-12 rounded-2xl bg-gradient-to-br from-amber-400 to-orange-500 p-0.5"
                >
                  <div
                    class="w-full h-full rounded-2xl bg-slate-800 flex items-center justify-center"
                  >
                    <Award class="w-6 h-6 text-amber-400" />
                  </div>
                </div>
                <div>
                  <h2 class="text-2xl font-bold text-white">Achievements</h2>
                  <p class="text-slate-400">Your recent wins</p>
                </div>
              </div>

              <!-- Achievement List -->
              <div class="space-y-3 mb-6">
                {#each learningStats.achievements as achievement}
                  <div
                    class="flex items-center gap-3 p-3 rounded-2xl bg-slate-700/30 hover:bg-slate-700/50 transition-colors duration-300"
                  >
                    <div
                      class="w-8 h-8 rounded-xl bg-gradient-to-br from-amber-400 to-orange-500 p-0.5"
                    >
                      <div
                        class="w-full h-full rounded-xl bg-slate-800 flex items-center justify-center"
                      >
                        <Trophy class="w-4 h-4 text-amber-400" />
                      </div>
                    </div>
                    <div class="flex-1">
                      <div class="text-white font-medium">{achievement}</div>
                      <div class="text-xs text-slate-400">Recently earned</div>
                    </div>
                  </div>
                {/each}
              </div>

              <!-- View All Button -->
              <Button
                class="w-full bg-gradient-to-r from-amber-500 to-orange-600 hover:from-amber-400 hover:to-orange-500 text-white font-semibold rounded-2xl transition-all duration-300 hover:scale-105"
              >
                <Award class="w-5 h-5 mr-2" />
                View All Achievements
              </Button>
            </div>

            <!-- Animated border glow -->
            <div
              class="absolute inset-0 rounded-3xl bg-gradient-to-r from-amber-400 to-orange-500 opacity-0 group-hover:opacity-20 blur-xl transition-opacity duration-500"
            ></div>
          </div>
        </div>

        <!-- Recent Activity -->
        <div class="vybe-interactive group relative">
          <div
            class="relative p-8 rounded-3xl bg-slate-800/40 backdrop-blur-sm border border-slate-700/50 hover:border-purple-400/50 transition-all duration-500"
          >
            <!-- Gradient overlay -->
            <div
              class="absolute inset-0 rounded-3xl bg-gradient-to-br from-purple-500/10 to-pink-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
            ></div>

            <!-- Content -->
            <div class="relative z-10">
              <!-- Header -->
              <div class="flex items-center gap-3 mb-6">
                <div
                  class="w-12 h-12 rounded-2xl bg-gradient-to-br from-purple-400 to-pink-500 p-0.5"
                >
                  <div
                    class="w-full h-full rounded-2xl bg-slate-800 flex items-center justify-center"
                  >
                    <Clock class="w-6 h-6 text-purple-400" />
                  </div>
                </div>
                <div>
                  <h2 class="text-2xl font-bold text-white">Recent Activity</h2>
                  <p class="text-slate-400">Your learning timeline</p>
                </div>
              </div>

              <!-- Activity List -->
              <div class="space-y-4">
                {#each learningStats.recentActivity as activity}
                  <div class="flex items-start gap-3">
                    <div
                      class="w-8 h-8 rounded-xl bg-gradient-to-br from-purple-400 to-pink-500 p-0.5 mt-1"
                    >
                      <div
                        class="w-full h-full rounded-xl bg-slate-800 flex items-center justify-center"
                      >
                        {#if activity.type === 'course_completed'}
                          <BookOpen class="w-4 h-4 text-purple-400" />
                        {:else if activity.type === 'skill_unlocked'}
                          <Star class="w-4 h-4 text-purple-400" />
                        {:else}
                          <Target class="w-4 h-4 text-purple-400" />
                        {/if}
                      </div>
                    </div>
                    <div class="flex-1">
                      <div class="text-white font-medium">{activity.title}</div>
                      <div class="text-xs text-slate-400">
                        {activity.date.toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                {/each}
              </div>
            </div>

            <!-- Animated border glow -->
            <div
              class="absolute inset-0 rounded-3xl bg-gradient-to-r from-purple-400 to-pink-500 opacity-0 group-hover:opacity-20 blur-xl transition-opacity duration-500"
            ></div>
          </div>
        </div>
      </div>
    </div>
  </Container>
</main>

<!-- Welcome Dialog -->
<Dialog
  bind:open={showWelcomeDialog}
  title="Welcome to Your Learning Dashboard!"
  size="md"
>
  <div class="space-y-4">
    <p>
      Your personalized learning dashboard is designed to help you track
      progress, discover new skills, and achieve your learning goals with the
      Vybe Method.
    </p>

    <div class="grid grid-cols-2 gap-4 text-sm">
      <div class="flex items-center gap-2">
        <BookOpen class="w-4 h-4 text-blue-500" />
        <span>Track course progress</span>
      </div>
      <div class="flex items-center gap-2">
        <Target class="w-4 h-4 text-green-500" />
        <span>Assess your skills</span>
      </div>
      <div class="flex items-center gap-2">
        <TrendingUp class="w-4 h-4 text-purple-500" />
        <span>View learning analytics</span>
      </div>
      <div class="flex items-center gap-2">
        <Award class="w-4 h-4 text-yellow-500" />
        <span>Earn achievements</span>
      </div>
    </div>
  </div>

  <svelte:fragment slot="footer" let:close>
    <div class="flex gap-2 justify-end">
      <Button variant="outline" onclick={close}>Skip Tour</Button>
      <Button onclick={close}>Start Learning</Button>
    </div>
  </svelte:fragment>
</Dialog>

<style>
  /* Advanced Vybe Cursor Effects */
  :global(.vybe-cursor) {
    position: fixed;
    width: 20px;
    height: 20px;
    background: linear-gradient(45deg, #06b6d4, #ec4899);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9999;
    mix-blend-mode: difference;
    transition: transform 0.1s ease;
  }

  :global(.vybe-cursor-glow) {
    position: fixed;
    width: 50px;
    height: 50px;
    background: radial-gradient(
      circle,
      rgba(6, 182, 212, 0.3),
      rgba(236, 72, 153, 0.2),
      transparent
    );
    border-radius: 50%;
    pointer-events: none;
    z-index: 9998;
    transition: transform 0.2s ease;
  }

  :global(.vybe-cursor.cursor-hover) {
    transform: scale(1.5);
    background: linear-gradient(45deg, #06b6d4, #ec4899, #8b5cf6);
  }

  :global(.vybe-cursor-glow.cursor-hover) {
    transform: scale(1.8);
    background: radial-gradient(
      circle,
      rgba(6, 182, 212, 0.4),
      rgba(236, 72, 153, 0.3),
      rgba(139, 92, 246, 0.2),
      transparent
    );
  }

  /* Advanced Animation System */
  .animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .animate-on-scroll.animate-in {
    opacity: 1;
    transform: translateY(0);
  }

  /* Interactive elements */
  .vybe-interactive {
    cursor: none;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .vybe-interactive:hover {
    transform: translateY(-5px) scale(1.02);
  }

  /* Enhanced accessibility for dashboard */
  :global(.dashboard-card:focus-within) {
    outline: 2px solid theme('colors.ring');
    outline-offset: 2px;
  }

  /* Responsive design enhancements */
  @media (max-width: 768px) {
    .vybe-cursor,
    .vybe-cursor-glow {
      display: none;
    }

    .vybe-interactive {
      cursor: pointer;
    }
  }

  /* Accessibility improvements */
  @media (prefers-reduced-motion: reduce) {
    .animate-on-scroll,
    .vybe-interactive {
      animation: none;
      transition: none;
    }

    .vybe-cursor,
    .vybe-cursor-glow {
      display: none;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    :global(.dashboard-card) {
      border-width: 2px;
    }

    .bg-gradient-to-r {
      background: none !important;
      color: currentColor !important;
    }

    .text-transparent {
      color: currentColor !important;
      background: none !important;
    }
  }

  /* Focus styles for accessibility */
  .vybe-interactive:focus {
    outline: 2px solid #06b6d4;
    outline-offset: 2px;
  }

  /* Print styles */
  @media print {
    :global(.dashboard-card) {
      break-inside: avoid;
      page-break-inside: avoid;
    }

    .vybe-cursor,
    .vybe-cursor-glow {
      display: none;
    }
  }

  /* Performance optimizations */
  .gpu-accelerated {
    transform: translateZ(0);
    will-change: transform, opacity;
  }

  /* Dark mode specific enhancements */
  @media (prefers-color-scheme: dark) {
    .vybe-cursor {
      mix-blend-mode: screen;
    }
  }
</style>
