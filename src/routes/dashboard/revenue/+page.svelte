<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import RevenueDashboard from '$lib/components/RevenueDashboard.svelte';
  import {
    revenueTrackingService,
    type PayoutHistory,
    type RevenueTransaction,
  } from '$lib/services/revenueTracking';
  import { authService } from '$lib/services/auth';

  // Component state
  let activeTab = 'overview';
  let payoutHistory: PayoutHistory[] = [];
  let recentTransactions: RevenueTransaction[] = [];
  let stripeAccountStatus: any = null;
  let loading = false;

  onMount(async () => {
    // Check if user is authenticated
    const user = await authService.getCurrentUser();
    if (!user) {
      goto('/auth/login');
      return;
    }

    // Load additional data for tabs
    await loadTabData();
  });

  async function loadTabData() {
    try {
      loading = true;

      const [payouts, transactions, accountStatus] = await Promise.all([
        revenueTrackingService.getPayoutHistory(10),
        revenueTrackingService.getTransactions(10),
        revenueTrackingService.getStripeAccountStatus().catch(() => null),
      ]);

      payoutHistory = payouts;
      recentTransactions = transactions;
      stripeAccountStatus = accountStatus;
    } catch (error) {
      console.error('Error loading tab data:', error);
    } finally {
      loading = false;
    }
  }

  function setActiveTab(tab: string) {
    activeTab = tab;
  }

  async function setupStripeAccount() {
    try {
      const user = await authService.getCurrentUser();
      if (!user) return;

      const result = await revenueTrackingService.createStripeAccount(
        user.email
      );

      if (result.onboardingUrl) {
        window.open(result.onboardingUrl, '_blank');
      }
    } catch (error) {
      console.error('Error setting up Stripe account:', error);
      alert('Failed to set up payment account. Please try again.');
    }
  }

  async function openStripeDashboard() {
    try {
      const dashboardUrl =
        await revenueTrackingService.getStripeDashboardLink();
      window.open(dashboardUrl, '_blank');
    } catch (error) {
      console.error('Error opening Stripe dashboard:', error);
      alert('Failed to open payment dashboard. Please try again.');
    }
  }

  function formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  }

  function getStatusBadgeClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'completed':
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }
</script>

<svelte:head>
  <title>Revenue Dashboard - VybeCoding.ai</title>
  <meta
    name="description"
    content="Track your Vybe Qube revenue, analytics, and payouts"
  />
</svelte:head>

<div class="revenue-page">
  <!-- Page Header -->
  <div class="page-header">
    <div class="header-content">
      <h1 class="page-title">Revenue Dashboard</h1>
      <p class="page-subtitle">
        Track your Vybe Qube earnings and manage payouts
      </p>
    </div>

    {#if stripeAccountStatus && !stripeAccountStatus.charges_enabled}
      <div class="setup-banner">
        <div class="banner-content">
          <span class="banner-icon">⚠️</span>
          <div class="banner-text">
            <h3>Payment Setup Required</h3>
            <p>Complete your payment account setup to receive payouts</p>
          </div>
          <button on:click={setupStripeAccount} class="banner-button">
            Complete Setup
          </button>
        </div>
      </div>
    {/if}
  </div>

  <!-- Navigation Tabs -->
  <div class="tab-navigation">
    <button
      class="tab-button {activeTab === 'overview' ? 'active' : ''}"
      on:click={() => setActiveTab('overview')}
    >
      Overview
    </button>
    <button
      class="tab-button {activeTab === 'transactions' ? 'active' : ''}"
      on:click={() => setActiveTab('transactions')}
    >
      Transactions
    </button>
    <button
      class="tab-button {activeTab === 'payouts' ? 'active' : ''}"
      on:click={() => setActiveTab('payouts')}
    >
      Payouts
    </button>
    <button
      class="tab-button {activeTab === 'settings' ? 'active' : ''}"
      on:click={() => setActiveTab('settings')}
    >
      Settings
    </button>
  </div>

  <!-- Tab Content -->
  <div class="tab-content">
    {#if activeTab === 'overview'}
      <RevenueDashboard />
    {:else if activeTab === 'transactions'}
      <div class="transactions-tab">
        <div class="tab-header">
          <h2 class="tab-title">Recent Transactions</h2>
          <button class="refresh-button" on:click={loadTabData}>
            🔄 Refresh
          </button>
        </div>

        {#if loading}
          <div class="loading-state">
            <div
              class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"
            ></div>
            <p class="text-gray-600 mt-2">Loading transactions...</p>
          </div>
        {:else if recentTransactions.length === 0}
          <div class="empty-state">
            <span class="empty-icon">📊</span>
            <h3>No Transactions Yet</h3>
            <p>
              Your Vybe Qube transactions will appear here once you start
              earning revenue.
            </p>
          </div>
        {:else}
          <div class="transactions-list">
            {#each recentTransactions as transaction}
              <div class="transaction-item">
                <div class="transaction-info">
                  <div class="transaction-main">
                    <span class="transaction-id">Qube {transaction.qubeId}</span
                    >
                    <span class="transaction-amount">
                      {revenueTrackingService.formatCurrency(
                        transaction.userRevenue
                      )}
                    </span>
                  </div>
                  <div class="transaction-details">
                    <span class="transaction-date"
                      >{formatDate(transaction.createdAt)}</span
                    >
                    <span
                      class="transaction-status {getStatusBadgeClass(
                        transaction.status
                      )}"
                    >
                      {transaction.status}
                    </span>
                  </div>
                </div>
              </div>
            {/each}
          </div>
        {/if}
      </div>
    {:else if activeTab === 'payouts'}
      <div class="payouts-tab">
        <div class="tab-header">
          <h2 class="tab-title">Payout History</h2>
          {#if stripeAccountStatus?.charges_enabled}
            <button class="dashboard-button" on:click={openStripeDashboard}>
              Open Stripe Dashboard
            </button>
          {/if}
        </div>

        {#if loading}
          <div class="loading-state">
            <div
              class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"
            ></div>
            <p class="text-gray-600 mt-2">Loading payouts...</p>
          </div>
        {:else if payoutHistory.length === 0}
          <div class="empty-state">
            <span class="empty-icon">💳</span>
            <h3>No Payouts Yet</h3>
            <p>
              Your payout history will appear here once you receive your first
              payment.
            </p>
          </div>
        {:else}
          <div class="payouts-list">
            {#each payoutHistory as payout}
              <div class="payout-item">
                <div class="payout-info">
                  <div class="payout-main">
                    <span class="payout-amount">
                      {revenueTrackingService.formatCurrency(payout.amount)}
                    </span>
                    <span
                      class="payout-status {getStatusBadgeClass(payout.status)}"
                    >
                      {payout.status}
                    </span>
                  </div>
                  <div class="payout-details">
                    <span class="payout-date"
                      >Initiated: {formatDate(payout.createdAt)}</span
                    >
                    {#if payout.expectedArrival}
                      <span class="payout-arrival"
                        >Expected: {formatDate(payout.expectedArrival)}</span
                      >
                    {/if}
                    <span class="payout-transactions"
                      >{payout.transactionCount} transactions</span
                    >
                  </div>
                </div>
              </div>
            {/each}
          </div>
        {/if}
      </div>
    {:else if activeTab === 'settings'}
      <div class="settings-tab">
        <div class="tab-header">
          <h2 class="tab-title">Payment Settings</h2>
        </div>

        <div class="settings-content">
          <div class="setting-section">
            <h3 class="setting-title">Payment Account</h3>
            {#if stripeAccountStatus}
              <div class="account-status">
                <div class="status-item">
                  <span class="status-label">Account Status:</span>
                  <span
                    class="status-value {stripeAccountStatus.charges_enabled
                      ? 'text-green-600'
                      : 'text-yellow-600'}"
                  >
                    {stripeAccountStatus.charges_enabled
                      ? 'Active'
                      : 'Setup Required'}
                  </span>
                </div>
                <div class="status-item">
                  <span class="status-label">Payouts Enabled:</span>
                  <span
                    class="status-value {stripeAccountStatus.payouts_enabled
                      ? 'text-green-600'
                      : 'text-red-600'}"
                  >
                    {stripeAccountStatus.payouts_enabled ? 'Yes' : 'No'}
                  </span>
                </div>
              </div>

              {#if stripeAccountStatus.charges_enabled}
                <button class="settings-button" on:click={openStripeDashboard}>
                  Manage Payment Account
                </button>
              {:else}
                <button class="settings-button" on:click={setupStripeAccount}>
                  Complete Account Setup
                </button>
              {/if}
            {:else}
              <div class="no-account">
                <p>
                  No payment account found. Set up your account to receive
                  payouts.
                </p>
                <button class="settings-button" on:click={setupStripeAccount}>
                  Set Up Payment Account
                </button>
              </div>
            {/if}
          </div>

          <div class="setting-section">
            <h3 class="setting-title">Payout Information</h3>
            <div class="info-grid">
              <div class="info-item">
                <span class="info-label">Minimum Payout:</span>
                <span class="info-value">$50.00</span>
              </div>
              <div class="info-item">
                <span class="info-label">Processing Fee:</span>
                <span class="info-value">$0.25 per transaction</span>
              </div>
              <div class="info-item">
                <span class="info-label">Platform Fee:</span>
                <span class="info-value">5% of revenue</span>
              </div>
              <div class="info-item">
                <span class="info-label">Payout Schedule:</span>
                <span class="info-value">Weekly (if eligible)</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    {/if}
  </div>
</div>

<style>
  .revenue-page {
    @apply max-w-7xl mx-auto p-6;
  }

  .page-header {
    @apply mb-8;
  }

  .header-content {
    @apply mb-4;
  }

  .page-title {
    @apply text-3xl font-bold text-gray-900;
  }

  .page-subtitle {
    @apply text-gray-600 mt-2;
  }

  .setup-banner {
    @apply bg-yellow-50 border border-yellow-200 rounded-lg p-4;
  }

  .banner-content {
    @apply flex items-center justify-between;
  }

  .banner-icon {
    @apply text-2xl mr-4;
  }

  .banner-text {
    @apply flex-1;
  }

  .banner-text h3 {
    @apply font-medium text-yellow-800;
  }

  .banner-text p {
    @apply text-yellow-700 text-sm;
  }

  .banner-button {
    @apply bg-yellow-600 text-white px-4 py-2 rounded-md hover:bg-yellow-700;
  }

  .tab-navigation {
    @apply flex border-b border-gray-200 mb-6;
  }

  .tab-button {
    @apply px-6 py-3 text-sm font-medium text-gray-600 hover:text-gray-900 border-b-2 border-transparent hover:border-gray-300;
  }

  .tab-button.active {
    @apply text-blue-600 border-blue-600;
  }

  .tab-content {
    @apply min-h-96;
  }

  .tab-header {
    @apply flex justify-between items-center mb-6;
  }

  .tab-title {
    @apply text-xl font-semibold text-gray-900;
  }

  .refresh-button,
  .dashboard-button,
  .settings-button {
    @apply bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm;
  }

  .loading-state,
  .empty-state {
    @apply flex flex-col items-center justify-center py-12;
  }

  .empty-icon {
    @apply text-4xl mb-4;
  }

  .empty-state h3 {
    @apply text-lg font-medium text-gray-900 mb-2;
  }

  .empty-state p {
    @apply text-gray-600 text-center max-w-md;
  }

  .transactions-list,
  .payouts-list {
    @apply space-y-4;
  }

  .transaction-item,
  .payout-item {
    @apply bg-white border border-gray-200 rounded-lg p-4;
  }

  .transaction-main,
  .payout-main {
    @apply flex justify-between items-center mb-2;
  }

  .transaction-id,
  .payout-amount {
    @apply font-medium text-gray-900;
  }

  .transaction-amount {
    @apply font-semibold text-green-600;
  }

  .transaction-details,
  .payout-details {
    @apply flex justify-between items-center text-sm text-gray-600;
  }

  .transaction-status,
  .payout-status {
    @apply px-2 py-1 rounded-full text-xs font-medium;
  }

  .settings-content {
    @apply space-y-8;
  }

  .setting-section {
    @apply bg-white border border-gray-200 rounded-lg p-6;
  }

  .setting-title {
    @apply text-lg font-medium text-gray-900 mb-4;
  }

  .account-status {
    @apply space-y-2 mb-4;
  }

  .status-item,
  .info-item {
    @apply flex justify-between;
  }

  .status-label,
  .info-label {
    @apply text-gray-600;
  }

  .status-value,
  .info-value {
    @apply font-medium;
  }

  .info-grid {
    @apply grid grid-cols-1 md:grid-cols-2 gap-4;
  }

  .no-account {
    @apply text-center py-4;
  }

  .no-account p {
    @apply text-gray-600 mb-4;
  }
</style>
