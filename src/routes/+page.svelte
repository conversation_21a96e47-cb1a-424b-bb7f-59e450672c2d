<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { userStore } from '../lib/stores/auth';
  import {
    ArrowRight,
    Sparkles,
    Brain,
    Code,
    Rocket,
    CheckCircle,
    Bot,
    FileText,
    MessageCircle,
    Play,
    BookOpen,
    Award
  } from 'lucide-svelte';

  let mounted = false;

  onMount(() => {
    mounted = true;
    initializeScrollAnimations();
    initializeParticleBackground();
  });

  // Redirect authenticated users to dashboard
  $: if (mounted && $userStore) {
    goto('/dashboard');
  }

  // Scroll-triggered animations (borrowed from courses page)
  function initializeScrollAnimations() {
    const observer = new IntersectionObserver(
      entries => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('animate-in');
          }
        });
      },
      { threshold: 0.1 }
    );

    // Use requestAnimationFrame for real browser timing
    requestAnimationFrame(() => {
      document.querySelectorAll('.animate-on-scroll').forEach(el => {
        observer.observe(el);
      });
    });
  }

  // Subtle particle background (borrowed from courses page)
  function initializeParticleBackground() {
    const canvas = document.createElement('canvas');
    canvas.className = 'particle-canvas';
    canvas.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: -1;
      opacity: 0.3;
    `;
    document.body.appendChild(canvas);

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;

    const particles: Array<{
      x: number;
      y: number;
      vx: number;
      vy: number;
      size: number;
      opacity: number;
      color: string;
    }> = [];

    // Create subtle particles
    for (let i = 0; i < 30; i++) {
      particles.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        vx: (Math.random() - 0.5) * 0.3,
        vy: (Math.random() - 0.5) * 0.3,
        size: Math.random() * 1.5 + 0.5,
        opacity: Math.random() * 0.3 + 0.1,
        color: Math.random() > 0.5 ? '#06b6d4' : '#ec4899',
      });
    }

    function animateParticles() {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      particles.forEach(particle => {
        particle.x += particle.vx;
        particle.y += particle.vy;

        if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
        if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;

        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fillStyle = particle.color;
        ctx.globalAlpha = particle.opacity;
        ctx.fill();
      });

      requestAnimationFrame(animateParticles);
    }
    animateParticles();

    window.addEventListener('resize', () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    });
  }

  // Real VybeCoding.ai features
  const features = [
    {
      icon: Brain,
      title: 'Multi-Agent System (MAS)',
      description:
        'Experience real AI collaboration with 7 specialized agents working together using CrewAI and AutoGen frameworks.',
      gradient: 'from-cyan-400 to-blue-500',
    },
    {
      icon: Code,
      title: 'Vybe Method Development',
      description:
        'Learn our proven BMAD Method V3 enhanced with autonomous Multi-Agent Systems for enterprise-grade development.',
      gradient: 'from-emerald-400 to-teal-500',
    },
    {
      icon: Rocket,
      title: '100% FOSS Technology',
      description:
        'Master development with completely open-source tools: SvelteKit, Appwrite.io, local LLMs, and privacy-first architecture.',
      gradient: 'from-purple-400 to-pink-500',
    },
  ];

  // Replace the stats array with new cards and hover facts
  const statCards = [
    {
      value: 'Built 100% by AI',
      icon: CheckCircle,
      hover: 'This entire website was designed, coded, and launched using only AI agents—no human coding required!'
    },
    {
      value: 'Follow the Method',
      icon: MessageCircle,
      hover: 'The step-by-step BMAD Method guides you from idea to launch—autonomously. Join us and let AI build your next big thing!'
    },
    {
      value: 'Proof in Action',
      icon: Bot,
      hover: 'VybeCoding.ai is living proof: AI-powered development isn\'t just possible—it\'s production-ready. If we can do it, so can you!'
    },
    {
      value: 'Community-First Mission',
      icon: FileText,
      hover: '“Rising tides raise all ships.” We unite developers to master AI, celebrate open source, and build real products together.'
    }
  ];
</script>

<svelte:head>
  <title>VybeCoding.ai - AI Development Platform</title>
  <meta
    name="description"
    content="Master AI development with VybeCoding.ai - Learn to build profitable applications with cutting-edge AI tools and proven methodologies."
  />
</svelte:head>

<!-- Homepage inspired by courses page design -->
<div
  class="min-h-screen relative overflow-hidden"
  role="main"
  aria-label="VybeCoding.ai homepage"
>
  <!-- Dynamic background (borrowed from courses page) -->
  <div
    class="absolute inset-0 bg-gradient-to-br from-slate-950 via-slate-900 to-slate-950"
  ></div>
  <div
    class="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(6,182,212,0.15),transparent_50%)]"
  ></div>
  <div
    class="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(236,72,153,0.1),transparent_50%)]"
  ></div>
  <div
    class="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(139,92,246,0.08),transparent_50%)]"
  ></div>

  <!-- Floating Geometric Elements (borrowed from courses page) -->
  <div class="absolute inset-0 pointer-events-none">
    <div
      class="absolute top-32 right-1/4 w-4 h-4 bg-cyan-400 rotate-45 animate-float opacity-60"
    ></div>
    <div
      class="absolute bottom-1/3 left-1/4 w-6 h-6 bg-pink-400 rounded-full animate-float-delayed opacity-40"
    ></div>
    <div
      class="absolute top-2/3 right-1/3 w-3 h-3 bg-purple-400 animate-float-slow opacity-50"
    ></div>
    <div
      class="absolute top-1/4 left-1/3 w-5 h-5 bg-cyan-300 rounded-full animate-pulse opacity-30"
    ></div>
  </div>

  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10 py-16">
    <!-- Hero Section -->
    <div class="text-center mb-20 animate-on-scroll">
      <!-- Badge -->
      <div
        class="inline-flex items-center px-6 py-3 rounded-full bg-gradient-to-r from-cyan-500/20 to-purple-500/20 border border-cyan-500/30 mb-8"
      >
        <Sparkles class="w-5 h-5 text-cyan-400 mr-3" />
        <span class="text-sm font-medium text-cyan-300"
          >AI Development Platform</span
        >
      </div>

      <!-- Main heading -->
      <h1 class="text-6xl md:text-7xl font-bold mb-8 leading-tight">
        <span
          class="bg-gradient-to-r from-white via-cyan-200 to-white bg-clip-text text-transparent"
        >
          Master AI
        </span>
        <br />
        <span
          class="bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400 bg-clip-text text-transparent"
        >
          Development
        </span>
      </h1>

      <!-- Description -->
      <p
        class="text-xl md:text-2xl text-slate-300 mb-12 leading-relaxed max-w-4xl mx-auto"
      >
        Learn to build <span class="text-cyan-400 font-semibold"
          >profitable AI applications</span
        >
        with our proven Vybe Method. Experience
        <span class="text-purple-400 font-semibold"
          >enterprise-grade development</span
        >
        with 100% FOSS technology.
      </p>

      <!-- CTA Buttons -->
      <div
        class="flex flex-col sm:flex-row gap-4 justify-center mb-16"
        role="group"
        aria-label="Main call-to-action buttons"
      >
        <button
          class="group px-8 py-4 bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-400 hover:to-blue-500 text-white font-bold rounded-2xl transition-all duration-300 hover:scale-105 hover:shadow-2xl hover:shadow-cyan-500/25"
          onclick={() => goto('/courses')}
          aria-label="Start learning AI development courses"
        >
          <div class="flex items-center justify-center">
            <Play class="w-5 h-5 mr-2" aria-hidden="true" />
            Start Learning
            <ArrowRight
              class="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-300"
              aria-hidden="true"
            />
          </div>
        </button>
        <button
          class="group px-8 py-4 bg-slate-800/50 backdrop-blur-sm border border-slate-600 hover:border-purple-400 text-white font-bold rounded-2xl transition-all duration-300 hover:scale-105"
          onclick={() => goto('/about')}
          aria-label="Learn more about VybeCoding.ai"
        >
          <div class="flex items-center justify-center">
            <BookOpen class="w-5 h-5 mr-2 text-purple-400" aria-hidden="true" />
            Learn More
            <ArrowRight
              class="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-300"
              aria-hidden="true"
            />
          </div>
        </button>
      </div>
    </div>

    <!-- Stats Section -->
    <section class="mb-20 animate-on-scroll" aria-labelledby="stats-heading">
      <h2 id="stats-heading" class="sr-only">Platform Statistics</h2>
      <div class="grid grid-cols-2 md:grid-cols-4 gap-8" role="list">
        {#each statCards as stat, index}
          <div
            class="vybe-flip-card group text-center relative cursor-pointer"
            tabindex="0"
            style="animation-delay: {index * 0.1}s"
            role="listitem"
          >
            <div class="vybe-flip-card-inner">
              <!-- Front of card -->
              <div class="vybe-flip-card-front relative p-6 rounded-2xl bg-slate-800/40 backdrop-blur-sm border border-slate-700/50 transition-all duration-500 group-hover:scale-105 group-focus:scale-105">
                <div class="w-12 h-12 mx-auto mb-4 rounded-xl bg-gradient-to-br from-cyan-400 to-blue-500 p-0.5 group-hover:scale-110 transition-transform duration-300" aria-hidden="true">
                  <div class="w-full h-full rounded-xl bg-slate-800 flex items-center justify-center">
                    <svelte:component this={stat.icon} class="w-6 h-6 text-cyan-400" aria-hidden="true" />
                  </div>
                </div>
                <div class="text-lg font-bold mb-2 text-white">{stat.value}</div>
              </div>
              <!-- Back of card (hover/tap reveal) -->
              <div class="vybe-flip-card-back absolute inset-0 flex items-center justify-center p-6 rounded-2xl bg-gradient-to-br from-cyan-500/90 to-purple-500/90 text-white text-sm font-medium opacity-0 group-hover:opacity-100 group-focus:opacity-100 transition-opacity duration-500 z-20">
                <span>{stat.hover}</span>
              </div>
            </div>
          </div>
        {/each}
      </div>
    </section>

    <!-- Features Section -->
    <section class="mb-20 animate-on-scroll" aria-labelledby="features-heading">
      <!-- Section Header -->
      <header class="text-center mb-16">
        <div
          class="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-cyan-500/20 to-purple-500/20 border border-cyan-500/30 mb-6"
          role="img"
          aria-label="Features section badge"
        >
          <Brain class="w-4 h-4 text-cyan-400 mr-2" aria-hidden="true" />
          <span class="text-sm font-medium text-cyan-300"
            >Why Choose VybeCoding.ai</span
          >
        </div>

        <h2
          id="features-heading"
          class="text-4xl md:text-5xl font-bold mb-6 leading-tight"
        >
          <span
            class="bg-gradient-to-r from-white via-cyan-200 to-white bg-clip-text text-transparent"
          >
            Build the
          </span>
          <br />
          <span
            class="bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400 bg-clip-text text-transparent"
          >
            Future
          </span>
        </h2>

        <p class="text-xl text-slate-300 max-w-3xl mx-auto leading-relaxed">
          Experience enterprise-grade AI development with our production-ready
          platform. Featuring 100% BMAD compliance and a powerful multi-agent
          system with real-time communication.
        </p>
      </header>

      <!-- Features Grid -->
      <div class="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
        {#each features as feature, index}
          <div
            class="group relative animate-on-scroll"
            style="animation-delay: {index * 0.15}s"
          >
            <!-- Main card -->
            <div
              class="relative h-full p-8 rounded-3xl bg-slate-800/40 backdrop-blur-sm border border-slate-700/50 hover:border-cyan-400/50 transition-all duration-700 group-hover:scale-105 group-hover:-translate-y-2 overflow-hidden"
            >
              <!-- Gradient overlay -->
              <div
                class="absolute inset-0 rounded-3xl bg-gradient-to-br {feature.gradient}/10 opacity-0 group-hover:opacity-100 transition-opacity duration-700"
              ></div>

              <!-- Animated border glow -->
              <div
                class="absolute inset-0 rounded-3xl bg-gradient-to-r {feature.gradient} opacity-0 group-hover:opacity-30 blur-xl transition-opacity duration-700"
              ></div>

              <!-- Content -->
              <div class="relative z-10">
                <!-- Icon -->
                <div
                  class="w-16 h-16 rounded-2xl bg-gradient-to-br {feature.gradient} p-0.5 mb-6 group-hover:scale-110 transition-transform duration-500"
                >
                  <div
                    class="w-full h-full rounded-2xl bg-slate-800 flex items-center justify-center"
                  >
                    <svelte:component
                      this={feature.icon}
                      class="w-8 h-8 text-white"
                    />
                  </div>
                </div>

                <!-- Title -->
                <h3
                  class="text-2xl font-bold mb-4 text-white transition-all duration-500"
                >
                  {feature.title}
                </h3>

                <!-- Description -->
                <p
                  class="text-slate-300 leading-relaxed group-hover:text-slate-200 transition-colors duration-300"
                >
                  {feature.description}
                </p>
              </div>
            </div>
          </div>
        {/each}
      </div>
    </section>

    <!-- CTA Section -->
    <section
      class="text-center animate-on-scroll"
      aria-labelledby="cta-heading"
    >
      <div
        class="relative p-12 rounded-3xl bg-slate-800/40 backdrop-blur-sm border border-slate-700/50 max-w-4xl mx-auto"
      >
        <!-- Gradient overlay -->
        <div
          class="absolute inset-0 rounded-3xl bg-gradient-to-br from-cyan-500/10 to-purple-500/10"
        ></div>

        <!-- Animated border glow -->
        <div
          class="absolute inset-0 rounded-3xl bg-gradient-to-r from-cyan-400 to-purple-500 opacity-20 blur-xl"
        ></div>

        <!-- Content -->
        <div class="relative z-10">
          <!-- Badge -->
          <div
            class="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-cyan-500/20 to-purple-500/20 border border-cyan-500/30 mb-6"
          >
            <Rocket class="w-4 h-4 text-cyan-400 mr-2" />
            <span class="text-sm font-medium text-cyan-300"
              >Start Your Journey</span
            >
          </div>

          <!-- Heading -->
          <h2
            id="cta-heading"
            class="text-4xl md:text-5xl font-bold mb-6 leading-tight"
          >
            <span
              class="bg-gradient-to-r from-white via-cyan-200 to-white bg-clip-text text-transparent"
            >
              Ready to Build
            </span>
            <br />
            <span
              class="bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400 bg-clip-text text-transparent"
            >
              AI Applications?
            </span>
          </h2>

          <!-- Description -->
          <p
            class="text-xl text-slate-300 mb-8 max-w-2xl mx-auto leading-relaxed"
          >
            Experience the future of AI development with our production-ready
            platform. Learn the Vybe Method and build with enterprise-grade
            Multi-Agent Systems.
          </p>

          <!-- CTA Buttons -->
          <div
            class="flex flex-col sm:flex-row gap-4 justify-center"
            role="group"
            aria-label="Call-to-action buttons"
          >
            <button
              class="group px-8 py-4 bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-400 hover:to-blue-500 text-white font-bold rounded-2xl transition-all duration-300 hover:scale-105 hover:shadow-2xl hover:shadow-cyan-500/25"
              onclick={() => goto('/courses')}
              aria-label="Browse AI development courses"
            >
              <div class="flex items-center justify-center">
                <Play class="w-5 h-5 mr-2" aria-hidden="true" />
                Browse Courses
                <ArrowRight
                  class="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-300"
                  aria-hidden="true"
                />
              </div>
            </button>
            <button
              class="group px-8 py-4 bg-slate-800/50 backdrop-blur-sm border border-slate-600 hover:border-purple-400 text-white font-bold rounded-2xl transition-all duration-300 hover:scale-105"
              onclick={() => goto('/auth/signup')}
              aria-label="Sign up for free account"
            >
              <div class="flex items-center justify-center">
                <Sparkles
                  class="w-5 h-5 mr-2 text-purple-400"
                  aria-hidden="true"
                />
                Get Started Free
                <ArrowRight
                  class="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-300"
                  aria-hidden="true"
                />
              </div>
            </button>
          </div>
        </div>
      </div>
    </section>
  </div>
</div>

<style>
  /* Scroll-triggered animations */
  .animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .animate-on-scroll.animate-in {
    opacity: 1;
    transform: translateY(0);
  }

  /* Floating animations for geometric elements */
  @keyframes float {
    0%,
    100% {
      transform: translateY(0px) rotate(0deg);
    }
    33% {
      transform: translateY(-10px) rotate(120deg);
    }
    66% {
      transform: translateY(5px) rotate(240deg);
    }
  }

  @keyframes float-delayed {
    0%,
    100% {
      transform: translateY(0px) scale(1);
    }
    50% {
      transform: translateY(-15px) scale(1.1);
    }
  }

  @keyframes float-slow {
    0%,
    100% {
      transform: translateY(0px) rotate(0deg);
    }
    50% {
      transform: translateY(-8px) rotate(180deg);
    }
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-float-delayed {
    animation: float-delayed 8s ease-in-out infinite;
  }

  .animate-float-slow {
    animation: float-slow 10s ease-in-out infinite;
  }

  /* Performance optimizations */
  .gpu-accelerated {
    transform: translateZ(0);
    will-change: transform, opacity;
  }

  /* Accessibility improvements */
  @media (prefers-reduced-motion: reduce) {
    .animate-on-scroll,
    .animate-float,
    .animate-float-delayed,
    .animate-float-slow {
      animation: none;
      transition: none;
    }
  }

  .vybe-flip-card {
    perspective: 1000px;
    outline: none;
  }
  .vybe-flip-card-inner {
    position: relative;
    width: 100%;
    height: 100%;
  }
  .vybe-flip-card-front,
  .vybe-flip-card-back {
    backface-visibility: hidden;
    transition: opacity 0.5s, transform 0.5s;
    width: 100%;
    height: 100%;
    position: relative;
  }
  .vybe-flip-card-back {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    pointer-events: none;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 1.5rem;
  }
  .vybe-flip-card:hover .vybe-flip-card-back,
  .vybe-flip-card:focus .vybe-flip-card-back {
    opacity: 1;
    pointer-events: auto;
  }
  .vybe-flip-card:hover .vybe-flip-card-front,
  .vybe-flip-card:focus .vybe-flip-card-front {
    opacity: 0.1;
  }
  @media (hover: none) and (pointer: coarse) {
    .vybe-flip-card:active .vybe-flip-card-back {
      opacity: 1;
      pointer-events: auto;
    }
    .vybe-flip-card:active .vybe-flip-card-front {
      opacity: 0.1;
    }
  }
</style>
