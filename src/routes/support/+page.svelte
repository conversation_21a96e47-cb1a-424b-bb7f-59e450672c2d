<script lang="ts">
  import { Container, <PERSON><PERSON>, Card, Button, Badge } from '$lib/components/ui';
  import {
    HelpCircle,
    MessageCircle,
    Book,
    Mail,
    Phone,
    Search,
  } from 'lucide-svelte';

  let searchQuery = $state('');

  const faqCategories = [
    {
      title: 'Getting Started',
      questions: [
        {
          q: 'How do I create an account?',
          a: 'Click "Sign Up" and follow the registration process.',
        },
        {
          q: 'What is the Vybe Method?',
          a: 'Our proven methodology for learning AI development through practical projects.',
        },
        {
          q: 'How do I access my courses?',
          a: 'Log in and visit your dashboard to see all enrolled courses.',
        },
      ],
    },
    {
      title: 'Technical Issues',
      questions: [
        {
          q: 'The platform is loading slowly',
          a: 'Try clearing your browser cache or switching to a different browser.',
        },
        {
          q: "I can't submit my project",
          a: 'Ensure your files meet the size requirements and try again.',
        },
        {
          q: "Video won't play",
          a: 'Check your internet connection and disable browser extensions.',
        },
      ],
    },
    {
      title: 'Billing & Subscriptions',
      questions: [
        {
          q: 'How do I cancel my subscription?',
          a: 'Go to Account Settings > Billing and click "Cancel Subscription".',
        },
        {
          q: 'Can I get a refund?',
          a: 'Yes, we offer a 30-day money-back guarantee for new subscriptions.',
        },
        {
          q: 'How do I update my payment method?',
          a: 'Visit Account Settings > Billing to update your payment information.',
        },
      ],
    },
  ];

  const supportChannels = [
    {
      icon: MessageCircle,
      title: 'Live Chat',
      description: 'Get instant help from our support team',
      availability: 'Available 9 AM - 6 PM EST',
      action: 'Start Chat',
      href: '#',
    },
    {
      icon: Mail,
      title: 'Email Support',
      description: 'Send us a detailed message about your issue',
      availability: 'Response within 24 hours',
      action: 'Send Email',
      href: 'mailto:<EMAIL>',
    },
    {
      icon: Phone,
      title: 'Phone Support',
      description: 'Speak directly with our technical team',
      availability: 'Mon-Fri 9 AM - 5 PM EST',
      action: 'Call Now',
      href: 'tel:******-VYBE-AI',
    },
  ];
</script>

<svelte:head>
  <title>Support - VybeCoding.ai | Get Help and Find Answers</title>
  <meta
    name="description"
    content="Get support for VybeCoding.ai platform. Find answers to common questions or contact our support team."
  />
</svelte:head>

<main role="main">
  <section class="py-20 bg-gradient-to-br from-primary/10 to-secondary/10">
    <Container>
      <div class="text-center max-w-4xl mx-auto">
        <h1 class="text-4xl md:text-5xl font-bold mb-6">
          How Can We <span class="text-primary">Help?</span>
        </h1>
        <p class="text-xl text-muted-foreground mb-8">
          Find answers to common questions or get in touch with our support team
        </p>

        <!-- Search Bar -->
        <div class="max-w-2xl mx-auto relative">
          <Search
            class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-muted-foreground"
          />
          <input
            type="text"
            bind:value={searchQuery}
            placeholder="Search help..."
            class="input pl-12 w-full text-lg"
          />
        </div>
      </div>
    </Container>
  </section>

  <!-- Support Channels -->
  <section class="py-20">
    <Container>
      <div class="text-center mb-16">
        <h2 class="text-3xl font-bold mb-4">Contact Support</h2>
        <p class="text-xl text-muted-foreground">
          Choose the best way to reach our support team
        </p>
      </div>

      <Grid cols="auto" gap="large" class="max-w-5xl mx-auto">
        {#each supportChannels as channel}
          <Card class="p-6 text-center hover:shadow-lg transition-shadow">
            <div
              class="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4"
            >
              <svelte:component
                this={channel.icon}
                class="w-6 h-6 text-primary"
              />
            </div>
            <h3 class="text-xl font-semibold mb-2">{channel.title}</h3>
            <p class="text-muted-foreground mb-3">{channel.description}</p>
            <Badge variant="outline" class="mb-4">{channel.availability}</Badge>
            <div>
              <Button href={channel.href} variant="outline" class="w-full">
                {channel.action}
              </Button>
            </div>
          </Card>
        {/each}
      </Grid>
    </Container>
  </section>

  <!-- FAQ Section -->
  <section class="py-20 bg-muted/50">
    <Container>
      <div class="text-center mb-16">
        <h2 class="text-3xl font-bold mb-4">Frequently Asked Questions</h2>
        <p class="text-xl text-muted-foreground">
          Quick answers to common questions
        </p>
      </div>

      <div class="max-w-4xl mx-auto space-y-8">
        {#each faqCategories as category}
          <Card class="p-6">
            <h3 class="text-xl font-semibold mb-6 flex items-center gap-2">
              <HelpCircle class="w-5 h-5 text-primary" />
              {category.title}
            </h3>

            <div class="space-y-4">
              {#each category.questions as faq}
                <details class="group">
                  <summary
                    class="flex items-center justify-between cursor-pointer p-3 rounded hover:bg-muted/50 transition-colors"
                  >
                    <span class="font-medium">{faq.q}</span>
                    <span
                      class="text-muted-foreground group-open:rotate-180 transition-transform"
                      >▼</span
                    >
                  </summary>
                  <div class="mt-2 p-3 text-muted-foreground">
                    {faq.a}
                  </div>
                </details>
              {/each}
            </div>
          </Card>
        {/each}
      </div>
    </Container>
  </section>

  <!-- Additional Resources -->
  <section class="py-20">
    <Container>
      <div class="text-center max-w-3xl mx-auto">
        <h2 class="text-3xl font-bold mb-6">Additional Resources</h2>
        <p class="text-xl text-muted-foreground mb-8">
          Explore our comprehensive documentation and community resources
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <Button href="/docs" variant="outline">
            <Book class="w-4 h-4 mr-2" />
            Documentation
          </Button>
          <Button href="/community" variant="outline">
            <MessageCircle class="w-4 h-4 mr-2" />
            Community Forum
          </Button>
        </div>
      </div>
    </Container>
  </section>
</main>
