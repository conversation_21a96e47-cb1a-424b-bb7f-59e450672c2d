<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import {
    Check,
    Star,
    Zap,
    Shield,
    Users,
    Crown,
    Rocket,
    ArrowRight,
    Sparkles,
    Target,
    TrendingUp,
    Award,
    Clock,
    Heart,
    Lightbulb,
  } from 'lucide-svelte';
  // Components will be imported as needed

  let user: any = null;
  let loading = $state(false);
  let selectedTier: string | null = $state(null);
  let mounted = false;

  // Enhanced pricing tiers with advanced features
  const pricingTiers = {
    free: {
      name: 'Explorer',
      subtitle: 'Perfect for getting started',
      price: 0,
      originalPrice: null,
      popular: false,
      icon: Rocket,
      gradient: 'from-slate-600 to-slate-800',
      features: [
        'Access to 3 foundational courses',
        'Community forum participation',
        '50 AI-powered code suggestions/month',
        'Basic Vybe Method tutorials',
        'Email support',
        'Mobile app access',
      ],
      limits: {
        courses: '3 courses',
        aiQueries: '50/month',
        storage: '1GB',
        projects: '2 projects',
        support: 'Community',
      },
      cta: 'Start Learning Free',
      benefits: [
        'No credit card required',
        'Instant access',
        'Community support',
      ],
    },
    pro: {
      name: 'Professional',
      subtitle: 'Most popular for serious learners',
      price: 29,
      originalPrice: 49,
      popular: true,
      icon: Crown,
      gradient: 'from-purple-600 to-blue-600',
      features: [
        'Unlimited access to all courses',
        'Advanced AI-powered development tools',
        'Unlimited code generation & suggestions',
        'Personal AI learning assistant',
        'Priority community support',
        'Live coding sessions with experts',
        'Advanced project templates',
        'Progress analytics & insights',
        'Mobile app with offline access',
        'Certificate of completion',
      ],
      limits: {
        courses: 'All courses',
        aiQueries: 'Unlimited',
        storage: '25GB',
        projects: 'Unlimited',
        support: 'Priority email + chat',
      },
      cta: 'Start 7-Day Free Trial',
      benefits: ['7-day free trial', 'Cancel anytime', 'Most popular choice'],
    },
    enterprise: {
      name: 'Enterprise',
      subtitle: 'For teams and organizations',
      price: 99,
      originalPrice: null,
      popular: false,
      icon: Shield,
      gradient: 'from-emerald-600 to-teal-600',
      features: [
        'Everything in Professional',
        'Team management dashboard',
        'Custom learning paths',
        'Advanced analytics & reporting',
        'SSO integration',
        'Dedicated account manager',
        'Custom integrations & API access',
        'White-label options',
        'Priority phone support',
        'Custom training sessions',
        'Advanced security features',
        'Compliance reporting',
      ],
      limits: {
        courses: 'All courses + custom',
        aiQueries: 'Unlimited',
        storage: '500GB',
        projects: 'Unlimited',
        support: 'Dedicated success manager',
      },
      cta: 'Contact Sales',
      benefits: [
        'Custom pricing available',
        'Dedicated support',
        'Enterprise features',
      ],
    },
  };

  onMount(async () => {
    mounted = true;

    // Initialize advanced cursor effects
    initializeCursorEffects();

    // Initialize scroll animations
    initializeScrollAnimations();

    // Initialize particle background
    initializeParticleBackground();

    // user = authService.getCurrentUser();
    user = null; // Demo mode
  });

  async function handleSubscribe(tierName: string) {
    if (!user) {
      goto('/auth/signin?redirect=/pricing');
      return;
    }

    if (tierName === 'free') {
      // Free tier - no payment needed
      goto('/dashboard');
      return;
    }

    loading = true;
    selectedTier = tierName;

    try {
      // Real subscription API call
      const response = await fetch('/api/subscription/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tier: tierName,
          userId: user.$id
        })
      });

      if (!response.ok) {
        throw new Error('Failed to create subscription');
      }

      const result = await response.json();

      if (result.success) {
        // Redirect to dashboard on successful subscription
        goto('/dashboard');
      } else {
        throw new Error(result.error || 'Subscription failed');
      }
    } catch (error) {
      console.error('Subscription error:', error);
      alert('Failed to start subscription. Please try again.');
    } finally {
      loading = false;
      selectedTier = null;
    }
  }

  // Advanced cursor effects (same as other pages)
  function initializeCursorEffects() {
    const cursor = document.createElement('div');
    cursor.className = 'vybe-cursor';
    document.body.appendChild(cursor);

    const cursorGlow = document.createElement('div');
    cursorGlow.className = 'vybe-cursor-glow';
    document.body.appendChild(cursorGlow);

    let mouseX = 0,
      mouseY = 0;
    let cursorX = 0,
      cursorY = 0;

    document.addEventListener('mousemove', e => {
      mouseX = e.clientX;
      mouseY = e.clientY;
    });

    function animateCursor() {
      cursorX += (mouseX - cursorX) * 0.1;
      cursorY += (mouseY - cursorY) * 0.1;

      cursor.style.transform = `translate(${cursorX - 10}px, ${cursorY - 10}px)`;
      cursorGlow.style.transform = `translate(${cursorX - 25}px, ${cursorY - 25}px)`;

      requestAnimationFrame(animateCursor);
    }
    animateCursor();

    // Enhanced hover effects
    document.addEventListener(
      'mouseenter',
      e => {
        const target = e.target as HTMLElement;
        if (target.matches('.vybe-interactive, button, a, .pricing-card')) {
          cursor.classList.add('cursor-hover');
          cursorGlow.classList.add('cursor-hover');
        }
      },
      true
    );

    document.addEventListener(
      'mouseleave',
      e => {
        const target = e.target as HTMLElement;
        if (target.matches('.vybe-interactive, button, a, .pricing-card')) {
          cursor.classList.remove('cursor-hover');
          cursorGlow.classList.remove('cursor-hover');
        }
      },
      true
    );
  }

  // Scroll-triggered animations
  function initializeScrollAnimations() {
    const observer = new IntersectionObserver(
      entries => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('animate-in');
          }
        });
      },
      { threshold: 0.1 }
    );

    // Observe all animatable elements with real browser timing
    requestAnimationFrame(() => {
      document.querySelectorAll('.animate-on-scroll').forEach(el => {
        observer.observe(el);
      });
    });
  }

  // Particle background system
  function initializeParticleBackground() {
    const canvas = document.createElement('canvas');
    canvas.className = 'particle-canvas';
    canvas.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: -1;
      opacity: 0.15;
    `;
    document.body.appendChild(canvas);

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;

    const particles: Array<{
      x: number;
      y: number;
      vx: number;
      vy: number;
      size: number;
      opacity: number;
      color: string;
    }> = [];

    // Create particles
    for (let i = 0; i < 30; i++) {
      particles.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        vx: (Math.random() - 0.5) * 0.3,
        vy: (Math.random() - 0.5) * 0.3,
        size: Math.random() * 2 + 0.5,
        opacity: Math.random() * 0.4 + 0.1,
        color: Math.random() > 0.5 ? '#8b5cf6' : '#06b6d4',
      });
    }

    function animateParticles() {
      if (!ctx) return;
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      particles.forEach(particle => {
        particle.x += particle.vx;
        particle.y += particle.vy;

        if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
        if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;

        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fillStyle = particle.color;
        ctx.globalAlpha = particle.opacity;
        ctx.fill();
      });

      requestAnimationFrame(animateParticles);
    }
    animateParticles();

    // Handle resize
    window.addEventListener('resize', () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    });
  }
</script>

<svelte:head>
  <title>Pricing Plans - VybeCoding.ai | Choose Your AI Learning Journey</title>
  <meta
    name="description"
    content="Transform your coding skills with VybeCoding.ai's revolutionary Vybe Method. Choose from Explorer, Professional, or Enterprise plans with AI-powered learning, unlimited courses, and expert support."
  />
</svelte:head>

<!-- Advanced Pricing Layout -->
<main class="min-h-screen relative overflow-hidden">
  <!-- Dynamic background -->
  <div
    class="absolute inset-0 bg-gradient-to-br from-slate-950 via-slate-900 to-slate-950"
  ></div>
  <div
    class="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(139,92,246,0.1),transparent_50%)]"
  ></div>
  <div
    class="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(6,182,212,0.08),transparent_50%)]"
  ></div>

  <!-- Hero Section -->
  <section class="relative z-10 pt-20 pb-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Advanced Header -->
      <div class="text-center mb-20 animate-on-scroll">
        <!-- Badge -->
        <div
          class="inline-flex items-center gap-2 bg-white/10 backdrop-blur-sm text-white px-6 py-3 rounded-full text-sm font-semibold mb-8 border border-white/20 shadow-lg"
        >
          <Sparkles class="w-4 h-4 text-purple-400" />
          <span>Revolutionary AI-Powered Learning</span>
        </div>

        <!-- INSPIRING AI EDUCATION HEADLINE -->
        <h1 class="text-6xl md:text-8xl font-black leading-tight mb-8">
          <span class="text-green-400 font-black">🚀 MASTER</span>
          <span
            class="block bg-gradient-to-r from-yellow-400 via-green-400 to-emerald-400 bg-clip-text text-transparent"
          >
            AI DEVELOPMENT
          </span>
          <span class="block text-white font-black">🎓 LEARN TODAY</span>
        </h1>

        <!-- INSPIRING AI EDUCATION SUBTITLE -->
        <div
          class="text-2xl md:text-3xl text-slate-200 max-w-5xl mx-auto leading-relaxed mb-12 font-bold space-y-4"
          style="line-height: 1.6;"
        >
          <p class="mb-4">
            Master the future of development! Our <span
              class="text-green-400 font-black bg-green-400/20 px-4 py-2 rounded inline-block mx-2"
              >AI-POWERED EDUCATION SYSTEM</span
            >
            teaches you to build
            <span
              class="text-yellow-400 font-black bg-yellow-400/20 px-4 py-2 rounded inline-block mx-2"
              >REAL AI APPLICATIONS</span
            > using cutting-edge tools.
          </p>

          <p>
            Your journey to <span
              class="text-emerald-400 font-black bg-emerald-400/20 px-4 py-2 rounded inline-block mx-2"
              >AI MASTERY</span
            > starts here!
          </p>
        </div>

        <!-- REAL PLATFORM STATS -->
        <div class="grid grid-cols-3 gap-8 mb-12 max-w-4xl mx-auto">
          <div class="text-center">
            <div class="text-4xl font-black text-green-400 mb-2">100%</div>
            <div class="text-sm text-slate-400 font-bold">
              AI-POWERED LEARNING
            </div>
          </div>
          <div class="text-center">
            <div class="text-4xl font-black text-yellow-400 mb-2">LIVE</div>
            <div class="text-sm text-slate-400 font-bold">PROJECT DEMOS</div>
          </div>
          <div class="text-center">
            <div class="text-4xl font-black text-emerald-400 mb-2">24/7</div>
            <div class="text-sm text-slate-400 font-bold">AI ASSISTANCE</div>
          </div>
        </div>

        <!-- Trust indicators -->
        <div
          class="flex flex-wrap items-center justify-center gap-8 text-slate-400"
        >
          <div class="flex items-center gap-2">
            <Users class="w-5 h-5 text-purple-400" />
            <span>10,000+ Active Learners</span>
          </div>
          <div class="flex items-center gap-2">
            <Star class="w-5 h-5 text-amber-400 fill-current" />
            <span>4.9/5 Average Rating</span>
          </div>
          <div class="flex items-center gap-2">
            <Award class="w-5 h-5 text-emerald-400" />
            <span>Industry Recognized</span>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Advanced Pricing Cards Section -->
  <section class="relative z-10 pb-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 animate-on-scroll">
        {#each Object.entries(pricingTiers) as [tierKey, tier], index}
          <div
            class="pricing-card vybe-interactive group relative"
            style="animation-delay: {index * 0.1}s"
          >
            <!-- Popular badge -->
            {#if tier.popular}
              <div
                class="absolute -top-4 left-1/2 transform -translate-x-1/2 z-20"
              >
                <div
                  class="bg-gradient-to-r from-purple-500 to-blue-600 text-white px-6 py-2 rounded-full text-sm font-bold shadow-lg"
                >
                  <Crown class="w-4 h-4 inline mr-2" />
                  Most Popular
                </div>
              </div>
            {/if}

            <div
              class="relative p-8 rounded-3xl bg-slate-800/40 backdrop-blur-sm border border-slate-700/50 hover:border-{tier.gradient.split(
                '-'
              )[1]}-400/50 transition-all duration-500 group-hover:scale-105 h-full"
            >
              <!-- Gradient overlay -->
              <div
                class="absolute inset-0 rounded-3xl bg-gradient-to-br {tier.gradient}/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
              ></div>

              <!-- Content -->
              <div class="relative z-10 h-full flex flex-col">
                <!-- Header -->
                <div class="text-center mb-8">
                  <!-- Icon -->
                  <div
                    class="w-16 h-16 rounded-2xl bg-gradient-to-br {tier.gradient} p-0.5 mb-6 mx-auto group-hover:scale-110 transition-transform duration-300"
                  >
                    <div
                      class="w-full h-full rounded-2xl bg-slate-800 flex items-center justify-center"
                    >
                      {#if tier.icon === Rocket}
                        <Rocket class="w-8 h-8 text-slate-400" />
                      {:else if tier.icon === Crown}
                        <Crown class="w-8 h-8 text-purple-400" />
                      {:else if tier.icon === Shield}
                        <Shield class="w-8 h-8 text-emerald-400" />
                      {/if}
                    </div>
                  </div>

                  <!-- Plan name and subtitle -->
                  <h3 class="text-2xl font-bold text-white mb-2">
                    {tier.name}
                  </h3>
                  <p class="text-slate-400 text-sm mb-6">{tier.subtitle}</p>

                  <!-- Pricing -->
                  <div class="mb-6">
                    <div class="flex items-center justify-center gap-2 mb-2">
                      {#if tier.originalPrice}
                        <span class="text-2xl text-slate-500 line-through"
                          >${tier.originalPrice}</span
                        >
                      {/if}
                      <span class="text-5xl font-bold text-white"
                        >${tier.price}</span
                      >
                      {#if tier.price > 0}
                        <span class="text-slate-400">/month</span>
                      {/if}
                    </div>
                    {#if tier.originalPrice}
                      <div class="text-sm text-emerald-400 font-semibold">
                        Save ${tier.originalPrice - tier.price}/month
                      </div>
                    {/if}
                  </div>

                  <!-- Benefits -->
                  <div class="flex flex-wrap gap-2 justify-center mb-8">
                    {#each tier.benefits as benefit}
                      <span
                        class="text-xs bg-{tier.gradient.split(
                          '-'
                        )[1]}-500/20 text-{tier.gradient.split(
                          '-'
                        )[1]}-300 px-3 py-1 rounded-full border border-{tier.gradient.split(
                          '-'
                        )[1]}-500/30"
                      >
                        {benefit}
                      </span>
                    {/each}
                  </div>
                </div>

                <!-- Features -->
                <div class="flex-1 mb-8">
                  <h4 class="text-lg font-semibold text-white mb-4">
                    What's included:
                  </h4>
                  <ul class="space-y-3">
                    {#each tier.features as feature}
                      <li class="flex items-start gap-3">
                        <div
                          class="w-5 h-5 rounded-full bg-emerald-500/20 flex items-center justify-center mt-0.5 flex-shrink-0"
                        >
                          <Check class="w-3 h-3 text-emerald-400" />
                        </div>
                        <span class="text-slate-300 text-sm leading-relaxed"
                          >{feature}</span
                        >
                      </li>
                    {/each}
                  </ul>
                </div>

                <!-- Limits -->
                <div class="mb-8">
                  <div
                    class="bg-slate-700/30 rounded-2xl p-4 border border-slate-600/50"
                  >
                    <h4 class="font-semibold text-white mb-3 text-sm">
                      Plan Details:
                    </h4>
                    <div class="grid grid-cols-2 gap-3 text-xs">
                      <div class="flex items-center gap-2">
                        <Target class="w-3 h-3 text-purple-400" />
                        <span class="text-slate-400">{tier.limits.courses}</span
                        >
                      </div>
                      <div class="flex items-center gap-2">
                        <Zap class="w-3 h-3 text-cyan-400" />
                        <span class="text-slate-400"
                          >{tier.limits.aiQueries}</span
                        >
                      </div>
                      <div class="flex items-center gap-2">
                        <Clock class="w-3 h-3 text-emerald-400" />
                        <span class="text-slate-400">{tier.limits.storage}</span
                        >
                      </div>
                      <div class="flex items-center gap-2">
                        <Heart class="w-3 h-3 text-pink-400" />
                        <span class="text-slate-400">{tier.limits.support}</span
                        >
                      </div>
                    </div>
                  </div>
                </div>

                <!-- CTA Button -->
                <button
                  onclick={() => handleSubscribe(tierKey)}
                  disabled={loading && selectedTier === tierKey}
                  class="w-full py-4 px-6 rounded-2xl font-semibold transition-all duration-300 bg-gradient-to-r {tier.gradient} hover:scale-105 text-white shadow-lg hover:shadow-xl {loading &&
                  selectedTier === tierKey
                    ? 'opacity-50 cursor-not-allowed'
                    : ''}"
                >
                  {#if loading && selectedTier === tierKey}
                    <div class="flex items-center justify-center gap-2">
                      <div
                        class="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"
                      ></div>
                      Processing...
                    </div>
                  {:else}
                    <div class="flex items-center justify-center gap-2">
                      {#if tier.icon === Rocket}
                        <Rocket class="w-5 h-5" />
                      {:else if tier.icon === Crown}
                        <Crown class="w-5 h-5" />
                      {:else if tier.icon === Shield}
                        <Shield class="w-5 h-5" />
                      {/if}
                      {tier.cta}
                      <ArrowRight
                        class="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300"
                      />
                    </div>
                  {/if}
                </button>
              </div>

              <!-- Animated border glow -->
              <div
                class="absolute inset-0 rounded-3xl bg-gradient-to-r {tier.gradient} opacity-0 group-hover:opacity-20 blur-xl transition-opacity duration-500"
              ></div>
            </div>
          </div>
        {/each}
      </div>
    </div>
  </section>

  <!-- Advanced FAQ Section -->
  <section class="relative z-10 pb-20">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Section Header -->
      <div class="text-center mb-16 animate-on-scroll">
        <div
          class="inline-flex items-center gap-2 bg-white/10 backdrop-blur-sm text-white px-6 py-3 rounded-full text-sm font-semibold mb-8 border border-white/20 shadow-lg"
        >
          <Lightbulb class="w-4 h-4 text-amber-400" />
          <span>Frequently Asked Questions</span>
        </div>

        <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">
          Everything You Need to <span
            class="bg-gradient-to-r from-purple-400 to-cyan-400 bg-clip-text text-transparent"
            >Know</span
          >
        </h2>

        <p class="text-xl text-slate-300 max-w-2xl mx-auto">
          Get answers to the most common questions about VybeCoding.ai and the
          Vybe Method.
        </p>
      </div>

      <!-- FAQ Items -->
      <div class="space-y-6 animate-on-scroll">
        <div class="vybe-interactive group relative">
          <div
            class="relative p-8 rounded-3xl bg-slate-800/40 backdrop-blur-sm border border-slate-700/50 hover:border-purple-400/50 transition-all duration-500"
          >
            <div
              class="absolute inset-0 rounded-3xl bg-gradient-to-br from-purple-500/10 to-blue-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
            ></div>
            <div class="relative z-10">
              <h3
                class="text-xl font-semibold text-white mb-4 flex items-center gap-3"
              >
                <div
                  class="w-8 h-8 rounded-xl bg-gradient-to-br from-purple-400 to-blue-500 p-0.5"
                >
                  <div
                    class="w-full h-full rounded-xl bg-slate-800 flex items-center justify-center"
                  >
                    <Rocket class="w-4 h-4 text-purple-400" />
                  </div>
                </div>
                What is the Vybe Method?
              </h3>
              <p class="text-slate-300 leading-relaxed">
                The Vybe Method is our revolutionary AI-powered learning
                approach that combines the proven BMAD methodology with
                Multi-Agent Systems (MAS) to create personalized, adaptive
                learning experiences that actually build profitable applications
                while you learn.
              </p>
            </div>
          </div>
        </div>

        <div class="vybe-interactive group relative">
          <div
            class="relative p-8 rounded-3xl bg-slate-800/40 backdrop-blur-sm border border-slate-700/50 hover:border-emerald-400/50 transition-all duration-500"
          >
            <div
              class="absolute inset-0 rounded-3xl bg-gradient-to-br from-emerald-500/10 to-teal-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
            ></div>
            <div class="relative z-10">
              <h3
                class="text-xl font-semibold text-white mb-4 flex items-center gap-3"
              >
                <div
                  class="w-8 h-8 rounded-xl bg-gradient-to-br from-emerald-400 to-teal-500 p-0.5"
                >
                  <div
                    class="w-full h-full rounded-xl bg-slate-800 flex items-center justify-center"
                  >
                    <Shield class="w-4 h-4 text-emerald-400" />
                  </div>
                </div>
                Can I cancel my subscription anytime?
              </h3>
              <p class="text-slate-300 leading-relaxed">
                Yes! You can cancel your subscription at any time with no
                questions asked. You'll continue to have access to your plan
                features until the end of your current billing period, and we'll
                send you a confirmation email.
              </p>
            </div>
          </div>
        </div>

        <div class="vybe-interactive group relative">
          <div
            class="relative p-8 rounded-3xl bg-slate-800/40 backdrop-blur-sm border border-slate-700/50 hover:border-cyan-400/50 transition-all duration-500"
          >
            <div
              class="absolute inset-0 rounded-3xl bg-gradient-to-br from-cyan-500/10 to-blue-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
            ></div>
            <div class="relative z-10">
              <h3
                class="text-xl font-semibold text-white mb-4 flex items-center gap-3"
              >
                <div
                  class="w-8 h-8 rounded-xl bg-gradient-to-br from-cyan-400 to-blue-500 p-0.5"
                >
                  <div
                    class="w-full h-full rounded-xl bg-slate-800 flex items-center justify-center"
                  >
                    <Zap class="w-4 h-4 text-cyan-400" />
                  </div>
                </div>
                What payment methods do you accept?
              </h3>
              <p class="text-slate-300 leading-relaxed">
                We accept all major credit cards (Visa, MasterCard, American
                Express), Apple Pay, Google Pay, and Stripe Link for secure,
                fast payments. All transactions are encrypted and PCI DSS
                compliant.
              </p>
            </div>
          </div>
        </div>

        <div class="vybe-interactive group relative">
          <div
            class="relative p-8 rounded-3xl bg-slate-800/40 backdrop-blur-sm border border-slate-700/50 hover:border-amber-400/50 transition-all duration-500"
          >
            <div
              class="absolute inset-0 rounded-3xl bg-gradient-to-br from-amber-500/10 to-orange-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
            ></div>
            <div class="relative z-10">
              <h3
                class="text-xl font-semibold text-white mb-4 flex items-center gap-3"
              >
                <div
                  class="w-8 h-8 rounded-xl bg-gradient-to-br from-amber-400 to-orange-500 p-0.5"
                >
                  <div
                    class="w-full h-full rounded-xl bg-slate-800 flex items-center justify-center"
                  >
                    <Star class="w-4 h-4 text-amber-400" />
                  </div>
                </div>
                Is there a free trial?
              </h3>
              <p class="text-slate-300 leading-relaxed">
                Absolutely! The Professional plan includes a 7-day free trial
                with full access to all features. The Explorer plan is always
                free with no time limit, perfect for getting started with the
                Vybe Method.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</main>

<style>
  /* Advanced Vybe Cursor Effects */
  :global(.vybe-cursor) {
    position: fixed;
    width: 20px;
    height: 20px;
    background: linear-gradient(45deg, #06b6d4, #ec4899);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9999;
    mix-blend-mode: difference;
    transition: transform 0.1s ease;
  }

  :global(.vybe-cursor-glow) {
    position: fixed;
    width: 50px;
    height: 50px;
    background: radial-gradient(
      circle,
      rgba(6, 182, 212, 0.3),
      rgba(236, 72, 153, 0.2),
      transparent
    );
    border-radius: 50%;
    pointer-events: none;
    z-index: 9998;
    transition: transform 0.2s ease;
  }

  :global(.vybe-cursor.cursor-hover) {
    transform: scale(1.5);
    background: linear-gradient(45deg, #06b6d4, #ec4899, #8b5cf6);
  }

  :global(.vybe-cursor-glow.cursor-hover) {
    transform: scale(1.8);
    background: radial-gradient(
      circle,
      rgba(6, 182, 212, 0.4),
      rgba(236, 72, 153, 0.3),
      rgba(139, 92, 246, 0.2),
      transparent
    );
  }

  /* Advanced Animation System */
  .animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .animate-on-scroll.animate-in {
    opacity: 1;
    transform: translateY(0);
  }

  /* Interactive elements */
  .vybe-interactive {
    cursor: none;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .vybe-interactive:hover {
    transform: translateY(-5px) scale(1.02);
  }

  /* Pricing card specific styles */
  .pricing-card {
    animation: fadeInUp 0.8s ease-out forwards;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Responsive design enhancements */
  @media (max-width: 768px) {
    .vybe-cursor,
    .vybe-cursor-glow {
      display: none;
    }

    .vybe-interactive {
      cursor: pointer;
    }

    .pricing-card {
      margin-bottom: 2rem;
    }
  }

  /* Accessibility improvements */
  @media (prefers-reduced-motion: reduce) {
    .animate-on-scroll,
    .vybe-interactive,
    .pricing-card {
      animation: none;
      transition: none;
    }

    .vybe-cursor,
    .vybe-cursor-glow {
      display: none;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .bg-gradient-to-r,
    .bg-gradient-to-br {
      background: none !important;
      color: currentColor !important;
    }

    .text-transparent {
      color: currentColor !important;
      background: none !important;
    }

    .border-slate-700 {
      border-color: currentColor !important;
    }
  }

  /* Focus styles for accessibility */
  .vybe-interactive:focus,
  .pricing-card:focus {
    outline: 2px solid #06b6d4;
    outline-offset: 2px;
  }

  /* Print styles */
  @media print {
    .vybe-cursor,
    .vybe-cursor-glow,
    .particle-canvas {
      display: none;
    }

    .pricing-card {
      break-inside: avoid;
      page-break-inside: avoid;
    }
  }

  /* Performance optimizations */
  .gpu-accelerated {
    transform: translateZ(0);
    will-change: transform, opacity;
  }

  /* Dark mode specific enhancements */
  @media (prefers-color-scheme: dark) {
    .vybe-cursor {
      mix-blend-mode: screen;
    }
  }

  /* Particle canvas styles */
  :global(.particle-canvas) {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    opacity: 0.15;
  }

  /* Enhanced gradient animations */
  @keyframes gradientShift {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  .animate-pulse {
    animation: gradientShift 3s ease-in-out infinite;
    background-size: 200% 200%;
  }

  /* Button hover enhancements */
  button:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }
</style>
