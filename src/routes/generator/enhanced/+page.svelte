<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import { writable } from 'svelte/store';
  import { RealTimeMonitor } from '$lib/services/monitoring/realTimeMonitor';
  import type { 
    SystemMetrics, 
    AgentActivity, 
    ServiceHealth, 
    ContentMetrics,
    QualityMetrics,
    AgentConversation,
    ContentGenerationSession
  } from '$lib/types/monitoring';

  // Enhanced Generator State Management
  let activeTab: 'generator' | 'observatory' | 'analytics' | 'services' | 'quality' = 'generator';
  let realTimeMonitor: RealTimeMonitor;
  let mounted = false;

  // Real-time data stores
  let systemMetrics: SystemMetrics = {
    cpu: 0, memory: 0, gpu: 0, network: 0, disk: 0, timestamp: new Date(),
    cpuUsage: 0, memoryUsage: 0, diskUsage: 0, networkThroughput: 0, activeConnections: 0, uptime: 0
  };
  let agentActivities: AgentActivity[] = [];
  let serviceHealth: ServiceHealth[] = [];
  let contentMetrics: ContentMetrics = {
    totalGenerated: 0, successRate: 0, averageQuality: 0, activeGenerations: 0, timestamp: new Date(),
    breakdown: { courses: 0, news: 0, documentation: 0, vybeQubes: 0 },
    qualityDistribution: { excellent: 0, good: 0, average: 0, poor: 0 },
    totalViews: 0, totalInteractions: 0, avgSessionDuration: 0, conversionRate: 0, topContent: []
  };
  let qualityMetrics: QualityMetrics = {
    overall: 0, accuracy: 0, engagement: 0, educational: 0, technical: 0,
    trend: 'stable', timestamp: new Date()
  };

  // Enhanced generation state
  let isGenerating = false;
  let generationSessions: ContentGenerationSession[] = [];
  let activeConversations: AgentConversation[] = [];

  // Content generation form
  let contentType: 'course' | 'news' | 'documentation' | 'vybe_qube' = 'course';
  let topic = '';
  let targetAudience = '';
  let requirements = {
    inspirationUrl: '',
    docsPath: '',
    additionalNotes: ''
  };

  // UI state
  let connectionStatus: 'connected' | 'connecting' | 'disconnected' = 'disconnected';
  let notifications: Array<{id: string, message: string, type: 'success' | 'error' | 'info'}> = [];

  onMount(async () => {
    mounted = true;
    
    // Initialize real-time monitoring
    realTimeMonitor = new RealTimeMonitor();
    
    // Subscribe to real-time data
    realTimeMonitor.systemMetrics.subscribe(value => systemMetrics = value);
    realTimeMonitor.agentActivities.subscribe(value => agentActivities = value);
    realTimeMonitor.serviceHealth.subscribe(value => serviceHealth = value);
    realTimeMonitor.contentMetrics.subscribe(value => contentMetrics = value);
    realTimeMonitor.qualityMetrics.subscribe(value => qualityMetrics = value);
    realTimeMonitor.connectionStatus.subscribe(value => connectionStatus = value);

    // Connect to monitoring service
    try {
      await realTimeMonitor.connect();
      showNotification('🔗 Real-time monitoring connected', 'success');
    } catch (error) {
      console.error('Failed to connect to monitoring:', error);
      showNotification('❌ Failed to connect to monitoring service', 'error');
    }

    // Load initial data
    await loadInitialData();
  });

  onDestroy(() => {
    if (realTimeMonitor) {
      realTimeMonitor.disconnect();
    }
  });

  async function loadInitialData() {
    try {
      // Load generation sessions
      const sessionsResponse = await fetch('/api/monitoring/sessions');
      if (sessionsResponse.ok) {
        const sessionsData = await sessionsResponse.json();
        generationSessions = sessionsData.sessions || [];
      }

      // Load active conversations
      const conversationsResponse = await fetch('/api/monitoring/conversations');
      if (conversationsResponse.ok) {
        const conversationsData = await conversationsResponse.json();
        activeConversations = conversationsData.conversations || [];
      }
    } catch (error) {
      console.error('Failed to load initial data:', error);
    }
  }

  function showNotification(message: string, type: 'success' | 'error' | 'info') {
    const id = `notif_${Date.now()}`;
    notifications = [...notifications, { id, message, type }];
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
      notifications = notifications.filter(n => n.id !== id);
    }, 5000);
  }

  async function startEnhancedGeneration() {
    if (!topic.trim()) {
      showNotification('❌ Please provide a topic for content generation', 'error');
      return;
    }

    isGenerating = true;
    
    try {
      const response = await fetch('/api/enhanced-generation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          contentType,
          topic,
          targetAudience,
          requirements,
          timestamp: new Date().toISOString()
        })
      });

      if (!response.ok) {
        throw new Error(`Generation failed: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        showNotification('🚀 Enhanced generation started successfully', 'success');
        
        // Add to generation sessions
        generationSessions = [{
          id: result.sessionId,
          type: contentType,
          status: 'initializing',
          startTime: new Date(),
          progress: 0,
          currentPhase: 'Initializing',
          agents: [],
          quality: { predicted: 0, confidence: 0 },
          metrics: { researchSources: 0, wordsGenerated: 0, revisionsCount: 0, validationScore: 0 }
        }, ...generationSessions];
      } else {
        throw new Error(result.error || 'Generation failed');
      }
    } catch (error) {
      console.error('Enhanced generation error:', error);
      showNotification(`❌ Generation failed: ${error.message}`, 'error');
    } finally {
      isGenerating = false;
    }
  }

  function getStatusColor(status: string): string {
    switch (status) {
      case 'healthy': case 'connected': case 'completed': return 'text-green-400';
      case 'warning': case 'connecting': case 'active': return 'text-yellow-400';
      case 'critical': case 'failed': case 'error': return 'text-red-400';
      case 'offline': case 'disconnected': return 'text-gray-400';
      default: return 'text-blue-400';
    }
  }

  function formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  function formatDuration(ms: number): string {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) return `${hours}h ${minutes % 60}m`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  }
</script>

<svelte:head>
  <title>Enhanced Generator - VybeCoding.ai</title>
  <meta name="description" content="Advanced Multi-Agent System monitoring and content generation platform" />
</svelte:head>

<main class="min-h-screen bg-slate-900 text-white">
  <!-- Enhanced Header with Real-time Status -->
  <div class="bg-slate-800 border-b border-slate-700 px-6 py-4">
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-4">
        <h1 class="text-2xl font-bold bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent">
          Enhanced Generator
        </h1>
        <div class="flex items-center gap-2">
          <div class="w-2 h-2 rounded-full {connectionStatus === 'connected' ? 'bg-green-400' : connectionStatus === 'connecting' ? 'bg-yellow-400' : 'bg-red-400'}"></div>
          <span class="text-sm {getStatusColor(connectionStatus)}">{connectionStatus}</span>
        </div>
      </div>
      
      <!-- Real-time System Status -->
      <div class="flex items-center gap-6 text-sm">
        <div class="flex items-center gap-2">
          <span class="text-gray-400">CPU:</span>
          <span class="{systemMetrics.cpu > 80 ? 'text-red-400' : systemMetrics.cpu > 60 ? 'text-yellow-400' : 'text-green-400'}">
            {systemMetrics.cpu.toFixed(1)}%
          </span>
        </div>
        <div class="flex items-center gap-2">
          <span class="text-gray-400">Memory:</span>
          <span class="{systemMetrics.memory > 80 ? 'text-red-400' : systemMetrics.memory > 60 ? 'text-yellow-400' : 'text-green-400'}">
            {systemMetrics.memory.toFixed(1)}%
          </span>
        </div>
        <div class="flex items-center gap-2">
          <span class="text-gray-400">GPU:</span>
          <span class="{systemMetrics.gpu > 80 ? 'text-red-400' : systemMetrics.gpu > 60 ? 'text-yellow-400' : 'text-green-400'}">
            {systemMetrics.gpu.toFixed(1)}%
          </span>
        </div>
        <div class="flex items-center gap-2">
          <span class="text-gray-400">Active:</span>
          <span class="text-cyan-400">{contentMetrics.activeGenerations}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Enhanced Tab Navigation -->
  <div class="bg-slate-800 border-b border-slate-700">
    <div class="px-6">
      <nav class="flex space-x-8">
        {#each [
          { id: 'generator', label: '🚀 Generator', badge: isGenerating ? 'ACTIVE' : null },
          { id: 'observatory', label: '🔭 Observatory', badge: agentActivities.length > 0 ? agentActivities.length.toString() : null },
          { id: 'analytics', label: '📊 Analytics', badge: qualityMetrics.overall > 0 ? `${qualityMetrics.overall}%` : null },
          { id: 'services', label: '⚙️ Services', badge: serviceHealth.filter(s => s.status === 'healthy').length.toString() },
          { id: 'quality', label: '✅ Quality', badge: qualityMetrics.trend === 'improving' ? '↗️' : qualityMetrics.trend === 'declining' ? '↘️' : '→' }
        ] as tab}
          <button
            class="relative py-4 px-2 text-sm font-medium transition-colors {activeTab === tab.id ? 'text-cyan-400 border-b-2 border-cyan-400' : 'text-gray-400 hover:text-white'}"
            on:click={() => activeTab = tab.id}
          >
            {tab.label}
            {#if tab.badge}
              <span class="absolute -top-1 -right-1 bg-cyan-500 text-xs px-1.5 py-0.5 rounded-full text-white">
                {tab.badge}
              </span>
            {/if}
          </button>
        {/each}
      </nav>
    </div>
  </div>

  <!-- Notifications -->
  {#if notifications.length > 0}
    <div class="fixed top-4 right-4 z-50 space-y-2">
      {#each notifications as notification}
        <div class="bg-slate-800 border border-slate-600 rounded-lg p-4 shadow-lg max-w-md {
          notification.type === 'success' ? 'border-green-500' :
          notification.type === 'error' ? 'border-red-500' :
          'border-blue-500'
        }">
          <p class="text-sm">{notification.message}</p>
        </div>
      {/each}
    </div>
  {/if}

  <!-- Main Content Area -->
  <div class="p-6">
    {#if activeTab === 'generator'}
      <!-- Enhanced Generator Tab -->
      <div class="max-w-4xl mx-auto space-y-8">
        <div class="text-center">
          <h2 class="text-3xl font-bold mb-4">Enhanced Content Generation</h2>
          <p class="text-gray-400 text-lg">AI-powered content creation with real-time monitoring and quality assurance</p>
        </div>

        <!-- Generation Form -->
        <div class="bg-slate-800 rounded-xl p-6 border border-slate-700">
          <h3 class="text-xl font-semibold mb-6">Content Configuration</h3>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Content Type Selection -->
            <div>
              <label class="block text-sm font-medium text-gray-300 mb-2">Content Type</label>
              <select bind:value={contentType} class="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-3 text-white focus:ring-2 focus:ring-cyan-500 focus:border-transparent">
                <option value="course">📚 Course</option>
                <option value="news">📰 News Article</option>
                <option value="documentation">📖 Documentation</option>
                <option value="vybe_qube">🎯 Vybe Qube</option>
              </select>
            </div>

            <!-- Topic Input -->
            <div>
              <label class="block text-sm font-medium text-gray-300 mb-2">Topic</label>
              <input
                type="text"
                bind:value={topic}
                placeholder="Enter content topic..."
                class="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
              />
            </div>

            <!-- Target Audience -->
            <div>
              <label class="block text-sm font-medium text-gray-300 mb-2">Target Audience</label>
              <input
                type="text"
                bind:value={targetAudience}
                placeholder="Describe your target audience..."
                class="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
              />
            </div>

            <!-- Inspiration URL -->
            <div>
              <label class="block text-sm font-medium text-gray-300 mb-2">Inspiration URL (Optional)</label>
              <input
                type="url"
                bind:value={requirements.inspirationUrl}
                placeholder="https://example.com/inspiration"
                class="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
              />
            </div>
          </div>

          <!-- Additional Requirements -->
          <div class="mt-6">
            <label class="block text-sm font-medium text-gray-300 mb-2">Additional Requirements</label>
            <textarea
              bind:value={requirements.additionalNotes}
              placeholder="Any specific requirements, style preferences, or constraints..."
              rows="3"
              class="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
            ></textarea>
          </div>

          <!-- Generate Button -->
          <div class="mt-8 text-center">
            <button
              on:click={startEnhancedGeneration}
              disabled={isGenerating || !topic.trim()}
              class="px-8 py-4 bg-gradient-to-r from-cyan-500 to-purple-600 text-white font-semibold rounded-lg hover:from-cyan-600 hover:to-purple-700 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105"
            >
              {#if isGenerating}
                <div class="flex items-center gap-3">
                  <div class="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  Generating Content...
                </div>
              {:else}
                🚀 Start Enhanced Generation
              {/if}
            </button>
          </div>
        </div>

        <!-- Active Generation Sessions -->
        {#if generationSessions.length > 0}
          <div class="bg-slate-800 rounded-xl p-6 border border-slate-700">
            <h3 class="text-xl font-semibold mb-6">Active Generation Sessions</h3>

            <div class="space-y-4">
              {#each generationSessions.slice(0, 5) as session}
                <div class="bg-slate-700 rounded-lg p-4 border border-slate-600">
                  <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center gap-3">
                      <div class="text-2xl">
                        {session.type === 'course' ? '📚' : session.type === 'news' ? '📰' : session.type === 'documentation' ? '📖' : '🎯'}
                      </div>
                      <div>
                        <h4 class="font-semibold">{session.type.charAt(0).toUpperCase() + session.type.slice(1)} Generation</h4>
                        <p class="text-sm text-gray-400">Started {formatDuration(Date.now() - session.startTime.getTime())} ago</p>
                      </div>
                    </div>
                    <div class="text-right">
                      <div class="text-sm font-medium {getStatusColor(session.status)}">{session.status.toUpperCase()}</div>
                      <div class="text-sm text-gray-400">{session.progress}% complete</div>
                    </div>
                  </div>

                  <!-- Progress Bar -->
                  <div class="w-full bg-slate-600 rounded-full h-2 mb-3">
                    <div
                      class="bg-gradient-to-r from-cyan-500 to-purple-500 h-2 rounded-full transition-all duration-500"
                      style="width: {session.progress}%"
                    ></div>
                  </div>

                  <div class="flex items-center justify-between text-sm">
                    <span class="text-gray-400">Phase: {session.currentPhase}</span>
                    <span class="text-cyan-400">Quality: {session.quality.predicted}%</span>
                  </div>
                </div>
              {/each}
            </div>
          </div>
        {/if}
      </div>

    {:else if activeTab === 'observatory'}
      <!-- Real-time Observatory Tab -->
      <div class="max-w-7xl mx-auto space-y-6">
        <div class="text-center">
          <h2 class="text-3xl font-bold mb-4">🔭 MAS Observatory</h2>
          <p class="text-gray-400 text-lg">Real-time monitoring of multi-agent system activities</p>
        </div>

        <!-- Agent Activities Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- Live Agent Activities -->
          <div class="bg-slate-800 rounded-xl p-6 border border-slate-700">
            <h3 class="text-xl font-semibold mb-4 flex items-center gap-2">
              <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
              Live Agent Activities
            </h3>

            <div class="space-y-3 max-h-96 overflow-y-auto">
              {#each agentActivities.slice(0, 10) as activity}
                <div class="bg-slate-700 rounded-lg p-3 border border-slate-600">
                  <div class="flex items-center justify-between mb-2">
                    <div class="flex items-center gap-2">
                      <span class="font-medium text-cyan-400">{activity.agentName}</span>
                      <span class="text-xs px-2 py-1 rounded-full {
                        activity.type === 'conversation' ? 'bg-blue-500/20 text-blue-300' :
                        activity.type === 'task' ? 'bg-green-500/20 text-green-300' :
                        activity.type === 'tool_use' ? 'bg-purple-500/20 text-purple-300' :
                        activity.type === 'error' ? 'bg-red-500/20 text-red-300' :
                        'bg-gray-500/20 text-gray-300'
                      }">{activity.type}</span>
                    </div>
                    <span class="text-xs text-gray-400">{formatDuration(Date.now() - activity.timestamp.getTime())} ago</span>
                  </div>
                  <p class="text-sm text-gray-300">{activity.activity}</p>
                  {#if activity.details.tool}
                    <p class="text-xs text-purple-400 mt-1">Tool: {activity.details.tool}</p>
                  {/if}
                </div>
              {:else}
                <div class="text-center text-gray-400 py-8">
                  <div class="text-4xl mb-2">🤖</div>
                  <p>No agent activities yet</p>
                </div>
              {/each}
            </div>
          </div>

          <!-- Active Conversations -->
          <div class="bg-slate-800 rounded-xl p-6 border border-slate-700">
            <h3 class="text-xl font-semibold mb-4">🗣️ Active Conversations</h3>

            <div class="space-y-3 max-h-96 overflow-y-auto">
              {#each activeConversations as conversation}
                <div class="bg-slate-700 rounded-lg p-3 border border-slate-600">
                  <div class="flex items-center justify-between mb-2">
                    <h4 class="font-medium text-white">{conversation.topic}</h4>
                    <div class="flex items-center gap-2">
                      <span class="text-xs text-gray-400">{conversation.participants.length} agents</span>
                      <div class="w-2 h-2 rounded-full {getStatusColor(conversation.status)}"></div>
                    </div>
                  </div>
                  <div class="flex items-center gap-2 mb-2">
                    <span class="text-sm text-gray-400">Consensus:</span>
                    <div class="flex-1 bg-slate-600 rounded-full h-2">
                      <div
                        class="bg-gradient-to-r from-red-500 via-yellow-500 to-green-500 h-2 rounded-full transition-all duration-500"
                        style="width: {conversation.consensus}%"
                      ></div>
                    </div>
                    <span class="text-sm text-cyan-400">{conversation.consensus}%</span>
                  </div>
                  <div class="flex flex-wrap gap-1">
                    {#each conversation.participants as participant}
                      <span class="text-xs px-2 py-1 bg-cyan-500/20 text-cyan-300 rounded">{participant}</span>
                    {/each}
                  </div>
                </div>
              {:else}
                <div class="text-center text-gray-400 py-8">
                  <div class="text-4xl mb-2">💬</div>
                  <p>No active conversations</p>
                </div>
              {/each}
            </div>
          </div>
        </div>

        <!-- System Performance Overview -->
        <div class="bg-slate-800 rounded-xl p-6 border border-slate-700">
          <h3 class="text-xl font-semibold mb-6">⚡ System Performance</h3>

          <div class="grid grid-cols-2 md:grid-cols-5 gap-6">
            {#each [
              { label: 'CPU', value: systemMetrics.cpu, unit: '%', color: 'cyan' },
              { label: 'Memory', value: systemMetrics.memory, unit: '%', color: 'purple' },
              { label: 'GPU', value: systemMetrics.gpu, unit: '%', color: 'green' },
              { label: 'Network', value: systemMetrics.network, unit: 'MB/s', color: 'blue' },
              { label: 'Disk', value: systemMetrics.disk, unit: '%', color: 'yellow' }
            ] as metric}
              <div class="text-center">
                <div class="text-2xl font-bold text-{metric.color}-400 mb-1">
                  {metric.value.toFixed(1)}{metric.unit}
                </div>
                <div class="text-sm text-gray-400">{metric.label}</div>
                <div class="w-full bg-slate-600 rounded-full h-2 mt-2">
                  <div
                    class="bg-{metric.color}-500 h-2 rounded-full transition-all duration-500"
                    style="width: {Math.min(metric.value, 100)}%"
                  ></div>
                </div>
              </div>
            {/each}
          </div>
        </div>
      </div>

    {:else if activeTab === 'analytics'}
      <!-- Analytics Dashboard Tab -->
      <div class="max-w-7xl mx-auto space-y-6">
        <div class="text-center">
          <h2 class="text-3xl font-bold mb-4">📊 Analytics Dashboard</h2>
          <p class="text-gray-400 text-lg">Comprehensive insights and performance metrics</p>
        </div>

        <!-- Key Metrics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {#each [
            {
              title: 'Total Generated',
              value: contentMetrics.totalGenerated,
              change: '+12%',
              icon: '📝',
              color: 'cyan'
            },
            {
              title: 'Success Rate',
              value: `${contentMetrics.successRate.toFixed(1)}%`,
              change: '+5%',
              icon: '✅',
              color: 'green'
            },
            {
              title: 'Avg Quality',
              value: `${contentMetrics.averageQuality.toFixed(1)}%`,
              change: '+8%',
              icon: '⭐',
              color: 'purple'
            },
            {
              title: 'Active Sessions',
              value: contentMetrics.activeGenerations,
              change: '→',
              icon: '🔄',
              color: 'blue'
            }
          ] as metric}
            <div class="bg-slate-800 rounded-xl p-6 border border-slate-700">
              <div class="flex items-center justify-between mb-4">
                <div class="text-2xl">{metric.icon}</div>
                <span class="text-sm text-{metric.color}-400">{metric.change}</span>
              </div>
              <div class="text-2xl font-bold text-white mb-1">{metric.value}</div>
              <div class="text-sm text-gray-400">{metric.title}</div>
            </div>
          {/each}
        </div>

        <!-- Quality Metrics Breakdown -->
        <div class="bg-slate-800 rounded-xl p-6 border border-slate-700">
          <h3 class="text-xl font-semibold mb-6">🎯 Quality Metrics</h3>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
            {#each [
              { label: 'Overall', value: qualityMetrics.overall, color: 'cyan' },
              { label: 'Accuracy', value: qualityMetrics.accuracy, color: 'green' },
              { label: 'Engagement', value: qualityMetrics.engagement, color: 'purple' },
              { label: 'Educational', value: qualityMetrics.educational, color: 'blue' },
              { label: 'Technical', value: qualityMetrics.technical, color: 'yellow' }
            ] as quality}
              <div class="text-center">
                <div class="relative w-20 h-20 mx-auto mb-3">
                  <svg class="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                    <path
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      fill="none"
                      stroke="rgb(51 65 85)"
                      stroke-width="2"
                    />
                    <path
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      fill="none"
                      stroke="rgb(34 197 94)"
                      stroke-width="2"
                      stroke-dasharray="{quality.value}, 100"
                    />
                  </svg>
                  <div class="absolute inset-0 flex items-center justify-center">
                    <span class="text-sm font-bold text-white">{quality.value}%</span>
                  </div>
                </div>
                <div class="text-sm text-gray-400">{quality.label}</div>
              </div>
            {/each}
          </div>
        </div>
      </div>

    {:else if activeTab === 'services'}
      <!-- Advanced Service Management Tab -->
      <div class="max-w-7xl mx-auto space-y-6">
        <div class="text-center">
          <h2 class="text-3xl font-bold mb-4">⚙️ Service Management</h2>
          <p class="text-gray-400 text-lg">Complete control over Multi-Agent System services and protocols</p>
        </div>

        <!-- Service Health Overview -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {#each [
            {
              id: 'mcp',
              name: 'Model Context Protocol',
              port: 3002,
              status: 'healthy',
              description: 'Agent-to-model communication protocol',
              metrics: { requests: 1247, errors: 3, uptime: 99.8 }
            },
            {
              id: 'a2a',
              name: 'Agent-to-Agent Protocol',
              port: 3003,
              status: 'healthy',
              description: 'Inter-agent communication system',
              metrics: { requests: 892, errors: 1, uptime: 99.9 }
            },
            {
              id: 'retrieval',
              name: 'Agentic Retrieval',
              port: 3004,
              status: 'warning',
              description: 'Advanced RAG and knowledge retrieval',
              metrics: { requests: 456, errors: 12, uptime: 97.2 }
            },
            {
              id: 'guardrails',
              name: 'Guardrails AI',
              port: 3005,
              status: 'healthy',
              description: 'Content safety and validation',
              metrics: { requests: 2341, errors: 5, uptime: 99.7 }
            },
            {
              id: 'ollama',
              name: 'Ollama LLM',
              port: 11434,
              status: 'healthy',
              description: 'Local language model service',
              metrics: { requests: 3456, errors: 8, uptime: 99.5 }
            },
            {
              id: 'websearch',
              name: 'Web Search',
              port: 3006,
              status: 'healthy',
              description: 'Real-time web research service',
              metrics: { requests: 234, errors: 2, uptime: 98.9 }
            }
          ] as service}
            <div class="bg-slate-800 rounded-xl p-6 border border-slate-700">
              <div class="flex items-center justify-between mb-4">
                <div class="flex items-center gap-3">
                  <div class="w-3 h-3 rounded-full {
                    service.status === 'healthy' ? 'bg-green-400' :
                    service.status === 'warning' ? 'bg-yellow-400' :
                    service.status === 'critical' ? 'bg-red-400' :
                    'bg-gray-400'
                  }"></div>
                  <h3 class="font-semibold text-white">{service.name}</h3>
                </div>
                <span class="text-sm text-gray-400">:{service.port}</span>
              </div>

              <p class="text-sm text-gray-400 mb-4">{service.description}</p>

              <div class="space-y-2 mb-4">
                <div class="flex justify-between text-sm">
                  <span class="text-gray-400">Requests:</span>
                  <span class="text-cyan-400">{service.metrics.requests.toLocaleString()}</span>
                </div>
                <div class="flex justify-between text-sm">
                  <span class="text-gray-400">Errors:</span>
                  <span class="text-red-400">{service.metrics.errors}</span>
                </div>
                <div class="flex justify-between text-sm">
                  <span class="text-gray-400">Uptime:</span>
                  <span class="text-green-400">{service.metrics.uptime}%</span>
                </div>
              </div>

              <div class="flex gap-2">
                <button class="flex-1 px-3 py-2 bg-slate-700 hover:bg-slate-600 text-white text-sm rounded-lg transition-colors">
                  Configure
                </button>
                <button class="flex-1 px-3 py-2 bg-cyan-600 hover:bg-cyan-700 text-white text-sm rounded-lg transition-colors">
                  Restart
                </button>
              </div>
            </div>
          {/each}
        </div>

        <!-- Service Configuration Panel -->
        <div class="bg-slate-800 rounded-xl p-6 border border-slate-700">
          <h3 class="text-xl font-semibold mb-6">🔧 Service Configuration</h3>

          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- MCP Configuration -->
            <div class="bg-slate-700 rounded-lg p-4">
              <h4 class="font-semibold text-cyan-400 mb-3">Model Context Protocol</h4>
              <div class="space-y-3">
                <div>
                  <label class="block text-sm text-gray-300 mb-1">Max Context Length</label>
                  <input type="number" value="8192" class="w-full bg-slate-600 border border-slate-500 rounded px-3 py-2 text-white text-sm">
                </div>
                <div>
                  <label class="block text-sm text-gray-300 mb-1">Temperature</label>
                  <input type="range" min="0" max="1" step="0.1" value="0.7" class="w-full">
                </div>
                <div class="flex items-center gap-2">
                  <input type="checkbox" checked class="rounded">
                  <label class="text-sm text-gray-300">Enable streaming responses</label>
                </div>
              </div>
            </div>

            <!-- Guardrails Configuration -->
            <div class="bg-slate-700 rounded-lg p-4">
              <h4 class="font-semibold text-green-400 mb-3">Guardrails AI</h4>
              <div class="space-y-3">
                <div>
                  <label class="block text-sm text-gray-300 mb-1">Safety Level</label>
                  <select class="w-full bg-slate-600 border border-slate-500 rounded px-3 py-2 text-white text-sm">
                    <option>Strict</option>
                    <option selected>Moderate</option>
                    <option>Permissive</option>
                  </select>
                </div>
                <div class="flex items-center gap-2">
                  <input type="checkbox" checked class="rounded">
                  <label class="text-sm text-gray-300">Content filtering</label>
                </div>
                <div class="flex items-center gap-2">
                  <input type="checkbox" checked class="rounded">
                  <label class="text-sm text-gray-300">Fact checking</label>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Real-time Service Logs -->
        <div class="bg-slate-800 rounded-xl p-6 border border-slate-700">
          <h3 class="text-xl font-semibold mb-6">📋 Real-time Service Logs</h3>

          <div class="bg-black rounded-lg p-4 font-mono text-sm max-h-64 overflow-y-auto">
            <div class="space-y-1">
              <div class="text-green-400">[2025-01-15 12:34:56] MCP: Request processed successfully (response_time: 45ms)</div>
              <div class="text-cyan-400">[2025-01-15 12:34:55] A2A: Agent collaboration initiated (session: gen_1737024896)</div>
              <div class="text-yellow-400">[2025-01-15 12:34:54] Retrieval: High latency detected (response_time: 1.2s)</div>
              <div class="text-green-400">[2025-01-15 12:34:53] Guardrails: Content validated (safety_score: 0.95)</div>
              <div class="text-blue-400">[2025-01-15 12:34:52] Ollama: Model loaded (qwen3:30b-a3b)</div>
              <div class="text-green-400">[2025-01-15 12:34:51] WebSearch: Query executed (results: 15, quality: 0.87)</div>
            </div>
          </div>
        </div>
      </div>

    {:else if activeTab === 'quality'}
      <!-- Quality Optimization Tab -->
      <div class="max-w-7xl mx-auto space-y-6">
        <div class="text-center">
          <h2 class="text-3xl font-bold mb-4">✅ Quality Optimization</h2>
          <p class="text-gray-400 text-lg">AI-powered content quality analysis and optimization recommendations</p>
        </div>

        <!-- Quality Overview Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {#each [
            {
              title: 'Overall Quality',
              value: qualityMetrics.overall,
              target: 85,
              trend: qualityMetrics.trend,
              color: 'cyan'
            },
            {
              title: 'Content Accuracy',
              value: qualityMetrics.accuracy,
              target: 90,
              trend: 'improving',
              color: 'green'
            },
            {
              title: 'User Engagement',
              value: qualityMetrics.engagement,
              target: 75,
              trend: 'stable',
              color: 'purple'
            },
            {
              title: 'Educational Value',
              value: qualityMetrics.educational,
              target: 80,
              trend: 'improving',
              color: 'blue'
            }
          ] as metric}
            <div class="bg-slate-800 rounded-xl p-6 border border-slate-700">
              <div class="flex items-center justify-between mb-4">
                <h3 class="font-semibold text-white">{metric.title}</h3>
                <span class="text-xs px-2 py-1 rounded-full {
                  metric.trend === 'improving' ? 'bg-green-500/20 text-green-400' :
                  metric.trend === 'declining' ? 'bg-red-500/20 text-red-400' :
                  'bg-gray-500/20 text-gray-400'
                }">
                  {metric.trend === 'improving' ? '↗️' : metric.trend === 'declining' ? '↘️' : '→'} {metric.trend}
                </span>
              </div>

              <div class="text-3xl font-bold text-{metric.color}-400 mb-2">{metric.value}%</div>

              <div class="flex items-center gap-2 mb-3">
                <span class="text-sm text-gray-400">Target:</span>
                <span class="text-sm text-{metric.color}-400">{metric.target}%</span>
              </div>

              <div class="w-full bg-slate-600 rounded-full h-2">
                <div
                  class="bg-{metric.color}-500 h-2 rounded-full transition-all duration-500"
                  style="width: {(metric.value / metric.target) * 100}%"
                ></div>
              </div>
            </div>
          {/each}
        </div>

        <!-- Quality Insights and Recommendations -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- AI Insights -->
          <div class="bg-slate-800 rounded-xl p-6 border border-slate-700">
            <h3 class="text-xl font-semibold mb-6 flex items-center gap-2">
              🧠 AI Quality Insights
            </h3>

            <div class="space-y-4">
              {#each [
                {
                  type: 'improvement',
                  title: 'Content Structure Enhancement',
                  description: 'Adding more subheadings and bullet points could improve readability by 15%',
                  confidence: 87,
                  impact: 'medium'
                },
                {
                  type: 'warning',
                  title: 'Technical Accuracy Check',
                  description: 'Recent content shows 3% decrease in technical accuracy. Consider additional fact-checking.',
                  confidence: 92,
                  impact: 'high'
                },
                {
                  type: 'success',
                  title: 'Engagement Optimization',
                  description: 'Interactive elements increased user engagement by 23% this week',
                  confidence: 95,
                  impact: 'high'
                }
              ] as insight}
                <div class="bg-slate-700 rounded-lg p-4 border border-slate-600">
                  <div class="flex items-start gap-3">
                    <div class="text-2xl">
                      {insight.type === 'improvement' ? '💡' : insight.type === 'warning' ? '⚠️' : '✅'}
                    </div>
                    <div class="flex-1">
                      <h4 class="font-semibold text-white mb-1">{insight.title}</h4>
                      <p class="text-sm text-gray-300 mb-2">{insight.description}</p>
                      <div class="flex items-center gap-4 text-xs">
                        <span class="text-gray-400">Confidence: {insight.confidence}%</span>
                        <span class="px-2 py-1 rounded-full {
                          insight.impact === 'high' ? 'bg-red-500/20 text-red-400' :
                          insight.impact === 'medium' ? 'bg-yellow-500/20 text-yellow-400' :
                          'bg-green-500/20 text-green-400'
                        }">
                          {insight.impact} impact
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              {/each}
            </div>
          </div>

          <!-- Quality Optimization Actions -->
          <div class="bg-slate-800 rounded-xl p-6 border border-slate-700">
            <h3 class="text-xl font-semibold mb-6">🎯 Optimization Actions</h3>

            <div class="space-y-4">
              {#each [
                {
                  action: 'Run Content Audit',
                  description: 'Analyze all content for quality improvements',
                  status: 'ready',
                  estimated: '15 min'
                },
                {
                  action: 'Update Quality Guidelines',
                  description: 'Refresh content creation guidelines based on latest insights',
                  status: 'in_progress',
                  estimated: '5 min'
                },
                {
                  action: 'Optimize Agent Prompts',
                  description: 'Fine-tune agent prompts for better quality output',
                  status: 'ready',
                  estimated: '10 min'
                },
                {
                  action: 'Generate Quality Report',
                  description: 'Create comprehensive quality analysis report',
                  status: 'ready',
                  estimated: '8 min'
                }
              ] as action}
                <div class="bg-slate-700 rounded-lg p-4 border border-slate-600">
                  <div class="flex items-center justify-between mb-2">
                    <h4 class="font-semibold text-white">{action.action}</h4>
                    <div class="flex items-center gap-2">
                      <span class="text-xs text-gray-400">{action.estimated}</span>
                      <button class="px-3 py-1 bg-cyan-600 hover:bg-cyan-700 text-white text-xs rounded transition-colors {
                        action.status === 'in_progress' ? 'opacity-50 cursor-not-allowed' : ''
                      }" disabled={action.status === 'in_progress'}>
                        {action.status === 'in_progress' ? 'Running...' : 'Start'}
                      </button>
                    </div>
                  </div>
                  <p class="text-sm text-gray-300">{action.description}</p>
                </div>
              {/each}
            </div>
          </div>
        </div>

        <!-- Quality Trends Chart -->
        <div class="bg-slate-800 rounded-xl p-6 border border-slate-700">
          <h3 class="text-xl font-semibold mb-6">📈 Quality Trends (Last 30 Days)</h3>

          <div class="h-64 bg-slate-700 rounded-lg flex items-center justify-center">
            <div class="text-center text-gray-400">
              <div class="text-4xl mb-2">📊</div>
              <p>Quality trends chart would be rendered here</p>
              <p class="text-sm">Integration with charting library needed</p>
            </div>
          </div>
        </div>
      </div>
    {/if}
  </div>
</main>
