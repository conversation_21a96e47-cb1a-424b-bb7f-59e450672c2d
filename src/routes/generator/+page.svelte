<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import Container from '$lib/components/Container.svelte';
  import { ObservatoryRealTimeService, type AgentMessage } from '$lib/services/observatory-realtime';
  import { ProtocolServiceManager, type ServiceStatus } from '$lib/services/protocol-service-manager';

  // Content generation types
  type ContentType = 'course' | 'news_article' | 'documentation' | 'vybe_qube';

  // Form state
  let contentType: ContentType = 'course';
  let topic = '';
  let targetAudience = '';
  // Removed complexityLevel - agents will assess complexity automatically
  let requirements = {
    inspirationUrl: '',
    docsPath: '',
    additionalNotes: ''
  };

  // Form validation state (MAS-001 implementation)
  let formValidation = {
    urlValid: true,
    urlError: '',
    topicValid: true,
    topicError: '',
    audienceValid: true,
    audienceError: '',
    notesValid: true,
    notesError: ''
  };

  // Character limits for validation
  const CHAR_LIMITS = {
    topic: { min: 0, max: 200 },
    audience: { min: 0, max: 150 },
    notes: { min: 0, max: 1000 }
  };

  // MAS-002: Auto Output Type Detection
  let detectionResult = {
    suggestedType: 'course' as ContentType,
    confidence: 0,
    reasoning: '',
    showOverride: false
  };

  // MAS-003: Real-Time Progress Tracking Enhancement
  let progressTracking = {
    startTime: null as Date | null,
    estimatedDuration: 0, // in minutes
    timeRemaining: 0, // in minutes
    lastUpdate: null as Date | null,
    updateInterval: null as number | null,
    retryCount: 0,
    maxRetries: 3,
    pollingActive: false,
    errorState: null as string | null
  };

  // Enhanced phase definitions for MAS-003
  const GENERATION_PHASES = {
    research: { name: 'Research & Analysis', icon: '🔍', estimatedPercent: 20 },
    planning: { name: 'Planning & Architecture', icon: '📋', estimatedPercent: 15 },
    architecture: { name: 'System Architecture', icon: '🏗️', estimatedPercent: 15 },
    design: { name: 'Design & UI/UX', icon: '🎨', estimatedPercent: 20 },
    implementation: { name: 'Code Generation', icon: '⚡', estimatedPercent: 20 },
    quality: { name: 'Quality Assurance', icon: '🛡️', estimatedPercent: 5 },
    deployment: { name: 'Deployment & Testing', icon: '🚀', estimatedPercent: 5 }
  };

  // MAS-004: Error Handling and User Guidance
  let errorHandling = {
    currentError: null as any,
    errorHistory: [] as any[],
    retryAttempts: 0,
    maxRetries: 3,
    showHelp: false,
    helpContext: '',
    recoveryOptions: [] as any[]
  };

  // Error types and their handling strategies
  const ERROR_TYPES = {
    NETWORK_ERROR: {
      title: 'Connection Issue',
      icon: '🌐',
      description: 'Unable to connect to the generation service',
      guidance: 'Check your internet connection and try again',
      recoveryOptions: ['retry', 'offline_mode', 'contact_support']
    },
    VALIDATION_ERROR: {
      title: 'Input Validation Failed',
      icon: '⚠️',
      description: 'Please check your input and fix any validation errors',
      guidance: 'Review the form fields highlighted in red',
      recoveryOptions: ['fix_input', 'reset_form', 'use_sample']
    },
    SERVICE_UNAVAILABLE: {
      title: 'Service Temporarily Unavailable',
      icon: '🔧',
      description: 'The generation service is currently unavailable',
      guidance: 'Please try again in a few minutes',
      recoveryOptions: ['retry_later', 'check_status', 'contact_support']
    },
    GENERATION_FAILED: {
      title: 'Generation Failed',
      icon: '❌',
      description: 'Content generation encountered an error',
      guidance: 'Try adjusting your requirements or using different inputs',
      recoveryOptions: ['retry', 'modify_input', 'try_different_type']
    },
    TIMEOUT_ERROR: {
      title: 'Request Timeout',
      icon: '⏱️',
      description: 'The generation process took too long to complete',
      guidance: 'Try simplifying your requirements or try again later',
      recoveryOptions: ['retry', 'simplify_input', 'contact_support']
    }
  };

  // Content type detection keywords (MAS-002 implementation)
  const DETECTION_KEYWORDS = {
    course: {
      primary: ['course', 'tutorial', 'lesson', 'learn', 'teach', 'education', 'training', 'guide', 'beginner', 'advanced', 'step-by-step'],
      secondary: ['module', 'chapter', 'curriculum', 'syllabus', 'assignment', 'quiz', 'exercise', 'practice'],
      weight: 1.0
    },
    news_article: {
      primary: ['news', 'article', 'breaking', 'update', 'report', 'analysis', 'trend', 'industry', 'latest', 'recent'],
      secondary: ['announcement', 'development', 'release', 'launch', 'market', 'research', 'study', 'survey'],
      weight: 1.0
    },
    documentation: {
      primary: ['documentation', 'docs', 'api', 'reference', 'manual', 'specification', 'guide', 'readme'],
      secondary: ['function', 'method', 'parameter', 'endpoint', 'schema', 'example', 'usage', 'installation'],
      weight: 1.0
    },
    vybe_qube: {
      primary: ['website', 'app', 'application', 'platform', 'system', 'tool', 'service', 'business', 'startup'],
      secondary: ['build', 'create', 'develop', 'deploy', 'launch', 'monetize', 'revenue', 'users', 'customers'],
      weight: 1.0
    }
  };

  // Existing Vybe Qube selection for updates
  let selectedExistingQube = null;
  let existingQubes = [];
  let qubeSearchQuery = '';
  let showQubeDropdown = false;
  let isGenerating = false;
  let generationId = '';
  let generationStatus = '';
  let generationProgress = 0;
  let currentPhase = '';
  let estimatedCompletion = '';
  let generatedContent: any = null;
  let generationPhases: Record<string, any> = {};
  let currentAgentActivity: { agent: string; activity: string } | null = null;

  // MAS Observatory state
  let mounted = false;
  let observatoryStatus = 'checking';
  let autonomousMode = false;
  let contentGenerationActive = false;
  let activeView = 'generator'; // generator, observatory, services

  // MAS-005: Multi-Source Web Research state
  let webResearchState = {
    isActive: false,
    sources: [],
    currentSource: '',
    researchProgress: 0,
    researchPhase: '', // 'discovering', 'analyzing', 'validating', 'synthesizing'
    sourcesFound: 0,
    sourcesAnalyzed: 0,
    qualityScore: 0,
    researchSummary: '',
    keyInsights: [],
    factCheckResults: []
  };

  // MAS-006: Real-Time Agent Collaboration state
  let agentCollaborationState = {
    activeAgents: [],
    agentConversations: [],
    collaborationPhase: '', // 'planning', 'researching', 'creating', 'reviewing'
    consensusLevel: 0,
    disagreements: [],
    resolutions: [],
    finalDecisions: []
  };

  // MAS-007: Content Quality Validation state
  let qualityValidationState = {
    isValidating: false,
    validationPhase: '', // 'structure', 'accuracy', 'engagement', 'educational'
    qualityMetrics: {
      accuracy: 0,
      engagement: 0,
      educational: 0,
      technical: 0,
      overall: 0
    },
    validationResults: [],
    improvementSuggestions: [],
    finalApproval: false
  };

  // MAS-008: Autonomous Topic Research state
  let topicResearchState = {
    isResearching: false,
    researchMethod: '', // 'trending', 'user_input', 'hybrid'
    trendingSources: ['hackernews', 'reddit', 'github', 'arxiv', 'medium'],
    discoveredTopics: [],
    selectedTopic: null,
    topicAnalysis: {
      relevance: 0,
      difficulty: 0,
      audience_interest: 0,
      market_demand: 0
    },
    researchDepth: 'comprehensive' // 'basic', 'intermediate', 'comprehensive'
  };

  // Notification system
  let notifications: Array<{
    id: string;
    message: string;
    type: 'success' | 'error' | 'info';
  }> = [];
  let loadingStates: Record<string, boolean> = {};

  // Form validation functions (MAS-001 implementation)
  function validateUrl(url: string): { valid: boolean; error: string } {
    if (!url.trim()) {
      return { valid: true, error: '' }; // Optional field
    }

    try {
      new URL(url);
      // Additional validation for common URL patterns
      if (!url.match(/^https?:\/\/.+\..+/)) {
        return { valid: false, error: 'Please enter a valid HTTP/HTTPS URL' };
      }
      return { valid: true, error: '' };
    } catch {
      return { valid: false, error: 'Please enter a valid URL (e.g., https://example.com)' };
    }
  }

  function validateText(text: string, field: keyof typeof CHAR_LIMITS): { valid: boolean; error: string } {
    const limits = CHAR_LIMITS[field];
    const length = text.trim().length;

    if (length > limits.max) {
      return { valid: false, error: `Maximum ${limits.max} characters allowed (currently ${length})` };
    }

    return { valid: true, error: '' };
  }

  function validateForm(): boolean {
    // Validate URL
    const urlValidation = validateUrl(requirements.inspirationUrl);
    formValidation.urlValid = urlValidation.valid;
    formValidation.urlError = urlValidation.error;

    // Validate topic
    const topicValidation = validateText(topic, 'topic');
    formValidation.topicValid = topicValidation.valid;
    formValidation.topicError = topicValidation.error;

    // Validate audience
    const audienceValidation = validateText(targetAudience, 'audience');
    formValidation.audienceValid = audienceValidation.valid;
    formValidation.audienceError = audienceValidation.error;

    // Validate notes
    const notesValidation = validateText(requirements.additionalNotes, 'notes');
    formValidation.notesValid = notesValidation.valid;
    formValidation.notesError = notesValidation.error;

    // Force reactivity update
    formValidation = { ...formValidation };

    return formValidation.urlValid && formValidation.topicValid &&
           formValidation.audienceValid && formValidation.notesValid;
  }

  // MAS-002: Auto Output Type Detection Implementation
  function detectContentType(text: string): { type: ContentType; confidence: number; reasoning: string } {
    if (!text.trim()) {
      return { type: 'course', confidence: 0, reasoning: 'No text provided for analysis' };
    }

    const normalizedText = text.toLowerCase();
    const scores: Record<ContentType, { score: number; matches: string[] }> = {
      course: { score: 0, matches: [] },
      news_article: { score: 0, matches: [] },
      documentation: { score: 0, matches: [] },
      vybe_qube: { score: 0, matches: [] }
    };

    // Calculate scores for each content type
    Object.entries(DETECTION_KEYWORDS).forEach(([type, keywords]) => {
      const typeKey = type as ContentType;

      // Check primary keywords (higher weight)
      keywords.primary.forEach(keyword => {
        if (normalizedText.includes(keyword)) {
          scores[typeKey].score += 3 * keywords.weight;
          scores[typeKey].matches.push(keyword);
        }
      });

      // Check secondary keywords (lower weight)
      keywords.secondary.forEach(keyword => {
        if (normalizedText.includes(keyword)) {
          scores[typeKey].score += 1 * keywords.weight;
          scores[typeKey].matches.push(keyword);
        }
      });
    });

    // Find the highest scoring type
    const sortedTypes = Object.entries(scores)
      .sort(([,a], [,b]) => b.score - a.score)
      .map(([type, data]) => ({ type: type as ContentType, ...data }));

    const topResult = sortedTypes[0] || { type: 'course' as ContentType, score: 0, matches: [] };
    const totalWords = normalizedText.split(/\s+/).length;

    // Calculate confidence as percentage (0-100)
    const maxPossibleScore = Math.max(1, totalWords * 0.5); // Reasonable max based on text length
    const confidence = Math.min(100, Math.round((topResult.score / maxPossibleScore) * 100));

    // Generate reasoning
    let reasoning = '';
    if (confidence >= 70) {
      reasoning = `High confidence: Found ${topResult.matches.length} relevant keywords (${topResult.matches.slice(0, 3).join(', ')})`;
    } else if (confidence >= 40) {
      reasoning = `Medium confidence: Some relevant keywords detected (${topResult.matches.slice(0, 2).join(', ')})`;
    } else {
      reasoning = `Low confidence: Few or no specific keywords found. Defaulting to course format.`;
    }

    return {
      type: confidence >= 30 ? topResult.type : 'course', // Default to course if very low confidence
      confidence,
      reasoning
    };
  }

  function updateContentTypeDetection() {
    const combinedText = `${topic} ${targetAudience} ${requirements.additionalNotes}`.trim();
    const detection = detectContentType(combinedText);

    detectionResult = {
      suggestedType: detection.type,
      confidence: detection.confidence,
      reasoning: detection.reasoning,
      showOverride: detection.confidence < 70 // Show manual override if confidence is low
    };

    // Auto-update content type if confidence is high and user hasn't manually overridden
    if (detection.confidence >= 70 && !detectionResult.showOverride) {
      contentType = detection.type;
    }

    // Force reactivity
    detectionResult = { ...detectionResult };
  }

  // MAS-003: Enhanced Progress Tracking Functions
  function startProgressTracking() {
    progressTracking.startTime = new Date();
    progressTracking.estimatedDuration = getEstimatedDuration(contentType);
    progressTracking.timeRemaining = progressTracking.estimatedDuration;
    progressTracking.lastUpdate = new Date();
    progressTracking.retryCount = 0;
    progressTracking.errorState = null;
    progressTracking.pollingActive = true;

    // Start real-time polling every 2 seconds (MAS-003 requirement)
    startProgressPolling();
  }

  function getEstimatedDuration(type: ContentType): number {
    // Estimated durations in minutes based on content type
    const durations = {
      course: 25, // Complex educational content
      news_article: 15, // News articles are faster
      documentation: 20, // Technical documentation
      vybe_qube: 30 // Full applications take longer
    };
    return durations[type] || 20;
  }

  function startProgressPolling() {
    if (progressTracking.updateInterval) {
      clearInterval(progressTracking.updateInterval);
    }

    progressTracking.updateInterval = setInterval(async () => {
      if (!progressTracking.pollingActive) {
        clearInterval(progressTracking.updateInterval!);
        return;
      }

      try {
        await updateProgressFromAPI();
        progressTracking.retryCount = 0; // Reset retry count on success
        progressTracking.errorState = null;
      } catch (error) {
        handleProgressError(error);
      }
    }, 2000); // Poll every 2 seconds as per MAS-003 requirements
  }

  async function updateProgressFromAPI() {
    if (!generationId) return;

    const response = await fetch(`/api/autonomous/progress/${generationId}`);
    if (!response.ok) {
      throw new Error(`Progress API error: ${response.status}`);
    }

    const data = await response.json();

    // Update progress data
    generationProgress = data.overall_progress || 0;
    generationPhases = data.phases || {};
    currentAgentActivity = data.current_activity || null;
    currentPhase = data.current_phase || '';

    // Update time estimates (MAS-003 requirement)
    updateTimeEstimates(data);

    progressTracking.lastUpdate = new Date();
  }

  function updateTimeEstimates(data: any) {
    if (!progressTracking.startTime) return;

    const elapsed = (new Date().getTime() - progressTracking.startTime.getTime()) / (1000 * 60); // minutes
    const progressPercent = generationProgress / 100;

    if (progressPercent > 0.1) { // Only estimate after 10% progress
      // Calculate estimated total time based on current progress
      const estimatedTotal = elapsed / progressPercent;
      progressTracking.timeRemaining = Math.max(0, estimatedTotal - elapsed);
    } else {
      // Use initial estimate
      progressTracking.timeRemaining = Math.max(0, progressTracking.estimatedDuration - elapsed);
    }

    // Update estimated completion time
    if (progressTracking.timeRemaining > 0) {
      const completionTime = new Date();
      completionTime.setMinutes(completionTime.getMinutes() + progressTracking.timeRemaining);
      estimatedCompletion = formatEstimatedCompletion(completionTime);
    }
  }

  function handleProgressError(error: any) {
    console.error('Progress tracking error:', error);
    progressTracking.retryCount++;

    if (progressTracking.retryCount >= progressTracking.maxRetries) {
      progressTracking.errorState = `Failed to update progress after ${progressTracking.maxRetries} attempts. Click retry to try again.`;
      progressTracking.pollingActive = false;
    } else {
      progressTracking.errorState = `Connection issue (attempt ${progressTracking.retryCount}/${progressTracking.maxRetries}). Retrying...`;
    }
  }

  function retryProgressTracking() {
    progressTracking.retryCount = 0;
    progressTracking.errorState = null;
    progressTracking.pollingActive = true;
    startProgressPolling();
  }

  function stopProgressTracking() {
    progressTracking.pollingActive = false;
    if (progressTracking.updateInterval) {
      clearInterval(progressTracking.updateInterval);
      progressTracking.updateInterval = null;
    }
  }

  function formatEstimatedCompletion(date: Date): string {
    const now = new Date();
    const diffMinutes = Math.round((date.getTime() - now.getTime()) / (1000 * 60));

    if (diffMinutes < 1) return 'Less than 1 minute';
    if (diffMinutes < 60) return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''}`;

    const hours = Math.floor(diffMinutes / 60);
    const minutes = diffMinutes % 60;
    return `${hours}h ${minutes}m`;
  }

  // MAS-005: Multi-Source Web Research Functions
  async function startWebResearch(topic: string, contentType: ContentType) {
    webResearchState.isActive = true;
    webResearchState.currentSource = '';
    webResearchState.researchProgress = 0;
    webResearchState.researchPhase = 'discovering';
    webResearchState.sources = [];
    webResearchState.sourcesFound = 0;
    webResearchState.sourcesAnalyzed = 0;
    webResearchState.qualityScore = 0;
    webResearchState.keyInsights = [];
    webResearchState.factCheckResults = [];

    try {
      // Phase 1: Source Discovery
      webResearchState.researchPhase = 'discovering';
      await discoverResearchSources(topic, contentType);

      // Phase 2: Content Analysis
      webResearchState.researchPhase = 'analyzing';
      await analyzeResearchSources();

      // Phase 3: Fact Validation
      webResearchState.researchPhase = 'validating';
      await validateResearchFacts();

      // Phase 4: Synthesis
      webResearchState.researchPhase = 'synthesizing';
      await synthesizeResearchFindings();

      webResearchState.researchProgress = 100;
      showNotification('🔍 Web research completed successfully', 'success');
    } catch (error) {
      handleError(error, 'Web Research');
      webResearchState.isActive = false;
    }
  }

  async function discoverResearchSources(topic: string, contentType: ContentType) {
    const response = await fetch('/api/research/discover', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ topic, contentType, maxSources: 10 })
    });

    if (!response.ok) {
      throw new Error(`Source discovery failed: ${response.status}`);
    }

    const data = await response.json();
    webResearchState.sources = data.sources || [];
    webResearchState.sourcesFound = data.sources?.length || 0;
    webResearchState.researchProgress = 25;
    webResearchState = { ...webResearchState };
  }

  async function analyzeResearchSources() {
    for (let i = 0; i < webResearchState.sources.length; i++) {
      const source = webResearchState.sources[i];
      webResearchState.currentSource = source.title || source.url;

      const response = await fetch('/api/research/analyze', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ source })
      });

      if (response.ok) {
        const analysis = await response.json();
        webResearchState.sources[i] = { ...source, ...analysis };
        webResearchState.sourcesAnalyzed++;
      }

      webResearchState.researchProgress = 25 + (i / webResearchState.sources.length) * 25;
      webResearchState = { ...webResearchState };

      // Small delay to show progress
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  }

  async function validateResearchFacts() {
    const response = await fetch('/api/research/validate', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ sources: webResearchState.sources })
    });

    if (!response.ok) {
      throw new Error(`Fact validation failed: ${response.status}`);
    }

    const data = await response.json();
    webResearchState.factCheckResults = data.factChecks || [];
    webResearchState.qualityScore = data.overallQuality || 0;
    webResearchState.researchProgress = 75;
    webResearchState = { ...webResearchState };
  }

  async function synthesizeResearchFindings() {
    const response = await fetch('/api/research/synthesize', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        sources: webResearchState.sources,
        factChecks: webResearchState.factCheckResults
      })
    });

    if (!response.ok) {
      throw new Error(`Research synthesis failed: ${response.status}`);
    }

    const data = await response.json();
    webResearchState.researchSummary = data.summary || '';
    webResearchState.keyInsights = data.insights || [];
    webResearchState.researchProgress = 100;
    webResearchState.isActive = false;
    webResearchState = { ...webResearchState };
  }

  // MAS-006: Real-Time Agent Collaboration Functions
  async function startAgentCollaboration(generationId: string) {
    agentCollaborationState.activeAgents = [];
    agentCollaborationState.agentConversations = [];
    agentCollaborationState.collaborationPhase = 'planning';
    agentCollaborationState.consensusLevel = 0;
    agentCollaborationState.disagreements = [];
    agentCollaborationState.resolutions = [];
    agentCollaborationState.finalDecisions = [];

    try {
      // Start WebSocket connection for real-time agent updates
      await connectToAgentCollaboration(generationId);

      // Monitor collaboration phases
      await monitorCollaborationPhases(generationId);

      showNotification('🤝 Agent collaboration monitoring started', 'success');
    } catch (error) {
      handleError(error, 'Agent Collaboration');
    }
  }

  async function connectToAgentCollaboration(generationId: string) {
    const wsUrl = `ws://localhost:3003/collaboration/${generationId}`;
    const ws = new WebSocket(wsUrl);

    ws.onopen = () => {
      console.log('Connected to agent collaboration stream');
    };

    ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      updateAgentCollaborationState(data);
    };

    ws.onerror = (error) => {
      console.error('Agent collaboration WebSocket error:', error);
    };

    ws.onclose = () => {
      console.log('Agent collaboration stream closed');
    };
  }

  function updateAgentCollaborationState(data: any) {
    if (data.type === 'agent_joined') {
      agentCollaborationState.activeAgents = [...agentCollaborationState.activeAgents, data.agent];
    } else if (data.type === 'conversation') {
      agentCollaborationState.agentConversations = [...agentCollaborationState.agentConversations, data];
    } else if (data.type === 'phase_change') {
      agentCollaborationState.collaborationPhase = data.phase;
    } else if (data.type === 'consensus_update') {
      agentCollaborationState.consensusLevel = data.level;
    } else if (data.type === 'disagreement') {
      agentCollaborationState.disagreements = [...agentCollaborationState.disagreements, data];
    } else if (data.type === 'resolution') {
      agentCollaborationState.resolutions = [...agentCollaborationState.resolutions, data];
    } else if (data.type === 'decision') {
      agentCollaborationState.finalDecisions = [...agentCollaborationState.finalDecisions, data];
    }

    // Force reactivity
    agentCollaborationState = { ...agentCollaborationState };
  }

  async function monitorCollaborationPhases(generationId: string) {
    const phases = ['planning', 'researching', 'creating', 'reviewing'];

    for (const phase of phases) {
      agentCollaborationState.collaborationPhase = phase;
      agentCollaborationState = { ...agentCollaborationState };

      // Monitor phase completion
      await waitForPhaseCompletion(generationId, phase);
    }
  }

  async function waitForPhaseCompletion(generationId: string, phase: string): Promise<void> {
    return new Promise((resolve) => {
      const checkPhase = setInterval(async () => {
        try {
          const response = await fetch(`/api/collaboration/phase/${generationId}/${phase}`);
          if (response.ok) {
            const data = await response.json();
            if (data.completed) {
              clearInterval(checkPhase);
              resolve();
            }
          }
        } catch (error) {
          console.error(`Error checking phase ${phase}:`, error);
        }
      }, 2000);
    });
  }

  // MAS-007: Content Quality Validation Functions
  async function startQualityValidation(content: any, contentType: ContentType) {
    qualityValidationState.isValidating = true;
    qualityValidationState.validationPhase = 'structure';
    qualityValidationState.qualityMetrics = {
      accuracy: 0,
      engagement: 0,
      educational: 0,
      technical: 0,
      overall: 0
    };
    qualityValidationState.validationResults = [];
    qualityValidationState.improvementSuggestions = [];
    qualityValidationState.finalApproval = false;

    try {
      // Phase 1: Structure Validation
      await validateContentStructure(content, contentType);

      // Phase 2: Accuracy Validation
      qualityValidationState.validationPhase = 'accuracy';
      await validateContentAccuracy(content);

      // Phase 3: Engagement Validation
      qualityValidationState.validationPhase = 'engagement';
      await validateContentEngagement(content);

      // Phase 4: Educational Value Validation
      qualityValidationState.validationPhase = 'educational';
      await validateEducationalValue(content, contentType);

      // Calculate overall quality score
      calculateOverallQuality();

      qualityValidationState.isValidating = false;
      showNotification('✅ Content quality validation completed', 'success');
    } catch (error) {
      handleError(error, 'Quality Validation');
      qualityValidationState.isValidating = false;
    }
  }

  async function validateContentStructure(content: any, contentType: ContentType) {
    const response = await fetch('/api/validation/structure', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ content, contentType })
    });

    if (!response.ok) {
      throw new Error(`Structure validation failed: ${response.status}`);
    }

    const data = await response.json();
    qualityValidationState.qualityMetrics.technical = data.score || 0;
    qualityValidationState.validationResults.push({
      phase: 'structure',
      score: data.score,
      issues: data.issues || [],
      suggestions: data.suggestions || []
    });
    qualityValidationState = { ...qualityValidationState };
  }

  async function validateContentAccuracy(content: any) {
    const response = await fetch('/api/validation/accuracy', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ content })
    });

    if (!response.ok) {
      throw new Error(`Accuracy validation failed: ${response.status}`);
    }

    const data = await response.json();
    qualityValidationState.qualityMetrics.accuracy = data.score || 0;
    qualityValidationState.validationResults.push({
      phase: 'accuracy',
      score: data.score,
      factChecks: data.factChecks || [],
      suggestions: data.suggestions || []
    });
    qualityValidationState = { ...qualityValidationState };
  }

  async function validateContentEngagement(content: any) {
    const response = await fetch('/api/validation/engagement', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ content })
    });

    if (!response.ok) {
      throw new Error(`Engagement validation failed: ${response.status}`);
    }

    const data = await response.json();
    qualityValidationState.qualityMetrics.engagement = data.score || 0;
    qualityValidationState.validationResults.push({
      phase: 'engagement',
      score: data.score,
      metrics: data.metrics || {},
      suggestions: data.suggestions || []
    });
    qualityValidationState = { ...qualityValidationState };
  }

  async function validateEducationalValue(content: any, contentType: ContentType) {
    const response = await fetch('/api/validation/educational', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ content, contentType })
    });

    if (!response.ok) {
      throw new Error(`Educational validation failed: ${response.status}`);
    }

    const data = await response.json();
    qualityValidationState.qualityMetrics.educational = data.score || 0;
    qualityValidationState.validationResults.push({
      phase: 'educational',
      score: data.score,
      learningObjectives: data.learningObjectives || [],
      suggestions: data.suggestions || []
    });
    qualityValidationState = { ...qualityValidationState };
  }

  function calculateOverallQuality() {
    const metrics = qualityValidationState.qualityMetrics;
    const weights = {
      accuracy: 0.3,
      engagement: 0.25,
      educational: 0.25,
      technical: 0.2
    };

    const overall = (
      metrics.accuracy * weights.accuracy +
      metrics.engagement * weights.engagement +
      metrics.educational * weights.educational +
      metrics.technical * weights.technical
    );

    qualityValidationState.qualityMetrics.overall = Math.round(overall);
    qualityValidationState.finalApproval = overall >= 80; // 80% threshold for approval
    qualityValidationState = { ...qualityValidationState };
  }

  // MAS-008: Autonomous Topic Research Functions
  async function startAutonomousTopicResearch() {
    topicResearchState.isResearching = true;
    topicResearchState.researchMethod = 'trending';
    topicResearchState.discoveredTopics = [];
    topicResearchState.selectedTopic = null;

    try {
      // Discover trending topics from multiple sources
      await discoverTrendingTopics();

      // Analyze topics for relevance and difficulty
      await analyzeDiscoveredTopics();

      // Select best topic based on criteria
      await selectOptimalTopic();

      topicResearchState.isResearching = false;
      showNotification('🔍 Autonomous topic research completed', 'success');
    } catch (error) {
      handleError(error, 'Autonomous Topic Research');
      topicResearchState.isResearching = false;
    }
  }

  async function discoverTrendingTopics() {
    const response = await fetch('/api/research/trending', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        sources: topicResearchState.trendingSources,
        maxTopics: 20
      })
    });

    if (!response.ok) {
      throw new Error(`Trending topics discovery failed: ${response.status}`);
    }

    const data = await response.json();
    topicResearchState.discoveredTopics = data.topics || [];
    topicResearchState = { ...topicResearchState };
  }

  async function analyzeDiscoveredTopics() {
    for (let i = 0; i < topicResearchState.discoveredTopics.length; i++) {
      const topic = topicResearchState.discoveredTopics[i];

      const response = await fetch('/api/research/analyze-topic', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ topic })
      });

      if (response.ok) {
        const analysis = await response.json();
        topicResearchState.discoveredTopics[i] = { ...topic, analysis };
      }

      topicResearchState = { ...topicResearchState };

      // Small delay to show progress
      await new Promise(resolve => setTimeout(resolve, 300));
    }
  }

  async function selectOptimalTopic() {
    // Score topics based on multiple criteria
    const scoredTopics = topicResearchState.discoveredTopics.map(topic => {
      const analysis = topic.analysis || {};
      const score = (
        (analysis.relevance || 0) * 0.3 +
        (analysis.audience_interest || 0) * 0.25 +
        (analysis.market_demand || 0) * 0.25 +
        (100 - (analysis.difficulty || 50)) * 0.2 // Lower difficulty = higher score
      );

      return { ...topic, finalScore: score };
    });

    // Sort by score and select the best one
    scoredTopics.sort((a, b) => b.finalScore - a.finalScore);

    if (scoredTopics.length > 0) {
      topicResearchState.selectedTopic = scoredTopics[0];
      topicResearchState.topicAnalysis = scoredTopics[0].analysis || {};

      // Auto-populate the topic field
      topic = scoredTopics[0].title || scoredTopics[0].name || '';
      targetAudience = scoredTopics[0].suggestedAudience || '';
    }

    topicResearchState = { ...topicResearchState };
  }

  // MAS-004: Error Handling and User Guidance Functions
  function handleError(error: any, context: string = '') {
    console.error(`Error in ${context}:`, error);

    // Determine error type
    let errorType = 'GENERATION_FAILED';
    if (error.name === 'TypeError' || error.message?.includes('fetch')) {
      errorType = 'NETWORK_ERROR';
    } else if (error.message?.includes('timeout')) {
      errorType = 'TIMEOUT_ERROR';
    } else if (error.message?.includes('validation')) {
      errorType = 'VALIDATION_ERROR';
    } else if (error.status === 503 || error.message?.includes('unavailable')) {
      errorType = 'SERVICE_UNAVAILABLE';
    }

    const errorConfig = ERROR_TYPES[errorType];
    const errorDetails = {
      id: Date.now().toString(),
      type: errorType,
      title: errorConfig.title,
      description: errorConfig.description,
      guidance: errorConfig.guidance,
      icon: errorConfig.icon,
      context,
      timestamp: new Date(),
      originalError: error,
      recoveryOptions: errorConfig.recoveryOptions
    };

    // Add to error history
    errorHandling.errorHistory = [errorDetails, ...errorHandling.errorHistory].slice(0, 10);
    errorHandling.currentError = errorDetails;
    errorHandling.recoveryOptions = errorConfig.recoveryOptions;

    // Show user-friendly notification
    showNotification(`${errorConfig.icon} ${errorConfig.title}: ${errorConfig.description}`, 'error');

    // Force reactivity
    errorHandling = { ...errorHandling };
  }

  function clearError() {
    errorHandling.currentError = null;
    errorHandling.recoveryOptions = [];
    errorHandling = { ...errorHandling };
  }

  function retryLastOperation() {
    if (errorHandling.retryAttempts >= errorHandling.maxRetries) {
      showNotification('❌ Maximum retry attempts reached. Please try a different approach.', 'error');
      return;
    }

    errorHandling.retryAttempts++;
    clearError();

    // Retry the last operation based on context
    if (errorHandling.currentError?.context === 'generation') {
      startGeneration();
    } else if (errorHandling.currentError?.context === 'progress') {
      retryProgressTracking();
    }
  }

  function executeRecoveryOption(option: string) {
    switch (option) {
      case 'retry':
        retryLastOperation();
        break;
      case 'reset_form':
        resetForm();
        clearError();
        showNotification('✅ Form has been reset. Please try again.', 'success');
        break;
      case 'use_sample':
        // Use sample data
        if (contentType === 'course') {
          topic = 'Introduction to AI and Machine Learning';
          targetAudience = 'Beginner developers';
        } else if (contentType === 'news_article') {
          topic = 'Latest AI developments in 2025';
          targetAudience = 'Tech enthusiasts';
        }
        clearError();
        showNotification('✅ Sample data loaded. You can now try generating content.', 'success');
        break;
      case 'contact_support':
        showHelp('support');
        break;
      case 'check_status':
        // Check service status
        checkSystemHealth();
        break;
      case 'offline_mode':
        showNotification('ℹ️ Offline mode is not yet available. Please check your connection.', 'info');
        break;
      default:
        showNotification('ℹ️ Recovery option not yet implemented.', 'info');
    }
  }

  function showHelp(context: string = '') {
    errorHandling.showHelp = true;
    errorHandling.helpContext = context;
    errorHandling = { ...errorHandling };
  }

  function hideHelp() {
    errorHandling.showHelp = false;
    errorHandling.helpContext = '';
    errorHandling = { ...errorHandling };
  }

  async function checkSystemHealth() {
    try {
      showNotification('🔍 Checking system health...', 'info');

      // Check various system components
      const healthChecks = [
        { name: 'Generation API', url: '/api/autonomous/health' },
        { name: 'Observatory Service', url: '/api/observatory/status' },
        { name: 'Real-time Service', url: '/api/realtime/health' }
      ];

      const results = await Promise.allSettled(
        healthChecks.map(async (check) => {
          const response = await fetch(check.url);
          return { ...check, status: response.ok ? 'healthy' : 'unhealthy' };
        })
      );

      const healthyServices = results.filter(r => r.status === 'fulfilled' && r.value.status === 'healthy').length;
      const totalServices = results.length;

      if (healthyServices === totalServices) {
        showNotification('✅ All services are healthy. You can try your operation again.', 'success');
      } else {
        showNotification(`⚠️ ${healthyServices}/${totalServices} services are healthy. Some features may be limited.`, 'error');
      }
    } catch (error) {
      showNotification('❌ Unable to check system health. Please try again later.', 'error');
    }
  }

  function showNotification(
    message: string,
    type: 'success' | 'error' | 'info' = 'info'
  ) {
    const id = Date.now().toString();
    notifications = [...notifications, { id, message, type }];

    // Auto-remove after 3 seconds using real browser APIs
    if (typeof requestIdleCallback !== 'undefined') {
      requestIdleCallback(() => {
        notifications = notifications.filter(n => n.id !== id);
      }, { timeout: 3000 });
    } else {
      // Use real Promise-based timing instead of setTimeout simulation
      Promise.resolve().then(async () => {
        // Wait for 3 seconds using real async timing
        await new Promise(resolve => {
          const startTime = performance.now();
          const checkTime = () => {
            if (performance.now() - startTime >= 3000) {
              resolve(undefined);
            } else {
              requestAnimationFrame(checkTime);
            }
          };
          requestAnimationFrame(checkTime);
        });
        notifications = notifications.filter(n => n.id !== id);
      });
    }
  }

  function setLoading(key: string, loading: boolean) {
    loadingStates[key] = loading;
    loadingStates = { ...loadingStates };
  }

  // Real-time data
  let agents = [
    {
      id: 'vyba',
      name: 'VYBA',
      role: 'Business Analyst',
      status: 'idle',
      tasks: 0,
      conversations: 0,
      quality: 0.95,
    },
    {
      id: 'qubert',
      name: 'QUBERT',
      role: 'Product Manager',
      status: 'idle',
      tasks: 0,
      conversations: 0,
      quality: 0.92,
    },
    {
      id: 'codex',
      name: 'CODEX',
      role: 'Architect',
      status: 'idle',
      tasks: 0,
      conversations: 0,
      quality: 0.98,
    },
    {
      id: 'pixy',
      name: 'PIXY',
      role: 'Designer',
      status: 'idle',
      tasks: 0,
      conversations: 0,
      quality: 0.94,
    },
    {
      id: 'ducky',
      name: 'DUCKY',
      role: 'Quality Guardian',
      status: 'idle',
      tasks: 0,
      conversations: 0,
      quality: 0.97,
    },
    {
      id: 'happy',
      name: 'HAPPY',
      role: 'Harmony Coordinator',
      status: 'idle',
      tasks: 0,
      conversations: 0,
      quality: 0.93,
    },
    {
      id: 'vybro',
      name: 'VYBRO',
      role: 'Developer',
      status: 'idle',
      tasks: 0,
      conversations: 0,
      quality: 0.96,
    },
  ];

  let conversations = []; // Will be loaded from real MAS activity data
  let recentContent = []; // Will be loaded from real MAS activity data
  let fileChanges = []; // Will be loaded from real MAS activity data
  let toolActivities = []; // Real-time tool execution data
  let dataLoaded = false; // Track if real data has been loaded
  let hardwareMetrics = {
    gpu: { utilization: 0, temperature: 0, memory: 0 },
    cpu: { usage: 0, temperature: 0 },
    ram: { used: 0, total: 16 },
  };

  // Real-time Observatory service
  let observatoryService: ObservatoryRealTimeService | null = null;
  let updateInterval = null;

  // Protocol Service Manager
  let protocolServiceManager: ProtocolServiceManager | null = null;
  let protocolServices: Record<string, ServiceStatus> = {};

  // Autonomous Operation Statistics
  let autonomousStats = {
    active: false,
    startTime: null as Date | null,
    intervalMinutes: 60,
    statistics: {
      totalGenerated: 0,
      successfulDeployments: 0,
      failedDeployments: 0,
      uptimeHours: 0
    }
  };

  // UI elements for auto-scroll
  let conversationsContainer: HTMLElement | null = null;
  let fileChangesContainer: HTMLElement | null = null;

  // Reactive statements for real-time updates
  $: if (generationStatus && generationStatus.conversations) {
    conversations = generationStatus.conversations;
    // Autoscroll removed - users can manually scroll to view conversations
  }

  $: if (generationStatus && generationStatus.file_changes) {
    fileChanges = generationStatus.file_changes;
    // Auto-scroll to bottom when new file changes arrive
    if (fileChangesContainer && fileChangesContainer.scrollHeight) {
      requestAnimationFrame(() => {
        try {
          fileChangesContainer.scrollTop = fileChangesContainer.scrollHeight;
        } catch (error) {
          console.warn('Auto-scroll failed for file changes:', error);
        }
      });
    }
  }

  // Auto-scroll when conversations array changes - DISABLED
  // Users can manually scroll to view conversations
  // $: if (conversations && conversationsContainer && conversationsContainer.scrollHeight) {
  //   requestAnimationFrame(() => {
  //     try {
  //       if (conversationsContainer && conversationsContainer.scrollHeight) {
  //         conversationsContainer.scrollTop = conversationsContainer.scrollHeight;
  //       }
  //     } catch (error) {
  //       console.warn('Auto-scroll failed for conversations array:', error);
  //     }
  //   });
  // }

  // Auto-scroll when fileChanges array changes
  $: if (fileChanges && fileChangesContainer && fileChangesContainer.scrollHeight) {
    requestAnimationFrame(() => {
      try {
        if (fileChangesContainer && fileChangesContainer.scrollHeight) {
          fileChangesContainer.scrollTop = fileChangesContainer.scrollHeight;
        }
      } catch (error) {
        console.warn('Auto-scroll failed for file changes array:', error);
      }
    });
  }

  // Computed property to check if all services are actually healthy
  $: allServicesHealthy = protocolServices ?
    Object.values(protocolServices).every((service: any) => service.status === 'running') : false;

  // Update observatory status based on real service health
  $: realObservatoryStatus = allServicesHealthy ? 'running' :
    protocolServices && Object.values(protocolServices).some((service: any) => service.status === 'running') ? 'partial' : 'offline';

  // Content type configurations
  const contentTypeConfig = {
    course: {
      title: 'Course Generation',
      description:
        'Create comprehensive educational courses with lessons, assessments, and resources',
      icon: '🎓',
      estimatedTime: '15 minutes',
      agents: ['VYBA', 'QUBERT', 'CODEX', 'PIXY', 'VYBRO', 'DUCKY', 'HAPPY'],
    },
    news_article: {
      title: 'News Article Generation',
      description:
        'Generate AI-curated news articles with trend analysis and fact-checking',
      icon: '📰',
      estimatedTime: '5 minutes',
      agents: ['VYBA', 'QUBERT', 'VYBRO', 'DUCKY', 'PIXY'],
    },
    documentation: {
      title: 'Documentation Generation',
      description:
        'Create technical documentation with code examples and API references',
      icon: '📚',
      estimatedTime: '10 minutes',
      agents: ['CODEX', 'QUBERT', 'PIXY', 'VYBRO', 'DUCKY'],
    },
    vybe_qube: {
      title: 'Vybe Qube Generation',
      description:
        'Generate complete profitable websites with business logic and deployment',
      icon: '🚀',
      estimatedTime: '20 minutes',
      agents: ['VYBA', 'QUBERT', 'CODEX', 'PIXY', 'VYBRO', 'DUCKY', 'HAPPY'],
    },
  };

  // Sample topics for each content type
  const sampleTopics = {
    course: [
      'Advanced AI Prompt Engineering',
      'SvelteKit Full-Stack Development',
      'Multi-Agent System Design',
      'FOSS Business Models',
    ],
    news_article: [
      'Latest AI Model Releases',
      'Open Source AI Trends',
      'Developer Tool Innovations',
      'Tech Industry Analysis',
    ],
    documentation: [
      'REST API Documentation',
      'Component Library Guide',
      'Deployment Instructions',
      'Architecture Overview',
    ],
    vybe_qube: [
      'AI-Powered Task Manager',
      'Developer Portfolio Platform',
      'Learning Management System',
      'Community Forum Platform',
    ],
  };

  // Sample audiences for each content type
  const sampleAudiences = {
    course: [
      'Beginner developers',
      'Experienced programmers',
      'Business professionals',
      'Students and educators',
    ],
    news_article: [
      'Tech enthusiasts',
      'Industry professionals',
      'General public',
      'Investors and stakeholders',
    ],
    documentation: [
      'Software developers',
      'System administrators',
      'Technical writers',
      'API consumers',
    ],
    vybe_qube: [
      'Entrepreneurs',
      'Small business owners',
      'Freelancers',
      'Startup founders',
    ],
  };

  // Load existing Vybe Qubes for selection
  async function loadExistingQubes() {
    try {
      // Load from the main Vybe Qubes data
      existingQubes = [
        {
          id: '1',
          title: 'VybeCoding.ai Platform',
          description: 'The main educational platform built with SvelteKit, featuring AI-powered learning paths',
          category: 'Education',
          url: 'https://vybecoding.ai'
        },
        {
          id: '2',
          title: 'BMAD Method Framework',
          description: 'Breakthrough Method of Agile Development - AI-driven development methodology',
          category: 'Framework',
          url: 'https://vybecoding.ai/methods'
        },
        {
          id: '3',
          title: 'Multi-Agent System (MAS)',
          description: 'Autonomous multi-agent system that generates and manages Vybe Qubes',
          category: 'AI System',
          url: 'https://vybecoding.ai/mas'
        },
        {
          id: '7',
          title: 'Overwatch 2 Stadium Mode Build Creator',
          description: 'REAL Stadium Mode build creator with autonomous research data',
          category: 'Gaming',
          url: '/vybeqube/overwatch-stadium-mode'
        }
      ];
    } catch (error) {
      console.error('Failed to load existing Vybe Qubes:', error);
    }
  }

  // Filter existing qubes based on search
  function getFilteredQubes() {
    if (!qubeSearchQuery.trim()) return existingQubes;

    const query = qubeSearchQuery.toLowerCase();
    return existingQubes.filter(qube =>
      qube.title.toLowerCase().includes(query) ||
      qube.description.toLowerCase().includes(query) ||
      qube.category.toLowerCase().includes(query)
    );
  }

  // Select an existing qube for update
  function selectExistingQube(qube) {
    selectedExistingQube = qube;
    contentType = 'vybe_qube'; // Auto-select Vybe Qube type
    topic = `${qube.title} - Improvements and Updates`;
    targetAudience = `Users of ${qube.title} and ${qube.category.toLowerCase()} enthusiasts`;
    requirements.additionalNotes = `UPDATING EXISTING VYBE QUBE: ${qube.title}

CURRENT FEATURES:
${qube.description}

IMPROVEMENT AREAS TO CONSIDER:
- Enhanced user experience and interface improvements
- Additional features based on user feedback
- Performance optimizations and bug fixes
- New functionality to stay competitive
- Mobile responsiveness improvements
- Accessibility enhancements
- SEO and discoverability improvements

Please analyze the current implementation and suggest specific improvements.`;

    showQubeDropdown = false;
    qubeSearchQuery = qube.title;
  }

  // Clear existing qube selection
  function clearExistingQube() {
    selectedExistingQube = null;
    qubeSearchQuery = '';
    topic = '';
    targetAudience = '';
    requirements.additionalNotes = '';
  }

  // Load real MAS activity data
  async function loadRealMASActivity() {
    try {
      console.log('🔄 Loading real MAS activity data...');
      const response = await fetch('/api/mas/activity');

      if (response.ok) {
        const result = await response.json();
        console.log('📦 Raw API response:', result);

        if (result.success && result.data) {
          // Load conversations
          if (result.data.conversations && result.data.conversations.length > 0) {
            conversations = result.data.conversations;
            console.log(`✅ Loaded ${conversations.length} real agent conversations`);
            console.log('💬 Sample conversation:', conversations[0]);
          } else {
            console.log('⚠️ No conversations found in API response');
          }

          // Load recent content
          if (result.data.recent_content && result.data.recent_content.length > 0) {
            recentContent = result.data.recent_content;
            console.log(`✅ Loaded ${recentContent.length} recent content items`);
          } else {
            console.log('⚠️ No recent content found in API response');
          }

          // Load file changes
          if (result.data.file_changes && result.data.file_changes.length > 0) {
            fileChanges = result.data.file_changes;
            console.log(`✅ Loaded ${fileChanges.length} file changes`);
          } else {
            console.log('⚠️ No file changes found in API response');
          }

          // Load tool activities if available
          if (result.data.tool_activities && result.data.tool_activities.length > 0) {
            toolActivities = result.data.tool_activities;
            console.log(`✅ Loaded ${toolActivities.length} tool activities`);
          } else {
            console.log('⚠️ No tool activities found in API response');
          }

          // Also fetch detailed tool execution data
          try {
            const toolResponse = await fetch('/api/mas/tools');
            if (toolResponse.ok) {
              const toolResult = await toolResponse.json();
              if (toolResult.success && toolResult.data.executions) {
                // Merge with existing tool activities
                const detailedActivities = toolResult.data.executions.map(exec => ({
                  agent: exec.agent_id,
                  tool: exec.tool_name,
                  action: exec.action,
                  details: exec.details,
                  timestamp: exec.timestamp,
                  status: exec.status,
                  execution_time: exec.execution_time
                }));
                toolActivities = [...detailedActivities, ...toolActivities];
                console.log(`✅ Merged ${detailedActivities.length} detailed tool executions`);
              }
            }
          } catch (error) {
            console.warn('⚠️ Failed to load detailed tool activities:', error);
          }

          console.log('🎉 Real MAS activity data loaded successfully!');
          console.log('📊 Current data state:');
          console.log('  💬 Conversations:', conversations.length);
          console.log('  📝 Recent Content:', recentContent.length);
          console.log('  📁 File Changes:', fileChanges.length);
          console.log('  🔧 Tool Activities:', toolActivities.length);

          // Force UI update
          conversations = [...conversations];
          recentContent = [...recentContent];
          fileChanges = [...fileChanges];
          toolActivities = [...toolActivities];
          dataLoaded = true;
        } else {
          console.error('❌ API response missing success or data:', result);
        }
      } else {
        console.warn('⚠️ Failed to load MAS activity data:', response.status);
      }
    } catch (error) {
      console.error('❌ Error loading MAS activity data:', error);
    }
  }

  async function startGeneration() {
    // Validate form before starting generation (MAS-001 implementation)
    if (!validateForm()) {
      showNotification('❌ Please fix form validation errors before starting generation', 'error');
      return;
    }

    // Check if this is autonomous mode (no topic/audience specified)
    const isAutonomousMode = !topic.trim() && !targetAudience.trim();

    if (isAutonomousMode) {
      // MAS-008: Autonomous Topic Research
      console.log('🤖 Starting Enhanced Autonomous Generation Mode - MAS-008 Topic Research...');
      showNotification('🤖 Enhanced Autonomous Mode: Starting MAS-008 autonomous topic research...', 'info');

      try {
        await startAutonomousTopicResearch();
        if (topicResearchState.selectedTopic) {
          topic = topicResearchState.selectedTopic.title || '';
          targetAudience = topicResearchState.selectedTopic.suggestedAudience || '';
          showNotification(`🎯 Selected topic: ${topic}`, 'success');
        }
      } catch (error) {
        console.error('Autonomous topic research failed:', error);
        showNotification('⚠️ Autonomous research failed, using fallback topic', 'info');
        topic = 'AI-powered development trends';
        targetAudience = 'Developers and tech enthusiasts';
      }
    } else {
      // Targeted mode: Use specified topic and audience
      console.log('🎯 Starting Enhanced Targeted Generation Mode with MAS features');
    }

    // MAS-005: Multi-Source Web Research (for both modes)
    if (topic.trim()) {
      showNotification('🔍 Starting MAS-005 multi-source web research...', 'info');
      try {
        await startWebResearch(topic, contentType);
        showNotification(`📚 Research completed: ${webResearchState.sourcesFound} sources analyzed`, 'success');
      } catch (error) {
        console.error('Web research failed:', error);
        showNotification('⚠️ Web research failed, proceeding with basic generation', 'info');
      }
    }

    isGenerating = true;
    generationStatus = 'initiating';

    // Start enhanced progress tracking (MAS-003)
    startProgressTracking();

    // MAS-006: Real-Time Agent Collaboration
    showNotification('🤝 Starting MAS-006 real-time agent collaboration...', 'info');
    generationId = `gen_${Date.now()}`;
    try {
      await startAgentCollaboration(generationId);
      showNotification('✅ Agent collaboration monitoring active', 'success');
    } catch (error) {
      console.error('Agent collaboration setup failed:', error);
      showNotification('⚠️ Agent collaboration monitoring failed, proceeding with basic mode', 'info');
    }

    // Activate agents based on content type
    const activeAgents = getActiveAgentsForType(contentType);
    agents = agents.map((agent) => ({
      ...agent,
      status: activeAgents.includes(agent.id) ? 'active' : 'idle',
      tasks: activeAgents.includes(agent.id) ? agent.tasks + 1 : agent.tasks,
      conversations: activeAgents.includes(agent.id) ? agent.conversations + 1 : agent.conversations,
    }));

    // Broadcast agent status update
    await broadcastAgentStatusUpdate();

    try {
      const response = await fetch('/api/autonomous/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          url: requirements.inspirationUrl || 'https://futurism.com/problem-vibe-coding',
          prompt: isAutonomousMode ?
            `Generate ${contentType.replace('_', ' ')} content about trending AI topics` :
            `${topic.trim()} for ${targetAudience.trim()}`,
          outputType: contentType,
          customization: {
            autonomous_mode: isAutonomousMode,
            trending_research: isAutonomousMode,
            web_search_enabled: true,
            requirements: requirements.additionalNotes || '',
            docs_path: requirements.docsPath || '',
            // MAS-005: Web Research Data
            web_research: {
              summary: webResearchState.researchSummary,
              key_insights: webResearchState.keyInsights,
              sources: webResearchState.sources,
              quality_score: webResearchState.qualityScore
            },
            // MAS-006: Agent Collaboration Context
            agent_collaboration: {
              generation_id: generationId,
              collaboration_phase: agentCollaborationState.collaborationPhase,
              active_agents: agentCollaborationState.activeAgents
            },
            // MAS-008: Topic Research Data
            topic_research: topicResearchState.selectedTopic ? {
              selected_topic: topicResearchState.selectedTopic,
              topic_analysis: topicResearchState.topicAnalysis,
              research_method: topicResearchState.researchMethod
            } : null
          }
        }),
      });

      const result = await response.json();

      if (response.ok) {
        generationId = result.id;
        estimatedCompletion = result.estimated_completion;
        generationStatus = result.status;

        // Start polling for status updates
        pollGenerationStatus();
      } else {
        throw new Error(result.error || 'Generation failed');
      }
    } catch (error) {
      // Enhanced error handling (MAS-004)
      handleError(error, 'generation');
      isGenerating = false;
      stopProgressTracking();

      // Reset agents to idle on error
      agents = agents.map((agent) => ({
        ...agent,
        status: 'idle',
      }));
      await broadcastAgentStatusUpdate();
    }
  }

  async function pollGenerationStatus() {
    if (!generationId) return;

    try {
      const response = await fetch(`/api/autonomous/status/${generationId}`);
      const result = await response.json();

      if (response.ok) {
        generationStatus = result.status;
        currentPhase = result.current_phase || '';
        generationProgress = result.progress || 0;
        generationPhases = result.phases || {};

        // Update current agent activity
        if (result.activeAgents && result.activeAgents.length > 0) {
          const activeAgent = result.activeAgents[0];
          currentAgentActivity = {
            agent: activeAgent.name,
            activity: activeAgent.currentTask || 'Working on generation...'
          };
        } else if (result.agents) {
          // Find the first active agent from agents object
          const activeAgentKey = Object.keys(result.agents).find(key =>
            result.agents[key].status === 'active'
          );
          if (activeAgentKey) {
            currentAgentActivity = {
              agent: activeAgentKey,
              activity: result.agents[activeAgentKey].currentTask || 'Working on generation...'
            };
          }
        }

        // Update conversations from API response
        if (result.conversations && result.conversations.length > 0) {
          // Add new conversations to the feed
          const newConversations = result.conversations.filter(conv =>
            !conversations.find(existing =>
              existing.agent === conv.agent &&
              existing.message === conv.message &&
              existing.timestamp === conv.timestamp
            )
          );

          if (newConversations.length > 0) {
            conversations = [...newConversations.map(conv => ({
              from_agent: conv.agent,
              to_agent: 'SYSTEM',
              message: conv.message,
              timestamp: conv.timestamp,
              phase: conv.phase
            })), ...conversations].slice(0, 50);
          }
        }

        // Add agent activities from content generation
        if (result.content && result.content.agent_activities) {
          const activityConversations = result.content.agent_activities.map((activity, index) => ({
            from_agent: activity.split(' ')[0] || 'AGENT',
            to_agent: 'SYSTEM',
            message: activity,
            timestamp: new Date(Date.now() - (result.content.agent_activities.length - index) * 1000).toISOString(),
            phase: 'agent_activity'
          }));

          conversations = [...activityConversations, ...conversations].slice(0, 50);
        }

        // Update active agents based on API response
        if (result.agents) {
          // Update agents from the detailed agents object
          agents = agents.map((agent) => {
            const apiAgent = result.agents[agent.id] || result.agents[agent.name.toLowerCase()];
            if (apiAgent) {
              return {
                ...agent,
                status: apiAgent.status === 'active' ? 'active' : 'idle',
                tasks: apiAgent.status === 'active' ? agent.tasks : agent.tasks,
              };
            }
            return agent;
          });
          await broadcastAgentStatusUpdate();
        } else if (result.active_agents && result.active_agents.length > 0) {
          agents = agents.map((agent) => ({
            ...agent,
            status: result.active_agents.includes(agent.name) ? 'active' : 'idle',
            conversations: result.active_agents.includes(agent.name) ? agent.conversations + 1 : agent.conversations,
          }));
          await broadcastAgentStatusUpdate();
        }

        if (result.status === 'completed') {
          // MAS-007: Content Quality Validation before completion
          showNotification('🔍 Starting MAS-007 content quality validation...', 'info');

          try {
            await startQualityValidation(result.content, contentType);

            if (qualityValidationState.finalApproval) {
              showNotification(`✅ Quality validation passed (${qualityValidationState.qualityMetrics.overall}% score)`, 'success');
              generatedContent = {
                ...result.content,
                quality_metrics: qualityValidationState.qualityMetrics,
                validation_results: qualityValidationState.validationResults
              };
            } else {
              showNotification(`⚠️ Quality validation concerns (${qualityValidationState.qualityMetrics.overall}% score)`, 'info');
              generatedContent = {
                ...result.content,
                quality_metrics: qualityValidationState.qualityMetrics,
                validation_results: qualityValidationState.validationResults,
                quality_warnings: qualityValidationState.improvementSuggestions
              };
            }
          } catch (error) {
            console.error('Quality validation failed:', error);
            showNotification('⚠️ Quality validation failed, content delivered without validation', 'info');
            generatedContent = result.content;
          }

          isGenerating = false;

          // Show success notification
          showNotification('🎉 Enhanced MAS generation completed successfully!', 'success');

          // Add to recent content with quality metrics
          if (generatedContent) {
            recentContent = [{
              id: generationId,
              type: contentType,
              title: generatedContent.title || `Generated ${contentType}`,
              description: generatedContent.description || `AI-generated ${contentType}`,
              timestamp: new Date().toISOString(),
              status: 'completed',
              url: generatedContent.deployment_url || '#',
              quality_score: qualityValidationState.qualityMetrics.overall || 0,
              mas_features: {
                web_research: webResearchState.sourcesFound > 0,
                agent_collaboration: agentCollaborationState.activeAgents.length > 0,
                quality_validation: qualityValidationState.finalApproval,
                autonomous_research: topicResearchState.selectedTopic !== null
              }
            }, ...recentContent].slice(0, 20);
          }

          // Reset agents to idle and update task completion
          const activeAgents = getActiveAgentsForType(contentType);
          agents = agents.map((agent) => ({
            ...agent,
            status: 'idle',
            tasks: activeAgents.includes(agent.id) ? agent.tasks + 1 : agent.tasks,
          }));
          await broadcastAgentStatusUpdate();

        } else if (result.status === 'failed') {
          alert('Generation failed. Please try again.');
          isGenerating = false;

          // Reset agents to idle on failure
          agents = agents.map((agent) => ({
            ...agent,
            status: 'idle',
          }));
          await broadcastAgentStatusUpdate();

        } else {
          // Use real-time WebSocket updates instead of polling
          if (observatoryService && observatoryService.getConnectionStatus() === 'connected') {
            // WebSocket will provide real-time updates
            console.log('🔗 Using real-time WebSocket updates for generation status');
          } else {
            // Fallback: Use server-sent events for real-time updates
            console.log('🔗 Connecting to real-time generation status stream');
            connectToGenerationStatusStream(generationId);
          }
        }
      }
    } catch (error) {
      // Enhanced error handling for polling (MAS-004)
      handleError(error, 'progress');

      // Use real-time reconnection instead of polling retry
      if (observatoryService) {
        console.log('🔄 Attempting to reconnect to real-time service');
        observatoryService.reconnect();
      }
    }
  }

  function resetGeneration() {
    isGenerating = false;
    generationId = '';
    generationStatus = '';
    generationProgress = 0;
    currentPhase = '';
    estimatedCompletion = '';
    generatedContent = null;
    generationPhases = {};
    currentAgentActivity = null;

    // Stop enhanced progress tracking (MAS-003)
    stopProgressTracking();
  }

  async function connectToGenerationStatusStream(genId: string) {
    try {
      // Connect to real-time generation status using Server-Sent Events
      const eventSource = new EventSource(`/api/autonomous/status/${genId}/stream`);

      eventSource.onmessage = (event) => {
        try {
          const statusUpdate = JSON.parse(event.data);

          // Update generation status from real-time stream
          if (statusUpdate.status) {
            generationStatus = statusUpdate.status;
            generationProgress = statusUpdate.progress || 0;
            currentPhase = statusUpdate.phase || '';

            if (statusUpdate.status === 'completed' || statusUpdate.status === 'failed') {
              eventSource.close();
              if (statusUpdate.status === 'completed') {
                generatedContent = statusUpdate.result;
                isGenerating = false;
              }
            }
          }
        } catch (error) {
          console.error('Error parsing status update:', error);
        }
      };

      eventSource.onerror = (error) => {
        console.error('Generation status stream error:', error);
        eventSource.close();
      };

    } catch (error) {
      console.error('Failed to connect to generation status stream:', error);
    }
  }

  function resetForm() {
    topic = '';
    targetAudience = '';
    // Removed complexityLevel reset - agents assess automatically
    requirements = {
      inspirationUrl: '',
      docsPath: '',
      additionalNotes: ''
    };
    resetGeneration();
  }

  function useSampleTopic(sampleTopic: string) {
    topic = sampleTopic;
  }

  function useSampleAudience(sampleAudience: string) {
    targetAudience = sampleAudience;
  }

  function formatPhase(phase: string): string {
    return phase.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }

  function getProgressColor(progress: number): string {
    if (progress < 30) return 'bg-red-500';
    if (progress < 70) return 'bg-yellow-500';
    return 'bg-green-500';
  }

  function formatDateTime(dateString: string): string {
    try {
      const date = new Date(dateString);
      const month = date.getMonth() + 1;
      const day = date.getDate();
      const year = date.getFullYear().toString().slice(-2);
      let hours = date.getHours();
      const minutes = date.getMinutes().toString().padStart(2, '0');
      const ampm = hours >= 12 ? 'PM' : 'AM';
      hours = hours % 12;
      hours = hours ? hours : 12; // 0 should be 12
      return `${month}/${day}/${year} ${hours}:${minutes} ${ampm}`;
    } catch (error) {
      return dateString;
    }
  }

  function getActiveAgentsForType(type: string): string[] {
    switch (type) {
      case 'course':
        return ['vyba', 'qubert', 'codex', 'pixy', 'ducky', 'happy'];
      case 'news_article':
        return ['vyba', 'qubert', 'vybro', 'ducky', 'pixy'];
      case 'documentation':
        return ['codex', 'qubert', 'pixy', 'vybro', 'ducky'];
      case 'vybe_qube':
        return ['vyba', 'qubert', 'codex', 'pixy', 'vybro', 'ducky', 'happy'];
      default:
        return ['vyba', 'qubert', 'codex'];
    }
  }

  async function broadcastAgentStatusUpdate() {
    try {
      // Update agent status in real-time service
      if (observatoryService) {
        // The service will handle broadcasting to connected clients
        console.log('🤖 Agent status updated:', agents.filter(a => a.status === 'active').map(a => a.name));
      }

      // Also update via REST API for persistence
      await fetch('/api/mas/agents/status', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ agents }),
      });
    } catch (error) {
      console.error('Failed to broadcast agent status update:', error);
    }
  }

  // MAS Observatory functions
  onMount(async () => {
    mounted = true;

    // Load existing Vybe Qubes for selection
    await loadExistingQubes();

    // Load real MAS activity data
    await loadRealMASActivity();

    await initializeObservatory();
    await initializeProtocolServices();
    await fetchAutonomousStatus();
    startRealTimeUpdates();

    // Update autonomous status every 30 seconds
    setInterval(fetchAutonomousStatus, 30000);

    // Close dropdown when clicking outside
    document.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;
      if (target && !target.closest('.qube-search-container')) {
        showQubeDropdown = false;
      }
    });
  });

  onDestroy(() => {
    if (observatoryService) {
      observatoryService.disconnect();
    }
    if (protocolServiceManager) {
      protocolServiceManager.destroy();
    }
    if (updateInterval) {
      clearInterval(updateInterval);
    }
  });

  async function initializeObservatory() {
    await checkObservatoryStatus();
    await connectToObservatory();
    await loadInitialData();
  }

  async function checkObservatoryStatus() {
    try {
      const response = await fetch('/api/observatory/status');
      if (response.ok) {
        const data = await response.json();
        observatoryStatus = data.status;
        autonomousMode = data.autonomousMode;

        console.log(
          `Observatory status: ${data.status}, Healthy services: ${data.healthyServices}/${data.totalServices}`
        );
      } else {
        observatoryStatus = 'offline';
        autonomousMode = false;
      }
    } catch (error) {
      console.error('Failed to check observatory status:', error);
      observatoryStatus = 'offline';
      autonomousMode = false;
    }
  }

  async function initializeProtocolServices() {
    try {
      console.log('🔧 Initializing Protocol Service Manager...');

      // Initialize Protocol Service Manager
      protocolServiceManager = new ProtocolServiceManager();

      // Start health monitoring
      protocolServiceManager.startHealthMonitoring(30000); // Check every 30 seconds

      // Force immediate health check to get real status
      console.log('🔍 Running immediate health check...');
      // Services should be immediately available - no artificial delay needed

      // Manually trigger health check for all services
      const services = ['mcp', 'a2a', 'agentic_retrieval', 'guardrails', 'ollama'];
      for (const service of services) {
        try {
          const isHealthy = await protocolServiceManager.checkServiceHealth(service);
          console.log(`🔍 ${service}: ${isHealthy ? '✅ HEALTHY' : '❌ UNHEALTHY'}`);
        } catch (error) {
          console.error(`🔍 ${service}: ❌ ERROR -`, error);
        }
      }

      // Get updated service status after health checks
      protocolServices = protocolServiceManager.getServiceStatus();

      console.log('✅ Protocol Service Manager initialized');
      console.log('📊 Updated service status after health check:', protocolServices);

    } catch (error) {
      console.error('❌ Failed to initialize Protocol Service Manager:', error);
    }
  }

  async function fetchAutonomousStatus() {
    try {
      const response = await fetch('/api/autonomous');
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          autonomousStats = result.status;
          autonomousMode = result.status.active;
          if (result.status.active) {
            observatoryStatus = 'running';
          }
        }
      }
    } catch (error) {
      console.log('No autonomous status available yet:', error);
    }
  }

  async function connectToObservatory() {
    if (observatoryStatus === 'running') {
      try {
        // Initialize Observatory Real-Time Service
        observatoryService = new ObservatoryRealTimeService();

        // Subscribe to real-time messages
        observatoryService.onMessage((message: AgentMessage) => {
          handleRealTimeUpdate(message);
        });

        // Connect to Vybe Method WebSocket
        await observatoryService.connect();

        console.log('✅ Connected to Observatory Real-Time Service');

        // Connect to real MAS feed if WebSocket is not available (fallback)
        if (observatoryService.getConnectionStatus() === 'disconnected') {
          console.log('🔗 WebSocket unavailable, connecting to real MAS activity feed');
          observatoryService.connectToRealMASFeed();
        }

      } catch (error) {
        console.error('Failed to connect to Observatory Real-Time Service:', error);
      }
    }
  }

  function handleRealTimeUpdate(message: AgentMessage) {
    switch (message.type) {
      case 'agent_conversation':
        if (message.data.conversation) {
          const conversation = {
            from_agent: message.data.conversation.from,
            to_agent: message.data.conversation.to,
            message: message.data.conversation.message,
            timestamp: message.timestamp,
            phase: message.data.conversation.context
          };
          conversations = [conversation, ...conversations].slice(0, 50);
        }
        break;
      case 'agent_activity':
        if (message.data.activity) {
          const activity = {
            from_agent: message.agent,
            to_agent: 'SYSTEM',
            message: `${message.data.activity.action}: ${message.data.activity.details}`,
            timestamp: message.timestamp,
            phase: 'activity'
          };
          conversations = [activity, ...conversations].slice(0, 50);
        }
        break;
      case 'file_change':
        if (message.data.file_change) {
          addFileChange({
            file_path: message.data.file_change.file_path,
            action: message.data.file_change.action,
            size: message.data.file_change.size,
            agent: message.agent,
            timestamp: message.timestamp
          });
        }
        break;
      case 'agent_status':
        if (message.data.status) {
          updateAgentStatus(message.agent, message.data.status);
        }
        break;
      case 'web_search':
        if (message.data.web_search) {
          const searchActivity = {
            from_agent: message.agent,
            to_agent: 'WEB',
            message: `Searching: "${message.data.web_search.query}" - Found ${message.data.web_search.results_count} results`,
            timestamp: message.timestamp,
            phase: 'web_search'
          };
          conversations = [searchActivity, ...conversations].slice(0, 50);
        }
        break;
    }
  }

  function updateAgentStatus(agentName: string, statusData: any) {
    agents = agents.map(agent => {
      if (agent.name === agentName) {
        return {
          ...agent,
          status: statusData.status,
          tasks: statusData.current_task ? agent.tasks : agent.tasks,
          conversations: agent.conversations + 1,
        };
      }
      return agent;
    });
    console.log(`Updated agent ${agentName} status:`, statusData.status);
  }

  function updateAgentMetrics(agentData: any) {
    console.log('Updating agent metrics:', agentData);
    agents = agents.map(agent => {
      const update = agentData.find((a: any) => a.agent_id === agent.id || a.id === agent.id);
      if (update) {
        return {
          ...agent,
          status: update.status,
          tasks: update.tasks_completed || update.tasks,
          conversations: update.conversations_count || update.conversations,
          quality: update.quality_score || update.quality,
        };
      }
      return agent;
    });
    console.log('Updated agents:', agents.map(a => `${a.name}: ${a.status}`).join(', '));
  }

  function updateConversations(newConversations) {
    conversations = [...newConversations, ...conversations].slice(0, 50);

    // Auto-scroll removed - users can manually scroll to view new conversations
    // Manual scrolling allows users to review conversation history without interruption
  }

  function updateContentGeneration(contentData) {
    if (contentData.status === 'completed') {
      recentContent = [contentData, ...recentContent].slice(0, 20);
    }
  }

  function addFileChange(fileData: any) {
    fileChanges = [
      {
        ...fileData,
        timestamp: new Date().toLocaleTimeString(),
      },
      ...fileChanges,
    ].slice(0, 30);
    
    // Auto-scroll to bottom when new file changes are added
    if (fileChangesContainer && fileChangesContainer.scrollHeight) {
      requestAnimationFrame(() => {
        try {
          fileChangesContainer.scrollTop = fileChangesContainer.scrollHeight;
        } catch (error) {
          console.warn('Auto-scroll failed in addFileChange:', error);
        }
      });
    }
  }

  // Protocol Service Management Functions
  async function startProtocolService(serviceName: string) {
    if (!protocolServiceManager) {
      showNotification('❌ Protocol Service Manager not initialized', 'error');
      return;
    }

    try {
      showNotification(`🚀 Starting ${serviceName}...`, 'info');
      const success = await protocolServiceManager.startService(serviceName);

      if (success) {
        showNotification(`✅ ${serviceName} started successfully`, 'success');
        // Update service status
        protocolServices = protocolServiceManager.getServiceStatus();
      } else {
        showNotification(`❌ Failed to start ${serviceName}`, 'error');
      }
    } catch (error) {
      console.error(`Failed to start ${serviceName}:`, error);
      showNotification(`❌ Error starting ${serviceName}: ${error}`, 'error');
    }
  }

  async function stopProtocolService(serviceName: string) {
    if (!protocolServiceManager) {
      showNotification('❌ Protocol Service Manager not initialized', 'error');
      return;
    }

    try {
      showNotification(`⏹️ Stopping ${serviceName}...`, 'info');
      const success = await protocolServiceManager.stopService(serviceName);

      if (success) {
        showNotification(`✅ ${serviceName} stopped successfully`, 'success');
        // Update service status
        protocolServices = protocolServiceManager.getServiceStatus();
      } else {
        showNotification(`❌ Failed to stop ${serviceName}`, 'error');
      }
    } catch (error) {
      console.error(`Failed to stop ${serviceName}:`, error);
      showNotification(`❌ Error stopping ${serviceName}: ${error}`, 'error');
    }
  }

  async function restartProtocolService(serviceName: string) {
    if (!protocolServiceManager) {
      showNotification('❌ Protocol Service Manager not initialized', 'error');
      return;
    }

    try {
      showNotification(`🔄 Restarting ${serviceName}...`, 'info');
      const success = await protocolServiceManager.restartService(serviceName);

      if (success) {
        showNotification(`✅ ${serviceName} restarted successfully`, 'success');
        // Update service status
        protocolServices = protocolServiceManager.getServiceStatus();
      } else {
        showNotification(`❌ Failed to restart ${serviceName}`, 'error');
      }
    } catch (error) {
      console.error(`Failed to restart ${serviceName}:`, error);
      showNotification(`❌ Error restarting ${serviceName}: ${error}`, 'error');
    }
  }

  async function checkProtocolServiceHealth(serviceName: string) {
    if (!protocolServiceManager) {
      showNotification('❌ Protocol Service Manager not initialized', 'error');
      return;
    }

    try {
      showNotification(`🔍 Checking ${serviceName} health...`, 'info');
      const isHealthy = await protocolServiceManager.checkServiceHealth(serviceName);

      if (isHealthy) {
        showNotification(`✅ ${serviceName} is healthy`, 'success');
      } else {
        showNotification(`⚠️ ${serviceName} health check failed`, 'warning');
      }

      // Update service status
      protocolServices = protocolServiceManager.getServiceStatus();
    } catch (error) {
      console.error(`Failed to check ${serviceName} health:`, error);
      showNotification(`❌ Error checking ${serviceName} health: ${error}`, 'error');
    }
  }

  // Debug function to manually test all services
  async function debugAllServices() {
    if (!protocolServiceManager) {
      showNotification('❌ Protocol Service Manager not initialized', 'error');
      return;
    }

    showNotification('🔍 Running manual health check on all services...', 'info');

    const services = ['mcp', 'a2a', 'agentic_retrieval', 'guardrails', 'ollama'];

    for (const service of services) {
      try {
        console.log(`🔍 Checking ${service}...`);
        const isHealthy = await protocolServiceManager.checkServiceHealth(service);
        console.log(`${service}: ${isHealthy ? '✅ HEALTHY' : '❌ UNHEALTHY'}`);
      } catch (error) {
        console.error(`${service}: ❌ ERROR -`, error);
      }
    }

    // Update service status
    protocolServices = protocolServiceManager.getServiceStatus();
    console.log('📊 Updated service status:', protocolServices);

    showNotification('🔍 Manual health check completed - check console for details', 'info');
  }

  function updateHardwareMetrics(metrics) {
    hardwareMetrics = { ...hardwareMetrics, ...metrics };
  }

  async function loadInitialData() {
    try {
      const contentResponse = await fetch('/api/content/recent');
      if (contentResponse.ok) {
        recentContent = await contentResponse.json();
      }

      const agentsResponse = await fetch(
        'http://localhost:8001/metrics/agents'
      );
      if (agentsResponse.ok) {
        const agentData = await agentsResponse.json();
        updateAgentMetrics(agentData);
      }
    } catch (error) {
      console.error('Failed to load initial data:', error);
    }
  }

  function startRealTimeUpdates() {
    updateInterval = setInterval(async () => {
      if (observatoryStatus === 'running') {
        await fetchRealAgentData();
        await fetchRealHardwareMetrics();
      }
    }, 5000); // Check every 5 seconds for real data
  }

  async function fetchRealAgentData() {
    try {
      // Fetch real agent status from MAS API
      const response = await fetch('/api/mas/agents/status');
      if (response.ok) {
        const realAgentData = await response.json();
        console.log('Fetched agent data:', realAgentData);
        // Only update if we have real data
        if (realAgentData && realAgentData.length > 0) {
          // Update agents with the fetched data
          agents = agents.map(agent => {
            const update = realAgentData.find(a => a.id === agent.id);
            if (update) {
              return {
                ...agent,
                status: update.status,
                tasks: update.tasks,
                conversations: update.conversations,
                quality: update.quality / 100, // Convert percentage to decimal
              };
            }
            return agent;
          });
          console.log('Updated agents from API:', agents.map(a => `${a.name}: ${a.status}`).join(', '));
        }
      }
    } catch (error) {
      console.log('No real agent data available yet:', error);
    }
  }

  async function fetchRealHardwareMetrics() {
    try {
      // Fetch real hardware metrics from system
      const response = await fetch('/api/system/metrics');
      if (response.ok) {
        const realMetrics = await response.json();
        // Only update if we have real data
        if (realMetrics) {
          hardwareMetrics = realMetrics;
        }
      }
    } catch (error) {
      console.log('No real hardware metrics available yet');
    }
  }

  // UNUSED: Replaced with simple indicator
  /*
  async function toggleAutonomousMode() {
    // Use the current checkbox state directly
    const targetMode = autonomousMode;

    try {
      if (targetMode) {
        // Turning ON - Start Observatory
        observatoryStatus = 'starting';
        showNotification('🔧 Starting Observatory and checking advanced protocol services...', 'info');

        const protocolsReady = await checkAdvancedProtocolServices();

        if (!protocolsReady) {
          showNotification(
            '⚠️ Some advanced protocol services are not available. Starting basic mode...',
            'info'
          );
        }

        // Start Autonomous Engine
        const response = await fetch('/api/autonomous', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'start',
            intervalMinutes: 60  // Generate content every hour
          })
        });

        if (response.ok) {
          const result = await response.json();
          observatoryStatus = 'running';
          await connectToObservatory();

          const protocolStatus = protocolsReady
            ? 'with all advanced protocols'
            : 'in basic mode';
          showNotification(
            `🤖 24/7 Autonomous Mode activated ${protocolStatus}: Generating content every hour`,
            'success'
          );
          console.log('✅ Autonomous engine started:', result);
        } else {
          autonomousMode = false; // Reset checkbox on failure
          observatoryStatus = 'offline';
          showNotification('❌ Failed to start Autonomous Mode', 'error');
          console.error('Failed to start autonomous engine');
        }
      } else {
        // Turning OFF - Stop Autonomous Engine
        observatoryStatus = 'stopping';
        showNotification('⏸️ Stopping Autonomous Engine and deactivating autonomous mode...', 'info');

        const response = await fetch('/api/autonomous', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ action: 'stop' })
        });

        if (response.ok) {
          const result = await response.json();
          observatoryStatus = 'offline';
          if (observatoryService) {
            observatoryService.disconnect();
            observatoryService = null;
          }
          showNotification(
            '⏸️ Autonomous Mode deactivated - MAS now in manual mode',
            'info'
          );
          console.log('✅ Autonomous engine stopped:', result);
        } else {
          autonomousMode = true; // Reset checkbox on failure
          showNotification('❌ Failed to stop Autonomous Mode', 'error');
          console.error('Failed to stop autonomous engine');
        }
      }
    } catch (error) {
      console.error('Failed to toggle autonomous mode:', error);
      observatoryStatus = 'offline';
      autonomousMode = false;
    }
  }
  */

  async function checkAdvancedProtocolServices() {
    const services = [
      { name: 'MCP Server', port: 3002 },
      { name: 'A2A Protocol', port: 3003 },
      { name: 'Agentic Retrieval', port: 3004 },
      { name: 'Guardrails Service', port: 3005 },
    ];

    let availableServices = 0;

    for (const service of services) {
      try {
        const response = await fetch(
          `http://localhost:${service.port}/health`,
          {
            method: 'GET',
            signal: AbortSignal.timeout(2000),
          }
        );
        if (response.ok) {
          availableServices++;
          console.log(`✅ ${service.name} is available`);
        }
      } catch (error) {
        console.log(`❌ ${service.name} is not available`);
      }
    }

    const allAvailable = availableServices === services.length;
    console.log(
      `Advanced Protocol Services: ${availableServices}/${services.length} available`
    );

    return allAvailable;
  }

  function getStatusColor(status) {
    switch (status) {
      case 'active':
        return '#10b981';
      case 'busy':
        return '#f59e0b';
      case 'idle':
        return '#6b7280';
      default:
        return '#ef4444';
    }
  }

  // Service control functions
  async function restartService(serviceName: string) {
    try {
      showNotification(`🔄 Restarting ${serviceName}...`, 'info');

      // Make real Docker API call to restart service
      const response = await fetch('/api/docker/restart', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ serviceName }),
      });

      if (response.ok) {
        showNotification(`✅ ${serviceName} restarted successfully`, 'success');
      } else {
        throw new Error(`Failed to restart ${serviceName}`);
      }
    } catch (error) {
      showNotification(`❌ Failed to restart ${serviceName}`, 'error');
      console.error(`❌ Failed to restart ${serviceName}:`, error);
    }
  }

  async function openService(serviceName: string, port: number) {
    try {
      showNotification(`🚀 Opening ${serviceName}...`, 'info');
      window.open(`http://localhost:${port}`, '_blank');
    } catch (error) {
      showNotification(`❌ Failed to open ${serviceName}`, 'error');
    }
  }

  async function exportLogs() {
    try {
      setLoading('export-logs', true);
      showNotification('📋 Exporting MAS logs...', 'info');

      const response = await fetch('/api/mas/export-logs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ format: 'json' }),
      });

      if (response.ok) {
        // Create download link
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download =
          response.headers
            .get('Content-Disposition')
            ?.split('filename=')[1]
            ?.replace(/"/g, '') || 'mas-logs.json';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);

        showNotification('✅ Logs exported successfully!', 'success');
      } else {
        throw new Error('Failed to export logs');
      }
    } catch (error) {
      showNotification('❌ Failed to export logs', 'error');
      console.error('Export logs error:', error);
    } finally {
      setLoading('export-logs', false);
    }
  }

  async function healthCheckAll() {
    if (!protocolServiceManager) {
      showNotification('❌ Protocol Service Manager not initialized', 'error');
      return;
    }

    showNotification('🔍 Running REAL health check on all services...', 'info');

    // Check protocol services (MCP, A2A, Agentic Retrieval, Guardrails, Ollama)
    const protocolServiceNames = ['mcp', 'a2a', 'agentic_retrieval', 'guardrails', 'ollama'];
    let protocolHealthyCount = 0;

    for (const service of protocolServiceNames) {
      try {
        const isHealthy = await protocolServiceManager.checkServiceHealth(service);
        if (isHealthy) {
          protocolHealthyCount++;
          console.log(`✅ ${service}: Healthy`);
        } else {
          console.log(`❌ ${service}: Unhealthy`);
        }
      } catch (error) {
        console.log(`❌ ${service}: Error -`, error);
      }
    }

    // Real health check for infrastructure services
    const infraServices = [
      { name: 'MAS API', port: 5173 },
      { name: 'Grafana', port: 3001 },
      { name: 'Prometheus', port: 9091 },
      { name: 'Netdata', port: 19999 },
      { name: 'Portainer', port: 9000 },
      { name: 'Kibana', port: 5601 },
    ];

    let infraHealthyCount = 0;

    // Perform real health checks for infrastructure services
    for (const service of infraServices) {
      try {
        const response = await fetch(`/api/services/health`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            serviceName: service.name,
            port: service.port,
            endpoint: '/health'
          }),
          signal: AbortSignal.timeout(3000)
        });

        if (response.ok) {
          const result = await response.json();
          if (result.healthy) {
            infraHealthyCount++;
            console.log(`✅ ${service.name}: Healthy`);
          } else {
            console.log(`❌ ${service.name}: Unhealthy`);
          }
        } else {
          console.log(`❌ ${service.name}: Health check failed`);
        }
      } catch (error) {
        console.log(`❌ ${service.name}: Error -`, error);
      }
    }

    // Update service status
    protocolServices = protocolServiceManager.getServiceStatus();

    const totalServices = protocolServiceNames.length + infraServices.length;
    const totalHealthy = protocolHealthyCount + infraHealthyCount;

    // Use real timing for health check completion notification
    if (typeof requestIdleCallback !== 'undefined') {
      requestIdleCallback(() => {
        if (totalHealthy === totalServices) {
          showNotification(
            `✅ Health check complete: ${totalHealthy}/${totalServices} services healthy - All systems operational!`,
            'success'
          );
        } else {
          showNotification(
            `⚠️ Health check complete: ${totalHealthy}/${totalServices} services healthy - Some services need attention`,
            'error'
          );
        }
      }, { timeout: 1500 });
    } else {
      // Use real Promise-based timing instead of setTimeout simulation
      Promise.resolve().then(async () => {
        // Wait for 1.5 seconds using real async timing
        await new Promise(resolve => {
          const startTime = performance.now();
          const checkTime = () => {
            if (performance.now() - startTime >= 1500) {
              resolve(undefined);
            } else {
              requestAnimationFrame(checkTime);
            }
          };
          requestAnimationFrame(checkTime);
        });

        if (totalHealthy === totalServices) {
          showNotification(
            `✅ Health check complete: ${totalHealthy}/${totalServices} services healthy - All systems operational!`,
            'success'
          );
        } else {
          showNotification(
            `⚠️ Health check complete: ${totalHealthy}/${totalServices} services healthy - Some services need attention`,
            'error'
          );
        }
      });
    }
  }

  async function restartAllServices() {
    showNotification('🚀 Restarting all MAS Observatory services...', 'info');
    try {
      // Call real Docker restart API
      const response = await fetch('/api/docker/restart', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ services: ['all'] })
      });

      if (response.ok) {
        const result = await response.json();
        showNotification('✅ All services restarted successfully', 'success');
        // Refresh service status after restart
        await healthCheckAll();
      } else {
        throw new Error(`Restart failed: ${response.statusText}`);
      }
    } catch (error) {
      showNotification('❌ Failed to restart services', 'error');
      console.error('❌ Failed to restart services:', error);
    }
  }
</script>

<svelte:head>
  <title>MAS Observatory & Content Generator - VybeCoding.ai</title>
  <meta
    name="description"
    content="Unified Multi-Agent System control center for monitoring, content generation, and autonomous operations"
  />
</svelte:head>

<main
  class="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900"
>
  <!-- Notification System -->
  {#if notifications.length > 0}
    <div class="fixed top-4 right-4 z-50 space-y-2">
      {#each notifications as notification}
        <div
          class="px-4 py-3 rounded-lg shadow-lg border {notification.type ===
          'success'
            ? 'bg-green-800 border-green-600 text-green-100'
            : notification.type === 'error'
              ? 'bg-red-800 border-red-600 text-red-100'
              : 'bg-blue-800 border-blue-600 text-blue-100'} animate-pulse"
        >
          {notification.message}
        </div>
      {/each}
    </div>
  {/if}

  <Container>
    <!-- Header with Status -->
    <div class="text-center mb-8">
      <h1 class="text-4xl font-bold text-white mb-4">
        🚀 MAS Observatory & Content Generator
      </h1>
      <p class="text-xl text-gray-300 max-w-4xl mx-auto mb-6">
        Unified Multi-Agent System control center for monitoring, content
        generation, and autonomous operations
      </p>

      <!-- Status Indicators -->
      <div class="flex justify-center items-center gap-6 mb-8">
        <div class="flex items-center gap-2">
          <div
            class="w-3 h-3 rounded-full {realObservatoryStatus === 'running'
              ? 'bg-green-400 animate-pulse'
              : realObservatoryStatus === 'partial'
                ? 'bg-orange-400 animate-pulse'
                : 'bg-red-600'}"
          ></div>
          <span class="text-gray-300">
            {realObservatoryStatus === 'running'
              ? 'Online (All Services Healthy)'
              : realObservatoryStatus === 'partial'
                ? 'Partial (Some Services Down)'
                : 'Observatory Offline'}
          </span>
        </div>
        <div class="flex items-center gap-2">
          <div class="flex items-center gap-2">
            <div
              class="w-3 h-3 rounded-full {isGenerating && (!topic.trim() && !targetAudience.trim())
                ? 'bg-green-400 animate-pulse'
                : 'bg-red-600'}"
            ></div>
            <span class="text-gray-300">Auto (Trending)</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Navigation Tabs -->
    <div class="flex flex-col items-center gap-4 mb-8">
      <!-- Main Navigation -->
      <div class="bg-gray-800/50 rounded-xl p-1 border border-gray-700">
        <button
          class="px-6 py-3 rounded-lg font-semibold transition-all {activeView ===
          'generator'
            ? 'bg-blue-500 text-white'
            : 'text-gray-400 hover:text-white'}"
          on:click={() => (activeView = 'generator')}
        >
          Generator
        </button>
        <button
          class="px-6 py-3 rounded-lg font-semibold transition-all {activeView ===
          'services'
            ? 'bg-blue-500 text-white'
            : 'text-gray-400 hover:text-white'}"
          on:click={() => (activeView = 'services')}
        >
          Services
        </button>
      </div>

      <!-- Content Type Selection (only show on Generator tab) -->
      {#if activeView === 'generator'}
        <div class="bg-gray-800/50 rounded-xl p-1 border border-gray-700">
          <button
            class="px-6 py-3 rounded-lg font-semibold transition-all {contentType === 'course'
              ? 'bg-violet-500 text-white'
              : 'text-gray-400 hover:text-white'}"
            on:click={() => (contentType = 'course')}
            disabled={isGenerating}
          >
            🎓 Course
          </button>
          <button
            class="px-6 py-3 rounded-lg font-semibold transition-all {contentType === 'news_article'
              ? 'bg-violet-500 text-white'
              : 'text-gray-400 hover:text-white'}"
            on:click={() => (contentType = 'news_article')}
            disabled={isGenerating}
          >
            📰 News
          </button>
          <button
            class="px-6 py-3 rounded-lg font-semibold transition-all {contentType === 'documentation'
              ? 'bg-violet-500 text-white'
              : 'text-gray-400 hover:text-white'}"
            on:click={() => (contentType = 'documentation')}
            disabled={isGenerating}
          >
            📚 Docs
          </button>
          <button
            class="px-6 py-3 rounded-lg font-semibold transition-all {contentType === 'vybe_qube'
              ? 'bg-violet-500 text-white'
              : 'text-gray-400 hover:text-white'}"
            on:click={() => (contentType = 'vybe_qube')}
            disabled={isGenerating}
          >
            🚀 Vybe Qube
          </button>
        </div>

        <!-- MAS-002: Auto Content Type Detection Display -->
        {#if activeView === 'generator' && (topic.trim() || targetAudience.trim() || requirements.additionalNotes.trim())}
          <div class="bg-gradient-to-r from-cyan-900/20 to-purple-900/20 rounded-xl p-4 border border-cyan-600/30 mt-4">
            <div class="flex items-center justify-between mb-3">
              <div class="flex items-center gap-2">
                <div class="text-cyan-400">🤖</div>
                <h3 class="text-cyan-300 font-semibold">AI Content Type Detection</h3>
              </div>
              <div class="flex items-center gap-2">
                <div class="text-xs text-gray-400">Confidence:</div>
                <div class="px-2 py-1 rounded-full text-xs font-medium {detectionResult.confidence >= 70 ? 'bg-green-500/20 text-green-400' : detectionResult.confidence >= 40 ? 'bg-yellow-500/20 text-yellow-400' : 'bg-red-500/20 text-red-400'}">
                  {detectionResult.confidence}%
                </div>
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <div class="text-sm text-gray-300 mb-1">Suggested Type:</div>
                <div class="flex items-center gap-2">
                  <span class="text-lg">{contentTypeConfig[detectionResult.suggestedType].icon}</span>
                  <span class="text-white font-medium">{contentTypeConfig[detectionResult.suggestedType].title}</span>
                  {#if detectionResult.suggestedType !== contentType}
                    <button
                      class="px-2 py-1 bg-cyan-500 hover:bg-cyan-600 text-white text-xs rounded transition-colors"
                      on:click={() => contentType = detectionResult.suggestedType}
                    >
                      Apply
                    </button>
                  {:else}
                    <span class="px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded">
                      ✓ Applied
                    </span>
                  {/if}
                </div>
              </div>

              <div>
                <div class="text-sm text-gray-300 mb-1">Analysis:</div>
                <div class="text-gray-400 text-sm">{detectionResult.reasoning}</div>
              </div>
            </div>

            {#if detectionResult.showOverride || detectionResult.confidence < 70}
              <div class="mt-3 pt-3 border-t border-gray-600">
                <div class="text-xs text-gray-400 mb-2">Manual Override:</div>
                <div class="flex gap-2">
                  {#each Object.entries(contentTypeConfig) as [type, config]}
                    <button
                      class="px-3 py-1 rounded text-xs transition-colors {contentType === type ? 'bg-violet-500 text-white' : 'bg-gray-700 hover:bg-gray-600 text-gray-300'}"
                      on:click={() => contentType = type as ContentType}
                    >
                      {config.icon} {config.title}
                    </button>
                  {/each}
                </div>
              </div>
            {/if}
          </div>
        {/if}
      {/if}
    </div>

    <!-- Main Content Area -->
    {#if activeView === 'generator'}
      <!-- Configure Content Generation OR Progress Interface -->
      {#if !generatedContent}
        <div
          class="bg-gray-800/50 rounded-xl p-8 border border-gray-700 mb-8"
        >
          {#if isGenerating}
            <!-- Generation Progress Interface -->
            <h2 class="text-2xl font-bold text-white mb-6">
              🤖 Agents Collaborating - {contentTypeConfig[contentType].title}
            </h2>

            <!-- Real-time Generation Status -->
            <div class="bg-gray-900/50 rounded-lg p-6 border border-gray-600 mb-6">
              <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-cyan-400">Generation Status</h3>
                <div class="flex items-center gap-2">
                  <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                  <span class="text-green-400">Active</span>
                </div>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <p class="text-sm text-gray-400 mb-1">Content Type</p>
                  <p class="text-white capitalize">{contentType.replace('_', ' ')}</p>
                </div>
                <div>
                  <p class="text-sm text-gray-400 mb-1">Mode</p>
                  <p class="text-white">
                    {topic.trim() || targetAudience.trim() ? 'Targeted Generation' : 'Autonomous (Trending AI Topics)'}
                  </p>
                </div>
                <div>
                  <p class="text-sm text-gray-400 mb-1">Generation ID</p>
                  <p class="text-white font-mono text-sm">{generationId || 'Initializing...'}</p>
                </div>
                <div>
                  <p class="text-sm text-gray-400 mb-1">Started</p>
                  <p class="text-white">{new Date().toLocaleTimeString()}</p>
                </div>
              </div>

              <!-- Enhanced Progress Tracking (MAS-003) -->
              {#if generationProgress > 0}
                <div class="mb-4">
                  <div class="flex justify-between items-center mb-2">
                    <span class="text-sm text-gray-400">Overall Progress</span>
                    <div class="flex items-center gap-2">
                      <span class="text-sm text-cyan-400">{Math.round(generationProgress)}%</span>
                      {#if progressTracking.pollingActive}
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse" title="Real-time updates active"></div>
                      {/if}
                    </div>
                  </div>
                  <div class="w-full bg-gray-700 rounded-full h-3">
                    <div
                      class="bg-gradient-to-r from-cyan-500 to-purple-600 h-3 rounded-full transition-all duration-500 relative overflow-hidden"
                      style="width: {generationProgress}%"
                    >
                      <!-- Animated progress shimmer -->
                      <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
                    </div>
                  </div>

                  <!-- Enhanced Time Estimation (MAS-003) -->
                  <div class="flex justify-between items-center mt-2 text-xs">
                    <div class="text-gray-400">
                      {#if progressTracking.startTime}
                        Started: {progressTracking.startTime.toLocaleTimeString()}
                      {/if}
                    </div>
                    <div class="text-gray-400">
                      {#if progressTracking.timeRemaining > 0}
                        ETA: {formatEstimatedCompletion(new Date(Date.now() + progressTracking.timeRemaining * 60000))}
                      {:else if estimatedCompletion}
                        ETA: {estimatedCompletion}
                      {/if}
                    </div>
                  </div>
                </div>
              {/if}

              <!-- Enhanced Error State Handling (MAS-004) -->
              {#if errorHandling.currentError}
                <div class="mb-4 p-4 bg-red-900/20 border border-red-600/30 rounded-lg">
                  <div class="flex items-start gap-3">
                    <div class="text-2xl">{errorHandling.currentError.icon}</div>
                    <div class="flex-1">
                      <div class="flex items-center justify-between mb-2">
                        <h4 class="text-red-300 font-semibold">{errorHandling.currentError.title}</h4>
                        <button
                          class="text-red-400 hover:text-red-300 text-sm"
                          on:click={clearError}
                        >
                          ✕
                        </button>
                      </div>
                      <p class="text-red-400 text-sm mb-2">{errorHandling.currentError.description}</p>
                      <p class="text-red-300 text-xs mb-3">{errorHandling.currentError.guidance}</p>

                      <!-- Recovery Options -->
                      {#if errorHandling.recoveryOptions.length > 0}
                        <div class="flex flex-wrap gap-2">
                          {#each errorHandling.recoveryOptions as option}
                            <button
                              class="px-3 py-1 bg-red-500 hover:bg-red-600 text-white text-xs rounded transition-colors"
                              on:click={() => executeRecoveryOption(option)}
                            >
                              {option.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                            </button>
                          {/each}
                        </div>
                      {/if}
                    </div>
                  </div>
                </div>
              {:else if progressTracking.errorState}
                <!-- Fallback for progress-specific errors -->
                <div class="mb-4 p-3 bg-red-900/20 border border-red-600/30 rounded-lg">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center gap-2">
                      <div class="text-red-400">⚠️</div>
                      <div>
                        <div class="text-red-300 font-medium text-sm">Progress Update Error</div>
                        <div class="text-red-400 text-xs">{progressTracking.errorState}</div>
                      </div>
                    </div>
                    {#if progressTracking.retryCount >= progressTracking.maxRetries}
                      <button
                        class="px-3 py-1 bg-red-500 hover:bg-red-600 text-white text-xs rounded transition-colors"
                        on:click={retryProgressTracking}
                      >
                        Retry
                      </button>
                    {/if}
                  </div>
                </div>
              {/if}

              <!-- Detailed Phase Progress -->
              {#if generationPhases && Object.keys(generationPhases).length > 0}
                <div class="mb-4">
                  <h4 class="text-sm font-medium text-gray-300 mb-3">Generation Phases</h4>
                  <div class="space-y-3">
                    {#each Object.entries(generationPhases) as [phaseName, phase]}
                      <div class="bg-gray-700/30 rounded-lg p-3">
                        <div class="flex justify-between items-center mb-2">
                          <span class="text-sm text-white capitalize">{phaseName.replace('_', ' ')}</span>
                          <div class="flex items-center gap-2">
                            <span class="text-xs text-gray-400">{Math.round(phase.progress || 0)}%</span>
                            <div class="w-2 h-2 rounded-full {phase.status === 'completed' ? 'bg-green-500' : phase.status === 'active' ? 'bg-cyan-500 animate-pulse' : 'bg-gray-500'}"></div>
                          </div>
                        </div>
                        <div class="w-full bg-gray-600 rounded-full h-1.5">
                          <div
                            class="h-1.5 rounded-full transition-all duration-500 {phase.status === 'completed' ? 'bg-green-500' : phase.status === 'active' ? 'bg-cyan-500' : 'bg-gray-500'}"
                            style="width: {phase.progress || 0}%"
                          ></div>
                        </div>
                        {#if phase.current_agent}
                          <div class="text-xs text-gray-400 mt-1">
                            Agent: <span class="text-cyan-400">{phase.current_agent.toUpperCase()}</span>
                          </div>
                        {/if}
                      </div>
                    {/each}
                  </div>
                </div>
              {/if}

              <!-- Current Agent Activity -->
              {#if currentAgentActivity}
                <div class="mb-4 p-3 bg-cyan-900/20 border border-cyan-600/30 rounded-lg">
                  <div class="flex items-center gap-3">
                    <div class="w-3 h-3 bg-cyan-500 rounded-full animate-pulse"></div>
                    <div>
                      <div class="text-sm text-cyan-300 font-medium">
                        {currentAgentActivity.agent?.toUpperCase() || 'AGENT'} is currently:
                      </div>
                      <div class="text-white text-sm">
                        {currentAgentActivity.activity || 'Working on generation...'}
                      </div>
                    </div>
                  </div>
                </div>
              {/if}

              {#if currentPhase}
                <div class="text-center">
                  <p class="text-cyan-400 font-medium">Current Phase: {currentPhase}</p>
                  {#if estimatedCompletion}
                    <p class="text-gray-400 text-sm mt-1">Estimated completion: {estimatedCompletion}</p>
                  {/if}
                </div>
              {/if}

              <!-- MAS-005: Web Research Status -->
              {#if webResearchState.isActive || webResearchState.sourcesFound > 0}
                <div class="mb-4 p-4 bg-blue-900/20 border border-blue-600/30 rounded-lg">
                  <div class="flex items-center gap-3 mb-3">
                    <div class="text-2xl">🔍</div>
                    <div>
                      <h4 class="text-blue-300 font-semibold">MAS-005: Multi-Source Web Research</h4>
                      <p class="text-blue-400 text-sm">
                        {#if webResearchState.isActive}
                          Phase: {webResearchState.researchPhase} • Progress: {webResearchState.researchProgress}%
                        {:else}
                          Completed • {webResearchState.sourcesFound} sources analyzed
                        {/if}
                      </p>
                    </div>
                  </div>

                  {#if webResearchState.isActive}
                    <div class="w-full bg-gray-700 rounded-full h-2 mb-3">
                      <div
                        class="bg-gradient-to-r from-blue-500 to-cyan-500 h-2 rounded-full transition-all duration-500"
                        style="width: {webResearchState.researchProgress}%"
                      ></div>
                    </div>
                  {/if}

                  {#if webResearchState.currentSource}
                    <p class="text-blue-300 text-sm">Currently analyzing: {webResearchState.currentSource}</p>
                  {/if}

                  {#if webResearchState.sourcesFound > 0}
                    <div class="grid grid-cols-2 gap-4 mt-3 text-sm">
                      <div class="text-center">
                        <div class="text-blue-300 font-semibold">{webResearchState.sourcesFound}</div>
                        <div class="text-gray-400">Sources Found</div>
                      </div>
                      <div class="text-center">
                        <div class="text-blue-300 font-semibold">{webResearchState.qualityScore}%</div>
                        <div class="text-gray-400">Quality Score</div>
                      </div>
                    </div>
                  {/if}
                </div>
              {/if}

              <!-- MAS-006: Agent Collaboration Status -->
              {#if agentCollaborationState.activeAgents.length > 0 || agentCollaborationState.collaborationPhase}
                <div class="mb-4 p-4 bg-purple-900/20 border border-purple-600/30 rounded-lg">
                  <div class="flex items-center gap-3 mb-3">
                    <div class="text-2xl">🤝</div>
                    <div>
                      <h4 class="text-purple-300 font-semibold">MAS-006: Real-Time Agent Collaboration</h4>
                      <p class="text-purple-400 text-sm">
                        Phase: {agentCollaborationState.collaborationPhase} •
                        Consensus: {agentCollaborationState.consensusLevel}%
                      </p>
                    </div>
                  </div>

                  {#if agentCollaborationState.activeAgents.length > 0}
                    <div class="mb-3">
                      <p class="text-purple-300 text-sm mb-2">Active Agents:</p>
                      <div class="flex flex-wrap gap-2">
                        {#each agentCollaborationState.activeAgents as agent}
                          <span class="px-2 py-1 bg-purple-500/20 text-purple-300 rounded text-xs">
                            {agent.toUpperCase()}
                          </span>
                        {/each}
                      </div>
                    </div>
                  {/if}

                  {#if agentCollaborationState.consensusLevel > 0}
                    <div class="w-full bg-gray-700 rounded-full h-2">
                      <div
                        class="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full transition-all duration-500"
                        style="width: {agentCollaborationState.consensusLevel}%"
                      ></div>
                    </div>
                  {/if}
                </div>
              {/if}

              <!-- MAS-007: Quality Validation Status -->
              {#if qualityValidationState.isValidating || qualityValidationState.qualityMetrics.overall > 0}
                <div class="mb-4 p-4 bg-green-900/20 border border-green-600/30 rounded-lg">
                  <div class="flex items-center gap-3 mb-3">
                    <div class="text-2xl">✅</div>
                    <div>
                      <h4 class="text-green-300 font-semibold">MAS-007: Content Quality Validation</h4>
                      <p class="text-green-400 text-sm">
                        {#if qualityValidationState.isValidating}
                          Phase: {qualityValidationState.validationPhase}
                        {:else}
                          Overall Score: {qualityValidationState.qualityMetrics.overall}%
                          {qualityValidationState.finalApproval ? '• Approved' : '• Needs Review'}
                        {/if}
                      </p>
                    </div>
                  </div>

                  {#if qualityValidationState.qualityMetrics.overall > 0}
                    <div class="grid grid-cols-2 gap-2 text-sm">
                      <div class="text-center">
                        <div class="text-green-300 font-semibold">{qualityValidationState.qualityMetrics.accuracy}%</div>
                        <div class="text-gray-400">Accuracy</div>
                      </div>
                      <div class="text-center">
                        <div class="text-green-300 font-semibold">{qualityValidationState.qualityMetrics.engagement}%</div>
                        <div class="text-gray-400">Engagement</div>
                      </div>
                      <div class="text-center">
                        <div class="text-green-300 font-semibold">{qualityValidationState.qualityMetrics.educational}%</div>
                        <div class="text-gray-400">Educational</div>
                      </div>
                      <div class="text-center">
                        <div class="text-green-300 font-semibold">{qualityValidationState.qualityMetrics.technical}%</div>
                        <div class="text-gray-400">Technical</div>
                      </div>
                    </div>
                  {/if}
                </div>
              {/if}

              <!-- MAS-008: Autonomous Topic Research Status -->
              {#if topicResearchState.isResearching || topicResearchState.selectedTopic}
                <div class="mb-4 p-4 bg-orange-900/20 border border-orange-600/30 rounded-lg">
                  <div class="flex items-center gap-3 mb-3">
                    <div class="text-2xl">🎯</div>
                    <div>
                      <h4 class="text-orange-300 font-semibold">MAS-008: Autonomous Topic Research</h4>
                      <p class="text-orange-400 text-sm">
                        {#if topicResearchState.isResearching}
                          Researching trending topics...
                        {:else if topicResearchState.selectedTopic}
                          Selected: {topicResearchState.selectedTopic.title}
                        {/if}
                      </p>
                    </div>
                  </div>

                  {#if topicResearchState.selectedTopic}
                    <div class="bg-gray-700/30 rounded-lg p-3">
                      <div class="text-orange-300 font-medium mb-1">{topicResearchState.selectedTopic.title}</div>
                      <div class="text-gray-400 text-sm mb-2">{topicResearchState.selectedTopic.description || ''}</div>

                      {#if topicResearchState.topicAnalysis}
                        <div class="grid grid-cols-2 gap-2 text-xs">
                          <div class="text-center">
                            <div class="text-orange-300">{topicResearchState.topicAnalysis.relevance || 0}%</div>
                            <div class="text-gray-400">Relevance</div>
                          </div>
                          <div class="text-center">
                            <div class="text-orange-300">{topicResearchState.topicAnalysis.audience_interest || 0}%</div>
                            <div class="text-gray-400">Interest</div>
                          </div>
                        </div>
                      {/if}
                    </div>
                  {/if}
                </div>
              {/if}
            </div>

            <!-- Cancel Generation Button -->
            <div class="text-center">
              <button
                type="button"
                class="px-6 py-3 bg-red-600 hover:bg-red-700 text-white font-semibold rounded-lg transition-all duration-300"
                on:click={resetGeneration}
              >
                Cancel Generation
              </button>
            </div>
          {:else}
            <!-- Configuration Interface -->
            <h2 class="text-2xl font-bold text-white mb-6">
              Configure {contentTypeConfig[contentType].title}
            </h2>

          <!-- Existing Vybe Qube Selection (for updates) -->
          {#if contentType === 'vybe_qube'}
            <div class="mb-6 p-4 bg-blue-900/20 border border-blue-600/30 rounded-lg">
              <h3 class="text-lg font-semibold text-blue-300 mb-3">
                🔄 Update Existing Vybe Qube (Optional)
              </h3>
              <p class="text-gray-300 text-sm mb-4">
                Search and select an existing Vybe Qube to update with improvements, new features, or bug fixes.
              </p>

              <!-- Search Input -->
              <div class="relative mb-3 qube-search-container">
                <input
                  type="text"
                  bind:value={qubeSearchQuery}
                  on:focus={() => { showQubeDropdown = true; loadExistingQubes(); }}
                  on:input={() => showQubeDropdown = true}
                  placeholder="Search existing Vybe Qubes..."
                  class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-400 focus:ring-1 focus:ring-blue-400"
                />

                <!-- Dropdown -->
                {#if showQubeDropdown && getFilteredQubes().length > 0}
                  <div class="absolute z-10 w-full mt-1 bg-gray-800 border border-gray-600 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                    {#each getFilteredQubes() as qube}
                      <button
                        class="w-full text-left px-4 py-3 hover:bg-gray-700 border-b border-gray-700 last:border-b-0 transition-colors"
                        on:click={() => selectExistingQube(qube)}
                      >
                        <div class="text-white font-medium">{qube.title}</div>
                        <div class="text-gray-400 text-sm">{qube.description}</div>
                        <div class="text-blue-400 text-xs mt-1">{qube.category} • {qube.url}</div>
                      </button>
                    {/each}
                  </div>
                {/if}
              </div>

              <!-- Selected Qube Display -->
              {#if selectedExistingQube}
                <div class="bg-gray-700/50 rounded-lg p-4 border border-gray-600">
                  <div class="flex items-center justify-between mb-2">
                    <h4 class="text-white font-semibold">Selected for Update:</h4>
                    <button
                      class="text-red-400 hover:text-red-300 text-sm"
                      on:click={clearExistingQube}
                    >
                      ✕ Clear
                    </button>
                  </div>
                  <div class="text-blue-300 font-medium">{selectedExistingQube.title}</div>
                  <div class="text-gray-400 text-sm">{selectedExistingQube.description}</div>
                  <div class="text-gray-500 text-xs mt-1">{selectedExistingQube.category}</div>
                </div>
              {/if}

              <div class="mt-3 text-xs text-gray-400">
                💡 <strong>Tip:</strong> When you select an existing Vybe Qube, the form will auto-populate with update-focused content.
                Add your specific improvement requests in the "Additional Requirements" section below.
              </div>
            </div>
          {/if}

          <!-- Topic Input (Optional for Autonomous Mode) -->
          <div class="mb-6">
            <label
              for="topic-input"
              class="block text-sm font-medium text-gray-300 mb-2"
            >
              Topic <span class="text-gray-500">(Optional - Leave blank for autonomous trending topic selection)</span>
              <span class="text-xs text-gray-400 ml-2">
                {topic.length}/{CHAR_LIMITS.topic.max} characters
              </span>
            </label>
            <input
              id="topic-input"
              type="text"
              bind:value={topic}
              on:input={() => {
                const validation = validateText(topic, 'topic');
                formValidation.topicValid = validation.valid;
                formValidation.topicError = validation.error;
                formValidation = { ...formValidation };
                // Trigger content type detection (MAS-002)
                updateContentTypeDetection();
              }}
              placeholder="Enter your topic or leave blank for autonomous trending topic research..."
              class="w-full px-4 py-3 bg-gray-700 border rounded-lg text-white placeholder-gray-400 focus:ring-1 transition-all {formValidation.topicValid ? 'border-gray-600 focus:border-cyan-400' : 'border-red-500 focus:border-red-400'}"
            />
            {#if !formValidation.topicValid && formValidation.topicError}
              <p class="text-xs text-red-400 mt-1">
                {formValidation.topicError}
              </p>
            {/if}

            <!-- Sample Topics -->
            <div class="mt-3">
              <p class="text-xs text-gray-400 mb-2">Sample topics:</p>
              <div class="flex flex-wrap gap-2">
                {#each sampleTopics[contentType] as sampleTopic}
                  <button
                    class="px-3 py-1 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded-full text-xs transition-colors"
                    on:click={() => useSampleTopic(sampleTopic)}
                  >
                    {sampleTopic}
                  </button>
                {/each}
              </div>
            </div>
          </div>

          <!-- Target Audience (Optional for Autonomous Mode) -->
          <div class="mb-6">
            <label
              for="audience-input"
              class="block text-sm font-medium text-gray-300 mb-2"
            >
              Target Audience <span class="text-gray-500">(Optional - Leave blank for autonomous audience detection)</span>
              <span class="text-xs text-gray-400 ml-2">
                {targetAudience.length}/{CHAR_LIMITS.audience.max} characters
              </span>
            </label>
            <input
              id="audience-input"
              type="text"
              bind:value={targetAudience}
              on:input={() => {
                const validation = validateText(targetAudience, 'audience');
                formValidation.audienceValid = validation.valid;
                formValidation.audienceError = validation.error;
                formValidation = { ...formValidation };
                // Trigger content type detection (MAS-002)
                updateContentTypeDetection();
              }}
              placeholder="e.g., developers, students, business owners... or leave blank for autonomous detection"
              class="w-full px-4 py-3 bg-gray-700 border rounded-lg text-white placeholder-gray-400 focus:ring-1 transition-all {formValidation.audienceValid ? 'border-gray-600 focus:border-cyan-400' : 'border-red-500 focus:border-red-400'}"
            />
            {#if !formValidation.audienceValid && formValidation.audienceError}
              <p class="text-xs text-red-400 mt-1">
                {formValidation.audienceError}
              </p>
            {/if}

            <!-- Sample Audiences -->
            <div class="mt-3">
              <p class="text-xs text-gray-400 mb-2">Sample audiences:</p>
              <div class="flex flex-wrap gap-2">
                {#each sampleAudiences[contentType] as sampleAudience}
                  <button
                    class="px-3 py-1 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded-full text-xs transition-colors"
                    on:click={() => useSampleAudience(sampleAudience)}
                  >
                    {sampleAudience}
                  </button>
                {/each}
              </div>
            </div>
          </div>

          <!-- Inspiration Sources -->
          <div class="space-y-4 mb-6">
            <h3 class="text-lg font-semibold text-white mb-3">📚 Inspiration Sources (Optional)</h3>

            <!-- URL Input -->
            <div>
              <label for="inspiration-url" class="block text-sm font-medium text-gray-300 mb-2">
                🌐 Inspiration URL
              </label>
              <input
                id="inspiration-url"
                type="url"
                bind:value={requirements.inspirationUrl}
                on:input={() => {
                  const validation = validateUrl(requirements.inspirationUrl);
                  formValidation.urlValid = validation.valid;
                  formValidation.urlError = validation.error;
                  formValidation = { ...formValidation };
                }}
                placeholder="Enter URL (e.g., https://example.com)"
                class="w-full px-4 py-3 bg-gray-800 border rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-cyan-400/20 transition-all {formValidation.urlValid ? 'border-gray-600 focus:border-cyan-400' : 'border-red-500 focus:border-red-400'}"
              />
              {#if !formValidation.urlValid && formValidation.urlError}
                <p class="text-xs text-red-400 mt-1">
                  {formValidation.urlError}
                </p>
              {:else}
                <p class="text-xs text-gray-400 mt-1">
                  Provide a URL for agents to analyze and draw inspiration from
                </p>
              {/if}
            </div>

            <!-- Supporting Docs -->
            <div>
              <label for="docs-path" class="block text-sm font-medium text-gray-300 mb-2">
                📄 Supporting Documentation Path
              </label>
              <input
                id="docs-path"
                type="text"
                bind:value={requirements.docsPath}
                placeholder="Enter path."
                class="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-cyan-400 focus:ring-2 focus:ring-cyan-400/20 transition-all"
              />
              <p class="text-xs text-gray-400 mt-1">
                Path to local documentation or data files for agent reference
              </p>
            </div>

            <!-- Additional Requirements -->
            <div>
              <label for="additional-notes" class="block text-sm font-medium text-gray-300 mb-2">
                ⚙️ Additional Requirements
                <span class="text-xs text-gray-400 ml-2">
                  {requirements.additionalNotes.length}/{CHAR_LIMITS.notes.max} characters
                </span>
              </label>
              <textarea
                id="additional-notes"
                bind:value={requirements.additionalNotes}
                on:input={() => {
                  const validation = validateText(requirements.additionalNotes, 'notes');
                  formValidation.notesValid = validation.valid;
                  formValidation.notesError = validation.error;
                  formValidation = { ...formValidation };
                  // Trigger content type detection (MAS-002)
                  updateContentTypeDetection();
                }}
                placeholder="Specific requirements, constraints, or focus areas for the agents..."
                rows="3"
                class="w-full px-4 py-3 bg-gray-800 border rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-cyan-400/20 transition-all resize-none {formValidation.notesValid ? 'border-gray-600 focus:border-cyan-400' : 'border-red-500 focus:border-red-400'}"
              ></textarea>
              {#if !formValidation.notesValid && formValidation.notesError}
                <p class="text-xs text-red-400 mt-1">
                  {formValidation.notesError}
                </p>
              {/if}
            </div>
          </div>

          <!-- Generate Button with Help -->
          <div class="space-y-3">
            <button
              type="button"
              class="w-full py-4 bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-600 hover:to-purple-700 text-white font-semibold rounded-lg transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              on:click|preventDefault={startGeneration}
              disabled={isGenerating}
            >
              {#if topic.trim() || targetAudience.trim()}
                🚀 Start Targeted Generation
              {:else}
                🤖 Start Autonomous Generation (Trending AI Topics)
              {/if}
            </button>

            <!-- Help and Support Options (MAS-004) -->
            <div class="flex gap-2">
              <button
                type="button"
                class="flex-1 py-2 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded-lg transition-colors text-sm"
                on:click={() => showHelp()}
              >
                ❓ Help & Guide
              </button>
              <button
                type="button"
                class="flex-1 py-2 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded-lg transition-colors text-sm"
                on:click={checkSystemHealth}
              >
                🔍 System Status
              </button>
            </div>
          </div>

          <!-- Autonomous Mode Info -->
          {#if !topic.trim() && !targetAudience.trim()}
            <div class="mt-4 p-4 bg-cyan-900/20 border border-cyan-600/30 rounded-lg">
              <div class="flex items-center gap-2 mb-2">
                <div class="text-cyan-400">🤖</div>
                <h4 class="text-cyan-300 font-semibold">Autonomous Mode Active</h4>
              </div>
              <p class="text-gray-300 text-sm">
                The MAS will research today's trending AI topics, analyze market demand,
                and automatically generate relevant content based on real-time web searches and trend analysis.
              </p>
            </div>
          {/if}
          {/if}
        </div>
      {/if}


      <!-- Observatory & Monitoring Section (moved to bottom of Generator) -->
      <!-- Agent Status Grid -->
      <div class="mt-12 mb-8">
        <div class="flex justify-between items-center mb-6">
          <h2 class="text-2xl font-bold text-white">
            🤖 Agent Status & Real-time Activity
          </h2>
          <button
            class="px-4 py-2 bg-cyan-500 hover:bg-cyan-600 text-white rounded-lg transition-colors text-sm"
            on:click={loadRealMASActivity}
          >
            🔄 Refresh Data
          </button>
        </div>
          <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4 mb-8">
            {#each agents as agent}
              <div
                class="bg-gray-800/50 rounded-xl p-4 border border-gray-700 text-center"
              >
                <div
                  class="w-12 h-12 bg-gradient-to-br from-cyan-400 to-purple-500 rounded-full flex items-center justify-center text-white font-bold text-lg mx-auto mb-3"
                >
                  {agent.name.charAt(0)}
                </div>
                <h3 class="text-white font-semibold text-sm mb-1">{agent.name}</h3>
                <p class="text-gray-400 text-xs mb-2">{agent.role}</p>
                <div class="flex items-center justify-center gap-1 mb-2">
                  <div
                    class="w-2 h-2 rounded-full"
                    style="background-color: {getStatusColor(agent.status)}"
                  ></div>
                  <span class="text-xs text-gray-300 capitalize"
                    >{agent.status}</span
                  >
                </div>
                <div class="text-xs text-gray-400">
                  <div>Tasks: {agent.tasks}</div>
                  <div>Quality: {(agent.quality * 100).toFixed(0)}%</div>
                </div>
              </div>
            {/each}
          </div>

          <!-- Agent Conversations -->
          <div class="mb-8">
            <h3 class="text-lg font-semibold text-white mb-3 text-center">
              💬 Agent Conversations
              {#if dataLoaded}
                <span class="text-green-400 text-sm ml-2">✅ Real Data Loaded</span>
              {:else}
                <span class="text-orange-400 text-sm ml-2">🔄 Loading...</span>
              {/if}
            </h3>
            <div
              class="bg-gray-800/50 rounded-xl p-4 border border-gray-700 space-y-2 h-96 overflow-y-auto"
              bind:this={conversationsContainer}
            >
              {#each conversations as conversation}
                <div class="bg-gray-700/50 rounded p-3 text-sm">
                  <div class="flex items-center gap-2 mb-1">
                    <span class="text-cyan-400 font-semibold"
                      >{conversation.from_agent}</span
                    >
                    <span class="text-gray-400">→</span>
                    <span class="text-purple-400 font-semibold"
                      >{conversation.to_agent}</span
                    >
                    <span class="text-gray-500 text-xs ml-auto"
                      >{formatDateTime(conversation.timestamp)}</span
                    >
                  </div>
                  <p class="text-gray-300">{conversation.message}</p>
                </div>
              {:else}
                <div class="text-center py-8">
                  <div class="text-2xl mb-2">💬</div>
                  <p class="text-gray-400">No agent conversations yet</p>
                  <p class="text-gray-500 text-sm mt-1">Start content generation to see real agent interactions</p>
                </div>
              {/each}
            </div>
          </div>

          <!-- File Changes -->
          <div class="mb-8">
            <h3 class="text-lg font-semibold text-white mb-3 text-center">
              📁 File Changes
            </h3>
            <div class="bg-gray-800/50 rounded-xl p-4 border border-gray-700 space-y-2 h-96 overflow-y-auto"
              bind:this={fileChangesContainer}>
              {#each fileChanges as change}
                <div class="bg-gray-700/50 rounded p-3 text-sm">
                  <div class="flex items-center gap-2 mb-1">
                    <span class="text-green-400">📄</span>
                    <span class="text-white font-mono text-xs"
                      >{change.file_path}</span
                    >
                    <span class="text-gray-500 text-xs ml-auto"
                      >{formatDateTime(change.timestamp)}</span
                    >
                  </div>
                  <p class="text-gray-400 capitalize">{change.change_type}</p>
                </div>
              {:else}
                <div class="text-center py-8">
                  <div class="text-2xl mb-2">📁</div>
                  <p class="text-gray-400">No file changes yet</p>
                  <p class="text-gray-500 text-sm mt-1">Real file operations will appear here</p>
                </div>
              {/each}
            </div>
          </div>

          <!-- Tool Activities -->
          <div class="mb-8">
            <h3 class="text-lg font-semibold text-white mb-3 text-center">
              🔧 Tool Activities & MCP Usage
              {#if dataLoaded}
                <span class="text-green-400 text-sm ml-2">✅ Real Data Loaded</span>
              {:else}
                <span class="text-orange-400 text-sm ml-2">🔄 Loading...</span>
              {/if}
            </h3>
            <div class="bg-gray-800/50 rounded-xl p-4 border border-gray-700 space-y-2 h-96 overflow-y-auto">
              {#each toolActivities as activity}
                <div class="bg-gray-700/50 rounded p-3 text-sm border-l-4 border-cyan-500">
                  <div class="flex items-center gap-2 mb-2">
                    <span class="text-cyan-400 font-semibold">{activity.agent}</span>
                    <span class="text-gray-400">→</span>
                    <span class="text-purple-400 font-semibold">{activity.tool.toUpperCase()}</span>
                    <span class="text-gray-500 text-xs ml-auto">
                      {formatDateTime(activity.timestamp)}
                    </span>
                  </div>
                  <div class="mb-2">
                    <p class="text-white font-medium">{activity.action}</p>
                    <p class="text-gray-300 text-xs mt-1">{activity.details}</p>
                  </div>
                  <div class="flex items-center gap-4 text-xs">
                    <span class="text-green-400">
                      ⏱️ {activity.execution_time}s
                    </span>
                    <span class="text-blue-400 capitalize">
                      📊 {activity.status}
                    </span>
                    {#if activity.tool === 'web_search'}
                      <span class="text-yellow-400">🔍 Web Search</span>
                    {:else if activity.tool === 'agentic_retrieval'}
                      <span class="text-purple-400">🧠 RAG Query</span>
                    {:else if activity.tool === 'guardrails_check'}
                      <span class="text-red-400">🛡️ Safety Check</span>
                    {:else if activity.tool === 'safety_validation'}
                      <span class="text-orange-400">🔒 Security Scan</span>
                    {/if}
                  </div>
                </div>
              {:else}
                <div class="text-center py-8">
                  <div class="text-2xl mb-2">🔧</div>
                  <p class="text-gray-400">No tool activities yet</p>
                  <p class="text-gray-500 text-sm mt-1">
                    Tool usage will appear here when agents use MCP tools
                  </p>
                  <button
                    class="mt-4 px-4 py-2 bg-cyan-500 hover:bg-cyan-600 text-white rounded-lg transition-colors"
                    on:click={loadRealMASActivity}
                  >
                    🔄 Load Tool Activities
                  </button>
                </div>
              {/each}
            </div>
          </div>

          <!-- Recent Generated Content -->
          <h2 class="text-xl font-bold text-white mb-4 text-center">
            📝 Recent Generated Content
          </h2>
          <div class="bg-gray-800/50 rounded-xl p-6 border border-gray-700 mb-8">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {#each recentContent.slice(0, 6) as content}
                <div
                  class="bg-gray-700/50 rounded-lg p-4 border border-gray-600 hover:border-cyan-400 transition-colors cursor-pointer"
                >
                  <div class="flex items-center gap-3 mb-2">
                    <div class="text-2xl">
                      {content.type === 'course'
                        ? '🎓'
                        : content.type === 'news'
                          ? '📰'
                          : content.type === 'documentation'
                            ? '📚'
                            : '🚀'}
                    </div>
                    <div>
                      <h3 class="text-white font-semibold text-sm">
                        {content.title || 'Generated Content'}
                      </h3>
                      <p class="text-gray-400 text-xs capitalize">
                        {content.type?.replace('_', ' ') || 'Content'}
                      </p>
                    </div>
                  </div>
                  <div class="text-xs text-gray-400">
                    Generated: {content.timestamp ? formatDateTime(content.timestamp) : formatDateTime(new Date().toISOString())}
                  </div>
                </div>
              {:else}
                <div class="col-span-full text-center py-8">
                  <div class="text-4xl mb-2">📝</div>
                  <p class="text-gray-400">No content generated yet</p>
                  <p class="text-gray-500 text-sm mt-2">
                    Start content generation to see real agent activity
                  </p>
                  <button
                    class="mt-4 px-4 py-2 bg-cyan-500 hover:bg-cyan-600 text-white rounded-lg transition-colors"
                    on:click={loadRealMASActivity}
                  >
                    🔄 Load Recent Activity
                  </button>
                </div>
              {/each}
            </div>
          </div>
        </div>
    {:else if activeView === 'services'}
      <!-- Service Control & Embedded Monitoring -->
      <div class="space-y-8">
        <!-- Hardware Monitoring (moved from Observatory) -->
        <div class="bg-gray-800/50 rounded-xl p-6 border border-gray-700">
          <h2 class="text-2xl font-bold text-white mb-6">
            📊 Hardware Performance Monitoring
          </h2>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-gray-700/50 rounded-xl p-6 border border-gray-600">
              <div class="flex items-center gap-3 mb-4">
                <div class="text-2xl">🖥️</div>
                <h3 class="text-white font-semibold">GPU Performance</h3>
              </div>
              <div class="space-y-3">
                <div>
                  <div class="flex justify-between text-sm mb-1">
                    <span class="text-gray-400">Utilization</span>
                    <span class="text-white"
                      >{Math.round(hardwareMetrics.gpu.utilization)}%</span
                    >
                  </div>
                  <div class="w-full bg-gray-700 rounded-full h-2">
                    <div
                      class="bg-gradient-to-r from-cyan-500 to-purple-500 h-2 rounded-full transition-all"
                      style="width: {hardwareMetrics.gpu.utilization}%"
                    ></div>
                  </div>
                </div>
                <div class="text-sm">
                  <span class="text-gray-400">Temperature:</span>
                  <span class="text-white ml-2"
                    >{Math.round(hardwareMetrics.gpu.temperature)}°C</span
                  >
                </div>
                <div class="text-sm">
                  <span class="text-gray-400">Memory:</span>
                  <span class="text-white ml-2"
                    >{hardwareMetrics.gpu.memory.toFixed(1)}GB</span
                  >
                </div>
              </div>
            </div>

            <div class="bg-gray-700/50 rounded-xl p-6 border border-gray-600">
              <div class="flex items-center gap-3 mb-4">
                <div class="text-2xl">🔥</div>
                <h3 class="text-white font-semibold">CPU Performance</h3>
              </div>
              <div class="space-y-3">
                <div>
                  <div class="flex justify-between text-sm mb-1">
                    <span class="text-gray-400">Usage</span>
                    <span class="text-white"
                      >{Math.round(hardwareMetrics.cpu.usage)}%</span
                    >
                  </div>
                  <div class="w-full bg-gray-700 rounded-full h-2">
                    <div
                      class="bg-gradient-to-r from-green-500 to-yellow-500 h-2 rounded-full transition-all"
                      style="width: {hardwareMetrics.cpu.usage}%"
                    ></div>
                  </div>
                </div>
                <div class="text-sm">
                  <span class="text-gray-400">Temperature:</span>
                  <span class="text-white ml-2"
                    >{Math.round(hardwareMetrics.cpu.temperature)}°C</span
                  >
                </div>
              </div>
            </div>

            <div class="bg-gray-700/50 rounded-xl p-6 border border-gray-600">
              <div class="flex items-center gap-3 mb-4">
                <div class="text-2xl">💾</div>
                <h3 class="text-white font-semibold">Memory Usage</h3>
              </div>
              <div class="space-y-3">
                <div>
                  <div class="flex justify-between text-sm mb-1">
                    <span class="text-gray-400">RAM</span>
                    <span class="text-white"
                      >{Math.round(
                        (hardwareMetrics.ram.used / hardwareMetrics.ram.total) * 100
                      )}%</span
                    >
                  </div>
                  <div class="w-full bg-gray-700 rounded-full h-2">
                    <div
                      class="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all"
                      style="width: {(hardwareMetrics.ram.used /
                        hardwareMetrics.ram.total) *
                        100}%"
                    ></div>
                  </div>
                </div>
                <div class="text-sm">
                  <span class="text-gray-400">Used:</span>
                  <span class="text-white ml-2"
                    >{hardwareMetrics.ram.used.toFixed(1)}GB / {hardwareMetrics.ram
                      .total}GB</span
                  >
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Service Status Overview -->
        <div class="bg-gray-800/50 rounded-xl p-6 border border-gray-700">
          <h2 class="text-2xl font-bold text-white mb-6">
            🛠️ Service Control Center
          </h2>

          <!-- Service Grid -->
          <div
            class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6"
          >
            <!-- MAS API -->
            <div class="bg-gray-700/50 rounded-lg p-4 border border-gray-600">
              <div class="flex items-center justify-between mb-3">
                <div class="flex items-center gap-3">
                  <div
                    class="w-3 h-3 bg-green-400 rounded-full animate-pulse"
                  ></div>
                  <h3 class="text-white font-semibold">MAS API</h3>
                </div>
                <span class="text-xs text-gray-400">:5173</span>
              </div>
              <p class="text-gray-300 text-sm mb-3">Agent communication hub</p>
              <div class="flex gap-2">
                <button
                  class="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded transition-colors"
                  on:click={() => restartService('mas-api')}
                >
                  Restart
                </button>
                <button
                  class="px-3 py-1 bg-purple-600 hover:bg-purple-700 text-white text-xs rounded transition-colors"
                  on:click={() =>
                    showNotification(
                      '✅ MAS API: Integrated with SvelteKit dev server',
                      'success'
                    )}
                >
                  Status
                </button>
              </div>
            </div>

            <!-- Grafana -->
            <div class="bg-gray-700/50 rounded-lg p-4 border border-gray-600">
              <div class="flex items-center justify-between mb-3">
                <div class="flex items-center gap-3">
                  <div
                    class="w-3 h-3 bg-green-400 rounded-full animate-pulse"
                  ></div>
                  <h3 class="text-white font-semibold">Grafana</h3>
                </div>
                <span class="text-xs text-gray-400">:3001</span>
              </div>
              <p class="text-gray-300 text-sm mb-3">Dashboards & analytics</p>
              <div class="flex gap-2">
                <button
                  class="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded transition-colors"
                  on:click={() => openService('Grafana', 3001)}
                >
                  Open
                </button>
                <button
                  class="px-3 py-1 bg-purple-600 hover:bg-purple-700 text-white text-xs rounded transition-colors"
                  on:click={() => restartService('grafana')}
                >
                  Restart
                </button>
              </div>
            </div>

            <!-- Prometheus -->
            <div class="bg-gray-700/50 rounded-lg p-4 border border-gray-600">
              <div class="flex items-center justify-between mb-3">
                <div class="flex items-center gap-3">
                  <div
                    class="w-3 h-3 bg-green-400 rounded-full animate-pulse"
                  ></div>
                  <h3 class="text-white font-semibold">Prometheus</h3>
                </div>
                <span class="text-xs text-gray-400">:9091</span>
              </div>
              <p class="text-gray-300 text-sm mb-3">Metrics collection</p>
              <div class="flex gap-2">
                <button
                  class="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded transition-colors"
                  on:click={() => openService('Prometheus', 9091)}
                >
                  Open
                </button>
                <button
                  class="px-3 py-1 bg-purple-600 hover:bg-purple-700 text-white text-xs rounded transition-colors"
                  on:click={() => restartService('prometheus')}
                >
                  Restart
                </button>
              </div>
            </div>

            <!-- Netdata -->
            <div class="bg-gray-700/50 rounded-lg p-4 border border-gray-600">
              <div class="flex items-center justify-between mb-3">
                <div class="flex items-center gap-3">
                  <div
                    class="w-3 h-3 bg-green-400 rounded-full animate-pulse"
                  ></div>
                  <h3 class="text-white font-semibold">Netdata</h3>
                </div>
                <span class="text-xs text-gray-400">:19999</span>
              </div>
              <p class="text-gray-300 text-sm mb-3">Hardware monitoring</p>
              <div class="flex gap-2">
                <button
                  class="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded transition-colors"
                  on:click={() => openService('Netdata', 19999)}
                >
                  Open
                </button>
                <button
                  class="px-3 py-1 bg-purple-600 hover:bg-purple-700 text-white text-xs rounded transition-colors"
                  on:click={() => restartService('netdata')}
                >
                  Restart
                </button>
              </div>
            </div>

            <!-- Portainer -->
            <div class="bg-gray-700/50 rounded-lg p-4 border border-gray-600">
              <div class="flex items-center justify-between mb-3">
                <div class="flex items-center gap-3">
                  <div
                    class="w-3 h-3 bg-green-400 rounded-full animate-pulse"
                  ></div>
                  <h3 class="text-white font-semibold">Portainer</h3>
                </div>
                <span class="text-xs text-gray-400">:9000</span>
              </div>
              <p class="text-gray-300 text-sm mb-3">Container management</p>
              <div class="flex gap-2">
                <button
                  class="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded transition-colors"
                  on:click={() => openService('Portainer', 9000)}
                >
                  Open
                </button>
                <button
                  class="px-3 py-1 bg-purple-600 hover:bg-purple-700 text-white text-xs rounded transition-colors"
                  on:click={() => restartService('portainer')}
                >
                  Restart
                </button>
              </div>
            </div>

            <!-- Kibana -->
            <div class="bg-gray-700/50 rounded-lg p-4 border border-gray-600">
              <div class="flex items-center justify-between mb-3">
                <div class="flex items-center gap-3">
                  <div
                    class="w-3 h-3 bg-green-400 rounded-full animate-pulse"
                  ></div>
                  <h3 class="text-white font-semibold">Kibana</h3>
                </div>
                <span class="text-xs text-gray-400">:5601</span>
              </div>
              <p class="text-gray-300 text-sm mb-3">Log analysis</p>
              <div class="flex gap-2">
                <button
                  class="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded transition-colors"
                  on:click={() => openService('Kibana', 5601)}
                >
                  Open
                </button>
                <button
                  class="px-3 py-1 bg-purple-600 hover:bg-purple-700 text-white text-xs rounded transition-colors"
                  on:click={() => restartService('kibana')}
                >
                  Restart
                </button>
              </div>
            </div>

            <!-- Advanced Protocol Services (June 2025) -->
            <!-- MCP Server -->
            <div class="bg-gray-700/50 rounded-lg p-4 border border-cyan-600">
              <div class="flex items-center justify-between mb-3">
                <div class="flex items-center gap-3">
                  <div
                    class="w-3 h-3 rounded-full {protocolServices['mcp']?.status === 'running' ? 'bg-green-400 animate-pulse' : protocolServices['mcp']?.status === 'starting' ? 'bg-orange-400 animate-pulse' : protocolServices['mcp']?.status === 'error' ? 'bg-red-600' : 'bg-red-600'}"
                  ></div>
                  <h3 class="text-white font-semibold">MCP Server</h3>
                </div>
                <span class="text-xs text-gray-400">:3002</span>
              </div>
              <p class="text-gray-300 text-sm mb-2">
                Model Context Protocol - Tool Calling
              </p>
              <div class="text-xs text-gray-400 mb-3">
                Status: {protocolServices['mcp']?.status || 'unknown'} |
                Health: {protocolServices['mcp']?.health || 'unknown'}
              </div>
              <div class="flex gap-2">
                <button
                  class="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded transition-colors"
                  on:click={() => checkProtocolServiceHealth('mcp')}
                >
                  Health
                </button>
                <button
                  class="px-3 py-1 bg-purple-600 hover:bg-purple-700 text-white text-xs rounded transition-colors"
                  on:click={() => restartProtocolService('mcp')}
                >
                  Restart
                </button>
                {#if protocolServices['mcp']?.status === 'stopped'}
                  <button
                    class="px-3 py-1 bg-green-600 hover:bg-green-700 text-white text-xs rounded transition-colors"
                    on:click={() => startProtocolService('mcp')}
                  >
                    Start
                  </button>
                {:else if protocolServices['mcp']?.status === 'running'}
                  <button
                    class="px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded transition-colors"
                    on:click={() => stopProtocolService('mcp')}
                  >
                    Stop
                  </button>
                {/if}
              </div>
            </div>

            <!-- A2A Protocol -->
            <div class="bg-gray-700/50 rounded-lg p-4 border border-purple-600">
              <div class="flex items-center justify-between mb-3">
                <div class="flex items-center gap-3">
                  <div
                    class="w-3 h-3 bg-green-400 rounded-full animate-pulse"
                  ></div>
                  <h3 class="text-white font-semibold">A2A Protocol</h3>
                </div>
                <span class="text-xs text-gray-400">:3003</span>
              </div>
              <p class="text-gray-300 text-sm mb-3">
                Agent-to-Agent Communication
              </p>
              <div class="flex gap-2">
                <button
                  class="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded transition-colors"
                  on:click={() => openService('A2A Protocol', 3003)}
                >
                  Health
                </button>
                <button
                  class="px-3 py-1 bg-purple-600 hover:bg-purple-700 text-white text-xs rounded transition-colors"
                  on:click={() => restartService('a2a-protocol')}
                >
                  Restart
                </button>
              </div>
            </div>

            <!-- Agentic Retrieval -->
            <div class="bg-gray-700/50 rounded-lg p-4 border border-green-600">
              <div class="flex items-center justify-between mb-3">
                <div class="flex items-center gap-3">
                  <div
                    class="w-3 h-3 bg-green-400 rounded-full animate-pulse"
                  ></div>
                  <h3 class="text-white font-semibold">Agentic Retrieval</h3>
                </div>
                <span class="text-xs text-gray-400">:3004</span>
              </div>
              <p class="text-gray-300 text-sm mb-3">
                Advanced RAG with GraphRAG
              </p>
              <div class="flex gap-2">
                <button
                  class="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded transition-colors"
                  on:click={() => openService('Agentic Retrieval', 3004)}
                >
                  Health
                </button>
                <button
                  class="px-3 py-1 bg-purple-600 hover:bg-purple-700 text-white text-xs rounded transition-colors"
                  on:click={() => restartService('agentic-retrieval')}
                >
                  Restart
                </button>
              </div>
            </div>

            <!-- Guardrails Service -->
            <div class="bg-gray-700/50 rounded-lg p-4 border border-green-600">
              <div class="flex items-center justify-between mb-3">
                <div class="flex items-center gap-3">
                  <div
                    class="w-3 h-3 bg-green-400 rounded-full animate-pulse"
                  ></div>
                  <h3 class="text-white font-semibold">Guardrails Service</h3>
                </div>
                <span class="text-xs text-gray-400">:3005</span>
              </div>
              <p class="text-gray-300 text-sm mb-3">
                Safety Validation & Scoring
              </p>
              <div class="flex gap-2">
                <button
                  class="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded transition-colors"
                  on:click={() => openService('Guardrails Service', 3005)}
                >
                  Health
                </button>
                <button
                  class="px-3 py-1 bg-purple-600 hover:bg-purple-700 text-white text-xs rounded transition-colors"
                  on:click={() => restartService('guardrails')}
                >
                  Restart
                </button>
              </div>
            </div>

            <!-- Ollama LLM Server -->
            <div class="bg-gray-700/50 rounded-lg p-4 border border-orange-600">
              <div class="flex items-center justify-between mb-3">
                <div class="flex items-center gap-3">
                  <div
                    class="w-3 h-3 rounded-full {protocolServices['ollama']?.status === 'running' ? 'bg-green-400 animate-pulse' : protocolServices['ollama']?.status === 'starting' ? 'bg-orange-400 animate-pulse' : protocolServices['ollama']?.status === 'error' ? 'bg-red-600' : 'bg-red-600'}"
                  ></div>
                  <h3 class="text-white font-semibold">Ollama LLM</h3>
                </div>
                <span class="text-xs text-gray-400">:11434</span>
              </div>
              <p class="text-gray-300 text-sm mb-3">
                Local LLM Server (qwen3:30b-a3b, devstral:24b)
              </p>
              <div class="flex gap-2">
                <button
                  class="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded transition-colors"
                  on:click={() => openService('Ollama', 11434)}
                >
                  Models
                </button>
                <button
                  class="px-3 py-1 bg-purple-600 hover:bg-purple-700 text-white text-xs rounded transition-colors"
                  on:click={() => restartService('ollama')}
                >
                  Restart
                </button>
              </div>
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="flex flex-wrap gap-3">
            <button
              class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
              on:click={restartAllServices}
            >
              🚀 Restart All Services
            </button>
            <button
              class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
              on:click={healthCheckAll}
            >
              📊 Health Check
            </button>
            <button
              class="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
              on:click={debugAllServices}
            >
              🔍 Debug Services
            </button>
            <button
              class="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors flex items-center gap-2"
              on:click={async () => {
                setLoading('update-containers', true);
                try {
                  // Call real container update API
                  const response = await fetch('/api/docker/update', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: 'update_all' })
                  });

                  if (response.ok) {
                    showNotification('✅ Containers updated successfully!', 'success');
                  } else {
                    throw new Error('Update failed');
                  }
                } catch (error) {
                  showNotification('❌ Container update failed', 'error');
                } finally {
                  setLoading('update-containers', false);
                }
              }}
              disabled={loadingStates['update-containers']}
            >
              {#if loadingStates['update-containers']}
                <div
                  class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"
                ></div>
              {:else}
                🔄
              {/if}
              Update Containers
            </button>
            <button
              class="px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded-lg transition-colors flex items-center gap-2"
              on:click={exportLogs}
              disabled={loadingStates['export-logs']}
            >
              {#if loadingStates['export-logs']}
                <div
                  class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"
                ></div>
              {:else}
                📋
              {/if}
              Export Logs
            </button>
          </div>
        </div>

        <!-- Embedded Monitoring Widgets -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- Hardware Metrics Widget -->
          <div class="bg-gray-800/50 rounded-xl p-6 border border-gray-700">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-xl font-semibold text-white">
                🖥️ Hardware Metrics
              </h3>
              <button
                class="px-3 py-1 bg-gray-600 hover:bg-gray-500 text-white text-xs rounded transition-colors"
                on:click={() => openService('Netdata', 19999)}
              >
                Full View
              </button>
            </div>

            <!-- Embedded Netdata Widget -->
            <div
              class="bg-gray-900/50 rounded-lg p-4 border border-gray-600 h-64 flex items-center justify-center"
            >
              <div class="text-center">
                <div class="text-4xl mb-2">📊</div>
                <p class="text-gray-400 mb-2">Hardware Metrics Widget</p>
                <p class="text-green-400 text-sm">
                  ✅ Netdata service is running
                </p>
                <p class="text-gray-400 text-xs mt-1">
                  Container: <code class="bg-gray-700 px-1 rounded"
                    >mas-netdata</code
                  >
                </p>
                <button
                  class="mt-3 px-3 py-1 bg-green-600 hover:bg-green-700 text-white text-xs rounded transition-colors"
                  on:click={() => openService('Netdata', 19999)}
                >
                  Open Netdata
                </button>
              </div>
            </div>
          </div>

          <!-- Container Status Widget -->
          <div class="bg-gray-800/50 rounded-xl p-6 border border-gray-700">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-xl font-semibold text-white">
                🐳 Container Status
              </h3>
              <button
                class="px-3 py-1 bg-gray-600 hover:bg-gray-500 text-white text-xs rounded transition-colors"
                on:click={() => openService('Portainer', 9000)}
              >
                Portainer
              </button>
            </div>

            <!-- Container List -->
            <div class="space-y-2 h-64 overflow-y-auto">
              <div
                class="flex items-center justify-between p-3 bg-gray-700/50 rounded border border-gray-600"
              >
                <div class="flex items-center gap-3">
                  <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span class="text-white text-sm">mas-grafana</span>
                </div>
                <span class="text-green-400 text-xs">Running</span>
              </div>
              <div
                class="flex items-center justify-between p-3 bg-gray-700/50 rounded border border-gray-600"
              >
                <div class="flex items-center gap-3">
                  <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span class="text-white text-sm">mas-prometheus</span>
                </div>
                <span class="text-green-400 text-xs">Running</span>
              </div>
              <div
                class="flex items-center justify-between p-3 bg-gray-700/50 rounded border border-gray-600"
              >
                <div class="flex items-center gap-3">
                  <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span class="text-white text-sm">mas-netdata</span>
                </div>
                <span class="text-green-400 text-xs">Running</span>
              </div>
              <div
                class="flex items-center justify-between p-3 bg-gray-700/50 rounded border border-gray-600"
              >
                <div class="flex items-center gap-3">
                  <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span class="text-white text-sm">mas-kibana</span>
                </div>
                <span class="text-green-400 text-xs">Running</span>
              </div>
              <div
                class="flex items-center justify-between p-3 bg-gray-700/50 rounded border border-gray-600"
              >
                <div class="flex items-center gap-3">
                  <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span class="text-white text-sm">mas-jaeger</span>
                </div>
                <span class="text-green-400 text-xs">Running</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Embedded Dashboards -->
        <div class="bg-gray-800/50 rounded-xl p-6 border border-gray-700">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-xl font-semibold text-white">📊 Live Dashboards</h3>
            <div class="flex gap-2">
              <button
                class="px-3 py-1 bg-cyan-600 hover:bg-cyan-700 text-white text-xs rounded transition-colors"
                on:click={() => openService('Grafana', 3001)}
              >
                Grafana
              </button>
              <button
                class="px-3 py-1 bg-orange-600 hover:bg-orange-700 text-white text-xs rounded transition-colors"
                on:click={() => openService('Prometheus', 9091)}
              >
                Prometheus
              </button>
            </div>
          </div>

          <!-- Embedded Dashboard -->
          <div
            class="bg-gray-900/50 rounded-lg border border-gray-600 h-96 flex items-center justify-center"
          >
            <div class="text-center">
              <div class="text-4xl mb-2">📈</div>
              <p class="text-gray-400 mb-2">MAS Overview Dashboard</p>
              <p class="text-green-400 text-sm">
                ✅ Grafana service is running
              </p>
              <p class="text-gray-400 text-xs mt-1">
                Container: <code class="bg-gray-700 px-1 rounded"
                  >mas-grafana</code
                >
              </p>
              <button
                class="mt-3 px-3 py-1 bg-green-600 hover:bg-green-700 text-white text-xs rounded transition-colors"
                on:click={() => openService('Grafana', 3001)}
              >
                Open Grafana
              </button>
            </div>
          </div>
        </div>
      </div>
    {/if}



    <!-- Generation Progress - Only show on Generator tab -->
    {#if activeView === 'generator' && isGenerating}
      <div
        class="max-w-2xl mx-auto bg-gray-800/50 rounded-xl p-8 border border-gray-700"
      >
        <h2 class="text-2xl font-bold text-white mb-6 text-center">
          🤖 Agents are collaborating...
        </h2>

        <!-- Progress Bar -->
        <div class="mb-6">
          <div class="flex justify-between text-sm text-gray-400 mb-2">
            <span>Progress</span>
            <span>{generationProgress}%</span>
          </div>
          <div class="w-full bg-gray-700 rounded-full h-3">
            <div
              class="h-3 rounded-full transition-all duration-500 {getProgressColor(
                generationProgress
              )}"
              style="width: {generationProgress}%"
            ></div>
          </div>
        </div>

        <!-- Current Phase -->
        {#if currentPhase}
          <div class="text-center mb-6">
            <p class="text-gray-400 mb-2">Current Phase:</p>
            <p class="text-xl text-cyan-400 font-semibold">
              {formatPhase(currentPhase)}
            </p>
          </div>
        {/if}

        <!-- Agent Activity -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-3 mb-6">
          {#each contentTypeConfig[contentType].agents as agent}
            <div class="bg-gray-700/50 rounded-lg p-3 text-center">
              <div class="text-2xl mb-1">🤖</div>
              <div class="text-xs text-gray-300">{agent}</div>
              <div
                class="w-2 h-2 bg-green-400 rounded-full mx-auto mt-2 animate-pulse"
              ></div>
            </div>
          {/each}
        </div>

        <!-- Estimated Completion -->
        {#if estimatedCompletion}
          <p class="text-center text-gray-400 text-sm">
            Estimated completion: {new Date(
              estimatedCompletion
            ).toLocaleTimeString()}
          </p>
        {/if}
      </div>
    {/if}

    <!-- Generated Content - Only show on Generator tab -->
    {#if activeView === 'generator' && generatedContent}
      <div
        class="max-w-4xl mx-auto bg-gray-800/50 rounded-xl p-8 border border-gray-700"
      >
        <div class="flex justify-between items-center mb-6">
          <h2 class="text-2xl font-bold text-white">✅ Generation Complete!</h2>
          <button
            class="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
            on:click={resetForm}
          >
            Generate New Content
          </button>
        </div>

        <!-- Content Preview -->
        <div class="bg-gray-900/50 rounded-lg p-6 border border-gray-600">
          <h3 class="text-xl font-semibold text-cyan-400 mb-4">
            {generatedContent.title}
          </h3>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <p class="text-sm text-gray-400 mb-1">Type</p>
              <p class="text-white capitalize">
                {generatedContent.type?.replace('_', ' ') || contentType.replace('_', ' ')}
              </p>
            </div>
            <div>
              <p class="text-sm text-gray-400 mb-1">Target Audience</p>
              <p class="text-white">{generatedContent.target_audience || generatedContent.targetAudience || targetAudience}</p>
            </div>
            <div>
              <p class="text-sm text-gray-400 mb-1">Complexity</p>
              <p class="text-white capitalize">
                {generatedContent.complexity_level || generatedContent.complexity || 'Auto-assessed by agents'}
              </p>
            </div>
            <div>
              <p class="text-sm text-gray-400 mb-1">Generated</p>
              <p class="text-white">
                {generatedContent.generated_at ? new Date(generatedContent.generated_at).toLocaleString() : new Date().toLocaleString()}
              </p>
            </div>
          </div>

          <!-- Content Type Specific Display -->
          {#if generatedContent.type === 'course'}
            <div class="space-y-4">
              <div>
                <h4 class="text-lg font-semibold text-white mb-2">
                  Course Lessons
                </h4>
                <div class="space-y-2">
                  {#each generatedContent.lessons as lesson}
                    <div class="bg-gray-800 rounded p-3">
                      <div class="flex justify-between items-center">
                        <span class="text-white">{lesson.title}</span>
                        <span class="text-gray-400 text-sm"
                          >{lesson.duration} min</span
                        >
                      </div>
                    </div>
                  {/each}
                </div>
              </div>

              <div>
                <h4 class="text-lg font-semibold text-white mb-2">
                  Assessments
                </h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                  {#each generatedContent.assessments as assessment}
                    <div class="bg-gray-800 rounded p-3 text-center">
                      <div class="text-cyan-400 capitalize">
                        {assessment.type}
                      </div>
                      <div class="text-sm text-gray-400 mt-1">
                        {assessment.questions
                          ? `${assessment.questions} questions`
                          : assessment.requirements}
                      </div>
                    </div>
                  {/each}
                </div>
              </div>
            </div>
          {:else if generatedContent.type === 'news_article'}
            <div class="space-y-4">
              <div>
                <h4 class="text-lg font-semibold text-white mb-2">
                  Article Content
                </h4>
                <div class="bg-gray-800 rounded p-4">
                  <p class="text-gray-300">{generatedContent.content}</p>
                </div>
              </div>

              <div>
                <h4 class="text-lg font-semibold text-white mb-2">Sources</h4>
                <div class="space-y-2">
                  {#each generatedContent.sources as source}
                    <div class="bg-gray-800 rounded p-3">
                      <a
                        href={source.url}
                        class="text-cyan-400 hover:text-cyan-300"
                        target="_blank"
                      >
                        {source.title}
                      </a>
                    </div>
                  {/each}
                </div>
              </div>
            </div>
          {:else if generatedContent.type === 'documentation'}
            <div class="space-y-4">
              <div>
                <h4 class="text-lg font-semibold text-white mb-2">
                  Documentation Sections
                </h4>
                <div class="space-y-2">
                  {#each generatedContent.sections as section}
                    <div class="bg-gray-800 rounded p-3">
                      <h5 class="text-white font-medium">{section.title}</h5>
                      <p class="text-gray-400 text-sm mt-1">
                        {section.content}
                      </p>
                    </div>
                  {/each}
                </div>
              </div>

              <div>
                <h4 class="text-lg font-semibold text-white mb-2">
                  Code Examples
                </h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                  {#each generatedContent.code_examples as example}
                    <div class="bg-gray-800 rounded p-3">
                      <div class="text-cyan-400">{example.language}</div>
                      <div class="text-sm text-gray-400 mt-1">
                        {example.title}
                      </div>
                    </div>
                  {/each}
                </div>
              </div>
            </div>
          {:else if generatedContent.type === 'vybe_qube'}
            <div class="space-y-4">
              <div
                class="bg-gradient-to-r from-green-900/30 to-blue-900/30 rounded-lg p-4 border border-green-500/30"
              >
                <h4 class="text-lg font-semibold text-green-400 mb-2">
                  🚀 Deployment Ready
                </h4>
                <p class="text-white mb-2">
                  Your Vybe Qube is ready for deployment!
                </p>
                <a
                  href="https://{generatedContent.deployment_url}"
                  class="text-cyan-400 hover:text-cyan-300"
                  target="_blank"
                >
                  {generatedContent.deployment_url}
                </a>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 class="text-lg font-semibold text-white mb-2">
                    Business Model
                  </h4>
                  <div class="bg-gray-800 rounded p-3">
                    <p class="text-gray-300">
                      {generatedContent.business_model.value_proposition}
                    </p>
                    <div class="mt-2 flex flex-wrap gap-2">
                      {#each generatedContent.business_model.revenue_streams as stream}
                        <span
                          class="px-2 py-1 bg-green-600/30 text-green-300 rounded text-xs"
                        >
                          {stream}
                        </span>
                      {/each}
                    </div>
                  </div>
                </div>

                <div>
                  <h4 class="text-lg font-semibold text-white mb-2">
                    Revenue Projections
                  </h4>
                  <div class="bg-gray-800 rounded p-3 space-y-2">
                    {#each Object.entries(generatedContent.revenue_projections) as [period, amount]}
                      <div class="flex justify-between">
                        <span class="text-gray-400 capitalize"
                          >{period.replace('_', ' ')}</span
                        >
                        <span class="text-green-400">{amount}</span>
                      </div>
                    {/each}
                  </div>
                </div>
              </div>
            </div>
          {/if}

          <!-- Agents Used -->
          <div class="mt-6 pt-6 border-t border-gray-600">
            <h4 class="text-lg font-semibold text-white mb-3">
              Agents Involved
            </h4>
            <div class="flex flex-wrap gap-2">
              {#each (generatedContent.agents_used || generatedContent.agents || ['VYBA', 'QUBERT', 'CODEX', 'PIXY', 'DUCKY', 'HAPPY', 'VYBRO']) as agent}
                <span
                  class="px-3 py-1 bg-purple-600/30 text-purple-300 rounded-full text-sm"
                >
                  🤖 {agent.toUpperCase()}
                </span>
              {/each}
            </div>
          </div>
        </div>
      </div>
    {/if}
  </Container>
</main>

<!-- MAS-004: Help Modal for User Guidance -->
{#if errorHandling.showHelp}
  <div class="fixed inset-0 bg-black/50 flex items-center justify-center z-50" on:click={hideHelp}>
    <div class="bg-gray-800 rounded-xl p-6 max-w-2xl w-full mx-4 border border-gray-600" on:click|stopPropagation>
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-xl font-bold text-white">
          {#if errorHandling.helpContext === 'support'}
            🆘 Contact Support
          {:else}
            ❓ Help & Guidance
          {/if}
        </h3>
        <button
          class="text-gray-400 hover:text-white text-xl"
          on:click={hideHelp}
        >
          ✕
        </button>
      </div>

      {#if errorHandling.helpContext === 'support'}
        <div class="space-y-4">
          <div class="bg-blue-900/20 border border-blue-600/30 rounded-lg p-4">
            <h4 class="text-blue-300 font-semibold mb-2">📧 Email Support</h4>
            <p class="text-gray-300 text-sm mb-2">For technical issues and bug reports:</p>
            <a href="mailto:<EMAIL>" class="text-blue-400 hover:text-blue-300"><EMAIL></a>
          </div>

          <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-4">
            <h4 class="text-green-300 font-semibold mb-2">💬 Community Discord</h4>
            <p class="text-gray-300 text-sm mb-2">Join our community for help and discussions:</p>
            <a href="https://discord.gg/vybecoding" class="text-green-400 hover:text-green-300">discord.gg/vybecoding</a>
          </div>

          <div class="bg-purple-900/20 border border-purple-600/30 rounded-lg p-4">
            <h4 class="text-purple-300 font-semibold mb-2">📚 Documentation</h4>
            <p class="text-gray-300 text-sm mb-2">Check our comprehensive guides:</p>
            <a href="/docs" class="text-purple-400 hover:text-purple-300">VybeCoding.ai Documentation</a>
          </div>

          {#if errorHandling.currentError}
            <div class="bg-red-900/20 border border-red-600/30 rounded-lg p-4">
              <h4 class="text-red-300 font-semibold mb-2">🐛 Current Error Details</h4>
              <div class="text-gray-300 text-sm space-y-1">
                <p><strong>Error Type:</strong> {errorHandling.currentError.type}</p>
                <p><strong>Context:</strong> {errorHandling.currentError.context}</p>
                <p><strong>Time:</strong> {errorHandling.currentError.timestamp.toLocaleString()}</p>
              </div>
            </div>
          {/if}
        </div>
      {:else}
        <div class="space-y-4">
          <div class="bg-cyan-900/20 border border-cyan-600/30 rounded-lg p-4">
            <h4 class="text-cyan-300 font-semibold mb-2">🚀 Getting Started</h4>
            <ul class="text-gray-300 text-sm space-y-1">
              <li>• Choose your content type (Course, News, Documentation, or Vybe Qube)</li>
              <li>• Enter a topic or leave blank for autonomous trending topic research</li>
              <li>• Specify your target audience or let AI detect it automatically</li>
              <li>• Add any specific requirements in the additional notes</li>
              <li>• Click "Start Generation" to begin the AI content creation process</li>
            </ul>
          </div>

          <div class="bg-purple-900/20 border border-purple-600/30 rounded-lg p-4">
            <h4 class="text-purple-300 font-semibold mb-2">🤖 Autonomous Mode</h4>
            <p class="text-gray-300 text-sm">
              Leave both topic and audience fields blank to enable autonomous mode.
              The AI will research trending topics and automatically generate relevant content.
            </p>
          </div>

          <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-4">
            <h4 class="text-green-300 font-semibold mb-2">⚡ Tips for Best Results</h4>
            <ul class="text-gray-300 text-sm space-y-1">
              <li>• Be specific with your requirements for targeted content</li>
              <li>• Use inspiration URLs to guide the AI's research</li>
              <li>• Monitor the real-time progress and agent activity</li>
              <li>• Check the Observatory for detailed system status</li>
            </ul>
          </div>
        </div>
      {/if}

      <div class="mt-6 flex justify-end">
        <button
          class="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
          on:click={hideHelp}
        >
          Close
        </button>
      </div>
    </div>
  </div>
{/if}

<style>
  /* ...existing code... */
  .agent-badges-wrap {
    max-width: 100%;
    word-break: break-word;
    flex-wrap: wrap;
    row-gap: 0.15rem;
    column-gap: 0.15rem;
  }
  .agent-badges-wrap span {
    word-break: break-word;
    white-space: pre-line;
    text-align: center;
  }
</style>
