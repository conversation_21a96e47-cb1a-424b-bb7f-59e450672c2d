<script lang="ts">
  import AccessibilityAudit from '$lib/components/AccessibilityAudit.svelte';
  import { Button } from '$lib/components/ui';
  import { themeActions, effectiveTheme } from '$lib/stores/theme';
  import {
    Sun,
    Moon,
    Monitor,
    Eye,
    Contrast,
    Accessibility,
    CheckCircle,
    AlertTriangle,
    Info,
  } from 'lucide-svelte';

  // Sample text elements to test
  const textSamples = [
    {
      class: 'text-foreground',
      label: 'Primary Text',
      text: 'This is primary text content',
    },
    {
      class: 'text-muted-foreground',
      label: 'Muted Text',
      text: 'This is muted/secondary text content',
    },
    {
      class: 'text-slate-400',
      label: 'Slate-400 (Problematic)',
      text: 'This text may be hard to read in light mode',
    },
    {
      class: 'text-slate-300',
      label: 'Slate-300 (Problematic)',
      text: 'This text is likely too light in light mode',
    },
    {
      class: 'text-light-secondary',
      label: 'Improved Secondary',
      text: 'This uses our improved secondary text color',
    },
    {
      class: 'text-light-tertiary',
      label: 'Improved Tertiary',
      text: 'This uses our improved tertiary text color',
    },
  ];

  function toggleTheme() {
    themeActions.toggleTheme();
  }
</script>

<svelte:head>
  <title>Accessibility Audit | VybeCoding.ai</title>
  <meta
    name="description"
    content="Comprehensive accessibility audit for VybeCoding.ai UI/UX"
  />
</svelte:head>

<main class="min-h-screen bg-background">
  <div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="text-center mb-8">
      <div class="flex items-center justify-center gap-3 mb-4">
        <Accessibility class="w-8 h-8 text-primary" />
        <h1 class="text-4xl font-bold">Accessibility Audit</h1>
      </div>
      <p class="text-xl text-muted-foreground max-w-2xl mx-auto">
        Comprehensive UI/UX accessibility testing for light and dark modes
      </p>

      <!-- Theme Toggle -->
      <div class="flex items-center justify-center gap-4 mt-6">
        <span class="text-sm font-medium">Current theme: {$effectiveTheme}</span
        >
        <Button
          on:click={toggleTheme}
          variant="outline"
          class="flex items-center gap-2"
        >
          {#if $effectiveTheme === 'dark'}
            <Sun class="w-4 h-4" />
            Switch to Light
          {:else}
            <Moon class="w-4 h-4" />
            Switch to Dark
          {/if}
        </Button>
      </div>
    </div>

    <!-- Text Samples Section -->
    <section class="mb-12">
      <h2 class="text-2xl font-bold mb-6 flex items-center gap-2">
        <Eye class="w-6 h-6" />
        Text Readability Samples
      </h2>

      <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {#each textSamples as sample}
          <div class="p-4 border rounded-lg bg-card">
            <h3 class="font-semibold mb-2 text-sm text-muted-foreground">
              {sample.label}
            </h3>
            <p class="{sample.class} text-base leading-relaxed">
              {sample.text}
            </p>
            <code class="text-xs text-muted-foreground mt-2 block">
              .{sample.class}
            </code>
          </div>
        {/each}
      </div>
    </section>

    <!-- Contrast Audit Component -->
    <section class="mb-12">
      <AccessibilityAudit />
    </section>

    <!-- Common UI Elements Test -->
    <section class="mb-12">
      <h2 class="text-2xl font-bold mb-6 flex items-center gap-2">
        <Contrast class="w-6 h-6" />
        Common UI Elements
      </h2>

      <div class="space-y-6">
        <!-- Buttons -->
        <div class="p-6 border rounded-lg bg-card">
          <h3 class="font-semibold mb-4">Buttons</h3>
          <div class="flex flex-wrap gap-3">
            <Button variant="default">Primary Button</Button>
            <Button variant="secondary">Secondary Button</Button>
            <Button variant="outline">Outline Button</Button>
            <Button variant="ghost">Ghost Button</Button>
            <Button variant="destructive">Destructive Button</Button>
          </div>
        </div>

        <!-- Form Elements -->
        <div class="p-6 border rounded-lg bg-card">
          <h3 class="font-semibold mb-4">Form Elements</h3>
          <div class="space-y-4 max-w-md">
            <div>
              <label class="block text-sm font-medium mb-2"> Text Input </label>
              <input
                type="text"
                placeholder="Text input"
                class="w-full px-3 py-2 border rounded-md bg-background text-foreground"
              />
            </div>
            <div>
              <label class="block text-sm font-medium mb-2">
                Select Dropdown
              </label>
              <select
                class="w-full px-3 py-2 border rounded-md bg-background text-foreground"
              >
                <option>Option 1</option>
                <option>Option 2</option>
                <option>Option 3</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Status Indicators -->
        <div class="p-6 border rounded-lg bg-card">
          <h3 class="font-semibold mb-4">Status Indicators</h3>
          <div class="space-y-3">
            <div class="flex items-center gap-2">
              <CheckCircle class="w-5 h-5 text-green-500" />
              <span class="text-green-700 dark:text-green-400"
                >Success message</span
              >
            </div>
            <div class="flex items-center gap-2">
              <Info class="w-5 h-5 text-blue-500" />
              <span class="text-blue-700 dark:text-blue-400"
                >Information message</span
              >
            </div>
            <div class="flex items-center gap-2">
              <AlertTriangle class="w-5 h-5 text-yellow-500" />
              <span class="text-yellow-700 dark:text-yellow-400"
                >Warning message</span
              >
            </div>
            <div class="flex items-center gap-2">
              <AlertTriangle class="w-5 h-5 text-red-500" />
              <span class="text-red-700 dark:text-red-400">Error message</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Recommendations -->
    <section class="mb-12">
      <h2 class="text-2xl font-bold mb-6">Accessibility Recommendations</h2>

      <div class="grid gap-6 md:grid-cols-2">
        <div
          class="p-6 border rounded-lg bg-green-50 dark:bg-green-950 border-green-200 dark:border-green-800"
        >
          <h3 class="font-semibold text-green-900 dark:text-green-100 mb-3">
            ✅ Improvements Applied
          </h3>
          <ul class="text-sm text-green-800 dark:text-green-200 space-y-2">
            <li>• Enhanced muted text contrast in light mode</li>
            <li>• Fixed slate-300/400/500 colors for light mode</li>
            <li>• Added high contrast mode support</li>
            <li>• Improved focus indicators</li>
            <li>• Created accessible color utilities</li>
          </ul>
        </div>

        <div
          class="p-6 border rounded-lg bg-blue-50 dark:bg-blue-950 border-blue-200 dark:border-blue-800"
        >
          <h3 class="font-semibold text-blue-900 dark:text-blue-100 mb-3">
            📋 Next Steps
          </h3>
          <ul class="text-sm text-blue-800 dark:text-blue-200 space-y-2">
            <li>• Test with screen readers</li>
            <li>• Verify keyboard navigation</li>
            <li>• Check color-blind accessibility</li>
            <li>• Validate ARIA labels</li>
            <li>• Test with real users</li>
          </ul>
        </div>
      </div>
    </section>
  </div>
</main>
