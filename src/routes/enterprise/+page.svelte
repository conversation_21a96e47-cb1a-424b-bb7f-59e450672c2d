<script lang="ts">
  import { Container, Grid, Card, Button, Badge } from '$lib/components/ui';
  import {
    Building,
    Users,
    Shield,
    Zap,
    CheckCircle,
    ArrowRight,
    Phone,
    Mail,
  } from 'lucide-svelte';
  import { onMount } from 'svelte';

  // Maya's Design System: Enterprise page analytics
  onMount(() => {
    console.log('Enterprise page viewed');
  });

  const enterpriseFeatures = [
    {
      icon: Users,
      title: 'Team Management',
      description:
        'Centralized dashboard for managing learner progress, assignments, and certifications across your organization.',
    },
    {
      icon: Shield,
      title: 'Enterprise Security',
      description:
        'SOC 2 compliance, SSO integration, advanced user permissions, and data encryption at rest and in transit.',
    },
    {
      icon: Zap,
      title: 'Custom Curriculum',
      description:
        'Tailored learning paths aligned with your business objectives and existing technology stack.',
    },
    {
      icon: Building,
      title: 'White-Label Solution',
      description:
        'Branded learning platform with your company logo, colors, and custom domain for seamless integration.',
    },
  ];

  const plans = [
    {
      name: 'Team',
      price: '$99',
      period: 'per user/month',
      description:
        'Perfect for small to medium teams getting started with AI development.',
      features: [
        'Up to 50 users',
        'Standard curriculum access',
        'Basic analytics dashboard',
        'Email support',
        'Team progress tracking',
        'Certificate management',
      ],
      popular: false,
    },
    {
      name: 'Enterprise',
      price: '$199',
      period: 'per user/month',
      description:
        'Comprehensive solution for large organizations with advanced needs.',
      features: [
        'Unlimited users',
        'Custom curriculum development',
        'Advanced analytics & reporting',
        'Dedicated success manager',
        'SSO integration',
        'API access',
        'Priority support',
        'White-label options',
      ],
      popular: true,
    },
    {
      name: 'Custom',
      price: 'Contact us',
      period: 'for pricing',
      description:
        'Fully customized solution tailored to your specific requirements.',
      features: [
        'Everything in Enterprise',
        'Custom integrations',
        'On-premise deployment',
        'Dedicated infrastructure',
        'Custom SLA agreements',
        'Professional services',
        'Training & consulting',
      ],
      popular: false,
    },
  ];

  const realPlatformHighlights = [
    {
      quote:
        'VybeCoding.ai represents the future of AI development education with its enterprise-grade Multi-Agent System and 100% FOSS technology stack.',
      author: 'Platform Architecture',
      role: 'Technical Foundation',
      company: 'Production-Ready System',
    },
    {
      quote:
        'The Vybe Method combines proven BMAD methodology with autonomous AI agents, delivering enterprise-grade development capabilities with complete privacy.',
      author: 'Development Team',
      role: 'Innovation Leaders',
      company: 'VybeCoding.ai',
    },
  ];

  const realStats = [
    { value: '48', label: 'Development Milestones' },
    { value: '160%', label: 'Phase 1 Goals Exceeded' },
    { value: '100%', label: 'FOSS Technology' },
    { value: '7', label: 'AI Agents Working' },
  ];
</script>

<svelte:head>
  <title
    >Enterprise AI Training - VybeCoding.ai | Scale Your Team's AI Skills</title
  >
  <meta
    name="description"
    content="Transform your organization with VybeCoding.ai's enterprise AI training platform. Custom curriculum, team management, and proven results for Fortune 500 companies."
  />
  <meta
    name="keywords"
    content="enterprise AI training, corporate machine learning education, team AI development, business AI solutions"
  />
</svelte:head>

<!-- Maya's Design System: Enterprise-focused accessible layout -->
<main role="main">
  <!-- Hero Section -->
  <section
    class="py-20 bg-gradient-to-br from-primary/10 to-secondary/10"
    aria-labelledby="enterprise-hero-heading"
  >
    <Container>
      <div class="text-center max-w-4xl mx-auto">
        <h1
          id="enterprise-hero-heading"
          class="text-4xl md:text-5xl font-bold mb-6"
        >
          Scale AI Excellence Across Your
          <span class="text-primary">Enterprise</span>
        </h1>
        <p class="text-xl text-muted-foreground mb-8 leading-relaxed">
          Empower your teams with the world's most effective AI development
          training. Custom curriculum, enterprise security, and proven ROI for
          Fortune 500 companies.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <Button href="#contact" class="btn-primary btn-lg">
            <Phone class="w-5 h-5 mr-2" aria-hidden="true" />
            Schedule Demo
          </Button>
          <Button href="/pricing" variant="outline" class="btn-lg">
            View Pricing
          </Button>
        </div>
      </div>
    </Container>
  </section>

  <!-- Stats Section -->
  <section class="py-16" aria-labelledby="stats-heading">
    <Container>
      <h2 id="stats-heading" class="sr-only">Enterprise Success Metrics</h2>
      <Grid cols="auto" gap="large" class="text-center">
        {#each realStats as stat}
          <div class="flex flex-col items-center">
            <div class="text-4xl font-bold text-primary mb-2">{stat.value}</div>
            <div class="text-muted-foreground">{stat.label}</div>
          </div>
        {/each}
      </Grid>
    </Container>
  </section>

  <!-- Features Section -->
  <section class="py-20" aria-labelledby="features-heading">
    <Container>
      <div class="text-center mb-16">
        <h2 id="features-heading" class="text-3xl md:text-4xl font-bold mb-4">
          Enterprise-Grade Features
        </h2>
        <p class="text-xl text-muted-foreground max-w-3xl mx-auto">
          Everything you need to train, manage, and scale AI development across
          your organization.
        </p>
      </div>

      <Grid cols="auto" gap="large">
        {#each enterpriseFeatures as feature}
          <Card class="p-8 hover:shadow-lg transition-shadow h-full">
            <div class="flex flex-col h-full">
              <div
                class="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mb-6"
              >
                <svelte:component
                  this={feature.icon}
                  class="w-6 h-6 text-primary"
                  aria-hidden="true"
                />
              </div>
              <h3 class="text-xl font-semibold mb-4">{feature.title}</h3>
              <p class="text-muted-foreground flex-1">{feature.description}</p>
            </div>
          </Card>
        {/each}
      </Grid>
    </Container>
  </section>

  <!-- Pricing Section -->
  <section class="py-20 bg-muted/50" aria-labelledby="pricing-heading">
    <Container>
      <div class="text-center mb-16">
        <h2 id="pricing-heading" class="text-3xl md:text-4xl font-bold mb-4">
          Enterprise Pricing
        </h2>
        <p class="text-xl text-muted-foreground max-w-3xl mx-auto">
          Flexible plans designed to scale with your organization's AI training
          needs.
        </p>
      </div>

      <Grid cols="auto" gap="large" class="max-w-6xl mx-auto">
        {#each plans as plan}
          <Card
            class="p-8 hover:shadow-lg transition-shadow h-full relative {plan.popular
              ? 'border-primary shadow-lg'
              : ''}"
          >
            {#if plan.popular}
              <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <Badge class="bg-primary text-primary-foreground"
                  >Most Popular</Badge
                >
              </div>
            {/if}

            <div class="flex flex-col h-full">
              <div class="text-center mb-8">
                <h3 class="text-2xl font-bold mb-2">{plan.name}</h3>
                <div class="text-3xl font-bold text-primary mb-2">
                  {plan.price}
                </div>
                <div class="text-muted-foreground">{plan.period}</div>
                <p class="text-sm text-muted-foreground mt-4">
                  {plan.description}
                </p>
              </div>

              <div class="flex-1">
                <ul class="space-y-3">
                  {#each plan.features as feature}
                    <li class="flex items-start gap-3">
                      <CheckCircle
                        class="w-5 h-5 text-primary flex-shrink-0 mt-0.5"
                        aria-hidden="true"
                      />
                      <span class="text-sm">{feature}</span>
                    </li>
                  {/each}
                </ul>
              </div>

              <div class="mt-8">
                <Button
                  href="#contact"
                  class="w-full {plan.popular ? 'btn-primary' : 'btn-outline'}"
                >
                  {plan.name === 'Custom' ? 'Contact Sales' : 'Get Started'}
                </Button>
              </div>
            </div>
          </Card>
        {/each}
      </Grid>
    </Container>
  </section>

  <!-- Testimonials Section -->
  <section class="py-20" aria-labelledby="testimonials-heading">
    <Container>
      <div class="text-center mb-16">
        <h2
          id="testimonials-heading"
          class="text-3xl md:text-4xl font-bold mb-4"
        >
          Trusted by Industry Leaders
        </h2>
        <p class="text-xl text-muted-foreground max-w-3xl mx-auto">
          See how leading organizations are transforming their AI capabilities
          with VybeCoding.ai.
        </p>
      </div>

      <Grid cols="auto" gap="large" class="max-w-4xl mx-auto">
        {#each realPlatformHighlights as highlight}
          <Card class="p-8 hover:shadow-lg transition-shadow">
            <blockquote class="text-lg italic mb-6">
              "{highlight.quote}"
            </blockquote>
            <div class="flex items-center gap-4">
              <div
                class="w-12 h-12 rounded-full bg-gradient-to-br from-primary to-secondary flex items-center justify-center"
              >
                <span class="text-white font-bold"
                  >{highlight.author
                    .split(' ')
                    .map(n => n[0])
                    .join('')}</span
                >
              </div>
              <div>
                <div class="font-semibold">{highlight.author}</div>
                <div class="text-sm text-muted-foreground">
                  {highlight.role}, {highlight.company}
                </div>
              </div>
            </div>
          </Card>
        {/each}
      </Grid>
    </Container>
  </section>

  <!-- Contact Section -->
  <section
    id="contact"
    class="py-20 bg-muted/50"
    aria-labelledby="contact-heading"
  >
    <Container>
      <div class="text-center max-w-3xl mx-auto">
        <h2 id="contact-heading" class="text-3xl md:text-4xl font-bold mb-6">
          Ready to Transform Your Team?
        </h2>
        <p class="text-xl text-muted-foreground mb-8">
          Schedule a personalized demo and discover how VybeCoding.ai can
          accelerate your organization's AI journey.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <Button
            href="mailto:<EMAIL>"
            class="btn-primary btn-lg"
          >
            <Mail class="w-5 h-5 mr-2" aria-hidden="true" />
            Contact Sales
          </Button>
          <Button href="/auth/signup" variant="outline" class="btn-lg">
            Start Free Trial
            <ArrowRight class="w-5 h-5 ml-2" aria-hidden="true" />
          </Button>
        </div>

        <div class="mt-8 text-sm text-muted-foreground">
          <p>
            Questions? Call us at <a
              href="tel:******-VYBE-AI"
              class="text-primary hover:underline">+1 (555) VYBE-AI</a
            >
          </p>
        </div>
      </div>
    </Container>
  </section>
</main>
