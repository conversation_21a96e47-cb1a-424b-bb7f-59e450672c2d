<script lang="ts">
  import { goto } from '$app/navigation';
  import { Eye, EyeOff, ArrowRight, Mail, Lock } from 'lucide-svelte';
  import { authService } from '../../../lib/services/auth';
  import { account } from '../../../lib/services/appwrite';

  // Simple form state management
  let email = $state('');
  let password = $state('');
  let loading = $state(false);
  let error = $state('');
  let showPassword = $state(false);

  // Handle form submission
  const handleSubmit = async (e: Event) => {
    e.preventDefault();

    if (!email || !password) {
      error = 'Please enter both email and password';
      return;
    }

    loading = true;
    error = '';

    try {
      await authService.signIn(email, password);
      goto('/dashboard');
    } catch (err: any) {
      error = err.message || 'Failed to sign in';
    } finally {
      loading = false;
    }
  };

  // Social authentication handlers
  const handleGoogleSignIn = async () => {
    loading = true;
    try {
      await account.createOAuth2Session(
        'google' as any,
        'http://localhost:5173/dashboard',
        'http://localhost:5173/auth/signin'
      );
    } catch (err: any) {
      error = err.message || 'Failed to sign in with Google';
      loading = false;
    }
  };

  const handleGitHubSignIn = async () => {
    loading = true;
    try {
      await account.createOAuth2Session(
        'github' as any,
        'http://localhost:5173/dashboard',
        'http://localhost:5173/auth/signin'
      );
    } catch (err: any) {
      error = err.message || 'Failed to sign in with GitHub';
      loading = false;
    }
  };
</script>

<svelte:head>
  <title>Sign In - VybeCoding.ai | Access Your AI Learning Dashboard</title>
  <meta
    name="description"
    content="Sign in to your VybeCoding.ai account to continue your AI development journey with the Vybe Method."
  />
</svelte:head>

<!-- Simplified Sign In Layout -->
<main class="bg-slate-900 p-4 pt-16 pb-16">
  <div class="w-full max-w-md sm:max-w-lg lg:max-w-xl mx-auto">
    <!-- Header -->
    <div class="text-center mb-6">
      <!-- VybeCoding.ai logo and text removed -->

      <h1 class="text-2xl font-bold text-white mb-1">Welcome Back</h1>
      <p class="text-slate-400 text-sm">Continue your AI development journey</p>
    </div>

    <!-- Form Card -->
    <div class="bg-slate-800 border border-slate-700 rounded-2xl p-6 shadow-xl">
      <!-- Error Alert -->
      {#if error}
        <div
          class="mb-6 p-4 rounded-xl bg-red-500/20 border border-red-500/30 text-red-300"
        >
          <span class="font-medium">{error}</span>
        </div>
      {/if}

      <!-- Social Authentication -->
      <div class="space-y-3 mb-4">
        <button
          type="button"
          onclick={handleGoogleSignIn}
          disabled={loading}
          class="w-full flex items-center justify-center gap-3 px-4 py-3 rounded-xl bg-white hover:bg-gray-50 text-gray-700 font-semibold transition-all duration-200 disabled:opacity-50"
        >
          <svg class="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
            <path
              d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
              fill="#4285F4"
            />
            <path
              d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
              fill="#34A853"
            />
            <path
              d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
              fill="#FBBC05"
            />
            <path
              d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
              fill="#EA4335"
            />
          </svg>
          Continue with Google
        </button>

        <button
          type="button"
          onclick={handleGitHubSignIn}
          disabled={loading}
          class="w-full flex items-center justify-center gap-3 px-4 py-3 rounded-xl bg-gray-900 hover:bg-gray-800 text-white font-semibold transition-all duration-200 disabled:opacity-50"
        >
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
            <path
              d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"
            />
          </svg>
          Continue with GitHub
        </button>
      </div>

      <!-- Divider -->
      <div class="relative mb-4">
        <div class="absolute inset-0 flex items-center">
          <div class="w-full border-t border-slate-600"></div>
        </div>
        <div class="relative flex justify-center text-sm">
          <span class="px-2 bg-slate-800 text-slate-400"
            >Or continue with email</span
          >
        </div>
      </div>

      <!-- Email Form -->
      <form onsubmit={handleSubmit} class="space-y-4">
        <!-- Email Field -->
        <div>
          <label
            for="email"
            class="block text-sm font-medium text-slate-300 mb-2">Email</label
          >
          <div class="relative">
            <Mail
              class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400"
            />
            <input
              id="email"
              type="email"
              bind:value={email}
              placeholder="Email address"
              class="w-full pl-10 pr-4 py-3 bg-slate-700 border border-slate-600 rounded-xl text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              required
            />
          </div>
        </div>

        <!-- Password Field -->
        <div>
          <label
            for="password"
            class="block text-sm font-medium text-slate-300 mb-2"
            >Password</label
          >
          <div class="relative">
            <Lock
              class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400"
            />
            <input
              id="password"
              type={showPassword ? 'text' : 'password'}
              bind:value={password}
              placeholder="Password"
              class="w-full pl-10 pr-12 py-3 bg-slate-700 border border-slate-600 rounded-xl text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              required
            />
            <button
              type="button"
              onclick={() => (showPassword = !showPassword)}
              class="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-300"
            >
              {#if showPassword}
                <EyeOff class="w-5 h-5" />
              {:else}
                <Eye class="w-5 h-5" />
              {/if}
            </button>
          </div>
        </div>

        <!-- Submit Button -->
        <button
          type="submit"
          disabled={loading}
          class="w-full flex items-center justify-center gap-2 px-4 py-3 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-semibold rounded-xl transition-all duration-200 disabled:opacity-50"
        >
          {#if loading}
            <div
              class="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"
            ></div>
            Signing In...
          {:else}
            Sign In
            <ArrowRight class="w-5 h-5" />
          {/if}
        </button>
      </form>

      <!-- Sign Up Link -->
      <div class="mt-4 text-center">
        <p class="text-slate-400">
          Don't have an account?
          <a
            href="/auth/signup"
            class="text-purple-400 hover:text-purple-300 font-medium"
            >Create account</a
          >
        </p>
      </div>
    </div>
  </div>
</main>
