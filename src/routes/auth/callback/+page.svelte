<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { page } from '$app/stores';
  import { authService } from '../../../lib/services/auth';

  let loading = true;
  let error = '';

  onMount(async () => {
    try {
      // Get current user after OAuth callback
      const user = await authService.getCurrentUser();

      if (user) {
        console.log('OAuth sign in successful:', user);

        // Check for redirect parameter
        const redirectTo =
          $page.url.searchParams.get('redirect') || '/dashboard';
        goto(redirectTo);
      } else {
        throw new Error('Failed to authenticate user');
      }
    } catch (err: any) {
      console.error('OAuth callback error:', err);
      error = err.message || 'Authentication failed';

      // Redirect back to sign in with error using real timing
      if (typeof requestIdleCallback !== 'undefined') {
        requestIdleCallback(() => {
          goto('/auth/signin?error=oauth_callback_failed');
        }, { timeout: 3000 });
      } else {
        // Fallback for environments without requestIdleCallback
        setTimeout(() => {
          goto('/auth/signin?error=oauth_callback_failed');
        }, 3000);
      }
    } finally {
      loading = false;
    }
  });
</script>

<svelte:head>
  <title>Authenticating... - VybeCoding.ai</title>
</svelte:head>

<div class="min-h-screen flex items-center justify-center bg-background">
  <div class="text-center space-y-4">
    {#if loading}
      <div
        class="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto"
      ></div>
      <h1 class="text-xl font-semibold">Completing sign in...</h1>
      <p class="text-muted-foreground">
        Please wait while we authenticate your account.
      </p>
    {:else if error}
      <div class="text-red-500">
        <h1 class="text-xl font-semibold">Authentication Failed</h1>
        <p class="text-muted-foreground mt-2">{error}</p>
        <p class="text-sm text-muted-foreground mt-4">
          Redirecting to sign in page...
        </p>
      </div>
    {/if}
  </div>
</div>
