<!--
  Vybe Method Buttons Demo Page
  Showcases the new Full Vybe Button and Vybe Qube Generator
  Implementation by <PERSON> (Developer) based on BMAD analysis
-->
<script lang="ts">
  import FullVybeButton from '$lib/components/vybe/FullVybeButtonSimple.svelte';
  import { <PERSON>rk<PERSON>, ArrowRight, CheckCircle, Globe } from 'lucide-svelte';

  // Demo state
  let contentGenerated = false;
  let qubeGenerated = false;
  let lastGeneratedContent: any[] = [];
  let lastGeneratedQube: any = null;

  // Handle content generation events
  function handleContentGenerated(event: CustomEvent) {
    contentGenerated = true;
    lastGeneratedContent = event.detail.content;
    console.log('Content generated:', event.detail.content);
  }

  function handleContentError(event: CustomEvent) {
    console.error('Content generation error:', event.detail.error);
  }

  // Handle qube generation events
  function handleQubeGenerated(event: CustomEvent) {
    qubeGenerated = true;
    lastGeneratedQube = event.detail.qube;
    console.log('Qube generated:', event.detail.qube);
  }

  function handleQubeError(event: CustomEvent) {
    console.error('Qube generation error:', event.detail.error);
  }

  function handleProgressUpdate(event: CustomEvent) {
    console.log('Progress update:', event.detail.progress);
  }
</script>

<svelte:head>
  <title>Vybe Method Demo - VybeCoding.ai</title>
  <meta
    name="description"
    content="Experience the power of autonomous AI development with Vybe Method buttons"
  />
</svelte:head>

<div
  class="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-orange-50"
>
  <!-- Hero Section -->
  <div class="relative overflow-hidden">
    <div
      class="absolute inset-0 bg-gradient-to-r from-indigo-600/10 to-orange-600/10"
    ></div>
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
      <div class="text-center">
        <Badge variant="outline" class="mb-4 bg-white/80">
          <Sparkles class="w-4 h-4 mr-2" />
          New Features Available
        </Badge>

        <h1 class="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
          Experience the
          <span
            class="bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent"
          >
            Vybe Method
          </span>
        </h1>

        <p class="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
          Transform any content or business idea into profitable digital assets
          using our autonomous AI agent network. No coding required, just pure
          AI-powered creation.
        </p>

        <div
          class="flex flex-col sm:flex-row gap-6 justify-center items-center"
        >
          <div class="text-center">
            <p class="text-sm text-gray-500 mb-3">Transform Content</p>
            <FullVybeButton
              size="lg"
              on:contentGenerated={handleContentGenerated}
              on:error={handleContentError}
            />
          </div>

          <div class="hidden sm:block text-gray-400">
            <ArrowRight class="w-6 h-6" />
          </div>

          <div class="text-center">
            <p class="text-sm text-gray-500 mb-3">
              Generate Profitable Website
            </p>
            <VybeQubeGenerator
              size="lg"
              on:qubeGenerated={handleQubeGenerated}
              on:progressUpdate={handleProgressUpdate}
              on:error={handleQubeError}
            />
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Features Section -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
    <div class="text-center mb-16">
      <h2 class="text-3xl font-bold text-gray-900 mb-4">
        Powered by 7 Specialized AI Agents
      </h2>
      <p class="text-lg text-gray-600">
        Each agent brings unique expertise to create comprehensive, profitable
        digital solutions
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
      <!-- Agent Cards -->
      <div
        class="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow"
      >
        <div class="text-3xl mb-3">🔮</div>
        <h3 class="font-semibold text-gray-900 mb-2">VYBA</h3>
        <p class="text-sm text-gray-600">Business Analysis & Market Research</p>
      </div>

      <div
        class="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow"
      >
        <div class="text-3xl mb-3">📦</div>
        <h3 class="font-semibold text-gray-900 mb-2">QUBERT</h3>
        <p class="text-sm text-gray-600">Product Strategy & Requirements</p>
      </div>

      <div
        class="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow"
      >
        <div class="text-3xl mb-3">🏗️</div>
        <h3 class="font-semibold text-gray-900 mb-2">CODEX</h3>
        <p class="text-sm text-gray-600">Technical Architecture & Design</p>
      </div>

      <div
        class="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow"
      >
        <div class="text-3xl mb-3">🎨</div>
        <h3 class="font-semibold text-gray-900 mb-2">PIXY</h3>
        <p class="text-sm text-gray-600">UI/UX Design & Branding</p>
      </div>

      <div
        class="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow"
      >
        <div class="text-3xl mb-3">🦆</div>
        <h3 class="font-semibold text-gray-900 mb-2">DUCKY</h3>
        <p class="text-sm text-gray-600">Quality Assurance & Testing</p>
      </div>

      <div
        class="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow"
      >
        <div class="text-3xl mb-3">😊</div>
        <h3 class="font-semibold text-gray-900 mb-2">HAPPY</h3>
        <p class="text-sm text-gray-600">Project Coordination & Optimization</p>
      </div>

      <div
        class="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow"
      >
        <div class="text-3xl mb-3">⚡</div>
        <h3 class="font-semibold text-gray-900 mb-2">VYBRO</h3>
        <p class="text-sm text-gray-600">Development & Deployment</p>
      </div>

      <div
        class="bg-gradient-to-br from-indigo-50 to-purple-50 rounded-xl p-6 border border-indigo-200 flex items-center justify-center"
      >
        <div class="text-center">
          <div class="text-2xl mb-2">🤝</div>
          <p class="text-sm font-medium text-indigo-900">Working Together</p>
          <p class="text-xs text-indigo-700">Autonomous Collaboration</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Results Section -->
  {#if contentGenerated || qubeGenerated}
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
      <div class="text-center mb-12">
        <h2 class="text-3xl font-bold text-gray-900 mb-4">
          🎉 Generation Complete!
        </h2>
        <p class="text-lg text-gray-600">
          Your AI agents have successfully created your digital assets
        </p>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {#if contentGenerated && lastGeneratedContent.length > 0}
          <div class="bg-white rounded-xl p-8 shadow-sm border border-gray-100">
            <div class="flex items-center gap-3 mb-6">
              <CheckCircle class="w-6 h-6 text-green-500" />
              <h3 class="text-xl font-semibold text-gray-900">
                Content Generated
              </h3>
            </div>

            <div class="space-y-4">
              {#each lastGeneratedContent as content}
                <div class="border border-gray-200 rounded-lg p-4">
                  <div class="flex items-center gap-2 mb-2">
                    <Badge variant="outline" size="sm">{content.type}</Badge>
                    <span class="text-sm text-gray-500"
                      >{content.metadata.wordCount} words</span
                    >
                  </div>
                  <h4 class="font-medium text-gray-900 mb-2">
                    {content.title}
                  </h4>
                  <p class="text-sm text-gray-600 line-clamp-3">
                    {content.content.substring(0, 150)}...
                  </p>
                </div>
              {/each}
            </div>
          </div>
        {/if}

        {#if qubeGenerated && lastGeneratedQube}
          <div class="bg-white rounded-xl p-8 shadow-sm border border-gray-100">
            <div class="flex items-center gap-3 mb-6">
              <CheckCircle class="w-6 h-6 text-green-500" />
              <h3 class="text-xl font-semibold text-gray-900">
                Vybe Qube Live
              </h3>
            </div>

            <div class="space-y-4">
              <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                <div class="flex items-center gap-2 mb-2">
                  <Globe class="w-4 h-4 text-green-600" />
                  <span class="font-medium text-green-900"
                    >Website Deployed</span
                  >
                </div>
                <p class="text-sm text-green-700 mb-3">
                  {lastGeneratedQube.businessIdea.substring(0, 100)}...
                </p>
                <a
                  href={lastGeneratedQube.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  class="inline-flex items-center gap-2 text-sm text-green-600 hover:text-green-800"
                >
                  Visit Website →
                </a>
              </div>

              <div class="grid grid-cols-2 gap-4">
                <div class="text-center p-3 bg-gray-50 rounded-lg">
                  <div class="text-lg font-bold text-gray-900">
                    {lastGeneratedQube.metrics.visitors}
                  </div>
                  <div class="text-xs text-gray-600">Visitors</div>
                </div>
                <div class="text-center p-3 bg-gray-50 rounded-lg">
                  <div class="text-lg font-bold text-gray-900">
                    ${lastGeneratedQube.metrics.revenue}
                  </div>
                  <div class="text-xs text-gray-600">Revenue</div>
                </div>
              </div>
            </div>
          </div>
        {/if}
      </div>
    </div>
  {/if}

  <!-- CTA Section -->
  <div class="bg-gradient-to-r from-indigo-600 to-purple-600 py-16">
    <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
      <h2 class="text-3xl font-bold text-white mb-4">
        Ready to Transform Your Ideas?
      </h2>
      <p class="text-xl text-indigo-100 mb-8">
        Join thousands of creators using the Vybe Method to build profitable
        digital assets
      </p>

      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <a
          href="/signup"
          class="inline-flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-lg text-indigo-600 bg-white hover:bg-gray-50 transition-colors"
        >
          Get Started Free
        </a>
        <a
          href="/methods"
          class="inline-flex items-center justify-center px-8 py-3 border border-white text-base font-medium rounded-lg text-white hover:bg-white/10 transition-colors"
        >
          Learn More
        </a>
      </div>
    </div>
  </div>
</div>

<style>
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
