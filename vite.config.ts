import { sveltekit } from '@sveltejs/kit/vite';
import { defineConfig } from 'vitest/config';

export default defineConfig({
  resolve: {
    alias: {
      // Monaco editor alias temporarily disabled to fix build issues
      // 'monaco-editor': 'monaco-editor/esm/vs/editor/editor.api.js',
    },
    dedupe: ['svelte'],
  },
  plugins: [
    sveltekit(),
    // Monaco editor plugin temporarily disabled to fix build issues
    // monacoEditorPlugin({
    //   languageWorkers: [
    //     'editorWorkerService',
    //     'typescript',
    //     'json',
    //     'html',
    //     'css',
    //   ],
    // }),
  ],
  test: {
    include: ['src/**/*.{test,spec}.{js,ts}', 'tests/**/*.{test,spec}.{js,ts}'],
    environment: 'jsdom',
    setupFiles: ['./tests/setup.js'],
    globals: true,
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: ['node_modules/', 'src/test/', '**/*.d.ts', '**/*.config.*'],
      thresholds: {
        global: {
          branches: 90,
          functions: 90,
          lines: 90,
          statements: 90,
        },
      },
    },
  },
  define: {
    // Enable HMR for better development experience
    __DEV__: process.env['NODE_ENV'] === 'development',
  },
  server: {
    port: 5173,
    host: true,
  },
  preview: {
    port: 4173,
    host: true,
  },
  optimizeDeps: {
    exclude: ['swagger-jsdoc', 'monaco-editor'],
    force: true,
    include: [],
  },
  build: {
    rollupOptions: {
      external: ['swagger-jsdoc'],
    },
  },
});
