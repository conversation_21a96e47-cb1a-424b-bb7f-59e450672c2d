# VybeCoding.ai Docker Compose - Production Environment
version: '3.8'

services:
  vybecoding-prod:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - NODE_ENV=production
    ports:
      - '3000:3000'
    environment:
      - NODE_ENV=production
      - VITE_ENVIRONMENT=production
      - VITE_APPWRITE_ENDPOINT=${VITE_APPWRITE_ENDPOINT}
      - VITE_APPWRITE_PROJECT_ID=${VITE_APPWRITE_PROJECT_ID}
      - VITE_APPWRITE_DATABASE_ID=${VITE_APPWRITE_DATABASE_ID}
      - VITE_ENABLE_DEBUG_MODE=false
      - VITE_ENABLE_ANALYTICS=true
      - VITE_LOG_LEVEL=error
    volumes:
      - ./logs:/app/logs:rw
      - vybecoding_prod_data:/app/data
    restart: always
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3000/api/health']
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    networks:
      - vybecoding_prod_network
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 512M
    logging:
      driver: 'json-file'
      options:
        max-size: '10m'
        max-file: '3'

  # Production Redis with persistence
  redis-prod:
    image: redis:7-alpine
    ports:
      - '6379:6379'
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    volumes:
      - redis_prod_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
    restart: always
    healthcheck:
      test: ['CMD', 'redis-cli', 'ping']
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - vybecoding_prod_network
    command: redis-server /usr/local/etc/redis/redis.conf --requirepass ${REDIS_PASSWORD}
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 256M
    logging:
      driver: 'json-file'
      options:
        max-size: '10m'
        max-file: '3'

  # Nginx reverse proxy with SSL
  nginx-prod:
    image: nginx:alpine
    ports:
      - '80:80'
      - '443:443'
    environment:
      - NGINX_HOST=${NGINX_HOST:-vybecoding.ai}
      - NGINX_PORT=80
    volumes:
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_prod_logs:/var/log/nginx
    depends_on:
      - vybecoding-prod
    restart: always
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost/health']
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - vybecoding_prod_network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.1'
          memory: 128M
    logging:
      driver: 'json-file'
      options:
        max-size: '10m'
        max-file: '3'

  # Monitoring with Prometheus (optional)
  prometheus:
    image: prom/prometheus:latest
    ports:
      - '9090:9090'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - vybecoding_prod_network
    profiles:
      - with-monitoring

  # Grafana for metrics visualization (optional)
  grafana:
    image: grafana/grafana:latest
    ports:
      - '3001:3000'
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
    depends_on:
      - prometheus
    restart: unless-stopped
    networks:
      - vybecoding_prod_network
    profiles:
      - with-monitoring

volumes:
  vybecoding_prod_data:
    driver: local
  redis_prod_data:
    driver: local
  nginx_prod_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  vybecoding_prod_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
