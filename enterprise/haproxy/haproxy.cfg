# HAProxy Enterprise Configuration for VybeCoding.ai
# Phase 6: Production Scaling & Enterprise Deployment

global
    # Process management
    daemon
    user haproxy
    group haproxy
    
    # Performance tuning
    maxconn 4096
    nbproc 1
    nbthread 4
    
    # SSL/TLS configuration
    ssl-default-bind-ciphers ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256
    ssl-default-bind-options ssl-min-ver TLSv1.2 no-tls-tickets
    ssl-default-server-ciphers ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256
    ssl-default-server-options ssl-min-ver TLSv1.2 no-tls-tickets
    
    # Logging
    log stdout local0 info
    
    # Stats socket for runtime management
    stats socket /var/run/haproxy.sock mode 600 level admin
    stats timeout 2m

defaults
    mode http
    log global
    option httplog
    option dontlognull
    option log-health-checks
    option forwardfor
    option http-server-close
    
    # Timeouts
    timeout connect 5000ms
    timeout client 50000ms
    timeout server 50000ms
    timeout http-request 10s
    timeout http-keep-alive 2s
    timeout check 10s
    
    # Error handling
    errorfile 400 /etc/haproxy/errors/400.http
    errorfile 403 /etc/haproxy/errors/403.http
    errorfile 408 /etc/haproxy/errors/408.http
    errorfile 500 /etc/haproxy/errors/500.http
    errorfile 502 /etc/haproxy/errors/502.http
    errorfile 503 /etc/haproxy/errors/503.http
    errorfile 504 /etc/haproxy/errors/504.http
    
    # Compression
    compression algo gzip
    compression type text/html text/plain text/css text/javascript application/javascript application/json application/xml

# Frontend - HTTP (redirect to HTTPS)
frontend vybecoding_http
    bind *:80
    
    # Security headers
    http-response set-header X-Frame-Options DENY
    http-response set-header X-Content-Type-Options nosniff
    http-response set-header X-XSS-Protection "1; mode=block"
    http-response set-header Strict-Transport-Security "max-age=********; includeSubDomains; preload"
    
    # Health check endpoint
    acl health_check path_beg /health
    http-request return status 200 content-type text/plain string "healthy" if health_check
    
    # Redirect HTTP to HTTPS
    redirect scheme https code 301 if !{ ssl_fc }

# Frontend - HTTPS
frontend vybecoding_https
    bind *:443 ssl crt /etc/ssl/certs/vybecoding.pem alpn h2,http/1.1
    
    # Security headers
    http-response set-header X-Frame-Options DENY
    http-response set-header X-Content-Type-Options nosniff
    http-response set-header X-XSS-Protection "1; mode=block"
    http-response set-header Strict-Transport-Security "max-age=********; includeSubDomains; preload"
    http-response set-header Referrer-Policy "strict-origin-when-cross-origin"
    
    # Rate limiting
    stick-table type ip size 100k expire 30s store http_req_rate(10s)
    http-request track-sc0 src
    http-request deny if { sc_http_req_rate(0) gt 20 }
    
    # API rate limiting (more restrictive)
    acl is_api path_beg /api/
    http-request deny if is_api { sc_http_req_rate(0) gt 10 }
    
    # MAS Observatory routing
    acl is_observatory hdr(host) -i observatory.vybecoding.ai
    acl is_observatory path_beg /observatory
    use_backend mas_observatory if is_observatory
    
    # API routing
    acl is_api path_beg /api/
    use_backend vybecoding_api if is_api
    
    # Static assets routing (CDN)
    acl is_static path_beg /static/ /assets/ /images/ /css/ /js/
    use_backend cdn_cache if is_static
    
    # WebSocket routing
    acl is_websocket hdr(upgrade) -i websocket
    use_backend vybecoding_websocket if is_websocket
    
    # Default backend
    default_backend vybecoding_app

# Backend - Main Application (Load Balanced)
backend vybecoding_app
    balance roundrobin
    option httpchk GET /api/health
    http-check expect status 200
    
    # Health check configuration
    default-server check inter 10s rise 2 fall 3 maxconn 100
    
    # Application servers
    server app1 vybecoding-app-1:3000 check weight 100
    server app2 vybecoding-app-2:3000 check weight 100
    server app3 vybecoding-app-3:3000 check weight 100
    
    # Session persistence (if needed)
    # cookie SERVERID insert indirect nocache
    
    # Connection pooling
    http-reuse always

# Backend - API (Optimized for API calls)
backend vybecoding_api
    balance leastconn
    option httpchk GET /api/health
    http-check expect status 200
    
    # API-specific timeouts
    timeout server 30s
    timeout connect 3s
    
    # API servers (same as app servers but optimized routing)
    server api1 vybecoding-app-1:3000 check weight 100 maxconn 50
    server api2 vybecoding-app-2:3000 check weight 100 maxconn 50
    server api3 vybecoding-app-3:3000 check weight 100 maxconn 50
    
    # API-specific headers
    http-request set-header X-Forwarded-Proto https
    http-request set-header X-Forwarded-Port 443

# Backend - WebSocket (Sticky sessions)
backend vybecoding_websocket
    balance source
    option httpchk GET /api/health
    
    # WebSocket-specific timeouts
    timeout server 1h
    timeout tunnel 1h
    
    # WebSocket servers
    server ws1 vybecoding-app-1:3000 check weight 100
    server ws2 vybecoding-app-2:3000 check weight 100
    server ws3 vybecoding-app-3:3000 check weight 100

# Backend - CDN Cache (Varnish)
backend cdn_cache
    balance roundrobin
    option httpchk GET /health
    
    # Cache-specific configuration
    timeout server 60s
    
    # Cache servers
    server cache1 cdn-cache:80 check weight 100

# Backend - MAS Observatory
backend mas_observatory
    balance roundrobin
    option httpchk GET /health
    
    # Observatory servers (from mas-observatory stack)
    server observatory1 nginx:80 check weight 100

# Stats interface
listen stats
    bind *:8404
    stats enable
    stats uri /stats
    stats refresh 30s
    stats admin if TRUE
    
    # Authentication (change in production)
    stats auth admin:vybe_stats_2024
    
    # Enhanced stats
    stats show-legends
    stats show-node
    stats show-desc VybeCoding.ai Enterprise Load Balancer

# Health check endpoint
listen health_check
    bind *:8405
    mode http
    monitor-uri /health
    option dontlognull
