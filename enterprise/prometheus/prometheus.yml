global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  - job_name: 'vybecoding-apps'
    static_configs:
      - targets: ['vybecoding-app-1:3000', 'vybecoding-app-2:3000', 'vybecoding-app-3:3000']
    metrics_path: /metrics
    scrape_interval: 10s

  - job_name: 'haproxy'
    static_configs:
      - targets: ['load-balancer:8404']
    metrics_path: /stats/prometheus

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-cluster:6379']

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-primary:5432']

  - job_name: 'auto-scaler'
    static_configs:
      - targets: ['auto-scaler:8000']
