# Enterprise Auto-Scaler Dockerfile
FROM python:3.11-alpine

# Install system dependencies
RUN apk add --no-cache \
    docker-cli \
    curl \
    ca-certificates

# Set working directory
WORKDIR /app

# Copy requirements
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY auto_scaler.py .
COPY config/ ./config/

# Create non-root user
RUN adduser -D -s /bin/sh autoscaler

# Change ownership
RUN chown -R autoscaler:autoscaler /app

# Switch to non-root user
USER autoscaler

# Health check
HEALTHCHECK --interval=60s --timeout=10s --start-period=30s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:8000/health')" || exit 1

# Run the auto-scaler
CMD ["python", "auto_scaler.py"]
