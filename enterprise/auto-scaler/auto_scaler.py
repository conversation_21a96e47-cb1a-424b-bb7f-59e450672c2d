#!/usr/bin/env python3
"""
Enterprise Auto-Scaler for VybeCoding.ai
Phase 6: Production Scaling & Enterprise Deployment

Features:
- Dynamic horizontal scaling based on metrics
- Intelligent load prediction
- Cost-optimized scaling decisions
- Integration with monitoring systems
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional
import aiohttp
import docker
import os
from dataclasses import dataclass
from enum import Enum

class ScalingAction(Enum):
    SCALE_UP = "scale_up"
    SCALE_DOWN = "scale_down"
    MAINTAIN = "maintain"

@dataclass
class ScalingMetrics:
    cpu_usage: float
    memory_usage: float
    request_rate: float
    response_time: float
    error_rate: float
    active_connections: int
    timestamp: datetime

@dataclass
class ScalingDecision:
    action: ScalingAction
    target_instances: int
    current_instances: int
    reason: str
    confidence: float
    estimated_cost_impact: float

class EnterpriseAutoScaler:
    """
    Enterprise-grade auto-scaler for VybeCoding.ai
    Implements intelligent scaling based on multiple metrics
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.docker_client = docker.from_env()
        
        # Configuration from environment
        self.prometheus_url = os.getenv('PROMETHEUS_URL', 'http://prometheus-enterprise:9090')
        self.scale_up_threshold = float(os.getenv('SCALE_UP_THRESHOLD', '80'))
        self.scale_down_threshold = float(os.getenv('SCALE_DOWN_THRESHOLD', '20'))
        self.min_instances = int(os.getenv('MIN_INSTANCES', '3'))
        self.max_instances = int(os.getenv('MAX_INSTANCES', '10'))
        
        # Scaling parameters
        self.cooldown_period = 300  # 5 minutes
        self.evaluation_window = 180  # 3 minutes
        self.prediction_window = 600  # 10 minutes
        
        # State tracking
        self.last_scaling_action = None
        self.last_scaling_time = None
        self.metrics_history: List[ScalingMetrics] = []
        self.scaling_history: List[ScalingDecision] = []
        
        # Service configuration
        self.service_prefix = "vybecoding-app"
        self.compose_project = "vybecoding"
        
    async def start_auto_scaling(self):
        """Start the auto-scaling monitoring loop"""
        self.logger.info("🚀 Starting Enterprise Auto-Scaler...")
        
        while True:
            try:
                # Collect current metrics
                metrics = await self.collect_metrics()
                if metrics:
                    self.metrics_history.append(metrics)
                    
                    # Keep only recent metrics
                    cutoff_time = datetime.now() - timedelta(seconds=self.prediction_window)
                    self.metrics_history = [
                        m for m in self.metrics_history 
                        if m.timestamp > cutoff_time
                    ]
                    
                    # Make scaling decision
                    decision = await self.make_scaling_decision(metrics)
                    
                    if decision.action != ScalingAction.MAINTAIN:
                        await self.execute_scaling_decision(decision)
                        self.scaling_history.append(decision)
                        
                        # Keep scaling history manageable
                        if len(self.scaling_history) > 100:
                            self.scaling_history = self.scaling_history[-50:]
                
                # Wait before next evaluation
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                self.logger.error(f"Auto-scaling error: {e}")
                await asyncio.sleep(60)  # Wait longer on error
    
    async def collect_metrics(self) -> Optional[ScalingMetrics]:
        """Collect current system metrics from Prometheus"""
        try:
            async with aiohttp.ClientSession() as session:
                # Collect various metrics
                cpu_usage = await self.query_prometheus(session, 'avg(cpu_usage_percent)')
                memory_usage = await self.query_prometheus(session, 'avg(memory_usage_percent)')
                request_rate = await self.query_prometheus(session, 'sum(rate(http_requests_total[5m]))')
                response_time = await self.query_prometheus(session, 'avg(http_request_duration_seconds)')
                error_rate = await self.query_prometheus(session, 'sum(rate(http_requests_total{status=~"5.."}[5m]))')
                active_connections = await self.query_prometheus(session, 'sum(active_connections)')
                
                # Create metrics object
                metrics = ScalingMetrics(
                    cpu_usage=cpu_usage or 0.0,
                    memory_usage=memory_usage or 0.0,
                    request_rate=request_rate or 0.0,
                    response_time=response_time or 0.0,
                    error_rate=error_rate or 0.0,
                    active_connections=int(active_connections or 0),
                    timestamp=datetime.now()
                )
                
                self.logger.info(f"📊 Metrics - CPU: {metrics.cpu_usage:.1f}%, "
                               f"Memory: {metrics.memory_usage:.1f}%, "
                               f"Requests/s: {metrics.request_rate:.1f}, "
                               f"Response: {metrics.response_time:.3f}s")
                
                return metrics
                
        except Exception as e:
            self.logger.error(f"Failed to collect metrics: {e}")
            return None
    
    async def query_prometheus(self, session: aiohttp.ClientSession, query: str) -> Optional[float]:
        """Query Prometheus for a specific metric"""
        try:
            url = f"{self.prometheus_url}/api/v1/query"
            params = {'query': query}
            
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    result = data.get('data', {}).get('result', [])
                    
                    if result and len(result) > 0:
                        value = result[0].get('value', [None, None])[1]
                        return float(value) if value else None
                        
        except Exception as e:
            self.logger.error(f"Prometheus query failed for '{query}': {e}")
        
        return None
    
    async def make_scaling_decision(self, current_metrics: ScalingMetrics) -> ScalingDecision:
        """Make intelligent scaling decision based on metrics and predictions"""
        current_instances = await self.get_current_instance_count()
        
        # Check cooldown period
        if (self.last_scaling_time and 
            datetime.now() - self.last_scaling_time < timedelta(seconds=self.cooldown_period)):
            return ScalingDecision(
                action=ScalingAction.MAINTAIN,
                target_instances=current_instances,
                current_instances=current_instances,
                reason="Cooldown period active",
                confidence=1.0,
                estimated_cost_impact=0.0
            )
        
        # Calculate scaling scores
        scale_up_score = self.calculate_scale_up_score(current_metrics)
        scale_down_score = self.calculate_scale_down_score(current_metrics)
        
        # Predict future load
        predicted_load = self.predict_future_load()
        
        # Make decision
        if scale_up_score > 0.7 and current_instances < self.max_instances:
            target_instances = min(current_instances + 1, self.max_instances)
            return ScalingDecision(
                action=ScalingAction.SCALE_UP,
                target_instances=target_instances,
                current_instances=current_instances,
                reason=f"High load detected (score: {scale_up_score:.2f})",
                confidence=scale_up_score,
                estimated_cost_impact=self.estimate_cost_impact(target_instances - current_instances)
            )
        
        elif scale_down_score > 0.7 and current_instances > self.min_instances:
            target_instances = max(current_instances - 1, self.min_instances)
            return ScalingDecision(
                action=ScalingAction.SCALE_DOWN,
                target_instances=target_instances,
                current_instances=current_instances,
                reason=f"Low load detected (score: {scale_down_score:.2f})",
                confidence=scale_down_score,
                estimated_cost_impact=self.estimate_cost_impact(target_instances - current_instances)
            )
        
        else:
            return ScalingDecision(
                action=ScalingAction.MAINTAIN,
                target_instances=current_instances,
                current_instances=current_instances,
                reason="Load within acceptable range",
                confidence=0.8,
                estimated_cost_impact=0.0
            )
    
    def calculate_scale_up_score(self, metrics: ScalingMetrics) -> float:
        """Calculate score for scaling up (0-1, higher means more need to scale up)"""
        scores = []
        
        # CPU usage score
        if metrics.cpu_usage > self.scale_up_threshold:
            scores.append((metrics.cpu_usage - self.scale_up_threshold) / (100 - self.scale_up_threshold))
        
        # Memory usage score
        if metrics.memory_usage > self.scale_up_threshold:
            scores.append((metrics.memory_usage - self.scale_up_threshold) / (100 - self.scale_up_threshold))
        
        # Response time score (if > 2 seconds, consider scaling)
        if metrics.response_time > 2.0:
            scores.append(min((metrics.response_time - 2.0) / 3.0, 1.0))
        
        # Error rate score (if > 1%, consider scaling)
        if metrics.error_rate > 0.01:
            scores.append(min(metrics.error_rate / 0.05, 1.0))
        
        # Request rate score (if very high, consider scaling)
        if metrics.request_rate > 100:
            scores.append(min((metrics.request_rate - 100) / 400, 1.0))
        
        return max(scores) if scores else 0.0
    
    def calculate_scale_down_score(self, metrics: ScalingMetrics) -> float:
        """Calculate score for scaling down (0-1, higher means more opportunity to scale down)"""
        scores = []
        
        # CPU usage score (low usage indicates scale down opportunity)
        if metrics.cpu_usage < self.scale_down_threshold:
            scores.append((self.scale_down_threshold - metrics.cpu_usage) / self.scale_down_threshold)
        
        # Memory usage score
        if metrics.memory_usage < self.scale_down_threshold:
            scores.append((self.scale_down_threshold - metrics.memory_usage) / self.scale_down_threshold)
        
        # Response time score (fast responses indicate capacity)
        if metrics.response_time < 0.5:
            scores.append((0.5 - metrics.response_time) / 0.5)
        
        # Low error rate indicates stability
        if metrics.error_rate < 0.001:
            scores.append(0.3)
        
        # Low request rate indicates low load
        if metrics.request_rate < 50:
            scores.append((50 - metrics.request_rate) / 50)
        
        return max(scores) if scores else 0.0
    
    def predict_future_load(self) -> float:
        """Predict future load based on historical patterns"""
        if len(self.metrics_history) < 10:
            return 0.5  # Neutral prediction with insufficient data
        
        # Simple trend analysis
        recent_metrics = self.metrics_history[-10:]
        cpu_trend = (recent_metrics[-1].cpu_usage - recent_metrics[0].cpu_usage) / 10
        memory_trend = (recent_metrics[-1].memory_usage - recent_metrics[0].memory_usage) / 10
        
        # Predict load increase/decrease
        predicted_change = (cpu_trend + memory_trend) / 2
        
        return max(0.0, min(1.0, 0.5 + predicted_change / 100))
    
    def estimate_cost_impact(self, instance_change: int) -> float:
        """Estimate cost impact of scaling decision"""
        # Simplified cost estimation (cost per instance per hour)
        cost_per_instance_hour = 0.50  # $0.50 per hour per instance
        return instance_change * cost_per_instance_hour
    
    async def get_current_instance_count(self) -> int:
        """Get current number of running application instances"""
        try:
            containers = self.docker_client.containers.list(
                filters={'name': self.service_prefix}
            )
            return len([c for c in containers if c.status == 'running'])
        except Exception as e:
            self.logger.error(f"Failed to get instance count: {e}")
            return self.min_instances
    
    async def execute_scaling_decision(self, decision: ScalingDecision):
        """Execute the scaling decision"""
        self.logger.info(f"🔧 Executing scaling decision: {decision.action.value}")
        self.logger.info(f"   Target instances: {decision.target_instances}")
        self.logger.info(f"   Reason: {decision.reason}")
        self.logger.info(f"   Confidence: {decision.confidence:.2f}")
        self.logger.info(f"   Cost impact: ${decision.estimated_cost_impact:.2f}/hour")
        
        try:
            if decision.action == ScalingAction.SCALE_UP:
                await self.scale_up_instance(decision.target_instances)
            elif decision.action == ScalingAction.SCALE_DOWN:
                await self.scale_down_instance(decision.target_instances)
            
            self.last_scaling_action = decision.action
            self.last_scaling_time = datetime.now()
            
            self.logger.info(f"✅ Scaling completed successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Scaling execution failed: {e}")
    
    async def scale_up_instance(self, target_count: int):
        """Scale up by creating new instances"""
        current_count = await self.get_current_instance_count()
        instances_to_create = target_count - current_count
        
        for i in range(instances_to_create):
            instance_id = current_count + i + 1
            container_name = f"{self.service_prefix}-{instance_id}"
            
            # Create new container (simplified - in production use docker-compose scale)
            self.logger.info(f"Creating instance: {container_name}")
            # Implementation would use docker-compose or Kubernetes API
    
    async def scale_down_instance(self, target_count: int):
        """Scale down by removing instances"""
        current_count = await self.get_current_instance_count()
        instances_to_remove = current_count - target_count
        
        # Get containers to remove (remove newest first)
        containers = self.docker_client.containers.list(
            filters={'name': self.service_prefix}
        )
        containers_to_remove = sorted(containers, key=lambda c: c.created, reverse=True)[:instances_to_remove]
        
        for container in containers_to_remove:
            self.logger.info(f"Removing instance: {container.name}")
            container.stop(timeout=30)
            container.remove()
    
    def get_scaling_report(self) -> Dict[str, Any]:
        """Generate scaling report"""
        current_instances = asyncio.run(self.get_current_instance_count())
        
        return {
            'timestamp': datetime.now().isoformat(),
            'current_instances': current_instances,
            'min_instances': self.min_instances,
            'max_instances': self.max_instances,
            'last_scaling_action': self.last_scaling_action.value if self.last_scaling_action else None,
            'last_scaling_time': self.last_scaling_time.isoformat() if self.last_scaling_time else None,
            'recent_decisions': [
                {
                    'action': d.action.value,
                    'target_instances': d.target_instances,
                    'reason': d.reason,
                    'confidence': d.confidence
                }
                for d in self.scaling_history[-10:]
            ],
            'metrics_summary': self.get_metrics_summary()
        }
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get summary of recent metrics"""
        if not self.metrics_history:
            return {'status': 'no_data'}
        
        recent_metrics = self.metrics_history[-10:]
        
        return {
            'avg_cpu_usage': sum(m.cpu_usage for m in recent_metrics) / len(recent_metrics),
            'avg_memory_usage': sum(m.memory_usage for m in recent_metrics) / len(recent_metrics),
            'avg_response_time': sum(m.response_time for m in recent_metrics) / len(recent_metrics),
            'avg_request_rate': sum(m.request_rate for m in recent_metrics) / len(recent_metrics),
            'metrics_count': len(recent_metrics)
        }

async def main():
    """Main auto-scaler function"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    scaler = EnterpriseAutoScaler()
    
    print("🚀 Starting Enterprise Auto-Scaler")
    print("📊 Monitoring system metrics for intelligent scaling")
    print("Press Ctrl+C to stop auto-scaler\n")
    
    try:
        await scaler.start_auto_scaling()
    except KeyboardInterrupt:
        print("\n🛑 Auto-scaler stopped by user")
        
        # Generate final report
        report = scaler.get_scaling_report()
        print("\n📊 Final Auto-Scaling Report:")
        print(f"Current Instances: {report['current_instances']}")
        print(f"Recent Decisions: {len(report['recent_decisions'])}")
    except Exception as e:
        print(f"❌ Auto-scaler error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
