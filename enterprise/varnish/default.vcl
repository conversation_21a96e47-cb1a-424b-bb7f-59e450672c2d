vcl 4.1;

backend default {
    .host = "load-balancer";
    .port = "80";
    .connect_timeout = 5s;
    .first_byte_timeout = 30s;
    .between_bytes_timeout = 5s;
}

sub vcl_recv {
    # Remove cookies for static content
    if (req.url ~ "\.(css|js|png|gif|jp(e)?g|swf|ico|pdf|mov|fla|zip|rar)$") {
        unset req.http.cookie;
    }
    
    # Cache API responses for 5 minutes
    if (req.url ~ "^/api/") {
        set req.http.Cache-Control = "max-age=300";
    }
}

sub vcl_backend_response {
    # Cache static content for 1 hour
    if (bereq.url ~ "\.(css|js|png|gif|jp(e)?g|swf|ico|pdf|mov|fla|zip|rar)$") {
        set beresp.ttl = 1h;
        set beresp.http.Cache-Control = "public, max-age=3600";
    }
    
    # Cache API responses
    if (bereq.url ~ "^/api/") {
        set beresp.ttl = 5m;
    }
}
